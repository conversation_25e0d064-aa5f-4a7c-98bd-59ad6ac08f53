{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "9a06e91e-7e60-4a82-bc4c-8b36b406d2ef", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932462048700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972c2349-b39a-4857-93ea-914036fd99ef", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932467286600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3749612b-e2fe-499b-b67e-ccb84ddc46a9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932467573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abecda88-5c23-428f-9d9e-f7d40afa8d64", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932477634900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee815820-fa26-4d5e-aed1-68696aa4771a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996210784600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968b8506-7444-41b2-b714-486c30144778", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996221813100, "endTime": 31996654942800}, "additional": {"children": ["32bc6faa-1f43-4253-8b20-da2e1a7815f5", "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "51249386-86bc-4420-bf41-8561be1b8484", "cabed5aa-2af1-47b3-9a46-a042adebd667", "1d7d2648-2ebd-4bb6-b2b7-9d375402bc7c", "bc09ecc7-58b6-4ab2-be96-6636bd474dcc", "0c1e7f55-34a2-4bad-ba2c-0791df80d37a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32bc6faa-1f43-4253-8b20-da2e1a7815f5", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996221816300, "endTime": 31996256562700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "8719a714-e9f7-4c24-a90e-938a401b67d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996256581800, "endTime": 31996653591800}, "additional": {"children": ["f09b54a7-a644-438b-a055-d68c11590b76", "11bd3f9b-c0ed-4fa9-a689-50698f860e3b", "056ca6fb-f7d2-4fc0-975c-865acf084fb6", "a6ea51b8-d3c1-49cf-9c57-95a977088e67", "30cb2508-b566-4ede-aaf5-055b9b204111", "7a43c042-3e47-4700-969b-52aac3993189", "a5402687-8a0a-407d-9949-5705dbbb9364", "5f79c457-3916-4489-97eb-568d948f033c", "cd4ab011-ae48-4933-b75b-a55bf97c5352"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "8325262b-2ace-41d8-b519-60928c7f630e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51249386-86bc-4420-bf41-8561be1b8484", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996653631300, "endTime": 31996654931100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "3aaab128-c625-4c79-8aa6-c8d0063056f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cabed5aa-2af1-47b3-9a46-a042adebd667", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996654935500, "endTime": 31996654936500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "14997c2e-03c0-40c4-afd7-ffa32ef1cb30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d7d2648-2ebd-4bb6-b2b7-9d375402bc7c", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996232482500, "endTime": 31996232533600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "2b7a817f-a603-4f3a-81b4-bc48eef5cddd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b7a817f-a603-4f3a-81b4-bc48eef5cddd", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996232482500, "endTime": 31996232533600}, "additional": {"logType": "info", "children": [], "durationId": "1d7d2648-2ebd-4bb6-b2b7-9d375402bc7c", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "bc09ecc7-58b6-4ab2-be96-6636bd474dcc", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996246785000, "endTime": 31996246805900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "3d7d1bb6-62b0-40f9-88ad-7643e51ab2ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d7d1bb6-62b0-40f9-88ad-7643e51ab2ed", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996246785000, "endTime": 31996246805900}, "additional": {"logType": "info", "children": [], "durationId": "bc09ecc7-58b6-4ab2-be96-6636bd474dcc", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "8b7e74aa-62c3-4ad0-9dbb-8d366d0cacce", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996246864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca6b077-d81b-4110-aae2-9044ea825f0a", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996256098700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8719a714-e9f7-4c24-a90e-938a401b67d0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996221816300, "endTime": 31996256562700}, "additional": {"logType": "info", "children": [], "durationId": "32bc6faa-1f43-4253-8b20-da2e1a7815f5", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "f09b54a7-a644-438b-a055-d68c11590b76", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996267250500, "endTime": 31996267259200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "cfff664a-4404-4956-a038-3514634a3084"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11bd3f9b-c0ed-4fa9-a689-50698f860e3b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996267279900, "endTime": 31996272157100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "dff0dbd5-1729-4616-a6f4-b72495396352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "056ca6fb-f7d2-4fc0-975c-865acf084fb6", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996272182700, "endTime": 31996402979300}, "additional": {"children": ["27d71f8d-8302-4d76-844a-d50b855e3aaf", "3a4fb60d-76b7-4580-98fa-becaa47b11d1", "537925d9-8c51-43ab-b443-b7562af415fc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "d2bd5d49-6019-483c-8f89-a4d1adbadbf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6ea51b8-d3c1-49cf-9c57-95a977088e67", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996403000600, "endTime": 31996443587700}, "additional": {"children": ["df243d39-4895-468f-9a93-319270823678"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "81cf66ae-61c5-4574-b842-cbdd5ec3afc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30cb2508-b566-4ede-aaf5-055b9b204111", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996443596300, "endTime": 31996627262200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "e0b49220-b5df-40aa-81f9-c6ecc9922443"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a43c042-3e47-4700-969b-52aac3993189", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996628219900, "endTime": 31996642312000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "46767e5b-6e8a-429e-9afb-d3a6c14d4ae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5402687-8a0a-407d-9949-5705dbbb9364", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996642373600, "endTime": 31996653413900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "959215ff-6ee0-4fad-9e68-a0128c08d3b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f79c457-3916-4489-97eb-568d948f033c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996653433800, "endTime": 31996653580500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "17009e09-13b4-46f5-b09e-e520d485eeb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfff664a-4404-4956-a038-3514634a3084", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996267250500, "endTime": 31996267259200}, "additional": {"logType": "info", "children": [], "durationId": "f09b54a7-a644-438b-a055-d68c11590b76", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "dff0dbd5-1729-4616-a6f4-b72495396352", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996267279900, "endTime": 31996272157100}, "additional": {"logType": "info", "children": [], "durationId": "11bd3f9b-c0ed-4fa9-a689-50698f860e3b", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "27d71f8d-8302-4d76-844a-d50b855e3aaf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996273059700, "endTime": 31996273083800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "056ca6fb-f7d2-4fc0-975c-865acf084fb6", "logId": "9f9c2896-de97-4c96-a231-cd4a74361b1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f9c2896-de97-4c96-a231-cd4a74361b1e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996273059700, "endTime": 31996273083800}, "additional": {"logType": "info", "children": [], "durationId": "27d71f8d-8302-4d76-844a-d50b855e3aaf", "parent": "d2bd5d49-6019-483c-8f89-a4d1adbadbf9"}}, {"head": {"id": "3a4fb60d-76b7-4580-98fa-becaa47b11d1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996277819500, "endTime": 31996401376900}, "additional": {"children": ["ac0117b0-fcd9-4d65-8748-0f2ccf2f2177", "cc015bfc-bfb3-42d4-bf0f-075f187cddd1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "056ca6fb-f7d2-4fc0-975c-865acf084fb6", "logId": "3f135c46-d57e-4e08-878d-d1fd9c64da93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac0117b0-fcd9-4d65-8748-0f2ccf2f2177", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996277821800, "endTime": 31996286548700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a4fb60d-76b7-4580-98fa-becaa47b11d1", "logId": "ed51b140-e9e8-4195-a6c2-2d3191c5a634"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc015bfc-bfb3-42d4-bf0f-075f187cddd1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996286567300, "endTime": 31996401354800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a4fb60d-76b7-4580-98fa-becaa47b11d1", "logId": "6cca39d0-d3de-4412-b527-9fbd7cb2a13e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c882c60-01ad-4aa3-8879-a56f2465df1e", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996277827100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dab764e-208d-48d4-9c2b-c37d637322f5", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996286133500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed51b140-e9e8-4195-a6c2-2d3191c5a634", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996277821800, "endTime": 31996286548700}, "additional": {"logType": "info", "children": [], "durationId": "ac0117b0-fcd9-4d65-8748-0f2ccf2f2177", "parent": "3f135c46-d57e-4e08-878d-d1fd9c64da93"}}, {"head": {"id": "15144062-9402-4381-a52f-93193adb0135", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996286585000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1b03f2-1e34-48b2-a888-75be14a6cb60", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996294554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7df29c7a-a20b-4f8a-bbf2-5c96a024da4c", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996294705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243ea113-788c-4315-a8dd-1f2efd22453b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996294855100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ada3e4d-45ce-43e1-9e65-d0c5585ddc2e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996294948700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "227e11a9-f2e7-4f5b-9159-9bee2530a0b0", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996297825500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab1277f-7716-4569-a12a-d80baacaed4c", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996303208300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d05377-4f4b-47c8-9258-3cf2fa0f265f", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996336756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdbb7ba4-26b2-42b7-bb30-aa01b63dbc33", "name": "Sdk init in 59 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996369556300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b24da73-5e9b-47b9-8750-9dfb70ab4c69", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996369779100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 22}, "markType": "other"}}, {"head": {"id": "e1d66bdf-9c6e-41aa-a869-a377b914bc25", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996369842400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 22}, "markType": "other"}}, {"head": {"id": "4b98f453-32d1-4732-98cb-eb2c5625c739", "name": "Project task initialization takes 30 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996400542500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9072fd36-00ed-42f8-92e7-dcbb45e10151", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996401075600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85a47cce-75dd-42ad-90a3-9ada9633d92e", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996401235200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "009bc3ba-9c32-44c3-ba95-cd3be76fc73b", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996401300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cca39d0-d3de-4412-b527-9fbd7cb2a13e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996286567300, "endTime": 31996401354800}, "additional": {"logType": "info", "children": [], "durationId": "cc015bfc-bfb3-42d4-bf0f-075f187cddd1", "parent": "3f135c46-d57e-4e08-878d-d1fd9c64da93"}}, {"head": {"id": "3f135c46-d57e-4e08-878d-d1fd9c64da93", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996277819500, "endTime": 31996401376900}, "additional": {"logType": "info", "children": ["ed51b140-e9e8-4195-a6c2-2d3191c5a634", "6cca39d0-d3de-4412-b527-9fbd7cb2a13e"], "durationId": "3a4fb60d-76b7-4580-98fa-becaa47b11d1", "parent": "d2bd5d49-6019-483c-8f89-a4d1adbadbf9"}}, {"head": {"id": "537925d9-8c51-43ab-b443-b7562af415fc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996402927000, "endTime": 31996402959700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "056ca6fb-f7d2-4fc0-975c-865acf084fb6", "logId": "6d2d3c99-41a3-4d21-8468-65370b0c5466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6d2d3c99-41a3-4d21-8468-65370b0c5466", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996402927000, "endTime": 31996402959700}, "additional": {"logType": "info", "children": [], "durationId": "537925d9-8c51-43ab-b443-b7562af415fc", "parent": "d2bd5d49-6019-483c-8f89-a4d1adbadbf9"}}, {"head": {"id": "d2bd5d49-6019-483c-8f89-a4d1adbadbf9", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996272182700, "endTime": 31996402979300}, "additional": {"logType": "info", "children": ["9f9c2896-de97-4c96-a231-cd4a74361b1e", "3f135c46-d57e-4e08-878d-d1fd9c64da93", "6d2d3c99-41a3-4d21-8468-65370b0c5466"], "durationId": "056ca6fb-f7d2-4fc0-975c-865acf084fb6", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "df243d39-4895-468f-9a93-319270823678", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996404453700, "endTime": 31996443572900}, "additional": {"children": ["09092bea-eadc-4834-bb05-7f1db4b8a8bd", "417fa006-11cd-4f9d-ad38-25f949f3c44d", "2744dbd4-0048-4e12-aa5f-9a528a9922a7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a6ea51b8-d3c1-49cf-9c57-95a977088e67", "logId": "d65ce9fd-c1fa-469c-adf6-387f45341894"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09092bea-eadc-4834-bb05-7f1db4b8a8bd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996409757900, "endTime": 31996409776900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df243d39-4895-468f-9a93-319270823678", "logId": "dfad0765-26b8-446a-8ab3-035dc3d65d28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfad0765-26b8-446a-8ab3-035dc3d65d28", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996409757900, "endTime": 31996409776900}, "additional": {"logType": "info", "children": [], "durationId": "09092bea-eadc-4834-bb05-7f1db4b8a8bd", "parent": "d65ce9fd-c1fa-469c-adf6-387f45341894"}}, {"head": {"id": "417fa006-11cd-4f9d-ad38-25f949f3c44d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996411436400, "endTime": 31996442013000}, "additional": {"children": ["79267814-205c-492b-99d6-503203c117c5", "44f0ffdf-b57f-445f-ae03-6fbf89018567"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df243d39-4895-468f-9a93-319270823678", "logId": "dd55ed02-c20f-4aa8-ab03-cce84e6a3231"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79267814-205c-492b-99d6-503203c117c5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996411437700, "endTime": 31996417153600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417fa006-11cd-4f9d-ad38-25f949f3c44d", "logId": "0cb0391f-305e-4bab-9366-897d2cd081a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44f0ffdf-b57f-445f-ae03-6fbf89018567", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996417183100, "endTime": 31996441995200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "417fa006-11cd-4f9d-ad38-25f949f3c44d", "logId": "20131171-edbd-4081-8d6e-f383139cd7b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f245d019-d424-4c65-a68b-d7f5346a99cb", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996411441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24994e98-0be1-4073-bcdf-d00dd2e8f777", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996416845400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb0391f-305e-4bab-9366-897d2cd081a7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996411437700, "endTime": 31996417153600}, "additional": {"logType": "info", "children": [], "durationId": "79267814-205c-492b-99d6-503203c117c5", "parent": "dd55ed02-c20f-4aa8-ab03-cce84e6a3231"}}, {"head": {"id": "30829eaa-ca9f-46df-b7e8-f9f22ff45901", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996417203100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9c3b00-22d9-4641-a6ad-16783286f59a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996426721200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "978788a3-2aff-4883-85e3-a0717cf90590", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996426892100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6837a8df-4e0e-41eb-bb11-6ba547bb136f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996427283500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ae9b00-45d3-4cc5-a32b-3e1122850008", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996427539100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ebacc49-60d5-4fc0-b3d3-0561e8716c96", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996427652700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34ffa70-922f-4d16-9177-3c0e00106b8d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996427786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a46d3283-1161-4bef-9926-6975f7d0fc01", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996428048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90982de8-090d-40d9-8de4-4494d499a3b8", "name": "Module entry task initialization takes 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996440496800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626feb2e-d9cb-4dcd-a10a-8637eed0293b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996441723500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b48524e-202f-4774-ac53-60ef96ee5f58", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996441881200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4c5ae4d-6cda-4cf7-a432-8b1fa61a045e", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996441942200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20131171-edbd-4081-8d6e-f383139cd7b8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996417183100, "endTime": 31996441995200}, "additional": {"logType": "info", "children": [], "durationId": "44f0ffdf-b57f-445f-ae03-6fbf89018567", "parent": "dd55ed02-c20f-4aa8-ab03-cce84e6a3231"}}, {"head": {"id": "dd55ed02-c20f-4aa8-ab03-cce84e6a3231", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996411436400, "endTime": 31996442013000}, "additional": {"logType": "info", "children": ["0cb0391f-305e-4bab-9366-897d2cd081a7", "20131171-edbd-4081-8d6e-f383139cd7b8"], "durationId": "417fa006-11cd-4f9d-ad38-25f949f3c44d", "parent": "d65ce9fd-c1fa-469c-adf6-387f45341894"}}, {"head": {"id": "2744dbd4-0048-4e12-aa5f-9a528a9922a7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996443542400, "endTime": 31996443558600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "df243d39-4895-468f-9a93-319270823678", "logId": "d1246ae7-efde-4a2f-a2e0-adf1f1bb8c20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1246ae7-efde-4a2f-a2e0-adf1f1bb8c20", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996443542400, "endTime": 31996443558600}, "additional": {"logType": "info", "children": [], "durationId": "2744dbd4-0048-4e12-aa5f-9a528a9922a7", "parent": "d65ce9fd-c1fa-469c-adf6-387f45341894"}}, {"head": {"id": "d65ce9fd-c1fa-469c-adf6-387f45341894", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996404453700, "endTime": 31996443572900}, "additional": {"logType": "info", "children": ["dfad0765-26b8-446a-8ab3-035dc3d65d28", "dd55ed02-c20f-4aa8-ab03-cce84e6a3231", "d1246ae7-efde-4a2f-a2e0-adf1f1bb8c20"], "durationId": "df243d39-4895-468f-9a93-319270823678", "parent": "81cf66ae-61c5-4574-b842-cbdd5ec3afc9"}}, {"head": {"id": "81cf66ae-61c5-4574-b842-cbdd5ec3afc9", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996403000600, "endTime": 31996443587700}, "additional": {"logType": "info", "children": ["d65ce9fd-c1fa-469c-adf6-387f45341894"], "durationId": "a6ea51b8-d3c1-49cf-9c57-95a977088e67", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "691e887e-c5a0-483a-b6d1-0d7daa86ead7", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996487184000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a2d7629-e54a-45c9-a7fe-4d3435b0403d", "name": "hvigorfile, resolve hvigorfile dependencies in 184 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996627126800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b49220-b5df-40aa-81f9-c6ecc9922443", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996443596300, "endTime": 31996627262200}, "additional": {"logType": "info", "children": [], "durationId": "30cb2508-b566-4ede-aaf5-055b9b204111", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "cd4ab011-ae48-4933-b75b-a55bf97c5352", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996627975000, "endTime": 31996628206300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "logId": "53255333-2ca4-4782-a8b0-a8625d0747e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d70dba9-fbf4-425d-b76b-19805393b7f8", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996628008900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53255333-2ca4-4782-a8b0-a8625d0747e4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996627975000, "endTime": 31996628206300}, "additional": {"logType": "info", "children": [], "durationId": "cd4ab011-ae48-4933-b75b-a55bf97c5352", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "b0dc1eee-efa9-4a8f-965e-866a1f9aba1c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996629415100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28b08bc9-1e3e-446e-9f73-8b741dd0b942", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996639809900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46767e5b-6e8a-429e-9afb-d3a6c14d4ae3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996628219900, "endTime": 31996642312000}, "additional": {"logType": "info", "children": [], "durationId": "7a43c042-3e47-4700-969b-52aac3993189", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "42a2fab9-9c36-46b4-99d5-d2e3fb8fca75", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996647550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84a59f9-51c0-432c-9098-d4369c6d61da", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996647726300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9acbde9d-711d-44db-a929-79410bdc2c95", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996650034900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "883a82f0-41e9-4938-bf0f-63a7a929a75f", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996650172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959215ff-6ee0-4fad-9e68-a0128c08d3b9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996642373600, "endTime": 31996653413900}, "additional": {"logType": "info", "children": [], "durationId": "a5402687-8a0a-407d-9949-5705dbbb9364", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "9d501263-fff3-4f8a-8b7e-ee3ba64059df", "name": "Configuration phase cost:387 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996653454900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17009e09-13b4-46f5-b09e-e520d485eeb5", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996653433800, "endTime": 31996653580500}, "additional": {"logType": "info", "children": [], "durationId": "5f79c457-3916-4489-97eb-568d948f033c", "parent": "8325262b-2ace-41d8-b519-60928c7f630e"}}, {"head": {"id": "8325262b-2ace-41d8-b519-60928c7f630e", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996256581800, "endTime": 31996653591800}, "additional": {"logType": "info", "children": ["cfff664a-4404-4956-a038-3514634a3084", "dff0dbd5-1729-4616-a6f4-b72495396352", "d2bd5d49-6019-483c-8f89-a4d1adbadbf9", "81cf66ae-61c5-4574-b842-cbdd5ec3afc9", "e0b49220-b5df-40aa-81f9-c6ecc9922443", "46767e5b-6e8a-429e-9afb-d3a6c14d4ae3", "959215ff-6ee0-4fad-9e68-a0128c08d3b9", "17009e09-13b4-46f5-b09e-e520d485eeb5", "53255333-2ca4-4782-a8b0-a8625d0747e4"], "durationId": "e9d6ab82-e87c-4dbc-aaed-4c0e2623a54a", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "0c1e7f55-34a2-4bad-ba2c-0791df80d37a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996654899200, "endTime": 31996654919000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b8506-7444-41b2-b714-486c30144778", "logId": "4326ac4e-b7cd-40cf-ba02-39db7e3b54ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4326ac4e-b7cd-40cf-ba02-39db7e3b54ec", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996654899200, "endTime": 31996654919000}, "additional": {"logType": "info", "children": [], "durationId": "0c1e7f55-34a2-4bad-ba2c-0791df80d37a", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "3aaab128-c625-4c79-8aa6-c8d0063056f0", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996653631300, "endTime": 31996654931100}, "additional": {"logType": "info", "children": [], "durationId": "51249386-86bc-4420-bf41-8561be1b8484", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "14997c2e-03c0-40c4-afd7-ffa32ef1cb30", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996654935500, "endTime": 31996654936500}, "additional": {"logType": "info", "children": [], "durationId": "cabed5aa-2af1-47b3-9a46-a042adebd667", "parent": "e792c8e4-916d-4261-aa9b-4e66e2a417e9"}}, {"head": {"id": "e792c8e4-916d-4261-aa9b-4e66e2a417e9", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996221813100, "endTime": 31996654942800}, "additional": {"logType": "info", "children": ["8719a714-e9f7-4c24-a90e-938a401b67d0", "8325262b-2ace-41d8-b519-60928c7f630e", "3aaab128-c625-4c79-8aa6-c8d0063056f0", "14997c2e-03c0-40c4-afd7-ffa32ef1cb30", "2b7a817f-a603-4f3a-81b4-bc48eef5cddd", "3d7d1bb6-62b0-40f9-88ad-7643e51ab2ed", "4326ac4e-b7cd-40cf-ba02-39db7e3b54ec"], "durationId": "968b8506-7444-41b2-b714-486c30144778"}}, {"head": {"id": "e233a999-080c-4db1-ba0c-553f4ed44e92", "name": "Configuration task cost before running: 440 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996655302700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0749d23-c230-4938-868f-be4b55635d21", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996661694600, "endTime": 31996667797100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "70b62a0b-0292-4efe-8a12-976ac741c051", "logId": "5bfd6af0-fa81-4ac5-b181-c4045edc463d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70b62a0b-0292-4efe-8a12-976ac741c051", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996657911100}, "additional": {"logType": "detail", "children": [], "durationId": "a0749d23-c230-4938-868f-be4b55635d21"}}, {"head": {"id": "84635579-49bf-45dc-a021-b78e4d8f954b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996658851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58369bcf-6e0d-4887-9c4f-caa7fc668b34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996659010300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70324d45-19a4-4c4f-ba9a-186c75f8a305", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996661707900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0329e48-c727-4a60-b5d7-383c3b394d6e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996667586500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777794d6-a49e-42ed-85ce-25ade1ce2cd5", "name": "entry : default@PreBuild cost memory 0.31655120849609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996667720400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bfd6af0-fa81-4ac5-b181-c4045edc463d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996661694600, "endTime": 31996667797100}, "additional": {"logType": "info", "children": [], "durationId": "a0749d23-c230-4938-868f-be4b55635d21"}}, {"head": {"id": "6580946a-ccae-4c4f-9fd7-296d5d53e02f", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996671987600, "endTime": 31996678601900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9dc71e87-6eff-446a-affd-0b7d410c9c8d", "logId": "02779af2-cd39-4f69-8cbb-0e31e2c2f515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dc71e87-6eff-446a-affd-0b7d410c9c8d", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996670590800}, "additional": {"logType": "detail", "children": [], "durationId": "6580946a-ccae-4c4f-9fd7-296d5d53e02f"}}, {"head": {"id": "9e6e47de-e3aa-472c-825d-0f8caa140452", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996671123500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f1ad8b6-4277-47b2-9263-b4912413bf05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996671240400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5506de1-eb94-4af1-8eb2-09417846b27e", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996671998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a74262f-3586-4c0c-b439-9a83718aa586", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996676771800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8008e95a-e778-44b0-9f4c-64a519a709be", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996678410100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be6ac9fd-ca66-462d-a294-20966043aee6", "name": "entry : default@GenerateMetadata cost memory 0.0945587158203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996678531400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02779af2-cd39-4f69-8cbb-0e31e2c2f515", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996671987600, "endTime": 31996678601900}, "additional": {"logType": "info", "children": [], "durationId": "6580946a-ccae-4c4f-9fd7-296d5d53e02f"}}, {"head": {"id": "fef3e23d-a5f7-44a5-a3aa-b0c4eff9745b", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680773700, "endTime": 31996681277000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f6b7c85c-a851-47e0-8261-eb0adb478d27", "logId": "c14e276f-fbc0-4f4e-a729-399f388a5e28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6b7c85c-a851-47e0-8261-eb0adb478d27", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680132500}, "additional": {"logType": "detail", "children": [], "durationId": "fef3e23d-a5f7-44a5-a3aa-b0c4eff9745b"}}, {"head": {"id": "19320741-6c64-4b00-abc1-c9a22ac9f936", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "210e37d2-2704-4cfa-bb40-8c130f270a2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be6b375c-5bc4-43b9-bff7-0e67b930db80", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680782600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b7533ac-8ec8-4850-a992-91371ebdc970", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b05a2ac-91e5-46da-8a05-9363d93833c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd404dc3-242c-4098-af9e-6485a0ffa696", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996681064300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5135728f-7700-4545-b91f-34ec08e47439", "name": "runTaskFromQueue task cost before running: 466 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996681202700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14e276f-fbc0-4f4e-a729-399f388a5e28", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996680773700, "endTime": 31996681277000, "totalTime": 399800}, "additional": {"logType": "info", "children": [], "durationId": "fef3e23d-a5f7-44a5-a3aa-b0c4eff9745b"}}, {"head": {"id": "afd2c826-2685-4b79-9b55-4e161487a3fc", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996685330400, "endTime": 31996689884200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1797c320-8bc9-4d12-b40f-92a095df0063", "logId": "54bb7973-164f-4ceb-ad7c-84b6c5308d81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1797c320-8bc9-4d12-b40f-92a095df0063", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996683753000}, "additional": {"logType": "detail", "children": [], "durationId": "afd2c826-2685-4b79-9b55-4e161487a3fc"}}, {"head": {"id": "143eaa0b-51fc-4039-a33f-b4e619e36851", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996684201700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2e06b19-fd36-4d8c-9744-d1145bd7d591", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996684321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a52798e-cc9f-4999-bba8-0b5d915f00dd", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996685344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f3c0508-c130-448a-9346-3ec9972c264d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996689471600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7471cb78-44ad-4708-be87-7746b7e58d5a", "name": "entry : default@MergeProfile cost memory 0.106201171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996689748500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54bb7973-164f-4ceb-ad7c-84b6c5308d81", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996685330400, "endTime": 31996689884200}, "additional": {"logType": "info", "children": [], "durationId": "afd2c826-2685-4b79-9b55-4e161487a3fc"}}, {"head": {"id": "e839b3bb-59b1-4ebb-9480-e1a44a1f65f6", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996695031200, "endTime": 31996697057000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6fc4f433-1d81-4e35-a347-2e2640541c3b", "logId": "c2909432-8236-4331-b8b3-da6055db52b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fc4f433-1d81-4e35-a347-2e2640541c3b", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996693387700}, "additional": {"logType": "detail", "children": [], "durationId": "e839b3bb-59b1-4ebb-9480-e1a44a1f65f6"}}, {"head": {"id": "6a3a0313-e581-4a53-9004-37c4b8d4c3a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996693771600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d5fc64-67a4-4369-996c-bb9d4e9b78cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996693887200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a7a0cd6-abd0-4007-94fd-cad43cba9204", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996695044100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b07ad0e9-f999-4ec2-8683-0088504bbc39", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996695907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e662eef-e236-4b6c-9abd-d029589603ed", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996696887100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f9c8fe-08f8-440a-bc2f-66b13e07656c", "name": "entry : default@CreateBuildProfile cost memory 0.10181427001953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996696988000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2909432-8236-4331-b8b3-da6055db52b0", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996695031200, "endTime": 31996697057000}, "additional": {"logType": "info", "children": [], "durationId": "e839b3bb-59b1-4ebb-9480-e1a44a1f65f6"}}, {"head": {"id": "b6a94d6f-fa80-475e-a595-5dc3623a6a30", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701400300, "endTime": 31996701785900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "50771805-08e7-4dca-9460-988a3903418b", "logId": "9b33737e-487d-43a2-bbc7-b48cdefeb0f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50771805-08e7-4dca-9460-988a3903418b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996700042000}, "additional": {"logType": "detail", "children": [], "durationId": "b6a94d6f-fa80-475e-a595-5dc3623a6a30"}}, {"head": {"id": "19489783-67e4-45bd-a6bc-416a48a5ad15", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996700467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67c0a5e2-caf9-4e62-a6f0-89ac3fa9f9b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996700565700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "989d2429-6543-45df-bb4a-c384d469efcd", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701410100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4ff791d-2815-4d74-a90d-8269e4e709dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701539000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8743af8a-0016-4246-a805-9771e0831a33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701594000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbb3845c-ced5-4bc5-accb-7d4af4096651", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701667800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3f5ad2-cdc0-408e-9b35-28a2f5ac2775", "name": "runTaskFromQueue task cost before running: 486 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701736700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b33737e-487d-43a2-bbc7-b48cdefeb0f3", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996701400300, "endTime": 31996701785900, "totalTime": 320400}, "additional": {"logType": "info", "children": [], "durationId": "b6a94d6f-fa80-475e-a595-5dc3623a6a30"}}, {"head": {"id": "1c669be3-9784-4b0a-81e5-0d4b35c923b4", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996707648400, "endTime": 31996708585000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4198cb0f-6ccb-412e-8df9-124121f54633", "logId": "c9a18fe1-46ae-4f5d-9651-60e8b84e17f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4198cb0f-6ccb-412e-8df9-124121f54633", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996703299600}, "additional": {"logType": "detail", "children": [], "durationId": "1c669be3-9784-4b0a-81e5-0d4b35c923b4"}}, {"head": {"id": "8534a544-b00d-4ba0-913c-8aeb972e6ce9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996703727600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0cc73e7-0aab-4cfc-a384-5d01b56c7760", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996703835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d1a4d8-9d5e-4bc2-acf2-f7dc1c94a60b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996707663500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c479eee0-9646-44b1-b19f-11a7acbe3813", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996707951700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef99df2c-7bd9-4eeb-8801-2e6207c7db42", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03856658935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996708244500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39658541-92ba-4991-8a1d-e6d07ac7e112", "name": "runTaskFromQueue task cost before running: 493 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996708497000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9a18fe1-46ae-4f5d-9651-60e8b84e17f1", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996707648400, "endTime": 31996708585000, "totalTime": 814200}, "additional": {"logType": "info", "children": [], "durationId": "1c669be3-9784-4b0a-81e5-0d4b35c923b4"}}, {"head": {"id": "16786a2d-b012-4c8b-8542-66e8d09134a4", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996714098800, "endTime": 31996716091400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "a37ef184-db8e-416a-b522-094074922838", "logId": "69db543f-7cd2-47d1-817e-34726cc5ba68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a37ef184-db8e-416a-b522-094074922838", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996710540100}, "additional": {"logType": "detail", "children": [], "durationId": "16786a2d-b012-4c8b-8542-66e8d09134a4"}}, {"head": {"id": "3ff9d6f5-a2f5-4ed9-a180-e72f59743d58", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996711259000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c549dcc0-b54a-41ff-a667-9c6100bfaffb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996711384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e94fee-1152-4edd-b9ef-42bdf1d1a5cc", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996714113500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd78b65-d66e-439e-bd34-fa71c7a02230", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996715647400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0027d856-1694-464e-aaf2-e1e262354e3a", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996715756700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dc93bdf-8c6a-4cc0-9fb9-987b6844fa32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996715834400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e05833-9e16-472e-801a-f0470746dcac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996715882800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe81e50-0815-4812-b143-b19a8dc8b96a", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1171417236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996715953200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17856758-8535-44b2-82d9-a577f8491eac", "name": "runTaskFromQueue task cost before running: 501 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996716036700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69db543f-7cd2-47d1-817e-34726cc5ba68", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996714098800, "endTime": 31996716091400, "totalTime": 1909600}, "additional": {"logType": "info", "children": [], "durationId": "16786a2d-b012-4c8b-8542-66e8d09134a4"}}, {"head": {"id": "c0f26adc-70dd-4e76-bdf8-07c2f9c07e98", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720233200, "endTime": 31996720791000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4d780d78-488a-46a8-8458-b50abe37e317", "logId": "39aa1b3f-f1ad-40c3-95ce-ccd589424785"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d780d78-488a-46a8-8458-b50abe37e317", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996719019700}, "additional": {"logType": "detail", "children": [], "durationId": "c0f26adc-70dd-4e76-bdf8-07c2f9c07e98"}}, {"head": {"id": "99078285-8c73-4a07-adf8-421a6928945b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996719368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f51a97-38b9-4dbf-b662-786ac3045538", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996719459500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "611355a4-ae55-4dd4-affa-4817fa01934a", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720245800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f72245-16bc-4b20-889a-7fc9c3d7fd20", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720401200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "144cd08c-3f53-437c-a586-82c59df516a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720511400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28dc46c2-eb90-416b-b42e-e918305073ce", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c233fa4b-2547-4082-9aac-27d4596518ef", "name": "runTaskFromQueue task cost before running: 505 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720699800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39aa1b3f-f1ad-40c3-95ce-ccd589424785", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996720233200, "endTime": 31996720791000, "totalTime": 424600}, "additional": {"logType": "info", "children": [], "durationId": "c0f26adc-70dd-4e76-bdf8-07c2f9c07e98"}}, {"head": {"id": "43924754-d89b-429a-98fc-c319fddbabbb", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996724401600, "endTime": 31996727537000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a2d6d034-9d99-4625-a25e-6fdb98b6fbc3", "logId": "d88d7adf-a6bf-4547-8707-adc18934d86c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2d6d034-9d99-4625-a25e-6fdb98b6fbc3", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996722485800}, "additional": {"logType": "detail", "children": [], "durationId": "43924754-d89b-429a-98fc-c319fddbabbb"}}, {"head": {"id": "afadbba2-8fbe-48a5-b0ea-7c7f8cf48c6d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996722945200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc734349-8f8f-4470-9a49-e1c74c30a069", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996723039200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "420cc40f-0470-4aa5-9016-cf598c73cdaf", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996724415200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d05d1d68-a54f-4c22-8e3c-f703b3d7cb96", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996727361700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0da1166-f8c3-4dd5-9417-e1f6072f7b26", "name": "entry : default@MakePackInfo cost memory 0.13921356201171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996727474200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d88d7adf-a6bf-4547-8707-adc18934d86c", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996724401600, "endTime": 31996727537000}, "additional": {"logType": "info", "children": [], "durationId": "43924754-d89b-429a-98fc-c319fddbabbb"}}, {"head": {"id": "225b806e-9fe0-4dc7-b198-620e1f1564cf", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996730827600, "endTime": 31996733789600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "f0f12f5f-7cca-494b-8b95-c0275e8b5c5d", "logId": "e74dc94d-3d20-4724-ba31-f20225b15b9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0f12f5f-7cca-494b-8b95-c0275e8b5c5d", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996729386100}, "additional": {"logType": "detail", "children": [], "durationId": "225b806e-9fe0-4dc7-b198-620e1f1564cf"}}, {"head": {"id": "e2d7bbe0-3e15-4ac2-9f01-e7bd35bed89f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996729723700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9802e2e7-917e-40f0-893f-72623ffe94a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996729813100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55cad34a-6466-4264-bb47-e24a600f86d2", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996730835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a063f6-ac01-4b4b-9768-c943dd725134", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996730964900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c8d1d5b-2e74-48b1-abd1-196a38215926", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996731689200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bac4be5b-aa02-4ac7-99df-2f137e67c6d7", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996733315700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f334182e-a6ab-43e0-9575-f70dc317a2bd", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996733445300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6e6e501-e10b-46cb-9796-f19eb143b44b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996733534600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0732cf1-71d9-4b31-a5d2-afd76333bee0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996733589200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6662c44e-bb28-481d-bd04-0afa46df1fab", "name": "entry : default@SyscapTransform cost memory 0.15120697021484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996733666700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce95fbc5-e5e3-4bd4-95c2-c3981b330df3", "name": "runTaskFromQueue task cost before running: 518 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996733738000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e74dc94d-3d20-4724-ba31-f20225b15b9d", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996730827600, "endTime": 31996733789600, "totalTime": 2894400}, "additional": {"logType": "info", "children": [], "durationId": "225b806e-9fe0-4dc7-b198-620e1f1564cf"}}, {"head": {"id": "6ea06fa4-6971-446b-ae03-cf7344b1f6b0", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996737564700, "endTime": 31996738869100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "912f9dfa-3dfc-4bdf-b694-32119121f4c4", "logId": "9c40f6b8-48dd-4bb7-9546-20ce779c0777"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "912f9dfa-3dfc-4bdf-b694-32119121f4c4", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996735629300}, "additional": {"logType": "detail", "children": [], "durationId": "6ea06fa4-6971-446b-ae03-cf7344b1f6b0"}}, {"head": {"id": "1548ac85-9deb-4488-893e-52aad81cc673", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996736148500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b60994e8-e95a-4ecb-b17a-16676e8e8bad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996736265300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3db0993e-4723-48a4-adaf-8882dd6a3cf7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996737575900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03df7192-2347-49d4-9bc8-2be4f2a11237", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996738597600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea26d0b2-0214-4bd3-a1fe-3bf8cbcdd475", "name": "entry : default@ProcessProfile cost memory 0.05995941162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996738774300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c40f6b8-48dd-4bb7-9546-20ce779c0777", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996737564700, "endTime": 31996738869100}, "additional": {"logType": "info", "children": [], "durationId": "6ea06fa4-6971-446b-ae03-cf7344b1f6b0"}}, {"head": {"id": "9bec8487-9475-4c5c-8779-af91baee449e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996744571000, "endTime": 31996750802200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c3871e60-135a-4682-8213-923aed0a7467", "logId": "6644fb75-f829-49fb-828f-e487b9a6cd15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3871e60-135a-4682-8213-923aed0a7467", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996742287200}, "additional": {"logType": "detail", "children": [], "durationId": "9bec8487-9475-4c5c-8779-af91baee449e"}}, {"head": {"id": "5adc9216-60d5-4326-86c8-b1be2afa70dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996742833400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "343ce046-24ae-4be3-bea5-9517a5c4afe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996742944100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8186d7b2-c27d-420c-aa32-20ae826fa523", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996744581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bb556c1-0fe7-4ca8-b977-4dd4e28814b3", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996750460100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc23c43-87ba-4503-a33c-6da4469b7f6c", "name": "entry : default@ProcessRouterMap cost memory 0.20333099365234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996750605100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6644fb75-f829-49fb-828f-e487b9a6cd15", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996744571000, "endTime": 31996750802200}, "additional": {"logType": "info", "children": [], "durationId": "9bec8487-9475-4c5c-8779-af91baee449e"}}, {"head": {"id": "a68001a4-cd66-447d-b5a6-a2a7fcd641fd", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996755592100, "endTime": 31996756924700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c7e92f25-b6d6-4dd7-8dd3-da2e026afbda", "logId": "20882e76-1a2b-423e-8dd3-6d9d7a4c9eef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7e92f25-b6d6-4dd7-8dd3-da2e026afbda", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996753888900}, "additional": {"logType": "detail", "children": [], "durationId": "a68001a4-cd66-447d-b5a6-a2a7fcd641fd"}}, {"head": {"id": "f3009231-dd64-4173-9f87-2a9d51413619", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996754489200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20a795c-7624-47ff-815c-64ee2bdb0914", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996754652800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a43f54-3867-4738-be77-ed296875ec70", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996755602100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5944c1f5-0119-4570-9bda-aaeea730070b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996755890200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663bec40-dbaf-4bbf-b67e-f2f0e8ede91e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996755968100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc426c75-119b-4ad7-bf79-2ee0e773fc62", "name": "entry : default@BuildNativeWithNinja cost memory 0.05695343017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996756580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f728c2b-8a8f-44c5-aff8-d1fb046a8215", "name": "runTaskFromQueue task cost before running: 541 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996756696500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20882e76-1a2b-423e-8dd3-6d9d7a4c9eef", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996755592100, "endTime": 31996756924700, "totalTime": 1072800}, "additional": {"logType": "info", "children": [], "durationId": "a68001a4-cd66-447d-b5a6-a2a7fcd641fd"}}, {"head": {"id": "0eeab9a1-7bdd-44ac-a021-2d148f4596bd", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996762377900, "endTime": 31996767245300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "abdce36f-29e6-4026-a23d-caa7a4808271", "logId": "0e4faada-f43e-4b72-a488-095209f49246"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abdce36f-29e6-4026-a23d-caa7a4808271", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996759946800}, "additional": {"logType": "detail", "children": [], "durationId": "0eeab9a1-7bdd-44ac-a021-2d148f4596bd"}}, {"head": {"id": "c96fa579-a280-43b0-88e5-b12622346af2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996760372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02f2e0a-f8b7-4807-a160-af57396be25b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996760475000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8045105c-5951-4aad-a97b-9ac4e9969cf6", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996761338900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f90d5c87-8a32-475b-8f33-ef61e0593a72", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996763994600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea9dff80-13cb-4482-b5a3-275a92caa3a0", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996765636100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1002cb70-aafa-4c7e-8161-6fbb23387bfd", "name": "entry : default@ProcessResource cost memory 0.1715087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996765740500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4faada-f43e-4b72-a488-095209f49246", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996762377900, "endTime": 31996767245300}, "additional": {"logType": "info", "children": [], "durationId": "0eeab9a1-7bdd-44ac-a021-2d148f4596bd"}}, {"head": {"id": "5afe8eb3-436e-4ce2-bb84-a3ee63f18139", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996778318000, "endTime": 31996797521800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "51fbd0ea-a556-4b3c-ae96-3d9d7c9e5530", "logId": "7d241968-1651-49df-b49e-b55923db6833"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51fbd0ea-a556-4b3c-ae96-3d9d7c9e5530", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996771022100}, "additional": {"logType": "detail", "children": [], "durationId": "5afe8eb3-436e-4ce2-bb84-a3ee63f18139"}}, {"head": {"id": "338a8a84-bdd2-4e22-be37-b59c1864fd37", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996771441300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2464cb1-dc7a-4f55-9b01-d64bf92c5ef1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996771546700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9374bc57-ade5-402d-b95f-47780d521054", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996778330400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce6c43ee-63a0-460a-add8-886374282375", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996797011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "421f9821-7c9d-4faf-bc00-405481305a19", "name": "entry : default@GenerateLoaderJson cost memory 0.7667922973632812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996797361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d241968-1651-49df-b49e-b55923db6833", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996778318000, "endTime": 31996797521800}, "additional": {"logType": "info", "children": [], "durationId": "5afe8eb3-436e-4ce2-bb84-a3ee63f18139"}}, {"head": {"id": "0295e7de-b8b8-4450-ac72-113b3fab7f0b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996807890200, "endTime": 31996813524200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2dbedb81-d627-4e84-b636-543daf8b25f6", "logId": "f90a2c2f-48e1-4e8b-9eba-6778d8f206ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dbedb81-d627-4e84-b636-543daf8b25f6", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996805630100}, "additional": {"logType": "detail", "children": [], "durationId": "0295e7de-b8b8-4450-ac72-113b3fab7f0b"}}, {"head": {"id": "d22b2ee0-fa37-44d2-9d93-ee0afa3e4958", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996806071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e51117f-9879-4162-a867-b717dd90fc49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996806195100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5398f4bf-6343-4f96-b6f0-dd08adb9d600", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996807928100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1443207-8987-4cae-bcb1-1791d886bc06", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996812075900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85fb9930-0ae3-44bb-a2a6-ae227cdb8504", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996812196500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce76c464-06cc-4f2e-85ac-fdbc28993318", "name": "entry : default@ProcessLibs cost memory 0.12569427490234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996813044600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fe7357-afe5-4455-b942-8cab115a6231", "name": "runTaskFromQueue task cost before running: 598 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996813368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f90a2c2f-48e1-4e8b-9eba-6778d8f206ea", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996807890200, "endTime": 31996813524200, "totalTime": 5456600}, "additional": {"logType": "info", "children": [], "durationId": "0295e7de-b8b8-4450-ac72-113b3fab7f0b"}}, {"head": {"id": "cb2e382b-e9ef-4398-b1a4-8cd01749e40e", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996823269200, "endTime": 31996862515400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5722892e-67ae-4f81-97e8-85981318d2b0", "logId": "ff974d5f-3d28-4e11-87df-5bc981246e09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5722892e-67ae-4f81-97e8-85981318d2b0", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996819180100}, "additional": {"logType": "detail", "children": [], "durationId": "cb2e382b-e9ef-4398-b1a4-8cd01749e40e"}}, {"head": {"id": "2ed62efd-5966-493b-8fd3-a0b9ce9ea175", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996819649400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1b722a-ff6e-48d2-a043-b228aae831c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996819758100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307ee17c-a388-4146-9de5-b463edacf11f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996820640700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b5fed0-a0b9-4162-bfae-efb3c0ac468d", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996823292300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6dbe13f-78e7-4f0a-81ee-90fbc8b9f007", "name": "Incremental task entry:default@CompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996862296900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c886626-0518-49e7-bc1c-b593063b7211", "name": "entry : default@CompileResource cost memory 1.4091720581054688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996862430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff974d5f-3d28-4e11-87df-5bc981246e09", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996823269200, "endTime": 31996862515400}, "additional": {"logType": "info", "children": [], "durationId": "cb2e382b-e9ef-4398-b1a4-8cd01749e40e"}}, {"head": {"id": "b15d8d2c-9f9b-413b-8421-109af0912d8e", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996868238900, "endTime": 31996869543300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2d96b175-b196-4da5-afd8-a3be2c8e43ef", "logId": "774b3d0b-8cfb-4275-a522-b028267d4a55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d96b175-b196-4da5-afd8-a3be2c8e43ef", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996865152500}, "additional": {"logType": "detail", "children": [], "durationId": "b15d8d2c-9f9b-413b-8421-109af0912d8e"}}, {"head": {"id": "295e84e7-6a99-4b04-b9f3-fa9dfdd07d76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996865642000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b90fc7b-09b6-4785-84b0-b4ed34b7230a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996865765100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "867a30e7-5353-46e9-82b9-823a2c0ebd09", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996868250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23bc2b0-97e7-488e-885f-c7d7386201de", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996868491900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2e1b99-568f-4c3a-8d35-93d1000656cb", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996869347300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d120be-c1b4-4661-9871-31d05b1a3286", "name": "entry : default@DoNativeStrip cost memory 0.0773468017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996869477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "774b3d0b-8cfb-4275-a522-b028267d4a55", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996868238900, "endTime": 31996869543300}, "additional": {"logType": "info", "children": [], "durationId": "b15d8d2c-9f9b-413b-8421-109af0912d8e"}}, {"head": {"id": "e7598f56-9427-4ee8-8222-c422d364074f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996880862600, "endTime": 31998915454600}, "additional": {"children": ["e3c207d0-2c44-4e46-9752-afe437f52373", "8f3c63e5-ea19-4e21-bece-656c7f337e74"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "3f7f93ab-7680-4ac8-985e-b9963f240b8a", "logId": "4ac9f41e-8f87-48e7-a4da-31d15b1e5cf3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f7f93ab-7680-4ac8-985e-b9963f240b8a", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996871405700}, "additional": {"logType": "detail", "children": [], "durationId": "e7598f56-9427-4ee8-8222-c422d364074f"}}, {"head": {"id": "acf3af5f-d634-4b34-b7e9-3880cf42296a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996871911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6f5c73e-51f8-4720-88b7-fab73a026ad9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996872024300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986a6e80-d8fc-4313-b09d-7e602875c2a3", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996880958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c1452a1-26c2-4ea3-a415-d5dd71bf137b", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996901276700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd81b06-be46-4c3c-bfcf-8941502c7a93", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996901482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649a8019-b63d-4730-9754-f5ad96ccf32e", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996917823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d388faa8-98d5-4b01-b52a-4c17beab40b9", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996919263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f7bc92-01b8-4a03-b97b-ee9ea6824cc4", "name": "default@CompileArkTS work[74] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996920407100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c207d0-2c44-4e46-9752-afe437f52373", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31997135954900, "endTime": 31998907184900}, "additional": {"children": ["9739cafc-6df8-4220-9775-5d87478af1f4", "140c37af-8b47-46ff-8db4-72740ecfc4f8", "5cbafc27-9969-4f43-943a-611cc0e3d02a", "13b2eaaa-0a6b-4000-bf42-463e79b01992", "4cb1eeb8-27cb-4ba2-ab1c-cea8e9ce27d9", "4bac4335-13a8-41df-acdd-34d12d01e642"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e7598f56-9427-4ee8-8222-c422d364074f", "logId": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d391328-7480-4f9b-993e-57374eb99d1e", "name": "default@CompileArkTS work[74] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996921286300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "685541a9-8315-4159-ae86-14ca80247152", "name": "default@CompileArkTS work[74] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996921384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a39e45ae-64ec-43ef-8af0-c5ad9612212b", "name": "CopyResources startTime: 31996921441400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996921443300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2bb2c70-4ed6-4b31-b1ad-db7e75d30865", "name": "default@CompileArkTS work[75] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996921495500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3c63e5-ea19-4e21-bece-656c7f337e74", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31998371234200, "endTime": 31998387847300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "e7598f56-9427-4ee8-8222-c422d364074f", "logId": "b0c8dd8b-b2ca-4ab6-aaac-2a3b56ac0e70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24110198-3627-4a78-83e6-830502db20ab", "name": "default@CompileArkTS work[75] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996922541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393bb3c5-8be9-44dd-a389-f03c864c6cb0", "name": "default@CompileArkTS work[75] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996922717200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2886ecb3-807b-4ca4-a353-4723cd83a9a5", "name": "entry : default@CompileArkTS cost memory 1.58685302734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996922862000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6851e1d8-6f54-4d95-957c-343e0b154561", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996932006600, "endTime": 31996936026500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "9ccd5e38-6cf0-4298-84df-086b72d6af0b", "logId": "a8e3f7a4-0172-4ffd-9ef7-f98d2608a6d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ccd5e38-6cf0-4298-84df-086b72d6af0b", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996927343200}, "additional": {"logType": "detail", "children": [], "durationId": "6851e1d8-6f54-4d95-957c-343e0b154561"}}, {"head": {"id": "0a6faecd-852f-42f0-bf7c-51f666be7ca9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996928109800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "902b33fc-38a8-45bd-9e16-60e743fe9e4e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996928223700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc2eb67-f8e7-4c52-bba9-fc4ac14b0485", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996932040500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "011c390b-c0bb-4a89-8c9d-8f7730180c31", "name": "entry : default@BuildJS cost memory 0.12712860107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996935539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8949e49-d522-463e-97c0-d77b6a33dc8a", "name": "runTaskFromQueue task cost before running: 720 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996935871900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e3f7a4-0172-4ffd-9ef7-f98d2608a6d2", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996932006600, "endTime": 31996936026500, "totalTime": 3744400}, "additional": {"logType": "info", "children": [], "durationId": "6851e1d8-6f54-4d95-957c-343e0b154561"}}, {"head": {"id": "3152e80f-eaa4-4e8b-9bcf-49a81f69cb98", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996941625600, "endTime": 31996943997400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f62a50d3-159d-4d3f-8e53-7987bbd79a1c", "logId": "a452dfb5-df13-411f-b9f1-80a7cbd5dd91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f62a50d3-159d-4d3f-8e53-7987bbd79a1c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996937732600}, "additional": {"logType": "detail", "children": [], "durationId": "3152e80f-eaa4-4e8b-9bcf-49a81f69cb98"}}, {"head": {"id": "ba5e7019-539d-41ea-af83-11c338ce840e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996938060100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726032cb-5107-4ddd-b813-2c00348e8f36", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996938152600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b8f30ad-25a3-4c02-94d3-2951f6e50779", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996941642900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460a8f8c-43fa-43f6-9d16-1223db0800a0", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996942760700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72cb47a2-b5ff-45c8-90a7-ce7b1b3abf58", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996943796100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b4a48e-221a-4b05-b451-a0f5e653d4f8", "name": "entry : default@CacheNativeLibs cost memory 0.08847808837890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996943919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a452dfb5-df13-411f-b9f1-80a7cbd5dd91", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996941625600, "endTime": 31996943997400}, "additional": {"logType": "info", "children": [], "durationId": "3152e80f-eaa4-4e8b-9bcf-49a81f69cb98"}}, {"head": {"id": "300963da-32f6-44b5-a04d-a82cc9e221dd", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997135236400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512d755c-e213-4a0d-b373-b3c47a901d4d", "name": "default@CompileArkTS work[74] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997135588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e583d5-608c-4a35-8a29-827a0fe222aa", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997135777200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f66eb5-40e1-4655-a939-72a1a15ec6d5", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997135841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a999d676-742a-4964-aac7-6a12547c0a03", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997135903100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e031a75e-1d4e-4cd0-a4f7-16579d3089f2", "name": "default@CompileArkTS work[75] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997136860400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d5575d0-b0a6-4d89-99c2-6d034d8f0a5b", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998388248300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2130cbda-ba60-4e02-9e01-4b85f3927e8e", "name": "CopyResources is end, endTime: 31998388408100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998388413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf74c0d-f9f1-473b-aa0c-baa3544a210b", "name": "default@CompileArkTS work[75] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998388741700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c8dd8b-b2ca-4ab6-aaac-2a3b56ac0e70", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31998371234200, "endTime": 31998387847300}, "additional": {"logType": "info", "children": [], "durationId": "8f3c63e5-ea19-4e21-bece-656c7f337e74", "parent": "4ac9f41e-8f87-48e7-a4da-31d15b1e5cf3"}}, {"head": {"id": "89e3729a-2526-4d35-a788-c5f9570f5449", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998717544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b08070-ea25-4fff-8272-063525f6cef4", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998908982200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9739cafc-6df8-4220-9775-5d87478af1f4", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31997136194500, "endTime": 31997140556900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e3c207d0-2c44-4e46-9752-afe437f52373", "logId": "a4185fc5-83fb-46e3-bfab-338ba95fd038"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4185fc5-83fb-46e3-bfab-338ba95fd038", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997136194500, "endTime": 31997140556900}, "additional": {"logType": "info", "children": [], "durationId": "9739cafc-6df8-4220-9775-5d87478af1f4", "parent": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}}, {"head": {"id": "140c37af-8b47-46ff-8db4-72740ecfc4f8", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31997140574300, "endTime": 31997140886200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e3c207d0-2c44-4e46-9752-afe437f52373", "logId": "08a3e269-f5f2-4e0b-a02d-4c26a5f7d895"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08a3e269-f5f2-4e0b-a02d-4c26a5f7d895", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997140574300, "endTime": 31997140886200}, "additional": {"logType": "info", "children": [], "durationId": "140c37af-8b47-46ff-8db4-72740ecfc4f8", "parent": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}}, {"head": {"id": "5cbafc27-9969-4f43-943a-611cc0e3d02a", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31997140948700, "endTime": 31997140988600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e3c207d0-2c44-4e46-9752-afe437f52373", "logId": "51647b11-a036-4fd7-a279-4af01dddc963"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51647b11-a036-4fd7-a279-4af01dddc963", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997140948700, "endTime": 31997140988600}, "additional": {"logType": "info", "children": [], "durationId": "5cbafc27-9969-4f43-943a-611cc0e3d02a", "parent": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}}, {"head": {"id": "13b2eaaa-0a6b-4000-bf42-463e79b01992", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31997141009900, "endTime": 31998827881400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e3c207d0-2c44-4e46-9752-afe437f52373", "logId": "d68f459d-9c10-4047-8262-7e5ac83f110f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d68f459d-9c10-4047-8262-7e5ac83f110f", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31997141009900, "endTime": 31998827881400}, "additional": {"logType": "info", "children": [], "durationId": "13b2eaaa-0a6b-4000-bf42-463e79b01992", "parent": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}}, {"head": {"id": "4cb1eeb8-27cb-4ba2-ab1c-cea8e9ce27d9", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998827899400, "endTime": 31998836222900}, "additional": {"children": ["d13171ae-50ed-4d8d-80f5-2c4f1d56c2ec", "a5c64fbe-bd7b-421f-9f74-5f1086298ad0", "3311846e-02bf-451b-a2c7-4666441dc784"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e3c207d0-2c44-4e46-9752-afe437f52373", "logId": "18cc299b-a8b4-431e-98e5-fb2161626f16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18cc299b-a8b4-431e-98e5-fb2161626f16", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998827899400, "endTime": 31998836222900}, "additional": {"logType": "info", "children": ["0ae62a11-e8f4-4290-a58a-4fcba3b5e3bd", "d116293e-eec0-49d6-ab69-aa192e7e3351", "52f10cb5-7e05-4a83-b0b9-908aeea4c783"], "durationId": "4cb1eeb8-27cb-4ba2-ab1c-cea8e9ce27d9", "parent": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}}, {"head": {"id": "d13171ae-50ed-4d8d-80f5-2c4f1d56c2ec", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998827912300, "endTime": 31998827917000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4cb1eeb8-27cb-4ba2-ab1c-cea8e9ce27d9", "logId": "0ae62a11-e8f4-4290-a58a-4fcba3b5e3bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ae62a11-e8f4-4290-a58a-4fcba3b5e3bd", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998827912300, "endTime": 31998827917000}, "additional": {"logType": "info", "children": [], "durationId": "d13171ae-50ed-4d8d-80f5-2c4f1d56c2ec", "parent": "18cc299b-a8b4-431e-98e5-fb2161626f16"}}, {"head": {"id": "a5c64fbe-bd7b-421f-9f74-5f1086298ad0", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998827920100, "endTime": 31998832785300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4cb1eeb8-27cb-4ba2-ab1c-cea8e9ce27d9", "logId": "d116293e-eec0-49d6-ab69-aa192e7e3351"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d116293e-eec0-49d6-ab69-aa192e7e3351", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998827920100, "endTime": 31998832785300}, "additional": {"logType": "info", "children": [], "durationId": "a5c64fbe-bd7b-421f-9f74-5f1086298ad0", "parent": "18cc299b-a8b4-431e-98e5-fb2161626f16"}}, {"head": {"id": "3311846e-02bf-451b-a2c7-4666441dc784", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998832789400, "endTime": 31998836210800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4cb1eeb8-27cb-4ba2-ab1c-cea8e9ce27d9", "logId": "52f10cb5-7e05-4a83-b0b9-908aeea4c783"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52f10cb5-7e05-4a83-b0b9-908aeea4c783", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998832789400, "endTime": 31998836210800}, "additional": {"logType": "info", "children": [], "durationId": "3311846e-02bf-451b-a2c7-4666441dc784", "parent": "18cc299b-a8b4-431e-98e5-fb2161626f16"}}, {"head": {"id": "4bac4335-13a8-41df-acdd-34d12d01e642", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998836238000, "endTime": 31998906994100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e3c207d0-2c44-4e46-9752-afe437f52373", "logId": "e6fb3562-6330-4cef-a76e-a6e8e5e56cf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6fb3562-6330-4cef-a76e-a6e8e5e56cf6", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998836238000, "endTime": 31998906994100}, "additional": {"logType": "info", "children": [], "durationId": "4bac4335-13a8-41df-acdd-34d12d01e642", "parent": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2"}}, {"head": {"id": "ef900760-be91-4e0e-9c1f-9cb2f3d471d0", "name": "default@CompileArkTS work[74] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998915239900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31997135954900, "endTime": 31998907184900}, "additional": {"logType": "info", "children": ["a4185fc5-83fb-46e3-bfab-338ba95fd038", "08a3e269-f5f2-4e0b-a02d-4c26a5f7d895", "51647b11-a036-4fd7-a279-4af01dddc963", "d68f459d-9c10-4047-8262-7e5ac83f110f", "18cc299b-a8b4-431e-98e5-fb2161626f16", "e6fb3562-6330-4cef-a76e-a6e8e5e56cf6"], "durationId": "e3c207d0-2c44-4e46-9752-afe437f52373", "parent": "4ac9f41e-8f87-48e7-a4da-31d15b1e5cf3"}}, {"head": {"id": "acadf977-9c49-4dcc-bb9e-78eb80672182", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998915381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac9f41e-8f87-48e7-a4da-31d15b1e5cf3", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996880862600, "endTime": 31998915454600, "totalTime": 1813294700}, "additional": {"logType": "info", "children": ["6d6ca6be-7cc1-44ea-a2e4-1eecd1a152c2", "b0c8dd8b-b2ca-4ab6-aaac-2a3b56ac0e70"], "durationId": "e7598f56-9427-4ee8-8222-c422d364074f"}}, {"head": {"id": "4ab0e10e-6bd6-4cdf-a495-1e2dae73950e", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998921221000, "endTime": 31998922466600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "01c184f5-efb1-4922-93e4-412399194599", "logId": "fd8eaaee-6b4b-45b6-8314-8b88c1a2000c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01c184f5-efb1-4922-93e4-412399194599", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998919320800}, "additional": {"logType": "detail", "children": [], "durationId": "4ab0e10e-6bd6-4cdf-a495-1e2dae73950e"}}, {"head": {"id": "eb6014e8-b11f-4e30-8d01-a5e139df4db1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998919820300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e543a54-7571-4131-a079-5710006dacf2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998919930200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af567eec-7607-4203-ae90-67109979253a", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998921231900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7caf924-9daa-4703-a18a-a7c99e39fb71", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998921465100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f52546c-300a-46f3-8f0b-9fe0fa3cf51a", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998922207100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16cb7e0a-9e7f-4278-bbea-9b9d83941fa4", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07306671142578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998922324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd8eaaee-6b4b-45b6-8314-8b88c1a2000c", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998921221000, "endTime": 31998922466600}, "additional": {"logType": "info", "children": [], "durationId": "4ab0e10e-6bd6-4cdf-a495-1e2dae73950e"}}, {"head": {"id": "04874cb9-0744-40c6-9aa7-749509e60deb", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998931514200, "endTime": 31999411081600}, "additional": {"children": ["9891b08e-9095-4355-b77c-da495dc96fc1", "c14d2bb6-fa07-4ecb-bb07-9ef3d11ac9fc", "aac5015f-f53d-4828-9d30-b4eeb4b690cc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "c5b65bcd-64c7-42ef-bef7-b060cae50799", "logId": "23a42b52-5300-459f-92f3-7cebbad1c434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5b65bcd-64c7-42ef-bef7-b060cae50799", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998924973600}, "additional": {"logType": "detail", "children": [], "durationId": "04874cb9-0744-40c6-9aa7-749509e60deb"}}, {"head": {"id": "a0685360-01c7-4a3e-b160-b1c261c50657", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998925296100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6002c661-1cce-443f-a14e-88d9eca1aa12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998925392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b3551d-a60c-4bf2-afbc-1b844a3ccf5f", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998931524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68d49607-e55e-4276-9c5d-4be6dd5eafdc", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998942970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc6924e-fb79-4af0-9422-3e0d6c1bd9d6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998943140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1053ef4-65aa-46ed-ae76-f5926332fe17", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998943250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3414ac-eba2-41bb-8e40-28365d711566", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998943306300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9891b08e-9095-4355-b77c-da495dc96fc1", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998943965000, "endTime": 31998945134800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "04874cb9-0744-40c6-9aa7-749509e60deb", "logId": "c1004c25-d767-4879-820a-73b7654f7e02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b05836ae-c58e-40c1-9836-68341d9c8bd9", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998945007800}, "additional": {"logType": "debug", "children": [], "durationId": "04874cb9-0744-40c6-9aa7-749509e60deb"}}, {"head": {"id": "c1004c25-d767-4879-820a-73b7654f7e02", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998943965000, "endTime": 31998945134800}, "additional": {"logType": "info", "children": [], "durationId": "9891b08e-9095-4355-b77c-da495dc96fc1", "parent": "23a42b52-5300-459f-92f3-7cebbad1c434"}}, {"head": {"id": "c14d2bb6-fa07-4ecb-bb07-9ef3d11ac9fc", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998945603400, "endTime": 31998946943600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "04874cb9-0744-40c6-9aa7-749509e60deb", "logId": "8f893e90-7cd6-4b67-97a8-f589f5901ff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "debe9401-47c9-4d2b-8678-5d72abe97f39", "name": "default@PackageHap work[76] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998946099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac5015f-f53d-4828-9d30-b4eeb4b690cc", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998946921100, "endTime": 31999410541600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "04874cb9-0744-40c6-9aa7-749509e60deb", "logId": "0bfd01a7-91ac-4142-9c8f-0afac4ca207d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3343608-8829-493d-8bff-ed5e326ff820", "name": "default@PackageHap work[76] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998946690600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87abc97f-9963-4fe2-add7-edb5bbe6b63c", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998946761600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f15ba09-8b59-4506-bb79-974e6ff5b5b2", "name": "default@PackageHap work[76] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998946845800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38fc112-7a49-47ac-b858-1a67f20b9153", "name": "default@PackageHap work[76] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998946898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f893e90-7cd6-4b67-97a8-f589f5901ff2", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998945603400, "endTime": 31998946943600}, "additional": {"logType": "info", "children": [], "durationId": "c14d2bb6-fa07-4ecb-bb07-9ef3d11ac9fc", "parent": "23a42b52-5300-459f-92f3-7cebbad1c434"}}, {"head": {"id": "0b00d680-f8c8-4b68-9244-a5b9d2585da5", "name": "entry : default@PackageHap cost memory 1.3212127685546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998950689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee31623c-aca9-4dba-8cc0-27f7ed821c49", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999410624900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ec33d2-d2a1-45a6-ae0d-5e70b4790d6a", "name": "default@PackageHap work[76] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999410916200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bfd01a7-91ac-4142-9c8f-0afac4ca207d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31998946921100, "endTime": 31999410541600}, "additional": {"logType": "info", "children": [], "durationId": "aac5015f-f53d-4828-9d30-b4eeb4b690cc", "parent": "23a42b52-5300-459f-92f3-7cebbad1c434"}}, {"head": {"id": "2cd317e5-358e-4ce8-8912-0f50946a45ed", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999411014700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a42b52-5300-459f-92f3-7cebbad1c434", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31998931514200, "endTime": 31999411081600, "totalTime": 479029600}, "additional": {"logType": "info", "children": ["c1004c25-d767-4879-820a-73b7654f7e02", "8f893e90-7cd6-4b67-97a8-f589f5901ff2", "0bfd01a7-91ac-4142-9c8f-0afac4ca207d"], "durationId": "04874cb9-0744-40c6-9aa7-749509e60deb"}}, {"head": {"id": "e021f5f6-3e75-4f82-b562-9b061d026861", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999416374100, "endTime": 31999417918600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "b32bdcaa-c580-4de9-9795-6456daa7676f", "logId": "ce9794c0-9105-4e90-9d5b-96a15c5f6586"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b32bdcaa-c580-4de9-9795-6456daa7676f", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999413893700}, "additional": {"logType": "detail", "children": [], "durationId": "e021f5f6-3e75-4f82-b562-9b061d026861"}}, {"head": {"id": "86d8413e-c42c-42e4-8320-37ef0dd9f1d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999414347600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9015be-863f-495c-868b-19fd26e1abf5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999414459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ef5398-4cd2-423c-9a99-e3ebfcc5aaa7", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999416381700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88eb8397-1c4a-48ad-a071-9e4f49964ad8", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999416663200}, "additional": {"logType": "warn", "children": [], "durationId": "e021f5f6-3e75-4f82-b562-9b061d026861"}}, {"head": {"id": "1a64f962-5ec4-47ba-8294-92fcd1b69213", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999417248800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa298ab-a556-4a5c-9483-2c9fb5a1dc5e", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999417375600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63205eee-8ce1-4607-99df-2fa0d4add1e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999417464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254d3ce8-9687-47eb-ab40-7b7d17ee4fd7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999417547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e7abe57-722f-4ed0-98a9-44cb2d2d6e30", "name": "entry : default@SignHap cost memory 0.11551666259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999417773800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebfa882-f780-4d63-806b-4483dfb2200c", "name": "runTaskFromQueue task cost before running: 3 s 203 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999417860300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9794c0-9105-4e90-9d5b-96a15c5f6586", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999416374100, "endTime": 31999417918600, "totalTime": 1467200}, "additional": {"logType": "info", "children": [], "durationId": "e021f5f6-3e75-4f82-b562-9b061d026861"}}, {"head": {"id": "49d031cf-4ead-4279-9cb1-aaa2b100e1ac", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999420614300, "endTime": 31999425634600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aea2380c-aa6f-4b70-a547-054bf8af2361", "logId": "67b87c26-540b-4bec-8ff8-5a61e6be612f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aea2380c-aa6f-4b70-a547-054bf8af2361", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999419440200}, "additional": {"logType": "detail", "children": [], "durationId": "49d031cf-4ead-4279-9cb1-aaa2b100e1ac"}}, {"head": {"id": "b15341f6-2acc-4a09-bb8d-8224933f167f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999419788200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7eda861-c744-480f-aa90-441fc212df2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999419873000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da0f7ae-1b06-4a67-aa59-78cd72a9656f", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999420622800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ff0798-e3fc-41ee-b7c5-ad9902f348e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999425289300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f766e52e-34d7-4d19-830c-41be8bb9dae6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999425399600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb603f94-951e-453b-9bb4-5ef335893c50", "name": "entry : default@CollectDebugSymbol cost memory 0.2394561767578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999425478200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96436dc2-3d0d-4c66-b927-931ba74bc23f", "name": "runTaskFromQueue task cost before running: 3 s 210 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999425558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67b87c26-540b-4bec-8ff8-5a61e6be612f", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999420614300, "endTime": 31999425634600, "totalTime": 4923500}, "additional": {"logType": "info", "children": [], "durationId": "49d031cf-4ead-4279-9cb1-aaa2b100e1ac"}}, {"head": {"id": "613adb9d-1fc5-4f29-b01c-8f52cd602d84", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999427029600, "endTime": 31999427277000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "3bc962f0-7124-49f2-9b8f-d2da69e2cd32", "logId": "2b7fd5f3-7158-40c1-b9ed-ad01c575f22d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bc962f0-7124-49f2-9b8f-d2da69e2cd32", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999426990600}, "additional": {"logType": "detail", "children": [], "durationId": "613adb9d-1fc5-4f29-b01c-8f52cd602d84"}}, {"head": {"id": "f34f55ab-23cc-4f5b-b299-05bdddd5c478", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999427034800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dd3cd68-ff6e-4809-84ea-4cb1df105072", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999427151300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c5aabb-fc11-4d6f-a446-32bf41be2e52", "name": "runTaskFromQueue task cost before running: 3 s 212 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999427224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7fd5f3-7158-40c1-b9ed-ad01c575f22d", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999427029600, "endTime": 31999427277000, "totalTime": 178900}, "additional": {"logType": "info", "children": [], "durationId": "613adb9d-1fc5-4f29-b01c-8f52cd602d84"}}, {"head": {"id": "352a6139-b377-4ae8-a9a3-6fa6305d361c", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434281300, "endTime": 31999434302000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bbf1ec27-1c1b-4349-afc6-d43e2fa84f05", "logId": "e488a939-b4b4-40d3-92ea-98062d3f80f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e488a939-b4b4-40d3-92ea-98062d3f80f3", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434281300, "endTime": 31999434302000}, "additional": {"logType": "info", "children": [], "durationId": "352a6139-b377-4ae8-a9a3-6fa6305d361c"}}, {"head": {"id": "f1bad97a-4c5c-48d8-b7ac-e8d3394aaaa5", "name": "BUILD SUCCESSFUL in 3 s 219 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434338400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "4f2bedee-3d0b-496f-b525-e8d87c94bd05", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31996215779100, "endTime": 31999434582400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 22}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "1d59ac21-edeb-40d4-b601-8d6be8905419", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434615700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6bc0a4-4fed-41aa-9d39-ed396bb40af0", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434680200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb89cf2c-29c5-4637-becd-6265577b38c2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78fd5685-efdf-4b15-b5ab-f0fbc176ef96", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b693c3-3389-40d3-857b-2d848d65412b", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999434832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1e8144-e3d1-4afe-b30f-2bacc269a83a", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999435097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c092a3ae-6832-4a55-b3eb-4c931d432d43", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999435628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707a3252-899b-4f9d-807a-e9c5733d57d2", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999435843600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f98e09-46aa-4885-92c9-7e246e00ffa7", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999435910600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f5f1df8-a232-45c4-a82a-0153fa789d80", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999435970400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d28d628-9c26-482d-9e54-207401f69376", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999436198200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4817bfe-5b57-4eb9-8291-28b2d4589e0a", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999436965500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f8131a-69c1-4c35-bf01-dd24752abdef", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437176200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18af5eba-38a9-499c-9b9d-8d8d30052465", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437243300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42856a7c-1bed-4276-8c1e-9ef9330d5fb5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8984b56-0623-4692-8ae3-4d21a87c3fae", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3201897c-9cc1-4580-8b82-80146007ec94", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437389200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f814e07-8cc4-4e12-bc4f-bcc4f8abf3c6", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ed4042-5234-4cac-81c6-41d347d131b5", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437819600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f821040b-7440-4423-9fd8-40dd48639a15", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999437992100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c488d668-53a9-47d8-8b43-11d0de03e12a", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999438282000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "504fa4d2-6ba4-4e71-be24-2e8e75f9274c", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999438368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4414d589-6240-4c15-a6e7-c5f59cf3d264", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999438427100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee8e544-b923-4043-a37a-57a7bc99916c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999440372400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b81f07-d36b-4170-86c5-042af2c28843", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999440934200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a64eb3-e57c-49a3-9e33-36329feb1d0f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999441839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17e38034-ca8f-4734-9002-dabf6f120bfc", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999442061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98fec82b-8a41-4a08-9314-a35dd76a24e9", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999442267300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b504e0f2-0d11-4b7c-920e-8154e1e8cac6", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999442806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e92271a7-c1b1-444a-83fd-225cda21b530", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999442885200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e3d216-b4c2-42ee-938e-8d2c41ae0f48", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999443068700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e41244d-253b-4fd2-8d9f-b6d3c78dfefd", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999443342000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e328ceea-4390-43b7-a74f-8118272c99a5", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999443824400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdddf23f-8d9b-4d21-be56-d4482185e766", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999444907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95fa4f0-3608-4d08-a8ae-e98e3e8737de", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999445454500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67c74598-da1f-42b0-b074-e4d3c0404f99", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999446197600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9be6e57-a626-4d75-a890-947ef379a9e6", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999446392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2059a998-fef6-4db6-8a82-144481199cf7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999446575900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa5d953-a8bb-4a23-b5bd-4d6ec99debf9", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999447144100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "817b97b0-ffa3-48da-b8ed-53b939345142", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999447361600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f83222-e114-4f2f-8c2d-4ec33daeb817", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999447431500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8cc35da-1a3e-48e3-b878-a04989d74e2c", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999447483300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "814863c2-c7c6-45b4-a2e6-d0e4ba227329", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999448235200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "139f1623-2e1f-4a84-ae81-2b16a5c85a2b", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999448478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3562055f-d861-4cb1-bea8-13306a954563", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999448718800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f85b55-d8fd-4b54-9e76-bc405f2dfe2b", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999454541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e789c65b-41be-46d1-8aa2-1f210bfa7202", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999454769000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fac5e58-6215-4252-85d1-e51e1f09d63e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999454938600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75269a5f-7211-41d8-9b82-7590e8048665", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999455002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1cf920-1921-4fb9-9e0e-17ced2d7ce5c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999455231500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f829e51-6a49-4b8a-9d4c-a6eb8c76f87e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999455878600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b35b32-e6f3-4f6c-9b0a-7c7617e3c0cb", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999456089800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5845edf1-999e-4527-8ff9-73d207627321", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999456355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8721f0-e1a5-49f3-8a42-b8da63920b6b", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999456627700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c1be022-91b5-4b76-88f4-c03ed437eae6", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999456854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eeb1c64-3572-4c95-8ef7-fcac052fb307", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999456945700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "463d8ed4-8c6a-4309-87d3-ecedfbe3fb78", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999457278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f6154c3-c22d-413f-b197-5b3dcf47bed4", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999459871600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53914b1a-5c3c-4606-b8fd-4cf81503a949", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999460286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d65fb55-f278-4faa-98b0-9a16f5f083a1", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999460732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040b8927-a9e5-4fa4-875d-e06b9c8cced2", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999461101900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}