{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "3ddb8861-7ac3-4df7-96d6-2133c684ec4e", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827821371100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7352a58f-bfcc-432b-b1b0-093bb5b070f3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827825440200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317ad1c1-3492-41e2-9da4-9e9406035a33", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827826150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9b6215d-87e5-4f28-ac04-0f758c62f6c1", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827835316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a4c913-671f-4f36-844b-6188888b0659", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937232235800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323457bd-6272-4192-9495-8a258ceb4874", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937241307000, "endTime": 30937765133800}, "additional": {"children": ["27e584d6-d4aa-4c94-9cd2-90db5fc9f002", "76c11569-ce7e-4f1c-935a-87ef3123ec75", "40872d97-770f-4909-a592-19189ff20d5e", "f625f69d-1e01-46e8-8648-beb51bb02270", "34db0d6d-105f-4959-be3b-dcffe22765b8", "8cc6cb96-f82c-4e7e-97a8-723063c18896", "578792fa-b91c-4e24-8ede-0a92f6f0ab68"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27e584d6-d4aa-4c94-9cd2-90db5fc9f002", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937241310300, "endTime": 30937260746600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "02f96f28-26c2-4ecc-bcef-c21bf7cbe6f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937260766900, "endTime": 30937761410500}, "additional": {"children": ["36a8df65-dfc1-493f-bb22-55d2f3c0caea", "ec55079c-7cf0-426f-bf2b-02a19e147d4f", "aac655b1-0500-42aa-bc59-65bb3eeb4daa", "5c209ee1-f89f-4b43-a6b7-08f1047b87f8", "d77656f3-ed95-458c-93c2-2fbaf96f91c5", "83566c27-04fb-4fe2-9203-53fe2d79ee33", "0a09ea74-15ba-437c-9a1a-0d67386db0d8", "b7c6d4e8-13e6-48a0-bd47-c97fd299fd97", "3f9d1a43-ed95-4d65-ad89-6750d51f9ebe"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40872d97-770f-4909-a592-19189ff20d5e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937761494300, "endTime": 30937765017900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "94c2c7e6-05d8-4cc3-bb4d-1000697fa370"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f625f69d-1e01-46e8-8648-beb51bb02270", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937765113500, "endTime": 30937765115900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "86eb03e6-58a0-4ead-a00d-99c88e486e9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34db0d6d-105f-4959-be3b-dcffe22765b8", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937246041200, "endTime": 30937246118300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "fc7df4b7-a6ad-401d-9154-39059d08ec72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc7df4b7-a6ad-401d-9154-39059d08ec72", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937246041200, "endTime": 30937246118300}, "additional": {"logType": "info", "children": [], "durationId": "34db0d6d-105f-4959-be3b-dcffe22765b8", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "8cc6cb96-f82c-4e7e-97a8-723063c18896", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937254274400, "endTime": 30937254296900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "ec48bbf2-73b3-4925-b5e1-060fb894d05c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec48bbf2-73b3-4925-b5e1-060fb894d05c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937254274400, "endTime": 30937254296900}, "additional": {"logType": "info", "children": [], "durationId": "8cc6cb96-f82c-4e7e-97a8-723063c18896", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "d7f348de-5e1a-44d2-8de1-ab5408343341", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937254496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0171d259-fa14-4fd9-86ee-80eaa0a97bad", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937260580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02f96f28-26c2-4ecc-bcef-c21bf7cbe6f3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937241310300, "endTime": 30937260746600}, "additional": {"logType": "info", "children": [], "durationId": "27e584d6-d4aa-4c94-9cd2-90db5fc9f002", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "36a8df65-dfc1-493f-bb22-55d2f3c0caea", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937266690300, "endTime": 30937266698500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "dfad8859-7256-4f24-92ae-e76d13361053"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec55079c-7cf0-426f-bf2b-02a19e147d4f", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937266762800, "endTime": 30937271128700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "118d87ac-047f-4bf5-90bf-58a65e478c0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aac655b1-0500-42aa-bc59-65bb3eeb4daa", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937271159100, "endTime": 30937495136800}, "additional": {"children": ["4dd713c9-4bb0-43bd-adaa-20a4f26e1769", "854fdbf0-5e37-4c66-8f29-d434fb592ad9", "0423f879-762a-4c3e-8d2e-eb11913afe10"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "d5668f86-5594-4342-bd0a-7e0e93767fd9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c209ee1-f89f-4b43-a6b7-08f1047b87f8", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937495155400, "endTime": 30937565332100}, "additional": {"children": ["78a180a1-fa4e-47e0-8bb9-d93642acf1b5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "e5cfe6e6-6083-4c3b-a883-b64dbd810abf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d77656f3-ed95-458c-93c2-2fbaf96f91c5", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937565480000, "endTime": 30937708956400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "4bf27fc9-def4-4bfb-8dba-a9d4edbda0b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83566c27-04fb-4fe2-9203-53fe2d79ee33", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937710706800, "endTime": 30937727017700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "ea6fbb98-37de-4e36-b290-d875f66dd13a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a09ea74-15ba-437c-9a1a-0d67386db0d8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937727040200, "endTime": 30937761091300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "29afd706-0cf4-45b4-bf92-dd6ca00c3f4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7c6d4e8-13e6-48a0-bd47-c97fd299fd97", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937761168700, "endTime": 30937761393700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "2c3f4a33-0aaa-47f7-b570-8bb30998e454"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfad8859-7256-4f24-92ae-e76d13361053", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937266690300, "endTime": 30937266698500}, "additional": {"logType": "info", "children": [], "durationId": "36a8df65-dfc1-493f-bb22-55d2f3c0caea", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "118d87ac-047f-4bf5-90bf-58a65e478c0f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937266762800, "endTime": 30937271128700}, "additional": {"logType": "info", "children": [], "durationId": "ec55079c-7cf0-426f-bf2b-02a19e147d4f", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "4dd713c9-4bb0-43bd-adaa-20a4f26e1769", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937272090700, "endTime": 30937272116900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aac655b1-0500-42aa-bc59-65bb3eeb4daa", "logId": "bc45f06d-928a-4911-81fc-d9169493d0ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc45f06d-928a-4911-81fc-d9169493d0ee", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937272090700, "endTime": 30937272116900}, "additional": {"logType": "info", "children": [], "durationId": "4dd713c9-4bb0-43bd-adaa-20a4f26e1769", "parent": "d5668f86-5594-4342-bd0a-7e0e93767fd9"}}, {"head": {"id": "854fdbf0-5e37-4c66-8f29-d434fb592ad9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937276416400, "endTime": 30937493859600}, "additional": {"children": ["fda641e8-1998-4524-9c21-fcbe97062e5a", "3ee5666b-851e-4035-aa69-75e9c0ffb7a4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aac655b1-0500-42aa-bc59-65bb3eeb4daa", "logId": "0acbbfa9-bb8d-4c9b-a6c3-27bce2466487"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fda641e8-1998-4524-9c21-fcbe97062e5a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937276419200, "endTime": 30937305059600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "854fdbf0-5e37-4c66-8f29-d434fb592ad9", "logId": "440eb533-df26-41f6-98cb-711cdcbed1ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ee5666b-851e-4035-aa69-75e9c0ffb7a4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937305076800, "endTime": 30937493845500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "854fdbf0-5e37-4c66-8f29-d434fb592ad9", "logId": "d0c0b78c-d34f-473d-a96a-e3752b9d3df1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3bc4546-5535-4d5e-a040-9b2e163ffda3", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937276428700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af07019-96df-481b-8c34-23cd9e8f14e4", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937304919600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440eb533-df26-41f6-98cb-711cdcbed1ca", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937276419200, "endTime": 30937305059600}, "additional": {"logType": "info", "children": [], "durationId": "fda641e8-1998-4524-9c21-fcbe97062e5a", "parent": "0acbbfa9-bb8d-4c9b-a6c3-27bce2466487"}}, {"head": {"id": "b189b3f9-a32e-4afe-9af8-5b4156a95a93", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937305130800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bafd6df-cd64-4465-a9dd-41cab14540f2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937342561200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f15a7cf4-0c2b-480b-8bad-03e64e26bd8f", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937342802900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac681b3-9baa-459d-93ed-a048eec0339d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937342955400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d9734ef-4e70-4fd7-b45c-842ff37f9739", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937343100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55bf8353-add2-4430-9651-45d3bc20d063", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937345870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ae1df9-50f8-41fe-9ff8-561964c269f1", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937378040900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f68f28-a0c6-4321-9500-c5fdb6c54aeb", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937397488500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072c5c26-9605-41bc-abbe-1785d20c824d", "name": "Sdk init in 52 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937433587100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a3cb0f-3c17-4e74-b51f-a03c0fad75ec", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937434021500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 5}, "markType": "other"}}, {"head": {"id": "7380cb13-e58f-43f8-97b3-0a9fac7ca9bd", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937434081400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 5}, "markType": "other"}}, {"head": {"id": "9b695121-c27e-43e0-8e1a-aae81377434d", "name": "Project task initialization takes 58 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937493516300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4223fa3-3315-425e-9251-bb50c53a65a3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937493656800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "297c322d-e836-45c9-904a-b9771cf7600f", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937493714800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216f5f4e-fd68-4f20-b0a9-328441a82dd5", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937493761600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c0b78c-d34f-473d-a96a-e3752b9d3df1", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937305076800, "endTime": 30937493845500}, "additional": {"logType": "info", "children": [], "durationId": "3ee5666b-851e-4035-aa69-75e9c0ffb7a4", "parent": "0acbbfa9-bb8d-4c9b-a6c3-27bce2466487"}}, {"head": {"id": "0acbbfa9-bb8d-4c9b-a6c3-27bce2466487", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937276416400, "endTime": 30937493859600}, "additional": {"logType": "info", "children": ["440eb533-df26-41f6-98cb-711cdcbed1ca", "d0c0b78c-d34f-473d-a96a-e3752b9d3df1"], "durationId": "854fdbf0-5e37-4c66-8f29-d434fb592ad9", "parent": "d5668f86-5594-4342-bd0a-7e0e93767fd9"}}, {"head": {"id": "0423f879-762a-4c3e-8d2e-eb11913afe10", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937495085300, "endTime": 30937495114700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aac655b1-0500-42aa-bc59-65bb3eeb4daa", "logId": "cfa2cc0f-ad7a-429e-81b6-0d68d808f816"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfa2cc0f-ad7a-429e-81b6-0d68d808f816", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937495085300, "endTime": 30937495114700}, "additional": {"logType": "info", "children": [], "durationId": "0423f879-762a-4c3e-8d2e-eb11913afe10", "parent": "d5668f86-5594-4342-bd0a-7e0e93767fd9"}}, {"head": {"id": "d5668f86-5594-4342-bd0a-7e0e93767fd9", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937271159100, "endTime": 30937495136800}, "additional": {"logType": "info", "children": ["bc45f06d-928a-4911-81fc-d9169493d0ee", "0acbbfa9-bb8d-4c9b-a6c3-27bce2466487", "cfa2cc0f-ad7a-429e-81b6-0d68d808f816"], "durationId": "aac655b1-0500-42aa-bc59-65bb3eeb4daa", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "78a180a1-fa4e-47e0-8bb9-d93642acf1b5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937495883400, "endTime": 30937565315800}, "additional": {"children": ["e724e316-a6c9-4e62-882a-3cc1e21ce018", "67212cb9-a29b-4190-8acd-b8815547843b", "15fa7121-95c6-4c12-91ee-43badb74575b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c209ee1-f89f-4b43-a6b7-08f1047b87f8", "logId": "84a80bf8-ebe1-43ec-9a5c-ed743dbe8580"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e724e316-a6c9-4e62-882a-3cc1e21ce018", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937499188500, "endTime": 30937499214000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78a180a1-fa4e-47e0-8bb9-d93642acf1b5", "logId": "2a95d4eb-4ce1-40df-9aa1-d84468563da6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a95d4eb-4ce1-40df-9aa1-d84468563da6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937499188500, "endTime": 30937499214000}, "additional": {"logType": "info", "children": [], "durationId": "e724e316-a6c9-4e62-882a-3cc1e21ce018", "parent": "84a80bf8-ebe1-43ec-9a5c-ed743dbe8580"}}, {"head": {"id": "67212cb9-a29b-4190-8acd-b8815547843b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937501673100, "endTime": 30937560766800}, "additional": {"children": ["12ffcbbf-68a0-4577-8bf3-bc6b12659cb4", "ff205980-7420-4c9a-ae59-b038a19c7126"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78a180a1-fa4e-47e0-8bb9-d93642acf1b5", "logId": "d9afd426-0f2a-4510-8960-305c9bc32257"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12ffcbbf-68a0-4577-8bf3-bc6b12659cb4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937501674800, "endTime": 30937506705500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67212cb9-a29b-4190-8acd-b8815547843b", "logId": "11693a9a-1a50-43c1-8861-c58ad97ff210"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff205980-7420-4c9a-ae59-b038a19c7126", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937506731000, "endTime": 30937560753100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67212cb9-a29b-4190-8acd-b8815547843b", "logId": "b95e16c7-9017-4a86-9515-e3a0d353f56f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a622fa9-5cf0-43f8-8d6d-bc1ecda80024", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937501679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78bf1bc5-2c81-40c4-ac8d-51db70032eab", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937506442500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11693a9a-1a50-43c1-8861-c58ad97ff210", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937501674800, "endTime": 30937506705500}, "additional": {"logType": "info", "children": [], "durationId": "12ffcbbf-68a0-4577-8bf3-bc6b12659cb4", "parent": "d9afd426-0f2a-4510-8960-305c9bc32257"}}, {"head": {"id": "98c2055f-1a34-46ab-9119-0cb22c97b0e9", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937506750600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55627faf-c8a5-4468-8f98-df5408e94d34", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937538479400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c541fe78-0e08-42d5-b31a-a6d5e990511f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937538991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf23433-5062-4fbe-83fb-1f5cc5b9f034", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937540364700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e730af53-8f38-497a-8140-261ac4a1f12c", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937541264500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b3924d6-db96-43e3-94f3-88ac3f6a3d20", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937541465800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ab706b-6507-483e-8457-682dbd892e9c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937541763300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e24695b-ed73-4ee2-80a7-9766d14428a4", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937542408200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b1925ea-e6d2-449a-b04c-2eb62e7f9896", "name": "Module entry task initialization takes 13 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937560247700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc8cf0e-8c6d-474f-994e-ed51942e9266", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937560499400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c115e7e7-a194-4495-8e59-95b078190e83", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937560573900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55c35ec-0635-4d7c-8af0-78de58aadaec", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937560682500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95e16c7-9017-4a86-9515-e3a0d353f56f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937506731000, "endTime": 30937560753100}, "additional": {"logType": "info", "children": [], "durationId": "ff205980-7420-4c9a-ae59-b038a19c7126", "parent": "d9afd426-0f2a-4510-8960-305c9bc32257"}}, {"head": {"id": "d9afd426-0f2a-4510-8960-305c9bc32257", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937501673100, "endTime": 30937560766800}, "additional": {"logType": "info", "children": ["11693a9a-1a50-43c1-8861-c58ad97ff210", "b95e16c7-9017-4a86-9515-e3a0d353f56f"], "durationId": "67212cb9-a29b-4190-8acd-b8815547843b", "parent": "84a80bf8-ebe1-43ec-9a5c-ed743dbe8580"}}, {"head": {"id": "15fa7121-95c6-4c12-91ee-43badb74575b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937565246300, "endTime": 30937565282500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78a180a1-fa4e-47e0-8bb9-d93642acf1b5", "logId": "567123c7-d173-43f5-bbb9-fa3f7fa5f229"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "567123c7-d173-43f5-bbb9-fa3f7fa5f229", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937565246300, "endTime": 30937565282500}, "additional": {"logType": "info", "children": [], "durationId": "15fa7121-95c6-4c12-91ee-43badb74575b", "parent": "84a80bf8-ebe1-43ec-9a5c-ed743dbe8580"}}, {"head": {"id": "84a80bf8-ebe1-43ec-9a5c-ed743dbe8580", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937495883400, "endTime": 30937565315800}, "additional": {"logType": "info", "children": ["2a95d4eb-4ce1-40df-9aa1-d84468563da6", "d9afd426-0f2a-4510-8960-305c9bc32257", "567123c7-d173-43f5-bbb9-fa3f7fa5f229"], "durationId": "78a180a1-fa4e-47e0-8bb9-d93642acf1b5", "parent": "e5cfe6e6-6083-4c3b-a883-b64dbd810abf"}}, {"head": {"id": "e5cfe6e6-6083-4c3b-a883-b64dbd810abf", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937495155400, "endTime": 30937565332100}, "additional": {"logType": "info", "children": ["84a80bf8-ebe1-43ec-9a5c-ed743dbe8580"], "durationId": "5c209ee1-f89f-4b43-a6b7-08f1047b87f8", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "8c28da33-3fe4-4f1d-b27e-1602a139ebb2", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937606479900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3d4991-7bbe-4218-be5a-1b57ac96e853", "name": "hvigorfile, resolve hvigorfile dependencies in 143 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937708462200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf27fc9-def4-4bfb-8dba-a9d4edbda0b5", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937565480000, "endTime": 30937708956400}, "additional": {"logType": "info", "children": [], "durationId": "d77656f3-ed95-458c-93c2-2fbaf96f91c5", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "3f9d1a43-ed95-4d65-ad89-6750d51f9ebe", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937710086400, "endTime": 30937710660800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "logId": "e44a8b01-b8f2-4f49-880a-63b65a0aeed1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1ce5657-ad87-4d69-b6d6-3fd820df42a7", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937710277100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e44a8b01-b8f2-4f49-880a-63b65a0aeed1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937710086400, "endTime": 30937710660800}, "additional": {"logType": "info", "children": [], "durationId": "3f9d1a43-ed95-4d65-ad89-6750d51f9ebe", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "3ebc3042-dae7-483d-80f5-4bea5f61ac39", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937713492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34515971-c1ec-44b8-b9f4-8167635c535c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937724832300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6fbb98-37de-4e36-b290-d875f66dd13a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937710706800, "endTime": 30937727017700}, "additional": {"logType": "info", "children": [], "durationId": "83566c27-04fb-4fe2-9203-53fe2d79ee33", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "1ff28630-91fb-4b63-96a3-1d8879758876", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937734467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13a45b09-722c-4ea2-8131-b2a0166eafce", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937734844900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87184b2b-5dfe-42e0-a844-2f2523635a29", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937745317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f7bc93-f070-4198-a934-cd0ce39ba195", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937745439500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29afd706-0cf4-45b4-bf92-dd6ca00c3f4b", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937727040200, "endTime": 30937761091300}, "additional": {"logType": "info", "children": [], "durationId": "0a09ea74-15ba-437c-9a1a-0d67386db0d8", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "d6b7e7aa-c52c-4d61-b128-2d6f6713ccc5", "name": "Configuration phase cost:495 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937761207400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c3f4a33-0aaa-47f7-b570-8bb30998e454", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937761168700, "endTime": 30937761393700}, "additional": {"logType": "info", "children": [], "durationId": "b7c6d4e8-13e6-48a0-bd47-c97fd299fd97", "parent": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb"}}, {"head": {"id": "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937260766900, "endTime": 30937761410500}, "additional": {"logType": "info", "children": ["dfad8859-7256-4f24-92ae-e76d13361053", "118d87ac-047f-4bf5-90bf-58a65e478c0f", "d5668f86-5594-4342-bd0a-7e0e93767fd9", "e5cfe6e6-6083-4c3b-a883-b64dbd810abf", "4bf27fc9-def4-4bfb-8dba-a9d4edbda0b5", "ea6fbb98-37de-4e36-b290-d875f66dd13a", "29afd706-0cf4-45b4-bf92-dd6ca00c3f4b", "2c3f4a33-0aaa-47f7-b570-8bb30998e454", "e44a8b01-b8f2-4f49-880a-63b65a0aeed1"], "durationId": "76c11569-ce7e-4f1c-935a-87ef3123ec75", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "578792fa-b91c-4e24-8ede-0a92f6f0ab68", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937764787400, "endTime": 30937764960300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "323457bd-6272-4192-9495-8a258ceb4874", "logId": "bdbe860a-79a5-4c48-8a36-0dded8b7e1e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdbe860a-79a5-4c48-8a36-0dded8b7e1e2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937764787400, "endTime": 30937764960300}, "additional": {"logType": "info", "children": [], "durationId": "578792fa-b91c-4e24-8ede-0a92f6f0ab68", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "94c2c7e6-05d8-4cc3-bb4d-1000697fa370", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937761494300, "endTime": 30937765017900}, "additional": {"logType": "info", "children": [], "durationId": "40872d97-770f-4909-a592-19189ff20d5e", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "86eb03e6-58a0-4ead-a00d-99c88e486e9a", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937765113500, "endTime": 30937765115900}, "additional": {"logType": "info", "children": [], "durationId": "f625f69d-1e01-46e8-8648-beb51bb02270", "parent": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed"}}, {"head": {"id": "f838e48a-c3d4-48b7-9cbb-4464c13f89ed", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937241307000, "endTime": 30937765133800}, "additional": {"logType": "info", "children": ["02f96f28-26c2-4ecc-bcef-c21bf7cbe6f3", "801cf6bf-1bd4-4df4-b341-1b5e6bcd5ffb", "94c2c7e6-05d8-4cc3-bb4d-1000697fa370", "86eb03e6-58a0-4ead-a00d-99c88e486e9a", "fc7df4b7-a6ad-401d-9154-39059d08ec72", "ec48bbf2-73b3-4925-b5e1-060fb894d05c", "bdbe860a-79a5-4c48-8a36-0dded8b7e1e2"], "durationId": "323457bd-6272-4192-9495-8a258ceb4874"}}, {"head": {"id": "a220ba3a-c545-4101-a99f-1b85e30893a4", "name": "Configuration task cost before running: 530 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937766189900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbd8b4d-8728-4c98-9e8a-edd6bd949def", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937781436900, "endTime": 30937794051800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a575a26a-d4d9-412a-abf4-b9d8a73e8377", "logId": "f3a567fb-c0fb-4259-8af1-3e3e19c8ab3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a575a26a-d4d9-412a-abf4-b9d8a73e8377", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937770271500}, "additional": {"logType": "detail", "children": [], "durationId": "8cbd8b4d-8728-4c98-9e8a-edd6bd949def"}}, {"head": {"id": "b7398f53-911d-40a6-812a-bb4f2e71d921", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937772918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af736490-58ce-414f-9955-5ef2d65d10fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937773056100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b8061c6-ca8c-4a0d-b4a9-55e0f5b5466a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937781506700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5992bfc4-7a04-447a-a729-01069a2e8790", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937793830200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b26bb2-d9f8-4b7d-8029-9ced6c8d6dbf", "name": "entry : default@PreBuild cost memory 0.31394195556640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937793978200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a567fb-c0fb-4259-8af1-3e3e19c8ab3d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937781436900, "endTime": 30937794051800}, "additional": {"logType": "info", "children": [], "durationId": "8cbd8b4d-8728-4c98-9e8a-edd6bd949def"}}, {"head": {"id": "b44fb9b3-3248-466f-991f-676e8282e959", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937800860600, "endTime": 30937803917400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "29d702a8-f2df-4cbf-92c8-4fa4ad58159a", "logId": "60f8fc59-f48c-4502-857a-ea736c1e4898"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29d702a8-f2df-4cbf-92c8-4fa4ad58159a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937798754700}, "additional": {"logType": "detail", "children": [], "durationId": "b44fb9b3-3248-466f-991f-676e8282e959"}}, {"head": {"id": "86b1417b-e42c-46d7-a74f-24c6bbab55c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937799365000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ba3ce4-b81b-418a-a394-ef0ad1b010a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937799512700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616063fe-5023-4d19-b7e1-896697ab8760", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937800877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "390884e7-b84a-465d-b408-868ddecb9f92", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937802687200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d932a82c-a82c-4de0-bed5-00c7d105d22e", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937803507800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e59defc0-6de4-49d3-a0b9-6ed764fed9cd", "name": "entry : default@GenerateMetadata cost memory 0.09578704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937803602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60f8fc59-f48c-4502-857a-ea736c1e4898", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937800860600, "endTime": 30937803917400}, "additional": {"logType": "info", "children": [], "durationId": "b44fb9b3-3248-466f-991f-676e8282e959"}}, {"head": {"id": "fe72d71d-b3c3-4700-8439-3e5f0fb66155", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806799200, "endTime": 30937807168800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "02030479-751f-45bb-bbd5-b7089728662e", "logId": "9a07d73a-cee3-4d73-ab0d-559bdc0f9b75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02030479-751f-45bb-bbd5-b7089728662e", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937805924600}, "additional": {"logType": "detail", "children": [], "durationId": "fe72d71d-b3c3-4700-8439-3e5f0fb66155"}}, {"head": {"id": "8746bf95-eecf-4636-a314-c83802559b12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806409300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3512d4-ec60-4474-b555-2c48e7a980b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806518600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304c3bd0-d40a-46f9-bb20-72f5feeb6b73", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806806400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b66d4a-cbcf-4c26-a8c2-c228d11961ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3356f9f-d02b-49d9-b674-9d99e673a8af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b68aa4e-accb-4be1-b2ed-97d3693c1707", "name": "entry : default@ConfigureCmake cost memory 0.03607177734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937807021400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dee45ab-cf2c-4642-b845-3738eecc7e73", "name": "runTaskFromQueue task cost before running: 571 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937807096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a07d73a-cee3-4d73-ab0d-559bdc0f9b75", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937806799200, "endTime": 30937807168800, "totalTime": 278500}, "additional": {"logType": "info", "children": [], "durationId": "fe72d71d-b3c3-4700-8439-3e5f0fb66155"}}, {"head": {"id": "57e0d6a1-b49d-4baf-91bc-4453dc789a56", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937812410800, "endTime": 30937814692700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "688a1540-b832-4266-8647-0cc76b9bcc06", "logId": "82bf9f17-420a-444b-85bd-c5f0c5e8d312"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "688a1540-b832-4266-8647-0cc76b9bcc06", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937810978500}, "additional": {"logType": "detail", "children": [], "durationId": "57e0d6a1-b49d-4baf-91bc-4453dc789a56"}}, {"head": {"id": "6401f57c-0bd3-4e06-9eed-5f2231cc8c5b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937811595300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84ead47-54ad-4fd8-bae6-66a408c4df25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937811724600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab89319d-0552-47b3-b161-afe1aad544d0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937812421700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c825614a-70b5-4b19-9878-bd9e5556e565", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937814316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af0fb38-5a5d-4f88-a342-b7711d6a7b85", "name": "entry : default@MergeProfile cost memory 0.10671234130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937814555600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82bf9f17-420a-444b-85bd-c5f0c5e8d312", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937812410800, "endTime": 30937814692700}, "additional": {"logType": "info", "children": [], "durationId": "57e0d6a1-b49d-4baf-91bc-4453dc789a56"}}, {"head": {"id": "10ef172e-da97-4507-a1fc-9533f0460bc5", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937818225100, "endTime": 30937820161600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a3ed80e-64d8-495c-b412-70d911d38fd0", "logId": "e2abc8f2-6a02-4ea1-bda1-caf943bbea88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a3ed80e-64d8-495c-b412-70d911d38fd0", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937816798900}, "additional": {"logType": "detail", "children": [], "durationId": "10ef172e-da97-4507-a1fc-9533f0460bc5"}}, {"head": {"id": "a51c577d-a388-451d-8be5-05e4ca16edee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937817178200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db6c864-de43-41af-99c2-97712fe77476", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937817286300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29890ba8-b5b2-49d1-9954-0a61ac920ddb", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937818237800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfcf94bc-0c77-4067-961a-d996ad863bfd", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937819147300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "459e4922-cd9b-4e64-b9fc-e7e987409779", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937819976100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6bd40c-e56b-4715-bdad-20ceb472aecc", "name": "entry : default@CreateBuildProfile cost memory 0.104278564453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937820064800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2abc8f2-6a02-4ea1-bda1-caf943bbea88", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937818225100, "endTime": 30937820161600}, "additional": {"logType": "info", "children": [], "durationId": "10ef172e-da97-4507-a1fc-9533f0460bc5"}}, {"head": {"id": "9f41954b-fbfa-470a-a624-bfd2a3f8bfb8", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937824921200, "endTime": 30937825984300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "eb6462fd-9de7-4fc8-ab7d-c5a16177ad9c", "logId": "9ee1988a-7e1e-4b23-9dfe-314c733637d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb6462fd-9de7-4fc8-ab7d-c5a16177ad9c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937822766600}, "additional": {"logType": "detail", "children": [], "durationId": "9f41954b-fbfa-470a-a624-bfd2a3f8bfb8"}}, {"head": {"id": "46dce490-53ea-4b91-b43b-45ecfd17c94c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937823485800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a1ebfe-c43b-462c-9320-68293f8f4707", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937823604100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1dbcd74-7f50-48d7-8e77-61d97fc23361", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937824937100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2755a29-d373-41a9-aa27-8202f0d6d95d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937825360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3509b9c-8f9c-4fca-8260-b31929632e8f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937825487400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37ea5924-75bf-4eea-9473-c925d370d71d", "name": "entry : default@PreCheckSyscap cost memory 0.03629302978515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937825778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3368691-021f-44e6-a725-2cac468c9a88", "name": "runTaskFromQueue task cost before running: 590 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937825894900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee1988a-7e1e-4b23-9dfe-314c733637d4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937824921200, "endTime": 30937825984300, "totalTime": 950300}, "additional": {"logType": "info", "children": [], "durationId": "9f41954b-fbfa-470a-a624-bfd2a3f8bfb8"}}, {"head": {"id": "f6e75f16-3949-4992-a8c4-1905f7317230", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937838391300, "endTime": 30937842416700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5eec29dd-7825-4153-9a18-e4ebf5a3f5b5", "logId": "61b2880c-bfb9-4469-a09a-592544af61e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5eec29dd-7825-4153-9a18-e4ebf5a3f5b5", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937830675400}, "additional": {"logType": "detail", "children": [], "durationId": "f6e75f16-3949-4992-a8c4-1905f7317230"}}, {"head": {"id": "59ad1b2b-00e9-4667-a949-cdd109199286", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937831098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e838bec8-dfe9-414f-9e54-2494febe6432", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937831208100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b25822b-3e7a-418a-ac94-ced49129b932", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937838419000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a3a1ae-f0a7-416c-afd7-862e68879927", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937839887400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a44b039-6f2f-4d61-b86f-e47c6f2c1a21", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03891754150390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937840345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a175e3f-c08b-47d4-aecc-47a687091a07", "name": "runTaskFromQueue task cost before running: 604 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937840490700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b2880c-bfb9-4469-a09a-592544af61e2", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937838391300, "endTime": 30937842416700, "totalTime": 2049400}, "additional": {"logType": "info", "children": [], "durationId": "f6e75f16-3949-4992-a8c4-1905f7317230"}}, {"head": {"id": "14fbd1fb-db6b-4c06-9d08-90b1d1ef145f", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937848839000, "endTime": 30937851793000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "a4262108-616f-4cee-ad59-212d3eafa1ac", "logId": "7002d919-5951-4f78-88ce-332425123ff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4262108-616f-4cee-ad59-212d3eafa1ac", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937846260000}, "additional": {"logType": "detail", "children": [], "durationId": "14fbd1fb-db6b-4c06-9d08-90b1d1ef145f"}}, {"head": {"id": "6518fb04-3f64-484a-a09f-8781f6efa16d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937846663700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096f0328-3bbc-4d07-9117-520c6b0fa53b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937846774600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dafc7381-bccd-4bc8-b02d-dbd96f9e58f4", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937848852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f458941-9a8c-4a1a-b547-6185be42e407", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937850853200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a008903-672f-4992-a4e0-910a8265eb33", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937851010200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1766be21-4dc1-4794-8849-031e4e9a8621", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937851154800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d0edbe-18ef-4cf9-be03-b55fb6547397", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937851446800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e859126-462b-44bb-8b82-d6a542423bd0", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11838531494140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937851575600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e924a3-271c-47d4-bfcd-c0a7f89c49da", "name": "runTaskFromQueue task cost before running: 615 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937851701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7002d919-5951-4f78-88ce-332425123ff2", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937848839000, "endTime": 30937851793000, "totalTime": 2818300}, "additional": {"logType": "info", "children": [], "durationId": "14fbd1fb-db6b-4c06-9d08-90b1d1ef145f"}}, {"head": {"id": "888a9526-5aff-4ca7-b328-3cf8971945fd", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937864446600, "endTime": 30937868053000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6cba7e08-4c72-4321-95f9-9cfbc912bd4b", "logId": "b8fb8bbb-3bac-40f2-8a17-41f6fb3b2ef1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cba7e08-4c72-4321-95f9-9cfbc912bd4b", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937854720300}, "additional": {"logType": "detail", "children": [], "durationId": "888a9526-5aff-4ca7-b328-3cf8971945fd"}}, {"head": {"id": "b7e6ca74-fe7c-4593-8ccd-c370e7c8e9c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937856019800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7119da65-6379-4d18-bca8-9e79f2486e5a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937856152000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71855758-3ba7-48bb-abcf-c5bd3df7b6cc", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937864469900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb3a9eb-0d5a-4111-9412-53654373dbfe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937865435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3bfa348-debc-4201-94cf-3d5eacb61ec9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937867017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d36c0f77-1848-4693-9f7c-8fbcfbc992b6", "name": "entry : default@BuildNativeWithCmake cost memory 0.0371246337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937867334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbee5f33-0fef-4e2e-aa0c-ae17d25fa591", "name": "runTaskFromQueue task cost before running: 632 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937867964300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fb8bbb-3bac-40f2-8a17-41f6fb3b2ef1", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937864446600, "endTime": 30937868053000, "totalTime": 3480400}, "additional": {"logType": "info", "children": [], "durationId": "888a9526-5aff-4ca7-b328-3cf8971945fd"}}, {"head": {"id": "c6810baf-4c9d-4371-b044-0adb681ec860", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937877827800, "endTime": 30937881900400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4daa5163-e5b1-4ef9-9e19-6513ae3164e6", "logId": "bedd0523-2446-4d03-a056-fa919f5849b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4daa5163-e5b1-4ef9-9e19-6513ae3164e6", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937875581900}, "additional": {"logType": "detail", "children": [], "durationId": "c6810baf-4c9d-4371-b044-0adb681ec860"}}, {"head": {"id": "6bee3a71-878f-41a5-8e90-e2632896c27c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937876399800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ac9eb0-d66d-4a6c-95c4-1f2cf8f73891", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937876527200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69621b9f-b454-4832-a280-9d05ee00762a", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937877840700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1f40fc-cfd0-4df8-b0d6-3eeba3753c62", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937881680200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efaef6ce-2cea-4961-b68b-da9d083970e4", "name": "entry : default@MakePackInfo cost memory 0.13954925537109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937881807600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedd0523-2446-4d03-a056-fa919f5849b2", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937877827800, "endTime": 30937881900400}, "additional": {"logType": "info", "children": [], "durationId": "c6810baf-4c9d-4371-b044-0adb681ec860"}}, {"head": {"id": "d7ca5b97-f320-426f-8684-5d9f526f9d3e", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937886570300, "endTime": 30937890214800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "1a9a98f3-a094-46f9-8996-892481567a32", "logId": "a5e1eefd-f6aa-4748-8a73-d55007c6b851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a9a98f3-a094-46f9-8996-892481567a32", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937884907700}, "additional": {"logType": "detail", "children": [], "durationId": "d7ca5b97-f320-426f-8684-5d9f526f9d3e"}}, {"head": {"id": "3e563cb8-3e78-49ee-a82c-0e546bbca651", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937885330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "956ff0fc-af87-401b-858e-2deb83d10fe3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937885431800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d9dbb2-c9a6-48d2-978c-2588d39f377a", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937886579400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b423100f-ae87-476a-b18b-d2fa4357805c", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937886716100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "584009a9-d1f0-43f9-8560-f6a8a1d2107e", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937887832000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60eaf365-f474-431a-b9ec-7aa9782a9db1", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937889509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea67960-f91c-4f63-898e-d8d005e910e1", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937889693900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a8fee07-fe71-4f2c-a1ce-71ffa806282d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937889827400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb11c9ea-4fb4-4f61-835d-800c9ffc8909", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937889938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3dd86be-3214-40b1-9f31-50e34b8b56be", "name": "entry : default@SyscapTransform cost memory 0.15436553955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937890065900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76742d23-85f0-435e-81a9-1c7c6effa4ec", "name": "runTaskFromQueue task cost before running: 654 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937890157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e1eefd-f6aa-4748-8a73-d55007c6b851", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937886570300, "endTime": 30937890214800, "totalTime": 3564900}, "additional": {"logType": "info", "children": [], "durationId": "d7ca5b97-f320-426f-8684-5d9f526f9d3e"}}, {"head": {"id": "a31009e3-34b6-4912-af62-ea4289f5afe1", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937910989600, "endTime": 30937912898300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f85401c2-2d78-4d3d-bb06-2608a5975935", "logId": "2a8a4222-0561-4886-8a46-4c0bc09352e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f85401c2-2d78-4d3d-bb06-2608a5975935", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937901601500}, "additional": {"logType": "detail", "children": [], "durationId": "a31009e3-34b6-4912-af62-ea4289f5afe1"}}, {"head": {"id": "31e1003a-80c6-43f0-9972-5dbd77ebc73a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937904481500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16d06c1d-554d-4acf-aed7-014e85fe8195", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937906186800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3bf410-3341-4322-8a9e-96a47f148968", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937911002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a7f4276-0ad0-4f10-bc3f-a63626ff0ce3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937912458800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6998499c-d270-4c02-b25c-50ca45603932", "name": "entry : default@ProcessProfile cost memory 0.0609893798828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937912613500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a8a4222-0561-4886-8a46-4c0bc09352e1", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937910989600, "endTime": 30937912898300}, "additional": {"logType": "info", "children": [], "durationId": "a31009e3-34b6-4912-af62-ea4289f5afe1"}}, {"head": {"id": "5163b121-a37e-41e8-b048-3b786ac0fff1", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937918917100, "endTime": 30937926753800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9d25eadb-ef8a-4552-be42-7d3776340220", "logId": "42156f80-69de-4ef7-9b83-b7c30dbc6423"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d25eadb-ef8a-4552-be42-7d3776340220", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937915945400}, "additional": {"logType": "detail", "children": [], "durationId": "5163b121-a37e-41e8-b048-3b786ac0fff1"}}, {"head": {"id": "e234ce7f-5123-42cd-83ac-253186fd71d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937916494100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa7fcb8f-a6aa-4feb-a5d3-28d1b0dfc1ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937916828100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f32f75-967c-42e4-87fb-47c77ac13f15", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937918932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b919f5-39aa-404f-a989-84ba1e29e1d9", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937926249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b9b586-c4c7-4f15-b467-99d89053c439", "name": "entry : default@ProcessRouterMap cost memory 0.20379638671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937926483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42156f80-69de-4ef7-9b83-b7c30dbc6423", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937918917100, "endTime": 30937926753800}, "additional": {"logType": "info", "children": [], "durationId": "5163b121-a37e-41e8-b048-3b786ac0fff1"}}, {"head": {"id": "abe9071b-3cf0-4767-8703-dad925e84eb5", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937931610000, "endTime": 30937932888600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "51d12b4f-d674-4ae3-9210-5bd13ab5644f", "logId": "c72e5a1e-2df5-4f57-88ec-4b65360bb75e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51d12b4f-d674-4ae3-9210-5bd13ab5644f", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937930263700}, "additional": {"logType": "detail", "children": [], "durationId": "abe9071b-3cf0-4767-8703-dad925e84eb5"}}, {"head": {"id": "a6a3f185-57ea-4add-a886-fe8432f29789", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937930706700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab2a5826-1e57-4a1f-a300-81b89fe8d3d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937930813700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "771cb230-443f-4995-8e4a-41bb483c41b2", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937931620300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f9db817-2840-4548-9080-59def610e70c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937931779700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a3a8b7-6a04-4cd6-a1df-8c9cea4104c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937931855700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a8e593a-59e6-4687-b270-3c02bce86812", "name": "entry : default@BuildNativeWithNinja cost memory 0.05672454833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937932707600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "168146ec-f3d0-41b5-bc16-032239a03e78", "name": "runTaskFromQueue task cost before running: 696 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937932827300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c72e5a1e-2df5-4f57-88ec-4b65360bb75e", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937931610000, "endTime": 30937932888600, "totalTime": 1197400}, "additional": {"logType": "info", "children": [], "durationId": "abe9071b-3cf0-4767-8703-dad925e84eb5"}}, {"head": {"id": "0de11dc0-f1db-458a-b5c4-83b7997a857b", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937939654600, "endTime": 30937946008000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "aca00b2b-9121-4c2b-a630-bce9fc80f6ee", "logId": "debe1b4e-9ff3-43b3-86f0-804259e20fab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aca00b2b-9121-4c2b-a630-bce9fc80f6ee", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937935547700}, "additional": {"logType": "detail", "children": [], "durationId": "0de11dc0-f1db-458a-b5c4-83b7997a857b"}}, {"head": {"id": "f2c5a2de-fc79-40d2-afe9-39373d1fa934", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937935923900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a9dfc0-cfa8-4585-802d-ccb0c9035468", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937936022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1eef8f5-9c3d-4c07-9f9d-4d01204e31b4", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937937083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f70fc1-ac87-44be-8005-8f662878cccc", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937941964500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea3fea8-ac07-4043-87c1-831ed48a6e51", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937943978200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abdea953-a944-49cc-992f-865af748d53e", "name": "entry : default@ProcessResource cost memory 0.17022705078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937944147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "debe1b4e-9ff3-43b3-86f0-804259e20fab", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937939654600, "endTime": 30937946008000}, "additional": {"logType": "info", "children": [], "durationId": "0de11dc0-f1db-458a-b5c4-83b7997a857b"}}, {"head": {"id": "f71a9e50-05b4-4793-80bf-a2ed12e0adfb", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937956364100, "endTime": 30937972953600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "20777c58-5ce0-4818-a69e-89b59ff47aeb", "logId": "873e9f9a-399d-46e0-bd79-c1399e49e981"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20777c58-5ce0-4818-a69e-89b59ff47aeb", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937951128700}, "additional": {"logType": "detail", "children": [], "durationId": "f71a9e50-05b4-4793-80bf-a2ed12e0adfb"}}, {"head": {"id": "63e21cd5-75d4-4f31-9ac8-8e0517036550", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937951596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3794a352-caac-44d5-b423-918897a0841a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937951896700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca906b4b-c606-4fbe-a772-ce92ad4a22c5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937956375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e03ce0-26f2-40cd-bd28-2e6426fcc61e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937972582700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8759aea3-d9cd-4ec0-903f-359a0648167d", "name": "entry : default@GenerateLoaderJson cost memory 0.7648162841796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937972800700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873e9f9a-399d-46e0-bd79-c1399e49e981", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937956364100, "endTime": 30937972953600}, "additional": {"logType": "info", "children": [], "durationId": "f71a9e50-05b4-4793-80bf-a2ed12e0adfb"}}, {"head": {"id": "5d301744-6d9d-4a0e-be59-b93f491f79c3", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937983258700, "endTime": 30937988107800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "48f2b04e-29d3-4d98-8a5a-574c6269f08e", "logId": "5ccdabf9-1d75-49a8-a72e-9cd8528fe886"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48f2b04e-29d3-4d98-8a5a-574c6269f08e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937981553900}, "additional": {"logType": "detail", "children": [], "durationId": "5d301744-6d9d-4a0e-be59-b93f491f79c3"}}, {"head": {"id": "3c0bfd56-24f9-4e48-9ca9-6f8af87c6ed5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937982259100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973c11cc-853b-4c2e-8c89-7093ff3e01d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937982380600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f9564b-401a-487d-ac19-b918c35f14e2", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937983273000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ddd988-1279-4a1f-80fa-ac7d76d215b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937987058500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c77cb77-5e0b-4143-b921-0278934dfc04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937987171700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972bef9b-a1b6-4e5d-bb51-477d8a0818fb", "name": "entry : default@ProcessLibs cost memory 0.279815673828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937987876200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763af3a0-04aa-4e81-81ca-475036ff1b2d", "name": "runTaskFromQueue task cost before running: 752 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937988019700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ccdabf9-1d75-49a8-a72e-9cd8528fe886", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937983258700, "endTime": 30937988107800, "totalTime": 4731300}, "additional": {"logType": "info", "children": [], "durationId": "5d301744-6d9d-4a0e-be59-b93f491f79c3"}}, {"head": {"id": "f5113f0d-538e-4699-9990-b7c2fce416ba", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937996275100, "endTime": 30938023864600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e1e119c5-4519-449b-bd1c-746641471bbb", "logId": "065c3074-8ae9-40d7-9ec3-7ccf744b8963"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1e119c5-4519-449b-bd1c-746641471bbb", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937991832000}, "additional": {"logType": "detail", "children": [], "durationId": "f5113f0d-538e-4699-9990-b7c2fce416ba"}}, {"head": {"id": "17cc16c5-4060-403c-82b5-6ecffd7e1f3d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937992373800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c417c881-f1dc-442b-893a-1ec82ef2d551", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937992484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d697e4d-f34a-421a-8b58-c9858140a50c", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937993527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8baf7e10-f01c-45a1-a2c0-50d18760a507", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937996299400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ead5c700-7613-4402-8361-bbbb91b2b9ce", "name": "Incremental task entry:default@CompileResource pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938023630400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9de1ad14-dfad-4abf-a31e-35e58a00b622", "name": "entry : default@CompileResource cost memory 1.4124069213867188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938023778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "065c3074-8ae9-40d7-9ec3-7ccf744b8963", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937996275100, "endTime": 30938023864600}, "additional": {"logType": "info", "children": [], "durationId": "f5113f0d-538e-4699-9990-b7c2fce416ba"}}, {"head": {"id": "767b4d10-ca9a-403b-a326-7e23d81e0622", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938035459700, "endTime": 30938037188400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "60313607-e69b-4fd3-a75c-4f04872ed496", "logId": "3a8dcf06-3c29-4ef7-828d-42bc120abf6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60313607-e69b-4fd3-a75c-4f04872ed496", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938032228400}, "additional": {"logType": "detail", "children": [], "durationId": "767b4d10-ca9a-403b-a326-7e23d81e0622"}}, {"head": {"id": "a2bb185f-090f-4631-8822-ee656f26f9a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938032728500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0410d2e6-694d-4908-b9fa-2802d6352c12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938032845000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53be9519-0490-45a9-8a76-e9835f2968b4", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938035472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b76be3c5-deb2-4e9a-970c-afface1beb19", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938035902300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c32df12-2365-4ff0-9076-737d13df9d67", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938036957500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b5f5a32-cb1f-4de1-9c65-b265a9060ca3", "name": "entry : default@DoNativeStrip cost memory 0.07678985595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938037070200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a8dcf06-3c29-4ef7-828d-42bc120abf6d", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938035459700, "endTime": 30938037188400}, "additional": {"logType": "info", "children": [], "durationId": "767b4d10-ca9a-403b-a326-7e23d81e0622"}}, {"head": {"id": "5928a259-8c48-49e6-b5ae-30ad2abf054c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938045495400, "endTime": 30938063176200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "76dd669a-aa15-49b9-8b98-3dabbae8fcb4", "logId": "d585ba75-0fb3-4c7d-bdeb-3cd3d31234b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76dd669a-aa15-49b9-8b98-3dabbae8fcb4", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938039767000}, "additional": {"logType": "detail", "children": [], "durationId": "5928a259-8c48-49e6-b5ae-30ad2abf054c"}}, {"head": {"id": "bb5b651e-71fc-4c25-8568-97286b48482b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938040143300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da7500de-c78a-4c88-8835-68e5e19873f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938040237100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793da54f-b0f5-4934-a1d7-645fc9915d7a", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938045506400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3f6d628-d5fd-4091-a3b9-056d3b6d6849", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938062926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3963d4d-6599-45e9-874b-da5c4461face", "name": "entry : default@CompileArkTS cost memory 0.6802215576171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938063067700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d585ba75-0fb3-4c7d-bdeb-3cd3d31234b7", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938045495400, "endTime": 30938063176200}, "additional": {"logType": "info", "children": [], "durationId": "5928a259-8c48-49e6-b5ae-30ad2abf054c"}}, {"head": {"id": "87918ed2-3ebb-4e15-bda6-9c2575f5bb76", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938071729400, "endTime": 30938075378800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "87606710-4d32-4466-9031-c5b58c205dc2", "logId": "f93a0efc-5282-4062-bf57-5aabb53dcf68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87606710-4d32-4466-9031-c5b58c205dc2", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938067433400}, "additional": {"logType": "detail", "children": [], "durationId": "87918ed2-3ebb-4e15-bda6-9c2575f5bb76"}}, {"head": {"id": "685d6413-36c9-4932-b516-2c8b037e0344", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938067921600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "044b5712-72ff-48ff-be71-e901ba46cd04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938068022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b98cc3-fe7b-4c7d-8679-28d8a8a7d976", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938071741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0222a640-b47b-4e13-83ac-91a0ff354a07", "name": "entry : default@BuildJS cost memory 0.12787628173828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938075172600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa8297d0-f407-4eb1-bfe1-4ea9c99310df", "name": "runTaskFromQueue task cost before running: 839 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938075313400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f93a0efc-5282-4062-bf57-5aabb53dcf68", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938071729400, "endTime": 30938075378800, "totalTime": 3560700}, "additional": {"logType": "info", "children": [], "durationId": "87918ed2-3ebb-4e15-bda6-9c2575f5bb76"}}, {"head": {"id": "b2841104-f227-4d59-a4e3-da03258a452b", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938081452800, "endTime": 30938083473000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "76e1e02a-0b88-402a-9b93-ebb03761987c", "logId": "6e19c394-6e52-4943-8822-3f45b825fdc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76e1e02a-0b88-402a-9b93-ebb03761987c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938077436100}, "additional": {"logType": "detail", "children": [], "durationId": "b2841104-f227-4d59-a4e3-da03258a452b"}}, {"head": {"id": "dab6fe5c-daa2-4658-9a6a-185ee1f4a588", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938078304000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6376793-28a2-4397-9874-2064daf2e514", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938078561000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48508076-15e0-435b-82a3-ab32f3e4d5ea", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938081463600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80945ab6-ca41-4027-aa32-50195e3bea11", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938082046800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "394f86dd-fdcc-49ae-a587-22e816c75294", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938083294100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa64f4c-9e23-4306-a38a-7e9e01407f5b", "name": "entry : default@CacheNativeLibs cost memory 0.0917205810546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938083407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e19c394-6e52-4943-8822-3f45b825fdc2", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938081452800, "endTime": 30938083473000}, "additional": {"logType": "info", "children": [], "durationId": "b2841104-f227-4d59-a4e3-da03258a452b"}}, {"head": {"id": "c4f367df-9153-4f3f-87f3-e53b8e71e23f", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938086956800, "endTime": 30938088144900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "1d0f12e9-c523-4088-afe2-26a24a972f7f", "logId": "70dfe3ef-e392-4447-af79-a2faf6e5090f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d0f12e9-c523-4088-afe2-26a24a972f7f", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938085416200}, "additional": {"logType": "detail", "children": [], "durationId": "c4f367df-9153-4f3f-87f3-e53b8e71e23f"}}, {"head": {"id": "5ab02ce0-808b-454e-8c11-36d4d66408e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938085820600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8305026-eb7c-4dce-aeb5-494bbc425a35", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938085919700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18edcf38-7c01-42df-992f-2e118a232842", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938086966500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bce5b7b-b54d-4825-b7ec-46d21274caa9", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938087195800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f27bb20-96fc-4212-b565-ff5939982ba8", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938087909400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dadd003b-616d-4128-bd4f-7381b5f3789a", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07342529296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938088002500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70dfe3ef-e392-4447-af79-a2faf6e5090f", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938086956800, "endTime": 30938088144900}, "additional": {"logType": "info", "children": [], "durationId": "c4f367df-9153-4f3f-87f3-e53b8e71e23f"}}, {"head": {"id": "d5c861ad-c6a5-40f4-87a1-905c7dc6c598", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938100353000, "endTime": 30938127179000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "55303d0b-cb09-460f-b5fa-3bf59898c62f", "logId": "09e764f4-cced-4038-b12c-d4153d91745f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55303d0b-cb09-460f-b5fa-3bf59898c62f", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938091285900}, "additional": {"logType": "detail", "children": [], "durationId": "d5c861ad-c6a5-40f4-87a1-905c7dc6c598"}}, {"head": {"id": "fbbc8e63-f837-4847-886b-52296395bd3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938091773000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10896b35-b8a0-43f7-9df9-3737da1ee0e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938091907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0de976-8f65-4e47-b2da-adb1c6aed673", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938100364100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ff5686-380f-4c97-9e67-54374117273b", "name": "Incremental task entry:default@PackageHap pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938126581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20fa085-ecb3-4441-8c82-c984ddcca309", "name": "entry : default@PackageHap cost memory 0.8406448364257812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938127035300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e764f4-cced-4038-b12c-d4153d91745f", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938100353000, "endTime": 30938127179000}, "additional": {"logType": "info", "children": [], "durationId": "d5c861ad-c6a5-40f4-87a1-905c7dc6c598"}}, {"head": {"id": "284c5105-523f-4a46-8321-9b7bb569338f", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938135985500, "endTime": 30938142633500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "1525279e-538b-4a55-a2d4-60aba394ab7d", "logId": "3b8249a5-c9b4-4fd4-9ee9-ac65ed13af9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1525279e-538b-4a55-a2d4-60aba394ab7d", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938132050300}, "additional": {"logType": "detail", "children": [], "durationId": "284c5105-523f-4a46-8321-9b7bb569338f"}}, {"head": {"id": "91f64f25-473e-4ea9-bc4f-439e51ce238b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938132447400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331d8dff-bf10-4018-a50e-0476bfa3e702", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938132708500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074e37b9-e59d-464c-8904-0e483a8c97fb", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938135996900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97572fa3-bd83-41ec-9c98-f1533a42b296", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938136369900}, "additional": {"logType": "warn", "children": [], "durationId": "284c5105-523f-4a46-8321-9b7bb569338f"}}, {"head": {"id": "5431da9f-3c18-47bd-84b0-1042f4804ce1", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938138152900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f1cf778-acc5-403f-8d2d-19ef896c681c", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938138873200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ab3926e-213e-4035-91e9-ead<PERSON>bcee77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938139383500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b45fab-d233-448b-b627-9c1cc0f1854a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938140437600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27d3a19-0678-4ac9-90d6-d9fcd54759d7", "name": "entry : default@SignHap cost memory 0.12066650390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938141385400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b111c8-13b0-414a-ba85-aaff432967f5", "name": "runTaskFromQueue task cost before running: 905 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938141935100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b8249a5-c9b4-4fd4-9ee9-ac65ed13af9e", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938135985500, "endTime": 30938142633500, "totalTime": 5821100}, "additional": {"logType": "info", "children": [], "durationId": "284c5105-523f-4a46-8321-9b7bb569338f"}}, {"head": {"id": "003c468d-5cbe-4ab5-afca-2d1b4791ce26", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938148436400, "endTime": 30938160286100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ec91ce6a-bb70-4b31-a7e1-77e08bd0ef1b", "logId": "b1336b2c-9692-45bd-aa40-077d85ee7092"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec91ce6a-bb70-4b31-a7e1-77e08bd0ef1b", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938146755300}, "additional": {"logType": "detail", "children": [], "durationId": "003c468d-5cbe-4ab5-afca-2d1b4791ce26"}}, {"head": {"id": "bf95e861-91ce-47eb-a55e-821b0bdb346b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938147213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e8c05a-e90d-425b-8970-57b74210c93b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938147341900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bba5f5fc-32ef-43fc-b44a-eff969c5b783", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938148454500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fd1db4-9906-4aa1-9c2f-88ee99589d72", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938159572000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfaac9f5-fb2d-4a36-997f-efa6ea100a16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938159892900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a75193-70ad-42e6-b2e8-3c1b6eed3736", "name": "entry : default@CollectDebugSymbol cost memory 0.23955535888671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938160074600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92172fca-8ff4-46a9-83f8-032b8b24f79a", "name": "runTaskFromQueue task cost before running: 924 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938160224800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1336b2c-9692-45bd-aa40-077d85ee7092", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938148436400, "endTime": 30938160286100, "totalTime": 11781600}, "additional": {"logType": "info", "children": [], "durationId": "003c468d-5cbe-4ab5-afca-2d1b4791ce26"}}, {"head": {"id": "2e931404-31fe-41f2-a155-fb08c54356ca", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938163583200, "endTime": 30938166468200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "203dc9b2-1a70-4acc-bb96-87c4eda77d91", "logId": "c61408d6-3ecd-4e47-ad19-14a9fa1c39af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "203dc9b2-1a70-4acc-bb96-87c4eda77d91", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938163469900}, "additional": {"logType": "detail", "children": [], "durationId": "2e931404-31fe-41f2-a155-fb08c54356ca"}}, {"head": {"id": "f03a338a-8f3e-450d-9ac1-f9d576879aa7", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938163591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8212f9fe-1d36-4d29-b094-b78f5b056866", "name": "entry : assembleHap cost memory 0.0113525390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938165771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56942dfa-e776-4e57-9e46-4b634a3cd41a", "name": "runTaskFromQueue task cost before running: 930 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938166114300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c61408d6-3ecd-4e47-ad19-14a9fa1c39af", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938163583200, "endTime": 30938166468200, "totalTime": 2420600}, "additional": {"logType": "info", "children": [], "durationId": "2e931404-31fe-41f2-a155-fb08c54356ca"}}, {"head": {"id": "5d471db8-4907-4f09-8827-ec95ad09021b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938185500000, "endTime": 30938185525800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4135acff-7142-46bf-b0a6-77159e9b05a7", "logId": "6bca9ddd-877b-4762-8958-abd0b5fb877d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bca9ddd-877b-4762-8958-abd0b5fb877d", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938185500000, "endTime": 30938185525800}, "additional": {"logType": "info", "children": [], "durationId": "5d471db8-4907-4f09-8827-ec95ad09021b"}}, {"head": {"id": "d79e731f-b472-4353-bb7c-b29c3696c7ae", "name": "BUILD SUCCESSFUL in 949 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938185592300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "05297881-f1bd-4d90-af18-d972296b3338", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30937236839500, "endTime": 30938186392400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 5}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "fc3b5917-4cf2-4129-b43c-bd7ac6408937", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938186425900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fcaa1c7-9a2a-40eb-8ddd-5b0609cd3404", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938186617700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a5c710-5132-4aef-80f0-3391bf07a700", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938186680600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5fa4ae-d94f-4c12-bf12-8d2467016b40", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938186726100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60626b9b-0cc3-450d-a022-9cd71a6201a1", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938186781500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb4b4da-6401-4e3d-969e-093f49c4ab51", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938187543200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee105b4-d272-4b2a-9cac-cad27e743aa7", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938189003300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "999527d7-b71c-48e1-8844-5e3533dc7a8e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938189945200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb1b374-b275-4e19-a19f-31787e61c4b8", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938190173100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "911d6d49-8ef6-4bcf-8398-24fe228c7878", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938192075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f4324b2-77ae-4783-84a5-0b93e5886d08", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938192718100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9d89e3-13e0-42a3-955b-e0b674e50bfc", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938194249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63c54a85-a992-4f1a-ba21-0c0cf42130ce", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938194700700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4551e65-317a-4229-92a3-6478285c59ba", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938194899700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad4fbb5-7fb4-4a49-bbb9-e26740bc2e92", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938195045700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d29b857-547e-42f9-94e7-07983ed324d1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938195146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd256e30-79a4-40a3-af27-9a7f496ba959", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938195308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b869d720-6de3-4da9-b8d8-89e8524bda2c", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938195793400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15729234-cb00-4987-b7a4-eeda5a45d6fa", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938196123200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd74356f-ddb0-498d-b1c5-431d2fe3be51", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938196703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c46f04e-cfe8-4379-b516-7165c<PERSON><PERSON>47", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938197165400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89cfe62b-038a-46a1-a1af-cf68f24192e3", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938197248800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd32abb-e206-4116-bb3a-7227ecfd266b", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938197304400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eea1610-53ab-4798-b776-de49d0f585db", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938197348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf1bcfe-6d1a-4a8d-8d3c-31a7dc54e014", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938199503700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad14a17-61fc-4235-8e7a-5a9be4d260fc", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938200405700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415f487d-e894-4479-88a6-b17b8a8420ee", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938202229100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362a9dcf-632e-4cb8-a623-e4bd9cd24a45", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938202563700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e26828-5d74-4292-99e6-7c3df151ce19", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938204318400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57847845-796d-4ba7-96c2-b9d43301a67b", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938206403600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "464db928-3588-4bcb-8e0f-3ce222dfaae5", "name": "Incremental task entry:default@BuildJS post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938207105700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "454adb39-5210-45a1-8922-4075f8859453", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938207252100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b910796-cfb8-4255-a2e0-71a33ca66721", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938207326000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a86c1d9-4a4b-4237-9208-743126c2db71", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938207373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "389cf322-8b3a-414e-a771-7812840b386d", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938207896500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057494e0-3803-4853-b0c6-b57f82483206", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938209236300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2de3d34-d5f4-4310-b4d8-d149646f0de7", "name": "Incremental task entry:default@SignHap post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938209669300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e79165d-3d8a-45f2-9727-f12f5811ae22", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938214389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbdc591-336e-4fbc-843b-42305729877e", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938215078500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d89838-cf08-48b3-aebd-2bd79ecd7b06", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938215422500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eb963be-d231-4ea7-9b89-cd4ec1a649e5", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938215747700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}