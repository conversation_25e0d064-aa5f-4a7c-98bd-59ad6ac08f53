{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "4e318d9b-6414-4784-8e92-bdf58c67743b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833246923500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6bced00-b33f-4cf2-8ba5-8f27d799cdc1", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833251412200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89d71ae1-238d-4801-8ea5-e1d5771947df", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833251695800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c43f42c-3de3-4825-96f6-b2b90538b5d6", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833259534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4526d007-6eeb-44fa-9151-fcd93638d2b6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928674265700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb3aa292-2748-407b-a329-0cb348a367db", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928681385600, "endTime": 31929171990700}, "additional": {"children": ["fa2c26c9-cca4-4c87-8210-e7505dd190eb", "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "334b9efe-18ef-4832-953a-db7d7c99722e", "696503fc-ab77-44f3-b3cd-cb435ab8b053", "72f2f1d3-d40a-4999-a0e8-40d94c4095c0", "0a53f81f-ac26-4a9c-8e43-661ce6cfb92b", "cc5985f4-ca1f-4a94-8c5f-cc79d098e1b6"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa2c26c9-cca4-4c87-8210-e7505dd190eb", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928681387400, "endTime": 31928696673900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "87ce9db0-791f-4a5a-a355-473ec0d54c6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928696698800, "endTime": 31929169726800}, "additional": {"children": ["b1e335be-e9e3-4f9c-968e-e31ffc6834e0", "3e4c9fb6-e230-4f68-b512-a19511188347", "86327c20-720b-4952-be9e-d9c1d851035d", "f2616dde-ab12-4205-92a9-28e0e56deddd", "d1a6566b-364b-4575-9f60-2110b20d8bcd", "0a2058f4-23e4-4691-82fe-b81b3eee9f32", "384dc576-e682-4d94-b00f-fc4bf9af0f68", "af03ba56-21ba-4c23-8746-08194701189f", "56a4a90b-f09f-43d8-ac07-614d93246662"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "334b9efe-18ef-4832-953a-db7d7c99722e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929169758000, "endTime": 31929171903500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "8ce04911-5b6f-42c5-b187-acf064e2b900"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "696503fc-ab77-44f3-b3cd-cb435ab8b053", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929171912700, "endTime": 31929171914300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "78789585-0983-48a6-ab5e-0e3c85cd6ded"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72f2f1d3-d40a-4999-a0e8-40d94c4095c0", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928684379900, "endTime": 31928684449600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "827798c6-2c57-4e21-9d19-0be3275ddc7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "827798c6-2c57-4e21-9d19-0be3275ddc7a", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928684379900, "endTime": 31928684449600}, "additional": {"logType": "info", "children": [], "durationId": "72f2f1d3-d40a-4999-a0e8-40d94c4095c0", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "0a53f81f-ac26-4a9c-8e43-661ce6cfb92b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928690730200, "endTime": 31928690750900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "7bec6928-4043-4d65-b694-61c0057d14b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bec6928-4043-4d65-b694-61c0057d14b8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928690730200, "endTime": 31928690750900}, "additional": {"logType": "info", "children": [], "durationId": "0a53f81f-ac26-4a9c-8e43-661ce6cfb92b", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "2c87378e-005e-433c-9c9d-4631b88f1977", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928690818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3192c8-fc93-43d7-a919-171be730ff62", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928696498100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ce9db0-791f-4a5a-a355-473ec0d54c6c", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928681387400, "endTime": 31928696673900}, "additional": {"logType": "info", "children": [], "durationId": "fa2c26c9-cca4-4c87-8210-e7505dd190eb", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "b1e335be-e9e3-4f9c-968e-e31ffc6834e0", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928702987600, "endTime": 31928702995200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "5221eeed-fa0b-43e5-a9ff-0b1f369c8172"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e4c9fb6-e230-4f68-b512-a19511188347", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928703008700, "endTime": 31928707891800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "ddd22851-fd28-4b04-8929-155333694cc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86327c20-720b-4952-be9e-d9c1d851035d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928707913500, "endTime": 31928862424400}, "additional": {"children": ["e7db1983-2873-40db-af18-c623d53b7f3d", "1cb19ac2-f1db-4474-9b4d-4ae1110eb562", "f2db3430-2732-482d-b190-5a21ccc28a6e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "e6a92544-2308-4db2-bc07-ed2703d390dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2616dde-ab12-4205-92a9-28e0e56deddd", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928862473600, "endTime": 31928898585700}, "additional": {"children": ["7ebfe1c1-47b1-4933-a6a9-e0e0a0f21031"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "04f70c34-80c4-41e8-8f86-01e09800578c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1a6566b-364b-4575-9f60-2110b20d8bcd", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928898596700, "endTime": 31929077992400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "8020c94a-34a9-4f63-97e1-3ae127efcb65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a2058f4-23e4-4691-82fe-b81b3eee9f32", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929079808700, "endTime": 31929157361900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "2d6fa38d-8d9a-4321-a905-90e886d86a8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "384dc576-e682-4d94-b00f-fc4bf9af0f68", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929157387000, "endTime": 31929169304700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "83f72c87-67a4-45ed-9f02-bdd740e55146"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af03ba56-21ba-4c23-8746-08194701189f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929169329400, "endTime": 31929169705300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "f1055fe8-b957-4b2a-a1d1-ea1b106bfbbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5221eeed-fa0b-43e5-a9ff-0b1f369c8172", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928702987600, "endTime": 31928702995200}, "additional": {"logType": "info", "children": [], "durationId": "b1e335be-e9e3-4f9c-968e-e31ffc6834e0", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "ddd22851-fd28-4b04-8929-155333694cc6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928703008700, "endTime": 31928707891800}, "additional": {"logType": "info", "children": [], "durationId": "3e4c9fb6-e230-4f68-b512-a19511188347", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "e7db1983-2873-40db-af18-c623d53b7f3d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928708497100, "endTime": 31928708516200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86327c20-720b-4952-be9e-d9c1d851035d", "logId": "3895066d-41fb-49ad-a118-6b695d86da8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3895066d-41fb-49ad-a118-6b695d86da8f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928708497100, "endTime": 31928708516200}, "additional": {"logType": "info", "children": [], "durationId": "e7db1983-2873-40db-af18-c623d53b7f3d", "parent": "e6a92544-2308-4db2-bc07-ed2703d390dc"}}, {"head": {"id": "1cb19ac2-f1db-4474-9b4d-4ae1110eb562", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928714319000, "endTime": 31928857451700}, "additional": {"children": ["f44ccfce-9206-4198-b71c-da52d40e711c", "93b85cc0-b705-4d93-80ba-afc47c790bd2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86327c20-720b-4952-be9e-d9c1d851035d", "logId": "233c632e-f1bf-41ca-8784-c793b0799d82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f44ccfce-9206-4198-b71c-da52d40e711c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928714320900, "endTime": 31928723163300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1cb19ac2-f1db-4474-9b4d-4ae1110eb562", "logId": "4faf7e49-67b7-40fa-8122-b96ec04f1af6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93b85cc0-b705-4d93-80ba-afc47c790bd2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928723182500, "endTime": 31928857435300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1cb19ac2-f1db-4474-9b4d-4ae1110eb562", "logId": "29286f4c-49d1-4a3d-be68-95b149c1e918"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2ca5c3c-a36a-4611-837c-be4e63b45d4b", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928714326400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10d0a33c-cf39-414a-9850-7d9ef8d36b3d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928722999700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4faf7e49-67b7-40fa-8122-b96ec04f1af6", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928714320900, "endTime": 31928723163300}, "additional": {"logType": "info", "children": [], "durationId": "f44ccfce-9206-4198-b71c-da52d40e711c", "parent": "233c632e-f1bf-41ca-8784-c793b0799d82"}}, {"head": {"id": "5fc651b9-e99d-4ad9-89a8-3b63300b4b4c", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928723195200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57678c52-782c-4bda-81a1-db38cf95d229", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928731600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5bbd8ef-2249-44f0-88a1-1ad073223637", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928731837600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19bd58f2-6af1-447a-b50a-a8b1a4a8d229", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928731997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27e8ae58-3471-492a-85c6-19af1f48141d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928732104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f3f0639-5ad5-427e-becf-494969099f8f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928733875900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e44a33b-3fe4-42af-a3e1-c9f5213d6acf", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928740816700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d6ea66-5b73-48f5-a6fc-c97f2f2aeddb", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928760060300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aaa9809-74c5-4554-ab17-36b15f3bddbb", "name": "Sdk init in 60 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928801388500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559d6a28-8967-4744-8e57-26cd239f2708", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928801558600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 21}, "markType": "other"}}, {"head": {"id": "6df7eb0d-165b-46de-a898-2bf12e69c339", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928801575700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 21}, "markType": "other"}}, {"head": {"id": "6b062514-c419-4412-9064-f09278129b3e", "name": "Project task initialization takes 50 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928856882200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0565a2ce-b310-486a-be74-62e9a5f33af8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928857179500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bdac0ad-fad9-4020-99a6-0c4093632f64", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928857275300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d3cd5c-3571-43b2-a1f9-bd4be282fe6a", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928857361400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29286f4c-49d1-4a3d-be68-95b149c1e918", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928723182500, "endTime": 31928857435300}, "additional": {"logType": "info", "children": [], "durationId": "93b85cc0-b705-4d93-80ba-afc47c790bd2", "parent": "233c632e-f1bf-41ca-8784-c793b0799d82"}}, {"head": {"id": "233c632e-f1bf-41ca-8784-c793b0799d82", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928714319000, "endTime": 31928857451700}, "additional": {"logType": "info", "children": ["4faf7e49-67b7-40fa-8122-b96ec04f1af6", "29286f4c-49d1-4a3d-be68-95b149c1e918"], "durationId": "1cb19ac2-f1db-4474-9b4d-4ae1110eb562", "parent": "e6a92544-2308-4db2-bc07-ed2703d390dc"}}, {"head": {"id": "f2db3430-2732-482d-b190-5a21ccc28a6e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928862380700, "endTime": 31928862405100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86327c20-720b-4952-be9e-d9c1d851035d", "logId": "377e2eb0-fc80-408f-bcbd-6511e17924e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "377e2eb0-fc80-408f-bcbd-6511e17924e8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928862380700, "endTime": 31928862405100}, "additional": {"logType": "info", "children": [], "durationId": "f2db3430-2732-482d-b190-5a21ccc28a6e", "parent": "e6a92544-2308-4db2-bc07-ed2703d390dc"}}, {"head": {"id": "e6a92544-2308-4db2-bc07-ed2703d390dc", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928707913500, "endTime": 31928862424400}, "additional": {"logType": "info", "children": ["3895066d-41fb-49ad-a118-6b695d86da8f", "233c632e-f1bf-41ca-8784-c793b0799d82", "377e2eb0-fc80-408f-bcbd-6511e17924e8"], "durationId": "86327c20-720b-4952-be9e-d9c1d851035d", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "7ebfe1c1-47b1-4933-a6a9-e0e0a0f21031", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928863554600, "endTime": 31928898574000}, "additional": {"children": ["7f591404-ef3f-458c-a9ef-bcff0b8fe950", "2237cc32-4a33-4089-9a9e-7202a43903e4", "bf585d99-07d4-45c5-8a61-f788858445b2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2616dde-ab12-4205-92a9-28e0e56deddd", "logId": "100bfbd5-a069-4d2f-b9b1-97ad52b5bd35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f591404-ef3f-458c-a9ef-bcff0b8fe950", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928874499400, "endTime": 31928874522200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ebfe1c1-47b1-4933-a6a9-e0e0a0f21031", "logId": "d9bef218-8d0b-4983-b810-1f13c956eca7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9bef218-8d0b-4983-b810-1f13c956eca7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928874499400, "endTime": 31928874522200}, "additional": {"logType": "info", "children": [], "durationId": "7f591404-ef3f-458c-a9ef-bcff0b8fe950", "parent": "100bfbd5-a069-4d2f-b9b1-97ad52b5bd35"}}, {"head": {"id": "2237cc32-4a33-4089-9a9e-7202a43903e4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928876949800, "endTime": 31928896807100}, "additional": {"children": ["9b7a3d86-3793-4868-a615-29aa0a5efb89", "1d091e46-c266-4c08-843a-7ee75552caf4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ebfe1c1-47b1-4933-a6a9-e0e0a0f21031", "logId": "78ebecb9-9c91-493b-a04f-fdcc4d0e3e1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b7a3d86-3793-4868-a615-29aa0a5efb89", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928876951900, "endTime": 31928880700000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2237cc32-4a33-4089-9a9e-7202a43903e4", "logId": "a5825759-348a-4748-9092-aa1bed6f99bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d091e46-c266-4c08-843a-7ee75552caf4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928880721900, "endTime": 31928896795000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2237cc32-4a33-4089-9a9e-7202a43903e4", "logId": "eaebf4a5-7226-4379-880c-3357e10f4e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e142cb2-0136-4e3f-817d-bc7512b18d49", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928876957100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6539ea34-fe4f-412a-931a-9fa0b3df1bed", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928880499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5825759-348a-4748-9092-aa1bed6f99bd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928876951900, "endTime": 31928880700000}, "additional": {"logType": "info", "children": [], "durationId": "9b7a3d86-3793-4868-a615-29aa0a5efb89", "parent": "78ebecb9-9c91-493b-a04f-fdcc4d0e3e1a"}}, {"head": {"id": "a05cc5ee-49a6-48f4-91f6-40774694b99c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928880733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d8ab0c-b98d-4ac6-bc1f-f4c1ad3ec12f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928890597200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b961972-991b-4bcb-aaaf-5f3f9adcd88f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928890740200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4d88a58-e84b-45d4-b525-1c44033f4821", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928891088200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f896de-ce37-4610-a368-de7d3371239e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928891308200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3be0fb27-5d5b-40da-93ea-75022bee5884", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928891386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa3f498-22cd-4e0a-832a-44feada6cd46", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928891442500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07aecea4-b341-4100-921d-517c92d43446", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928891507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40145c36-b83b-453c-b251-162a1e0c323d", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928895313500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5000c4e8-723b-4db4-81f8-f7de734584aa", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928896575800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6209b5b3-8a2c-443d-b49f-f65e0bc7e471", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928896684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd9c8ac8-a27c-44ff-8e2c-bcea9c37c6c6", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928896741200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaebf4a5-7226-4379-880c-3357e10f4e35", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928880721900, "endTime": 31928896795000}, "additional": {"logType": "info", "children": [], "durationId": "1d091e46-c266-4c08-843a-7ee75552caf4", "parent": "78ebecb9-9c91-493b-a04f-fdcc4d0e3e1a"}}, {"head": {"id": "78ebecb9-9c91-493b-a04f-fdcc4d0e3e1a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928876949800, "endTime": 31928896807100}, "additional": {"logType": "info", "children": ["a5825759-348a-4748-9092-aa1bed6f99bd", "eaebf4a5-7226-4379-880c-3357e10f4e35"], "durationId": "2237cc32-4a33-4089-9a9e-7202a43903e4", "parent": "100bfbd5-a069-4d2f-b9b1-97ad52b5bd35"}}, {"head": {"id": "bf585d99-07d4-45c5-8a61-f788858445b2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928898533000, "endTime": 31928898554700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ebfe1c1-47b1-4933-a6a9-e0e0a0f21031", "logId": "f1347854-2c63-443f-a4fd-f3c5a993719f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1347854-2c63-443f-a4fd-f3c5a993719f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928898533000, "endTime": 31928898554700}, "additional": {"logType": "info", "children": [], "durationId": "bf585d99-07d4-45c5-8a61-f788858445b2", "parent": "100bfbd5-a069-4d2f-b9b1-97ad52b5bd35"}}, {"head": {"id": "100bfbd5-a069-4d2f-b9b1-97ad52b5bd35", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928863554600, "endTime": 31928898574000}, "additional": {"logType": "info", "children": ["d9bef218-8d0b-4983-b810-1f13c956eca7", "78ebecb9-9c91-493b-a04f-fdcc4d0e3e1a", "f1347854-2c63-443f-a4fd-f3c5a993719f"], "durationId": "7ebfe1c1-47b1-4933-a6a9-e0e0a0f21031", "parent": "04f70c34-80c4-41e8-8f86-01e09800578c"}}, {"head": {"id": "04f70c34-80c4-41e8-8f86-01e09800578c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928862473600, "endTime": 31928898585700}, "additional": {"logType": "info", "children": ["100bfbd5-a069-4d2f-b9b1-97ad52b5bd35"], "durationId": "f2616dde-ab12-4205-92a9-28e0e56deddd", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "a6601e4d-8ddf-48a6-832f-e7096fbfb7d1", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928953724400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb164cb-b17c-4eda-82b1-683409a73914", "name": "hvigorfile, resolve hvigorfile dependencies in 179 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929077606900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8020c94a-34a9-4f63-97e1-3ae127efcb65", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928898596700, "endTime": 31929077992400}, "additional": {"logType": "info", "children": [], "durationId": "d1a6566b-364b-4575-9f60-2110b20d8bcd", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "56a4a90b-f09f-43d8-ac07-614d93246662", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929079427200, "endTime": 31929079762900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "logId": "6494d61b-6d7b-44f6-9f76-58bbad67bb58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdc030bc-57aa-49cd-adc8-440958ce8370", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929079462900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6494d61b-6d7b-44f6-9f76-58bbad67bb58", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929079427200, "endTime": 31929079762900}, "additional": {"logType": "info", "children": [], "durationId": "56a4a90b-f09f-43d8-ac07-614d93246662", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "524eac83-80b5-4c68-9149-3b8b98025fcb", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929081336100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02db03c3-9bdf-46f1-b79c-a160442d00a7", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929156767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d6fa38d-8d9a-4321-a905-90e886d86a8d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929079808700, "endTime": 31929157361900}, "additional": {"logType": "info", "children": [], "durationId": "0a2058f4-23e4-4691-82fe-b81b3eee9f32", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "7620a35c-3414-43a3-a72b-3d68db449f67", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929162704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47120888-36df-4ef8-b949-37c836ab9a61", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929162882400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600e019d-5bb8-40fc-a6b6-ad5fe1c77675", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929165295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb55bb2e-b7a3-4f0b-bd54-d16a86fb1f0a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929165427000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83f72c87-67a4-45ed-9f02-bdd740e55146", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929157387000, "endTime": 31929169304700}, "additional": {"logType": "info", "children": [], "durationId": "384dc576-e682-4d94-b00f-fc4bf9af0f68", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "aec5a2b9-65bb-4f30-94cb-ef376df3c775", "name": "Configuration phase cost:467 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929169354300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1055fe8-b957-4b2a-a1d1-ea1b106bfbbe", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929169329400, "endTime": 31929169705300}, "additional": {"logType": "info", "children": [], "durationId": "af03ba56-21ba-4c23-8746-08194701189f", "parent": "1c52e5ac-ba36-4871-8549-97440fd7a4de"}}, {"head": {"id": "1c52e5ac-ba36-4871-8549-97440fd7a4de", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928696698800, "endTime": 31929169726800}, "additional": {"logType": "info", "children": ["5221eeed-fa0b-43e5-a9ff-0b1f369c8172", "ddd22851-fd28-4b04-8929-155333694cc6", "e6a92544-2308-4db2-bc07-ed2703d390dc", "04f70c34-80c4-41e8-8f86-01e09800578c", "8020c94a-34a9-4f63-97e1-3ae127efcb65", "2d6fa38d-8d9a-4321-a905-90e886d86a8d", "83f72c87-67a4-45ed-9f02-bdd740e55146", "f1055fe8-b957-4b2a-a1d1-ea1b106bfbbe", "6494d61b-6d7b-44f6-9f76-58bbad67bb58"], "durationId": "cdfbdcb5-29c2-4460-8a84-466bef7cc29b", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "cc5985f4-ca1f-4a94-8c5f-cc79d098e1b6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929171676100, "endTime": 31929171885800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb3aa292-2748-407b-a329-0cb348a367db", "logId": "98f7bd67-2bd4-4acc-977d-ed3d4abe3012"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98f7bd67-2bd4-4acc-977d-ed3d4abe3012", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929171676100, "endTime": 31929171885800}, "additional": {"logType": "info", "children": [], "durationId": "cc5985f4-ca1f-4a94-8c5f-cc79d098e1b6", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "8ce04911-5b6f-42c5-b187-acf064e2b900", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929169758000, "endTime": 31929171903500}, "additional": {"logType": "info", "children": [], "durationId": "334b9efe-18ef-4832-953a-db7d7c99722e", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "78789585-0983-48a6-ab5e-0e3c85cd6ded", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929171912700, "endTime": 31929171914300}, "additional": {"logType": "info", "children": [], "durationId": "696503fc-ab77-44f3-b3cd-cb435ab8b053", "parent": "9fa85d6d-037d-4286-9f47-d80c0460e8aa"}}, {"head": {"id": "9fa85d6d-037d-4286-9f47-d80c0460e8aa", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928681385600, "endTime": 31929171990700}, "additional": {"logType": "info", "children": ["87ce9db0-791f-4a5a-a355-473ec0d54c6c", "1c52e5ac-ba36-4871-8549-97440fd7a4de", "8ce04911-5b6f-42c5-b187-acf064e2b900", "78789585-0983-48a6-ab5e-0e3c85cd6ded", "827798c6-2c57-4e21-9d19-0be3275ddc7a", "7bec6928-4043-4d65-b694-61c0057d14b8", "98f7bd67-2bd4-4acc-977d-ed3d4abe3012"], "durationId": "eb3aa292-2748-407b-a329-0cb348a367db"}}, {"head": {"id": "5edaa3e6-a7f3-4c8c-a27e-c4ace791deeb", "name": "Configuration task cost before running: 495 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929172539900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e88c7db4-17f2-442b-a9ba-744812926565", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929192541700, "endTime": 31929218767400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "93dfbc56-1682-4ee4-9259-612405f843e7", "logId": "21cbee21-d6fa-41eb-be83-1ac3a2fb3923"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93dfbc56-1682-4ee4-9259-612405f843e7", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929180696800}, "additional": {"logType": "detail", "children": [], "durationId": "e88c7db4-17f2-442b-a9ba-744812926565"}}, {"head": {"id": "49e475a6-54d2-4cff-baa6-b8ebde2409db", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929183066000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fcdbb88-687f-40b9-adb3-09b3b9291855", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929183430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6deeee69-40ca-4765-a584-3b3d3e180f72", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929192560800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "960e30ab-3ef1-4b53-8472-8d61cb5f56a0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929218486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17834e62-6c90-4091-bf3d-71520a517e46", "name": "entry : default@PreBuild cost memory 0.3079681396484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929218683300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21cbee21-d6fa-41eb-be83-1ac3a2fb3923", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929192541700, "endTime": 31929218767400}, "additional": {"logType": "info", "children": [], "durationId": "e88c7db4-17f2-442b-a9ba-744812926565"}}, {"head": {"id": "93364c76-3ef1-4609-bb4f-a49650ca0692", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929228915700, "endTime": 31929231085900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b23a5589-de19-4178-8f93-b5c36938c88b", "logId": "ea38e06e-298a-4349-9d82-49f5b74a65d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b23a5589-de19-4178-8f93-b5c36938c88b", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929227293800}, "additional": {"logType": "detail", "children": [], "durationId": "93364c76-3ef1-4609-bb4f-a49650ca0692"}}, {"head": {"id": "e24015eb-d199-495e-bf18-e6155c2c4189", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929227869600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7c14f85-be68-4ed6-aa33-bbb6c9b4ecfe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929227993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcefe888-a719-413e-b749-3cb4373c9fd8", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929228927800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e27adb0f-6939-44e8-8cff-2710d7a07cc2", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929229822900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bbfa28e-eccd-4fcc-93a0-9136d38d4298", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929230882700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e48df00-d8cf-45d6-8483-d3c8845f9228", "name": "entry : default@GenerateMetadata cost memory 0.09326934814453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929231011200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea38e06e-298a-4349-9d82-49f5b74a65d6", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929228915700, "endTime": 31929231085900}, "additional": {"logType": "info", "children": [], "durationId": "93364c76-3ef1-4609-bb4f-a49650ca0692"}}, {"head": {"id": "763ce3d8-9b49-4d81-8e67-566dcc26965a", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233685900, "endTime": 31929234106900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8bb58a2f-af30-4fd7-8d82-068b0a78d693", "logId": "f09a7b38-7ee8-4485-8857-ca05d08f6197"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bb58a2f-af30-4fd7-8d82-068b0a78d693", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929232847700}, "additional": {"logType": "detail", "children": [], "durationId": "763ce3d8-9b49-4d81-8e67-566dcc26965a"}}, {"head": {"id": "c242e0f9-a77f-4a5e-bc79-a424ff315027", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2aee37-eaed-4b8b-a572-7a8ca0771f05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233485700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8aaf9c0-fa35-400d-8eb0-c8218b6afe16", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233696400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a9bc95f-57df-474f-9eab-47f81d94f4e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f019d457-a403-4cda-9af4-2268069ac87d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233880600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5414d7a-9460-4758-873d-900e522b4572", "name": "entry : default@ConfigureCmake cost memory 0.03614044189453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233960600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b329bf4a-c729-4eb6-b1ec-98f35b37e003", "name": "runTaskFromQueue task cost before running: 557 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929234046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f09a7b38-7ee8-4485-8857-ca05d08f6197", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929233685900, "endTime": 31929234106900, "totalTime": 338500}, "additional": {"logType": "info", "children": [], "durationId": "763ce3d8-9b49-4d81-8e67-566dcc26965a"}}, {"head": {"id": "c560c87c-8ad6-43b9-a484-cabb9b7d895a", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929237576600, "endTime": 31929241866600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f90d1df9-0afb-44d5-8b10-2488a02455a3", "logId": "42053c7a-18e3-4566-bb35-996fc037afb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f90d1df9-0afb-44d5-8b10-2488a02455a3", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929236166200}, "additional": {"logType": "detail", "children": [], "durationId": "c560c87c-8ad6-43b9-a484-cabb9b7d895a"}}, {"head": {"id": "294c4784-528c-4e91-a59b-9ad790ad6907", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929236565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a0a339-df42-4ea1-b8be-3dfd6811397f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929236685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f425bf20-05da-44ba-9924-ace7b48a69d5", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929237589400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2237032-68a1-4a5b-ba17-6a4610ca7b9f", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929240580200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "125e9d8a-7bbf-482f-9854-b7b6df9a5a5a", "name": "entry : default@MergeProfile cost memory 0.10601806640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929241690400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42053c7a-18e3-4566-bb35-996fc037afb9", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929237576600, "endTime": 31929241866600}, "additional": {"logType": "info", "children": [], "durationId": "c560c87c-8ad6-43b9-a484-cabb9b7d895a"}}, {"head": {"id": "12ffa025-95e6-4712-a819-fa44ec9e06ca", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929249016100, "endTime": 31929252857000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "50e39fa5-3ecf-4efb-994e-c8dea2116914", "logId": "b85d3ac5-0c5f-4f7c-8415-67ae6a933e7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50e39fa5-3ecf-4efb-994e-c8dea2116914", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929244789300}, "additional": {"logType": "detail", "children": [], "durationId": "12ffa025-95e6-4712-a819-fa44ec9e06ca"}}, {"head": {"id": "9e366055-d7ca-44f5-96bb-662cbe9de205", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929245777100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7db304-0248-43d0-8c1a-11b8480b84a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929246778300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8890df4-d41f-47cd-ae9f-5b4b2096c661", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929249032600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "957cd60a-d8e5-457d-a736-714c80fe7951", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929250016900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c053cb42-92ca-4b77-a056-c205723bf1ee", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929252548300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "416ef632-3e47-4599-a735-fbd14a3329fc", "name": "entry : default@CreateBuildProfile cost memory 0.10243988037109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929252768900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b85d3ac5-0c5f-4f7c-8415-67ae6a933e7b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929249016100, "endTime": 31929252857000}, "additional": {"logType": "info", "children": [], "durationId": "12ffa025-95e6-4712-a819-fa44ec9e06ca"}}, {"head": {"id": "2606ffff-21ea-4713-9af6-04d570a1840b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256350000, "endTime": 31929260972600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ae8f9e60-42a9-47ea-828c-23a69a1ee069", "logId": "05eeb34b-a476-4617-82eb-46b1265951d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae8f9e60-42a9-47ea-828c-23a69a1ee069", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929254799500}, "additional": {"logType": "detail", "children": [], "durationId": "2606ffff-21ea-4713-9af6-04d570a1840b"}}, {"head": {"id": "bfc814ce-7d84-4125-b96f-4de0d4e74539", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929255188400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae8c3651-1716-4c3e-b955-345f77a3105d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929255291800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f499873-4a37-4c16-960f-544a843dacc7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256368100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "143a389f-4a0d-4c9a-b313-b87f297e9601", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256577100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4efbc3f5-4730-479e-b29d-b6aef74cfaa6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256700800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbcf4343-f14b-4766-8143-4cdac80e74b2", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256823600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777be5b7-b34a-4d71-a532-2a372b27a920", "name": "runTaskFromQueue task cost before running: 579 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256918000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05eeb34b-a476-4617-82eb-46b1265951d4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929256350000, "endTime": 31929260972600, "totalTime": 551100}, "additional": {"logType": "info", "children": [], "durationId": "2606ffff-21ea-4713-9af6-04d570a1840b"}}, {"head": {"id": "290c5642-f1a5-46a9-9f24-a013f5d5cde6", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929284540700, "endTime": 31929285436600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e5f7cce6-d0d9-49b4-b300-5cf838c1fdc5", "logId": "218a92c6-1d13-47ee-b312-74a059cdd451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5f7cce6-d0d9-49b4-b300-5cf838c1fdc5", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929263047300}, "additional": {"logType": "detail", "children": [], "durationId": "290c5642-f1a5-46a9-9f24-a013f5d5cde6"}}, {"head": {"id": "9d3abab7-268c-4a28-9a7a-e33c9ef2dcaf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929263413500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0231a9d2-6dd6-4bc6-a7b8-e73c8ed41a3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929263522400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e279969-0f96-45f1-af78-dcd045a7a26f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929284555500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe37643-41e8-4bb4-b8b0-799cdc4fc70e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929284963000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50d2aed-a2e4-4185-974d-1c18e091f2ad", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03856658935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929285269600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3bca76b-f6b4-490d-8501-084f877a799b", "name": "runTaskFromQueue task cost before running: 608 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929285373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218a92c6-1d13-47ee-b312-74a059cdd451", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929284540700, "endTime": 31929285436600, "totalTime": 813900}, "additional": {"logType": "info", "children": [], "durationId": "290c5642-f1a5-46a9-9f24-a013f5d5cde6"}}, {"head": {"id": "4abfa402-9e13-467a-a532-924139cc4759", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929291186500, "endTime": 31929294585800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "7890a0d1-9496-4a00-804b-f603af561fa9", "logId": "bc998d12-9da9-4c1f-8bbd-9540dc449249"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7890a0d1-9496-4a00-804b-f603af561fa9", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929287515500}, "additional": {"logType": "detail", "children": [], "durationId": "4abfa402-9e13-467a-a532-924139cc4759"}}, {"head": {"id": "59584e21-dfef-48c8-84a4-77d2099bb01b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929288401600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a188995-d3dd-42a5-9e29-ba53ae28176f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929288634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b894894-cb67-4454-8fcb-93eeefc5fbac", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929291197300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8166019d-f5ed-408e-a54d-f132028f2f33", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929293324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38699c5f-bb90-4b58-81f7-a592e49e6690", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929293477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3562029e-18cc-4230-af0f-3ef70d7205ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929293571800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16e1b67a-cd34-4a63-b3de-dc3068b66c02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929293885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a57dc121-94f3-4823-8017-ef256f72b0ff", "name": "entry : default@ProcessIntegratedHsp cost memory 0.117950439453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929294411200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec61eb5-2259-4a39-81b3-f61cd2305057", "name": "runTaskFromQueue task cost before running: 617 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929294520500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc998d12-9da9-4c1f-8bbd-9540dc449249", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929291186500, "endTime": 31929294585800, "totalTime": 3313400}, "additional": {"logType": "info", "children": [], "durationId": "4abfa402-9e13-467a-a532-924139cc4759"}}, {"head": {"id": "0a252fe8-dbbb-4b67-8bec-b59eb5582b83", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300052300, "endTime": 31929300435600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9a3d5b91-28c3-4ad5-b5a6-dd9f7b29af0a", "logId": "c9f378f4-e3bb-4148-a54e-64afcaa605c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a3d5b91-28c3-4ad5-b5a6-dd9f7b29af0a", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929298829700}, "additional": {"logType": "detail", "children": [], "durationId": "0a252fe8-dbbb-4b67-8bec-b59eb5582b83"}}, {"head": {"id": "28b62ddd-9aba-462e-af54-99697d170724", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929299245300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0341a055-af63-4905-8c28-70c7d97c6c38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929299372800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23bc8ee1-ab2d-4ee8-992e-e7b9094baf04", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300062700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79e262b5-871d-4ad4-a642-5a8552731444", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300178100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec4e9ea0-1f1e-4e03-adb1-5b8609eac0e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300231700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b89a51c-9293-4db9-9138-9fc79e8eaec8", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300300200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52362a5-5018-46ac-8fa0-b033b2006b13", "name": "runTaskFromQueue task cost before running: 623 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300369700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f378f4-e3bb-4148-a54e-64afcaa605c7", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929300052300, "endTime": 31929300435600, "totalTime": 301700}, "additional": {"logType": "info", "children": [], "durationId": "0a252fe8-dbbb-4b67-8bec-b59eb5582b83"}}, {"head": {"id": "2338e9ae-7c49-49cd-b05b-a6e3fd781140", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929303751500, "endTime": 31929307940400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7c340baf-f1da-41f9-a67d-4105ddaae968", "logId": "7c93e07a-7231-47f7-b9fd-99d1069c2663"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c340baf-f1da-41f9-a67d-4105ddaae968", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929301994400}, "additional": {"logType": "detail", "children": [], "durationId": "2338e9ae-7c49-49cd-b05b-a6e3fd781140"}}, {"head": {"id": "eb31265c-2694-4e90-92bc-d24073dae891", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929302671800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fa946fb-3da1-469c-a844-91cc3a1fb372", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929302780400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c77439a-04db-4253-88d7-84237005ad1e", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929303762100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b82201-9a14-4fff-b3ee-165d48baff74", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929307532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0106b8b6-43a8-450e-b8ab-eff6c1b29821", "name": "entry : default@MakePackInfo cost memory 0.13919830322265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929307847800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c93e07a-7231-47f7-b9fd-99d1069c2663", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929303751500, "endTime": 31929307940400}, "additional": {"logType": "info", "children": [], "durationId": "2338e9ae-7c49-49cd-b05b-a6e3fd781140"}}, {"head": {"id": "2cdb3064-c972-4691-b071-eb2ab7e49b67", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929312105600, "endTime": 31929316206600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "d35f086a-c15d-4e8e-8e36-c7b22364a89e", "logId": "d8f990b0-30b2-40e8-97a5-85df71df36b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d35f086a-c15d-4e8e-8e36-c7b22364a89e", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929310328500}, "additional": {"logType": "detail", "children": [], "durationId": "2cdb3064-c972-4691-b071-eb2ab7e49b67"}}, {"head": {"id": "e81b414c-eab9-48b2-8aa7-d5c04aa96542", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929310740000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "527350c1-c156-48a1-809b-7f2b7966039f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929310844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a7dc95-7308-4e1d-a373-fceaa9142170", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929312251100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1db178f3-9000-4053-940a-c59ebe83e0ca", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929312440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eece8ad-6d07-4665-b9c6-b69da2e68d4f", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929313144900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c6f2fe-02c0-47a3-a2e6-95beb181373c", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929315448600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde986dc-736e-4d4f-a716-48bdc0e927a6", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929315766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0a6f2b-94dd-495c-8ff8-61c1a84f3817", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929315882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ed03a6-3b4e-424f-b361-5a53a06b0bf2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929315943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d036d44f-19bb-4663-aa95-e9279efd0956", "name": "entry : default@SyscapTransform cost memory 0.15081024169921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929316028000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be0aacc6-843f-46c4-a6c9-6681747b5f3e", "name": "runTaskFromQueue task cost before running: 639 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929316106600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f990b0-30b2-40e8-97a5-85df71df36b1", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929312105600, "endTime": 31929316206600, "totalTime": 3982200}, "additional": {"logType": "info", "children": [], "durationId": "2cdb3064-c972-4691-b071-eb2ab7e49b67"}}, {"head": {"id": "236320b3-b80b-43f4-84df-01ac6aae4d19", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929323029400, "endTime": 31929325224900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "d12399d5-97ba-4232-9552-1fc0bf24ea3e", "logId": "4e997e3a-b320-4aa2-9898-85fe2398fff4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d12399d5-97ba-4232-9552-1fc0bf24ea3e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929318090700}, "additional": {"logType": "detail", "children": [], "durationId": "236320b3-b80b-43f4-84df-01ac6aae4d19"}}, {"head": {"id": "508502fb-5b5b-4927-a29f-d4c1494d310e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929318512100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45b8275a-ee6c-4197-83bd-d59a58253150", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929318648500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f4a31e-24c0-4c2d-a24b-8e3343eae59e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929323044800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d994e27a-bc1f-4f1b-9303-636ea7063d49", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929324976200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f48e188-c659-4ac5-82f2-1cb5e27287f0", "name": "entry : default@ProcessProfile cost memory 0.05995941162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929325110100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e997e3a-b320-4aa2-9898-85fe2398fff4", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929323029400, "endTime": 31929325224900}, "additional": {"logType": "info", "children": [], "durationId": "236320b3-b80b-43f4-84df-01ac6aae4d19"}}, {"head": {"id": "b475f146-f8dd-429e-bb50-41bb42b1fbc9", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929330360500, "endTime": 31929338420200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7ef251de-d32b-46dd-a24e-aef7bdf63d91", "logId": "ac6286a6-379f-46c5-8293-33d516981210"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ef251de-d32b-46dd-a24e-aef7bdf63d91", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929327775400}, "additional": {"logType": "detail", "children": [], "durationId": "b475f146-f8dd-429e-bb50-41bb42b1fbc9"}}, {"head": {"id": "df0a6167-562d-429a-b7a7-1263af0fa3b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929328206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2677b119-ee82-43bf-ab37-4155b7ed49f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929328318900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40badc6-f528-4884-b634-1e52d55fd89f", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929330373500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3239bba-1ba7-4e6f-bc07-17ad401d348d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929338043700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c7a04d-781e-4790-8c2f-fc98d989c30a", "name": "entry : default@ProcessRouterMap cost memory 0.2028045654296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929338337000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6286a6-379f-46c5-8293-33d516981210", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929330360500, "endTime": 31929338420200}, "additional": {"logType": "info", "children": [], "durationId": "b475f146-f8dd-429e-bb50-41bb42b1fbc9"}}, {"head": {"id": "7d07ab3f-8d71-401e-8ef7-4adf22afdb88", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929344082200, "endTime": 31929345556600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f3ab49e7-dc3d-4ae1-b582-b0cb71f0e0f9", "logId": "5df64845-2ee9-4ff5-ad45-73a6e19f8e0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3ab49e7-dc3d-4ae1-b582-b0cb71f0e0f9", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929341740000}, "additional": {"logType": "detail", "children": [], "durationId": "7d07ab3f-8d71-401e-8ef7-4adf22afdb88"}}, {"head": {"id": "8c55c023-0d72-438a-95fa-7c5c6003367e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929342398700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01f5e0f9-b9f5-44ef-9ad7-62cc66a32935", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929342562000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8971e0d2-3bc8-457b-a48a-6de2bb7636b2", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929344095400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a402bc-e67c-4c2a-b97d-2d1a2b8d2ed2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929344348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3628eb5-108a-464e-a6cf-cfddcc12f80e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929344416000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d43ed73-0d9c-4f41-ab64-8f666f342875", "name": "entry : default@BuildNativeWithNinja cost memory 0.056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929345281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cc3a892-dc8c-4e89-9d73-e1a0080ab57c", "name": "runTaskFromQueue task cost before running: 668 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929345472600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df64845-2ee9-4ff5-ad45-73a6e19f8e0d", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929344082200, "endTime": 31929345556600, "totalTime": 1353200}, "additional": {"logType": "info", "children": [], "durationId": "7d07ab3f-8d71-401e-8ef7-4adf22afdb88"}}, {"head": {"id": "0eba08e9-318c-4264-94db-8d803a15f0da", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929360557600, "endTime": 31929377111300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "de8112c8-1a5d-4d9f-9516-5d9e40f79f20", "logId": "9dfe4825-bdc8-4386-8728-b32054a7d8eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de8112c8-1a5d-4d9f-9516-5d9e40f79f20", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929354381000}, "additional": {"logType": "detail", "children": [], "durationId": "0eba08e9-318c-4264-94db-8d803a15f0da"}}, {"head": {"id": "dee086b4-f3f6-4ec5-b950-9cdfdb9de3c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929355198300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b70cb83-48bb-4120-bbf7-23b3179e4a01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929355448300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af3a68b-919b-4138-97b6-c4f92b1af341", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929358250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf45229-00d0-4856-acdd-2f17a116eb10", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929367871700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b340f77-8800-42e2-b26d-326eeedeb784", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929374240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa25c603-cc88-4f5f-8180-520c7b4ca84c", "name": "entry : default@ProcessResource cost memory 0.16942596435546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929374437700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dfe4825-bdc8-4386-8728-b32054a7d8eb", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929360557600, "endTime": 31929377111300}, "additional": {"logType": "info", "children": [], "durationId": "0eba08e9-318c-4264-94db-8d803a15f0da"}}, {"head": {"id": "58e2bce8-cfb2-481e-af22-bb6411861a09", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929391014800, "endTime": 31929436947500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "95c9328e-5fb9-4463-93be-1a2ed7685d53", "logId": "2e1e9017-79a6-43d1-94ab-c9f319acf8bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95c9328e-5fb9-4463-93be-1a2ed7685d53", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929382222300}, "additional": {"logType": "detail", "children": [], "durationId": "58e2bce8-cfb2-481e-af22-bb6411861a09"}}, {"head": {"id": "176e52cf-e05e-4a85-b59b-590bc605fc31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929382923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "403fe2d3-1786-45ea-b4db-fdc27fff0735", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929383064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f59f218-4196-487c-b1c3-839beda21fcf", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929391029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d4193c-6b02-4610-ba48-7dfe2c920b1e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929436030700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ce7715-1af9-496f-9e7a-2d84738aacba", "name": "entry : default@GenerateLoaderJson cost memory 0.762481689453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929436576100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1e9017-79a6-43d1-94ab-c9f319acf8bb", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929391014800, "endTime": 31929436947500}, "additional": {"logType": "info", "children": [], "durationId": "58e2bce8-cfb2-481e-af22-bb6411861a09"}}, {"head": {"id": "e80184dd-86d7-4c7c-acf6-91cd04a71a47", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929462212600, "endTime": 31929468841000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "511ea38a-adb3-4daa-ba72-d0bfb9530dfc", "logId": "a33ac181-b775-447d-9216-8270cb30138a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "511ea38a-adb3-4daa-ba72-d0bfb9530dfc", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929457030500}, "additional": {"logType": "detail", "children": [], "durationId": "e80184dd-86d7-4c7c-acf6-91cd04a71a47"}}, {"head": {"id": "cb4fa3a5-5728-4978-93a2-6dc6198ac504", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929458029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c33882de-08ee-4e73-bce5-41be7a9dd498", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929458520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59458276-5ece-49ed-9e08-4bde63238b3a", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929462262500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07fd7c23-5583-4fc6-b4f3-4522c1a34d98", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929466813900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07dd3520-722e-4584-b032-b9b1cb73ba02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929467019100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd983ed3-cba3-4efa-8208-1e97e233daf8", "name": "entry : default@ProcessLibs cost memory 0.12518310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929468473500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b82d53e-cdd8-49eb-bf74-eedfa8138386", "name": "runTaskFromQueue task cost before running: 791 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929468681000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a33ac181-b775-447d-9216-8270cb30138a", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929462212600, "endTime": 31929468841000, "totalTime": 6384300}, "additional": {"logType": "info", "children": [], "durationId": "e80184dd-86d7-4c7c-acf6-91cd04a71a47"}}, {"head": {"id": "e9352ae4-3f85-40f1-8a5c-8abf2980d3d2", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929482323200, "endTime": 31929531450000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "833c413f-e938-4c16-9367-e724a67e0f27", "logId": "f956543d-c3f5-4837-a995-f6433a85b0c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "833c413f-e938-4c16-9367-e724a67e0f27", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929472739200}, "additional": {"logType": "detail", "children": [], "durationId": "e9352ae4-3f85-40f1-8a5c-8abf2980d3d2"}}, {"head": {"id": "f53a5253-bc45-4fc4-9e0c-ca2a78b306a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929473460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e142d57b-2b14-4c2e-8958-273c4aea072a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929473637800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "788a8f37-51a1-4f04-b8f1-34e72630df6e", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929475170100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46f3462-5ee7-4355-96eb-45b3be96b4b8", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929482351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aea951b1-b2ff-4188-86f2-ce54c71634b5", "name": "Incremental task entry:default@CompileResource pre-execution cost: 47 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929531223600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0635f5ec-b5cb-4776-b2c3-01199482b165", "name": "entry : default@CompileResource cost memory 1.408172607421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929531350900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f956543d-c3f5-4837-a995-f6433a85b0c1", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929482323200, "endTime": 31929531450000}, "additional": {"logType": "info", "children": [], "durationId": "e9352ae4-3f85-40f1-8a5c-8abf2980d3d2"}}, {"head": {"id": "3f39d371-4846-46a2-9f54-3a75b35c47bb", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929539041200, "endTime": 31929541270000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d4bd5a88-c1d6-4576-a1ba-35889623c988", "logId": "5e636792-5f57-4d70-bb4b-6564f2f6037f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4bd5a88-c1d6-4576-a1ba-35889623c988", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929535296700}, "additional": {"logType": "detail", "children": [], "durationId": "3f39d371-4846-46a2-9f54-3a75b35c47bb"}}, {"head": {"id": "af0272f6-20f5-4cec-888b-28aad32ecd37", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929535718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e829899-f1c3-4085-b7fb-d3689a3286ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929535825900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e26166ee-3727-471e-b7ad-5293cb0c488e", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929539068400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77491768-9d8a-48a0-86c1-5f28cd69979b", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929539519400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac9ba4b7-9c0e-47ce-b55d-39f576aa088c", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929540916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce839598-e1e9-4568-b924-b93cd346707f", "name": "entry : default@DoNativeStrip cost memory 0.075531005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929541172000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e636792-5f57-4d70-bb4b-6564f2f6037f", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929539041200, "endTime": 31929541270000}, "additional": {"logType": "info", "children": [], "durationId": "3f39d371-4846-46a2-9f54-3a75b35c47bb"}}, {"head": {"id": "7f75c8c1-c8ee-42c0-ad8e-f0ea9c474066", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929548873400, "endTime": 31931738086000}, "additional": {"children": ["efb41890-dd6d-4f17-ba9b-c5fbde06d346", "4dd3f3d8-9129-4f5f-bef7-e34cfea99c46"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "d5dbb1e4-e45d-45be-bcc2-1b5d1b0e6b0d", "logId": "5b4ec021-6c4f-49ea-8e89-47af51029471"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5dbb1e4-e45d-45be-bcc2-1b5d1b0e6b0d", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929544591600}, "additional": {"logType": "detail", "children": [], "durationId": "7f75c8c1-c8ee-42c0-ad8e-f0ea9c474066"}}, {"head": {"id": "b55c1f07-78fe-44c1-a1eb-87c8eabe514a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929545065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d281fbe-b920-49b9-b8a1-839bb3715bc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929545170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60341123-93c5-48cb-8333-a2ccc238de7a", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929548885000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49a43a8-3494-4f8a-afa1-c851df4df451", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929563677800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1873470-9e64-4d8d-9252-dfaa9c66313b", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929563809600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc4aed2-7930-4334-8cfb-1c9ac61596e9", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929576862800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f363899a-47de-4a92-9a40-cf2ea0a41b71", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929577647500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a893643-6baa-411a-97cb-765d25a80691", "name": "default@CompileArkTS work[71] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929579068300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31929836043500, "endTime": 31931730950900}, "additional": {"children": ["1bf3743c-81c1-45f8-9fe6-50ed3397f534", "33e6ea76-d928-48f3-a4d2-00074df9ccde", "228933a8-2466-45dc-8877-679215049f62", "db097216-9e77-4ed2-be6f-aba07311e415", "5f5252ed-e6b6-49dd-b3d9-f699680e39ec", "468a951b-e0f7-40dd-ab02-33c802ba8109"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7f75c8c1-c8ee-42c0-ad8e-f0ea9c474066", "logId": "326dc53e-5db4-469c-a80f-782e4b7be774"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3f8093d-cc5e-410f-9584-dc4e13128c69", "name": "default@CompileArkTS work[71] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929579831700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a9d0544-92cf-4bb2-8bb8-78f842064048", "name": "default@CompileArkTS work[71] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929579923200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a7495e-23a6-4946-85ea-9bcfa4ec282d", "name": "CopyResources startTime: 31929580000600", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929580002300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e88695-12a1-489e-96fc-044cb0e1cca8", "name": "default@CompileArkTS work[72] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929580053100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd3f3d8-9129-4f5f-bef7-e34cfea99c46", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31931404091800, "endTime": 31931418926100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7f75c8c1-c8ee-42c0-ad8e-f0ea9c474066", "logId": "626af5f5-cba6-4b87-9309-9842e04a00c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bc7be05-ed75-4229-aa89-921bf3c5a0d9", "name": "default@CompileArkTS work[72] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929580787500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe9304f-1a8a-4cec-a819-1d0b3c8e2964", "name": "default@CompileArkTS work[72] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929580866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "362ab573-5833-4b25-bf05-769a3d241c6a", "name": "entry : default@CompileArkTS cost memory 1.5843582153320312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929580945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e13510c3-cbcc-44ea-bb75-76b4c87b5e6f", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929587703300, "endTime": 31929649960600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "4bd8d351-cbec-4ddc-a873-28abf0c584d7", "logId": "b9abdb31-c135-4aca-a34b-fe96ce84f531"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bd8d351-cbec-4ddc-a873-28abf0c584d7", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929582237900}, "additional": {"logType": "detail", "children": [], "durationId": "e13510c3-cbcc-44ea-bb75-76b4c87b5e6f"}}, {"head": {"id": "7a730e9e-4993-4f2c-997e-220e85317a8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929582563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a90e0e-bc56-4740-98b7-2dbaeb16609a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929582660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af301d2-8a36-4cf5-87de-b1f29746cfc6", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929587723200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bddffd5-6ad6-4152-a127-4c6cd8d63d74", "name": "entry : default@BuildJS cost memory 0.12693023681640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929649664700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77777905-1e39-4f6f-8afc-4d3a2c660f9a", "name": "runTaskFromQueue task cost before running: 972 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929649836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9abdb31-c135-4aca-a34b-fe96ce84f531", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929587703300, "endTime": 31929649960600, "totalTime": 62090200}, "additional": {"logType": "info", "children": [], "durationId": "e13510c3-cbcc-44ea-bb75-76b4c87b5e6f"}}, {"head": {"id": "96235810-2350-4ac5-9a31-136885ff5f97", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929656536100, "endTime": 31929658723100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e4e25bf2-1460-4c2b-8d61-73c8024591a2", "logId": "bbec209a-59fd-4594-a3b3-b9b0d6c23e5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4e25bf2-1460-4c2b-8d61-73c8024591a2", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929652249600}, "additional": {"logType": "detail", "children": [], "durationId": "96235810-2350-4ac5-9a31-136885ff5f97"}}, {"head": {"id": "4cd260f1-83b0-4901-adb5-2eb28147e19d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929652851500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57fd63e-97bf-4974-8062-562ddcade5ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929652962500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49b35bcd-c54d-4805-bc1e-16b671e23355", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929656548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f1f5a95-6c50-49a9-af62-f5c39c31f8a7", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929657119900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "418a1748-4f6b-4eeb-8188-eb8e87491c00", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929658477600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a4bcbb3-f272-4c34-903d-8d38c28ecb42", "name": "entry : default@CacheNativeLibs cost memory 0.089202880859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929658606000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbec209a-59fd-4594-a3b3-b9b0d6c23e5c", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929656536100, "endTime": 31929658723100}, "additional": {"logType": "info", "children": [], "durationId": "96235810-2350-4ac5-9a31-136885ff5f97"}}, {"head": {"id": "a14af166-34f9-4042-b97c-32089b107122", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929835072700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "322c6bad-6ec8-4c4a-afc0-77a8abd7cf17", "name": "default@CompileArkTS work[71] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929835908600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82337ce8-b754-4115-9b6e-2bacdd0044a7", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929836044300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2a1d41-0d07-4ac3-976a-5679f81e8b2a", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929836101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f375f8e7-e2ea-46e5-8448-97701ee15f60", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929836445200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba66b17-3d19-4e7a-b558-5a07e0375c1a", "name": "default@CompileArkTS work[72] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929838049500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4ff38a4-d9ac-4b1b-a1ce-ebbaa389705c", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931419126800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68efeed4-0c70-40aa-ae97-39242fddf8cf", "name": "CopyResources is end, endTime: 31931419535400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931419541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dece905-05bb-4ae3-92e1-8e44e50d3256", "name": "default@CompileArkTS work[72] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931420419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626af5f5-cba6-4b87-9309-9842e04a00c0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31931404091800, "endTime": 31931418926100}, "additional": {"logType": "info", "children": [], "durationId": "4dd3f3d8-9129-4f5f-bef7-e34cfea99c46", "parent": "5b4ec021-6c4f-49ea-8e89-47af51029471"}}, {"head": {"id": "b14a841b-959d-4315-8d08-56b20a02cc8d", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931731393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bf3743c-81c1-45f8-9fe6-50ed3397f534", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31929836353000, "endTime": 31929840910400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "logId": "ee461651-ef5a-47ca-9eea-a9850f8d104b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee461651-ef5a-47ca-9eea-a9850f8d104b", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929836353000, "endTime": 31929840910400}, "additional": {"logType": "info", "children": [], "durationId": "1bf3743c-81c1-45f8-9fe6-50ed3397f534", "parent": "326dc53e-5db4-469c-a80f-782e4b7be774"}}, {"head": {"id": "33e6ea76-d928-48f3-a4d2-00074df9ccde", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31929840928700, "endTime": 31929841053700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "logId": "9efd8c48-9e1d-401f-a482-a73846413a15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9efd8c48-9e1d-401f-a482-a73846413a15", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929840928700, "endTime": 31929841053700}, "additional": {"logType": "info", "children": [], "durationId": "33e6ea76-d928-48f3-a4d2-00074df9ccde", "parent": "326dc53e-5db4-469c-a80f-782e4b7be774"}}, {"head": {"id": "228933a8-2466-45dc-8877-679215049f62", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31929841066400, "endTime": 31929841100300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "logId": "cf04435f-fdfb-4842-9778-e4051ea8c6e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf04435f-fdfb-4842-9778-e4051ea8c6e5", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929841066400, "endTime": 31929841100300}, "additional": {"logType": "info", "children": [], "durationId": "228933a8-2466-45dc-8877-679215049f62", "parent": "326dc53e-5db4-469c-a80f-782e4b7be774"}}, {"head": {"id": "db097216-9e77-4ed2-be6f-aba07311e415", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31929841119000, "endTime": 31931637867100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "logId": "4bc87dba-fac8-4eaa-97fb-f8e9b9abd138"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bc87dba-fac8-4eaa-97fb-f8e9b9abd138", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929841119000, "endTime": 31931637867100}, "additional": {"logType": "info", "children": [], "durationId": "db097216-9e77-4ed2-be6f-aba07311e415", "parent": "326dc53e-5db4-469c-a80f-782e4b7be774"}}, {"head": {"id": "5f5252ed-e6b6-49dd-b3d9-f699680e39ec", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931637884900, "endTime": 31931643002200}, "additional": {"children": ["0da9634c-1a5a-4900-a7ca-61e060cf6b3a", "42f8951a-714f-43bb-8fa2-a4e1c522f831", "7392b13e-3d59-4405-ac75-d8a08230d3e6"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "logId": "7f241e5a-f72e-4293-9129-52013ab5189c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f241e5a-f72e-4293-9129-52013ab5189c", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931637884900, "endTime": 31931643002200}, "additional": {"logType": "info", "children": ["6e61b953-32bb-4bed-b327-545f870fec76", "6c2848d1-ccc0-438f-8a57-e7899710aed3", "4bc56512-b304-4c5e-a81a-b74b06ed1a24"], "durationId": "5f5252ed-e6b6-49dd-b3d9-f699680e39ec", "parent": "326dc53e-5db4-469c-a80f-782e4b7be774"}}, {"head": {"id": "0da9634c-1a5a-4900-a7ca-61e060cf6b3a", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931637903500, "endTime": 31931637911500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f5252ed-e6b6-49dd-b3d9-f699680e39ec", "logId": "6e61b953-32bb-4bed-b327-545f870fec76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e61b953-32bb-4bed-b327-545f870fec76", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931637903500, "endTime": 31931637911500}, "additional": {"logType": "info", "children": [], "durationId": "0da9634c-1a5a-4900-a7ca-61e060cf6b3a", "parent": "7f241e5a-f72e-4293-9129-52013ab5189c"}}, {"head": {"id": "42f8951a-714f-43bb-8fa2-a4e1c522f831", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931637915100, "endTime": 31931640046600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f5252ed-e6b6-49dd-b3d9-f699680e39ec", "logId": "6c2848d1-ccc0-438f-8a57-e7899710aed3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c2848d1-ccc0-438f-8a57-e7899710aed3", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931637915100, "endTime": 31931640046600}, "additional": {"logType": "info", "children": [], "durationId": "42f8951a-714f-43bb-8fa2-a4e1c522f831", "parent": "7f241e5a-f72e-4293-9129-52013ab5189c"}}, {"head": {"id": "7392b13e-3d59-4405-ac75-d8a08230d3e6", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931640050500, "endTime": 31931642987700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5f5252ed-e6b6-49dd-b3d9-f699680e39ec", "logId": "4bc56512-b304-4c5e-a81a-b74b06ed1a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bc56512-b304-4c5e-a81a-b74b06ed1a24", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931640050500, "endTime": 31931642987700}, "additional": {"logType": "info", "children": [], "durationId": "7392b13e-3d59-4405-ac75-d8a08230d3e6", "parent": "7f241e5a-f72e-4293-9129-52013ab5189c"}}, {"head": {"id": "468a951b-e0f7-40dd-ab02-33c802ba8109", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931643030300, "endTime": 31931730805000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "logId": "dbdbdc62-7ce7-4d36-94d7-e237a3ed2e82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbdbdc62-7ce7-4d36-94d7-e237a3ed2e82", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931643030300, "endTime": 31931730805000}, "additional": {"logType": "info", "children": [], "durationId": "468a951b-e0f7-40dd-ab02-33c802ba8109", "parent": "326dc53e-5db4-469c-a80f-782e4b7be774"}}, {"head": {"id": "97786ce2-0aa3-4675-8a00-bb0048ad0b65", "name": "default@CompileArkTS work[71] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931737733200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "326dc53e-5db4-469c-a80f-782e4b7be774", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31929836043500, "endTime": 31931730950900}, "additional": {"logType": "info", "children": ["ee461651-ef5a-47ca-9eea-a9850f8d104b", "9efd8c48-9e1d-401f-a482-a73846413a15", "cf04435f-fdfb-4842-9778-e4051ea8c6e5", "4bc87dba-fac8-4eaa-97fb-f8e9b9abd138", "7f241e5a-f72e-4293-9129-52013ab5189c", "dbdbdc62-7ce7-4d36-94d7-e237a3ed2e82"], "durationId": "efb41890-dd6d-4f17-ba9b-c5fbde06d346", "parent": "5b4ec021-6c4f-49ea-8e89-47af51029471"}}, {"head": {"id": "111c62e9-be41-4de1-954d-0a18e9ab60a3", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931737946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b4ec021-6c4f-49ea-8e89-47af51029471", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31929548873400, "endTime": 31931738086000, "totalTime": 1927034600}, "additional": {"logType": "info", "children": ["326dc53e-5db4-469c-a80f-782e4b7be774", "626af5f5-cba6-4b87-9309-9842e04a00c0"], "durationId": "7f75c8c1-c8ee-42c0-ad8e-f0ea9c474066"}}, {"head": {"id": "76ee5962-68fe-4f4d-8b84-bb44c9623c79", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931746502400, "endTime": 31931748135700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "380258d5-a438-4480-8eb9-cc144c20b6b6", "logId": "2a3aa98e-9d55-4f85-88e2-0f1e582038e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "380258d5-a438-4480-8eb9-cc144c20b6b6", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931744448600}, "additional": {"logType": "detail", "children": [], "durationId": "76ee5962-68fe-4f4d-8b84-bb44c9623c79"}}, {"head": {"id": "b9ce61ff-5390-47bf-bce1-049ac952d1ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931745305500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77224030-85d2-4ca4-95bb-d6f8ed4351d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931745459200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7aca2d-5372-48b7-8833-fdf0c9255cf1", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931746512800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d935b9-c5bf-48bc-8de6-78efeeff4825", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931746980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0fef30e-3e04-4818-a34c-4eed3b03e973", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931747888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ab02a8-f322-49f0-a81d-3f86193e43f6", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0731048583984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931748001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3aa98e-9d55-4f85-88e2-0f1e582038e9", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931746502400, "endTime": 31931748135700}, "additional": {"logType": "info", "children": [], "durationId": "76ee5962-68fe-4f4d-8b84-bb44c9623c79"}}, {"head": {"id": "42634232-514e-42de-8e1b-6b6010cd9d7b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931773452400, "endTime": 31932328219600}, "additional": {"children": ["1b5e143a-be70-45ac-9cc9-23ea6b14861b", "60612d9b-aa0c-47c7-8a41-945683eec13e", "7b32b9b0-9aae-4834-8770-c6a4a01c2740"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "4a8b4a4b-3280-45c5-a131-0a5d6071f58c", "logId": "85cdd7a3-d78f-4515-bc51-5edd804abefa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a8b4a4b-3280-45c5-a131-0a5d6071f58c", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931753524100}, "additional": {"logType": "detail", "children": [], "durationId": "42634232-514e-42de-8e1b-6b6010cd9d7b"}}, {"head": {"id": "950a52fb-7b1a-4dc7-a28a-a4408f547e01", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931753981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879f45a7-a21b-4fcb-bc36-a46cf70744e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931754091200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a5621e-fe79-4e1c-8948-01756a67209f", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931773465400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958f689c-0698-4593-b76a-3c27f7ee36c5", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931798614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa0c52e5-12fd-4557-a690-179eef399c6e", "name": "Incremental task entry:default@PackageHap pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931798788800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c11da2-d559-45ac-8edd-efbc81c417af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931798959400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5e6d04-d3e3-4aff-b38c-3ee3dabe21cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931799059900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5e143a-be70-45ac-9cc9-23ea6b14861b", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931800525400, "endTime": 31931802399900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42634232-514e-42de-8e1b-6b6010cd9d7b", "logId": "cc244297-280f-4160-a90e-4945719eda8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "715220fe-ef29-41f6-81eb-0937d53152f1", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931802225700}, "additional": {"logType": "debug", "children": [], "durationId": "42634232-514e-42de-8e1b-6b6010cd9d7b"}}, {"head": {"id": "cc244297-280f-4160-a90e-4945719eda8b", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931800525400, "endTime": 31931802399900}, "additional": {"logType": "info", "children": [], "durationId": "1b5e143a-be70-45ac-9cc9-23ea6b14861b", "parent": "85cdd7a3-d78f-4515-bc51-5edd804abefa"}}, {"head": {"id": "60612d9b-aa0c-47c7-8a41-945683eec13e", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931803111400, "endTime": 31931805614900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42634232-514e-42de-8e1b-6b6010cd9d7b", "logId": "3f97e71a-a358-4f44-a111-572fbc09f3c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72fb372d-c20f-43d1-a81c-ac34958cc254", "name": "default@PackageHap work[73] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931804358900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b32b9b0-9aae-4834-8770-c6a4a01c2740", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931805570200, "endTime": 31932327617800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "42634232-514e-42de-8e1b-6b6010cd9d7b", "logId": "c668c5ee-afc8-47a8-b028-433213e75f60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98672c39-1730-464a-b0ec-fcf5e00031ca", "name": "default@PackageHap work[73] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931805290800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a64d460-39bc-4839-ae68-0fddcfb8e4cd", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931805394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079e99e8-9de4-4ee2-a509-dbaeb1954a23", "name": "default@PackageHap work[73] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931805495100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4663f915-c30b-411c-9df7-d8f5b70b5ce8", "name": "default@PackageHap work[73] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931805557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f97e71a-a358-4f44-a111-572fbc09f3c4", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931803111400, "endTime": 31931805614900}, "additional": {"logType": "info", "children": [], "durationId": "60612d9b-aa0c-47c7-8a41-945683eec13e", "parent": "85cdd7a3-d78f-4515-bc51-5edd804abefa"}}, {"head": {"id": "e45e880a-b575-4613-b9ea-8c138bda8478", "name": "entry : default@PackageHap cost memory 1.2933883666992188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931810979800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f394c45d-db01-4054-88a1-601696a2a442", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931811912200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4aebc84-87cf-4711-a02d-de67831ccc70", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932327764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d8c2e63-f42d-4aba-b281-095e7eb62e22", "name": "default@PackageHap work[73] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932328017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c668c5ee-afc8-47a8-b028-433213e75f60", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31931805570200, "endTime": 31932327617800}, "additional": {"logType": "info", "children": [], "durationId": "7b32b9b0-9aae-4834-8770-c6a4a01c2740", "parent": "85cdd7a3-d78f-4515-bc51-5edd804abefa"}}, {"head": {"id": "4647f8f4-bd56-4aa6-9b3e-be2853ec4267", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932328135900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85cdd7a3-d78f-4515-bc51-5edd804abefa", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31931773452400, "endTime": 31932328219600, "totalTime": 554168100}, "additional": {"logType": "info", "children": ["cc244297-280f-4160-a90e-4945719eda8b", "3f97e71a-a358-4f44-a111-572fbc09f3c4", "c668c5ee-afc8-47a8-b028-433213e75f60"], "durationId": "42634232-514e-42de-8e1b-6b6010cd9d7b"}}, {"head": {"id": "04ff37fd-3be1-4236-9d57-8494eaf2e909", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932336662300, "endTime": 31932339953900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "710de2e8-86ec-461b-88ed-7b277a91f752", "logId": "245324d5-1b85-476e-8feb-7c05921f694e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "710de2e8-86ec-461b-88ed-7b277a91f752", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932333195300}, "additional": {"logType": "detail", "children": [], "durationId": "04ff37fd-3be1-4236-9d57-8494eaf2e909"}}, {"head": {"id": "d59fd2c9-66cc-472e-9b93-48af1b1c0ffb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932333647800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "168bc9c6-e340-4346-a3dd-0c07944cf0f7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932333759900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "390a2f7e-ef22-4d4a-8a62-5d5a96a9d90d", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932336675700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cdbf2b7-f498-43bf-af97-fb43605bb6d4", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932337085300}, "additional": {"logType": "warn", "children": [], "durationId": "04ff37fd-3be1-4236-9d57-8494eaf2e909"}}, {"head": {"id": "61b819b9-f03b-4d42-b943-74a2cac1b808", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932338254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a1adfda-461b-4718-9a76-f58f41c795c6", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932338408900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c64de67-f03c-48de-b19b-5a4df617ec1b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932338563400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b508e506-669a-4729-86db-af9428214542", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932338993700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74251fc-87e1-401e-b7e5-27a624c5cba2", "name": "entry : default@SignHap cost memory 0.11623382568359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932339568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa6edeb6-95cd-41f6-9825-92df40fe9a84", "name": "runTaskFromQueue task cost before running: 3 s 662 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932339707300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "245324d5-1b85-476e-8feb-7c05921f694e", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932336662300, "endTime": 31932339953900, "totalTime": 3007200}, "additional": {"logType": "info", "children": [], "durationId": "04ff37fd-3be1-4236-9d57-8494eaf2e909"}}, {"head": {"id": "9b6b513c-2465-4482-969e-08176209ff22", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932344403400, "endTime": 31932357592700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e59cadb9-524f-4ce9-b70e-86eed4bf2a3a", "logId": "a3faae12-28aa-4232-bd81-0aad301cc2c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e59cadb9-524f-4ce9-b70e-86eed4bf2a3a", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932342878700}, "additional": {"logType": "detail", "children": [], "durationId": "9b6b513c-2465-4482-969e-08176209ff22"}}, {"head": {"id": "27c2eb5c-aaf5-4b8a-a3de-162b539e3ea4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932343388200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2ab20f-1a59-4ced-9780-df27e7d35b76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932343504400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb1f616-edc7-4eb0-aaa3-e15d82ffcf8f", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932344426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18a09bb1-476f-44bf-9c1d-3f2045722379", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932357240700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de78821c-25c0-4ec7-a5c3-b950dff512f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932357375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360dfe10-9ba1-4c95-924f-e18c19387b62", "name": "entry : default@CollectDebugSymbol cost memory 0.23889923095703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932357457100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc5c517b-1f12-4898-bf5c-f09fae5767f1", "name": "runTaskFromQueue task cost before running: 3 s 680 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932357537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3faae12-28aa-4232-bd81-0aad301cc2c6", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932344403400, "endTime": 31932357592700, "totalTime": 13121500}, "additional": {"logType": "info", "children": [], "durationId": "9b6b513c-2465-4482-969e-08176209ff22"}}, {"head": {"id": "1dc5eae7-7075-4324-9a3c-51f8441d8c19", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932363205000, "endTime": 31932363612200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "10c63aa3-4f17-41e7-8fc8-d9af053381dc", "logId": "ce04c312-640b-44e9-b0f0-c827845b6927"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10c63aa3-4f17-41e7-8fc8-d9af053381dc", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932363104600}, "additional": {"logType": "detail", "children": [], "durationId": "1dc5eae7-7075-4324-9a3c-51f8441d8c19"}}, {"head": {"id": "d879ff6e-9d24-4391-8636-008df6979715", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932363215700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87f9227-6f59-4393-9602-9f6d9b130b35", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932363452300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e32dd1c-63b2-4ddc-b532-b5e92f2dc3c5", "name": "runTaskFromQueue task cost before running: 3 s 686 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932363545700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce04c312-640b-44e9-b0f0-c827845b6927", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932363205000, "endTime": 31932363612200, "totalTime": 319600}, "additional": {"logType": "info", "children": [], "durationId": "1dc5eae7-7075-4324-9a3c-51f8441d8c19"}}, {"head": {"id": "13caba30-2f22-4310-aeec-c3c28d58d047", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932378869200, "endTime": 31932378900500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13a5f78c-99b8-4456-87f2-38630d6d5d35", "logId": "c9f1d81a-8e5a-46ba-b994-fe6b898a6e46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9f1d81a-8e5a-46ba-b994-fe6b898a6e46", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932378869200, "endTime": 31932378900500}, "additional": {"logType": "info", "children": [], "durationId": "13caba30-2f22-4310-aeec-c3c28d58d047"}}, {"head": {"id": "cba6c72d-0fc0-4ed9-89da-e94cb03f98ce", "name": "BUILD SUCCESSFUL in 3 s 701 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932378966800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "fb7b0fa8-96c5-41df-b0a2-8a5d5020a427", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31928677959300, "endTime": 31932379894000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 21}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "290ee1f4-6d76-4249-adaa-f5416ea6b3ec", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932379932700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df1705e-3d30-4ea6-addb-ebd97a212462", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932380048300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb49e5cf-90bb-49ae-99fe-4e974b6c2779", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932380162700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f9d22e-4e5b-4215-a21a-6e5ef46c299a", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932380234100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c64d8a-8ef7-4358-8257-edf5f33519cd", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932380295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9f37ad-6b4d-4b51-baf8-9942cb09428c", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932380563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2398026d-2272-4a29-ae71-0c0cd2c0a44c", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932381578100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d52e7803-5a7d-47da-8220-6e7a5afa070e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932383496800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e38ccf09-5cb7-49d0-b95d-3aea244e2e4a", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932383679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee987859-ada8-46be-9f54-67e1858c5435", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932383845700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0266764-2f57-4eeb-8720-f093a8084f14", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932384368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70ddcde1-678f-4bb1-bbf8-acb8ba65ca37", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932386659400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c01a32-cb12-41cd-8b9f-33a848ec151d", "name": "Incremental task entry:default@SyscapTransform post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932387045600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d7d33b2-a02a-450d-96b8-54e196152bdb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932387220000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6db36b1-827a-4811-9314-d4fa3fd29677", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932387283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5a0b0e-8886-4c45-b073-98f7ca4cc571", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932387331600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e9c561-1e84-4c83-a4f9-dfdb16807803", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932387378900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "487d3b1e-5cb8-4950-91a9-5401189c88eb", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932387886000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9316bbd8-3bf3-4b97-95ce-b844b42d7e0f", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932388504800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08a8ce61-b4a3-46d8-960b-85596124c42a", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932389005200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bdd31be-a115-4d3f-addc-6c321a4c7b98", "name": "Incremental task entry:default@ProcessLibs post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932389582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78ad81b-e6cf-4c4d-b2b4-5f46df8941d5", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932389689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76037640-c41a-450a-9b69-d292f6e6f9e2", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932389747600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10dbe532-7862-4995-a461-26a275330aed", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932391672300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55571a32-3a43-4f9c-83df-482e79f79f03", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932392071400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d11387-12f8-4e01-87ff-fe60ba56c1ce", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932394758200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec92af1b-8a0e-4825-8f57-c7e6b948a038", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932395438300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "094fbf95-d685-4beb-b572-a483d409f88e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932395727400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f0bbc0-c55e-42f8-a8e7-d33675f96625", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932396304800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ade7658-db11-4210-935c-ca4c4ef1652b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932396385400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7523e86-8810-498b-bf53-29e74ff0ecd8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932396572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bc7687c-0daf-475e-bd54-5cae04b6a13d", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932396921700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e263172-10df-468b-a2ed-21875f869e7e", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932397539100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b86827df-ba31-434d-bcec-8a2aacfa4fe8", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932398718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87cba1be-7064-4a48-ab98-86377ab1042d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932399429100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740fb148-83e6-49bc-ae84-31cf385a042e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932400392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6376e29c-9d35-42f8-901b-d48b12a23a1b", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932400602400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6604aa55-f845-46b0-bb83-f862c21c2c79", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932401081600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7da8a23-503b-4227-b92c-8823f9bee63f", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932401861500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52482090-a783-43d2-89c8-91db8be8c5ea", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932402490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4268fc9f-959a-4ee5-a9ff-8011eec9a345", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932419402900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d601e7d9-b659-42b0-a7d8-b434e40b9c38", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932419536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed2097f-d871-4429-b097-ead0de4e133c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932421922000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "491e3d06-6d4e-44a0-8fa1-2c9f4459e402", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932422227600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df10f2c7-3270-4265-bdde-cbd54f8ddbdd", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932423321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97426e62-08ce-4b89-a2ef-3a8a6f58ca22", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932433250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a52543e-dc2a-4892-a38f-e99cfd870673", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932433495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08d6d6a5-1b63-4b2f-b08c-bad44710dbfa", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932433801300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4988e576-1b43-47c5-ae9d-7956155d8601", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932433874400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c3c2da-c77d-4931-85f9-163e2eea4b9e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932434052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5eb1e9-b9c9-40f0-b5b7-ba1b4b6349fd", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932435412200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3dd91c8-2371-4179-a031-80758f836050", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932435870100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "853dae27-e9ed-48a3-9708-5321c879e77e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932436082100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f5023e-4ade-411a-9c42-b0e699ce7c15", "name": "Incremental task entry:default@PackageHap post-execution cost:19 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932438577600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff08e5f2-f009-43df-a681-cb666e107d1a", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932439367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a17a79-4166-4233-9d2d-af0afe74d458", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932439575800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16fac58d-8e83-47b7-bfa0-e1b5941b1bf8", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932439877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e1397d-87cb-4c6e-af2c-eb5d8dfa854c", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932442959700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eb4dd4d-2bd0-4009-890a-8c916f9d63c9", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932443462000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6587f9e7-14eb-445d-b718-4c891a838063", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932443832100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf03ce6-3595-4d54-b0a3-0ab530a778aa", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31932444069800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}