{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "e2581131-34d1-4383-83a1-3184a94816a4", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441998865300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872c335b-08e9-4bf7-bee8-dcf3ef0a3b0a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30442156010200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b2dda8-8cdb-4304-9d64-9e56e71730a4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30442156264500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691c6ef6-e52e-4e5e-bd64-23769ff5f0ad", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491949815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491957631500, "endTime": 30492553843500}, "additional": {"children": ["ea16ad4c-e599-4a1b-9269-3843b6bf7026", "de92200d-50e1-46de-8d85-45daba0150d2", "0da1fa2c-ab05-486c-886c-06b41f116111", "431dc5df-aab3-44c6-8128-2029b0738d6c", "524de494-54bb-41d0-8327-e37f37813956", "4a7dcc83-8481-4f66-8c4a-9afde1f3f84b", "1df8f3e3-a1bd-49f0-83e4-2ad459489eb0"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "3b75dabf-45eb-499a-ac58-990112e49f31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea16ad4c-e599-4a1b-9269-3843b6bf7026", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491957634200, "endTime": 30491970724000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "675edd85-6a91-4148-9de2-39425c3b15a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de92200d-50e1-46de-8d85-45daba0150d2", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491970738700, "endTime": 30492552486100}, "additional": {"children": ["c8f1a063-bc22-48e3-bfeb-364b2871579d", "c977fe5a-950b-4133-bde9-7d109beb7384", "aa1773cd-8c52-41f7-9592-837fb7816ca8", "12275b28-18ce-4502-8eb5-bc590891442d", "3e477c16-0ca0-4b04-93cb-5e0360365c7b", "d5b05a53-6a63-48fc-b633-11898100971d", "b4f59200-ccf3-4525-99eb-cde4c9246cd2", "7dc9d52e-17a2-43f0-934a-92b1f359de41", "41dd6b47-97ad-4451-9ba3-86a62a98730b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0da1fa2c-ab05-486c-886c-06b41f116111", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492552506900, "endTime": 30492553832200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "fbf48032-826c-49bf-8710-5e0936c74c26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "431dc5df-aab3-44c6-8128-2029b0738d6c", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492553837300, "endTime": 30492553838300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "bc03da8f-56a5-48d4-ba09-e95cd6eac5da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "524de494-54bb-41d0-8327-e37f37813956", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491960685000, "endTime": 30491960736100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "a0892e52-6d3b-456c-b6ea-9b9a8fc76091"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0892e52-6d3b-456c-b6ea-9b9a8fc76091", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491960685000, "endTime": 30491960736100}, "additional": {"logType": "info", "children": [], "durationId": "524de494-54bb-41d0-8327-e37f37813956", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "4a7dcc83-8481-4f66-8c4a-9afde1f3f84b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491966270800, "endTime": 30491966290600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "0c04c185-68ba-4449-8af7-9c1b4668f226"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c04c185-68ba-4449-8af7-9c1b4668f226", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491966270800, "endTime": 30491966290600}, "additional": {"logType": "info", "children": [], "durationId": "4a7dcc83-8481-4f66-8c4a-9afde1f3f84b", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "d9b35a72-47b9-45da-8b3f-1a336510190c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491966338200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "414c5f89-ae01-418e-a8bf-918f78bd7184", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491970587000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "675edd85-6a91-4148-9de2-39425c3b15a7", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491957634200, "endTime": 30491970724000}, "additional": {"logType": "info", "children": [], "durationId": "ea16ad4c-e599-4a1b-9269-3843b6bf7026", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "c8f1a063-bc22-48e3-bfeb-364b2871579d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491978031700, "endTime": 30491978042400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "516cecc4-e17d-431e-a08a-ad1b4336e2a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c977fe5a-950b-4133-bde9-7d109beb7384", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491978063700, "endTime": 30491982250400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "26e57cfb-1fd1-4eb1-a8a8-2a3d0311693a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa1773cd-8c52-41f7-9592-837fb7816ca8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491982268500, "endTime": 30492143876200}, "additional": {"children": ["24437b91-7d84-4a79-b878-9550a5bd3cda", "597c1e10-6ded-4800-a1b0-08340199f3b2", "35468a02-881c-40ed-a7cd-332693cb9cf3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "e97e549d-34cb-4b66-8fae-9321679882f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12275b28-18ce-4502-8eb5-bc590891442d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492143940000, "endTime": 30492280558200}, "additional": {"children": ["85e356fa-e961-44d5-98d9-c8d1884c77c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "37ab066d-2d70-428d-a987-ec01dbdea6f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e477c16-0ca0-4b04-93cb-5e0360365c7b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492280567900, "endTime": 30492529291000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "17ec3ee1-a4fd-41b5-b45e-89a19e164121"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5b05a53-6a63-48fc-b633-11898100971d", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492531068700, "endTime": 30492543757900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "115b7424-51d2-436a-bbbc-f18294d5b040"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4f59200-ccf3-4525-99eb-cde4c9246cd2", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492543780800, "endTime": 30492552317800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "4e51a8a5-bb0f-4ca5-9a7f-5c7d1e4b5767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7dc9d52e-17a2-43f0-934a-92b1f359de41", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492552336200, "endTime": 30492552461500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "2d5a6810-b052-4141-9c74-a64306bf753b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "516cecc4-e17d-431e-a08a-ad1b4336e2a4", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491978031700, "endTime": 30491978042400}, "additional": {"logType": "info", "children": [], "durationId": "c8f1a063-bc22-48e3-bfeb-364b2871579d", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "26e57cfb-1fd1-4eb1-a8a8-2a3d0311693a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491978063700, "endTime": 30491982250400}, "additional": {"logType": "info", "children": [], "durationId": "c977fe5a-950b-4133-bde9-7d109beb7384", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "24437b91-7d84-4a79-b878-9550a5bd3cda", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491982937900, "endTime": 30491982957800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa1773cd-8c52-41f7-9592-837fb7816ca8", "logId": "8aa15478-bea9-4b9f-88a4-daeeadd181ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8aa15478-bea9-4b9f-88a4-daeeadd181ce", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491982937900, "endTime": 30491982957800}, "additional": {"logType": "info", "children": [], "durationId": "24437b91-7d84-4a79-b878-9550a5bd3cda", "parent": "e97e549d-34cb-4b66-8fae-9321679882f6"}}, {"head": {"id": "597c1e10-6ded-4800-a1b0-08340199f3b2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491985859000, "endTime": 30492138842300}, "additional": {"children": ["2769c282-8607-4eb7-9c78-014ad0bddcdc", "4060d2df-4e26-412c-96ab-144d82a1f1e3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa1773cd-8c52-41f7-9592-837fb7816ca8", "logId": "c7975059-00e1-4466-9038-5ed89818eb8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2769c282-8607-4eb7-9c78-014ad0bddcdc", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491985861000, "endTime": 30491992491500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "597c1e10-6ded-4800-a1b0-08340199f3b2", "logId": "2c78be27-ebca-41c3-b2cd-a1f4795191f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4060d2df-4e26-412c-96ab-144d82a1f1e3", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491992516900, "endTime": 30492138821800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "597c1e10-6ded-4800-a1b0-08340199f3b2", "logId": "d908bfe0-328e-4a74-9462-fd1ecfb5da67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ee41c42-7032-4c7c-9a86-0aa8fcae3d7e", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491985867700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b51d641-40f5-4b62-9304-12fd5a0901df", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491992282600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c78be27-ebca-41c3-b2cd-a1f4795191f7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491985861000, "endTime": 30491992491500}, "additional": {"logType": "info", "children": [], "durationId": "2769c282-8607-4eb7-9c78-014ad0bddcdc", "parent": "c7975059-00e1-4466-9038-5ed89818eb8f"}}, {"head": {"id": "47a64870-f15f-4f8b-8fa9-da6cd9e71e05", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491992538200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1285ee-a34c-4d60-991a-3281cc6c27ec", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492000249200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44e729a1-043e-4783-8b07-6d5d753af18c", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492000499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a3144af-3b01-4ac5-b8e6-c7b13751b7a0", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492000783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd2a004-9eeb-44ea-ab5c-ef07079957c4", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492000906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38636fb0-4f64-4c9d-8df4-d257e801a5fa", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492002683300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fd011a-4c08-4eb8-aa22-b0b01d205275", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492007915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46172864-d136-4262-a943-4472c54561f4", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492020746000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb944559-b86e-4289-8ba3-eb1e6231f0d8", "name": "Sdk init in 49 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492058123300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f36b30-9484-4926-ae09-f1cf219314a8", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492058298600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 57}, "markType": "other"}}, {"head": {"id": "6fd2d3b3-86cb-45f2-8621-7517df368ccf", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492058357400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 57}, "markType": "other"}}, {"head": {"id": "67fc5ff5-b317-442a-b468-2574db613839", "name": "Project task initialization takes 79 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492138159900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c8f8800-9431-4360-bf8f-3fb6bb5a0503", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492138397300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7040ba61-017a-4ad3-9217-9c5b8af97cd6", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492138582100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a83c8215-1bd7-4611-9b1f-57ebbf69c7e9", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492138760800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d908bfe0-328e-4a74-9462-fd1ecfb5da67", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491992516900, "endTime": 30492138821800}, "additional": {"logType": "info", "children": [], "durationId": "4060d2df-4e26-412c-96ab-144d82a1f1e3", "parent": "c7975059-00e1-4466-9038-5ed89818eb8f"}}, {"head": {"id": "c7975059-00e1-4466-9038-5ed89818eb8f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491985859000, "endTime": 30492138842300}, "additional": {"logType": "info", "children": ["2c78be27-ebca-41c3-b2cd-a1f4795191f7", "d908bfe0-328e-4a74-9462-fd1ecfb5da67"], "durationId": "597c1e10-6ded-4800-a1b0-08340199f3b2", "parent": "e97e549d-34cb-4b66-8fae-9321679882f6"}}, {"head": {"id": "35468a02-881c-40ed-a7cd-332693cb9cf3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492143816700, "endTime": 30492143843000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa1773cd-8c52-41f7-9592-837fb7816ca8", "logId": "27f33f32-89ee-44f4-a34f-1665770b396f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27f33f32-89ee-44f4-a34f-1665770b396f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492143816700, "endTime": 30492143843000}, "additional": {"logType": "info", "children": [], "durationId": "35468a02-881c-40ed-a7cd-332693cb9cf3", "parent": "e97e549d-34cb-4b66-8fae-9321679882f6"}}, {"head": {"id": "e97e549d-34cb-4b66-8fae-9321679882f6", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491982268500, "endTime": 30492143876200}, "additional": {"logType": "info", "children": ["8aa15478-bea9-4b9f-88a4-daeeadd181ce", "c7975059-00e1-4466-9038-5ed89818eb8f", "27f33f32-89ee-44f4-a34f-1665770b396f"], "durationId": "aa1773cd-8c52-41f7-9592-837fb7816ca8", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "85e356fa-e961-44d5-98d9-c8d1884c77c5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492145182000, "endTime": 30492280544500}, "additional": {"children": ["a273fe29-51a0-4edc-975f-0bcb5160540d", "8d168bd3-7de4-41b8-bc46-cf6792a26233", "4169d617-5d24-4aa2-8bd2-b41a3430cff6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "12275b28-18ce-4502-8eb5-bc590891442d", "logId": "5b18289b-014f-41e5-b3a5-185aa46da98a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a273fe29-51a0-4edc-975f-0bcb5160540d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492161633900, "endTime": 30492161655700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e356fa-e961-44d5-98d9-c8d1884c77c5", "logId": "fca80b33-c27f-459d-89a7-007d60653b6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fca80b33-c27f-459d-89a7-007d60653b6d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492161633900, "endTime": 30492161655700}, "additional": {"logType": "info", "children": [], "durationId": "a273fe29-51a0-4edc-975f-0bcb5160540d", "parent": "5b18289b-014f-41e5-b3a5-185aa46da98a"}}, {"head": {"id": "8d168bd3-7de4-41b8-bc46-cf6792a26233", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492169036300, "endTime": 30492270942000}, "additional": {"children": ["e1367473-8352-414f-8970-a9e2ab526ca5", "18d051b7-e50a-42db-878a-b23be8b706ba"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e356fa-e961-44d5-98d9-c8d1884c77c5", "logId": "0a560f9f-fe79-456f-8103-0948ba9ec582"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1367473-8352-414f-8970-a9e2ab526ca5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492169040400, "endTime": 30492211885900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d168bd3-7de4-41b8-bc46-cf6792a26233", "logId": "8c1a155e-9a68-4e59-ac3e-bf4c45101dac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18d051b7-e50a-42db-878a-b23be8b706ba", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492211964200, "endTime": 30492270916100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8d168bd3-7de4-41b8-bc46-cf6792a26233", "logId": "47d8bc82-f6e6-4886-a2f1-b5a5458e409f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a8ad912-24ed-4e05-a4db-6de501e7ecca", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492169049000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1afdffb8-15ba-421a-b980-90c4ff711b6e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492211378200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1a155e-9a68-4e59-ac3e-bf4c45101dac", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492169040400, "endTime": 30492211885900}, "additional": {"logType": "info", "children": [], "durationId": "e1367473-8352-414f-8970-a9e2ab526ca5", "parent": "0a560f9f-fe79-456f-8103-0948ba9ec582"}}, {"head": {"id": "c5ba64a1-7f54-474e-b554-86ca5a191216", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492211995800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0398047-3525-410d-b52f-9c27fe25ac0d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492240511800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7575a4a-5fb4-4e7f-9d12-e55f5d70704a", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492240791200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d075b4ad-cd80-4e6c-9f76-f2d704c2539c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492241040900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99033d2e-ed8d-4b46-8180-81eb242f6e6e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492243983100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3042b8ce-a068-43d0-96e2-df82c8d62e69", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492247782400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764c5ad6-51c2-4d09-a9f7-0a218d9444f0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492247888200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a89031f0-2420-44b0-a504-34eb713a1e14", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492247967600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90c016df-dbb5-4f2d-8c40-ccedd6891014", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492268856100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5730494-5fff-46ab-b5b8-99f8ea0b27c4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492269227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6202c34-10a4-4d13-9f10-783cf3a8981b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492270095800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e5d4c19-ca2b-42b5-89b8-32d775f9c065", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492270208600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d8bc82-f6e6-4886-a2f1-b5a5458e409f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492211964200, "endTime": 30492270916100}, "additional": {"logType": "info", "children": [], "durationId": "18d051b7-e50a-42db-878a-b23be8b706ba", "parent": "0a560f9f-fe79-456f-8103-0948ba9ec582"}}, {"head": {"id": "0a560f9f-fe79-456f-8103-0948ba9ec582", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492169036300, "endTime": 30492270942000}, "additional": {"logType": "info", "children": ["8c1a155e-9a68-4e59-ac3e-bf4c45101dac", "47d8bc82-f6e6-4886-a2f1-b5a5458e409f"], "durationId": "8d168bd3-7de4-41b8-bc46-cf6792a26233", "parent": "5b18289b-014f-41e5-b3a5-185aa46da98a"}}, {"head": {"id": "4169d617-5d24-4aa2-8bd2-b41a3430cff6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492280499700, "endTime": 30492280524000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85e356fa-e961-44d5-98d9-c8d1884c77c5", "logId": "11834914-5b26-4f23-8f71-38a91dc14fd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11834914-5b26-4f23-8f71-38a91dc14fd8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492280499700, "endTime": 30492280524000}, "additional": {"logType": "info", "children": [], "durationId": "4169d617-5d24-4aa2-8bd2-b41a3430cff6", "parent": "5b18289b-014f-41e5-b3a5-185aa46da98a"}}, {"head": {"id": "5b18289b-014f-41e5-b3a5-185aa46da98a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492145182000, "endTime": 30492280544500}, "additional": {"logType": "info", "children": ["fca80b33-c27f-459d-89a7-007d60653b6d", "0a560f9f-fe79-456f-8103-0948ba9ec582", "11834914-5b26-4f23-8f71-38a91dc14fd8"], "durationId": "85e356fa-e961-44d5-98d9-c8d1884c77c5", "parent": "37ab066d-2d70-428d-a987-ec01dbdea6f5"}}, {"head": {"id": "37ab066d-2d70-428d-a987-ec01dbdea6f5", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492143940000, "endTime": 30492280558200}, "additional": {"logType": "info", "children": ["5b18289b-014f-41e5-b3a5-185aa46da98a"], "durationId": "12275b28-18ce-4502-8eb5-bc590891442d", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "435b17bb-1ef6-4624-837c-4224ce4b57d7", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492409588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1e428bf-223f-4b49-bf28-938af3edd3c3", "name": "hvigorfile, resolve hvigorfile dependencies in 249 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492529090200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17ec3ee1-a4fd-41b5-b45e-89a19e164121", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492280567900, "endTime": 30492529291000}, "additional": {"logType": "info", "children": [], "durationId": "3e477c16-0ca0-4b04-93cb-5e0360365c7b", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "41dd6b47-97ad-4451-9ba3-86a62a98730b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492530245300, "endTime": 30492531047100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de92200d-50e1-46de-8d85-45daba0150d2", "logId": "e1ab8296-58ce-4115-a678-f9f94a32e51e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "031a8aab-cf46-4eb2-8975-f8980c3a3144", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492530275100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ab8296-58ce-4115-a678-f9f94a32e51e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492530245300, "endTime": 30492531047100}, "additional": {"logType": "info", "children": [], "durationId": "41dd6b47-97ad-4451-9ba3-86a62a98730b", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "e466c4a4-f857-45f1-872e-44e394941a16", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492532397500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffaa2c84-616f-4f8e-a325-e143dd6449a5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492542890800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115b7424-51d2-436a-bbbc-f18294d5b040", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492531068700, "endTime": 30492543757900}, "additional": {"logType": "info", "children": [], "durationId": "d5b05a53-6a63-48fc-b633-11898100971d", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "723ccef0-b887-4a68-965b-33c045a2fe18", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492547570100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34cd6c6d-0f4d-4976-920f-6bcf2a8687d3", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492547702900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79f52188-8864-4359-ba17-a05d419fb071", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492549407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdd0cff3-595d-4c6a-b532-0a6e04949f49", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492549676900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e51a8a5-bb0f-4ca5-9a7f-5c7d1e4b5767", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492543780800, "endTime": 30492552317800}, "additional": {"logType": "info", "children": [], "durationId": "b4f59200-ccf3-4525-99eb-cde4c9246cd2", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "0b8d7b87-0db2-4487-b7fa-eaed2e3ad752", "name": "Configuration phase cost:575 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492552358600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5a6810-b052-4141-9c74-a64306bf753b", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492552336200, "endTime": 30492552461500}, "additional": {"logType": "info", "children": [], "durationId": "7dc9d52e-17a2-43f0-934a-92b1f359de41", "parent": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389"}}, {"head": {"id": "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491970738700, "endTime": 30492552486100}, "additional": {"logType": "info", "children": ["516cecc4-e17d-431e-a08a-ad1b4336e2a4", "26e57cfb-1fd1-4eb1-a8a8-2a3d0311693a", "e97e549d-34cb-4b66-8fae-9321679882f6", "37ab066d-2d70-428d-a987-ec01dbdea6f5", "17ec3ee1-a4fd-41b5-b45e-89a19e164121", "115b7424-51d2-436a-bbbc-f18294d5b040", "4e51a8a5-bb0f-4ca5-9a7f-5c7d1e4b5767", "2d5a6810-b052-4141-9c74-a64306bf753b", "e1ab8296-58ce-4115-a678-f9f94a32e51e"], "durationId": "de92200d-50e1-46de-8d85-45daba0150d2", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "1df8f3e3-a1bd-49f0-83e4-2ad459489eb0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492553797900, "endTime": 30492553818700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df", "logId": "a5963609-2c82-4710-97ba-24b1dcda5c53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5963609-2c82-4710-97ba-24b1dcda5c53", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492553797900, "endTime": 30492553818700}, "additional": {"logType": "info", "children": [], "durationId": "1df8f3e3-a1bd-49f0-83e4-2ad459489eb0", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "fbf48032-826c-49bf-8710-5e0936c74c26", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492552506900, "endTime": 30492553832200}, "additional": {"logType": "info", "children": [], "durationId": "0da1fa2c-ab05-486c-886c-06b41f116111", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "bc03da8f-56a5-48d4-ba09-e95cd6eac5da", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492553837300, "endTime": 30492553838300}, "additional": {"logType": "info", "children": [], "durationId": "431dc5df-aab3-44c6-8128-2029b0738d6c", "parent": "3b75dabf-45eb-499a-ac58-990112e49f31"}}, {"head": {"id": "3b75dabf-45eb-499a-ac58-990112e49f31", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491957631500, "endTime": 30492553843500}, "additional": {"logType": "info", "children": ["675edd85-6a91-4148-9de2-39425c3b15a7", "30021cc8-a9f9-4ffd-9b6e-9fe5d6525389", "fbf48032-826c-49bf-8710-5e0936c74c26", "bc03da8f-56a5-48d4-ba09-e95cd6eac5da", "a0892e52-6d3b-456c-b6ea-9b9a8fc76091", "0c04c185-68ba-4449-8af7-9c1b4668f226", "a5963609-2c82-4710-97ba-24b1dcda5c53"], "durationId": "97a1da31-93e8-4ca8-9c27-1fbcae91f1df"}}, {"head": {"id": "6f149854-8197-4cf3-b1a3-2555f6fc767b", "name": "Configuration task cost before running: 602 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492554026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11da70bb-a266-436d-b280-010c6ff75642", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492563866200, "endTime": 30492576419400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a1eb370d-f1d6-4327-9baf-13076dd95d73", "logId": "b10aa6c3-f4cd-47d2-96d0-2c2ccfa8ea5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1eb370d-f1d6-4327-9baf-13076dd95d73", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492557619800}, "additional": {"logType": "detail", "children": [], "durationId": "11da70bb-a266-436d-b280-010c6ff75642"}}, {"head": {"id": "4f42dc8d-b39f-4c64-b598-c5bb67fafa77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492558012900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22311533-5366-47ec-9c60-9d8f70b81522", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492558193700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d461bf2-1b63-4826-9c6b-46c47addf3f4", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492563887300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd38a2c-a39a-4e60-9835-f087c97e85ee", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492576136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490bcafa-654a-4bdf-9e32-9ffbb573dfb7", "name": "entry : default@PreBuild cost memory 0.3162689208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492576263900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b10aa6c3-f4cd-47d2-96d0-2c2ccfa8ea5f", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492563866200, "endTime": 30492576419400}, "additional": {"logType": "info", "children": [], "durationId": "11da70bb-a266-436d-b280-010c6ff75642"}}, {"head": {"id": "3f18d074-0cb0-47c6-9a22-a5737e84452c", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492582823200, "endTime": 30492585460200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e79679cf-5ffc-4c13-a1c9-6366d652306a", "logId": "0500dbd2-bdc3-4795-9300-b279d3288a34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e79679cf-5ffc-4c13-a1c9-6366d652306a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492581126400}, "additional": {"logType": "detail", "children": [], "durationId": "3f18d074-0cb0-47c6-9a22-a5737e84452c"}}, {"head": {"id": "063d4ebb-58e0-4ee5-82c6-60db4bcac9c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492581786800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a03e71e-7dae-44ba-984b-0d79c0789c9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492581914500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3579dda6-dada-4083-b1e8-0ca8d4ff63ee", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492582835600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab05350-8080-454a-8630-c639ce621035", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492583930400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d2a067-a69d-4d72-8326-4c28ff04e8d8", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492585196300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f1a8f3-baa3-435d-9e69-21437fbd0ea8", "name": "entry : default@GenerateMetadata cost memory 0.096160888671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492585344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0500dbd2-bdc3-4795-9300-b279d3288a34", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492582823200, "endTime": 30492585460200}, "additional": {"logType": "info", "children": [], "durationId": "3f18d074-0cb0-47c6-9a22-a5737e84452c"}}, {"head": {"id": "489e356d-397b-4987-9d9f-e801ee3f2732", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492588230300, "endTime": 30492590245600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "85aab087-bee2-4cfe-a0c3-5fb2c6eb6ce4", "logId": "b586aa97-730f-4ec6-a404-b01d46c82880"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85aab087-bee2-4cfe-a0c3-5fb2c6eb6ce4", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492587194400}, "additional": {"logType": "detail", "children": [], "durationId": "489e356d-397b-4987-9d9f-e801ee3f2732"}}, {"head": {"id": "d5bf7299-81a1-4daf-b58a-c5f6c55eeb79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492587792900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4366f11d-fb78-4065-b0c6-49426e0fae56", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492587942300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f11838ba-f3f1-46b6-b894-5639ed41a405", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492588239400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd66e1a3-2d07-41dc-bd31-aa6ca1dad293", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492588362100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d61578-e679-4aab-9c7c-4ba26c7fc495", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492588470300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c5309c-0d5b-4d92-aeae-f3cfe6469689", "name": "entry : default@ConfigureCmake cost memory 0.03661346435546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492588943300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b9ecb3-c308-4390-8720-144aecad8bd2", "name": "runTaskFromQueue task cost before running: 637 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492589531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b586aa97-730f-4ec6-a404-b01d46c82880", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492588230300, "endTime": 30492590245600, "totalTime": 1224100}, "additional": {"logType": "info", "children": [], "durationId": "489e356d-397b-4987-9d9f-e801ee3f2732"}}, {"head": {"id": "f05a46d6-3fa5-4ae8-bbf0-fab1daecdec6", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492594428600, "endTime": 30492596244200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "55e15775-f303-41de-b3a3-0acc21b9cc7b", "logId": "b5c66b6d-9aa7-4225-9f73-e10c5892ebd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55e15775-f303-41de-b3a3-0acc21b9cc7b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492593090200}, "additional": {"logType": "detail", "children": [], "durationId": "f05a46d6-3fa5-4ae8-bbf0-fab1daecdec6"}}, {"head": {"id": "3c45ec09-7edc-4aff-ae2a-601ab85b9134", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492593479500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee8763b6-ac90-49b5-b1d9-2a5bf0d0043b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492593592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6afa442-ef27-4ae1-aa58-a1ab459dc018", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492594443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d3e4e3e-1923-49c5-b94b-19e74b5b869c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492596007200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d304bc-ff45-4436-b72c-11f951ff048b", "name": "entry : default@MergeProfile cost memory 0.10691070556640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492596152200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5c66b6d-9aa7-4225-9f73-e10c5892ebd8", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492594428600, "endTime": 30492596244200}, "additional": {"logType": "info", "children": [], "durationId": "f05a46d6-3fa5-4ae8-bbf0-fab1daecdec6"}}, {"head": {"id": "7f1bf396-b9c8-4001-890f-9b87121c9ea0", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492601348200, "endTime": 30492603476000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "85ba0a50-331f-4864-8564-b702723dd62c", "logId": "9ec8092e-7a2e-4324-bdf0-883d060f5026"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85ba0a50-331f-4864-8564-b702723dd62c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492599708600}, "additional": {"logType": "detail", "children": [], "durationId": "7f1bf396-b9c8-4001-890f-9b87121c9ea0"}}, {"head": {"id": "5501f245-5edf-4784-955e-d90807adc499", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492600151800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf11fab6-d7e7-49e7-9a51-2fa73a960758", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492600283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "722350b2-9a06-4b7d-9409-92098042f968", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492601363800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e596f442-434e-4e1f-9ded-8de7fc640d40", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492602378700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c53de25-2ac4-470a-a190-7763c2a11484", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492603302800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad3a66a-0660-49cc-8704-8c9fc69d7f6d", "name": "entry : default@CreateBuildProfile cost memory 0.1044769287109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492603400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec8092e-7a2e-4324-bdf0-883d060f5026", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492601348200, "endTime": 30492603476000}, "additional": {"logType": "info", "children": [], "durationId": "7f1bf396-b9c8-4001-890f-9b87121c9ea0"}}, {"head": {"id": "7727ae43-9e92-4e70-8fb3-b443d76bbc87", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492607953900, "endTime": 30492608672400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "62472d5c-39a2-4f02-bebd-45594733c740", "logId": "25d39d3d-5c76-4806-969a-9fc9fc73321f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62472d5c-39a2-4f02-bebd-45594733c740", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492605082700}, "additional": {"logType": "detail", "children": [], "durationId": "7727ae43-9e92-4e70-8fb3-b443d76bbc87"}}, {"head": {"id": "c6239607-16bd-4a15-ba43-e58654684c69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492606987100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86155ec-e53a-41d1-855c-c24cc8d84465", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492607116400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caf4bcba-9208-4657-ae18-e2a270a49c2e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492607964100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7e2d48-4ad1-42c8-8952-5e7e6bc4adf2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492608170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80a3cc9-8d5f-4e29-b209-548c428db71c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492608251400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e4afc8-a512-44b2-9ff9-c522db474c47", "name": "entry : default@PreCheckSyscap cost memory 0.0373382568359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492608475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6388bc3b-c201-4c45-a622-fe9a70f48246", "name": "runTaskFromQueue task cost before running: 656 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492608589300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25d39d3d-5c76-4806-969a-9fc9fc73321f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492607953900, "endTime": 30492608672400, "totalTime": 614000}, "additional": {"logType": "info", "children": [], "durationId": "7727ae43-9e92-4e70-8fb3-b443d76bbc87"}}, {"head": {"id": "1e77cff1-78fd-45e8-9343-192769fa6a04", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492614456100, "endTime": 30492615093700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "72a5de78-723a-48b7-ac34-71218ca7418c", "logId": "11d9c9eb-7e18-4bae-aff7-ec1868667940"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72a5de78-723a-48b7-ac34-71218ca7418c", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492610342900}, "additional": {"logType": "detail", "children": [], "durationId": "1e77cff1-78fd-45e8-9343-192769fa6a04"}}, {"head": {"id": "6c7e9519-e034-44bb-a683-a29f90f2635e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492610723700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38897b8d-278f-468d-a0b0-0af40e2bca50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492610818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3921554-e73f-46de-a93d-6a21432bcd15", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492614468600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6041099-e00d-4cd6-90c0-13e8e8888155", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492614733800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbf92cf3-ff46-4ab7-ade0-42aef211ea58", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03912353515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492614944600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d62010-c492-44ac-a36a-eaf249a0dc88", "name": "runTaskFromQueue task cost before running: 663 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492615032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d9c9eb-7e18-4bae-aff7-ec1868667940", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492614456100, "endTime": 30492615093700, "totalTime": 562700}, "additional": {"logType": "info", "children": [], "durationId": "1e77cff1-78fd-45e8-9343-192769fa6a04"}}, {"head": {"id": "db6cad36-df3a-4aa4-a783-4106e2a5ff53", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492619725400, "endTime": 30492624223300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "4f9e56ef-5d2e-44b2-bbd0-7cb8e8f87a37", "logId": "6d0d70bd-8b1d-4535-b2ef-d45ed282a442"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f9e56ef-5d2e-44b2-bbd0-7cb8e8f87a37", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492616983700}, "additional": {"logType": "detail", "children": [], "durationId": "db6cad36-df3a-4aa4-a783-4106e2a5ff53"}}, {"head": {"id": "3ef47b8e-ace5-4021-a6be-750886db2618", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492617405800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08de8678-0884-4019-84bc-8d2722e407c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492617520100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3828ee38-76c8-4e39-aca5-2b3b94f90fd2", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492619737200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d05396a3-fb6d-4fe9-87a6-badda979ac1f", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492622079200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ad8795-5897-42e1-bb68-efd62da2023f", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492623741200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af05b935-254a-48b2-8397-c3a985ef5c6c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492623866200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0bed55-b106-488a-9ca9-2332488c1226", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492623926300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf0c95d-cd8a-4a0b-81c9-4fbe91c72a8b", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11870574951171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492624056400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9309a45-2525-496f-b46a-e0c0901e92a4", "name": "runTaskFromQueue task cost before running: 672 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492624159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d0d70bd-8b1d-4535-b2ef-d45ed282a442", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492619725400, "endTime": 30492624223300, "totalTime": 4416400}, "additional": {"logType": "info", "children": [], "durationId": "db6cad36-df3a-4aa4-a783-4106e2a5ff53"}}, {"head": {"id": "cfd9d249-2b20-4711-9ba8-43a98318c494", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629207900, "endTime": 30492629808900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2651a9c1-3a75-4814-b7e8-ff1371ae7c77", "logId": "fdca07a8-0025-4f08-a1de-b215df51713d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2651a9c1-3a75-4814-b7e8-ff1371ae7c77", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492628028800}, "additional": {"logType": "detail", "children": [], "durationId": "cfd9d249-2b20-4711-9ba8-43a98318c494"}}, {"head": {"id": "87285e95-1c93-4603-a436-e26705b30f6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492628381400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1029b9-635e-4437-8bef-8b412ac4155c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492628481900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d00f5ee-ac2a-4473-8183-e4bf6456518f", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629220800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca94fe8-a144-4d8b-976a-5c0ce722b925", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629333800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3820a1c4-8baa-434c-ae10-dfc88ee9d8e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629392300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726a7d99-d7f1-4dd4-8166-02fd7ed88f7a", "name": "entry : default@BuildNativeWithCmake cost memory 0.037567138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629469000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23558811-9243-4747-94e6-a851565ec1ac", "name": "runTaskFromQueue task cost before running: 677 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629550600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdca07a8-0025-4f08-a1de-b215df51713d", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492629207900, "endTime": 30492629808900, "totalTime": 325100}, "additional": {"logType": "info", "children": [], "durationId": "cfd9d249-2b20-4711-9ba8-43a98318c494"}}, {"head": {"id": "5e817e56-0cc2-4078-9cbe-a5334296d957", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492633805300, "endTime": 30492637932200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c70b51dc-27dd-45f8-b814-7339fee53698", "logId": "cbe4ee5e-2ee7-49ec-a2b7-d9c9e254c3f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c70b51dc-27dd-45f8-b814-7339fee53698", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492632519000}, "additional": {"logType": "detail", "children": [], "durationId": "5e817e56-0cc2-4078-9cbe-a5334296d957"}}, {"head": {"id": "47cfc041-a3eb-4f99-8b4c-3f86f4735aae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492632878500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5711032b-500b-4e68-b4f9-314fe247d566", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492632981900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8e4d75-ad0c-45df-846c-9a58ce5d17d7", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492633814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "748a4555-1568-4b9f-bae0-8158dd94ec2d", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492637524900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a4b5c41-ba75-4f9b-9ce9-7d5515cfe098", "name": "entry : default@MakePackInfo cost memory 0.13995361328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492637842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbe4ee5e-2ee7-49ec-a2b7-d9c9e254c3f6", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492633805300, "endTime": 30492637932200}, "additional": {"logType": "info", "children": [], "durationId": "5e817e56-0cc2-4078-9cbe-a5334296d957"}}, {"head": {"id": "934f680a-d699-42c4-9192-2bc5e0600f03", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492645435400, "endTime": 30492649085300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "27adf30a-9eb7-4ecf-898e-9ba04e76ba7c", "logId": "f5441ae0-3009-4bd6-905b-dee6ad8381bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27adf30a-9eb7-4ecf-898e-9ba04e76ba7c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492643540200}, "additional": {"logType": "detail", "children": [], "durationId": "934f680a-d699-42c4-9192-2bc5e0600f03"}}, {"head": {"id": "3194a47d-483d-40db-bbd9-6e0424b45f0c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492644032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e426e75-e156-497e-936d-fffbacb6cc2f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492644193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf5d311-307c-4828-8837-1fcea0abe9d1", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492645445400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843b3ed8-b753-4b05-a493-ef94249657b8", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492645580700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d00745-f70b-4264-bdf3-1449fe444dd3", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492646210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ee7084-1c7c-47fd-b60e-c95a0c8d42b5", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492648224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7e1518-119e-405d-a5e2-e12df9feec4a", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492648366900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63364ba1-0c37-47c0-b5d5-42f148eae491", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492648464500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863ab347-0d7b-4879-880f-6ddfafe202df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492648518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a69fda4f-d8f1-45e6-9b2e-ae9830b77f64", "name": "entry : default@SyscapTransform cost memory 0.1540985107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492648594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "191c21e9-afe4-4233-acd9-58b03a97b04c", "name": "runTaskFromQueue task cost before running: 697 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492649001000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5441ae0-3009-4bd6-905b-dee6ad8381bd", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492645435400, "endTime": 30492649085300, "totalTime": 3516300}, "additional": {"logType": "info", "children": [], "durationId": "934f680a-d699-42c4-9192-2bc5e0600f03"}}, {"head": {"id": "6ddd86e8-7916-4227-88ba-3e4908a1861d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492654994400, "endTime": 30492657601000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0e2d8c0c-c2ce-4529-bcc8-31d4c672a905", "logId": "f0f833e0-b2f0-4646-95c0-ef301c97cb74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e2d8c0c-c2ce-4529-bcc8-31d4c672a905", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492650966300}, "additional": {"logType": "detail", "children": [], "durationId": "6ddd86e8-7916-4227-88ba-3e4908a1861d"}}, {"head": {"id": "255efb18-ce74-46e2-aacf-fbc4739e4264", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492651837400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92558eb3-f6e3-4f75-8aa6-7200350f5269", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492652217600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7283101-b6c9-45b5-b9ed-e7f7d91edec9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492655008500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9489e4a-6107-4e73-ae94-df0b3f5e7fbd", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492657381400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c339ab-d6e1-4913-917e-bec7b249c5e5", "name": "entry : default@ProcessProfile cost memory 0.061187744140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492657513700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f833e0-b2f0-4646-95c0-ef301c97cb74", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492654994400, "endTime": 30492657601000}, "additional": {"logType": "info", "children": [], "durationId": "6ddd86e8-7916-4227-88ba-3e4908a1861d"}}, {"head": {"id": "5cc7c5cc-c6c2-443c-9891-a57cb9eff635", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492663574200, "endTime": 30492667824600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "171d24e1-e9f8-4438-b239-64dddec050a0", "logId": "42580ac0-2e51-4a6d-a55f-61cba28530ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "171d24e1-e9f8-4438-b239-64dddec050a0", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492661509500}, "additional": {"logType": "detail", "children": [], "durationId": "5cc7c5cc-c6c2-443c-9891-a57cb9eff635"}}, {"head": {"id": "37269c4d-1530-4829-b4bd-c6b8e0a6b5e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492661964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e06109c2-ace2-4ae9-9874-b4daf1557f2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492662064100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07890008-39e7-4e30-90b5-41b39a012d09", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492663584900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb12626-4370-4c88-a17c-11b74d6f632b", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492667403300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3afea5de-5dcb-4ea0-af1e-86b17de09af5", "name": "entry : default@ProcessRouterMap cost memory 0.20343017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492667690800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42580ac0-2e51-4a6d-a55f-61cba28530ae", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492663574200, "endTime": 30492667824600}, "additional": {"logType": "info", "children": [], "durationId": "5cc7c5cc-c6c2-443c-9891-a57cb9eff635"}}, {"head": {"id": "7c779e64-902b-4afa-b9c5-5cd0d629bc6f", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492672171200, "endTime": 30492673856100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e0595bf9-b94a-4333-9959-fa64c0f6c9a0", "logId": "c4d4da90-e260-47f3-b14d-d38e7a564249"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0595bf9-b94a-4333-9959-fa64c0f6c9a0", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492670551400}, "additional": {"logType": "detail", "children": [], "durationId": "7c779e64-902b-4afa-b9c5-5cd0d629bc6f"}}, {"head": {"id": "e0369761-a789-4566-9dcd-d0ac3b2e973f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492670911200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "688b2960-548a-4545-8144-14ffdf99927f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492671007100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553ddd08-6887-414b-b840-7be70d06ef0d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492672184200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93c7551-c11c-405a-846e-327a75b24266", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492672820900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6288d8a-470f-417d-b0de-8b2df95d8d8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492672970200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72827bcb-8877-4592-8c7e-0501af05e6ca", "name": "entry : default@BuildNativeWithNinja cost memory 0.05692291259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492673684800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f85f3ab8-0a27-4777-8d04-3dd74371aafc", "name": "runTaskFromQueue task cost before running: 721 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492673795500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4d4da90-e260-47f3-b14d-d38e7a564249", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492672171200, "endTime": 30492673856100, "totalTime": 1606600}, "additional": {"logType": "info", "children": [], "durationId": "7c779e64-902b-4afa-b9c5-5cd0d629bc6f"}}, {"head": {"id": "3a4b24f9-0f20-4d17-b8a3-9eeb594e5c82", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492678226300, "endTime": 30492683442800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b902fbe3-5097-4a2c-a0ca-17cf49983837", "logId": "03bcb356-422a-4765-a82f-519665ebdfea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b902fbe3-5097-4a2c-a0ca-17cf49983837", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492676090300}, "additional": {"logType": "detail", "children": [], "durationId": "3a4b24f9-0f20-4d17-b8a3-9eeb594e5c82"}}, {"head": {"id": "bdef9a3f-4eed-4729-8d79-b797169ed6f2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492676456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8bf3a4-6642-474a-aa77-64d4ed09c29b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492676565700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c57180f-9edd-41fe-95cc-2d31af116104", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492677321300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfe7abbc-fe50-4cd1-a3d2-0617b071a5ea", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492679713200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d136d2-0b2d-4785-b2c7-f7abdd59a41b", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492681417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3814fff1-35b5-4f0f-bbfd-39d8e82ad96d", "name": "entry : default@ProcessResource cost memory 0.1706085205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492681539200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03bcb356-422a-4765-a82f-519665ebdfea", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492678226300, "endTime": 30492683442800}, "additional": {"logType": "info", "children": [], "durationId": "3a4b24f9-0f20-4d17-b8a3-9eeb594e5c82"}}, {"head": {"id": "61b9cea4-9424-422f-b396-fb1147f641f7", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492691543200, "endTime": 30492703381900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5f770b59-c089-4d2f-a1d0-9668a0a52f57", "logId": "ed3d2be5-e824-473c-a467-1e9cde265340"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f770b59-c089-4d2f-a1d0-9668a0a52f57", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492687594500}, "additional": {"logType": "detail", "children": [], "durationId": "61b9cea4-9424-422f-b396-fb1147f641f7"}}, {"head": {"id": "ee9ab21c-dab9-49aa-9801-dc8aede8f6d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492687933800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14310bca-2cbe-43da-a1fd-01999e8306b5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492688025300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e71214-8171-42ca-b317-7870d9dcbba5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492691554800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de1716a9-7628-46db-b1be-c1740924180b", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492703157900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b9da38-4ca6-42b0-9a34-d493d90b221b", "name": "entry : default@GenerateLoaderJson cost memory 0.7675323486328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492703311200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed3d2be5-e824-473c-a467-1e9cde265340", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492691543200, "endTime": 30492703381900}, "additional": {"logType": "info", "children": [], "durationId": "61b9cea4-9424-422f-b396-fb1147f641f7"}}, {"head": {"id": "8f8d4650-8511-4d62-97d9-3f3029786409", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492711305700, "endTime": 30492714767400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5ad6fdd3-ab82-4d17-8cdb-2722684050ba", "logId": "de95c47a-b7eb-4de0-9ee5-80bef71da5bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ad6fdd3-ab82-4d17-8cdb-2722684050ba", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492710025800}, "additional": {"logType": "detail", "children": [], "durationId": "8f8d4650-8511-4d62-97d9-3f3029786409"}}, {"head": {"id": "7a199252-a7d8-4476-89f7-32af86e79ba3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492710436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9707aba6-4466-4b37-af66-e5fc6289e42d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492710537600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7ce8440-8c4e-4c4b-94fe-50b343079f9b", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492711322200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "921ca11b-aff7-46d5-b946-cd21439a117a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492713078100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9762c07-41e9-4570-aad3-50c3cd6ecb7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492713369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add34785-5dd1-4217-9793-f487122dfd50", "name": "entry : default@ProcessLibs cost memory 0.12680816650390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492714422300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66723c3-162c-4bf6-93e1-1e0f6af2bf60", "name": "runTaskFromQueue task cost before running: 762 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492714683900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de95c47a-b7eb-4de0-9ee5-80bef71da5bd", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492711305700, "endTime": 30492714767400, "totalTime": 3244700}, "additional": {"logType": "info", "children": [], "durationId": "8f8d4650-8511-4d62-97d9-3f3029786409"}}, {"head": {"id": "d3a6647f-d72a-4833-9b97-35905c4d8a59", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492720693700, "endTime": 30492748698600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "33bdfb72-531a-42aa-8b1b-353275603a57", "logId": "5b699b34-d601-4839-a3a4-90fa7bb45990"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33bdfb72-531a-42aa-8b1b-353275603a57", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492717097600}, "additional": {"logType": "detail", "children": [], "durationId": "d3a6647f-d72a-4833-9b97-35905c4d8a59"}}, {"head": {"id": "a7b6255e-d01e-4616-95b5-c75524b655bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492717467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f6be30-9360-40e6-a978-c1c965bfddfc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492717566600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17690135-1fb0-455a-bdb6-a097271bbf4e", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492718324900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9830ddb-cc97-418e-812c-4fde71efe104", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492720722800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad66c500-64d9-486d-a8a9-94eafadb929e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492748423100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4894d8d3-c9fa-4bb3-af9f-6fbd81cbd5c2", "name": "entry : default@CompileResource cost memory 1.6380233764648438", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492748561200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b699b34-d601-4839-a3a4-90fa7bb45990", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492720693700, "endTime": 30492748698600}, "additional": {"logType": "info", "children": [], "durationId": "d3a6647f-d72a-4833-9b97-35905c4d8a59"}}, {"head": {"id": "ae663975-929c-4576-96e2-cabdc4f2cd72", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492758742800, "endTime": 30492760077400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e3c4e216-b11d-42f5-8e0f-1dd52c7bf660", "logId": "e1207619-9939-4588-8302-d4b5934c8d22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3c4e216-b11d-42f5-8e0f-1dd52c7bf660", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492753480600}, "additional": {"logType": "detail", "children": [], "durationId": "ae663975-929c-4576-96e2-cabdc4f2cd72"}}, {"head": {"id": "c9b45b85-9436-45bc-8e47-fc9a5a8b7589", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492753833400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f9175f3-d79f-4487-94a7-44ac43012378", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492753928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06fcdb14-cfb5-460d-b3b8-1cf082b027a9", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492758768100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27618177-ba6b-4b2b-a7c9-2b9675950ff9", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492759079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c69624c-3893-4944-a4bd-e53fe0b05c22", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492759913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a27e50d9-f59f-4282-8a58-b45c78a725a3", "name": "entry : default@DoNativeStrip cost memory 0.07678985595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492760009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1207619-9939-4588-8302-d4b5934c8d22", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492758742800, "endTime": 30492760077400}, "additional": {"logType": "info", "children": [], "durationId": "ae663975-929c-4576-96e2-cabdc4f2cd72"}}, {"head": {"id": "5ef7df80-6f35-411b-9482-456c66d9cb7a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492765764300, "endTime": 30492779705200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "bd05d60c-acd8-4876-b30a-b085c883a4bf", "logId": "41343b30-bf5a-4817-9761-d5ce0670605d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd05d60c-acd8-4876-b30a-b085c883a4bf", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492761890100}, "additional": {"logType": "detail", "children": [], "durationId": "5ef7df80-6f35-411b-9482-456c66d9cb7a"}}, {"head": {"id": "24abf2e4-3be2-4d35-b314-2e5e23cfbac6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492762266300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94364bde-fcc2-4fed-9746-c2a6914a7886", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492762366700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b5e49e-baea-40a1-8bd1-bc6d96cc0d93", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492765774700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9ac2a9b-0523-4ba3-9e1e-c91ef20eb7a3", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492779472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc06d53-7fcb-4ae7-a661-a0346627030a", "name": "entry : default@CompileArkTS cost memory 0.6808090209960938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492779598700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41343b30-bf5a-4817-9761-d5ce0670605d", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492765764300, "endTime": 30492779705200}, "additional": {"logType": "info", "children": [], "durationId": "5ef7df80-6f35-411b-9482-456c66d9cb7a"}}, {"head": {"id": "c42d7a44-c221-485f-b134-b0489f755d15", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492788962200, "endTime": 30492792377100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "db42d04e-8ce0-446f-9260-13c11cb8829a", "logId": "6ec78232-8e27-46dd-85f9-7315ad364c44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db42d04e-8ce0-446f-9260-13c11cb8829a", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492783279500}, "additional": {"logType": "detail", "children": [], "durationId": "c42d7a44-c221-485f-b134-b0489f755d15"}}, {"head": {"id": "0a9d36ad-f7de-4a1f-87b8-53e6954e5c6f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492783787300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205a5011-8822-452c-b5d1-140382dac062", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492783940100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf5fb312-d374-4575-adc3-6cb86dac1f97", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492788976700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6253e9-0978-41e5-bd90-854a1f3d454b", "name": "entry : default@BuildJS cost memory 0.12818145751953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492792167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75fc2870-e866-4c2e-b23a-54e09851256a", "name": "runTaskFromQueue task cost before running: 840 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492792315600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec78232-8e27-46dd-85f9-7315ad364c44", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492788962200, "endTime": 30492792377100, "totalTime": 3329900}, "additional": {"logType": "info", "children": [], "durationId": "c42d7a44-c221-485f-b134-b0489f755d15"}}, {"head": {"id": "3ec54766-3398-49cc-9c97-aecadefe0c81", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492797055300, "endTime": 30492798935800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a3e436e7-085c-495a-a385-db5ec11ad508", "logId": "7d234c8f-55b8-47b0-8a32-9a9eb024cb7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3e436e7-085c-495a-a385-db5ec11ad508", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492794063100}, "additional": {"logType": "detail", "children": [], "durationId": "3ec54766-3398-49cc-9c97-aecadefe0c81"}}, {"head": {"id": "12ed4976-677b-4b89-8c52-4a1d9f4d0209", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492794420700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d178c64a-5c15-41ca-9eb0-51a26112771b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492794514200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ae5a61-15db-40c5-b973-aebe63b8e734", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492797066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacbab8d-86df-4eb2-8be7-1d87ccdaccb8", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492797625600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb8d0270-4762-4c40-aeaf-8efd1bdd5c30", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492798709500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f61209-6b24-40db-922b-d8dd3207fcb7", "name": "entry : default@CacheNativeLibs cost memory 0.0909576416015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492798848700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d234c8f-55b8-47b0-8a32-9a9eb024cb7a", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492797055300, "endTime": 30492798935800}, "additional": {"logType": "info", "children": [], "durationId": "3ec54766-3398-49cc-9c97-aecadefe0c81"}}, {"head": {"id": "1222b8dd-e5d9-4095-8a3f-ed096236eba8", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492802461200, "endTime": 30492803466200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "d6e4cd57-a1da-44f6-a22d-73d603f38de1", "logId": "7ed41e7c-9885-4d70-8459-87ae17a96773"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6e4cd57-a1da-44f6-a22d-73d603f38de1", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492800851900}, "additional": {"logType": "detail", "children": [], "durationId": "1222b8dd-e5d9-4095-8a3f-ed096236eba8"}}, {"head": {"id": "262655d7-3696-430b-9208-3f901d5b1dc1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492801249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6614966-8989-4d2e-ae94-c7ad4e5ef4fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492801365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d74978a-598c-4991-91f6-d65be3a6f0e1", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492802471100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25bfca56-371d-4fb9-aa18-fc561e955308", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492802695500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "329ee81d-18c3-4b08-9bc3-7163861f8196", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492803324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f68b3c47-eb96-4f28-a043-ca7369ecce6e", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07283782958984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492803403600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed41e7c-9885-4d70-8459-87ae17a96773", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492802461200, "endTime": 30492803466200}, "additional": {"logType": "info", "children": [], "durationId": "1222b8dd-e5d9-4095-8a3f-ed096236eba8"}}, {"head": {"id": "31c862b9-a695-4c6f-a5c9-c5624cf19aa8", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492814794200, "endTime": 30492833346400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "0efd390b-1c29-4d5c-a94c-6af30ec0df24", "logId": "fa8d6492-bcc3-4474-b666-71163fc86a95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0efd390b-1c29-4d5c-a94c-6af30ec0df24", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492806209500}, "additional": {"logType": "detail", "children": [], "durationId": "31c862b9-a695-4c6f-a5c9-c5624cf19aa8"}}, {"head": {"id": "a02075de-624a-43eb-adf2-8cf03fcef5f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492807456800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aefaa099-c9dc-4b82-955c-13ef9923c6f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492807575500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfdbbeae-e4df-4170-bba0-69dc77683ef5", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492814804700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af371cc-226e-45c9-b019-ef0bcce61d70", "name": "Incremental task entry:default@PackageHap pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492833116800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b84e3827-aadc-46bc-b9f8-d4e50ca83579", "name": "entry : default@PackageHap cost memory 0.8405380249023438", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492833272200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa8d6492-bcc3-4474-b666-71163fc86a95", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492814794200, "endTime": 30492833346400}, "additional": {"logType": "info", "children": [], "durationId": "31c862b9-a695-4c6f-a5c9-c5624cf19aa8"}}, {"head": {"id": "560d3610-09c4-4038-87da-e166047fc556", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492840968000, "endTime": 30492842891900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "badfa21a-dbd8-42b0-8eb1-465965f852eb", "logId": "8bfea8af-a198-4680-bb39-f56cdd8d53b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "badfa21a-dbd8-42b0-8eb1-465965f852eb", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492836420300}, "additional": {"logType": "detail", "children": [], "durationId": "560d3610-09c4-4038-87da-e166047fc556"}}, {"head": {"id": "490359ba-6878-4c68-a620-02d684a368a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492837088200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51f43fe-587d-42eb-ad88-2ffbd6f64c35", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492837243600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be4a500-c35e-4bf1-acfc-1cefee1a582e", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492840978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39fcebe-c3a6-4f55-b3e1-fdf95e77229c", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492841265800}, "additional": {"logType": "warn", "children": [], "durationId": "560d3610-09c4-4038-87da-e166047fc556"}}, {"head": {"id": "56416586-1b44-4adf-b336-8ed21359b232", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492842066000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553cfde5-3c27-407e-9e70-04f9184add17", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492842203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd66e4e7-51d5-4f92-b0ec-248a58bb7f1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492842317800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06ac8589-5aa0-4fe8-8573-d4e201360c36", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492842392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04bec696-abbc-4d7c-8c0b-85d5d067ff13", "name": "entry : default@SignHap cost memory 0.1196136474609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492842691400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3cd65cf-7b8a-4e14-b525-69e485851a69", "name": "runTaskFromQueue task cost before running: 890 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492842816900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bfea8af-a198-4680-bb39-f56cdd8d53b3", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492840968000, "endTime": 30492842891900, "totalTime": 1825300}, "additional": {"logType": "info", "children": [], "durationId": "560d3610-09c4-4038-87da-e166047fc556"}}, {"head": {"id": "ea094c74-904c-45dc-a303-ac64d4cb862a", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492846574400, "endTime": 30492853794000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a61fe6dd-aaf2-4fc8-9dfa-9422c4533594", "logId": "4645ae82-5bca-42d3-9898-796bf67be838"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a61fe6dd-aaf2-4fc8-9dfa-9422c4533594", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492844603200}, "additional": {"logType": "detail", "children": [], "durationId": "ea094c74-904c-45dc-a303-ac64d4cb862a"}}, {"head": {"id": "34e2299e-57c2-459e-8e84-966eb83abcf7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492845014600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f893877c-69ac-4121-ac96-50f9544787f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492845108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968dcecc-e0b6-40e7-8578-fbb12af91c22", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492846585500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13bb3f49-5778-47a7-9db5-8f3835d65fa2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492852293600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ce03f5-6329-435a-b672-bb750b9a9619", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492852408300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e8e2d8-8e85-4f5f-8d18-cfbd97e16783", "name": "entry : default@CollectDebugSymbol cost memory 0.23998260498046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492852488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c3c0d9d-14a8-4cea-aa91-e1494ad88cc8", "name": "runTaskFromQueue task cost before running: 901 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492853486000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4645ae82-5bca-42d3-9898-796bf67be838", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492846574400, "endTime": 30492853794000, "totalTime": 6852600}, "additional": {"logType": "info", "children": [], "durationId": "ea094c74-904c-45dc-a303-ac64d4cb862a"}}, {"head": {"id": "8b4bf07e-db0a-4715-a058-ab3a542ef2db", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492858192400, "endTime": 30492859685000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "bf1d327d-5aee-49ff-8ef0-b0a30dca1ce6", "logId": "a004f68e-65c6-476e-9ac9-5209ef572d61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf1d327d-5aee-49ff-8ef0-b0a30dca1ce6", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492858097000}, "additional": {"logType": "detail", "children": [], "durationId": "8b4bf07e-db0a-4715-a058-ab3a542ef2db"}}, {"head": {"id": "84d05127-5ddb-4d50-8a75-1f0c61e91b16", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492858200100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d4d3a9-d1f2-4758-afed-88b148f03433", "name": "entry : assembleHap cost memory 0.0113525390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492859163200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dfd0171-f995-4df7-a696-790358d55112", "name": "runTaskFromQueue task cost before running: 907 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492859532700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a004f68e-65c6-476e-9ac9-5209ef572d61", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492858192400, "endTime": 30492859685000, "totalTime": 1302900}, "additional": {"logType": "info", "children": [], "durationId": "8b4bf07e-db0a-4715-a058-ab3a542ef2db"}}, {"head": {"id": "659bc060-c017-4338-9bc5-c93b1dc9cfee", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492870084300, "endTime": 30492870111100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44048f39-9c95-4b7f-9f0d-0f2071a2291a", "logId": "5cb7601e-2410-4ef8-ab64-d265977369da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cb7601e-2410-4ef8-ab64-d265977369da", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492870084300, "endTime": 30492870111100}, "additional": {"logType": "info", "children": [], "durationId": "659bc060-c017-4338-9bc5-c93b1dc9cfee"}}, {"head": {"id": "a3c7f00f-b381-4495-8488-021506a79990", "name": "BUILD SUCCESSFUL in 918 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492870292100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "39a62f20-58f9-46ac-9585-a897b55b5ff8", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30491952903000, "endTime": 30492871032700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 57}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "7957b892-52fd-470b-ba74-11dc0d0d0ab3", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492871069000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d16e5e-047a-497d-89a3-48c0df144dc5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492871235300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ea2b53-3b62-4354-bc79-d5445993e628", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492871338500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2abd436-1c87-4519-a93e-c7bd58249356", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492871437900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d68d5b7-2983-421d-aa5c-64a721158688", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492871515900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57187713-4db1-4049-bfdd-822d7aea08c7", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492871841900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a68190-d979-40e9-9498-f945cd636e4b", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492873529200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be952d25-e342-44f9-b77e-cdc5a03b8ec7", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492874141900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15e56afe-674d-4f0c-8aa0-08c2162213bb", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492874384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a850bc-aaaf-4f16-bfe9-f82584de36f2", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492874720300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "743bdafb-40c6-4927-bbfe-af1c82c4c437", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492875155600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f84167-489b-4d12-bc37-c14e3a2b8e22", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492876935300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a1c1a0-1267-4a85-a0de-5d5693b9742b", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492877371400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c251f6b-3e2c-4ab5-99d6-70ed3cc7a17f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492877486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b630bc84-1379-4771-bcb2-81a51332de48", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492877567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65983883-e0a4-487e-bd15-8e88d9c7c21f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492877682300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f6d1a2-ed10-4ac2-aed6-0434aa25b9c8", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492877746100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70678c80-46fe-413e-95f0-9bb4c4328deb", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492878046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a0217a-c989-4611-9208-b5b8087f055e", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492878388200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e43e39d-aec1-4ca2-bb03-134ceb358a92", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492878805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ee9150-a6d5-4936-9214-b3f41397b5f5", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492879160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f2026a-f3f5-4ba7-a189-f5df3431b0aa", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492879317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef50311-7a3c-4216-a47d-b3ff3f895d67", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492879506200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa53e9ea-8a44-498b-99ee-cf4265f662fc", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492879557100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c5462d-4505-4e75-8984-b48616d35bba", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492880580400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80276a8e-ff46-4f97-a9f1-b4a87bcd3cf7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492881304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a16045a-94bb-495b-a169-147dd4b87148", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492882700100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0435cf-891a-4762-abed-74eedc05a253", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492882945700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d0f2a0-9b8b-4195-bb7e-14d3cc4b3fb7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492883201100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dfeb6a9-7730-4e27-a3d1-a7d5257d5d56", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492884119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b74daf6-3919-46b7-ac94-198d6efd2007", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492884748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45af301-242f-48ad-a2d2-9081ffb98887", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492884848600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ed9a922-84c1-4217-93c3-993caed34aae", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492884900900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077e7d22-35de-4296-b87f-f06a5ac735aa", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492884946000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b202e7e4-4794-4134-805e-f56ce9cf3a80", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492885133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ec60c0-7ab5-4c00-aae3-6c364ff99e88", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492885435100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18bc9dc1-fd15-4cc4-b026-91de3af5ca60", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492885719600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd08a08-56b4-4a9d-9b40-848130e44f6a", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492890769300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7a2768-4235-4c5e-849b-d677866a006d", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492891091800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c559272f-14bf-4017-be4a-21c997b982a2", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492891346200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27177161-32a8-46fd-bd48-17446c57959c", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492891580200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}