import CommonConstants from "../../common/CommonConstants";
import { CommonDialog } from "../../util/CommonDialog";
import Logger from "../../util/Logger";
import { preferences } from "@kit.ArkData";
import { BusinessError } from "@kit.BasicServicesKit";

@Builder
export function PersonalizationBuilder(name: string, param: Object) {
  Personalization()
}

@Component
export struct Personalization {
  pageInfos: NavPathStack = new NavPathStack();
  scroller: Scroller = new Scroller();
  private diag: CommonDialog = new CommonDialog();
  private ctx: UIContext = this.getUIContext();
  private dataPreferences: preferences.Preferences | null = null;
  @State bacColor: string = '#EEEEE1'

  build() {
    NavDestination() {
      Column() {
        Scroll(this.scroller) {
          Column({ space: 10 }) {
            Row() {
              Row({ space: 10 }) {
                Image($r('app.media.doc'))
                  .width(20)
                  .height(20)
                Text('玩法介绍')
                  .fontSize(16)
              }
              Image($r('app.media.nav_arrow'))
                .width(16)
                .height(16)

            }
            .width('90%')
            .height(50)
            .backgroundColor('#FFFFFF')
            .padding(15)
            .justifyContent(FlexAlign.SpaceBetween)
            .borderRadius(24)
            .onClick(
              () => {
                // 跳转到玩法介绍页面
                this.pageInfos.pushPathByName('gameRules', null);
              }
            )

            Row() {
              Row({ space: 10 }) {
                Image($r('app.media.shudu'))
                  .width(20)
                  .height(20)
                Text('游戏设置')
                  .fontSize(16)
              }
              Image($r('app.media.nav_arrow'))
                .width(16)
                .height(16)

            }
            .width('90%')
            .height(50)
            .backgroundColor('#FFFFFF')
            .padding(15)
            .justifyContent(FlexAlign.SpaceBetween)
            .borderRadius(24)
            .onClick(
              () => {
                // 跳转到游戏设置页面
                this.pageInfos.pushPathByName('gameSettings', null);
              }
            )

          }
          .width(CommonConstants.FULL_PARENT)
          .justifyContent(FlexAlign.Start)
        }
        .scrollable(ScrollDirection.Vertical) // 滚动方向为垂直方向
        .scrollBar(BarState.Off) // 滚动条常驻显示
        .edgeEffect(EdgeEffect.Spring) // 滚动到边沿后回弹
      }.height(CommonConstants.FULL_PARENT)
      .width(CommonConstants.FULL_PARENT)

    }.title('个人中心')
    .linearGradient({
      angle: 180,
      colors: [['#FFF6F6', 0.0], ['#FFF0F0', 0.5], ['#FFFAFA', 1.0]]
    }).onReady((context: NavDestinationContext) => {
      this.pageInfos = context.pathStack;
      Logger.info("current page config info is " + JSON.stringify(context.getConfigInRouteMap()));
    })
  }

  aboutToAppear() {
    preferences.getPreferences(getContext(this), 'myStore', (err: BusinessError, val: preferences.Preferences) => {
      if (err) {
        console.error("Failed to get preferences. code =" + err.code + ", message =" + err.message);
        return;
      }
      this.dataPreferences = val;
      console.info("Succeeded in getting preferences.");
    })
  }
}
