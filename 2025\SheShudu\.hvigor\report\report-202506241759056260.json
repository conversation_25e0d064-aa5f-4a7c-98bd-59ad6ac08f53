{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "67e78ed3-c416-4b2d-bda9-b8690498c4aa", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098967700300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d2b5852-8492-4c0c-a454-6db31127b957", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098972732900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60346ff2-2e66-47dd-b58f-ea34db6df2ce", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098973731300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe2658d8-fa34-480c-b52c-2a0fe3493269", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098977729400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09a4b5f-3568-4032-a3aa-f1057148f532", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177518444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177527428600, "endTime": 34177781709000}, "additional": {"children": ["10001763-c154-4bc5-9a67-269c4cb239d5", "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "e700af6a-7a50-4735-8a70-fadb722b0dd7", "8c10c3c5-7c3b-4ad3-ba0f-a3f6151ce092", "c1789686-829f-4916-99f2-387bd2eb7d85", "d82c1d0f-9e03-40aa-a045-041fdc3d4f41", "7649d503-4c86-46db-8536-b0de9fec0854"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c9d4622e-bef2-45ce-8143-54219133d60c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10001763-c154-4bc5-9a67-269c4cb239d5", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177527431300, "endTime": 34177542908200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "93c52095-c214-4cbb-ab68-d13e71cd50fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177542931600, "endTime": 34177780586400}, "additional": {"children": ["098d05ca-a486-42f9-8597-7ca08754724f", "81a31a95-62ef-430b-948c-284a0290a6dc", "a7fba42c-ca91-4945-8b02-425fa9b55351", "edf795c3-4cf2-4d42-917b-caff9ea5501d", "9d9d6f7e-08a5-4445-b94b-d463d5c401fd", "8d642415-fca1-4421-85a1-2f2e0970b7fb", "af2983d7-a54f-4082-a004-cd8b1c8d63eb", "529dcf3a-1070-4cd9-8ac6-f622e28fe03d", "322c6d57-cfb4-4428-9d65-36402fd749de"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e700af6a-7a50-4735-8a70-fadb722b0dd7", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177780608800, "endTime": 34177781698800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "030c826b-f34d-4594-a06a-2df4609d345b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c10c3c5-7c3b-4ad3-ba0f-a3f6151ce092", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177781704200, "endTime": 34177781705200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "670bfd7e-7851-468f-961d-e08759872192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1789686-829f-4916-99f2-387bd2eb7d85", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177530732900, "endTime": 34177530779900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "60daff3b-51b4-44f1-9612-c516e4a79acb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60daff3b-51b4-44f1-9612-c516e4a79acb", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177530732900, "endTime": 34177530779900}, "additional": {"logType": "info", "children": [], "durationId": "c1789686-829f-4916-99f2-387bd2eb7d85", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "d82c1d0f-9e03-40aa-a045-041fdc3d4f41", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177537574100, "endTime": 34177537595000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "2f6d2937-472f-44bc-9ac5-8e555adf6e11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f6d2937-472f-44bc-9ac5-8e555adf6e11", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177537574100, "endTime": 34177537595000}, "additional": {"logType": "info", "children": [], "durationId": "d82c1d0f-9e03-40aa-a045-041fdc3d4f41", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "8ea98966-a827-4f92-a3ad-3a5367b9d07c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177537658400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35cd746d-4551-423e-87eb-9a257fb0e256", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177542730300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93c52095-c214-4cbb-ab68-d13e71cd50fa", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177527431300, "endTime": 34177542908200}, "additional": {"logType": "info", "children": [], "durationId": "10001763-c154-4bc5-9a67-269c4cb239d5", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "098d05ca-a486-42f9-8597-7ca08754724f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177550846100, "endTime": 34177550852300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "92eecab0-9673-4d3a-be18-8174e24f85af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81a31a95-62ef-430b-948c-284a0290a6dc", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177550868800, "endTime": 34177557081800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "cb7cc6d2-6764-4f43-be47-764e93faffc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7fba42c-ca91-4945-8b02-425fa9b55351", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177557101000, "endTime": 34177664623900}, "additional": {"children": ["6c73cf1d-6258-4c48-9efd-14314c66f4a0", "f3bd3a51-24b1-4c61-b139-43b1a6ebcc7f", "2036c3bf-0a2b-4584-a473-b6222a79309d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "9269f0dd-dfc9-4320-8274-d89029875548"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edf795c3-4cf2-4d42-917b-caff9ea5501d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177664638600, "endTime": 34177684785200}, "additional": {"children": ["3a79ccf3-c553-47e3-8f37-438fd200969e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "98fc18af-d5f6-41a8-8fb4-6778e5cdb956"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d9d6f7e-08a5-4445-b94b-d463d5c401fd", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177684795000, "endTime": 34177759606500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "6580b99a-1008-43f1-aa57-5baf9d1adb2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d642415-fca1-4421-85a1-2f2e0970b7fb", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177760493000, "endTime": 34177770642400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "ebd8a21f-0335-4095-b561-2e835b48c17c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af2983d7-a54f-4082-a004-cd8b1c8d63eb", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177770669600, "endTime": 34177780442800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "9aec5acd-77e2-47d5-ab72-cbc4a9a971b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "529dcf3a-1070-4cd9-8ac6-f622e28fe03d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177780461400, "endTime": 34177780575700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "f653e418-548d-4466-9d27-a5ddd02c4524"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92eecab0-9673-4d3a-be18-8174e24f85af", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177550846100, "endTime": 34177550852300}, "additional": {"logType": "info", "children": [], "durationId": "098d05ca-a486-42f9-8597-7ca08754724f", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "cb7cc6d2-6764-4f43-be47-764e93faffc1", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177550868800, "endTime": 34177557081800}, "additional": {"logType": "info", "children": [], "durationId": "81a31a95-62ef-430b-948c-284a0290a6dc", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "6c73cf1d-6258-4c48-9efd-14314c66f4a0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177557879900, "endTime": 34177557899000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7fba42c-ca91-4945-8b02-425fa9b55351", "logId": "276ef100-96a1-4dbf-b4d6-f96695cd9d0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "276ef100-96a1-4dbf-b4d6-f96695cd9d0c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177557879900, "endTime": 34177557899000}, "additional": {"logType": "info", "children": [], "durationId": "6c73cf1d-6258-4c48-9efd-14314c66f4a0", "parent": "9269f0dd-dfc9-4320-8274-d89029875548"}}, {"head": {"id": "f3bd3a51-24b1-4c61-b139-43b1a6ebcc7f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177560587800, "endTime": 34177663882400}, "additional": {"children": ["2fb9ce8a-3c7b-429d-9ecd-5f559e85e147", "e5a0ca41-d39b-44e2-91f6-0bde32398b13"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7fba42c-ca91-4945-8b02-425fa9b55351", "logId": "023ba311-678d-4e46-9094-fb37e3d4a7bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fb9ce8a-3c7b-429d-9ecd-5f559e85e147", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177560591300, "endTime": 34177570977000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3bd3a51-24b1-4c61-b139-43b1a6ebcc7f", "logId": "abe74a76-f7f3-4fb1-ad07-fd5686e3b250"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5a0ca41-d39b-44e2-91f6-0bde32398b13", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177571001200, "endTime": 34177663869100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f3bd3a51-24b1-4c61-b139-43b1a6ebcc7f", "logId": "dea1ed08-c37c-40b4-a449-a058e4d3ff9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b96e3e2-efb3-41d4-a191-d40b406e11dd", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177560600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "372f0c89-d2db-49b5-a809-5ac12bdd993f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177570790900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abe74a76-f7f3-4fb1-ad07-fd5686e3b250", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177560591300, "endTime": 34177570977000}, "additional": {"logType": "info", "children": [], "durationId": "2fb9ce8a-3c7b-429d-9ecd-5f559e85e147", "parent": "023ba311-678d-4e46-9094-fb37e3d4a7bd"}}, {"head": {"id": "f0f42d23-fe80-40af-8104-6413de0bd6b5", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177571021400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb9365b-2e64-4ea6-85eb-c16a74989e3a", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177580659900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "573a767f-3864-4cd9-841d-d65e48552493", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177580861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b9a962-c273-47f7-b00d-94b040069304", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177582114900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841325fe-27f2-4e61-b3a9-00953dbd592a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177582307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f57e509-7fcc-4ea9-aa8a-3ea05b00a7d9", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177584529300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec1e3e29-4756-43b0-b5a8-b388aa553889", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177590896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb26f54-ac54-4fc4-9557-f334fa6a175f", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177605743300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d483cd0-1c21-4e62-9226-e7f45920fe40", "name": "Sdk init in 43 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177634717400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a8e9894-b123-4d3f-8603-a3a4ba321da9", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177634866000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 59}, "markType": "other"}}, {"head": {"id": "2714494b-5c34-4b40-8fa5-0d91d8a14712", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177634927000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 59}, "markType": "other"}}, {"head": {"id": "dd2ca0a1-0d49-458e-9cb2-d6215c019ce9", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177663563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9df082e3-c71d-46ce-891f-ddd2d486a9bd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177663710100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22b67da1-a127-4b09-9dac-24edf6c54db5", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177663775000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2908105-237a-4db7-8c6b-b5dd2f4e7517", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177663822600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea1ed08-c37c-40b4-a449-a058e4d3ff9f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177571001200, "endTime": 34177663869100}, "additional": {"logType": "info", "children": [], "durationId": "e5a0ca41-d39b-44e2-91f6-0bde32398b13", "parent": "023ba311-678d-4e46-9094-fb37e3d4a7bd"}}, {"head": {"id": "023ba311-678d-4e46-9094-fb37e3d4a7bd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177560587800, "endTime": 34177663882400}, "additional": {"logType": "info", "children": ["abe74a76-f7f3-4fb1-ad07-fd5686e3b250", "dea1ed08-c37c-40b4-a449-a058e4d3ff9f"], "durationId": "f3bd3a51-24b1-4c61-b139-43b1a6ebcc7f", "parent": "9269f0dd-dfc9-4320-8274-d89029875548"}}, {"head": {"id": "2036c3bf-0a2b-4584-a473-b6222a79309d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177664585800, "endTime": 34177664603200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7fba42c-ca91-4945-8b02-425fa9b55351", "logId": "5e645391-75e5-4ca5-86cb-a03aad09d0ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e645391-75e5-4ca5-86cb-a03aad09d0ce", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177664585800, "endTime": 34177664603200}, "additional": {"logType": "info", "children": [], "durationId": "2036c3bf-0a2b-4584-a473-b6222a79309d", "parent": "9269f0dd-dfc9-4320-8274-d89029875548"}}, {"head": {"id": "9269f0dd-dfc9-4320-8274-d89029875548", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177557101000, "endTime": 34177664623900}, "additional": {"logType": "info", "children": ["276ef100-96a1-4dbf-b4d6-f96695cd9d0c", "023ba311-678d-4e46-9094-fb37e3d4a7bd", "5e645391-75e5-4ca5-86cb-a03aad09d0ce"], "durationId": "a7fba42c-ca91-4945-8b02-425fa9b55351", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "3a79ccf3-c553-47e3-8f37-438fd200969e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177665224800, "endTime": 34177684772300}, "additional": {"children": ["19485da8-814f-4b79-9477-08bb4f9e749f", "60f6b45c-5da8-47f7-93cc-c5e5f9bf6ede", "f2415315-3705-4df7-b810-e00349a8a948"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "edf795c3-4cf2-4d42-917b-caff9ea5501d", "logId": "d3cb8ad8-64d5-4ccb-a393-755a5007a423"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19485da8-814f-4b79-9477-08bb4f9e749f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177667982600, "endTime": 34177667997700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a79ccf3-c553-47e3-8f37-438fd200969e", "logId": "29b6de36-943d-45bd-9148-86b2034f43f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29b6de36-943d-45bd-9148-86b2034f43f4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177667982600, "endTime": 34177667997700}, "additional": {"logType": "info", "children": [], "durationId": "19485da8-814f-4b79-9477-08bb4f9e749f", "parent": "d3cb8ad8-64d5-4ccb-a393-755a5007a423"}}, {"head": {"id": "60f6b45c-5da8-47f7-93cc-c5e5f9bf6ede", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177671045200, "endTime": 34177683269700}, "additional": {"children": ["c034e707-a2fb-4a49-819e-eeae47a10411", "212d6ae3-d5f1-49b6-b363-609d19f3ff88"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a79ccf3-c553-47e3-8f37-438fd200969e", "logId": "5e1da338-6149-4e04-8a30-73408afa2328"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c034e707-a2fb-4a49-819e-eeae47a10411", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177671047600, "endTime": 34177673498100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60f6b45c-5da8-47f7-93cc-c5e5f9bf6ede", "logId": "1e067ef7-889d-4d04-8853-18bf2bfb5e92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "212d6ae3-d5f1-49b6-b363-609d19f3ff88", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177673513700, "endTime": 34177683256800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60f6b45c-5da8-47f7-93cc-c5e5f9bf6ede", "logId": "d21ea06d-9ff9-40d7-802a-2c9256ace004"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdb6ab5b-004c-47fe-8fc8-e618b8eca945", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177671052700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7104b9-d1fe-4072-ad05-3abb2983417f", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177673367800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e067ef7-889d-4d04-8853-18bf2bfb5e92", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177671047600, "endTime": 34177673498100}, "additional": {"logType": "info", "children": [], "durationId": "c034e707-a2fb-4a49-819e-eeae47a10411", "parent": "5e1da338-6149-4e04-8a30-73408afa2328"}}, {"head": {"id": "1bd73535-c357-4919-a012-cad9fb8adc7f", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177673521000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3bde5b-3b0d-44ab-95f4-686a1233844b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679185300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405c40d7-9af9-4691-a9d5-8a40f9aa15d9", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679315700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d71a2ffa-d495-4efb-a84c-8ed7ab4744a0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679509000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1897d2-c408-40b1-ace2-ca610ad82d74", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679685500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da317fce-efe0-4847-91a4-c0f818e3389b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679795700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cc9fe0-3de9-4d60-bc0d-e3789d0d48bd", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679861300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27b06e4f-27cc-4359-b62f-a95e4dcd5ffb", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177679920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82717941-7b1d-45fc-9c21-ab89ea860920", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177682931700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da0b1481-bc45-47e6-b14b-359dfb0dda05", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177683095600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0984c552-c026-4e65-a108-ffffe947cbf8", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177683160100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b26eadb-a72a-42fe-8125-d2460d92fd31", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177683209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21ea06d-9ff9-40d7-802a-2c9256ace004", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177673513700, "endTime": 34177683256800}, "additional": {"logType": "info", "children": [], "durationId": "212d6ae3-d5f1-49b6-b363-609d19f3ff88", "parent": "5e1da338-6149-4e04-8a30-73408afa2328"}}, {"head": {"id": "5e1da338-6149-4e04-8a30-73408afa2328", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177671045200, "endTime": 34177683269700}, "additional": {"logType": "info", "children": ["1e067ef7-889d-4d04-8853-18bf2bfb5e92", "d21ea06d-9ff9-40d7-802a-2c9256ace004"], "durationId": "60f6b45c-5da8-47f7-93cc-c5e5f9bf6ede", "parent": "d3cb8ad8-64d5-4ccb-a393-755a5007a423"}}, {"head": {"id": "f2415315-3705-4df7-b810-e00349a8a948", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177684737800, "endTime": 34177684756100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a79ccf3-c553-47e3-8f37-438fd200969e", "logId": "a0c109b1-4df1-4c47-bf7a-d9f1e6876082"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0c109b1-4df1-4c47-bf7a-d9f1e6876082", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177684737800, "endTime": 34177684756100}, "additional": {"logType": "info", "children": [], "durationId": "f2415315-3705-4df7-b810-e00349a8a948", "parent": "d3cb8ad8-64d5-4ccb-a393-755a5007a423"}}, {"head": {"id": "d3cb8ad8-64d5-4ccb-a393-755a5007a423", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177665224800, "endTime": 34177684772300}, "additional": {"logType": "info", "children": ["29b6de36-943d-45bd-9148-86b2034f43f4", "5e1da338-6149-4e04-8a30-73408afa2328", "a0c109b1-4df1-4c47-bf7a-d9f1e6876082"], "durationId": "3a79ccf3-c553-47e3-8f37-438fd200969e", "parent": "98fc18af-d5f6-41a8-8fb4-6778e5cdb956"}}, {"head": {"id": "98fc18af-d5f6-41a8-8fb4-6778e5cdb956", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177664638600, "endTime": 34177684785200}, "additional": {"logType": "info", "children": ["d3cb8ad8-64d5-4ccb-a393-755a5007a423"], "durationId": "edf795c3-4cf2-4d42-917b-caff9ea5501d", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "21abdb41-76e0-4e0a-9e38-5508406ed3db", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177708538700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405004ab-c248-4a29-960b-42eb77757c00", "name": "hvigorfile, resolve hvigorfile dependencies in 75 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177759471000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6580b99a-1008-43f1-aa57-5baf9d1adb2e", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177684795000, "endTime": 34177759606500}, "additional": {"logType": "info", "children": [], "durationId": "9d9d6f7e-08a5-4445-b94b-d463d5c401fd", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "322c6d57-cfb4-4428-9d65-36402fd749de", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177760306200, "endTime": 34177760481100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "logId": "98bfdb3b-ae63-41e0-bb1f-ffd9fb2485ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e07b9260-89da-43f2-a0f9-b8260d18c9df", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177760327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98bfdb3b-ae63-41e0-bb1f-ffd9fb2485ca", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177760306200, "endTime": 34177760481100}, "additional": {"logType": "info", "children": [], "durationId": "322c6d57-cfb4-4428-9d65-36402fd749de", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "e52cc86c-7237-4eee-8df9-51e499c12c93", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177761939700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7159945e-6884-4052-9514-2c18514f5131", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177768869000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd8a21f-0335-4095-b561-2e835b48c17c", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177760493000, "endTime": 34177770642400}, "additional": {"logType": "info", "children": [], "durationId": "8d642415-fca1-4421-85a1-2f2e0970b7fb", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "cf1d4e3d-5ebc-4aa1-8712-a397e651f38c", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177775321700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac00777-1423-463c-bca8-4bd978cd7484", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177775436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e261950e-31da-45c2-9b24-77c833ce718a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177778030700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c749d242-f5c7-4ff9-a767-c10493c5d0a1", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177778183700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aec5acd-77e2-47d5-ab72-cbc4a9a971b9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177770669600, "endTime": 34177780442800}, "additional": {"logType": "info", "children": [], "durationId": "af2983d7-a54f-4082-a004-cd8b1c8d63eb", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "0249a20b-1dc2-4d86-b4bd-c7e64d3ad68d", "name": "Configuration phase cost:230 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177780478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f653e418-548d-4466-9d27-a5ddd02c4524", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177780461400, "endTime": 34177780575700}, "additional": {"logType": "info", "children": [], "durationId": "529dcf3a-1070-4cd9-8ac6-f622e28fe03d", "parent": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b"}}, {"head": {"id": "8ab3d87d-7e64-47d2-8ce3-496019b89b2b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177542931600, "endTime": 34177780586400}, "additional": {"logType": "info", "children": ["92eecab0-9673-4d3a-be18-8174e24f85af", "cb7cc6d2-6764-4f43-be47-764e93faffc1", "9269f0dd-dfc9-4320-8274-d89029875548", "98fc18af-d5f6-41a8-8fb4-6778e5cdb956", "6580b99a-1008-43f1-aa57-5baf9d1adb2e", "ebd8a21f-0335-4095-b561-2e835b48c17c", "9aec5acd-77e2-47d5-ab72-cbc4a9a971b9", "f653e418-548d-4466-9d27-a5ddd02c4524", "98bfdb3b-ae63-41e0-bb1f-ffd9fb2485ca"], "durationId": "23eb51b9-a86b-4aeb-b7dd-75746ba37cd0", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "7649d503-4c86-46db-8536-b0de9fec0854", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177781676800, "endTime": 34177781689800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5", "logId": "dabcb17f-b6af-4288-830a-306d3bd5fd67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dabcb17f-b6af-4288-830a-306d3bd5fd67", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177781676800, "endTime": 34177781689800}, "additional": {"logType": "info", "children": [], "durationId": "7649d503-4c86-46db-8536-b0de9fec0854", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "030c826b-f34d-4594-a06a-2df4609d345b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177780608800, "endTime": 34177781698800}, "additional": {"logType": "info", "children": [], "durationId": "e700af6a-7a50-4735-8a70-fadb722b0dd7", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "670bfd7e-7851-468f-961d-e08759872192", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177781704200, "endTime": 34177781705200}, "additional": {"logType": "info", "children": [], "durationId": "8c10c3c5-7c3b-4ad3-ba0f-a3f6151ce092", "parent": "c9d4622e-bef2-45ce-8143-54219133d60c"}}, {"head": {"id": "c9d4622e-bef2-45ce-8143-54219133d60c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177527428600, "endTime": 34177781709000}, "additional": {"logType": "info", "children": ["93c52095-c214-4cbb-ab68-d13e71cd50fa", "8ab3d87d-7e64-47d2-8ce3-496019b89b2b", "030c826b-f34d-4594-a06a-2df4609d345b", "670bfd7e-7851-468f-961d-e08759872192", "60daff3b-51b4-44f1-9612-c516e4a79acb", "2f6d2937-472f-44bc-9ac5-8e555adf6e11", "dabcb17f-b6af-4288-830a-306d3bd5fd67"], "durationId": "51d9c2fc-6046-447a-a6fb-bfeb47787fd5"}}, {"head": {"id": "f62f19ff-ae5e-4aa5-aec7-830090ac3da2", "name": "Configuration task cost before running: 259 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177781864400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f251fd44-bc84-4b8c-8397-46e97fe84140", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177785558300, "endTime": 34177793602700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4b569a46-d595-4bbe-b6b9-5f262c55e3a5", "logId": "b078438e-0efc-4389-badd-dc0179539298"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b569a46-d595-4bbe-b6b9-5f262c55e3a5", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177783026700}, "additional": {"logType": "detail", "children": [], "durationId": "f251fd44-bc84-4b8c-8397-46e97fe84140"}}, {"head": {"id": "dde31a4d-ff2a-45a0-82ba-655f349ffa9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177783381000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5205df6-4c2b-4c26-ad76-e4c7208d6669", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177783466100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d5fa42-0e57-46ee-83ed-18510f4ecb4b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177785569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1686dc82-ee13-479b-bce8-274bab0c70ed", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177793386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d4ce78f-794b-48cc-9e98-ea42f3251b6f", "name": "entry : default@PreBuild cost memory 0.31174468994140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177793532400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b078438e-0efc-4389-badd-dc0179539298", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177785558300, "endTime": 34177793602700}, "additional": {"logType": "info", "children": [], "durationId": "f251fd44-bc84-4b8c-8397-46e97fe84140"}}, {"head": {"id": "cce1d5b9-3b31-4425-b5ec-9fbb55df9588", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177798346700, "endTime": 34177799976100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "392b12c4-89d7-47eb-b3c6-f7db9a9d9e49", "logId": "73d76bdc-7f9b-49f7-90ab-f07612fa8d3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "392b12c4-89d7-47eb-b3c6-f7db9a9d9e49", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177797140800}, "additional": {"logType": "detail", "children": [], "durationId": "cce1d5b9-3b31-4425-b5ec-9fbb55df9588"}}, {"head": {"id": "8459fd9c-c123-4f87-ba62-b81c611d8099", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177797469400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eabba0c9-e51c-4759-9f72-eb11a3a9b9d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177797562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363b3a1d-a766-417b-a4bc-876ebc72d6b7", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177798357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f617e8d2-2db4-4b3a-9421-fc1c16c0f5f8", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177799043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf5d6e4-8f2a-4fd3-9979-29bd341384da", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177799815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44ad1e4-86d2-4104-8a05-055ab079b6fb", "name": "entry : default@GenerateMetadata cost memory 0.09076690673828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177799909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d76bdc-7f9b-49f7-90ab-f07612fa8d3a", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177798346700, "endTime": 34177799976100}, "additional": {"logType": "info", "children": [], "durationId": "cce1d5b9-3b31-4425-b5ec-9fbb55df9588"}}, {"head": {"id": "f5d826eb-6bfa-4f5f-84ce-1ad09af164c8", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177801980600, "endTime": 34177802456700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0e234aa0-e86b-4d31-af12-ef8c829f6fe8", "logId": "62ec5e02-c586-4034-9e52-65ec5a570205"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e234aa0-e86b-4d31-af12-ef8c829f6fe8", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177801398600}, "additional": {"logType": "detail", "children": [], "durationId": "f5d826eb-6bfa-4f5f-84ce-1ad09af164c8"}}, {"head": {"id": "625b99e7-dad2-428a-bec7-cbcf5937e02c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177801745100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ca2c82-a9ad-48d4-aeb7-6b13b33bed3f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177801839500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "515e4b85-aeff-4d9f-8c8e-52c74bc90386", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177801986800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b61805-bfa5-43be-b75f-c5968e5c37ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177802073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "018955e5-586b-424b-ab45-948dc9e92229", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177802173200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "231d7a46-2d93-4023-97de-d69676b415c6", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177802297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aad6039-bbee-4f4f-bbc1-567585811a1c", "name": "runTaskFromQueue task cost before running: 280 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177802391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ec5e02-c586-4034-9e52-65ec5a570205", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177801980600, "endTime": 34177802456700, "totalTime": 391500}, "additional": {"logType": "info", "children": [], "durationId": "f5d826eb-6bfa-4f5f-84ce-1ad09af164c8"}}, {"head": {"id": "721e965c-aa21-472d-8fe8-b01f3bd5f972", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177806415400, "endTime": 34177808670100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "88c31370-8bbb-44d6-b39c-b3e86637f748", "logId": "83be3625-e501-4ee0-b371-25dae957931d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88c31370-8bbb-44d6-b39c-b3e86637f748", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177804473300}, "additional": {"logType": "detail", "children": [], "durationId": "721e965c-aa21-472d-8fe8-b01f3bd5f972"}}, {"head": {"id": "3d4e6b77-4ccd-4254-aa5d-6911db0ab8e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177805259200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c64044-57c4-43f1-a61b-c48ecee7ae99", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177805372500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30fa966a-c7d3-4efb-b91f-f8f30c305d12", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177806427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e1d2cd-ae60-49af-96f7-3cd71e937a5d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177807943700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40b3656-ccb7-4887-b2f8-b4b3b712224a", "name": "entry : default@MergeProfile cost memory 0.10483551025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177808105000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83be3625-e501-4ee0-b371-25dae957931d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177806415400, "endTime": 34177808670100}, "additional": {"logType": "info", "children": [], "durationId": "721e965c-aa21-472d-8fe8-b01f3bd5f972"}}, {"head": {"id": "0226a3d7-3988-4695-b91c-e9bccd143d59", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177817390000, "endTime": 34177825988000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "84c0dbea-38ba-49ae-9453-63a34a0d8b59", "logId": "df207d93-726e-4147-a119-2dcc01379ebe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84c0dbea-38ba-49ae-9453-63a34a0d8b59", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177812053800}, "additional": {"logType": "detail", "children": [], "durationId": "0226a3d7-3988-4695-b91c-e9bccd143d59"}}, {"head": {"id": "e27198b1-cae0-459a-87bc-f16b1dfcc03f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177813724000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5819f9ac-ba54-413f-8f5e-980525a4c2a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177814246600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffbe0e6b-0e5c-471a-9c22-5fe7efcc24a4", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177817454500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce18ebe5-62be-484d-966e-257c0e65d75d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177820486600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2ee2e06-6bae-47e1-8fa9-357158955284", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177824886600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61fd6fb5-f577-4aa5-bc52-d9a2a9644968", "name": "entry : default@CreateBuildProfile cost memory 0.10105133056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177825687900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df207d93-726e-4147-a119-2dcc01379ebe", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177817390000, "endTime": 34177825988000}, "additional": {"logType": "info", "children": [], "durationId": "0226a3d7-3988-4695-b91c-e9bccd143d59"}}, {"head": {"id": "17b83715-fe48-4457-851d-66b8f8afb756", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177834846500, "endTime": 34177835205900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d13471fc-b621-475b-9f3e-59191545fadd", "logId": "d8a14636-74ba-4ddf-b8e7-825a01ac2da8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d13471fc-b621-475b-9f3e-59191545fadd", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177833599100}, "additional": {"logType": "detail", "children": [], "durationId": "17b83715-fe48-4457-851d-66b8f8afb756"}}, {"head": {"id": "031a3a8f-b06c-47d8-b9ee-0b60f6c34ec4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177833994000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c2a85d-2b04-4c32-9967-ff24580ad381", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177834096600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe426300-d4f6-46dc-bc28-978cbaf79b89", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177834854500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c3f131-5a37-451a-9335-7ad34a827d1b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177834959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11338a6b-5607-47e4-a1fa-1e1c37227740", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177835011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee8a878c-f6a2-4adb-ac74-eacab92a8568", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177835076000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5bdaeb0-727c-412a-b7bd-1071963a096e", "name": "runTaskFromQueue task cost before running: 313 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177835158300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8a14636-74ba-4ddf-b8e7-825a01ac2da8", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177834846500, "endTime": 34177835205900, "totalTime": 296300}, "additional": {"logType": "info", "children": [], "durationId": "17b83715-fe48-4457-851d-66b8f8afb756"}}, {"head": {"id": "e379bd39-5f64-4118-ba93-674e7e66c5a2", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177842036500, "endTime": 34177842612000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "314f2b23-f41d-4b11-8df2-52cfab4e1abd", "logId": "41584adf-a8bd-4a1d-8e4f-273c699f4f87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "314f2b23-f41d-4b11-8df2-52cfab4e1abd", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177837048100}, "additional": {"logType": "detail", "children": [], "durationId": "e379bd39-5f64-4118-ba93-674e7e66c5a2"}}, {"head": {"id": "c4bc89a3-2c12-48db-bfb0-9e554582dce1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177837596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73720d2-caa7-429b-929f-d90c615928ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177837728300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33ebe28b-f03a-4bc0-a47f-c17b4005f68b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177842047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce93b86c-18a5-4ee1-81b8-fb6448cd56c1", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177842278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6271259-5a34-489e-a992-49383b43b36c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.037109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177842466800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6151a8b2-7b59-421f-9aa6-606379014681", "name": "runTaskFromQueue task cost before running: 320 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177842544200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41584adf-a8bd-4a1d-8e4f-273c699f4f87", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177842036500, "endTime": 34177842612000, "totalTime": 495300}, "additional": {"logType": "info", "children": [], "durationId": "e379bd39-5f64-4118-ba93-674e7e66c5a2"}}, {"head": {"id": "42729c20-e2fe-4c14-872a-baa443efa665", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177846174800, "endTime": 34177847896400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "faf6f7f2-3678-472c-a1b5-c9c497476e1e", "logId": "659d3ce2-8825-4c74-9171-f38ffb479d05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "faf6f7f2-3678-472c-a1b5-c9c497476e1e", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177844065300}, "additional": {"logType": "detail", "children": [], "durationId": "42729c20-e2fe-4c14-872a-baa443efa665"}}, {"head": {"id": "e5d5f836-db46-4308-b5e5-ee03626021ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177844417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc584d3-d08d-4bc2-9147-5b4119fbbc22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177844510900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fe51bf-0a33-4ae4-b145-cf723e597e12", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177846185900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f39f873-12d4-47ff-a42c-d8087804034f", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177847481500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "589daa34-78b8-4a0f-a32f-a117b978b8db", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177847592700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c770b322-0d0f-4a52-ab2e-d46c4b92e280", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177847674100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcde6120-0d8a-46f6-84fd-95c55c84db75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177847722300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a64507-1085-49f6-ac93-3c83f9372049", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1171417236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177847787700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851ba680-96e4-4fee-9823-0dee2ad46ad0", "name": "runTaskFromQueue task cost before running: 325 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177847848900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659d3ce2-8825-4c74-9171-f38ffb479d05", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177846174800, "endTime": 34177847896400, "totalTime": 1662600}, "additional": {"logType": "info", "children": [], "durationId": "42729c20-e2fe-4c14-872a-baa443efa665"}}, {"head": {"id": "416eb6a3-5270-4b2a-b3fb-d37814af98ac", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851306500, "endTime": 34177851702300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cb4bd04c-afd4-4fc8-8ee6-36dcc4abe419", "logId": "77dc1f66-a04c-44d2-a631-5f45f904b2ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb4bd04c-afd4-4fc8-8ee6-36dcc4abe419", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177849950900}, "additional": {"logType": "detail", "children": [], "durationId": "416eb6a3-5270-4b2a-b3fb-d37814af98ac"}}, {"head": {"id": "1c6cc477-aa79-423b-adef-a2e172486ebc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177850312800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca6dcbe7-45cc-488c-bcd4-b7e8826a0806", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177850448700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff8e40f-8b6b-407e-9eb3-fef6c1fb3a38", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba53731-96ea-4238-933b-5bbb5a337a40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851439800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "617b8274-ed96-4694-a2d3-bb04a54861d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22269bca-5d83-469e-8a03-9fc8210436e1", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7537cf5b-ef70-44ea-8600-444a3e6cbeee", "name": "runTaskFromQueue task cost before running: 329 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851643300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77dc1f66-a04c-44d2-a631-5f45f904b2ef", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177851306500, "endTime": 34177851702300, "totalTime": 320400}, "additional": {"logType": "info", "children": [], "durationId": "416eb6a3-5270-4b2a-b3fb-d37814af98ac"}}, {"head": {"id": "89bdca44-7fab-463f-aada-b82efc2c67ee", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177858589200, "endTime": 34177861800200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7abf28f6-acf8-4bf5-b4bd-1c953deeb75f", "logId": "52f3de8a-5b95-4779-a732-d09600fb5298"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7abf28f6-acf8-4bf5-b4bd-1c953deeb75f", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177856738600}, "additional": {"logType": "detail", "children": [], "durationId": "89bdca44-7fab-463f-aada-b82efc2c67ee"}}, {"head": {"id": "21a03fb2-03d1-4cd1-ae8d-2f7ab97b6dcb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177857449700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bae72f7-3501-46eb-bf78-e44c1d883ca7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177857832700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "027f7baf-5f76-4510-b769-66910b3f2132", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177858597800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d777f05e-4e32-4587-8990-dc17eb840952", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177861597000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e41cedec-b0d6-41b7-bd91-bc0b071f96c8", "name": "entry : default@MakePackInfo cost memory 0.138458251953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177861735500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52f3de8a-5b95-4779-a732-d09600fb5298", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177858589200, "endTime": 34177861800200}, "additional": {"logType": "info", "children": [], "durationId": "89bdca44-7fab-463f-aada-b82efc2c67ee"}}, {"head": {"id": "c9f7d363-284f-4ad9-8d52-0f91eb724db3", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177865281100, "endTime": 34177867775400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "15858414-89e7-4a3a-bb04-88af550cf172", "logId": "f74667db-3e0a-45e5-8093-a8d92<PERSON><PERSON><PERSON>"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15858414-89e7-4a3a-bb04-88af550cf172", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177863705100}, "additional": {"logType": "detail", "children": [], "durationId": "c9f7d363-284f-4ad9-8d52-0f91eb724db3"}}, {"head": {"id": "79341e35-7d0c-4f35-9379-594c8359018b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177864049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474abc7b-eefe-4160-9523-83273ace7c11", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177864166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c59c692e-6671-4d94-8660-1ad0edbbe44e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177865290000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68306660-57d7-4973-be98-68f4332514e6", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177865429200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d642de7-f314-4de6-a458-a901f3642211", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177865954200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "064e98cd-6922-448f-943f-fcf37b2da117", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177867261300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3188281d-aee8-43a1-8f2a-dceac38c32e8", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177867360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c9a5e95-f8d4-4586-8730-d18357624426", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177867438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "219bb0f3-dcb1-43b7-84e6-bef0bdf89dfa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177867488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d07cb5-4d0f-4ff3-8412-e7cf50bd3416", "name": "entry : default@SyscapTransform cost memory 0.15081024169921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177867557700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a157a2-cff2-421f-975a-1562b93abc4c", "name": "runTaskFromQueue task cost before running: 345 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177867659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74667db-3e0a-45e5-8093-a8d92<PERSON><PERSON><PERSON>", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177865281100, "endTime": 34177867775400, "totalTime": 2330900}, "additional": {"logType": "info", "children": [], "durationId": "c9f7d363-284f-4ad9-8d52-0f91eb724db3"}}, {"head": {"id": "908251a5-5a6a-41bf-9b1c-42e8169a7cfa", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177872913400, "endTime": 34177874172300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7c338b83-86de-4e96-bd4d-8c26fe53f56e", "logId": "4c3c7eb4-deeb-41cc-b905-c65796826eb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c338b83-86de-4e96-bd4d-8c26fe53f56e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177869190900}, "additional": {"logType": "detail", "children": [], "durationId": "908251a5-5a6a-41bf-9b1c-42e8169a7cfa"}}, {"head": {"id": "da40a2e5-e39f-4549-a65c-ba017c567ebe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177870720000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bd5f0f-aa63-47c9-a4e1-4a16fc91c631", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177871223100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea3fc46c-453e-4e83-b1c7-b85e55e8fa1a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177872924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1df3c3b-7f0d-4399-8556-a9ece4541a08", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177873993500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255c3b49-577b-4ac9-97db-a9c2ef189853", "name": "entry : default@ProcessProfile cost memory 0.05930328369140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177874100500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3c7eb4-deeb-41cc-b905-c65796826eb4", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177872913400, "endTime": 34177874172300}, "additional": {"logType": "info", "children": [], "durationId": "908251a5-5a6a-41bf-9b1c-42e8169a7cfa"}}, {"head": {"id": "ecedf2c4-54a7-4e7e-a514-8620a2450a5d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177878048200, "endTime": 34177881996400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ca854884-fefc-4cf7-ae73-299216c8fab6", "logId": "7827b5b5-63b2-46ea-a09f-dfbd394238d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca854884-fefc-4cf7-ae73-299216c8fab6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177875612700}, "additional": {"logType": "detail", "children": [], "durationId": "ecedf2c4-54a7-4e7e-a514-8620a2450a5d"}}, {"head": {"id": "d09179e7-57f8-4ed4-8954-4bac1167b544", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177875953300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f613d76-fd91-411c-8702-e0a6bb87026e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177876057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e647612f-3e0b-4830-88e3-77499346f939", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177878059200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa56b7d6-ebbe-4651-ab06-fc610353a1ee", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177881797400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e50dd4-074e-4170-b7aa-87c6c5396b8f", "name": "entry : default@ProcessRouterMap cost memory 0.20146942138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177881926800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7827b5b5-63b2-46ea-a09f-dfbd394238d1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177878048200, "endTime": 34177881996400}, "additional": {"logType": "info", "children": [], "durationId": "ecedf2c4-54a7-4e7e-a514-8620a2450a5d"}}, {"head": {"id": "3025c660-3370-404e-a6bd-3ee75fa1d693", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177885369900, "endTime": 34177886905700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3d09e14f-436c-4568-a223-cf7d1b9a4968", "logId": "b85382d4-57c4-40d1-bd4f-3662423d0c5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d09e14f-436c-4568-a223-cf7d1b9a4968", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177884234400}, "additional": {"logType": "detail", "children": [], "durationId": "3025c660-3370-404e-a6bd-3ee75fa1d693"}}, {"head": {"id": "d56c964f-ab0b-48cc-a56c-b09c8ba11463", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177884558600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d747195c-0b61-423e-b4ad-ff3a852ff813", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177884655700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91dccf10-1aa0-4735-91eb-9a565f5f061f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177885383200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6503a749-2b4d-4dc7-8525-87cfbb343cdb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177885615900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2864dec1-7559-4a2e-b388-fc16ca70a773", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177885693200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a763ec-4920-466a-8817-8ffa7e56ea09", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177886698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fdd08cd-0863-4d6e-8c39-ab8f70137206", "name": "runTaskFromQueue task cost before running: 364 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177886836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b85382d4-57c4-40d1-bd4f-3662423d0c5f", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177885369900, "endTime": 34177886905700, "totalTime": 1445800}, "additional": {"logType": "info", "children": [], "durationId": "3025c660-3370-404e-a6bd-3ee75fa1d693"}}, {"head": {"id": "bf7bcf26-4016-4fcf-a7e5-ae8c68a588ea", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177892800400, "endTime": 34177897284300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a21f7d4b-4ab3-4df8-83ff-0a234515830e", "logId": "4f0d53dc-41fd-45ac-adf9-537150285409"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a21f7d4b-4ab3-4df8-83ff-0a234515830e", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177890589000}, "additional": {"logType": "detail", "children": [], "durationId": "bf7bcf26-4016-4fcf-a7e5-ae8c68a588ea"}}, {"head": {"id": "b5022d42-9bfc-4b24-93df-8aea566c780b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177890950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b67874-c006-4402-b073-67a991b55570", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177891047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c220693-accb-4e9a-aec3-075105788bdd", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177891841900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b83573-3b83-43a5-b4e4-6f60aeaa7d56", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177894103200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9464553f-9f38-4c71-a2b1-fb6bf36047a6", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177895665800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec1b77e1-b591-405f-a3c8-7b5a142b1a2e", "name": "entry : default@ProcessResource cost memory 0.1686553955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177895763800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0d53dc-41fd-45ac-adf9-537150285409", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177892800400, "endTime": 34177897284300}, "additional": {"logType": "info", "children": [], "durationId": "bf7bcf26-4016-4fcf-a7e5-ae8c68a588ea"}}, {"head": {"id": "ca349125-2404-4a35-83b0-43c742a50629", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177906970600, "endTime": 34177921593600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b4953018-0171-40af-bbf7-5acbf33e3edf", "logId": "7b84792f-31e1-428c-a609-3bbb6a5a32fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4953018-0171-40af-bbf7-5acbf33e3edf", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177900304900}, "additional": {"logType": "detail", "children": [], "durationId": "ca349125-2404-4a35-83b0-43c742a50629"}}, {"head": {"id": "0a603be0-0fae-4525-bcec-786da0129384", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177900837400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0b698b-e4f0-435f-9f62-f9e1066ad390", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177901512500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9baa160-e96f-4f96-8c14-9fb8639b91c4", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177906983100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2eeedf-46cd-4a36-a389-02aba18f55d4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177921391800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbeea784-d2c6-46ac-b1c5-2a0f52f28ef7", "name": "entry : default@GenerateLoaderJson cost memory 0.7634124755859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177921526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b84792f-31e1-428c-a609-3bbb6a5a32fa", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177906970600, "endTime": 34177921593600}, "additional": {"logType": "info", "children": [], "durationId": "ca349125-2404-4a35-83b0-43c742a50629"}}, {"head": {"id": "b24956a5-c0f4-46f7-b356-f0286fdaa5fe", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177928578800, "endTime": 34177933571000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4bd299c9-5f52-4f12-9bcf-f688d7f294ee", "logId": "38ce235c-3e7d-4843-9ae2-585f7aa2d67c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bd299c9-5f52-4f12-9bcf-f688d7f294ee", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177927305700}, "additional": {"logType": "detail", "children": [], "durationId": "b24956a5-c0f4-46f7-b356-f0286fdaa5fe"}}, {"head": {"id": "fd06d0e5-60fe-4aa3-b17f-26b11d90bfb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177927710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48c6d578-1268-4c90-9d61-8511f759105a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177927839700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a913da64-76ff-4a33-a0c3-2a98f3534beb", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177928593500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75e9e6a-e369-4646-967d-161367480f9f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177932065800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20155506-9189-44a9-8a05-9505968434c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177932227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e9c0d4-b309-4232-966e-415ee7c268bb", "name": "entry : default@ProcessLibs cost memory -5.384407043457031", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177933333700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21f3171-4986-4ef6-9cd6-4021e677336b", "name": "runTaskFromQueue task cost before running: 411 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177933493200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ce235c-3e7d-4843-9ae2-585f7aa2d67c", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177928578800, "endTime": 34177933571000, "totalTime": 4880700}, "additional": {"logType": "info", "children": [], "durationId": "b24956a5-c0f4-46f7-b356-f0286fdaa5fe"}}, {"head": {"id": "6b4160e7-239e-4cee-ab21-007a5192ecd5", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177940711900, "endTime": 34177960224100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "35df3f5a-592c-481c-8f20-71e73e58f9ea", "logId": "8df95a02-5eeb-457b-92ca-abb7446523dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35df3f5a-592c-481c-8f20-71e73e58f9ea", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177936387600}, "additional": {"logType": "detail", "children": [], "durationId": "6b4160e7-239e-4cee-ab21-007a5192ecd5"}}, {"head": {"id": "6e821cdd-6618-44f7-816c-beee125495cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177936983600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038f2eff-47fe-4cd4-93ca-d6260e723743", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177937150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874c1bce-ab7b-446b-965b-87b5c4076a54", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177938021400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19370405-c9f3-4f49-93b4-1ee1a8ed2db8", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177940735300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7affa036-a542-4ec4-8c3d-f5bc9c9d99a8", "name": "Incremental task entry:default@CompileResource pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177960020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4de33dc-38de-4a88-95f3-5529b3132ae8", "name": "entry : default@CompileResource cost memory 1.4072341918945312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177960145500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8df95a02-5eeb-457b-92ca-abb7446523dd", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177940711900, "endTime": 34177960224100}, "additional": {"logType": "info", "children": [], "durationId": "6b4160e7-239e-4cee-ab21-007a5192ecd5"}}, {"head": {"id": "5b7b082a-51ef-48b2-9d40-cfaa8e1bd319", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177966822900, "endTime": 34177968587200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "59d2c8bd-8b12-4589-ad6b-1bf3f7976fd7", "logId": "4c20f2f8-df8f-4fe5-9f27-8f63b63b78d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59d2c8bd-8b12-4589-ad6b-1bf3f7976fd7", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177962601300}, "additional": {"logType": "detail", "children": [], "durationId": "5b7b082a-51ef-48b2-9d40-cfaa8e1bd319"}}, {"head": {"id": "1cf1b6ca-0ff7-45bb-8876-0123883f97f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177963500400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc5a215-1dce-408c-91db-666e945dde36", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177963694700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc88fb17-e5fc-4c07-bac1-24d09fd213d1", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177966857800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9992bd94-d6d1-4509-af63-1e986e334e47", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177967083500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dce49588-d83c-4ddb-932b-abf755606f21", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177968114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d25f30cf-39b6-4232-81ed-b2f97c5521f7", "name": "entry : default@DoNativeStrip cost memory 0.0740509033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177968488800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c20f2f8-df8f-4fe5-9f27-8f63b63b78d9", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177966822900, "endTime": 34177968587200}, "additional": {"logType": "info", "children": [], "durationId": "5b7b082a-51ef-48b2-9d40-cfaa8e1bd319"}}, {"head": {"id": "f5135106-5b32-4252-8658-f82f087d332e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177977450600, "endTime": 34180176549100}, "additional": {"children": ["4a61254b-a420-489c-948b-3950b26e02f1", "ef2687a4-75f8-4fee-9890-e96a931d5121"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "b04852ee-88ff-40ff-b923-c05c0eec620e", "logId": "eedc24d3-0464-41c4-97ef-a9f7ca2f9ae7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b04852ee-88ff-40ff-b923-c05c0eec620e", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177972797900}, "additional": {"logType": "detail", "children": [], "durationId": "f5135106-5b32-4252-8658-f82f087d332e"}}, {"head": {"id": "e94d2275-adb8-4059-a9ae-4392bbe697d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177973320000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0513bef2-9bc8-4e40-a203-6556d72368d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177973426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bbf313-9ff4-423d-b733-54146f3f6c72", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177977464000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "622ca7a3-9ab1-4b98-8dec-a8c0d355f3b2", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177992454100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62e56df-ad19-4acc-a60a-4b886ecc1ac2", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177992593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80223718-19e4-418b-9aef-788d5052eae5", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178010596300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045eb07d-a955-4f60-9fd9-c00a99a8b1f8", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178011695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbbd8377-59af-458f-9cd7-5c15c2986b5a", "name": "default@CompileArkTS work[118] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178013513800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a61254b-a420-489c-948b-3950b26e02f1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34178227361300, "endTime": 34180166174900}, "additional": {"children": ["0543c79f-57d7-4a10-a6cd-3898f038dc8e", "c3e9dcce-4623-4845-81ec-c6c3a5ed9d80", "951a7e88-372a-4886-9a17-9c07a7f5a6e2", "58fdf5f7-8db4-4b4c-8aa4-d2329af790c8", "751088b2-e4e7-44d8-884b-b0ec3b296345", "77eb8c51-e1be-4f5d-9820-804921c61c59"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f5135106-5b32-4252-8658-f82f087d332e", "logId": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "703c6657-072d-455d-82af-aba393587d0b", "name": "default@CompileArkTS work[118] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178014491500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f27f5f5-9bb8-4a93-a77a-ff9185a3e3e1", "name": "default@CompileArkTS work[118] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178014694500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d881541-4350-4715-85ca-dbf2ec1f9d9c", "name": "CopyResources startTime: 34178014792500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178014796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72d975e-923e-4c9e-9937-1e666b262bdb", "name": "default@CompileArkTS work[119] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178014913100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef2687a4-75f8-4fee-9890-e96a931d5121", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34179759366800, "endTime": 34179772058700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f5135106-5b32-4252-8658-f82f087d332e", "logId": "46695336-4300-4de1-b510-c9386c40655e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4220829e-0a73-492d-b32e-c1b1b0fcc61a", "name": "default@CompileArkTS work[119] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178016301200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e92d4261-b301-4f7f-9b19-7eeea2eb248a", "name": "default@CompileArkTS work[119] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178016455200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f32d1f97-7d82-4f23-a31b-1add1bb37950", "name": "entry : default@CompileArkTS cost memory 1.590728759765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178016560000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a29ab1-ea52-4d3a-ab93-45325e56469d", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178023449100, "endTime": 34178027387600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2ab95fed-b168-4443-89b8-e9817ad7233f", "logId": "ea5c0f04-4de0-4b08-8678-81ef89a924c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ab95fed-b168-4443-89b8-e9817ad7233f", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178018194300}, "additional": {"logType": "detail", "children": [], "durationId": "b6a29ab1-ea52-4d3a-ab93-45325e56469d"}}, {"head": {"id": "46486f56-e99b-4bbe-96e4-89503b39a731", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178018593600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc17799d-6416-4522-9b1c-41d38f1680a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178018705500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb68629-d9c2-415c-83c3-c04dbc7d8708", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178023461300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95c3db5-d371-4b0c-8946-4ca45392c2e4", "name": "entry : default@BuildJS cost memory 0.12652587890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178027198000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfcc1b3-58e3-40e5-b9be-d979d9fe86c4", "name": "runTaskFromQueue task cost before running: 505 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178027327400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea5c0f04-4de0-4b08-8678-81ef89a924c2", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178023449100, "endTime": 34178027387600, "totalTime": 3859400}, "additional": {"logType": "info", "children": [], "durationId": "b6a29ab1-ea52-4d3a-ab93-45325e56469d"}}, {"head": {"id": "0defa0be-ddab-47ab-a402-e234bb2bc8c4", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178032045400, "endTime": 34178033695400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5265b9c4-f5aa-4d06-a824-07f2459a1d60", "logId": "55ba31fa-9ea8-4233-a030-8f8f03b8b14b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5265b9c4-f5aa-4d06-a824-07f2459a1d60", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178028979500}, "additional": {"logType": "detail", "children": [], "durationId": "0defa0be-ddab-47ab-a402-e234bb2bc8c4"}}, {"head": {"id": "1cfc55fd-65a5-40c3-9d8c-99a51162b507", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178029300000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b227a8e4-e9c6-4076-9552-df46e35ca583", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178029393900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6d0c2d-c6cf-49a8-919c-d22312ad3917", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178032055800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a1c589b-3527-4406-9273-b7edf20201d1", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178032399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058367fe-49a6-4157-af66-c8ede2b2fbc6", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178033497700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ba3088c-cdc6-4d51-8217-573bf49bf248", "name": "entry : default@CacheNativeLibs cost memory 0.08740997314453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178033628100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ba31fa-9ea8-4233-a030-8f8f03b8b14b", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178032045400, "endTime": 34178033695400}, "additional": {"logType": "info", "children": [], "durationId": "0defa0be-ddab-47ab-a402-e234bb2bc8c4"}}, {"head": {"id": "96a06541-44a5-4608-aebc-51d6135779ec", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178226546200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feac201c-b5c3-43b2-8da9-c1b1177aceb9", "name": "default@CompileArkTS work[118] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178227161100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61940911-2f57-4f51-8a9b-a3972f0ede88", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178227327400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f46b82-acd9-4e21-808b-7bb62983fef1", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178227395100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0d6142-a00e-41e2-a9e2-fec3a7f5383c", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178227459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d694941-0cb1-4d6e-b4b3-812e20184c56", "name": "default@CompileArkTS work[119] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178229865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc6491e-846d-4003-bcd9-be4b7a0d508e", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34179772248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62de89b3-c3ed-4d9f-94b5-c4e1ac252eac", "name": "CopyResources is end, endTime: 34179772373500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34179772376900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8690d7f-b501-4a68-bdf9-b911bfd18886", "name": "default@CompileArkTS work[119] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34179772454600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46695336-4300-4de1-b510-c9386c40655e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34179759366800, "endTime": 34179772058700}, "additional": {"logType": "info", "children": [], "durationId": "ef2687a4-75f8-4fee-9890-e96a931d5121", "parent": "eedc24d3-0464-41c4-97ef-a9f7ca2f9ae7"}}, {"head": {"id": "02262f35-7d25-466d-9a24-d6fb71e8b077", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34179772539600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f20225-90d4-4a64-b5ce-50a4cbb685d4", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180167742000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0543c79f-57d7-4a10-a6cd-3898f038dc8e", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34178227469300, "endTime": 34178231691200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4a61254b-a420-489c-948b-3950b26e02f1", "logId": "267ff8d6-d545-4d19-a608-94fde82e9240"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "267ff8d6-d545-4d19-a608-94fde82e9240", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178227469300, "endTime": 34178231691200}, "additional": {"logType": "info", "children": [], "durationId": "0543c79f-57d7-4a10-a6cd-3898f038dc8e", "parent": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}}, {"head": {"id": "c3e9dcce-4623-4845-81ec-c6c3a5ed9d80", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34178231710900, "endTime": 34178231997000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4a61254b-a420-489c-948b-3950b26e02f1", "logId": "2335531e-d693-4ffe-a7c8-502545926719"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2335531e-d693-4ffe-a7c8-502545926719", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178231710900, "endTime": 34178231997000}, "additional": {"logType": "info", "children": [], "durationId": "c3e9dcce-4623-4845-81ec-c6c3a5ed9d80", "parent": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}}, {"head": {"id": "951a7e88-372a-4886-9a17-9c07a7f5a6e2", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34178232709200, "endTime": 34178232760500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4a61254b-a420-489c-948b-3950b26e02f1", "logId": "d702be14-4bcf-49b2-a378-e70ee7c5482c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d702be14-4bcf-49b2-a378-e70ee7c5482c", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178232709200, "endTime": 34178232760500}, "additional": {"logType": "info", "children": [], "durationId": "951a7e88-372a-4886-9a17-9c07a7f5a6e2", "parent": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}}, {"head": {"id": "58fdf5f7-8db4-4b4c-8aa4-d2329af790c8", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34178232780600, "endTime": 34180044779900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4a61254b-a420-489c-948b-3950b26e02f1", "logId": "0f291588-4c6a-4f2d-bb90-24ab9ebc1be5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f291588-4c6a-4f2d-bb90-24ab9ebc1be5", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34178232780600, "endTime": 34180044779900}, "additional": {"logType": "info", "children": [], "durationId": "58fdf5f7-8db4-4b4c-8aa4-d2329af790c8", "parent": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}}, {"head": {"id": "751088b2-e4e7-44d8-884b-b0ec3b296345", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180044799500, "endTime": 34180050308500}, "additional": {"children": ["c4363187-4ffe-44b3-a5a5-3b302d042ef0", "f969e0de-7320-4a7a-802f-f3828c67b9d7", "b5334085-dd54-45cb-9340-1252f73a6c27"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4a61254b-a420-489c-948b-3950b26e02f1", "logId": "c2e28622-63e0-4ef0-ab1e-b9d94c1c8d7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2e28622-63e0-4ef0-ab1e-b9d94c1c8d7a", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180044799500, "endTime": 34180050308500}, "additional": {"logType": "info", "children": ["e5d3b312-01bb-441a-bd09-0cff73bb567e", "aa5d3031-45f2-4332-9b7a-823c33bf38ec", "d5d67c10-0a25-4bfb-9427-6d0ef5925e88"], "durationId": "751088b2-e4e7-44d8-884b-b0ec3b296345", "parent": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}}, {"head": {"id": "c4363187-4ffe-44b3-a5a5-3b302d042ef0", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180044822100, "endTime": 34180044829400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "751088b2-e4e7-44d8-884b-b0ec3b296345", "logId": "e5d3b312-01bb-441a-bd09-0cff73bb567e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5d3b312-01bb-441a-bd09-0cff73bb567e", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180044822100, "endTime": 34180044829400}, "additional": {"logType": "info", "children": [], "durationId": "c4363187-4ffe-44b3-a5a5-3b302d042ef0", "parent": "c2e28622-63e0-4ef0-ab1e-b9d94c1c8d7a"}}, {"head": {"id": "f969e0de-7320-4a7a-802f-f3828c67b9d7", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180044833100, "endTime": 34180046455900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "751088b2-e4e7-44d8-884b-b0ec3b296345", "logId": "aa5d3031-45f2-4332-9b7a-823c33bf38ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa5d3031-45f2-4332-9b7a-823c33bf38ec", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180044833100, "endTime": 34180046455900}, "additional": {"logType": "info", "children": [], "durationId": "f969e0de-7320-4a7a-802f-f3828c67b9d7", "parent": "c2e28622-63e0-4ef0-ab1e-b9d94c1c8d7a"}}, {"head": {"id": "b5334085-dd54-45cb-9340-1252f73a6c27", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180046459500, "endTime": 34180050294300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "751088b2-e4e7-44d8-884b-b0ec3b296345", "logId": "d5d67c10-0a25-4bfb-9427-6d0ef5925e88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5d67c10-0a25-4bfb-9427-6d0ef5925e88", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180046459500, "endTime": 34180050294300}, "additional": {"logType": "info", "children": [], "durationId": "b5334085-dd54-45cb-9340-1252f73a6c27", "parent": "c2e28622-63e0-4ef0-ab1e-b9d94c1c8d7a"}}, {"head": {"id": "77eb8c51-e1be-4f5d-9820-804921c61c59", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180050326200, "endTime": 34180166029000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4a61254b-a420-489c-948b-3950b26e02f1", "logId": "e13857d3-3c98-45ab-a29c-7da8c650a0c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e13857d3-3c98-45ab-a29c-7da8c650a0c9", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180050326200, "endTime": 34180166029000}, "additional": {"logType": "info", "children": [], "durationId": "77eb8c51-e1be-4f5d-9820-804921c61c59", "parent": "915024f1-b22a-4112-b3c7-c8a47e6b133f"}}, {"head": {"id": "46f906f5-c20e-4e77-921d-9f63b9f23f16", "name": "default@CompileArkTS work[118] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180176345900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915024f1-b22a-4112-b3c7-c8a47e6b133f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34178227361300, "endTime": 34180166174900}, "additional": {"logType": "info", "children": ["267ff8d6-d545-4d19-a608-94fde82e9240", "2335531e-d693-4ffe-a7c8-502545926719", "d702be14-4bcf-49b2-a378-e70ee7c5482c", "0f291588-4c6a-4f2d-bb90-24ab9ebc1be5", "c2e28622-63e0-4ef0-ab1e-b9d94c1c8d7a", "e13857d3-3c98-45ab-a29c-7da8c650a0c9"], "durationId": "4a61254b-a420-489c-948b-3950b26e02f1", "parent": "eedc24d3-0464-41c4-97ef-a9f7ca2f9ae7"}}, {"head": {"id": "868e843b-0771-4e95-b8a3-b7863ad6a42f", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180176481800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eedc24d3-0464-41c4-97ef-a9f7ca2f9ae7", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177977450600, "endTime": 34180176549100, "totalTime": 1977977800}, "additional": {"logType": "info", "children": ["915024f1-b22a-4112-b3c7-c8a47e6b133f", "46695336-4300-4de1-b510-c9386c40655e"], "durationId": "f5135106-5b32-4252-8658-f82f087d332e"}}, {"head": {"id": "7b91d5a2-e106-48d3-b11a-fd884eadc902", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180182898500, "endTime": 34180184140900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "60ed37d4-2ddc-4278-b12f-8639b210ee8d", "logId": "d4fa3cb1-ddfd-4b0d-8ee5-e49f61d71447"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60ed37d4-2ddc-4278-b12f-8639b210ee8d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180181162400}, "additional": {"logType": "detail", "children": [], "durationId": "7b91d5a2-e106-48d3-b11a-fd884eadc902"}}, {"head": {"id": "81682431-7277-4b18-a653-ccc9a531eb3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180181602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d1b170-f638-4d65-a40c-39e4dc6d352c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180181753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "458b5f8b-1712-432e-8734-744bd709990b", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180182912600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed51278c-7ebc-4350-9026-cd50eb56dde8", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180183225300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54559d87-de0a-4047-a6fb-fe3faefa0132", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180183977400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a938ea5-2854-4e08-a960-01786c88bf10", "name": "entry : default@GeneratePkgModuleJson cost memory 0.076507568359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180184072600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4fa3cb1-ddfd-4b0d-8ee5-e49f61d71447", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180182898500, "endTime": 34180184140900}, "additional": {"logType": "info", "children": [], "durationId": "7b91d5a2-e106-48d3-b11a-fd884eadc902"}}, {"head": {"id": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180202834800, "endTime": 34180677543600}, "additional": {"children": ["a7e747d5-48b5-448e-a820-bb631d96df82", "a25fbe58-9745-4aff-b02c-17d89936431d", "0721e80e-5cc1-49ef-ba44-ebb437aed818"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "ebd94e3d-1051-4c44-92f6-501733f3895d", "logId": "c67c2411-1292-4a04-a352-7b6e0e9031fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebd94e3d-1051-4c44-92f6-501733f3895d", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180190456700}, "additional": {"logType": "detail", "children": [], "durationId": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05"}}, {"head": {"id": "ba782b01-dc67-4c09-be6c-d23dd20af032", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180190867700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a05148-fa6b-4485-adb3-b099e3548942", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180191515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df911010-2a7b-4515-81e8-3543c376d564", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180202849800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae7d1b45-8709-4c7f-8985-a46d21e05221", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180217040600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a733b8-6fd0-4abd-90dd-6ff1013383c0", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180217212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "308dc809-97c4-41c0-83da-ca2c121f0262", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180217312100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fec5e31-91cd-4a09-b3f0-ec79af566116", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180217366300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e747d5-48b5-448e-a820-bb631d96df82", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180218065600, "endTime": 34180220232800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05", "logId": "bb3dc1e3-e8d5-498c-87f4-f847a401454c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e92817a-c0f3-4284-bcf7-2bda15fe1c62", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180219252300}, "additional": {"logType": "debug", "children": [], "durationId": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05"}}, {"head": {"id": "bb3dc1e3-e8d5-498c-87f4-f847a401454c", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180218065600, "endTime": 34180220232800}, "additional": {"logType": "info", "children": [], "durationId": "a7e747d5-48b5-448e-a820-bb631d96df82", "parent": "c67c2411-1292-4a04-a352-7b6e0e9031fb"}}, {"head": {"id": "a25fbe58-9745-4aff-b02c-17d89936431d", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180221428000, "endTime": 34180224038500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05", "logId": "2bcd485a-09cd-4d83-a966-22d4ebc8fc63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df0e6778-d92f-4e7f-8f11-8cfa10c9ef67", "name": "default@PackageHap work[120] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180222774400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0721e80e-5cc1-49ef-ba44-ebb437aed818", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180224008600, "endTime": 34180674047900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05", "logId": "8cb3b186-213e-4afc-8724-f8e927effa9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af3186f9-aab7-49ea-8acf-675996c00ad4", "name": "default@PackageHap work[120] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180223735200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eb4535b-7e56-46f3-ade3-b2892bb159c4", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180223831400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5b99d6-67d7-448d-af77-83c355cc7756", "name": "default@PackageHap work[120] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180223930100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87693f6b-4b09-4351-bbe2-83c78d848514", "name": "default@PackageHap work[120] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180223987900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bcd485a-09cd-4d83-a966-22d4ebc8fc63", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180221428000, "endTime": 34180224038500}, "additional": {"logType": "info", "children": [], "durationId": "a25fbe58-9745-4aff-b02c-17d89936431d", "parent": "c67c2411-1292-4a04-a352-7b6e0e9031fb"}}, {"head": {"id": "3e2f1017-d3ee-48dd-b98d-e53cec0e9eae", "name": "entry : default@PackageHap cost memory 1.2813873291015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180230568800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f613899-ad1c-4d4a-804b-740dd9fd61f9", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180674186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "848dd41b-0f26-4b5a-a074-066722dd197d", "name": "default@PackageHap work[120] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180677067400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb3b186-213e-4afc-8724-f8e927effa9e", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34180224008600, "endTime": 34180674047900}, "additional": {"logType": "info", "children": [], "durationId": "0721e80e-5cc1-49ef-ba44-ebb437aed818", "parent": "c67c2411-1292-4a04-a352-7b6e0e9031fb"}}, {"head": {"id": "56f76534-e55c-4a67-9807-14689e1197aa", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180677450400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c67c2411-1292-4a04-a352-7b6e0e9031fb", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180202834800, "endTime": 34180677543600, "totalTime": 471216200}, "additional": {"logType": "info", "children": ["bb3dc1e3-e8d5-498c-87f4-f847a401454c", "2bcd485a-09cd-4d83-a966-22d4ebc8fc63", "8cb3b186-213e-4afc-8724-f8e927effa9e"], "durationId": "f94c2dcf-8dc7-4306-ae48-88620f2b4a05"}}, {"head": {"id": "e95ef6a1-31f3-455b-81b8-342917deef74", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180694138600, "endTime": 34180696695900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "eb4d9f72-9459-405e-ac26-bdfbe0bfcfc8", "logId": "f8298567-10dd-4608-b730-2d59d51c67d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb4d9f72-9459-405e-ac26-bdfbe0bfcfc8", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180683003100}, "additional": {"logType": "detail", "children": [], "durationId": "e95ef6a1-31f3-455b-81b8-342917deef74"}}, {"head": {"id": "946c79bb-9e2b-4f32-a5b2-572a7796238b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180683516800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7d5d96-f6e8-44c1-adc0-71f7f3411caa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180683638400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8214ae0a-faf7-4f5f-91fe-3ba81a7c0b44", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180694153300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49029ac6-a31d-47d7-b274-4f045d06dca0", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180694649500}, "additional": {"logType": "warn", "children": [], "durationId": "e95ef6a1-31f3-455b-81b8-342917deef74"}}, {"head": {"id": "c8172b68-94ab-4bb3-b21d-09bfb3af87ad", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180695543400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff0dd7e-cfd3-4776-9376-b439ee4b87e7", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180695672200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c59e130-27b3-4980-a66f-d3f16842bcd0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180695763000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa5fec93-4d5b-4329-9f49-764d06949a23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180695878700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0461170c-60c8-4bdd-bc32-b312f606fc90", "name": "entry : default@SignHap cost memory 0.116943359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180696486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d2fe02-50ba-444a-8115-7839e906999e", "name": "runTaskFromQueue task cost before running: 3 s 174 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180696595200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8298567-10dd-4608-b730-2d59d51c67d9", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180694138600, "endTime": 34180696695900, "totalTime": 2436800}, "additional": {"logType": "info", "children": [], "durationId": "e95ef6a1-31f3-455b-81b8-342917deef74"}}, {"head": {"id": "49316a8b-2b66-4e83-ad75-3f21e354119d", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180704536500, "endTime": 34180718538600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ba5d65a8-b6b8-4b99-9026-b2ef8ffd3ac5", "logId": "5119fe37-87ef-484b-a2ae-f09cde27da44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba5d65a8-b6b8-4b99-9026-b2ef8ffd3ac5", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180701104100}, "additional": {"logType": "detail", "children": [], "durationId": "49316a8b-2b66-4e83-ad75-3f21e354119d"}}, {"head": {"id": "77bc3fe3-b7e8-4fd8-83ae-d4fa5ee87430", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180701959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1903f38f-4016-45e4-b03d-00677913663a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180702081900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758b6875-5b73-4959-8859-6a67a447893d", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180704547700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dafc3169-b6ce-4900-a7cc-0e8e88500f67", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180718194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99c69cc-5023-46f4-b261-337d840f3272", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180718317500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5983997-9270-4239-b40b-64287e63f203", "name": "entry : default@CollectDebugSymbol cost memory 0.23859405517578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180718399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2858fe-5e82-4ff7-985c-04ffa8947e9f", "name": "runTaskFromQueue task cost before running: 3 s 196 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180718479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5119fe37-87ef-484b-a2ae-f09cde27da44", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180704536500, "endTime": 34180718538600, "totalTime": 13922100}, "additional": {"logType": "info", "children": [], "durationId": "49316a8b-2b66-4e83-ad75-3f21e354119d"}}, {"head": {"id": "5fbf43b7-570d-4d90-9fcf-529e4bb17abf", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180722312800, "endTime": 34180724337400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "690d4f9f-cb03-4a9b-b605-3da9c305bcf2", "logId": "9e01f131-ca41-4e5f-826c-76b112096140"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "690d4f9f-cb03-4a9b-b605-3da9c305bcf2", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180722050800}, "additional": {"logType": "detail", "children": [], "durationId": "5fbf43b7-570d-4d90-9fcf-529e4bb17abf"}}, {"head": {"id": "f0900fec-a132-4538-88e3-337527d2fe91", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180722324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3db5881-aac8-4684-a3e1-6a4938a3a8c4", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180722572500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec458447-64e6-478a-aa92-edd20735873b", "name": "runTaskFromQueue task cost before running: 3 s 201 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180724026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e01f131-ca41-4e5f-826c-76b112096140", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180722312800, "endTime": 34180724337400, "totalTime": 1002400}, "additional": {"logType": "info", "children": [], "durationId": "5fbf43b7-570d-4d90-9fcf-529e4bb17abf"}}, {"head": {"id": "bfb5a5d3-966e-4a82-8b82-fa5acc44be69", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180738196500, "endTime": 34180738268400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "343ad0ee-6af7-48d2-8768-16dd16f8cda7", "logId": "e1b78448-b8a7-450c-8d03-117c7cf64656"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1b78448-b8a7-450c-8d03-117c7cf64656", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180738196500, "endTime": 34180738268400}, "additional": {"logType": "info", "children": [], "durationId": "bfb5a5d3-966e-4a82-8b82-fa5acc44be69"}}, {"head": {"id": "30011e5e-e24f-41b8-ac52-80b085433a40", "name": "BUILD SUCCESSFUL in 3 s 216 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180738318200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "0a1ea403-9508-4299-89c4-113a0668b749", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34177522876200, "endTime": 34180739800600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 59}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "2e19569f-441f-404b-88a1-b657847e64ae", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180739847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3144424-0633-4425-8de1-ecc621e46f22", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180739979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea332b4-792d-49d5-b38f-39b55e56aea8", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180740044300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a9523f-ccfb-4088-8adb-dd8ef8e82f43", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180740098500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d92599a-4716-46c9-8d9a-b0851f495df3", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180740493300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55d97c4-1121-47a6-be99-e82ec56c3508", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180740975700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f943cda-b2b8-40dd-abfc-cabeeb81379a", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180741585400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6800e370-fb5e-4fe3-9c36-60abd9872a45", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180742043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38df8e1a-e94d-4d83-95c7-0e35a8f20d73", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180742137500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf1c3ec-157c-49c0-a713-1fd886560569", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180742217700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "948c9e27-4466-4738-af38-568e21380012", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180742461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc37c55c-d34a-4c98-92cf-868c7d2b7b8b", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180743357100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20957124-e390-4ef8-8620-f58c45aaec7b", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180743593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b92d10-f0f6-4295-bda0-2be494271654", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180743671000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399c14bd-4354-4d84-9351-eb11cbbef7a0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180743726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26b6226-f8d8-42c0-b114-f36742635d60", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180743781300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b7048ff-d9aa-4fda-aa4a-025939c8d4ec", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180743832600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e27d03c-7245-45cf-a89c-4ea15bfd5fe0", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180744088500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deca8265-d712-44f7-bb6c-4384627278c7", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180744542100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342617c3-8f2d-4c86-b873-b4f4d9f87a72", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180744780300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e9d860-7803-4c6c-a093-6d22beac9733", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180745108900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e413ae9-8055-47b4-b012-a3614aa09923", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180745185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd474115-a1d2-43c3-b829-4dd0cf9d41fb", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180745270300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c9e69ce-d931-4f28-a8d0-ef2ee3497416", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180747298300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "945886e5-a9e3-45c1-ae68-3101759a1190", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180747733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7694717e-037b-44ba-916d-703b12c7c99a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180748522000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811b72b2-a72b-4c54-8a4f-a4dff79f0e33", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180748732900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72599dc4-3da5-4a1e-b25c-3ca699f9f14e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180748920400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d51b69-122a-4b60-a99d-18ad385d2721", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180749509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a86bd96-4746-48da-9b50-07837b75d091", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180749593700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e75d8a-aa01-4b09-a6d2-76681cc47737", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180749948000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b39567db-d08e-413f-b6c8-bcf8b7ecece6", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180750172000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30fc3874-f536-4789-82f0-3e236f7ed568", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180750869400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e5dad8-64f1-4ca2-bc30-a7e3d0db40f9", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180751955100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5736fe39-0495-4f20-9f96-18a6db15d4e4", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180752472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258dbb9c-94a3-4ac7-adc1-6367e2daf7fe", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180754323800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116ec528-396c-42d9-993e-81f339c17235", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180754559000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f8dc74-e0c2-4e9b-b116-73e79fa74d2a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180755043100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afab828d-39b5-44ff-a9b8-ac9a28271eb9", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180755617900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6d24bd-fd98-48dd-83af-7799b7f26eaf", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180755871600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2329c454-05b0-4c8a-b548-16709a4370ec", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180755981800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ae325ee-3c15-47db-b886-90b04c5ac495", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180756041600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d6b6df-8cd4-4472-a3c4-c2a14932bb30", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180756884600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e58876e-f315-487c-932a-357e258157de", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180757133500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "720089f9-2fde-4c6c-8b73-44a572e65928", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180757330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45fea65-90ea-4f32-9154-4f4a89679b30", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180763189500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e001fcb2-6036-4867-a68e-a888e667c098", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180763410900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cebf54-ec7c-4fe4-9e72-e8f88c29fa18", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180763579900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67aae4a4-222a-4683-98fe-69d074773c26", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180763705700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21b6abdd-1fdf-4c87-9a7c-aab69b86ee25", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180763897900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddda0aa9-5901-4d8e-aad7-5b0ad2d92d06", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180764500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7e95be-a296-4e73-936c-c1bf541574fa", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180764707900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c74cc21d-630f-43ff-995f-9b514a35440f", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180764900700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46de5a53-959a-4be0-9c7f-c95e6b9edddd", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180765266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8175c936-bb27-4113-93cb-ee984bddde9b", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180765517900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1749f3a9-7ddf-4264-95df-f018aa3fdc43", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180765610700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec5ec19-30bb-4321-8184-485c7<PERSON><PERSON><PERSON>", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180765820500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30eb4dbe-6c8e-4538-a527-3b7e10ea9538", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180768182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f9b784-5830-43ac-b755-2e6cad1279b7", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180768851000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd71fc6-f75c-4f98-bbc7-81d623d2aeee", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180769312400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b6974ee-0230-4396-9281-0156f0e42a69", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180769672200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}