{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "d507365e-114f-4842-8f1b-bf642269c667", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313573766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea88554-d8db-46be-8125-1429edcfc2ce", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313593003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a75cf3-3830-4fbb-8ba4-f8f0bc4f4c16", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313606002000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "525db955-1197-408d-822f-c5dce7cf5d70", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313606250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c53cbe04-40c7-41e5-82a0-d4f49927e446", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333662781900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaede06a-b8df-4bbb-9a01-440be93f979e", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333669193100, "endTime": 32334391024600}, "additional": {"children": ["35a54b3e-4457-4035-adfa-88bd0b256109", "6c6709cc-7534-41fb-b41c-65ee33a73181", "b53ee623-20f0-4ba6-be70-42d4c449cb24", "0da7c55e-64e7-4ce9-a203-ee431589b72d", "d24ed837-1c68-4aa4-bc26-c237930f01eb", "e1000714-ae90-42b9-9fed-86b6552d5a53", "010588e1-f70b-4e80-93fd-fdfe56db9f7c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35a54b3e-4457-4035-adfa-88bd0b256109", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333669196500, "endTime": 32333683609900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "0e7ca716-1f93-4c91-bc73-1aa4055fa372"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c6709cc-7534-41fb-b41c-65ee33a73181", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333683631800, "endTime": 32334389216900}, "additional": {"children": ["cf525abb-6f56-4cff-bef2-6727f400d037", "d6bcb001-a77e-4471-9e95-38c44fe9c19e", "8c96284f-17d9-40fc-b976-8e6ca7ab2a8f", "092dcf73-f095-4cfa-b55d-cd70f5c0e00c", "9d2c039c-26fc-4164-85dc-c59aebd4d1d9", "1fd59dfe-4b60-4e73-af3b-084d661fbd7e", "18457933-1779-4df9-9087-837a10fc13da", "194ae6a2-7b5a-4622-92be-88feafe05eea", "dd32a30a-127b-4059-9d2b-3b8b10f38b62"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "95ee2d3b-929d-49ab-8c54-b138643a593b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b53ee623-20f0-4ba6-be70-42d4c449cb24", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334389235700, "endTime": 32334391013700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "870e250a-79dc-4e3b-b71f-a26b91c5ca3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0da7c55e-64e7-4ce9-a203-ee431589b72d", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334391019800, "endTime": 32334391021300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "e5d26c49-33e1-4c3f-84a4-4cfa5e1a232b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d24ed837-1c68-4aa4-bc26-c237930f01eb", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333673178100, "endTime": 32333673232000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "4ade77a9-48ca-4e75-a0e0-e5485cabeba6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ade77a9-48ca-4e75-a0e0-e5485cabeba6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333673178100, "endTime": 32333673232000}, "additional": {"logType": "info", "children": [], "durationId": "d24ed837-1c68-4aa4-bc26-c237930f01eb", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "e1000714-ae90-42b9-9fed-86b6552d5a53", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333677987200, "endTime": 32333678014800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "dda9a1f0-33ac-4a69-bd50-fe746c29cccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dda9a1f0-33ac-4a69-bd50-fe746c29cccf", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333677987200, "endTime": 32333678014800}, "additional": {"logType": "info", "children": [], "durationId": "e1000714-ae90-42b9-9fed-86b6552d5a53", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "aeac48d7-7656-4a78-9567-9d2de4c5e194", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333678119300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62b9526f-2d2b-4f2f-b9e8-780ef63bba96", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333683463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7ca716-1f93-4c91-bc73-1aa4055fa372", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333669196500, "endTime": 32333683609900}, "additional": {"logType": "info", "children": [], "durationId": "35a54b3e-4457-4035-adfa-88bd0b256109", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "cf525abb-6f56-4cff-bef2-6727f400d037", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333689166300, "endTime": 32333689174000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "4f75ea08-3633-4e47-b5c1-0078fd261004"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6bcb001-a77e-4471-9e95-38c44fe9c19e", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333689187500, "endTime": 32333693656400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "e98762da-52aa-4cd3-8b2f-7465ba644a8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c96284f-17d9-40fc-b976-8e6ca7ab2a8f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333693679700, "endTime": 32333912535300}, "additional": {"children": ["7c1ba4cc-2b84-4a32-8d41-ae627b847ea9", "61a4961f-e153-4f32-9929-c528d4454aaf", "7fc6278f-faa7-4910-8ad3-5e8f21287235"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "8d6dab06-63c5-42a7-832c-0c7c5d400981"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "092dcf73-f095-4cfa-b55d-cd70f5c0e00c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333912549000, "endTime": 32333990528600}, "additional": {"children": ["3534064a-386e-4994-8849-3496de0c1064"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "2da71063-7850-49f8-a15e-193b3ffb8d54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d2c039c-26fc-4164-85dc-c59aebd4d1d9", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333990534400, "endTime": 32334344374400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "662b42fe-0314-4947-b510-a1c2b589b84d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fd59dfe-4b60-4e73-af3b-084d661fbd7e", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334346955700, "endTime": 32334356911300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "0cb8c2d5-23b3-4c5c-9a7f-5500ded32e12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18457933-1779-4df9-9087-837a10fc13da", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334356934600, "endTime": 32334388971200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "de9c9608-5b4b-4cf4-b072-b0f3412d0fdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "194ae6a2-7b5a-4622-92be-88feafe05eea", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334388992900, "endTime": 32334389199900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "7fa3885a-94f1-4252-89d0-50d18ffc5c4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f75ea08-3633-4e47-b5c1-0078fd261004", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333689166300, "endTime": 32333689174000}, "additional": {"logType": "info", "children": [], "durationId": "cf525abb-6f56-4cff-bef2-6727f400d037", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "e98762da-52aa-4cd3-8b2f-7465ba644a8e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333689187500, "endTime": 32333693656400}, "additional": {"logType": "info", "children": [], "durationId": "d6bcb001-a77e-4471-9e95-38c44fe9c19e", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "7c1ba4cc-2b84-4a32-8d41-ae627b847ea9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333694373200, "endTime": 32333694395300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c96284f-17d9-40fc-b976-8e6ca7ab2a8f", "logId": "1836823e-8374-4d9b-acee-644c7bef2d9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1836823e-8374-4d9b-acee-644c7bef2d9a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333694373200, "endTime": 32333694395300}, "additional": {"logType": "info", "children": [], "durationId": "7c1ba4cc-2b84-4a32-8d41-ae627b847ea9", "parent": "8d6dab06-63c5-42a7-832c-0c7c5d400981"}}, {"head": {"id": "61a4961f-e153-4f32-9929-c528d4454aaf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333696948900, "endTime": 32333910819500}, "additional": {"children": ["8faee838-d10e-4fe1-aa2d-6e28d8a1db2e", "e63526b7-9d77-4155-a1f2-d72aecc78ab8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c96284f-17d9-40fc-b976-8e6ca7ab2a8f", "logId": "9030d4ed-0088-4376-957f-72986dfad301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8faee838-d10e-4fe1-aa2d-6e28d8a1db2e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333696951800, "endTime": 32333703767600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61a4961f-e153-4f32-9929-c528d4454aaf", "logId": "17a6021a-1b98-45e4-8303-560ec3c6cefa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e63526b7-9d77-4155-a1f2-d72aecc78ab8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333703817800, "endTime": 32333910804800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61a4961f-e153-4f32-9929-c528d4454aaf", "logId": "1f4015d6-1fbc-423a-ac5e-3d86b4dc054f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29435b87-5738-43d6-a6da-5a7437351038", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333696957500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c06ea94c-6647-49cb-99cb-c1e828335bf0", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333703282100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a6021a-1b98-45e4-8303-560ec3c6cefa", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333696951800, "endTime": 32333703767600}, "additional": {"logType": "info", "children": [], "durationId": "8faee838-d10e-4fe1-aa2d-6e28d8a1db2e", "parent": "9030d4ed-0088-4376-957f-72986dfad301"}}, {"head": {"id": "548a089c-e280-4fe6-bf3e-b3c6db2f92e4", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333703840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace55a56-8166-4455-9ac6-6a1dda4fdc38", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333711873600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7add1527-93ff-4414-be11-9e3485a40c76", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333711989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aaeaaa1-f81b-41d8-93f4-4105ff28f516", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333712232300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9a39273-d7b7-4c93-9bac-a5af647ecab1", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333712401700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc95b62d-d87c-4b06-aa73-d66a5cec20c8", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333714658000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cafe7f8e-e738-428e-845c-017651e59be4", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333718254800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "088e71cf-c680-4361-bc72-a376efe35baa", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333731702300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d96d29e-148c-45cb-8431-cb653587cbb2", "name": "Sdk init in 123 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333841718500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28b65a29-d35c-4b26-87a0-2f01d4cb6464", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333841897000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 28}, "markType": "other"}}, {"head": {"id": "8c050b20-76d4-4972-b2e1-86a41ff444bd", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333841913200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 28}, "markType": "other"}}, {"head": {"id": "9c10a897-c3fe-49e8-bac1-aa5607b469e9", "name": "Project task initialization takes 55 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333910440200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43af44e2-f66a-40f4-a6de-76631aab4548", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333910556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f933813-e7c5-4697-b5ea-4204cf4745d9", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333910612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d66d806-3e99-443b-938e-2f5586a0f115", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333910740200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4015d6-1fbc-423a-ac5e-3d86b4dc054f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333703817800, "endTime": 32333910804800}, "additional": {"logType": "info", "children": [], "durationId": "e63526b7-9d77-4155-a1f2-d72aecc78ab8", "parent": "9030d4ed-0088-4376-957f-72986dfad301"}}, {"head": {"id": "9030d4ed-0088-4376-957f-72986dfad301", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333696948900, "endTime": 32333910819500}, "additional": {"logType": "info", "children": ["17a6021a-1b98-45e4-8303-560ec3c6cefa", "1f4015d6-1fbc-423a-ac5e-3d86b4dc054f"], "durationId": "61a4961f-e153-4f32-9929-c528d4454aaf", "parent": "8d6dab06-63c5-42a7-832c-0c7c5d400981"}}, {"head": {"id": "7fc6278f-faa7-4910-8ad3-5e8f21287235", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333912495400, "endTime": 32333912516300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8c96284f-17d9-40fc-b976-8e6ca7ab2a8f", "logId": "4e1e4c5e-d112-48ca-a2d0-d7379dbbb5d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e1e4c5e-d112-48ca-a2d0-d7379dbbb5d8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333912495400, "endTime": 32333912516300}, "additional": {"logType": "info", "children": [], "durationId": "7fc6278f-faa7-4910-8ad3-5e8f21287235", "parent": "8d6dab06-63c5-42a7-832c-0c7c5d400981"}}, {"head": {"id": "8d6dab06-63c5-42a7-832c-0c7c5d400981", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333693679700, "endTime": 32333912535300}, "additional": {"logType": "info", "children": ["1836823e-8374-4d9b-acee-644c7bef2d9a", "9030d4ed-0088-4376-957f-72986dfad301", "4e1e4c5e-d112-48ca-a2d0-d7379dbbb5d8"], "durationId": "8c96284f-17d9-40fc-b976-8e6ca7ab2a8f", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "3534064a-386e-4994-8849-3496de0c1064", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333914028600, "endTime": 32333990519000}, "additional": {"children": ["1952c932-2480-4f69-8df5-9de4d7f32b1a", "477cb65e-ae5b-49c8-b650-9b834cba860c", "2609a0a7-1943-4b98-a31c-b8699936d674"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "092dcf73-f095-4cfa-b55d-cd70f5c0e00c", "logId": "8eb136ac-b826-4d0f-bcf4-78a8a02dfa83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1952c932-2480-4f69-8df5-9de4d7f32b1a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333920058400, "endTime": 32333920093700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3534064a-386e-4994-8849-3496de0c1064", "logId": "fbbd9ee4-16c3-4a94-8834-945f31269434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbbd9ee4-16c3-4a94-8834-945f31269434", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333920058400, "endTime": 32333920093700}, "additional": {"logType": "info", "children": [], "durationId": "1952c932-2480-4f69-8df5-9de4d7f32b1a", "parent": "8eb136ac-b826-4d0f-bcf4-78a8a02dfa83"}}, {"head": {"id": "477cb65e-ae5b-49c8-b650-9b834cba860c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333925420800, "endTime": 32333985768600}, "additional": {"children": ["6f720837-b7fa-4034-b1fb-fb669a7f5d6c", "d5d965ce-241d-4ccb-b418-654d3f5b58bd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3534064a-386e-4994-8849-3496de0c1064", "logId": "209f236d-df0e-403b-9ff0-f26d59358b72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f720837-b7fa-4034-b1fb-fb669a7f5d6c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333925423100, "endTime": 32333930781300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "477cb65e-ae5b-49c8-b650-9b834cba860c", "logId": "2dc1b9fd-4d25-4938-b5e0-8e8a99f52fbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5d965ce-241d-4ccb-b418-654d3f5b58bd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333930800600, "endTime": 32333985748500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "477cb65e-ae5b-49c8-b650-9b834cba860c", "logId": "2f6b6f57-21d3-46f3-81b1-25b7b5191440"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac37bdeb-e456-40c6-aab8-122ff703a4d7", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333925428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a010ed-14bf-480c-869d-25c109763058", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333930587300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc1b9fd-4d25-4938-b5e0-8e8a99f52fbb", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333925423100, "endTime": 32333930781300}, "additional": {"logType": "info", "children": [], "durationId": "6f720837-b7fa-4034-b1fb-fb669a7f5d6c", "parent": "209f236d-df0e-403b-9ff0-f26d59358b72"}}, {"head": {"id": "829a65d2-7fe8-48a7-8252-6cb4f4a113dd", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333931113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a1acaf-1144-4ecd-af84-81d8d3c4d364", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333951205000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7fcaa1-f16d-447b-96c5-b6594f279d59", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333951486500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfe141c-d2b0-495c-a12b-8d31c0077c8e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333952000400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a22ba7-2d99-42d0-905a-60261c322fd8", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333954985500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296dfb93-e751-45c9-974d-4a9c952f9e06", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333955374100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a77b0acb-ba49-4699-9237-cc6da2e4048a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333955464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b10a5d1-e9fd-4dd5-949d-9df6fadf16de", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333970557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c3b4f8-26ab-40ed-9810-e63d929c4b38", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333985103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dcd1504-82d4-4b7a-a756-424e18e6e9df", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333985449300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ac781d-d15c-42bc-a1e1-d2b87dff6d16", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333985557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6edace19-466b-4526-8c9f-ba3a45acd360", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333985686700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f6b6f57-21d3-46f3-81b1-25b7b5191440", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333930800600, "endTime": 32333985748500}, "additional": {"logType": "info", "children": [], "durationId": "d5d965ce-241d-4ccb-b418-654d3f5b58bd", "parent": "209f236d-df0e-403b-9ff0-f26d59358b72"}}, {"head": {"id": "209f236d-df0e-403b-9ff0-f26d59358b72", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333925420800, "endTime": 32333985768600}, "additional": {"logType": "info", "children": ["2dc1b9fd-4d25-4938-b5e0-8e8a99f52fbb", "2f6b6f57-21d3-46f3-81b1-25b7b5191440"], "durationId": "477cb65e-ae5b-49c8-b650-9b834cba860c", "parent": "8eb136ac-b826-4d0f-bcf4-78a8a02dfa83"}}, {"head": {"id": "2609a0a7-1943-4b98-a31c-b8699936d674", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333990482000, "endTime": 32333990503600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3534064a-386e-4994-8849-3496de0c1064", "logId": "7846adc3-1c75-4108-9e84-89539259e922"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7846adc3-1c75-4108-9e84-89539259e922", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333990482000, "endTime": 32333990503600}, "additional": {"logType": "info", "children": [], "durationId": "2609a0a7-1943-4b98-a31c-b8699936d674", "parent": "8eb136ac-b826-4d0f-bcf4-78a8a02dfa83"}}, {"head": {"id": "8eb136ac-b826-4d0f-bcf4-78a8a02dfa83", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333914028600, "endTime": 32333990519000}, "additional": {"logType": "info", "children": ["fbbd9ee4-16c3-4a94-8834-945f31269434", "209f236d-df0e-403b-9ff0-f26d59358b72", "7846adc3-1c75-4108-9e84-89539259e922"], "durationId": "3534064a-386e-4994-8849-3496de0c1064", "parent": "2da71063-7850-49f8-a15e-193b3ffb8d54"}}, {"head": {"id": "2da71063-7850-49f8-a15e-193b3ffb8d54", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333912549000, "endTime": 32333990528600}, "additional": {"logType": "info", "children": ["8eb136ac-b826-4d0f-bcf4-78a8a02dfa83"], "durationId": "092dcf73-f095-4cfa-b55d-cd70f5c0e00c", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "bec6121c-3c85-4d9d-9659-d3d5b305cafe", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334069015100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e065870-cf9c-46f2-add1-433bfa1e38e0", "name": "hvigorfile, resolve hvigorfile dependencies in 354 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334344008600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "662b42fe-0314-4947-b510-a1c2b589b84d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333990534400, "endTime": 32334344374400}, "additional": {"logType": "info", "children": [], "durationId": "9d2c039c-26fc-4164-85dc-c59aebd4d1d9", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "dd32a30a-127b-4059-9d2b-3b8b10f38b62", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334345283300, "endTime": 32334346937000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6c6709cc-7534-41fb-b41c-65ee33a73181", "logId": "06133e0f-fae5-439c-9e67-d77da00b7210"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d749a05e-30c1-4e1a-9d6f-b0ee9aee29ad", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334345311200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06133e0f-fae5-439c-9e67-d77da00b7210", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334345283300, "endTime": 32334346937000}, "additional": {"logType": "info", "children": [], "durationId": "dd32a30a-127b-4059-9d2b-3b8b10f38b62", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "e2837f1e-6193-42b1-baf3-53dc7c6e28d5", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334348831700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76357fd0-25da-4753-a1b5-b741062a7a15", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334356279500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb8c2d5-23b3-4c5c-9a7f-5500ded32e12", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334346955700, "endTime": 32334356911300}, "additional": {"logType": "info", "children": [], "durationId": "1fd59dfe-4b60-4e73-af3b-084d661fbd7e", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "da9dcf43-6029-459e-818c-0244d1063ec4", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334369237600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029015c2-1e65-4a0e-b606-632e2419b552", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334369396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e8a25ab-0c5b-4fb3-bb72-961b916ef17d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334382853600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1dba6f0-ac58-4f83-8731-1801a7d8ffa0", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334383726300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9c9608-5b4b-4cf4-b072-b0f3412d0fdc", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334356934600, "endTime": 32334388971200}, "additional": {"logType": "info", "children": [], "durationId": "18457933-1779-4df9-9087-837a10fc13da", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "b88e1bfe-13d8-4236-86f0-9536039fe802", "name": "Configuration phase cost:700 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334389014000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa3885a-94f1-4252-89d0-50d18ffc5c4c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334388992900, "endTime": 32334389199900}, "additional": {"logType": "info", "children": [], "durationId": "194ae6a2-7b5a-4622-92be-88feafe05eea", "parent": "95ee2d3b-929d-49ab-8c54-b138643a593b"}}, {"head": {"id": "95ee2d3b-929d-49ab-8c54-b138643a593b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333683631800, "endTime": 32334389216900}, "additional": {"logType": "info", "children": ["4f75ea08-3633-4e47-b5c1-0078fd261004", "e98762da-52aa-4cd3-8b2f-7465ba644a8e", "8d6dab06-63c5-42a7-832c-0c7c5d400981", "2da71063-7850-49f8-a15e-193b3ffb8d54", "662b42fe-0314-4947-b510-a1c2b589b84d", "0cb8c2d5-23b3-4c5c-9a7f-5500ded32e12", "de9c9608-5b4b-4cf4-b072-b0f3412d0fdc", "7fa3885a-94f1-4252-89d0-50d18ffc5c4c", "06133e0f-fae5-439c-9e67-d77da00b7210"], "durationId": "6c6709cc-7534-41fb-b41c-65ee33a73181", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "010588e1-f70b-4e80-93fd-fdfe56db9f7c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334390894200, "endTime": 32334390916400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aaede06a-b8df-4bbb-9a01-440be93f979e", "logId": "49ccd27a-1c80-4b19-af9d-5242e3f27f3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49ccd27a-1c80-4b19-af9d-5242e3f27f3f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334390894200, "endTime": 32334390916400}, "additional": {"logType": "info", "children": [], "durationId": "010588e1-f70b-4e80-93fd-fdfe56db9f7c", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "870e250a-79dc-4e3b-b71f-a26b91c5ca3c", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334389235700, "endTime": 32334391013700}, "additional": {"logType": "info", "children": [], "durationId": "b53ee623-20f0-4ba6-be70-42d4c449cb24", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "e5d26c49-33e1-4c3f-84a4-4cfa5e1a232b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334391019800, "endTime": 32334391021300}, "additional": {"logType": "info", "children": [], "durationId": "0da7c55e-64e7-4ce9-a203-ee431589b72d", "parent": "dcbdd1ce-4466-47b7-9334-38870ae78acf"}}, {"head": {"id": "dcbdd1ce-4466-47b7-9334-38870ae78acf", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333669193100, "endTime": 32334391024600}, "additional": {"logType": "info", "children": ["0e7ca716-1f93-4c91-bc73-1aa4055fa372", "95ee2d3b-929d-49ab-8c54-b138643a593b", "870e250a-79dc-4e3b-b71f-a26b91c5ca3c", "e5d26c49-33e1-4c3f-84a4-4cfa5e1a232b", "4ade77a9-48ca-4e75-a0e0-e5485cabeba6", "dda9a1f0-33ac-4a69-bd50-fe746c29cccf", "49ccd27a-1c80-4b19-af9d-5242e3f27f3f"], "durationId": "aaede06a-b8df-4bbb-9a01-440be93f979e"}}, {"head": {"id": "1c694c5a-135f-48f6-8e52-999b1e0dd06a", "name": "Configuration task cost before running: 726 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334391409200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f3d782a-27bc-4a8e-bf43-2bba573e76b9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334400887300, "endTime": 32334410031600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a69055d9-1c4b-4c91-843c-042454eda94e", "logId": "ad492548-abaf-44ba-aa25-061894f624c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a69055d9-1c4b-4c91-843c-042454eda94e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334393552800}, "additional": {"logType": "detail", "children": [], "durationId": "7f3d782a-27bc-4a8e-bf43-2bba573e76b9"}}, {"head": {"id": "dbbb0061-7954-4f9b-8ff0-3de6f06b1eb5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334394622100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbdac832-446c-41e9-8c2c-bf42da232c88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334394753500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47a0aa1-2db7-45aa-a20e-2715d6efdc50", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334400900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caf13f93-69c1-49a8-ac9f-708a128f184c", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334409781600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb1f078e-9951-45e8-993a-c4076f076433", "name": "entry : default@PreBuild cost memory 0.31058502197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334409938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad492548-abaf-44ba-aa25-061894f624c1", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334400887300, "endTime": 32334410031600}, "additional": {"logType": "info", "children": [], "durationId": "7f3d782a-27bc-4a8e-bf43-2bba573e76b9"}}, {"head": {"id": "daba13c0-a202-4a98-95dd-d00b7024c2b7", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334418894700, "endTime": 32334422105100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f9538162-47d3-40bf-bb18-2ab3846bf880", "logId": "856e39d9-7484-46fb-b511-ba4db062ffbc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9538162-47d3-40bf-bb18-2ab3846bf880", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334414765400}, "additional": {"logType": "detail", "children": [], "durationId": "daba13c0-a202-4a98-95dd-d00b7024c2b7"}}, {"head": {"id": "309d0f4b-a775-4847-b046-f18ee0784bd4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334415281300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad09d9c6-911f-469f-91c6-d7dbc21e1a0b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334416978700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "063e028a-9ded-49b1-adf0-822f0978d969", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334418919500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce559458-93a1-417f-8351-bc96b9765d81", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334420400400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67b0b55b-8818-47f4-af92-82411f02b0f8", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334421792700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9051c8b2-e08e-4a20-9b3c-6a868059e234", "name": "entry : default@GenerateMetadata cost memory 0.09297943115234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334422014000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856e39d9-7484-46fb-b511-ba4db062ffbc", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334418894700, "endTime": 32334422105100}, "additional": {"logType": "info", "children": [], "durationId": "daba13c0-a202-4a98-95dd-d00b7024c2b7"}}, {"head": {"id": "efb81d51-b662-4332-89e5-6879636e4b00", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425202000, "endTime": 32334425772400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "92e17889-966e-4a00-ac9f-136d21997a39", "logId": "71240d0c-4043-42fa-89e2-90336cf8044b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92e17889-966e-4a00-ac9f-136d21997a39", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334424481000}, "additional": {"logType": "detail", "children": [], "durationId": "efb81d51-b662-4332-89e5-6879636e4b00"}}, {"head": {"id": "f95ec1d0-9c52-4645-bdcd-16f243ad150d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334424928200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0691853b-2576-4c1b-af13-18b8d22d41fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425046000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "596b5aa6-5ae8-4e3f-af84-928f4968a646", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fdb4b98-1483-4949-ad90-cf7ea1cf3e41", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425349400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ad93ebd-c554-4e46-ac83-2b2184ec45f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425429700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a922777d-93f8-4812-a0c7-1faf1e8f8280", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425553900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a2e4fa-6583-416f-a51c-9931e457c1f7", "name": "runTaskFromQueue task cost before running: 760 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425670600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71240d0c-4043-42fa-89e2-90336cf8044b", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334425202000, "endTime": 32334425772400, "totalTime": 438500}, "additional": {"logType": "info", "children": [], "durationId": "efb81d51-b662-4332-89e5-6879636e4b00"}}, {"head": {"id": "da5894e2-1fb7-42e1-b6f9-2eadd5537389", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334430039600, "endTime": 32334432030700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "cc362159-7d32-4489-a8de-b44fa74e6fa4", "logId": "3faa86a1-0219-4b46-849b-dd9f670836f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc362159-7d32-4489-a8de-b44fa74e6fa4", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334428163800}, "additional": {"logType": "detail", "children": [], "durationId": "da5894e2-1fb7-42e1-b6f9-2eadd5537389"}}, {"head": {"id": "908aebe3-cfb3-4afa-b63a-afc7f7900ef2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334428554800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224726d2-9fec-412c-ab98-8d4b5f50da32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334428713900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a22f6f7-5aba-444d-ad7c-e5ef19329e30", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334430051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e659089f-08ad-42f2-9e51-1c01db35b2ba", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334431749800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c3ca47-8a23-4692-b5b6-d6e9f71269c7", "name": "entry : default@MergeProfile cost memory 0.10483551025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334431905400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3faa86a1-0219-4b46-849b-dd9f670836f5", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334430039600, "endTime": 32334432030700}, "additional": {"logType": "info", "children": [], "durationId": "da5894e2-1fb7-42e1-b6f9-2eadd5537389"}}, {"head": {"id": "ed4b74de-44d5-4c8b-9764-17fe004d1d11", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334435446600, "endTime": 32334437420100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dc8e0744-c350-4cb1-a215-e1ac06494819", "logId": "0f309ec9-7250-427a-a75f-67d3082677a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc8e0744-c350-4cb1-a215-e1ac06494819", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334434197400}, "additional": {"logType": "detail", "children": [], "durationId": "ed4b74de-44d5-4c8b-9764-17fe004d1d11"}}, {"head": {"id": "ac0733e7-7ee2-460f-9a03-09c4cd37892a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334434593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7919fcd-dc06-4f45-8af0-cb7ea7bc8c0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334434732000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e93a87e2-e75b-4508-9062-a933801f4b17", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334435457800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75694a49-f420-4c6e-9c1e-d45062939f15", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334436099700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db6e134-8b3e-40d0-af4e-58be3261925f", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334437225900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df6a9643-cb21-4ee1-a4e3-782e196ff829", "name": "entry : default@CreateBuildProfile cost memory 0.10101318359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334437344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f309ec9-7250-427a-a75f-67d3082677a3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334435446600, "endTime": 32334437420100}, "additional": {"logType": "info", "children": [], "durationId": "ed4b74de-44d5-4c8b-9764-17fe004d1d11"}}, {"head": {"id": "24f6d8de-dc46-416b-8d1a-ee4b5059fc83", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441408600, "endTime": 32334441819000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f6142ce9-138c-4366-bc32-7feb1e891daa", "logId": "4bacb3b5-0006-4966-9e51-cb3dd70d3e0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6142ce9-138c-4366-bc32-7feb1e891daa", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334440237100}, "additional": {"logType": "detail", "children": [], "durationId": "24f6d8de-dc46-416b-8d1a-ee4b5059fc83"}}, {"head": {"id": "0730c7ce-0afd-45a6-ae91-161442fd7cfb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334440582600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8863cd8c-945c-4f04-9335-4d20b0a896e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334440692100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19458e2d-3696-4f1f-9176-bf8753823042", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441420300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be5aa2da-94c4-4d11-a552-87252e2a649c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441542800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b32156-9159-4a4e-a9a6-85c3e7e8c6d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441611900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3a390dc-f7e9-4bc6-ba4a-7959d2ece5b7", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441687900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "840a8d71-3e24-47d0-9d09-639ad0c0b5b8", "name": "runTaskFromQueue task cost before running: 776 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441762200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bacb3b5-0006-4966-9e51-cb3dd70d3e0c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334441408600, "endTime": 32334441819000, "totalTime": 337500}, "additional": {"logType": "info", "children": [], "durationId": "24f6d8de-dc46-416b-8d1a-ee4b5059fc83"}}, {"head": {"id": "5395c3b8-f84c-43a0-842e-40a0a458bc2c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334448935200, "endTime": 32334449705600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1f045914-3f57-48ef-b2b1-250f1c6aa489", "logId": "e3e9d330-a06b-4593-8622-99a098f82ed7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f045914-3f57-48ef-b2b1-250f1c6aa489", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334443682200}, "additional": {"logType": "detail", "children": [], "durationId": "5395c3b8-f84c-43a0-842e-40a0a458bc2c"}}, {"head": {"id": "65acda41-7164-44f9-a733-d103fa7fa186", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334444116100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f3f0c2-9043-4191-9909-73961de178bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334444228700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b5c935-5661-4f4f-8dfb-430578fb3133", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334448946300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115ea0df-a67b-49ef-96d5-984ed7be8bd6", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334449273100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "657d3056-5543-4e08-bae2-015ccfde6190", "name": "entry : default@GeneratePkgContextInfo cost memory 0.14545440673828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334449532600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "769c6769-a6cf-4937-8ec3-5019ae203367", "name": "runTaskFromQueue task cost before running: 784 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334449638300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e9d330-a06b-4593-8622-99a098f82ed7", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334448935200, "endTime": 32334449705600, "totalTime": 671400}, "additional": {"logType": "info", "children": [], "durationId": "5395c3b8-f84c-43a0-842e-40a0a458bc2c"}}, {"head": {"id": "a75c9f46-5b12-4cdc-820d-3706b5bfcb31", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334457895500, "endTime": 32334465802900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "4767c009-ace0-45d3-9128-2dd08c2863d9", "logId": "991891b7-9413-46c5-ae1b-d62ceb041a18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4767c009-ace0-45d3-9128-2dd08c2863d9", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334452023400}, "additional": {"logType": "detail", "children": [], "durationId": "a75c9f46-5b12-4cdc-820d-3706b5bfcb31"}}, {"head": {"id": "68f9ff12-1a1e-432d-a28d-a3af7c801b87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334452606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4febed3-c193-4b69-85d5-a3208c896b3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334452818100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031d74cb-d16c-4d85-895b-042ff69b0846", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334457907700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d984979e-6ef1-4494-b5f0-0ba3a5638e87", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334459932100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff76d70c-877b-465c-8b35-2d1d405f0c25", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334460089300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e458b1da-5df1-4354-835e-27133c814d12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334464674900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a61f452-5424-41f3-aab6-5f6fea455196", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334464964300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a543f0-0978-4839-a026-93431546b4a6", "name": "entry : default@ProcessIntegratedHsp cost memory -4.7309112548828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334465241600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0cc16c-152a-4803-a0a8-7af500ae03f2", "name": "runTaskFromQueue task cost before running: 800 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334465574300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "991891b7-9413-46c5-ae1b-d62ceb041a18", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334457895500, "endTime": 32334465802900, "totalTime": 7544700}, "additional": {"logType": "info", "children": [], "durationId": "a75c9f46-5b12-4cdc-820d-3706b5bfcb31"}}, {"head": {"id": "d2d70ec7-7f5f-4aee-a145-47274b3a42c0", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470513100, "endTime": 32334470940800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7135fa08-c086-40b4-9836-11c02c2faa71", "logId": "0aa66d88-5168-4d85-a512-07069920d27e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7135fa08-c086-40b4-9836-11c02c2faa71", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334468961100}, "additional": {"logType": "detail", "children": [], "durationId": "d2d70ec7-7f5f-4aee-a145-47274b3a42c0"}}, {"head": {"id": "e1e9fed7-af1e-47a5-90f6-5f8aac15b49e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334469529000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b618328d-6f7d-48b4-a8d4-1c0446491178", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334469658300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a338955-a3e1-49d3-809d-19afad7a7a5c", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470524900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e036898-15b8-4fa8-9c3c-faed1c74f2eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470678000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c983a0-5c84-4f1d-ae47-b6f8fe1c2a17", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470738400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b52010-3e41-4f28-b411-2fc2d5a6cdc2", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470819000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61c0c22-1908-4b65-84bf-5fbf369a3c12", "name": "runTaskFromQueue task cost before running: 806 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470888700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa66d88-5168-4d85-a512-07069920d27e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334470513100, "endTime": 32334470940800, "totalTime": 360200}, "additional": {"logType": "info", "children": [], "durationId": "d2d70ec7-7f5f-4aee-a145-47274b3a42c0"}}, {"head": {"id": "cc1f51ca-8f2d-439e-a139-13f57bc97ee9", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334474504100, "endTime": 32334478759800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "715187f3-38f2-412e-9b29-ee5f0b7679ff", "logId": "211491a6-d00f-4a47-b560-de229de7dc87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "715187f3-38f2-412e-9b29-ee5f0b7679ff", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334472829400}, "additional": {"logType": "detail", "children": [], "durationId": "cc1f51ca-8f2d-439e-a139-13f57bc97ee9"}}, {"head": {"id": "e577391e-9a03-4e05-8827-9bcbc3a662c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334473390500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fa76e8c-5b6f-4185-9556-3d01cf6ff8fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334473524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac8c50e-5a0e-4775-818d-2a954246d557", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334474514800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90198e66-3449-44cb-a432-c5d94ae715bb", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334478512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33d6b6b-c827-4dd9-a53f-5b788d32fafb", "name": "entry : default@MakePackInfo cost memory 0.1545562744140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334478673600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211491a6-d00f-4a47-b560-de229de7dc87", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334474504100, "endTime": 32334478759800}, "additional": {"logType": "info", "children": [], "durationId": "cc1f51ca-8f2d-439e-a139-13f57bc97ee9"}}, {"head": {"id": "afc2ce2a-bea5-42d7-8bdd-0e805fbf7fef", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334488379100, "endTime": 32334492090000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "781ec513-2ef5-4f4c-92cc-b3718df91a7e", "logId": "62048021-7a7e-425f-8175-bf9bb2726429"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "781ec513-2ef5-4f4c-92cc-b3718df91a7e", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334485739100}, "additional": {"logType": "detail", "children": [], "durationId": "afc2ce2a-bea5-42d7-8bdd-0e805fbf7fef"}}, {"head": {"id": "9548db8e-0574-416d-b2b7-c7c7b4e27ec7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334486445000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7168f926-27bb-4c6c-a2d1-266f15c58f14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334486586200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f7374a-c09d-4360-8c42-8e986c5fab85", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334488399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed4109ea-4085-49c5-b077-a9c03b05a0b6", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334488581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92132660-7d71-4693-b7ea-e9d5192491be", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334489609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2877e131-8834-4707-8fd4-84977201a8a6", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334491491100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43857ae7-64cb-4745-bcc4-1fcd14f107eb", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334491705100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6531365-77cb-4619-8014-e2738922005b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334491819400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e580b147-70bd-4a71-8d95-8f48de57360e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334491877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3f24ef4-4374-43b3-9800-40e87b468154", "name": "entry : default@SyscapTransform cost memory 0.15171051025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334491950800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52de29c4-b8c8-427d-80e3-51acfed83f92", "name": "runTaskFromQueue task cost before running: 827 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334492037600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62048021-7a7e-425f-8175-bf9bb2726429", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334488379100, "endTime": 32334492090000, "totalTime": 3644800}, "additional": {"logType": "info", "children": [], "durationId": "afc2ce2a-bea5-42d7-8bdd-0e805fbf7fef"}}, {"head": {"id": "8f9babf3-16a2-4053-a627-da5f303362ba", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334496534900, "endTime": 32334499376400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "840e6d67-6cb3-42db-9937-f772c30f1463", "logId": "80d6c376-d55e-4dff-beef-7858c3d7d173"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "840e6d67-6cb3-42db-9937-f772c30f1463", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334494259700}, "additional": {"logType": "detail", "children": [], "durationId": "8f9babf3-16a2-4053-a627-da5f303362ba"}}, {"head": {"id": "bcde0b45-6a24-4042-aeef-5fd037bc0326", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334494661800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571224ea-dbe6-40bd-b083-3b2b143d5fef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334494792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c164b409-e62b-4956-91de-917ff6b49a74", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334496548300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efeeef38-026d-4448-9791-624668fe7c97", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334499031700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f00d62-4dde-432f-8507-2b9a60a0ac62", "name": "entry : default@ProcessProfile cost memory 0.06029510498046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334499284000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d6c376-d55e-4dff-beef-7858c3d7d173", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334496534900, "endTime": 32334499376400}, "additional": {"logType": "info", "children": [], "durationId": "8f9babf3-16a2-4053-a627-da5f303362ba"}}, {"head": {"id": "277e5e19-abc2-4fcd-87af-f10234eed637", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334507313000, "endTime": 32334514505700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "749de52e-d877-4ef6-89fa-48d488f70f2e", "logId": "f148e8fe-06da-4fa9-b6ec-e50b8bed78ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "749de52e-d877-4ef6-89fa-48d488f70f2e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334501981700}, "additional": {"logType": "detail", "children": [], "durationId": "277e5e19-abc2-4fcd-87af-f10234eed637"}}, {"head": {"id": "3c0836f8-e081-496b-96a4-74f9802c6db7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334502922600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db3a63b-d805-4c5b-a602-e785f3a556ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334503203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e17b90b-866a-40bb-9358-a70b393bf6ab", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334507324000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "871bee3b-722d-4a72-bdc9-e99e5463056b", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334513453700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4889d25e-f212-4cc7-ab01-e9ecff86e047", "name": "entry : default@ProcessRouterMap cost memory 0.20147705078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334513613600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f148e8fe-06da-4fa9-b6ec-e50b8bed78ff", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334507313000, "endTime": 32334514505700}, "additional": {"logType": "info", "children": [], "durationId": "277e5e19-abc2-4fcd-87af-f10234eed637"}}, {"head": {"id": "27cd05fc-ffa4-43fe-8644-92ff8ae8012c", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334556696100, "endTime": 32334560173800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "813de491-1edd-4b1f-8a84-4b99a10fcbbc", "logId": "0fade0d4-cf53-43d0-9b41-35e334c31257"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "813de491-1edd-4b1f-8a84-4b99a10fcbbc", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334554640800}, "additional": {"logType": "detail", "children": [], "durationId": "27cd05fc-ffa4-43fe-8644-92ff8ae8012c"}}, {"head": {"id": "140da527-1845-4119-a974-5fb1714ba3f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334555110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08dd1bb4-070d-4522-a121-3c341e38272f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334555318000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e3d1585-83ed-45bd-ba0e-23df546b8f08", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334556710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e0a40e-6828-4eb3-a003-c8ec33539ce7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334557018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f64a2b05-fe66-4bc7-8835-9e5c83c4f25a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334557225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bdea64c-8650-4e9a-a4b6-a369ac43d95c", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334559866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1c5a29-5548-4d04-b2d6-fd2ffbbdde56", "name": "runTaskFromQueue task cost before running: 895 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334560084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fade0d4-cf53-43d0-9b41-35e334c31257", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334556696100, "endTime": 32334560173800, "totalTime": 3359600}, "additional": {"logType": "info", "children": [], "durationId": "27cd05fc-ffa4-43fe-8644-92ff8ae8012c"}}, {"head": {"id": "a3e3846f-c1cc-4208-aa19-dc5947ff75f4", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334574511300, "endTime": 32334584868500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9948ed25-72b8-4bbc-86b3-f56b24d695c6", "logId": "9993caee-e5f8-4045-930d-c8eda9999691"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9948ed25-72b8-4bbc-86b3-f56b24d695c6", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334567452700}, "additional": {"logType": "detail", "children": [], "durationId": "a3e3846f-c1cc-4208-aa19-dc5947ff75f4"}}, {"head": {"id": "4d651066-bb44-4cfa-b383-93c6cee67973", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334567839500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c2e1e51-b064-4dae-b6f2-3c8809356c5c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334568215400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2474bc-1585-4505-a6aa-75f8cc3dc2fb", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334572804000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08c1228f-e098-45c2-bab5-0da8862fb3f2", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334577023100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36efb2b7-5b54-4507-8ddc-122bc571323f", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334580965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4498bfe-d896-4940-a796-cd1c2e8873a8", "name": "entry : default@ProcessResource cost memory 0.16942596435546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334581418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9993caee-e5f8-4045-930d-c8eda9999691", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334574511300, "endTime": 32334584868500}, "additional": {"logType": "info", "children": [], "durationId": "a3e3846f-c1cc-4208-aa19-dc5947ff75f4"}}, {"head": {"id": "0b12766e-7f4b-4504-9179-9759360a1c56", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334599993100, "endTime": 32334636853100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "32775d41-a1d1-4920-a834-748eb1354637", "logId": "fd0beaed-4a5d-41f1-b8c1-7eb6bc480122"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32775d41-a1d1-4920-a834-748eb1354637", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334594282400}, "additional": {"logType": "detail", "children": [], "durationId": "0b12766e-7f4b-4504-9179-9759360a1c56"}}, {"head": {"id": "20a759a8-2278-4da6-b838-b8dfcf42d139", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334594683700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45cdbae3-c0da-4ea1-a09e-08e234bfea46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334594806100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5979830-ff9e-48dc-9459-b9e9fa11b596", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334600005600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5a96024-823e-4f9b-8614-05c8d40c8e5e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334636575200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4bbb4c9-534b-4abf-8848-88480777ff16", "name": "entry : default@GenerateLoaderJson cost memory 0.7676467895507812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334636771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0beaed-4a5d-41f1-b8c1-7eb6bc480122", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334599993100, "endTime": 32334636853100}, "additional": {"logType": "info", "children": [], "durationId": "0b12766e-7f4b-4504-9179-9759360a1c56"}}, {"head": {"id": "ec909e0d-ff1f-4e4a-9022-c6dc3bc3a6a1", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334648996100, "endTime": 32334653175900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e3e45860-89c4-4eb6-b222-d89fd65280f8", "logId": "2e0f8cab-75bc-4f43-a4c7-b9e5ad86207b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3e45860-89c4-4eb6-b222-d89fd65280f8", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334645598000}, "additional": {"logType": "detail", "children": [], "durationId": "ec909e0d-ff1f-4e4a-9022-c6dc3bc3a6a1"}}, {"head": {"id": "3b2358cf-6945-4438-b767-ff9a8baa8eb5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334647801500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf63c427-2a8f-4c1b-8240-026ccbf14c36", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334647932600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e34258-ba2d-4bb0-8a66-220de972ee89", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334649012300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d552708c-96f7-4541-ace0-b7e8eed90ea7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334651741300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "681ac213-79e7-4989-8f7d-93ba5ed43baf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334651862800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93e9ea6d-d454-40e1-8472-86391eb39065", "name": "entry : default@ProcessLibs cost memory 0.12572479248046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334652787000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4145c962-87be-4cdf-9cf8-c875b6567e9c", "name": "runTaskFromQueue task cost before running: 988 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334653043900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0f8cab-75bc-4f43-a4c7-b9e5ad86207b", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334648996100, "endTime": 32334653175900, "totalTime": 3987100}, "additional": {"logType": "info", "children": [], "durationId": "ec909e0d-ff1f-4e4a-9022-c6dc3bc3a6a1"}}, {"head": {"id": "74e93087-4c5f-4f9d-a7cb-e5f0b095701f", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334663717200, "endTime": 32334690794100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0411b708-314b-4ac7-a314-241e88c47cd3", "logId": "780e3750-5775-4260-8d5a-f24d5867257f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0411b708-314b-4ac7-a314-241e88c47cd3", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334658505200}, "additional": {"logType": "detail", "children": [], "durationId": "74e93087-4c5f-4f9d-a7cb-e5f0b095701f"}}, {"head": {"id": "502636f6-6e04-43c0-8c74-4896d7080089", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334658969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ea1ed6-8dda-4101-80cb-7605ffa18ad1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334659148300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac92660c-6ff2-4218-aa3b-8e062c327581", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334660223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7087b318-b0bf-4423-a2ef-b00f31303d0d", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334663757300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dab9d30c-290a-4fb8-8150-e529806b874f", "name": "Incremental task entry:default@CompileResource pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334690566300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e038b4b-c285-441b-9234-38ca0a13385d", "name": "entry : default@CompileResource cost memory -4.463600158691406", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334690712200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780e3750-5775-4260-8d5a-f24d5867257f", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334663717200, "endTime": 32334690794100}, "additional": {"logType": "info", "children": [], "durationId": "74e93087-4c5f-4f9d-a7cb-e5f0b095701f"}}, {"head": {"id": "ac932c79-74f0-4184-8149-4418217484bb", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334695517100, "endTime": 32334697275700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2b31add7-99de-4669-af52-21972fbcfa3e", "logId": "2da25f2d-ac4b-489c-9a18-2cc5fa878bba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b31add7-99de-4669-af52-21972fbcfa3e", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334692970200}, "additional": {"logType": "detail", "children": [], "durationId": "ac932c79-74f0-4184-8149-4418217484bb"}}, {"head": {"id": "0952263c-3044-4316-9dac-49858d37faf2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334693380800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9841a42a-1dd2-4de4-a39e-370d62d77027", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334693485400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf439cb-4a82-4c9e-8a1f-6db0f375fe48", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334695528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0268f6be-8a48-4391-a468-d005ae87289c", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334695803200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9981217f-5d5a-445c-853c-3390b174b649", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334697064400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3f8b0a8-5f23-44a9-96a2-6c0ba33dee88", "name": "entry : default@DoNativeStrip cost memory 0.075164794921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334697206100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2da25f2d-ac4b-489c-9a18-2cc5fa878bba", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334695517100, "endTime": 32334697275700}, "additional": {"logType": "info", "children": [], "durationId": "ac932c79-74f0-4184-8149-4418217484bb"}}, {"head": {"id": "0319aa29-fe5f-4461-adc6-dde5c92becac", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334702871700, "endTime": 32336595442600}, "additional": {"children": ["3b25a5a7-c7b7-4960-8498-98523432d2a4", "09fc9b90-f982-4e2c-996f-30f7717919b2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "51d31523-ae76-45a0-b395-04a661d98afa", "logId": "7bb37cf5-4445-4dd3-b704-7a89e711463f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51d31523-ae76-45a0-b395-04a661d98afa", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334698903400}, "additional": {"logType": "detail", "children": [], "durationId": "0319aa29-fe5f-4461-adc6-dde5c92becac"}}, {"head": {"id": "7761968e-4d61-4b4b-9a42-be11a2a496a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334699308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11e11bd-22a7-4287-8336-ecf5e814b5f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334699428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27202ef2-6774-4433-9560-5c69b6d378e3", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334702883800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fc0382f-9219-4bd6-ad95-f5830d3ea2ed", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334717439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dacfffa-7bdf-4a0b-acfa-63a410d72887", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334717587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "518d62f6-6f13-4c14-be1d-532e3b5b376a", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334728604200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d1914e7-9878-4a8b-bc11-aceffa6bc3d2", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334729031500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6dab5d5-d137-4bf4-9199-0245cc64913f", "name": "default@CompileArkTS work[83] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334730551200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32334935488400, "endTime": 32336586193200}, "additional": {"children": ["5807dea4-9f85-4c71-8f04-136b54af9a31", "1437bd68-8efa-47f0-897e-2ad8eb27fd1a", "4afb7be9-df2d-475e-bffc-37019a5ee40d", "8a7959c2-0561-4bc5-be07-2af78f342c80", "119723ff-a034-4b4c-bf18-42d472c2ddcb", "79f3b3f4-8a36-42f6-aaac-c4b9625f15c8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0319aa29-fe5f-4461-adc6-dde5c92becac", "logId": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0fc1477-900d-4a49-8313-27c59c9bb3d3", "name": "default@CompileArkTS work[83] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334732200500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0996c6f3-6567-4da0-b1b8-07d836248d38", "name": "default@CompileArkTS work[83] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334732357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d133f4c-af61-4a85-9bcc-44c27c5ddc34", "name": "CopyResources startTime: 32334732448700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334732451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e624ca5-e9b0-471c-b6b2-ffce2eaa5db2", "name": "default@CompileArkTS work[84] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334732516200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fc9b90-f982-4e2c-996f-30f7717919b2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32336286368600, "endTime": 32336328090600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0319aa29-fe5f-4461-adc6-dde5c92becac", "logId": "f5f7fc0b-0043-49cd-91d7-162e69ff944d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c522f53c-25fa-4249-9fc9-ea5731743f9a", "name": "default@CompileArkTS work[84] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334733649300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d308b94c-6033-4dc6-bc83-971c27145e49", "name": "default@CompileArkTS work[84] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334733765600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd560fda-e0dd-43db-b258-380f8de38710", "name": "entry : default@CompileArkTS cost memory 1.5879898071289062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334733851100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b701bc-4915-46a8-af7c-93633c8beb87", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334738316600, "endTime": 32334742410600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2c113804-6985-44bd-b5c3-c2535f09c59c", "logId": "8e2c7a05-1d6e-4b69-bf0c-8af4d60a8e51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c113804-6985-44bd-b5c3-c2535f09c59c", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334735095100}, "additional": {"logType": "detail", "children": [], "durationId": "66b701bc-4915-46a8-af7c-93633c8beb87"}}, {"head": {"id": "140eff60-929a-46bc-bc86-2463e4cc683c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334735382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8161853-c131-48b8-87dd-0e744ddd2690", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334735460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3651c4bc-b0b6-44e3-bb3b-506c01c7b401", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334738326800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1136280c-4f41-413b-b1c7-9ceda3642ca0", "name": "entry : default@BuildJS cost memory 0.1268463134765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334742051400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8cbd04-1be6-4833-89ad-7410177deb12", "name": "runTaskFromQueue task cost before running: 1 s 77 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334742322700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e2c7a05-1d6e-4b69-bf0c-8af4d60a8e51", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334738316600, "endTime": 32334742410600, "totalTime": 3979400}, "additional": {"logType": "info", "children": [], "durationId": "66b701bc-4915-46a8-af7c-93633c8beb87"}}, {"head": {"id": "28be232f-8bf0-4a51-bdbe-074d6b8591c9", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334750577500, "endTime": 32334753124000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8804f115-0219-4286-af6b-5277fd52b416", "logId": "17c68b86-91a9-49e7-be18-484c6c05a110"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8804f115-0219-4286-af6b-5277fd52b416", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334744746000}, "additional": {"logType": "detail", "children": [], "durationId": "28be232f-8bf0-4a51-bdbe-074d6b8591c9"}}, {"head": {"id": "cec551fc-8390-4903-a4ec-f43ebd54ad7a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334745118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "728980b0-859c-415f-9386-aee6353345cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334745281300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8314a396-9a87-4c6b-958c-7ea068bc842b", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334750588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dad3f76-0a83-4ab4-b384-0a2979d662c9", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334750917600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "963821ef-eab7-43a2-be1b-67d85b70d138", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334752283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f4ad1f9-c83e-40ed-8675-c48c6895de35", "name": "entry : default@CacheNativeLibs cost memory 0.0886077880859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334752830100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c68b86-91a9-49e7-be18-484c6c05a110", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334750577500, "endTime": 32334753124000}, "additional": {"logType": "info", "children": [], "durationId": "28be232f-8bf0-4a51-bdbe-074d6b8591c9"}}, {"head": {"id": "f561fedd-3e98-4268-9c9e-9da25cf2ff78", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334935039300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e5ad7f1-dc53-4e48-9312-34d8e4ad8b2e", "name": "default@CompileArkTS work[83] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334935365000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bec1915-4485-4fdb-88cb-798d159f418c", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32335042946400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a22612-f219-4c95-88af-a24e83610928", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32335043075000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd553cfb-76d9-4f1c-a091-355e3a42a431", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32335043219400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f43f58f-f2cd-44c6-b685-7b17406763b0", "name": "default@CompileArkTS work[84] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32335044124200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db75c034-26fe-4de2-88e0-5044f2fd3021", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336328503300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e03e77b-f79b-4d0b-ab46-c346b71e0d47", "name": "CopyResources is end, endTime: 32336328886700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336328892200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca558992-06e9-472a-b585-00747971bfbe", "name": "default@CompileArkTS work[84] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336330910100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f7fc0b-0043-49cd-91d7-162e69ff944d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32336286368600, "endTime": 32336328090600}, "additional": {"logType": "info", "children": [], "durationId": "09fc9b90-f982-4e2c-996f-30f7717919b2", "parent": "7bb37cf5-4445-4dd3-b704-7a89e711463f"}}, {"head": {"id": "01b69705-144d-4110-8ad8-5fefcb65c926", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336586733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5807dea4-9f85-4c71-8f04-136b54af9a31", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32334935572100, "endTime": 32334939815700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "logId": "e2937669-da25-46c3-a650-008432f4d45c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2937669-da25-46c3-a650-008432f4d45c", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334935572100, "endTime": 32334939815700}, "additional": {"logType": "info", "children": [], "durationId": "5807dea4-9f85-4c71-8f04-136b54af9a31", "parent": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}}, {"head": {"id": "1437bd68-8efa-47f0-897e-2ad8eb27fd1a", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32334939863200, "endTime": 32334940044100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "logId": "03a59655-a1e7-4a88-89cb-85eac599fe1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03a59655-a1e7-4a88-89cb-85eac599fe1f", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334939863200, "endTime": 32334940044100}, "additional": {"logType": "info", "children": [], "durationId": "1437bd68-8efa-47f0-897e-2ad8eb27fd1a", "parent": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}}, {"head": {"id": "4afb7be9-df2d-475e-bffc-37019a5ee40d", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32334940068600, "endTime": 32334940133200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "logId": "8e9d11c4-7e53-4429-a8ff-cec81c62ca29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e9d11c4-7e53-4429-a8ff-cec81c62ca29", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334940068600, "endTime": 32334940133200}, "additional": {"logType": "info", "children": [], "durationId": "4afb7be9-df2d-475e-bffc-37019a5ee40d", "parent": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}}, {"head": {"id": "8a7959c2-0561-4bc5-be07-2af78f342c80", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32334940147300, "endTime": 32336488907500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "logId": "2b4692a4-4e14-42f1-92d3-bd90a9dd09d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b4692a4-4e14-42f1-92d3-bd90a9dd09d2", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334940147300, "endTime": 32336488907500}, "additional": {"logType": "info", "children": [], "durationId": "8a7959c2-0561-4bc5-be07-2af78f342c80", "parent": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}}, {"head": {"id": "119723ff-a034-4b4c-bf18-42d472c2ddcb", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336488926600, "endTime": 32336494455600}, "additional": {"children": ["9e0ddc31-a507-4659-b100-b4fa3cb18e59", "58eb2363-a678-49e8-a0a1-016e3bc963b8", "77d45f34-0450-4e4c-b82d-4550f93cffb2"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "logId": "359e70f3-ad2b-44a7-9fb2-d1057e510515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "359e70f3-ad2b-44a7-9fb2-d1057e510515", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336488926600, "endTime": 32336494455600}, "additional": {"logType": "info", "children": ["00f07a85-f22d-4d79-8180-8afe739f4738", "f0e284e3-53da-43ce-b492-fe16e1bf5fae", "f169b337-9051-4d28-be41-28aeabdf9f54"], "durationId": "119723ff-a034-4b4c-bf18-42d472c2ddcb", "parent": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}}, {"head": {"id": "9e0ddc31-a507-4659-b100-b4fa3cb18e59", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336488946100, "endTime": 32336488952200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "119723ff-a034-4b4c-bf18-42d472c2ddcb", "logId": "00f07a85-f22d-4d79-8180-8afe739f4738"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00f07a85-f22d-4d79-8180-8afe739f4738", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336488946100, "endTime": 32336488952200}, "additional": {"logType": "info", "children": [], "durationId": "9e0ddc31-a507-4659-b100-b4fa3cb18e59", "parent": "359e70f3-ad2b-44a7-9fb2-d1057e510515"}}, {"head": {"id": "58eb2363-a678-49e8-a0a1-016e3bc963b8", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336488955500, "endTime": 32336491404200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "119723ff-a034-4b4c-bf18-42d472c2ddcb", "logId": "f0e284e3-53da-43ce-b492-fe16e1bf5fae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0e284e3-53da-43ce-b492-fe16e1bf5fae", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336488955500, "endTime": 32336491404200}, "additional": {"logType": "info", "children": [], "durationId": "58eb2363-a678-49e8-a0a1-016e3bc963b8", "parent": "359e70f3-ad2b-44a7-9fb2-d1057e510515"}}, {"head": {"id": "77d45f34-0450-4e4c-b82d-4550f93cffb2", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336491408900, "endTime": 32336494439800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "119723ff-a034-4b4c-bf18-42d472c2ddcb", "logId": "f169b337-9051-4d28-be41-28aeabdf9f54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f169b337-9051-4d28-be41-28aeabdf9f54", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336491408900, "endTime": 32336494439800}, "additional": {"logType": "info", "children": [], "durationId": "77d45f34-0450-4e4c-b82d-4550f93cffb2", "parent": "359e70f3-ad2b-44a7-9fb2-d1057e510515"}}, {"head": {"id": "79f3b3f4-8a36-42f6-aaac-c4b9625f15c8", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336494472400, "endTime": 32336586050200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "logId": "0abb12c5-64c8-45be-9745-5a6216ba1028"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0abb12c5-64c8-45be-9745-5a6216ba1028", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336494472400, "endTime": 32336586050200}, "additional": {"logType": "info", "children": [], "durationId": "79f3b3f4-8a36-42f6-aaac-c4b9625f15c8", "parent": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850"}}, {"head": {"id": "a52d94b4-df4b-48fc-925c-38ee3fd0313f", "name": "default@CompileArkTS work[83] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336595160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a27a3c6-d2fe-48b6-b94a-dca22e8c3850", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32334935488400, "endTime": 32336586193200}, "additional": {"logType": "info", "children": ["e2937669-da25-46c3-a650-008432f4d45c", "03a59655-a1e7-4a88-89cb-85eac599fe1f", "8e9d11c4-7e53-4429-a8ff-cec81c62ca29", "2b4692a4-4e14-42f1-92d3-bd90a9dd09d2", "359e70f3-ad2b-44a7-9fb2-d1057e510515", "0abb12c5-64c8-45be-9745-5a6216ba1028"], "durationId": "3b25a5a7-c7b7-4960-8498-98523432d2a4", "parent": "7bb37cf5-4445-4dd3-b704-7a89e711463f"}}, {"head": {"id": "b9f5e444-1eb2-479d-a28d-00e0ebdf94e2", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336595314800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb37cf5-4445-4dd3-b704-7a89e711463f", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32334702871700, "endTime": 32336595442600, "totalTime": 1681739800}, "additional": {"logType": "info", "children": ["1a27a3c6-d2fe-48b6-b94a-dca22e8c3850", "f5f7fc0b-0043-49cd-91d7-162e69ff944d"], "durationId": "0319aa29-fe5f-4461-adc6-dde5c92becac"}}, {"head": {"id": "513692f8-4461-4c15-a331-9e7b1ad3cdbe", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336605483900, "endTime": 32336607315600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "aa75c4c6-d557-46e5-b21c-8a2b9a791682", "logId": "2e13b23a-994b-4c0e-877f-cf4b30ffd352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa75c4c6-d557-46e5-b21c-8a2b9a791682", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336603717000}, "additional": {"logType": "detail", "children": [], "durationId": "513692f8-4461-4c15-a331-9e7b1ad3cdbe"}}, {"head": {"id": "dcbc7c36-81b7-46d3-a89d-618225a9f7dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336604314200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b83dcf7b-170b-4e43-8552-bc554b867bc6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336604439000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5add8a-c1a2-4e82-9d9f-c414b6477892", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336605494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873968eb-eb8a-4ec5-8886-5acb66d95140", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336605929400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ab3028-8273-4c49-9f7e-36d1d7eb3dd0", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336607041900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d9ba126-8b4b-4e59-94f1-179bf06de4f8", "name": "entry : default@GeneratePkgModuleJson cost memory 0.1357421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336607232600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e13b23a-994b-4c0e-877f-cf4b30ffd352", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336605483900, "endTime": 32336607315600}, "additional": {"logType": "info", "children": [], "durationId": "513692f8-4461-4c15-a331-9e7b1ad3cdbe"}}, {"head": {"id": "b1247235-9247-43c9-bec0-b833c794a87b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336623755300, "endTime": 32337163670700}, "additional": {"children": ["fb896588-4abd-4cdf-80b8-16875a2ac9a8", "6e821d9a-8dd2-4ea3-9a2a-70e5affbdeb1", "0b67642d-aef5-4654-af93-489e7c1eccc9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "f5279ffa-abf4-40ce-ad6a-a6c69f9b7695", "logId": "ea7c78be-e589-4791-9934-417fef370b43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5279ffa-abf4-40ce-ad6a-a6c69f9b7695", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336610258400}, "additional": {"logType": "detail", "children": [], "durationId": "b1247235-9247-43c9-bec0-b833c794a87b"}}, {"head": {"id": "5f5343d9-e034-48d3-8fe1-851df0aabc8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336610665000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96ee403-a71f-4c9d-83de-4184e96101fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336610777500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc0b7b4-8e22-4453-b83b-e006df10b358", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336623768300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4e1778-6d6a-4b7f-88e5-a04d2e68c200", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336644592900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7848071-767b-47d8-a869-67c594c76df3", "name": "Incremental task entry:default@PackageHap pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336644741100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e5dc02-2342-49a3-b718-5791c7e4c961", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336644841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "487a90d5-bc8c-4f64-906a-cd618499cf9b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336644899000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb896588-4abd-4cdf-80b8-16875a2ac9a8", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336645685600, "endTime": 32336652267200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1247235-9247-43c9-bec0-b833c794a87b", "logId": "92002bfd-0d6d-45b1-acb2-a401ba99f7bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "227aa2f3-7844-432b-8553-9e4dd4e83c92", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336650067400}, "additional": {"logType": "debug", "children": [], "durationId": "b1247235-9247-43c9-bec0-b833c794a87b"}}, {"head": {"id": "92002bfd-0d6d-45b1-acb2-a401ba99f7bc", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336645685600, "endTime": 32336652267200}, "additional": {"logType": "info", "children": [], "durationId": "fb896588-4abd-4cdf-80b8-16875a2ac9a8", "parent": "ea7c78be-e589-4791-9934-417fef370b43"}}, {"head": {"id": "6e821d9a-8dd2-4ea3-9a2a-70e5affbdeb1", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336653054000, "endTime": 32336657013900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b1247235-9247-43c9-bec0-b833c794a87b", "logId": "6e4108d5-8016-4427-892b-7ab7f22dc932"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7043d9d8-6c01-4d81-83ae-e18c4119578b", "name": "default@PackageHap work[85] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336653961700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b67642d-aef5-4654-af93-489e7c1eccc9", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336656928100, "endTime": 32337162905300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b1247235-9247-43c9-bec0-b833c794a87b", "logId": "60000028-ceb3-4642-8bf7-a51b3ab4114d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acba8007-c2f1-49b4-98c1-a9c41de08c31", "name": "default@PackageHap work[85] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336656434200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8b67be1-a5c9-42c2-988a-e672739a103d", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336656681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed6abf08-46a5-47cd-9507-29fea4a64cfe", "name": "default@PackageHap work[85] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336656795100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd8162b-67f4-41b3-8e40-7a561eeff70a", "name": "default@PackageHap work[85] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336656944100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e4108d5-8016-4427-892b-7ab7f22dc932", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336653054000, "endTime": 32336657013900}, "additional": {"logType": "info", "children": [], "durationId": "6e821d9a-8dd2-4ea3-9a2a-70e5affbdeb1", "parent": "ea7c78be-e589-4791-9934-417fef370b43"}}, {"head": {"id": "97d26a0a-2651-4d05-bdbc-a80e5a95d3ba", "name": "entry : default@PackageHap cost memory -4.990966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336662342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3558233-ef68-4e89-b2f1-ef7461138fd5", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336662885900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb121f14-960f-4099-91e8-477d293b95c8", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337163061100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6162d98b-402f-41ea-b9da-21651003e881", "name": "default@PackageHap work[85] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337163500600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60000028-ceb3-4642-8bf7-a51b3ab4114d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32336656928100, "endTime": 32337162905300}, "additional": {"logType": "info", "children": [], "durationId": "0b67642d-aef5-4654-af93-489e7c1eccc9", "parent": "ea7c78be-e589-4791-9934-417fef370b43"}}, {"head": {"id": "ea7c78be-e589-4791-9934-417fef370b43", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32336623755300, "endTime": 32337163670700, "totalTime": 539153200}, "additional": {"logType": "info", "children": ["92002bfd-0d6d-45b1-acb2-a401ba99f7bc", "6e4108d5-8016-4427-892b-7ab7f22dc932", "60000028-ceb3-4642-8bf7-a51b3ab4114d"], "durationId": "b1247235-9247-43c9-bec0-b833c794a87b"}}, {"head": {"id": "d01c84f5-497c-4e1f-9a6d-65ec7e0a125b", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337172534300, "endTime": 32337174854800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "f08cf529-a6c4-4190-b180-f6f3ac5292df", "logId": "3ed02791-664d-4d5e-9c6d-8d4171455fe3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f08cf529-a6c4-4190-b180-f6f3ac5292df", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337168797400}, "additional": {"logType": "detail", "children": [], "durationId": "d01c84f5-497c-4e1f-9a6d-65ec7e0a125b"}}, {"head": {"id": "838675cd-bfad-428f-9de8-4044fc08f269", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337169259100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da1dc605-dbd6-4c82-8450-f717ebc903dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337169404900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c140eb9-7b1f-4f8f-ae35-0f8a7d9aa92c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337172546300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809dd5d7-b3cd-41d2-9dfd-ce79cf8b6d50", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337172958300}, "additional": {"logType": "warn", "children": [], "durationId": "d01c84f5-497c-4e1f-9a6d-65ec7e0a125b"}}, {"head": {"id": "f509ff12-4949-4a92-b816-0a7e6b829bb4", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337173963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0499dc-137e-408f-8b8d-655a4374365c", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337174099500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a00b5c8-6869-4c9a-809b-39067e28bfea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337174334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13972339-802d-4f2f-a82b-9a1ec52c249d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337174402900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e980a8-ab4b-4a09-9f52-7253ac48f169", "name": "entry : default@SignHap cost memory 0.1148223876953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337174676100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397ac688-a10f-4f7e-933f-33376b00d540", "name": "runTaskFromQueue task cost before running: 3 s 509 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337174791100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed02791-664d-4d5e-9c6d-8d4171455fe3", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337172534300, "endTime": 32337174854800, "totalTime": 2236000}, "additional": {"logType": "info", "children": [], "durationId": "d01c84f5-497c-4e1f-9a6d-65ec7e0a125b"}}, {"head": {"id": "b9b5da52-ae59-4ce9-9586-26cb31bc2f1d", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337178376200, "endTime": 32337188070400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "43c1c58d-faf4-4ab9-bf0a-cb4cdea54dac", "logId": "5d2de3a2-eb01-444b-9dff-40e0be0de717"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43c1c58d-faf4-4ab9-bf0a-cb4cdea54dac", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337176893900}, "additional": {"logType": "detail", "children": [], "durationId": "b9b5da52-ae59-4ce9-9586-26cb31bc2f1d"}}, {"head": {"id": "d617c99d-d83d-47e3-afdf-041a08711006", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337177339400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01bc8bb2-b28e-4811-aa78-f9e709fd5ed0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337177445500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b4956f-73a0-40ac-8466-df01ef946c8a", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337178386100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52206a6c-9b22-4122-9f32-17674d987513", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337187429600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21bc32c2-d135-4a30-bed1-acc9cf7cc2c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337187551200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88174854-ffd3-459f-8046-be7f653228df", "name": "entry : default@CollectDebugSymbol cost memory 0.27518463134765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337187888100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752071cd-402d-4c72-815e-e62eed85a645", "name": "runTaskFromQueue task cost before running: 3 s 523 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337188004500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d2de3a2-eb01-444b-9dff-40e0be0de717", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337178376200, "endTime": 32337188070400, "totalTime": 9605300}, "additional": {"logType": "info", "children": [], "durationId": "b9b5da52-ae59-4ce9-9586-26cb31bc2f1d"}}, {"head": {"id": "f27d32d5-4b69-4687-9d50-047c25dd91c3", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337191345100, "endTime": 32337191892300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "736e2fc3-2d93-437b-bad9-d26df85a1133", "logId": "3d7912fa-68b9-4af6-971b-7b5f52030783"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "736e2fc3-2d93-437b-bad9-d26df85a1133", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337191220000}, "additional": {"logType": "detail", "children": [], "durationId": "f27d32d5-4b69-4687-9d50-047c25dd91c3"}}, {"head": {"id": "1e6f8880-0282-4f31-a70d-fcad8385350c", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337191355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93bd44f-2261-433b-a7ad-90857a087789", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337191572200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73222d64-4936-4e0f-bd17-f0fce8110118", "name": "runTaskFromQueue task cost before running: 3 s 526 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337191675500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d7912fa-68b9-4af6-971b-7b5f52030783", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337191345100, "endTime": 32337191892300, "totalTime": 310000}, "additional": {"logType": "info", "children": [], "durationId": "f27d32d5-4b69-4687-9d50-047c25dd91c3"}}, {"head": {"id": "b7f8af37-af8e-473b-adf9-319ab3f2ebf5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337200748200, "endTime": 32337200771900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1421b6bd-4b91-4d66-84d5-67c89c73bf57", "logId": "9359a4cf-c3d7-4bd6-a2cc-f2f99dfb613f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9359a4cf-c3d7-4bd6-a2cc-f2f99dfb613f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337200748200, "endTime": 32337200771900}, "additional": {"logType": "info", "children": [], "durationId": "b7f8af37-af8e-473b-adf9-319ab3f2ebf5"}}, {"head": {"id": "2c49f03f-a19c-47c9-8428-7c92dff7e49a", "name": "BUILD SUCCESSFUL in 3 s 535 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337200811900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "5db5801e-ed33-4860-97fd-4f6cd57fda19", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32333665790700, "endTime": 32337201586100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 28}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "1369fb58-4911-4d07-9c71-5215524eb13a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337201806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db9c72d1-bb5c-4fd1-8dcb-e3b1fe925071", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337202005900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4d7cfd-a3b6-4a52-b801-675572ed94ec", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337202071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b0c5bcb-95f2-462a-b23a-11f57f78067c", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337202132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1930fef3-2a1a-414a-9b25-dd302ced8b0c", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337202228900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b3f7443-432a-4dad-9396-0835a9588498", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337202572300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c58a13-f9f2-4fc3-8be3-bcbcec00e57c", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337203355900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c54a14b-15c0-4621-b39a-a7b8ee064d8a", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337203630900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e2d6e9-59ed-466c-89df-2feffe26db39", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337203853700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a2a0bf4-d190-4bf0-a26b-8b7804d0d892", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337203941700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da7e97f-ac05-4b60-9117-344e3b20f6f3", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337204215800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199c2fc3-3def-4b76-8a71-c74bdd9aa5d3", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337205200500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd0b0e07-d2fa-4fbc-8dc5-0424a6e51db9", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337205508500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3defdf0-6bf6-4244-9237-b101c22226fe", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337205588900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50416341-e90c-4295-9c0d-db026b0d0863", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337205795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303137e9-cb1c-4d7e-baf8-ecd3b956ee66", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337205888600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c931b52-a753-4ed1-921c-304cc3fc62f5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337206268600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b4fcc0-93f7-448a-8857-4cab800c9153", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337206739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a267ab5-61a5-49ab-9497-c13190ca4ff8", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337206975400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26a7acff-2183-44b9-be42-f68446c3441d", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337207180600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0951eee1-3a3a-48ff-b5a8-10696f0bc16a", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337207477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26f33040-bf0f-46de-bfc2-9de6529158e8", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337207550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b821981f-e3f3-46ff-91ea-66fd62030175", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337207603700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "959bcbb8-a3f5-4805-9781-8e169b039495", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337209603100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49f72c3a-bdf2-4beb-831d-608e2b056cbf", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337210098300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695ccf6d-2f90-4723-bc66-9dbb0da566b3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337211108700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b87a01c-7781-4b75-b20c-8d279e2d3996", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337211362000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6064260b-b4a0-4f12-b3d9-4284e0bcd77d", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337211561300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3f7b1bf-f0e8-4d07-99a6-cd65e3ea580d", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337212244400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c921290-d719-4778-9e51-6418a5b45d5e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337212345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db10e6d-b234-4044-986d-3254898fca07", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337214351200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3eb802f-e39c-41c5-ab14-d18af23b25df", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337214696500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62441e65-de6f-46cd-8895-95b0981711a4", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337215317900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d402f9e2-35fb-4721-9d5f-284c033bf224", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337218913800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8541a4ed-d79a-4444-91eb-18e8938c332c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337219823100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318b322c-8a8c-4697-9881-32c6b85300b2", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337220697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde4ac5c-d3e6-4243-b08b-252ad276a639", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337220964500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80624ee5-8eef-4d8b-9754-d5d52231fa2e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337221348300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe409e73-79d1-4979-ab6b-393be899732c", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337222187500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dec94f95-74be-4606-a0c8-3a6dcdb5fde8", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337222505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c071e8e-ed15-4454-a1e7-bcd38c442e93", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337222704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "883d2ebe-4e4d-4f45-b3f2-88fb9b10b16d", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337222864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbefdad9-25e3-46ce-82f9-e319deace0cb", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337224310100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c5feb5d-5688-4e09-aa95-9691265c230f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337224660300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cefb4185-79f1-4248-8c63-cbc8458bfb48", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337224894500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae2ac2d-8b86-4ac7-9965-3f5393218110", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337231998900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f698c30d-f539-44a1-b589-f98cc104933f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337232271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea14192-a518-489d-9e4f-ef10534764b1", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337232449500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a72510-f697-488c-9255-f9352fbfe2f7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337232511500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87a7636b-e966-4672-9c1d-b1e2a87637b5", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337232757500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d261792-b055-49d5-bed7-26fc262a7da7", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337233527700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e19addb7-b4ab-41c1-a1d7-66d51a3a1a00", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337233800300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1128847e-5eca-498a-ba5f-2f1431c9da9d", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337233996100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bad248d-ed88-4158-9642-847e478196a0", "name": "Incremental task entry:default@PackageHap post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337234272800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d05931-f312-409f-99d7-c6561e26098b", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337234444100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616811b6-e6b9-48e5-a5f9-52d254b47fae", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337234516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0114997-8c47-436c-bdde-a1764171d637", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337234708000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598c35ed-3c47-4f6b-bf2b-6085b173f06f", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337236914500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72d6390-b170-4eaa-b5e4-72744434d3c3", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337237264800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50d0388a-e397-4eba-8f0a-96b81391082d", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337237553300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b683c419-506c-4154-8d28-13e4c2fa9d9e", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32337238029700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}