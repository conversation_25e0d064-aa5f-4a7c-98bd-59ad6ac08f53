{"compileConfig": {"deviceType": "default", "Path": "C:\\Program Files\\Huawei\\DevEco Studio\\tools\\node\\", "localPropertiesPath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\local.properties", "note": "false", "aceProfilePath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "buildMode": "debug", "img2bin": "true", "projectProfilePath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5", "compilerType": "ark", "appResource": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", "watchMode": "false", "logLevel": "3", "aceBuildJson": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets", "aceSoPath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\nativeDependencies.txt", "cachePath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug", "aceModuleBuild": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", "aceSuperVisualPath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\supervisual", "aceModuleJsonPath": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json"}, "patchConfig": {"changedFileList": "D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\patch\\default\\changedFileList.json"}}