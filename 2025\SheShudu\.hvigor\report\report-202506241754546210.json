{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "b535fba5-da92-45a2-9b2f-7c31ac8cd925", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823991833100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b50095e3-7a9e-4d6d-be1f-05ab31f5db21", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33824101973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703dacd0-0426-497e-9a5c-662661310684", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33824102268900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c91dbde0-0540-4099-89f4-3488fa890ab8", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926514590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926521326400, "endTime": 33926937239800}, "additional": {"children": ["a934f093-7560-45d4-9d8c-9e73450f66d4", "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "3ebbca2b-b857-4a0c-95c8-298fc29d159a", "5fbf8082-2ee7-4c60-8a7f-31b8deb17c7b", "b2c250b6-c512-4afb-9cb4-3f5f90543418", "85574a2c-dcc1-44d0-9af6-e3d5d5d46639", "c53c64c1-bda7-4eab-8299-8a50d132bb45"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a934f093-7560-45d4-9d8c-9e73450f66d4", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926521327900, "endTime": 33926533610500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "5fff9418-f76b-4436-9dd1-63825a38c20e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926533651300, "endTime": 33926935443400}, "additional": {"children": ["33fbdac1-fd01-47f8-9586-0fc062336e27", "b8d6258a-9130-426c-a091-2711edb31a48", "d3178b5b-333a-4148-86b8-62005d027ecb", "37d67b46-df3b-4cb4-be9a-9f0dc6ace433", "c7c2ffe6-2451-4d2a-8e34-19cd5d48e332", "bae39f24-4b53-40b9-b4f7-506544c5e8ac", "9b9a955e-eb96-4123-abae-124914b25bda", "66ab369d-e240-44e1-bbe4-f3bf6738a6ea", "87babf42-1ad1-4cd0-ac67-a8e006ea493c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ebbca2b-b857-4a0c-95c8-298fc29d159a", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926935470200, "endTime": 33926937099400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "3cdc6661-0d31-4d1c-8779-dbe047830c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fbf8082-2ee7-4c60-8a7f-31b8deb17c7b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926937216600, "endTime": 33926937218800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "0201aae6-aecd-407d-9656-ed28d2778b03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2c250b6-c512-4afb-9cb4-3f5f90543418", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926524422000, "endTime": 33926524466900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "d89c5bcf-00b1-499f-a920-895da9604aaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d89c5bcf-00b1-499f-a920-895da9604aaf", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926524422000, "endTime": 33926524466900}, "additional": {"logType": "info", "children": [], "durationId": "b2c250b6-c512-4afb-9cb4-3f5f90543418", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "85574a2c-dcc1-44d0-9af6-e3d5d5d46639", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926528760700, "endTime": 33926528778600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "8c6c4588-63f1-4b19-821b-acaa4647fa4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c6c4588-63f1-4b19-821b-acaa4647fa4c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926528760700, "endTime": 33926528778600}, "additional": {"logType": "info", "children": [], "durationId": "85574a2c-dcc1-44d0-9af6-e3d5d5d46639", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "374e01c6-cb8b-4cb3-8057-cff3ee8e661c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926528857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51874463-4cf7-46b2-91bd-0b37e657df0c", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926533487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fff9418-f76b-4436-9dd1-63825a38c20e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926521327900, "endTime": 33926533610500}, "additional": {"logType": "info", "children": [], "durationId": "a934f093-7560-45d4-9d8c-9e73450f66d4", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "33fbdac1-fd01-47f8-9586-0fc062336e27", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926538974000, "endTime": 33926538981100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "a4a11dc9-72f4-4914-8e7e-7ef84c1949cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d6258a-9130-426c-a091-2711edb31a48", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926538995600, "endTime": 33926542555100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "3464cf87-8dd4-489c-9942-eb1469ec88f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3178b5b-333a-4148-86b8-62005d027ecb", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926542566700, "endTime": 33926702608300}, "additional": {"children": ["fa4c5fc7-5aef-4a1b-b864-85c51b758cea", "799c4f8a-d9d3-4f8a-bbc0-212492b9b4ca", "ba207ab4-8cac-4130-ad9e-1c12f9aecd5a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "934bcdb2-70e0-444f-87e2-c294530b5e86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37d67b46-df3b-4cb4-be9a-9f0dc6ace433", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926702661300, "endTime": 33926749017100}, "additional": {"children": ["4c5244b3-5db1-4b2a-8149-8aba0d8a6f32"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "80630018-cecb-4dcd-af39-4e7094c7f03d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7c2ffe6-2451-4d2a-8e34-19cd5d48e332", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926749053700, "endTime": 33926903032900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "cabd7a06-3d01-4675-b9b8-97dad908b532"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bae39f24-4b53-40b9-b4f7-506544c5e8ac", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926903897500, "endTime": 33926921411500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "eacd1ae4-3dc1-4e89-9c53-fbc308d2f990"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b9a955e-eb96-4123-abae-124914b25bda", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926921433800, "endTime": 33926935199400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "314b5d31-8490-47b8-a38b-ba3dc3b18565"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66ab369d-e240-44e1-bbe4-f3bf6738a6ea", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926935243500, "endTime": 33926935394700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "2f30106d-2466-4baf-8e54-9a40a67f04e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4a11dc9-72f4-4914-8e7e-7ef84c1949cb", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926538974000, "endTime": 33926538981100}, "additional": {"logType": "info", "children": [], "durationId": "33fbdac1-fd01-47f8-9586-0fc062336e27", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "3464cf87-8dd4-489c-9942-eb1469ec88f4", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926538995600, "endTime": 33926542555100}, "additional": {"logType": "info", "children": [], "durationId": "b8d6258a-9130-426c-a091-2711edb31a48", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "fa4c5fc7-5aef-4a1b-b864-85c51b758cea", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926543090600, "endTime": 33926543110700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d3178b5b-333a-4148-86b8-62005d027ecb", "logId": "d9684e93-939a-4fe5-b000-15462930ec22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9684e93-939a-4fe5-b000-15462930ec22", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926543090600, "endTime": 33926543110700}, "additional": {"logType": "info", "children": [], "durationId": "fa4c5fc7-5aef-4a1b-b864-85c51b758cea", "parent": "934bcdb2-70e0-444f-87e2-c294530b5e86"}}, {"head": {"id": "799c4f8a-d9d3-4f8a-bbc0-212492b9b4ca", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926545276600, "endTime": 33926701820300}, "additional": {"children": ["90c6a415-c75a-4c90-9df9-a160fe2e7663", "25526d5b-428f-4fed-892d-71cac27fdd44"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d3178b5b-333a-4148-86b8-62005d027ecb", "logId": "940bd201-3f77-4b43-89fb-0ed19ed39478"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90c6a415-c75a-4c90-9df9-a160fe2e7663", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926545278800, "endTime": 33926553943300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "799c4f8a-d9d3-4f8a-bbc0-212492b9b4ca", "logId": "48533d45-16a9-4ac3-9ade-c8fe7f1c90fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25526d5b-428f-4fed-892d-71cac27fdd44", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926553959400, "endTime": 33926701794300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "799c4f8a-d9d3-4f8a-bbc0-212492b9b4ca", "logId": "d4175d64-6d6d-4152-be58-e19900622b01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b975335d-7f6b-426e-8db0-870ec617d4b1", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926545286000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ceadb8-e1a6-4fc8-980d-31f91027785e", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926553812100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48533d45-16a9-4ac3-9ade-c8fe7f1c90fa", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926545278800, "endTime": 33926553943300}, "additional": {"logType": "info", "children": [], "durationId": "90c6a415-c75a-4c90-9df9-a160fe2e7663", "parent": "940bd201-3f77-4b43-89fb-0ed19ed39478"}}, {"head": {"id": "23f63eb4-53dc-4913-9be1-c9c829fe62cc", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926553973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68af5c8-4e02-4c00-9d34-9040198c2cc3", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926562055900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1422d5-5f1f-4da0-880f-535f3771c9ce", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926562291200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f01febe-25b3-4a72-8bdc-c1952a547059", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926562505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9481b554-7f6b-4570-928b-200c745b19b6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926562627500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40708c3c-c9e6-44ff-ab88-958877ed0675", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926566580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618b085e-bb26-457d-a81f-3ff291b900fd", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926571215900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff537f2-59d9-475b-95fc-3ab21e332810", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926592400900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764924c7-25ca-467e-8438-6974d8c3d52f", "name": "Sdk init in 80 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926652494600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a873cf0-1b60-4db0-bce2-f9c7891339e9", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926652665800}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 54}, "markType": "other"}}, {"head": {"id": "eda55e7b-7e6d-414c-b434-3f74c59f7878", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926652767300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 54}, "markType": "other"}}, {"head": {"id": "63081750-e88e-4965-8bbb-6d695da270ab", "name": "Project task initialization takes 48 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926701403500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "562fc6e6-cc86-42b8-bc72-57864d23ab5c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926701537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe3ef5ce-470c-49bf-9ef4-0755c6b679d3", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926701597600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf0ebe6-3c2f-46d1-8246-443094fae7cc", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926701734100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4175d64-6d6d-4152-be58-e19900622b01", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926553959400, "endTime": 33926701794300}, "additional": {"logType": "info", "children": [], "durationId": "25526d5b-428f-4fed-892d-71cac27fdd44", "parent": "940bd201-3f77-4b43-89fb-0ed19ed39478"}}, {"head": {"id": "940bd201-3f77-4b43-89fb-0ed19ed39478", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926545276600, "endTime": 33926701820300}, "additional": {"logType": "info", "children": ["48533d45-16a9-4ac3-9ade-c8fe7f1c90fa", "d4175d64-6d6d-4152-be58-e19900622b01"], "durationId": "799c4f8a-d9d3-4f8a-bbc0-212492b9b4ca", "parent": "934bcdb2-70e0-444f-87e2-c294530b5e86"}}, {"head": {"id": "ba207ab4-8cac-4130-ad9e-1c12f9aecd5a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926702572600, "endTime": 33926702592300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d3178b5b-333a-4148-86b8-62005d027ecb", "logId": "be450fb0-7975-4820-9ae1-af6f202b7cfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be450fb0-7975-4820-9ae1-af6f202b7cfd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926702572600, "endTime": 33926702592300}, "additional": {"logType": "info", "children": [], "durationId": "ba207ab4-8cac-4130-ad9e-1c12f9aecd5a", "parent": "934bcdb2-70e0-444f-87e2-c294530b5e86"}}, {"head": {"id": "934bcdb2-70e0-444f-87e2-c294530b5e86", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926542566700, "endTime": 33926702608300}, "additional": {"logType": "info", "children": ["d9684e93-939a-4fe5-b000-15462930ec22", "940bd201-3f77-4b43-89fb-0ed19ed39478", "be450fb0-7975-4820-9ae1-af6f202b7cfd"], "durationId": "d3178b5b-333a-4148-86b8-62005d027ecb", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "4c5244b3-5db1-4b2a-8149-8aba0d8a6f32", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926703447200, "endTime": 33926749005500}, "additional": {"children": ["3acd0b20-5712-45da-b4a3-b91cf5b5000f", "b109e051-0204-4f1f-a496-75afd5b22e86", "df709ab1-79e8-43ff-a75e-bc211e87fe15"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "37d67b46-df3b-4cb4-be9a-9f0dc6ace433", "logId": "4f566512-c381-4ed5-b1e9-7c1e6ab16064"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3acd0b20-5712-45da-b4a3-b91cf5b5000f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926709937600, "endTime": 33926709958800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c5244b3-5db1-4b2a-8149-8aba0d8a6f32", "logId": "c1a5e26b-5095-4850-a615-4822828f978b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1a5e26b-5095-4850-a615-4822828f978b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926709937600, "endTime": 33926709958800}, "additional": {"logType": "info", "children": [], "durationId": "3acd0b20-5712-45da-b4a3-b91cf5b5000f", "parent": "4f566512-c381-4ed5-b1e9-7c1e6ab16064"}}, {"head": {"id": "b109e051-0204-4f1f-a496-75afd5b22e86", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926714942700, "endTime": 33926747056000}, "additional": {"children": ["095ce541-efcb-4bb6-b4eb-a04723ce06d3", "ac344feb-22af-424a-8882-39287484f086"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c5244b3-5db1-4b2a-8149-8aba0d8a6f32", "logId": "a25f4f9c-d4d1-4b2f-b550-3b2ed715c93b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "095ce541-efcb-4bb6-b4eb-a04723ce06d3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926714944800, "endTime": 33926719953100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b109e051-0204-4f1f-a496-75afd5b22e86", "logId": "82b0d1ab-99f0-426a-a2ff-1a0818d57b98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac344feb-22af-424a-8882-39287484f086", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926719970300, "endTime": 33926747040300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b109e051-0204-4f1f-a496-75afd5b22e86", "logId": "9314b9df-3418-4bb1-a05d-fd0e29d79e86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59bbfa8d-b721-4af4-b6b0-d625eafd9f6d", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926714950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "011c9dcb-f150-4ec6-986e-d30513f49a1d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926719818600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b0d1ab-99f0-426a-a2ff-1a0818d57b98", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926714944800, "endTime": 33926719953100}, "additional": {"logType": "info", "children": [], "durationId": "095ce541-efcb-4bb6-b4eb-a04723ce06d3", "parent": "a25f4f9c-d4d1-4b2f-b550-3b2ed715c93b"}}, {"head": {"id": "7f92e446-8e06-4355-85fc-dbb3d04c2f25", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926719978400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f190695a-c022-4018-8133-cdfab06afa7e", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926728838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf798d0-9816-4694-be86-cc8fca90c419", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926729013900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "064f0185-e029-49a5-8f70-4642abe5a8a0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926729297500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13b70caf-447e-4823-be86-e592a48fea8f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926729464800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a2ec1d1-1ae2-4745-8e7e-12498d47864a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926729717200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd060e03-2e11-4508-bf76-5271e5dbf612", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926729892800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dee7948-5822-4089-995e-fdacee349d6f", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926729971200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6f862e-14b5-4d49-ac4d-8f74eba778f3", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926746267900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d8b881-e559-4fc4-acb0-1d60c72e4dfb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926746440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc12b04-2bec-4365-beba-04cb65447d79", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926746775200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c8fba5e-2f55-4656-bff3-dbabb574711a", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926746969400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9314b9df-3418-4bb1-a05d-fd0e29d79e86", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926719970300, "endTime": 33926747040300}, "additional": {"logType": "info", "children": [], "durationId": "ac344feb-22af-424a-8882-39287484f086", "parent": "a25f4f9c-d4d1-4b2f-b550-3b2ed715c93b"}}, {"head": {"id": "a25f4f9c-d4d1-4b2f-b550-3b2ed715c93b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926714942700, "endTime": 33926747056000}, "additional": {"logType": "info", "children": ["82b0d1ab-99f0-426a-a2ff-1a0818d57b98", "9314b9df-3418-4bb1-a05d-fd0e29d79e86"], "durationId": "b109e051-0204-4f1f-a496-75afd5b22e86", "parent": "4f566512-c381-4ed5-b1e9-7c1e6ab16064"}}, {"head": {"id": "df709ab1-79e8-43ff-a75e-bc211e87fe15", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926748929700, "endTime": 33926748983600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c5244b3-5db1-4b2a-8149-8aba0d8a6f32", "logId": "651fd419-9e6e-4555-adff-dd5af130b4a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "651fd419-9e6e-4555-adff-dd5af130b4a4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926748929700, "endTime": 33926748983600}, "additional": {"logType": "info", "children": [], "durationId": "df709ab1-79e8-43ff-a75e-bc211e87fe15", "parent": "4f566512-c381-4ed5-b1e9-7c1e6ab16064"}}, {"head": {"id": "4f566512-c381-4ed5-b1e9-7c1e6ab16064", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926703447200, "endTime": 33926749005500}, "additional": {"logType": "info", "children": ["c1a5e26b-5095-4850-a615-4822828f978b", "a25f4f9c-d4d1-4b2f-b550-3b2ed715c93b", "651fd419-9e6e-4555-adff-dd5af130b4a4"], "durationId": "4c5244b3-5db1-4b2a-8149-8aba0d8a6f32", "parent": "80630018-cecb-4dcd-af39-4e7094c7f03d"}}, {"head": {"id": "80630018-cecb-4dcd-af39-4e7094c7f03d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926702661300, "endTime": 33926749017100}, "additional": {"logType": "info", "children": ["4f566512-c381-4ed5-b1e9-7c1e6ab16064"], "durationId": "37d67b46-df3b-4cb4-be9a-9f0dc6ace433", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "8dc723b2-757f-43da-8853-39a3927f414f", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926812203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "400b858f-898c-4bc1-b81a-6586e143e376", "name": "hvigorfile, resolve hvigorfile dependencies in 154 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926902901400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cabd7a06-3d01-4675-b9b8-97dad908b532", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926749053700, "endTime": 33926903032900}, "additional": {"logType": "info", "children": [], "durationId": "c7c2ffe6-2451-4d2a-8e34-19cd5d48e332", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "87babf42-1ad1-4cd0-ac67-a8e006ea493c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926903725100, "endTime": 33926903886000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "logId": "c953601d-01c3-44b9-a0d7-cde4e8a17f74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d35de0df-a094-4d75-8d17-f74a732a7ef0", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926903745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c953601d-01c3-44b9-a0d7-cde4e8a17f74", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926903725100, "endTime": 33926903886000}, "additional": {"logType": "info", "children": [], "durationId": "87babf42-1ad1-4cd0-ac67-a8e006ea493c", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "37df724f-77ba-43b0-9e1e-f94de585895d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926904855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6587c802-0f78-4411-9bfa-7ec3206b0c48", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926920382900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eacd1ae4-3dc1-4e89-9c53-fbc308d2f990", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926903897500, "endTime": 33926921411500}, "additional": {"logType": "info", "children": [], "durationId": "bae39f24-4b53-40b9-b4f7-506544c5e8ac", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "00ec2a22-cc77-44a8-978b-7950205e92fc", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926929179100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2a90d18-db34-47d2-82a1-c642f7eaf150", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926929649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2483214-7e6c-4432-a571-a11e855eab59", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926931862100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce85547-52d6-4944-ab09-b7385cf775ce", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926931991400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "314b5d31-8490-47b8-a38b-ba3dc3b18565", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926921433800, "endTime": 33926935199400}, "additional": {"logType": "info", "children": [], "durationId": "9b9a955e-eb96-4123-abae-124914b25bda", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "5a964d6f-b576-493c-ac19-eef2c587ccf4", "name": "Configuration phase cost:397 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926935279900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f30106d-2466-4baf-8e54-9a40a67f04e3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926935243500, "endTime": 33926935394700}, "additional": {"logType": "info", "children": [], "durationId": "66ab369d-e240-44e1-bbe4-f3bf6738a6ea", "parent": "9aa62f4c-9dbf-4098-9927-77d8a5599261"}}, {"head": {"id": "9aa62f4c-9dbf-4098-9927-77d8a5599261", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926533651300, "endTime": 33926935443400}, "additional": {"logType": "info", "children": ["a4a11dc9-72f4-4914-8e7e-7ef84c1949cb", "3464cf87-8dd4-489c-9942-eb1469ec88f4", "934bcdb2-70e0-444f-87e2-c294530b5e86", "80630018-cecb-4dcd-af39-4e7094c7f03d", "cabd7a06-3d01-4675-b9b8-97dad908b532", "eacd1ae4-3dc1-4e89-9c53-fbc308d2f990", "314b5d31-8490-47b8-a38b-ba3dc3b18565", "2f30106d-2466-4baf-8e54-9a40a67f04e3", "c953601d-01c3-44b9-a0d7-cde4e8a17f74"], "durationId": "7e7f1950-0253-47ed-abd5-1fbae8ac803a", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "c53c64c1-bda7-4eab-8299-8a50d132bb45", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926937065500, "endTime": 33926937085900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c2aacb7-641b-4243-b9c8-e3890528ef6b", "logId": "0ff3e7c1-9de6-44be-8365-b62d3e8ce535"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ff3e7c1-9de6-44be-8365-b62d3e8ce535", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926937065500, "endTime": 33926937085900}, "additional": {"logType": "info", "children": [], "durationId": "c53c64c1-bda7-4eab-8299-8a50d132bb45", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "3cdc6661-0d31-4d1c-8779-dbe047830c8d", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926935470200, "endTime": 33926937099400}, "additional": {"logType": "info", "children": [], "durationId": "3ebbca2b-b857-4a0c-95c8-298fc29d159a", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "0201aae6-aecd-407d-9656-ed28d2778b03", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926937216600, "endTime": 33926937218800}, "additional": {"logType": "info", "children": [], "durationId": "5fbf8082-2ee7-4c60-8a7f-31b8deb17c7b", "parent": "2dca6c98-8444-4682-b2ca-9a325cb08b31"}}, {"head": {"id": "2dca6c98-8444-4682-b2ca-9a325cb08b31", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926521326400, "endTime": 33926937239800}, "additional": {"logType": "info", "children": ["5fff9418-f76b-4436-9dd1-63825a38c20e", "9aa62f4c-9dbf-4098-9927-77d8a5599261", "3cdc6661-0d31-4d1c-8779-dbe047830c8d", "0201aae6-aecd-407d-9656-ed28d2778b03", "d89c5bcf-00b1-499f-a920-895da9604aaf", "8c6c4588-63f1-4b19-821b-acaa4647fa4c", "0ff3e7c1-9de6-44be-8365-b62d3e8ce535"], "durationId": "0c2aacb7-641b-4243-b9c8-e3890528ef6b"}}, {"head": {"id": "465eb6c8-2211-418a-a6ca-c3acdf3bae1c", "name": "Configuration task cost before running: 420 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926937452100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "261f5871-2641-4249-9569-8c47ae77c880", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926945562200, "endTime": 33926963011700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e891efa6-88b3-4308-9e6e-eea78075fa4f", "logId": "cd2b8703-021c-41df-b300-5ae3120912c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e891efa6-88b3-4308-9e6e-eea78075fa4f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926938979700}, "additional": {"logType": "detail", "children": [], "durationId": "261f5871-2641-4249-9569-8c47ae77c880"}}, {"head": {"id": "afa20093-1322-4ce8-8fd6-0baebe195728", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926939463600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77b0cd79-0b8a-4804-a8f8-597148608c53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926939580400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c05b5d9-3148-4153-83d2-df4344060749", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926945582100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf0543c8-d86a-46f7-9400-4d00e6d39c50", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926962506700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f62960-b1a3-437c-8274-82fb2deed244", "name": "entry : default@PreBuild cost memory 0.31160736083984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926962767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd2b8703-021c-41df-b300-5ae3120912c0", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926945562200, "endTime": 33926963011700}, "additional": {"logType": "info", "children": [], "durationId": "261f5871-2641-4249-9569-8c47ae77c880"}}, {"head": {"id": "79860b29-dca5-4cd6-a604-0c92b7b4a535", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926974953800, "endTime": 33926978780400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bde8ab47-4fda-4e72-b175-25e61e5e82d6", "logId": "df1a8368-ce0d-4179-a5e0-2a81e748d694"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bde8ab47-4fda-4e72-b175-25e61e5e82d6", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926972150600}, "additional": {"logType": "detail", "children": [], "durationId": "79860b29-dca5-4cd6-a604-0c92b7b4a535"}}, {"head": {"id": "4cce9e44-1d13-4768-88f7-28748b9ed048", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926973394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56ff8da-f1e1-4f01-9921-e5ba8fee12be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926973576100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa8a980b-1af9-4920-bceb-ea857339c663", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926974973900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c1a71b-c3a6-4bdd-b853-65da339b7b53", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926976341700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f5a8eb-8946-4038-93b5-b90241d63959", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926978495100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e4b9970-04cf-476d-b2b5-b1934507db01", "name": "entry : default@GenerateMetadata cost memory 0.09416961669921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926978646700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1a8368-ce0d-4179-a5e0-2a81e748d694", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926974953800, "endTime": 33926978780400}, "additional": {"logType": "info", "children": [], "durationId": "79860b29-dca5-4cd6-a604-0c92b7b4a535"}}, {"head": {"id": "5b2f1006-300e-4d55-9d76-ef1f07bb57b1", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926987152300, "endTime": 33926988175400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3332e224-620b-427b-a2e1-bf8f52ad37bd", "logId": "b67589f9-0939-4b8f-a554-158f4e487f87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3332e224-620b-427b-a2e1-bf8f52ad37bd", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926981288200}, "additional": {"logType": "detail", "children": [], "durationId": "5b2f1006-300e-4d55-9d76-ef1f07bb57b1"}}, {"head": {"id": "746ffef0-77f6-4a20-b7a7-c3d05b1ca1af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926983277100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ba2e3fc-031e-4a9c-8f8f-e9d938ca2793", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926984076000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05176524-6203-41f6-981d-179ef14fda4c", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926987199000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb8a61ca-e433-46be-9886-78e470c3f29b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926987707100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb61c9b3-b7b3-41ae-990d-57d3c2082730", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926987817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54884324-e4bd-442d-87c9-28635782bbb9", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926987922900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1f3098-0108-40dc-9452-fc2a00e49b50", "name": "runTaskFromQueue task cost before running: 471 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926988080000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b67589f9-0939-4b8f-a554-158f4e487f87", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926987152300, "endTime": 33926988175400, "totalTime": 878900}, "additional": {"logType": "info", "children": [], "durationId": "5b2f1006-300e-4d55-9d76-ef1f07bb57b1"}}, {"head": {"id": "d164bf39-7363-45a1-b89f-8978832bcd5e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926998113100, "endTime": 33927008906500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3e2e98e1-0785-46b3-9f3b-4fbf19626475", "logId": "c89e4002-8e9d-40f8-83da-e5e3d1e9663b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e2e98e1-0785-46b3-9f3b-4fbf19626475", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926993020100}, "additional": {"logType": "detail", "children": [], "durationId": "d164bf39-7363-45a1-b89f-8978832bcd5e"}}, {"head": {"id": "28ff9c1f-f393-4cec-a6bd-eb64e8b397dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926996677100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "735a5cc4-8caf-472c-a0d6-d6557d8d181f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926997091000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba58478-7ec1-43ea-966a-99421d6c7a6a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926998150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eac12d3-1d16-44e5-b944-1a34de8df03e", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927008675400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "006ce07a-2aa0-4ed7-b929-d57d07aaf78e", "name": "entry : default@MergeProfile cost memory 0.10488128662109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927008833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c89e4002-8e9d-40f8-83da-e5e3d1e9663b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926998113100, "endTime": 33927008906500}, "additional": {"logType": "info", "children": [], "durationId": "d164bf39-7363-45a1-b89f-8978832bcd5e"}}, {"head": {"id": "e1c595d2-7ed0-41bf-870e-ac70430f2e9f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927014290800, "endTime": 33927017946200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0aa27c49-3170-4191-83e8-7390e32e5d44", "logId": "dbd5d3dc-65c0-4d38-ab97-54db2244913d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aa27c49-3170-4191-83e8-7390e32e5d44", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927011423700}, "additional": {"logType": "detail", "children": [], "durationId": "e1c595d2-7ed0-41bf-870e-ac70430f2e9f"}}, {"head": {"id": "5827c89f-8c83-4896-9677-6617b1d20a2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927012190900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aafb495e-3f56-40ac-a04f-4a756ddf338e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927012357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f457d5b8-57cc-4008-828e-0bed78786b2a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927014318900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29bb10d5-25a4-4a7e-a982-6d9f70a1761a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927015977900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d28369-9aa8-4fc5-b35c-e31ecd5d39b7", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927017689100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c724bbfe-e7fe-4ee7-89d2-60505952380a", "name": "entry : default@CreateBuildProfile cost memory 0.1016845703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927017845600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd5d3dc-65c0-4d38-ab97-54db2244913d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927014290800, "endTime": 33927017946200}, "additional": {"logType": "info", "children": [], "durationId": "e1c595d2-7ed0-41bf-870e-ac70430f2e9f"}}, {"head": {"id": "360b3f1d-cd9b-4ad8-be34-22e05c87272a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024348000, "endTime": 33927024857700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "de5707e5-2ded-403b-937e-24ea17a9970d", "logId": "43501dd8-4537-415a-aeaf-e1b9ee50e181"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de5707e5-2ded-403b-937e-24ea17a9970d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927020097900}, "additional": {"logType": "detail", "children": [], "durationId": "360b3f1d-cd9b-4ad8-be34-22e05c87272a"}}, {"head": {"id": "44835d32-ac26-4e05-952a-0883ca262e22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927020929800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b232ea-7627-4f4c-88a7-dca624cbf81a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927023372500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb83a105-86fd-40f2-a6c4-4ebcb3b30504", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024358400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c935dc-4b85-480d-ab2a-6fd71ff1587e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024528100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d9d4f2a-c7fb-449a-a7ae-6b12626406ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024615200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "468f8f64-8f7a-4b9e-acb7-a08bd23886f6", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024699900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "288cf926-4139-436c-994e-02bc9309216e", "name": "runTaskFromQueue task cost before running: 508 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024782600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43501dd8-4537-415a-aeaf-e1b9ee50e181", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927024348000, "endTime": 33927024857700, "totalTime": 415100}, "additional": {"logType": "info", "children": [], "durationId": "360b3f1d-cd9b-4ad8-be34-22e05c87272a"}}, {"head": {"id": "e84e0437-70a4-4a32-9d49-b85669e82768", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927032202500, "endTime": 33927032897900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "49049c44-4bf5-4a91-9c8c-5ce7df64678e", "logId": "1be97aa4-1e3d-4ed9-bc4e-8c5b75469976"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49049c44-4bf5-4a91-9c8c-5ce7df64678e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927026681000}, "additional": {"logType": "detail", "children": [], "durationId": "e84e0437-70a4-4a32-9d49-b85669e82768"}}, {"head": {"id": "46768343-bf32-49ba-840a-b2c645225e64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927027209300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb7e2e2-afb3-4668-8056-ae4d8c48af6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927027339400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5c5acf3-5637-45fb-bdcb-993fc1b92f54", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927032213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a716905a-0df6-43a1-89fe-8d591bcc207e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927032442500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "428d531c-4641-4b48-9ea9-989bdb874699", "name": "entry : default@GeneratePkgContextInfo cost memory 0.038543701171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927032697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7318e9a-465a-48c5-b5c9-b2f689b7c267", "name": "runTaskFromQueue task cost before running: 516 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927032794600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1be97aa4-1e3d-4ed9-bc4e-8c5b75469976", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927032202500, "endTime": 33927032897900, "totalTime": 571800}, "additional": {"logType": "info", "children": [], "durationId": "e84e0437-70a4-4a32-9d49-b85669e82768"}}, {"head": {"id": "1a59d87c-f560-4992-a9de-ac3f553067b5", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927037051500, "endTime": 33927038990300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "46c7adb1-d112-4cdf-8cce-3cc59ef2a031", "logId": "5115d57b-38ed-4720-964a-274a3b2adacf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46c7adb1-d112-4cdf-8cce-3cc59ef2a031", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927034554600}, "additional": {"logType": "detail", "children": [], "durationId": "1a59d87c-f560-4992-a9de-ac3f553067b5"}}, {"head": {"id": "420de779-c9a0-49c8-81ba-9db929e90a56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927035126300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe5eef0-5713-450e-8ec5-6fadc66b410b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927035329200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19830512-b94f-41d7-a47e-3508ff8b2965", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927037063300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5c6204-45a3-4846-849c-623f212dd823", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927038468300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7a5992-fb25-45a9-a0ed-9f211bba51c9", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927038625000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28a4c32c-4d2c-4911-9282-493fadfcf624", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927038723000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785abcd0-e7cc-4c74-bd35-8149ec1c3570", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927038773800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad1250d-8274-4494-b827-bd53e00dd8fe", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11734771728515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927038865700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "289eb345-cdf1-4c51-86cd-fdd932f2f806", "name": "runTaskFromQueue task cost before running: 522 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927038937300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5115d57b-38ed-4720-964a-274a3b2adacf", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927037051500, "endTime": 33927038990300, "totalTime": 1869900}, "additional": {"logType": "info", "children": [], "durationId": "1a59d87c-f560-4992-a9de-ac3f553067b5"}}, {"head": {"id": "75ceb0f3-a8c8-4e38-9f12-9d706e79fdb6", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042592100, "endTime": 33927043047200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2313640b-24d9-4ad2-92cd-40dea025fad1", "logId": "c9e48fe8-93b6-4923-b438-9c3feb2d592c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2313640b-24d9-4ad2-92cd-40dea025fad1", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927041272500}, "additional": {"logType": "detail", "children": [], "durationId": "75ceb0f3-a8c8-4e38-9f12-9d706e79fdb6"}}, {"head": {"id": "8db69872-1907-431c-8ccf-bd79624b34c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927041711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d3b57d-f744-4a23-87e2-108f673a8625", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927041841800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a092a40f-2eaf-4ac3-83e0-40866789e53c", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042606600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a90438-b179-49be-b66c-64e9324b045b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042777600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a48644-df8f-4d19-8325-68a9cbbfee75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042834600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8f81b53-c26e-40ab-b9d9-89b9bf514812", "name": "entry : default@BuildNativeWithCmake cost memory 0.037261962890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042911400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf72cd84-0ffc-4d2b-b610-b0269d7e641d", "name": "runTaskFromQueue task cost before running: 526 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042989700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9e48fe8-93b6-4923-b438-9c3feb2d592c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927042592100, "endTime": 33927043047200, "totalTime": 380000}, "additional": {"logType": "info", "children": [], "durationId": "75ceb0f3-a8c8-4e38-9f12-9d706e79fdb6"}}, {"head": {"id": "a05d21d1-8544-4a53-9522-68a3feab6c9b", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927047495700, "endTime": 33927051185700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b318f8ef-f55a-4b9e-8802-d8bef07ffea8", "logId": "45e636f9-ab11-4ca5-b05e-2eefa2792b5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b318f8ef-f55a-4b9e-8802-d8bef07ffea8", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927045677700}, "additional": {"logType": "detail", "children": [], "durationId": "a05d21d1-8544-4a53-9522-68a3feab6c9b"}}, {"head": {"id": "6dd8dc32-7bb5-4e7e-ac94-8d0b818a4f52", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927046455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35943f12-8994-40f1-9cf8-d5088dab9a14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927046675300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d0fb89-de79-4fb3-ad93-c9b2b7331217", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927047507900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baca4316-261c-4d18-a71e-dab21ad720aa", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927050952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf638e8a-bf5d-4a4f-85ee-f157624414bb", "name": "entry : default@MakePackInfo cost memory 0.1386566162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927051075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e636f9-ab11-4ca5-b05e-2eefa2792b5e", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927047495700, "endTime": 33927051185700}, "additional": {"logType": "info", "children": [], "durationId": "a05d21d1-8544-4a53-9522-68a3feab6c9b"}}, {"head": {"id": "072121cd-ef99-4aa2-9e7a-f69037656f43", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927056078600, "endTime": 33927059505100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "5d0079e2-22c7-44a7-8126-30efc430e866", "logId": "d684323f-718c-4616-a089-eb1ad1fd03cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d0079e2-22c7-44a7-8126-30efc430e866", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927053551100}, "additional": {"logType": "detail", "children": [], "durationId": "072121cd-ef99-4aa2-9e7a-f69037656f43"}}, {"head": {"id": "29c1e11d-93d5-47f1-8226-aa5bb7750b2b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927054091700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b7d2de-09b3-4baf-bc89-3e9dcc7438a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927054317100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50dc76b-1add-40bb-845c-65004d47a984", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927056094000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785461a5-1299-4af3-9989-795de5dae3d5", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927056688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e6f0ab-d6b2-472a-b1a0-68a7615b219d", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927057384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c16a1e2-a817-4933-96f6-1e446f293d4b", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927058913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad33c5c-9896-41db-ab93-5c62451e7899", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927059053400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa30af5-3d31-4f95-b30d-4abaab23c6e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927059157300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec72709e-1ed5-4c9d-ada5-44934cfd8399", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927059241000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aaa0909-08d4-420c-88fe-40c8af0082b6", "name": "entry : default@SyscapTransform cost memory 0.3952484130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927059367100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f341d84e-93a9-4e0e-864b-9e79a64ac795", "name": "runTaskFromQueue task cost before running: 542 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927059446300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d684323f-718c-4616-a089-eb1ad1fd03cf", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927056078600, "endTime": 33927059505100, "totalTime": 3349700}, "additional": {"logType": "info", "children": [], "durationId": "072121cd-ef99-4aa2-9e7a-f69037656f43"}}, {"head": {"id": "370b64d1-5a79-45e8-955d-01fda2e5806c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927065488300, "endTime": 33927067662200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8a4ef997-7c92-4769-ba17-45cd10f75f49", "logId": "dd8b0444-7840-4653-b9a5-b022ff866b55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a4ef997-7c92-4769-ba17-45cd10f75f49", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927062456300}, "additional": {"logType": "detail", "children": [], "durationId": "370b64d1-5a79-45e8-955d-01fda2e5806c"}}, {"head": {"id": "ae03d98c-8ae5-4357-9c3b-c57eeb582498", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927063493500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a79857-4070-4961-94cb-8436295b261a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927063658700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7c7404c-8ef1-465d-816c-fb7e74bf5045", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927065506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7c4b36-58bc-4704-9721-bd747daf6191", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927067308600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef0b303-2cb2-4301-b6bc-0a1bbba2b6d9", "name": "entry : default@ProcessProfile cost memory 0.0602874755859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927067475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8b0444-7840-4653-b9a5-b022ff866b55", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927065488300, "endTime": 33927067662200}, "additional": {"logType": "info", "children": [], "durationId": "370b64d1-5a79-45e8-955d-01fda2e5806c"}}, {"head": {"id": "58315414-5321-4453-80af-5f0cb24fb090", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927075924600, "endTime": 33927087267300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "caff3f32-0268-43f0-a26b-2eba62f367e8", "logId": "7d4bb3cc-b867-4e26-a4d9-6f07cd7bcd96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caff3f32-0268-43f0-a26b-2eba62f367e8", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927069935400}, "additional": {"logType": "detail", "children": [], "durationId": "58315414-5321-4453-80af-5f0cb24fb090"}}, {"head": {"id": "c43d71d3-615f-4d9b-9611-1372c0b2a3e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927070616900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03eb8197-b2f7-4b30-9980-8649a41538e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927070747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40ecbebe-11a9-483e-82b5-08e6699002b6", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927075939900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635d641c-c168-4f0e-b424-77e077293954", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927086840200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae777f6-6331-4464-95f0-f120eb2f17c6", "name": "entry : default@ProcessRouterMap cost memory 0.23430633544921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927087108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d4bb3cc-b867-4e26-a4d9-6f07cd7bcd96", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927075924600, "endTime": 33927087267300}, "additional": {"logType": "info", "children": [], "durationId": "58315414-5321-4453-80af-5f0cb24fb090"}}, {"head": {"id": "1d5c7ec5-ed6b-4821-9965-b036bc284a47", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927096324900, "endTime": 33927099411600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "85b015f7-8bba-43be-81c5-938c06885f54", "logId": "b39d10f0-99cf-482b-81de-cada29ecd04f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85b015f7-8bba-43be-81c5-938c06885f54", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927092497700}, "additional": {"logType": "detail", "children": [], "durationId": "1d5c7ec5-ed6b-4821-9965-b036bc284a47"}}, {"head": {"id": "30c913c0-581d-47ee-8572-6275e133ddc1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927093060700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499cd57d-f0dc-4d80-8a5f-8a0f86f7ecc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927093360700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b8f557f-2a27-4eb6-981f-b38b65053e3f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927096345300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843553c4-a116-4f55-a9e0-0af89e729ac5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927097091800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d54027f-d76a-45d7-904b-261b6d78544d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927097539600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961e35f8-e04f-4197-8929-ba6668f51d62", "name": "entry : default@BuildNativeWithNinja cost memory 0.05660247802734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927099177300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c071b2f-820e-4ac1-94c3-54d9782af2d8", "name": "runTaskFromQueue task cost before running: 582 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927099342200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b39d10f0-99cf-482b-81de-cada29ecd04f", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927096324900, "endTime": 33927099411600, "totalTime": 3020700}, "additional": {"logType": "info", "children": [], "durationId": "1d5c7ec5-ed6b-4821-9965-b036bc284a47"}}, {"head": {"id": "e5be315d-afc5-4fb0-84a5-20efeeadc02b", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927109575900, "endTime": 33927119361600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4eaa6006-6f71-4427-8d29-6bbb60f893ed", "logId": "ac0c63b3-5866-4970-b15e-be8ae2ea71be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4eaa6006-6f71-4427-8d29-6bbb60f893ed", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927103630600}, "additional": {"logType": "detail", "children": [], "durationId": "e5be315d-afc5-4fb0-84a5-20efeeadc02b"}}, {"head": {"id": "c5ddf482-914f-4421-888a-84cd29d5d17d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927104204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56739a90-4c14-4faf-a3c4-7301b984c5ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927104872800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d612e6cf-d637-4565-85ea-26074db14055", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927107431900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a46bb25-b8e7-47b4-807c-7318ec5c7459", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927113631100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df92221f-2518-423f-885c-086b3abd6e27", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927116024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa5e40b3-d5ae-44c3-b825-2dd288359d6f", "name": "entry : default@ProcessResource cost memory 0.16931915283203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927116425900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0c63b3-5866-4970-b15e-be8ae2ea71be", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927109575900, "endTime": 33927119361600}, "additional": {"logType": "info", "children": [], "durationId": "e5be315d-afc5-4fb0-84a5-20efeeadc02b"}}, {"head": {"id": "55ced8d2-a9be-4b49-8487-76a81344067d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927135369700, "endTime": 33927169104000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9ce423f0-cd98-45f2-a162-a9584bcfcd20", "logId": "3de311cc-ed95-417e-8516-5238c8c496a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ce423f0-cd98-45f2-a162-a9584bcfcd20", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927126035000}, "additional": {"logType": "detail", "children": [], "durationId": "55ced8d2-a9be-4b49-8487-76a81344067d"}}, {"head": {"id": "08d30ea2-1ea3-4108-b879-81ec6fb29646", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927126528400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00e538af-edb8-4b40-83cf-c140f45133c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927127040100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "979d492f-b4e7-4e2f-8fb2-b307913af56f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927135382600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a968e1e-e8b6-4436-8dac-681c064e6124", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927168658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d8fc95a-1f7a-4f6b-8d09-b1d76074d94e", "name": "entry : default@GenerateLoaderJson cost memory 0.7665328979492188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927168979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de311cc-ed95-417e-8516-5238c8c496a2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927135369700, "endTime": 33927169104000}, "additional": {"logType": "info", "children": [], "durationId": "55ced8d2-a9be-4b49-8487-76a81344067d"}}, {"head": {"id": "055a010f-8085-4770-a564-9a9c3ea51e83", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927191516200, "endTime": 33927199133000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "209787c2-b8ec-4aee-9349-2bec06a0909a", "logId": "f52c8aa0-05cc-4037-b24d-1fb608e04552"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "209787c2-b8ec-4aee-9349-2bec06a0909a", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927188482400}, "additional": {"logType": "detail", "children": [], "durationId": "055a010f-8085-4770-a564-9a9c3ea51e83"}}, {"head": {"id": "3765317b-08fa-45d0-9d4a-acf43ba42f65", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927190123900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22560327-f4a0-4616-950e-6e5492c5b5aa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927190252600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e1074ed-d59f-44c8-8387-9ac925a8cd50", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927191536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee19d38-5d67-4055-b0f1-4e2bcbb9c698", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927197181200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34deec3f-cb11-44d4-9bd2-3859e7159dae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927197339900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0f0a7a-bd26-4e94-aee4-df3c42f71306", "name": "entry : default@ProcessLibs cost memory 0.125213623046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927198666300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "692db729-3ca1-4553-a544-6a129edc8ee1", "name": "runTaskFromQueue task cost before running: 682 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927198961200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52c8aa0-05cc-4037-b24d-1fb608e04552", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927191516200, "endTime": 33927199133000, "totalTime": 7334700}, "additional": {"logType": "info", "children": [], "durationId": "055a010f-8085-4770-a564-9a9c3ea51e83"}}, {"head": {"id": "922cc012-a6af-466b-9890-42d680130e8c", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927210559600, "endTime": 33927237585700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1e5159de-6c4d-4b34-8062-bb4396cd6add", "logId": "c828ccb8-9dbc-459a-a205-e2774e04d255"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e5159de-6c4d-4b34-8062-bb4396cd6add", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927206490900}, "additional": {"logType": "detail", "children": [], "durationId": "922cc012-a6af-466b-9890-42d680130e8c"}}, {"head": {"id": "740bd94c-065c-4d97-869b-f59c18ce03f2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927206939400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b45cc7-f3ad-48a7-8dca-b4ba923b64a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927207081900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9158f49c-5132-423f-a076-e6484d1a6fc7", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927208027400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a68ef8-0881-414d-847a-bf2fe05a65e3", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927210585000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac66bd96-498e-4f4c-bfa0-75bd293f1406", "name": "Incremental task entry:default@CompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927237227800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27bf2947-9d02-4cac-9ab1-30c778a4b027", "name": "entry : default@CompileResource cost memory 1.4084625244140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927237446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c828ccb8-9dbc-459a-a205-e2774e04d255", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927210559600, "endTime": 33927237585700}, "additional": {"logType": "info", "children": [], "durationId": "922cc012-a6af-466b-9890-42d680130e8c"}}, {"head": {"id": "02f72e1e-c154-4602-8a53-7ea97b87cf0b", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927249528600, "endTime": 33927251572000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6192629b-d627-4d45-90f0-a67d96144246", "logId": "e81feaea-022e-4943-b59d-08a47e6b721d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6192629b-d627-4d45-90f0-a67d96144246", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927243390800}, "additional": {"logType": "detail", "children": [], "durationId": "02f72e1e-c154-4602-8a53-7ea97b87cf0b"}}, {"head": {"id": "65199154-a613-466c-9480-6198b521bb65", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927243835600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b37904c7-0c38-4ca8-b10b-164be63a4a42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927243980500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3079cdb8-e86f-4774-bc71-691704f1484b", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927249540600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7b0893a-2af0-4db5-a1fc-52804b82517d", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927250289300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f34f2b-4278-4aa6-927c-8c6d06dd3611", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927251384600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90940ca0-595e-448b-8a2b-8bed3aa60dbe", "name": "entry : default@DoNativeStrip cost memory 0.07431793212890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927251501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81feaea-022e-4943-b59d-08a47e6b721d", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927249528600, "endTime": 33927251572000}, "additional": {"logType": "info", "children": [], "durationId": "02f72e1e-c154-4602-8a53-7ea97b87cf0b"}}, {"head": {"id": "81de471f-caa5-475b-9a63-f6511bdc48c4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927262214800, "endTime": 33929302899700}, "additional": {"children": ["d29423d7-30c1-49cd-b848-9e64752fd29c", "f7cba9f9-5a55-4b22-937b-a7570b1ce38c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "090ce269-4f53-497c-bf71-fdbd194a6248", "logId": "8fd90909-0801-4dd3-96b7-c6e407b32308"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "090ce269-4f53-497c-bf71-fdbd194a6248", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927254403400}, "additional": {"logType": "detail", "children": [], "durationId": "81de471f-caa5-475b-9a63-f6511bdc48c4"}}, {"head": {"id": "9e9ca728-3115-40f6-b7b1-31d9686e829d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927255097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92245c37-bf81-42e3-b6a5-7c77b520cb7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927255643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a87300-a710-443e-bdf4-ad9bd829b357", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927262230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa740722-c217-469b-b9b4-e36c176280f6", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927279571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347a7ec4-c587-4155-91e2-ea0c047296de", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927279746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25ab9408-019b-4697-9123-53f2c9859f9e", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927292056000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af123359-bd14-4ba6-98a9-69f8c7b2493b", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927292458900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16ea62e-ee53-4783-ac6d-879a6dd5d89f", "name": "default@CompileArkTS work[109] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927293320200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d29423d7-30c1-49cd-b848-9e64752fd29c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33927403230000, "endTime": 33929292438900}, "additional": {"children": ["ee16ef36-f75e-4ca2-9693-930c2ae53cbf", "040a9808-0b75-449a-b02d-cd70356379d0", "b7b42ecb-c25d-4a58-98ed-341efc773fc7", "46efccf9-d48c-45a7-a439-540b6bc24734", "0248cde4-4f02-4c2a-a0c2-c8a080a38600", "ccaee2a3-96c3-4c92-bbc5-3e076e61fc61"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "81de471f-caa5-475b-9a63-f6511bdc48c4", "logId": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a07b9bb-c054-4158-baeb-da826acdb480", "name": "default@CompileArkTS work[109] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927294220300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb024b8a-1037-403f-9671-a368dfeafebc", "name": "default@CompileArkTS work[109] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927294724100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ecf7f7-1aba-481c-b49d-0fc4a9dded32", "name": "CopyResources startTime: 33927294919200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927294924500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a1aab1-38bd-49a5-9255-f9339bc572c7", "name": "default@CompileArkTS work[110] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927295010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7cba9f9-5a55-4b22-937b-a7570b1ce38c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33928534156200, "endTime": 33928549607400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "81de471f-caa5-475b-9a63-f6511bdc48c4", "logId": "93d8d83b-71a9-48c3-9081-4555c8e94ea0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f1c275c-ae44-4b08-8437-3f0449b8f589", "name": "default@CompileArkTS work[110] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927295855100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da15675-6752-4a3f-919f-ea5379ee7cc5", "name": "default@CompileArkTS work[110] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927295955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3fd95af-7614-48ae-8536-f3c0c49f397d", "name": "entry : default@CompileArkTS cost memory 1.5900115966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927296083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38751eec-9399-46a0-80fd-5134bb08cf07", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927301347000, "endTime": 33927304273700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "c4000f76-171d-4411-b180-ea25e92067f4", "logId": "0647430f-c6b7-4900-9c57-d09f7c568d54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4000f76-171d-4411-b180-ea25e92067f4", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927297668800}, "additional": {"logType": "detail", "children": [], "durationId": "38751eec-9399-46a0-80fd-5134bb08cf07"}}, {"head": {"id": "a42dd899-1b40-4740-b386-057e7e7dc412", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927298075100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03ff2b7b-929b-480b-b092-8e95402e7f4d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927298164400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82da5fc-e6e6-4593-931e-01d5750b2b8e", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927301357700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdcf20dd-c9c7-4c5e-9d86-38432bfa9692", "name": "entry : default@BuildJS cost memory 0.12709808349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927304087300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b115fb31-bdc3-4056-bfc5-03acc756486c", "name": "runTaskFromQueue task cost before running: 787 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927304212600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0647430f-c6b7-4900-9c57-d09f7c568d54", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927301347000, "endTime": 33927304273700, "totalTime": 2846000}, "additional": {"logType": "info", "children": [], "durationId": "38751eec-9399-46a0-80fd-5134bb08cf07"}}, {"head": {"id": "c08867c5-95fa-46ea-9899-7c07770e6cc5", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927309191600, "endTime": 33927310943000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "30c82217-f079-40ae-952d-5850bcf35bf5", "logId": "d5a2bb1d-5b5a-48f0-8fdf-9cc724233ab0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30c82217-f079-40ae-952d-5850bcf35bf5", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927306265800}, "additional": {"logType": "detail", "children": [], "durationId": "c08867c5-95fa-46ea-9899-7c07770e6cc5"}}, {"head": {"id": "fc77be89-1b0f-469d-81e4-690a45be669f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927306719200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f2179f-e33c-4f45-a77c-e166d2b69603", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927306814700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c31ec15-1e1a-420d-a84e-3493099d06d5", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927309203600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa378e7-fd2a-47eb-a5ea-513427337725", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927309556600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796f4591-298d-404c-96ec-6aeeaa3b10bc", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927310748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bb7f89f-a8d2-4c8f-89a8-6c1a0dfc46e6", "name": "entry : default@CacheNativeLibs cost memory 0.08769989013671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927310875000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a2bb1d-5b5a-48f0-8fdf-9cc724233ab0", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927309191600, "endTime": 33927310943000}, "additional": {"logType": "info", "children": [], "durationId": "c08867c5-95fa-46ea-9899-7c07770e6cc5"}}, {"head": {"id": "f71c1953-d0a5-45b3-81b9-58fbc7503f5f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927402527300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a70dc0a-58bb-4a12-8d68-5cf824824408", "name": "default@CompileArkTS work[109] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927402908600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "937456a3-9f81-438b-83d9-9f8fd7e34400", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927403025200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d864af8-a770-4560-88c1-091f64e9a420", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927403077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa062ec7-2c29-4833-9879-c654abd2c86a", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927403350500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92d66628-5826-46eb-ac65-c20c71316af2", "name": "default@CompileArkTS work[110] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927405012900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e568158d-ca81-413a-afd7-cf5f44364422", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33928549937900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5868645c-fdc7-4aa8-a6fc-919b1e10c734", "name": "CopyResources is end, endTime: 33928550106400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33928550114800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0be294c-2662-4a63-a382-8a854d350fb2", "name": "default@CompileArkTS work[110] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33928550388400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d8d83b-71a9-48c3-9081-4555c8e94ea0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33928534156200, "endTime": 33928549607400}, "additional": {"logType": "info", "children": [], "durationId": "f7cba9f9-5a55-4b22-937b-a7570b1ce38c", "parent": "8fd90909-0801-4dd3-96b7-c6e407b32308"}}, {"head": {"id": "dc73e7a9-d231-4fd5-aeab-cabb3466ca34", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33928766280100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5917ce6-2b13-49ed-8cd7-04fbe0740fca", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929292729900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee16ef36-f75e-4ca2-9693-930c2ae53cbf", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33927403356100, "endTime": 33927406687900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d29423d7-30c1-49cd-b848-9e64752fd29c", "logId": "018918b5-adf5-436a-af56-46304e9a7f9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "018918b5-adf5-436a-af56-46304e9a7f9d", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927403356100, "endTime": 33927406687900}, "additional": {"logType": "info", "children": [], "durationId": "ee16ef36-f75e-4ca2-9693-930c2ae53cbf", "parent": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}}, {"head": {"id": "040a9808-0b75-449a-b02d-cd70356379d0", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33927406706100, "endTime": 33927406801800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d29423d7-30c1-49cd-b848-9e64752fd29c", "logId": "34930abd-5140-4182-b45e-2764c0344f5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34930abd-5140-4182-b45e-2764c0344f5f", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927406706100, "endTime": 33927406801800}, "additional": {"logType": "info", "children": [], "durationId": "040a9808-0b75-449a-b02d-cd70356379d0", "parent": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}}, {"head": {"id": "b7b42ecb-c25d-4a58-98ed-341efc773fc7", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33927406813900, "endTime": 33927406852200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d29423d7-30c1-49cd-b848-9e64752fd29c", "logId": "6e272a99-79a6-47e6-b952-31dedcd90ac3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e272a99-79a6-47e6-b952-31dedcd90ac3", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927406813900, "endTime": 33927406852200}, "additional": {"logType": "info", "children": [], "durationId": "b7b42ecb-c25d-4a58-98ed-341efc773fc7", "parent": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}}, {"head": {"id": "46efccf9-d48c-45a7-a439-540b6bc24734", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33927406872100, "endTime": 33929157254800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d29423d7-30c1-49cd-b848-9e64752fd29c", "logId": "7597fee7-b365-4646-a6b5-e0d0225c514d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7597fee7-b365-4646-a6b5-e0d0225c514d", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927406872100, "endTime": 33929157254800}, "additional": {"logType": "info", "children": [], "durationId": "46efccf9-d48c-45a7-a439-540b6bc24734", "parent": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}}, {"head": {"id": "0248cde4-4f02-4c2a-a0c2-c8a080a38600", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929157275200, "endTime": 33929174430200}, "additional": {"children": ["587d8e02-feda-4c87-9451-1fcbfab2289f", "494a9291-73e7-4be6-a058-cf3672709b6b", "11e974fb-f3b8-48e8-958e-7461d3e8985e"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d29423d7-30c1-49cd-b848-9e64752fd29c", "logId": "2c05323d-6bf7-443a-9068-3088a5de413e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c05323d-6bf7-443a-9068-3088a5de413e", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929157275200, "endTime": 33929174430200}, "additional": {"logType": "info", "children": ["c0a8da34-7d71-407b-a06d-a6e0a346b4d3", "e6dabff8-fbcd-406c-bb9c-0a709ab7cd5f", "84ea168e-e13f-493c-b718-6559271fa3dd"], "durationId": "0248cde4-4f02-4c2a-a0c2-c8a080a38600", "parent": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}}, {"head": {"id": "587d8e02-feda-4c87-9451-1fcbfab2289f", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929157290400, "endTime": 33929157297300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0248cde4-4f02-4c2a-a0c2-c8a080a38600", "logId": "c0a8da34-7d71-407b-a06d-a6e0a346b4d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0a8da34-7d71-407b-a06d-a6e0a346b4d3", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929157290400, "endTime": 33929157297300}, "additional": {"logType": "info", "children": [], "durationId": "587d8e02-feda-4c87-9451-1fcbfab2289f", "parent": "2c05323d-6bf7-443a-9068-3088a5de413e"}}, {"head": {"id": "494a9291-73e7-4be6-a058-cf3672709b6b", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929157300800, "endTime": 33929165291300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0248cde4-4f02-4c2a-a0c2-c8a080a38600", "logId": "e6dabff8-fbcd-406c-bb9c-0a709ab7cd5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6dabff8-fbcd-406c-bb9c-0a709ab7cd5f", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929157300800, "endTime": 33929165291300}, "additional": {"logType": "info", "children": [], "durationId": "494a9291-73e7-4be6-a058-cf3672709b6b", "parent": "2c05323d-6bf7-443a-9068-3088a5de413e"}}, {"head": {"id": "11e974fb-f3b8-48e8-958e-7461d3e8985e", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929165484000, "endTime": 33929174414600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0248cde4-4f02-4c2a-a0c2-c8a080a38600", "logId": "84ea168e-e13f-493c-b718-6559271fa3dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84ea168e-e13f-493c-b718-6559271fa3dd", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929165484000, "endTime": 33929174414600}, "additional": {"logType": "info", "children": [], "durationId": "11e974fb-f3b8-48e8-958e-7461d3e8985e", "parent": "2c05323d-6bf7-443a-9068-3088a5de413e"}}, {"head": {"id": "ccaee2a3-96c3-4c92-bbc5-3e076e61fc61", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929174447100, "endTime": 33929292288500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d29423d7-30c1-49cd-b848-9e64752fd29c", "logId": "9bc40eac-230e-4164-be36-e4e20562ead6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bc40eac-230e-4164-be36-e4e20562ead6", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929174447100, "endTime": 33929292288500}, "additional": {"logType": "info", "children": [], "durationId": "ccaee2a3-96c3-4c92-bbc5-3e076e61fc61", "parent": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e"}}, {"head": {"id": "90aed952-4e4f-47fa-80be-88fd3786a50c", "name": "default@CompileArkTS work[109] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929302563400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9b9424d-1ee1-4f08-9d59-b138b4b4c22e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33927403230000, "endTime": 33929292438900}, "additional": {"logType": "info", "children": ["018918b5-adf5-436a-af56-46304e9a7f9d", "34930abd-5140-4182-b45e-2764c0344f5f", "6e272a99-79a6-47e6-b952-31dedcd90ac3", "7597fee7-b365-4646-a6b5-e0d0225c514d", "2c05323d-6bf7-443a-9068-3088a5de413e", "9bc40eac-230e-4164-be36-e4e20562ead6"], "durationId": "d29423d7-30c1-49cd-b848-9e64752fd29c", "parent": "8fd90909-0801-4dd3-96b7-c6e407b32308"}}, {"head": {"id": "8fd90909-0801-4dd3-96b7-c6e407b32308", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33927262214800, "endTime": 33929302899700, "totalTime": 1923224100}, "additional": {"logType": "info", "children": ["d9b9424d-1ee1-4f08-9d59-b138b4b4c22e", "93d8d83b-71a9-48c3-9081-4555c8e94ea0"], "durationId": "81de471f-caa5-475b-9a63-f6511bdc48c4"}}, {"head": {"id": "2dfce9e4-6c88-4e70-b28b-fb1b194516f0", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929309667900, "endTime": 33929312587300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "41d73d70-a2f9-42aa-b048-64f512c8c1c0", "logId": "3033a7b1-7015-4cd2-af57-c00b63854ab9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41d73d70-a2f9-42aa-b048-64f512c8c1c0", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929307530200}, "additional": {"logType": "detail", "children": [], "durationId": "2dfce9e4-6c88-4e70-b28b-fb1b194516f0"}}, {"head": {"id": "2664e9cf-deee-456f-a0ce-800b5c59580e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929307914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bc68b8-cfbb-4df6-85d7-edc64f4474dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929308013600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b45878f-4f1b-492b-a361-dacf578b9802", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929309679200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7432a8a3-9f1a-4af4-9b21-c925dac932a8", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929309936200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30df35f-4bf6-4a9c-b328-c541e7d1fa47", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929311570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84f00651-6db4-4992-9a89-85d4faa87b1a", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07265472412109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929312011300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3033a7b1-7015-4cd2-af57-c00b63854ab9", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929309667900, "endTime": 33929312587300}, "additional": {"logType": "info", "children": [], "durationId": "2dfce9e4-6c88-4e70-b28b-fb1b194516f0"}}, {"head": {"id": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929330263200, "endTime": 33929941209500}, "additional": {"children": ["ecef28da-f30b-4802-bad7-130d761ce5be", "f6b94e17-36e7-4852-8700-57a1e101ad93", "b66a0b2e-0e3a-4f49-afd7-99ac7016fe03"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "c55d9113-7f69-4be0-9b52-4ed7652f80c6", "logId": "671fd44a-4043-4912-a2db-c0eadc554c75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c55d9113-7f69-4be0-9b52-4ed7652f80c6", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929318620600}, "additional": {"logType": "detail", "children": [], "durationId": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1"}}, {"head": {"id": "5bd15253-98d5-49a6-bbe9-9121983c88e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929319423100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cbafabe-ff1c-452b-b687-b0c87532cd6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929319548600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4fc7cb-0f75-4283-a7c3-3e7a81d70608", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929330274500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ca0e1f3-597b-4d5b-b81a-e852e92f684e", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929342755600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b467a0a-21f9-4be3-9a88-ea8d73e1e03a", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929342954800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb52e46-cb1f-4eb6-a4b6-021b9680442b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929343157000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30a0ff5d-e000-4621-8b8f-e725ae0595bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929343232200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecef28da-f30b-4802-bad7-130d761ce5be", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929345227900, "endTime": 33929346906300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1", "logId": "4a925d4d-0edc-4bdf-8843-e422ae2dc232"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f4a0266-4a74-497f-91ff-ffa1ee2264a5", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929346749000}, "additional": {"logType": "debug", "children": [], "durationId": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1"}}, {"head": {"id": "4a925d4d-0edc-4bdf-8843-e422ae2dc232", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929345227900, "endTime": 33929346906300}, "additional": {"logType": "info", "children": [], "durationId": "ecef28da-f30b-4802-bad7-130d761ce5be", "parent": "671fd44a-4043-4912-a2db-c0eadc554c75"}}, {"head": {"id": "f6b94e17-36e7-4852-8700-57a1e101ad93", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929347749600, "endTime": 33929351118500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1", "logId": "70878555-6406-4819-8f02-f2e9ed56915e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a3140ec-4c38-4cb6-90f4-4ecf9d82fa0d", "name": "default@PackageHap work[111] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929348534300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66a0b2e-0e3a-4f49-afd7-99ac7016fe03", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929464698900, "endTime": 33929940736700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1", "logId": "cd1cc73f-d13c-49e2-8bca-bc2f4f6bb4a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bdd09b1-6e29-46cc-bb9e-f5195b6eae8c", "name": "default@PackageHap work[111] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929350938400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9ddf37-a618-4dbb-8448-14fcd84a2739", "name": "default@PackageHap work[111] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929351056500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70878555-6406-4819-8f02-f2e9ed56915e", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929347749600, "endTime": 33929351118500}, "additional": {"logType": "info", "children": [], "durationId": "f6b94e17-36e7-4852-8700-57a1e101ad93", "parent": "671fd44a-4043-4912-a2db-c0eadc554c75"}}, {"head": {"id": "fec784a1-dc22-4dbf-9b42-04d949d122fd", "name": "entry : default@PackageHap cost memory -5.070899963378906", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929355242600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9bfc88-a69f-46dc-b5a0-cc29ec94e596", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929464318600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f070fc60-bf94-4b64-9a8c-f5420ea7fe58", "name": "default@PackageHap work[111] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929464530100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "565a0bb9-3c89-44a8-addd-2773afccbb47", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929525750500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db7451fe-9029-4a4e-a38b-e4cdf3572ed6", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929940815000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9af3360-5f8f-4513-9612-918dbc16604d", "name": "default@PackageHap work[111] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929940998000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1cc73f-d13c-49e2-8bca-bc2f4f6bb4a8", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33929464698900, "endTime": 33929940736700}, "additional": {"logType": "info", "children": [], "durationId": "b66a0b2e-0e3a-4f49-afd7-99ac7016fe03", "parent": "671fd44a-4043-4912-a2db-c0eadc554c75"}}, {"head": {"id": "736c54d5-50bc-4969-a4be-afd09a0094de", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929941079100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "671fd44a-4043-4912-a2db-c0eadc554c75", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929330263200, "endTime": 33929941209500, "totalTime": 501146700}, "additional": {"logType": "info", "children": ["4a925d4d-0edc-4bdf-8843-e422ae2dc232", "70878555-6406-4819-8f02-f2e9ed56915e", "cd1cc73f-d13c-49e2-8bca-bc2f4f6bb4a8"], "durationId": "548a0a78-65aa-4c67-8c77-b5d1ec19a1d1"}}, {"head": {"id": "ab1b2d58-bafc-48aa-9095-9e1857805f53", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929948309600, "endTime": 33929950099300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "ecd1626a-b94c-4191-b04b-26158acf369c", "logId": "4da115b6-5a0a-482a-83b8-46ce05822698"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecd1626a-b94c-4191-b04b-26158acf369c", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929945478300}, "additional": {"logType": "detail", "children": [], "durationId": "ab1b2d58-bafc-48aa-9095-9e1857805f53"}}, {"head": {"id": "a30d4828-e836-46d3-9932-e7492c2cf424", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929945877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7016b920-9b77-4237-b5f8-42e50fdc5e83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929945972200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b7612b1-eadb-4d20-ac48-06c68ee17b72", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929948318900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17241192-c723-48a9-8203-25955401a115", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929948691200}, "additional": {"logType": "warn", "children": [], "durationId": "ab1b2d58-bafc-48aa-9095-9e1857805f53"}}, {"head": {"id": "975d07c4-847a-4837-b8a4-a1af795079a6", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929949220700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da8d9d53-6180-49c0-b709-31a8bd336e63", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929949305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6727a7be-eb12-4d14-840d-e02c98d1c4dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929949380200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25779a96-8a60-4672-b05b-9f96f097e476", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929949429600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e802c1-6deb-4d63-bbaf-f07e264372f0", "name": "entry : default@SignHap cost memory 0.11454010009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929949941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a212f3a0-95e8-49e1-b8a5-6617d79181c5", "name": "runTaskFromQueue task cost before running: 3 s 433 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929950040300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da115b6-5a0a-482a-83b8-46ce05822698", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929948309600, "endTime": 33929950099300, "totalTime": 1711500}, "additional": {"logType": "info", "children": [], "durationId": "ab1b2d58-bafc-48aa-9095-9e1857805f53"}}, {"head": {"id": "51b441ba-93f9-44f5-b3fb-d42f1551c4fa", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929953029100, "endTime": 33929958016100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cd0afbba-e1ed-4994-ac9d-46c6e0e6729e", "logId": "b7e05f4a-0a87-4bf7-b4cf-03f40cfe09cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd0afbba-e1ed-4994-ac9d-46c6e0e6729e", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929951819500}, "additional": {"logType": "detail", "children": [], "durationId": "51b441ba-93f9-44f5-b3fb-d42f1551c4fa"}}, {"head": {"id": "ef0b5303-ad50-4517-8a89-5aa5440f3634", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929952171100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f833d6d-6b77-4f33-b21c-cfb1ae13a501", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929952259800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621b2632-6ae5-4842-99c7-f63bd2d438a6", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929953037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b4c3d1c-37cb-48a9-bd70-33fe3b650b97", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929957673100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db2483e-f5c8-4e01-b005-680d2294674d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929957803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ace6960-a4d6-459d-a094-0460f55229fc", "name": "entry : default@CollectDebugSymbol cost memory 0.2417144775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929957884100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0061c9ff-871e-472b-92d9-ff4a7430410f", "name": "runTaskFromQueue task cost before running: 3 s 441 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929957960700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e05f4a-0a87-4bf7-b4cf-03f40cfe09cb", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929953029100, "endTime": 33929958016100, "totalTime": 4911100}, "additional": {"logType": "info", "children": [], "durationId": "51b441ba-93f9-44f5-b3fb-d42f1551c4fa"}}, {"head": {"id": "f75b5d91-8f21-4900-b7dc-79ac25bbd91c", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929960153900, "endTime": 33929960561500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fa8c2785-8ed5-4e7b-a8b3-07176e5b94ef", "logId": "27f15eae-3111-4970-93b2-cf06af707da8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa8c2785-8ed5-4e7b-a8b3-07176e5b94ef", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929960070200}, "additional": {"logType": "detail", "children": [], "durationId": "f75b5d91-8f21-4900-b7dc-79ac25bbd91c"}}, {"head": {"id": "0e22e6ff-475e-43f3-9f63-9eb9aa04305d", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929960162800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c55baf40-50c3-4dfa-83d8-2ee5de81163a", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929960408300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d2910f-711e-4943-94f3-642c88e9cf7f", "name": "runTaskFromQueue task cost before running: 3 s 443 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929960502200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f15eae-3111-4970-93b2-cf06af707da8", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929960153900, "endTime": 33929960561500, "totalTime": 329800}, "additional": {"logType": "info", "children": [], "durationId": "f75b5d91-8f21-4900-b7dc-79ac25bbd91c"}}, {"head": {"id": "654d5d7a-8f4a-4ee7-98cc-bad1cd89720b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929968923800, "endTime": 33929968943000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f21eb2b-d458-4adf-8911-ac2a79255ec0", "logId": "c6c2c1c0-d3c5-4398-ac29-e1b9d27eed7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6c2c1c0-d3c5-4398-ac29-e1b9d27eed7c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929968923800, "endTime": 33929968943000}, "additional": {"logType": "info", "children": [], "durationId": "654d5d7a-8f4a-4ee7-98cc-bad1cd89720b"}}, {"head": {"id": "2cc49733-7d6c-4184-a000-44e983e033f0", "name": "BUILD SUCCESSFUL in 3 s 452 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929969016600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d0412a09-da0e-4054-b55d-7598a81b0c4a", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33926517699700, "endTime": 33929969644000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 54}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "569e421f-1dc2-4376-999a-3b52d16ca224", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929969791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3af0bc6-be22-41e7-9823-58ab0eaa7de1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929969893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717436e0-4af5-42a6-ab58-2c83263e7571", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929969951400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9712dbe-f654-4cf2-be6b-9d2225b7e4b2", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929970001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "812b944a-61d9-4e50-87fd-d45c1a0a3e60", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929970064600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b6ab6a8-cdfb-4a57-913d-7b4711645a89", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929970472800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab10155-56ac-4b2e-8c43-022629ef1fb7", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929971080400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "268f096b-00d6-4228-8e40-1c755e975631", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929971384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67898d81-413a-4d92-a1c6-078548f025d5", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929971458500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f794d3-69ae-47c3-93d2-d7bb8403126a", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929971519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92704181-b7ce-4563-85d4-96f0f79a18b5", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929971922400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6a9518-1503-4c6e-afe8-42da37426db7", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929972904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cdf23b2-bf08-481d-9bd3-1c601879e9d5", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973195900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811903c4-cb28-4bc0-af64-0df6b85d4c7f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973270700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "333d2ac6-8998-4054-a4b7-3f1e10c01a43", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973329800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a998af-9844-4134-b081-bbc5d908df2a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b099fa-eb91-4b77-80f3-999667fccba7", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973428100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983ed2a1-7131-4318-99d4-ed85ffeb0d92", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973750700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4d220a8-4d89-4657-a322-2bcce5c8fc0d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929973956400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31357339-3b9f-412a-ad18-7e04c8d1ef70", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929974187500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a0363a-427f-4917-b879-2c538fc66a70", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929974548900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4260e902-01ac-42a8-b47a-3ac2b039c2e4", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929974692700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de329978-6adc-46c7-9379-93d44b48251e", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929974775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434fc67c-01dc-4d88-ae8f-9f6239fceacd", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929976569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "369585aa-9e37-4ce1-a5fb-b4ef8b6ab276", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929977170500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d84f45b2-381d-4506-9f9b-66575c95f1d9", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929978101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b094c9-20d3-46a9-bd68-305846d8d36b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929978337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8a51bb-b135-4764-8d28-d9131ed54895", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929978521300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239868e4-9a4a-456a-b40e-2a4b478cb680", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929978967800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdc157af-248f-4a90-abfa-a25afcb4b656", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929979037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933316b7-cb2b-4b90-8641-0c471cfd72fe", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929979263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "625bed9f-9fee-4fbf-bcf9-3baaddde33d2", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929979512100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e292e641-1369-4d41-b8e0-84df9de5cf55", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929980057000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0420ab8c-df17-4017-be85-a0e95a826ac3", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929981303400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc29cd9-f9b9-4551-b19a-75cd54e818f7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929981804300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d1c5b5-af7c-402a-9b23-03c2ac34b828", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929982582800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27857d1b-4f46-4e0e-9f90-04a80e1a957a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929982918700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341539df-74e4-4773-b195-d1470c9a4940", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929983202900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63375763-bdf6-4d95-8dcd-207f9a5fff48", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929983909800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ddf78d8-2c93-4199-b9eb-13d1f15489f7", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929984193400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e4baf1-c6de-4dfe-8920-99433b9b7e1c", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929984274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04841f98-8b81-48da-ae86-b181553f9ec8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929984324100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0360b4e-5a3e-4daf-8837-4f83d2ed43be", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929985268500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd2a26b-a46a-47f6-8b95-7eabdbcff849", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929985525800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336217cc-6be5-406e-88fe-23d03a501393", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929985736700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89266e44-cf0f-4fbe-8cc0-9d76acc0365b", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929993800800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30c8338f-e51e-4e48-b466-cd1b5f508575", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929994042800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb15a1ac-c7b3-4972-9dfc-08947dfba94d", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929994916500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62803137-975b-4004-a74f-5138a947dcc6", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929995047600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1025c82d-5957-47e1-917b-19e419f7c29d", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929995354200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7991b86-a108-4cd1-a10e-c209ba38ce9c", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929996722600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec7c1c3-ed47-499e-aa66-4b9c1f3b05ec", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929997024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84dd87e8-29bb-4fcd-8ffa-f5ebb927ac34", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929997252500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e1648e0-737c-4223-a2cf-34a85a6d1518", "name": "Incremental task entry:default@PackageHap post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929997494300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43ad4863-5d94-4d35-96be-f185b03dd481", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929997654600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb1c22b-ea19-4dad-bd07-027d68d5685b", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929997723400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53297eb7-167a-4f71-b122-c03df9aa4e7a", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33929997907600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70fc5789-c348-4e92-b8dd-9e85f67104a7", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930000038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e690b5af-f1e2-48ee-a140-45983f310d3b", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930000267800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d3cb34-c507-46be-8d9a-b7ac50984703", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930000507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b0e6dd-0a0c-4793-8c59-b21da439b8f3", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930000767800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}