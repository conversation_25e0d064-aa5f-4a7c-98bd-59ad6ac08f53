{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "0be8f7b3-d478-4c10-9ea6-3fd1cf01e6b8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418995476000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b13507b-4d98-4814-a90e-a6e25888ec7b", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30419010115100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0049bc99-7855-4f8e-bd2a-9d76aeb16ad2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30419027495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc80848d-1776-4b06-9f35-c6eb31f4864b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30419027745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca91c38-5292-4b44-a33e-f2dae9e5542a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439729090900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439734357800, "endTime": 30440110234300}, "additional": {"children": ["e60cccbb-ddd6-4795-a27b-b8dbfc11910f", "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "87f151eb-9651-4442-ae60-e05579c0c6ca", "c756c99c-3343-4bde-a00d-361c98826be6", "5c40a6ac-1713-47bd-9f8c-e5411cc0c7b5", "66a70f7b-662b-426c-8e19-a83e72997f93", "75a1da1c-1bb2-4537-aeec-7b0c1e1473c6"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60cccbb-ddd6-4795-a27b-b8dbfc11910f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439734359000, "endTime": 30439746315700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "a24d4fce-81e8-44c4-ab6f-dff114265c6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439746364100, "endTime": 30440109068100}, "additional": {"children": ["859e514f-af9e-435b-adf6-6d9b74286ade", "2376f890-530e-4b8c-bfed-17081ffc1176", "227af31c-e62c-4a9a-990a-c0bb7a482900", "abdc85ea-cf22-4301-ac28-d02e43b251f9", "9a50bf2b-b18c-4f80-af2d-6cfb660ed519", "e845d9b9-ae7a-49d3-8647-918251aae168", "8f1e45a1-e7a0-45ce-bbf2-82f5ff7333b8", "d07f5da9-4af6-4f50-b64e-e8f916014afb", "43b2b4d1-539e-4d1a-b924-77187677c435"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "5b2b0334-982f-467b-96b5-aac686a3c775"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87f151eb-9651-4442-ae60-e05579c0c6ca", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440109090800, "endTime": 30440110223700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "0d98af74-43f2-4efc-b7a9-055180684b7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c756c99c-3343-4bde-a00d-361c98826be6", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440110227800, "endTime": 30440110228500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "c97143e2-ae98-4fa2-9ae2-a8296d111a21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c40a6ac-1713-47bd-9f8c-e5411cc0c7b5", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439736647900, "endTime": 30439736681100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "7ec14704-ade9-4881-bd04-72a5327101b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ec14704-ade9-4881-bd04-72a5327101b0", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439736647900, "endTime": 30439736681100}, "additional": {"logType": "info", "children": [], "durationId": "5c40a6ac-1713-47bd-9f8c-e5411cc0c7b5", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "66a70f7b-662b-426c-8e19-a83e72997f93", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439740850000, "endTime": 30439740872600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "f438142e-d1d1-45ba-b840-cbfa1c1ebc1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f438142e-d1d1-45ba-b840-cbfa1c1ebc1f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439740850000, "endTime": 30439740872600}, "additional": {"logType": "info", "children": [], "durationId": "66a70f7b-662b-426c-8e19-a83e72997f93", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "537e0a77-b414-4ea7-a3b7-4327856f540b", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439740915500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a205c2f4-3dd7-492f-b655-947e57fa9af0", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439746173800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a24d4fce-81e8-44c4-ab6f-dff114265c6e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439734359000, "endTime": 30439746315700}, "additional": {"logType": "info", "children": [], "durationId": "e60cccbb-ddd6-4795-a27b-b8dbfc11910f", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "859e514f-af9e-435b-adf6-6d9b74286ade", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439751318000, "endTime": 30439751326800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "75c17011-780f-4304-81e7-1f846be72861"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2376f890-530e-4b8c-bfed-17081ffc1176", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439751341900, "endTime": 30439754525100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "d9ea7f4f-e17b-4a36-9602-d12f01707c96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "227af31c-e62c-4a9a-990a-c0bb7a482900", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439754541400, "endTime": 30439898935700}, "additional": {"children": ["f4e3fcfe-d024-4ff8-b75e-a1a3e418bc7d", "a90964b5-2238-4961-9028-e44432b564a7", "b52d0ba6-cb84-409c-8c1e-c6443179c3a6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "17f820a7-9a6f-48cc-b733-b1ecb2685ecc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abdc85ea-cf22-4301-ac28-d02e43b251f9", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439898950600, "endTime": 30439925092700}, "additional": {"children": ["b2fa5573-f2b6-463c-add6-c0b65bfde269"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "941450a3-9e63-4389-9e69-136cbd23e114"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a50bf2b-b18c-4f80-af2d-6cfb660ed519", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439925100700, "endTime": 30440087590900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "3b90d765-3b29-439c-9c22-81cde0aead0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e845d9b9-ae7a-49d3-8647-918251aae168", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440088460800, "endTime": 30440098316200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "ca3c8abd-4685-4f13-9430-6863b6d6a186"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f1e45a1-e7a0-45ce-bbf2-82f5ff7333b8", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440098334300, "endTime": 30440108901800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "827cb122-4d09-41ea-832b-ede79f86ae99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d07f5da9-4af6-4f50-b64e-e8f916014afb", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440108919800, "endTime": 30440109057600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "7cfd8f39-5e02-4925-9c25-035fdd0bb3f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75c17011-780f-4304-81e7-1f846be72861", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439751318000, "endTime": 30439751326800}, "additional": {"logType": "info", "children": [], "durationId": "859e514f-af9e-435b-adf6-6d9b74286ade", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "d9ea7f4f-e17b-4a36-9602-d12f01707c96", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439751341900, "endTime": 30439754525100}, "additional": {"logType": "info", "children": [], "durationId": "2376f890-530e-4b8c-bfed-17081ffc1176", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "f4e3fcfe-d024-4ff8-b75e-a1a3e418bc7d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439755049000, "endTime": 30439755063500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "227af31c-e62c-4a9a-990a-c0bb7a482900", "logId": "af78da3d-db85-42c0-bf89-5b16008b9acb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af78da3d-db85-42c0-bf89-5b16008b9acb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439755049000, "endTime": 30439755063500}, "additional": {"logType": "info", "children": [], "durationId": "f4e3fcfe-d024-4ff8-b75e-a1a3e418bc7d", "parent": "17f820a7-9a6f-48cc-b733-b1ecb2685ecc"}}, {"head": {"id": "a90964b5-2238-4961-9028-e44432b564a7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439756625100, "endTime": 30439898142800}, "additional": {"children": ["3fe52303-a58c-482f-b7d0-0dfc7e5b2fa2", "5ba12679-3adc-40b9-9225-77dd0ae9d51b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "227af31c-e62c-4a9a-990a-c0bb7a482900", "logId": "b24c3d97-f28b-4db9-ad73-20fb4a3a072b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fe52303-a58c-482f-b7d0-0dfc7e5b2fa2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439756626800, "endTime": 30439761769200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a90964b5-2238-4961-9028-e44432b564a7", "logId": "8529ec75-351b-4698-8953-11efdc0a7ffd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ba12679-3adc-40b9-9225-77dd0ae9d51b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439761788700, "endTime": 30439898126100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a90964b5-2238-4961-9028-e44432b564a7", "logId": "d82e8734-95a6-4496-a50d-ae7ad24d1294"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0fb901a-d5ba-4470-8e92-20c827e375ad", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439756630900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c3591a-0e59-4bbe-b8dc-af1dfd9ef385", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439761614600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8529ec75-351b-4698-8953-11efdc0a7ffd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439756626800, "endTime": 30439761769200}, "additional": {"logType": "info", "children": [], "durationId": "3fe52303-a58c-482f-b7d0-0dfc7e5b2fa2", "parent": "b24c3d97-f28b-4db9-ad73-20fb4a3a072b"}}, {"head": {"id": "8473ebaa-18f2-4ab9-864f-f76e9c44615d", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439761799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1cacc71-6d27-4462-be1b-41b81ac986c4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439769864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa28c85-5d38-443a-8fd7-b4d14a35d37d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439770231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eccc9ef8-df8b-46df-9e6a-5c0a9dfbc2c9", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439770411200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc52b80-e31a-463d-8afa-cbece8a94f18", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439770510100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e8c817b-fb1b-4c78-bdf7-8df7dd9eb182", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439772941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b63f058-bc11-41fd-bd15-c0efbb4a75c1", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439778360600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b3b855a-a1ef-45a6-a59e-737237ed963b", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439787522400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2e9a7f2-68fe-4520-a70a-15f3b34e2a78", "name": "Sdk init in 48 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439827099600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "767cf30d-106f-4fa6-b509-4904885807f6", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439828798200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 56}, "markType": "other"}}, {"head": {"id": "105263e1-7f37-4ea4-8115-aad10e242721", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439828820400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 56}, "markType": "other"}}, {"head": {"id": "eb4ee13f-1251-40a3-ae88-9069dc96b23f", "name": "Project task initialization takes 68 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439897806900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93a624e5-4fd7-4594-8f40-45d3c71423a4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439897956000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c792e173-a187-4bc3-9147-bf5802143a6d", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439898021200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6ad531-4c63-4eb0-a390-112f9f6ec621", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439898071600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82e8734-95a6-4496-a50d-ae7ad24d1294", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439761788700, "endTime": 30439898126100}, "additional": {"logType": "info", "children": [], "durationId": "5ba12679-3adc-40b9-9225-77dd0ae9d51b", "parent": "b24c3d97-f28b-4db9-ad73-20fb4a3a072b"}}, {"head": {"id": "b24c3d97-f28b-4db9-ad73-20fb4a3a072b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439756625100, "endTime": 30439898142800}, "additional": {"logType": "info", "children": ["8529ec75-351b-4698-8953-11efdc0a7ffd", "d82e8734-95a6-4496-a50d-ae7ad24d1294"], "durationId": "a90964b5-2238-4961-9028-e44432b564a7", "parent": "17f820a7-9a6f-48cc-b733-b1ecb2685ecc"}}, {"head": {"id": "b52d0ba6-cb84-409c-8c1e-c6443179c3a6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439898902000, "endTime": 30439898921100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "227af31c-e62c-4a9a-990a-c0bb7a482900", "logId": "66ee117a-c125-4c2b-a2f2-9b797014ee33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66ee117a-c125-4c2b-a2f2-9b797014ee33", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439898902000, "endTime": 30439898921100}, "additional": {"logType": "info", "children": [], "durationId": "b52d0ba6-cb84-409c-8c1e-c6443179c3a6", "parent": "17f820a7-9a6f-48cc-b733-b1ecb2685ecc"}}, {"head": {"id": "17f820a7-9a6f-48cc-b733-b1ecb2685ecc", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439754541400, "endTime": 30439898935700}, "additional": {"logType": "info", "children": ["af78da3d-db85-42c0-bf89-5b16008b9acb", "b24c3d97-f28b-4db9-ad73-20fb4a3a072b", "66ee117a-c125-4c2b-a2f2-9b797014ee33"], "durationId": "227af31c-e62c-4a9a-990a-c0bb7a482900", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "b2fa5573-f2b6-463c-add6-c0b65bfde269", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439899687900, "endTime": 30439925080500}, "additional": {"children": ["3a70f2dd-745a-4be0-b1c0-25feb3425b0d", "9c3d77f8-7f49-45ef-a522-8ec2f29000d6", "f08d83f1-1807-4626-b017-07f37f44a001"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abdc85ea-cf22-4301-ac28-d02e43b251f9", "logId": "91e65c5e-8385-4e94-b160-ecc0aa06a2d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a70f2dd-745a-4be0-b1c0-25feb3425b0d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439903162900, "endTime": 30439903183000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2fa5573-f2b6-463c-add6-c0b65bfde269", "logId": "cff47dec-9d4a-4fdc-afda-c91b1f52187f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cff47dec-9d4a-4fdc-afda-c91b1f52187f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439903162900, "endTime": 30439903183000}, "additional": {"logType": "info", "children": [], "durationId": "3a70f2dd-745a-4be0-b1c0-25feb3425b0d", "parent": "91e65c5e-8385-4e94-b160-ecc0aa06a2d9"}}, {"head": {"id": "9c3d77f8-7f49-45ef-a522-8ec2f29000d6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439906800300, "endTime": 30439922520300}, "additional": {"children": ["e92c86b0-cfea-4948-b075-bd1914eb03a7", "385d4d5a-8d9a-47ec-a009-2de594d5b3e2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2fa5573-f2b6-463c-add6-c0b65bfde269", "logId": "4d28085e-8c4c-46b0-bf77-588f25db798e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e92c86b0-cfea-4948-b075-bd1914eb03a7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439906802500, "endTime": 30439910717500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c3d77f8-7f49-45ef-a522-8ec2f29000d6", "logId": "a252819f-9584-4db8-81e3-3cc25d28514c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "385d4d5a-8d9a-47ec-a009-2de594d5b3e2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439910739100, "endTime": 30439922506800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9c3d77f8-7f49-45ef-a522-8ec2f29000d6", "logId": "384223f0-be53-41a6-a4fd-e4704b37600f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ff6ebee-913b-40fa-8a1e-41c7a06296dc", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439906807600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cecc8d29-e3d5-4155-9a48-05fa81fd7c8d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439910355200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a252819f-9584-4db8-81e3-3cc25d28514c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439906802500, "endTime": 30439910717500}, "additional": {"logType": "info", "children": [], "durationId": "e92c86b0-cfea-4948-b075-bd1914eb03a7", "parent": "4d28085e-8c4c-46b0-bf77-588f25db798e"}}, {"head": {"id": "4be5cd42-a868-4573-9460-2c36fbafd7aa", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439910755000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05b79d96-0fa3-414e-81f8-02238afde5bb", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec9c519-d0a8-49f8-82e0-84fa7e99eb96", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "081b7e46-d111-42f2-a9a5-e7d5314612ad", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918553500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea1a543-23cb-42a3-bf26-083cbefe854d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918702000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d07f9f-2228-42f2-bfc3-1d67f582f17a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918776300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a7b791-3a90-4a88-842c-c677444872b1", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918829500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3096b78d-8154-4c1f-9d50-4987d9d9a284", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439918897300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e911df90-06a8-4dfa-ae73-296a3d63a1cf", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439922167100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cadc4b67-4d7c-44b6-ba91-cc570ca9143f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439922329200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bc69c7b-6efc-4a2e-9ae6-598a8f6117a1", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439922406100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dc62749-579d-40b0-9c24-544fe55a1ed7", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439922457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "384223f0-be53-41a6-a4fd-e4704b37600f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439910739100, "endTime": 30439922506800}, "additional": {"logType": "info", "children": [], "durationId": "385d4d5a-8d9a-47ec-a009-2de594d5b3e2", "parent": "4d28085e-8c4c-46b0-bf77-588f25db798e"}}, {"head": {"id": "4d28085e-8c4c-46b0-bf77-588f25db798e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439906800300, "endTime": 30439922520300}, "additional": {"logType": "info", "children": ["a252819f-9584-4db8-81e3-3cc25d28514c", "384223f0-be53-41a6-a4fd-e4704b37600f"], "durationId": "9c3d77f8-7f49-45ef-a522-8ec2f29000d6", "parent": "91e65c5e-8385-4e94-b160-ecc0aa06a2d9"}}, {"head": {"id": "f08d83f1-1807-4626-b017-07f37f44a001", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439925048100, "endTime": 30439925064900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2fa5573-f2b6-463c-add6-c0b65bfde269", "logId": "aa77a750-2b8f-45dc-9511-1f8f810be8f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa77a750-2b8f-45dc-9511-1f8f810be8f5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439925048100, "endTime": 30439925064900}, "additional": {"logType": "info", "children": [], "durationId": "f08d83f1-1807-4626-b017-07f37f44a001", "parent": "91e65c5e-8385-4e94-b160-ecc0aa06a2d9"}}, {"head": {"id": "91e65c5e-8385-4e94-b160-ecc0aa06a2d9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439899687900, "endTime": 30439925080500}, "additional": {"logType": "info", "children": ["cff47dec-9d4a-4fdc-afda-c91b1f52187f", "4d28085e-8c4c-46b0-bf77-588f25db798e", "aa77a750-2b8f-45dc-9511-1f8f810be8f5"], "durationId": "b2fa5573-f2b6-463c-add6-c0b65bfde269", "parent": "941450a3-9e63-4389-9e69-136cbd23e114"}}, {"head": {"id": "941450a3-9e63-4389-9e69-136cbd23e114", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439898950600, "endTime": 30439925092700}, "additional": {"logType": "info", "children": ["91e65c5e-8385-4e94-b160-ecc0aa06a2d9"], "durationId": "abdc85ea-cf22-4301-ac28-d02e43b251f9", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "92caada9-f5da-4225-97b4-1b8c7952822c", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439968547700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cae4e6cb-cd24-4d58-82dd-de9eb64fe1ee", "name": "hvigorfile, resolve hvigorfile dependencies in 163 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440087468700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b90d765-3b29-439c-9c22-81cde0aead0d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439925100700, "endTime": 30440087590900}, "additional": {"logType": "info", "children": [], "durationId": "9a50bf2b-b18c-4f80-af2d-6cfb660ed519", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "43b2b4d1-539e-4d1a-b924-77187677c435", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440088285500, "endTime": 30440088449700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "logId": "3f827962-ced6-4055-a368-776cb556b48e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "794fb66f-e7d7-455e-bfd7-1a882a7a9621", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440088307500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f827962-ced6-4055-a368-776cb556b48e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440088285500, "endTime": 30440088449700}, "additional": {"logType": "info", "children": [], "durationId": "43b2b4d1-539e-4d1a-b924-77187677c435", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "f16308f9-9fa4-4b23-9591-6f435eff52ee", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440089706500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6e02a99-f126-4c1c-91a1-6f6327174d1d", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440097761200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca3c8abd-4685-4f13-9430-6863b6d6a186", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440088460800, "endTime": 30440098316200}, "additional": {"logType": "info", "children": [], "durationId": "e845d9b9-ae7a-49d3-8647-918251aae168", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "fdfb1eb5-63ac-431b-9ccd-773c6c43e6c8", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440102850000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e1e5d9-d786-40fd-9df5-0317ae164107", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440103160200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d011a64-99a8-4363-a69a-2b5402dcd7ce", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440105239600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4e29a8a-023a-4a0f-abba-4dcea67aabb6", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440105549200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827cb122-4d09-41ea-832b-ede79f86ae99", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440098334300, "endTime": 30440108901800}, "additional": {"logType": "info", "children": [], "durationId": "8f1e45a1-e7a0-45ce-bbf2-82f5ff7333b8", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "d935258c-e02d-4b3e-b9ed-acfff3fcabb8", "name": "Configuration phase cost:358 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440108940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cfd8f39-5e02-4925-9c25-035fdd0bb3f6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440108919800, "endTime": 30440109057600}, "additional": {"logType": "info", "children": [], "durationId": "d07f5da9-4af6-4f50-b64e-e8f916014afb", "parent": "5b2b0334-982f-467b-96b5-aac686a3c775"}}, {"head": {"id": "5b2b0334-982f-467b-96b5-aac686a3c775", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439746364100, "endTime": 30440109068100}, "additional": {"logType": "info", "children": ["75c17011-780f-4304-81e7-1f846be72861", "d9ea7f4f-e17b-4a36-9602-d12f01707c96", "17f820a7-9a6f-48cc-b733-b1ecb2685ecc", "941450a3-9e63-4389-9e69-136cbd23e114", "3b90d765-3b29-439c-9c22-81cde0aead0d", "ca3c8abd-4685-4f13-9430-6863b6d6a186", "827cb122-4d09-41ea-832b-ede79f86ae99", "7cfd8f39-5e02-4925-9c25-035fdd0bb3f6", "3f827962-ced6-4055-a368-776cb556b48e"], "durationId": "a4ee555e-86f9-49f4-a7b1-fd56282ebf33", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "75a1da1c-1bb2-4537-aeec-7b0c1e1473c6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440110201200, "endTime": 30440110214900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0bae00d1-6f6d-4027-8fe6-28d07c059d37", "logId": "21566f56-48a3-4391-a31e-acba3e502b96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21566f56-48a3-4391-a31e-acba3e502b96", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440110201200, "endTime": 30440110214900}, "additional": {"logType": "info", "children": [], "durationId": "75a1da1c-1bb2-4537-aeec-7b0c1e1473c6", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "0d98af74-43f2-4efc-b7a9-055180684b7d", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440109090800, "endTime": 30440110223700}, "additional": {"logType": "info", "children": [], "durationId": "87f151eb-9651-4442-ae60-e05579c0c6ca", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "c97143e2-ae98-4fa2-9ae2-a8296d111a21", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440110227800, "endTime": 30440110228500}, "additional": {"logType": "info", "children": [], "durationId": "c756c99c-3343-4bde-a00d-361c98826be6", "parent": "470b0121-fb0d-42ff-8d47-e4c9fd363375"}}, {"head": {"id": "470b0121-fb0d-42ff-8d47-e4c9fd363375", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439734357800, "endTime": 30440110234300}, "additional": {"logType": "info", "children": ["a24d4fce-81e8-44c4-ab6f-dff114265c6e", "5b2b0334-982f-467b-96b5-aac686a3c775", "0d98af74-43f2-4efc-b7a9-055180684b7d", "c97143e2-ae98-4fa2-9ae2-a8296d111a21", "7ec14704-ade9-4881-bd04-72a5327101b0", "f438142e-d1d1-45ba-b840-cbfa1c1ebc1f", "21566f56-48a3-4391-a31e-acba3e502b96"], "durationId": "0bae00d1-6f6d-4027-8fe6-28d07c059d37"}}, {"head": {"id": "37c34036-89a8-40a6-8666-35d522bbc87d", "name": "Configuration task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440110384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e44bf8-537d-430e-9c4d-66ba8a0b9e4f", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440114219200, "endTime": 30440164823500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "72d3896b-bf92-4dfe-aba2-8a8b86102754", "logId": "19d11c2d-41ca-4a62-8ec3-797cafd3d6d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72d3896b-bf92-4dfe-aba2-8a8b86102754", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440111753300}, "additional": {"logType": "detail", "children": [], "durationId": "38e44bf8-537d-430e-9c4d-66ba8a0b9e4f"}}, {"head": {"id": "8efc44c6-a688-498c-9a41-7c665ae699a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440112067200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750e52a9-efda-4ec1-86db-590056503377", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440112158400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d37e057-19f9-45a7-b0c2-f7436b474189", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440114235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b10cd9b0-67f2-4e2a-8387-4847d2825af8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 47 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440162971600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5de26b-0a4d-4d5d-a315-0d7624befbf0", "name": "entry : default@PreBuild cost memory 0.308990478515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440164425500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19d11c2d-41ca-4a62-8ec3-797cafd3d6d7", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440114219200, "endTime": 30440164823500}, "additional": {"logType": "info", "children": [], "durationId": "38e44bf8-537d-430e-9c4d-66ba8a0b9e4f"}}, {"head": {"id": "526923ef-5e6e-46d5-a7ae-1e7d007d1d28", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440265246100, "endTime": 30440279169500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "97e61d95-8b41-4e7f-bc48-5a5f58189d7d", "logId": "67ba9dfc-f397-42d9-b610-1371268f1559"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97e61d95-8b41-4e7f-bc48-5a5f58189d7d", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440247799700}, "additional": {"logType": "detail", "children": [], "durationId": "526923ef-5e6e-46d5-a7ae-1e7d007d1d28"}}, {"head": {"id": "911892a7-6019-4a0d-96de-cf6263646a7f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440251075000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed255814-1569-4328-8f3b-cc221045cb52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440251860500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ffc2f91-c01e-4695-999e-71aae4083149", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440265359300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f4dd44-6634-4e77-92f9-97045d7c5caf", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440275055700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bcd0ddc-e70a-442c-b9ec-3b4d3b9a4614", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440278482600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2c511d-a56b-4fcb-aaa6-87fe6566977f", "name": "entry : default@GenerateMetadata cost memory 0.09674072265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440278878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67ba9dfc-f397-42d9-b610-1371268f1559", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440265246100, "endTime": 30440279169500}, "additional": {"logType": "info", "children": [], "durationId": "526923ef-5e6e-46d5-a7ae-1e7d007d1d28"}}, {"head": {"id": "e8f178df-f7ab-4c7c-bf15-8044db54e356", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440289624500, "endTime": 30440303450000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2a5fd818-e386-44ad-a0bf-6149dc3d0640", "logId": "ff9c851a-f133-4b91-aa39-0d303db3f5f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a5fd818-e386-44ad-a0bf-6149dc3d0640", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440287441900}, "additional": {"logType": "detail", "children": [], "durationId": "e8f178df-f7ab-4c7c-bf15-8044db54e356"}}, {"head": {"id": "a3e3081e-fb37-45c4-8380-d1219ad4dc8d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440288720300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb7dfa23-29d5-4c12-b2be-08fdac69e2a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440289082100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "057fb9e5-1208-454b-926a-ff9d80995b79", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440289635200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c388a7-df92-4af8-b901-819eafbf9335", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440289765900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59042dea-ad51-4f03-a9ed-abddff0cd8a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440289889500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27752d5-d833-4e59-b172-c4e1c7b327b2", "name": "entry : default@ConfigureCmake cost memory 0.0365142822265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440293457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0cd917e-f771-4f8b-8c82-39f0e9bec36e", "name": "runTaskFromQueue task cost before running: 563 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440294452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9c851a-f133-4b91-aa39-0d303db3f5f3", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440289624500, "endTime": 30440303450000, "totalTime": 4791100}, "additional": {"logType": "info", "children": [], "durationId": "e8f178df-f7ab-4c7c-bf15-8044db54e356"}}, {"head": {"id": "5acc15d5-6db5-4635-9dd2-911d6df2b239", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440349016900, "endTime": 30440377398100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8ca15c77-2462-4449-b123-7fcfb2043fa3", "logId": "028f40db-e097-4e5a-aebb-7e70ab2a8c85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ca15c77-2462-4449-b123-7fcfb2043fa3", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440346574700}, "additional": {"logType": "detail", "children": [], "durationId": "5acc15d5-6db5-4635-9dd2-911d6df2b239"}}, {"head": {"id": "c08058bc-f9dc-4d40-92f5-9574e8598493", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440347463800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029f8d44-e91c-40fe-bfb0-490a9a7affd3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440347854400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1084abe-a4a3-4096-8f33-4f9b63dced2f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440349027700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddcc3732-f91b-4b04-97b4-bddc7e394f5c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 27 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440376025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e612f519-1d30-4b72-8072-e20e0fe67d1d", "name": "entry : default@MergeProfile cost memory 0.10689544677734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440377272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "028f40db-e097-4e5a-aebb-7e70ab2a8c85", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440349016900, "endTime": 30440377398100}, "additional": {"logType": "info", "children": [], "durationId": "5acc15d5-6db5-4635-9dd2-911d6df2b239"}}, {"head": {"id": "724897f8-61b7-4c2d-97ee-032af3a390f7", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440483991000, "endTime": 30440488113200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "281bcc25-cb08-49c2-94aa-c0125a9d921a", "logId": "0d9b9507-be77-4019-a940-39e99946690e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "281bcc25-cb08-49c2-94aa-c0125a9d921a", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440406357700}, "additional": {"logType": "detail", "children": [], "durationId": "724897f8-61b7-4c2d-97ee-032af3a390f7"}}, {"head": {"id": "c7e57df5-0f46-4fd8-b55d-15b322fa014d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440416668900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e4c25f8-b34f-4002-8e9a-2f5c6f7c9456", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440470870100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22a0db17-0840-4944-9b4e-d9da5ad12f38", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440484005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db01281b-04b8-4c9f-b4e8-63ffe08bad1c", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440485665700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "044ccdde-34e3-40c8-b7af-860f5bb91bf0", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440487325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79832c1c-9e11-4be9-ba1e-277172c2fd9f", "name": "entry : default@CreateBuildProfile cost memory 0.105926513671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440487455900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9b9507-be77-4019-a940-39e99946690e", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440483991000, "endTime": 30440488113200}, "additional": {"logType": "info", "children": [], "durationId": "724897f8-61b7-4c2d-97ee-032af3a390f7"}}, {"head": {"id": "fc47520e-c6c2-4742-aa28-909772bdd29c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440493429600, "endTime": 30440494438500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "64c31823-8a3a-4502-a44d-48ad1d1af5b2", "logId": "148e9b76-f2a0-4b83-8f58-9fc2fe1b52b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64c31823-8a3a-4502-a44d-48ad1d1af5b2", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440492015600}, "additional": {"logType": "detail", "children": [], "durationId": "fc47520e-c6c2-4742-aa28-909772bdd29c"}}, {"head": {"id": "9f01ffb1-b757-48ac-a083-a795139d30d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440492370900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55d3e92c-bc1c-4081-9525-a91b5a91c4cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440492474800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9de0f588-6bee-4aac-9b44-3312e103e53e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440493438600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d38cec-2e27-4dda-b803-dc3ca0b539ae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440493624700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "866f5355-de8d-487b-abc4-e966e6da2c3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440493867200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf24dab-95b6-4612-9070-8a836ebf9053", "name": "entry : default@PreCheckSyscap cost memory 0.03673553466796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440494274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82f196f-4bb5-4a6c-ba4e-406ff3bde27a", "name": "runTaskFromQueue task cost before running: 763 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440494377200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148e9b76-f2a0-4b83-8f58-9fc2fe1b52b1", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440493429600, "endTime": 30440494438500, "totalTime": 929600}, "additional": {"logType": "info", "children": [], "durationId": "fc47520e-c6c2-4742-aa28-909772bdd29c"}}, {"head": {"id": "1e2833d3-a73f-4d2e-8e9d-4867f6d39ead", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440542273700, "endTime": 30440543856400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "44c451e0-fa30-49a2-933b-fdba873f37ee", "logId": "336c4c74-df77-4bf4-b070-22d67e9a7a28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c451e0-fa30-49a2-933b-fdba873f37ee", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440495934100}, "additional": {"logType": "detail", "children": [], "durationId": "1e2833d3-a73f-4d2e-8e9d-4867f6d39ead"}}, {"head": {"id": "0d554b8a-ee83-48b0-a791-faa970eebf2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440496257100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e79a2fc-99fd-44fa-82cc-1d08aa433a17", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440497254800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fe32df-53e9-4cc8-84e4-2ea88f5ac639", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440542286300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3cb1df-1144-4c19-9208-1f613f49b5e6", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440542813900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b414ac14-4b5b-47a3-a68b-17a706ded40d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.039093017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440543199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a86b4523-9b4d-4fd3-b7cf-4223cceaa6f5", "name": "runTaskFromQueue task cost before running: 812 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440543591900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336c4c74-df77-4bf4-b070-22d67e9a7a28", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440542273700, "endTime": 30440543856400, "totalTime": 1283400}, "additional": {"logType": "info", "children": [], "durationId": "1e2833d3-a73f-4d2e-8e9d-4867f6d39ead"}}, {"head": {"id": "8ad02301-ae43-4d33-8a9e-5cedaf47884e", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440549055700, "endTime": 30440556263400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "471b4a01-b77f-4b3c-8946-d00788cb1dcc", "logId": "e90ba566-4b13-4014-8cb0-d3fadfc4f3da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "471b4a01-b77f-4b3c-8946-d00788cb1dcc", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440545698900}, "additional": {"logType": "detail", "children": [], "durationId": "8ad02301-ae43-4d33-8a9e-5cedaf47884e"}}, {"head": {"id": "e581edd5-63c2-486b-a554-f3e93a278e53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440546337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917558e0-7944-4648-b5c0-8480cd7ed990", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440546447200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34540fa9-156e-4c0c-b1e8-b5bdd0c8af40", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440549067100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a4a22f-437f-4dc7-8c44-f35f506c902a", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440551504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4f7d1e-5b70-4a8f-bd80-db07e0119f75", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440551778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b60c0af8-6653-403b-8a45-7d45d75837d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440551922800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a7c779-a9a0-4a74-a535-37277a3222a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440552187900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9255e884-3b2d-40c5-889a-d98a15f04123", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1187896728515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440554311100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4bdc8f7-c4ee-470f-8cbb-4717c2f4b500", "name": "runTaskFromQueue task cost before running: 824 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440555870600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90ba566-4b13-4014-8cb0-d3fadfc4f3da", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440549055700, "endTime": 30440556263400, "totalTime": 6775300}, "additional": {"logType": "info", "children": [], "durationId": "8ad02301-ae43-4d33-8a9e-5cedaf47884e"}}, {"head": {"id": "f99a1f97-0a2f-4548-ac25-f2bcb6434c0f", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440570302100, "endTime": 30440571757200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "721e9879-b66c-432c-a793-d1f21e0cce90", "logId": "798b35db-73bb-42c7-8c7a-f032135819f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "721e9879-b66c-432c-a793-d1f21e0cce90", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440566254900}, "additional": {"logType": "detail", "children": [], "durationId": "f99a1f97-0a2f-4548-ac25-f2bcb6434c0f"}}, {"head": {"id": "f0ff8cd1-3e53-4222-8046-f0c551b1298b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440566797200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "023bb7cc-10e5-4c70-9833-da034fcd0f26", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440567133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ead5f77-f36d-4f1f-b8d3-0857db7cd996", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440570318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e629d712-c53b-4e13-87a0-6d161a22ce4f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440570773600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57324388-8678-4c7c-b951-00cc4ee3ed0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440570908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "292fb17c-b868-4f20-b670-d67c94d5928e", "name": "entry : default@BuildNativeWithCmake cost memory 0.037567138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440571049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea916337-6895-4d31-a581-280754d2377d", "name": "runTaskFromQueue task cost before running: 840 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440571466500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798b35db-73bb-42c7-8c7a-f032135819f9", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440570302100, "endTime": 30440571757200, "totalTime": 1141100}, "additional": {"logType": "info", "children": [], "durationId": "f99a1f97-0a2f-4548-ac25-f2bcb6434c0f"}}, {"head": {"id": "7a83b900-ff4b-4b34-8abb-ae2a6caabe20", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440626287700, "endTime": 30440638812600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5a591d28-64f1-4296-939f-f09e69b9176e", "logId": "ed65c6f6-feab-4973-a4fe-4d9cfee06791"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a591d28-64f1-4296-939f-f09e69b9176e", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440593895200}, "additional": {"logType": "detail", "children": [], "durationId": "7a83b900-ff4b-4b34-8abb-ae2a6caabe20"}}, {"head": {"id": "e288e893-ea40-4c13-a3e5-eb7d6dc1cf96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440622831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d105a3a-7d3d-4511-8f4a-1feaa0c043a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440623083600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97fff08d-7cda-49d1-a9f6-976f27a2146b", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440626330600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59be9922-40f6-480f-a54c-21236a2b58a4", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440638137600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5680f1cb-ada7-472c-9807-985585f2bb22", "name": "entry : default@MakePackInfo cost memory 0.139801025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440638384700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed65c6f6-feab-4973-a4fe-4d9cfee06791", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440626287700, "endTime": 30440638812600}, "additional": {"logType": "info", "children": [], "durationId": "7a83b900-ff4b-4b34-8abb-ae2a6caabe20"}}, {"head": {"id": "0b4128b2-305b-4730-9288-a33c309f9838", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440654973000, "endTime": 30440662080900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "d8230721-b5bf-420f-8e7e-39fe7e6ff8fc", "logId": "20e5cf6d-7c30-404b-ae81-e6d16f249b85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8230721-b5bf-420f-8e7e-39fe7e6ff8fc", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440651278600}, "additional": {"logType": "detail", "children": [], "durationId": "0b4128b2-305b-4730-9288-a33c309f9838"}}, {"head": {"id": "3817f1ce-07d4-4039-ba4e-729c74ff23ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440652728300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda4065f-66b6-4e4d-8d0a-e6d4bcd1f07a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440653056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5cebebe-4bfc-4b97-8586-ee055d64c997", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440654986300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f9432d-e8f9-4489-90ab-dd357a74cec1", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440655391600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5742553a-1bb0-4bdb-838f-391b40e43646", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440657437900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395c3b41-d915-4a0f-91f1-b71b6a1bb50b", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440660910100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5244539d-469b-4d8f-9956-6e07d6c0de4a", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440661262400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3d1f9d-ef35-44cc-a1c0-3bd4709707b6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440661404300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1e318c-bb4b-4e56-9e72-c5eca3432a34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440661527300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75f9caec-568e-4a2c-b86f-c578ef501b09", "name": "entry : default@SyscapTransform cost memory 0.15489959716796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440661851800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70e0e6a-7525-4f8f-a85a-eaffcccc57d3", "name": "runTaskFromQueue task cost before running: 930 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440662015000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e5cf6d-7c30-404b-ae81-e6d16f249b85", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440654973000, "endTime": 30440662080900, "totalTime": 7020100}, "additional": {"logType": "info", "children": [], "durationId": "0b4128b2-305b-4730-9288-a33c309f9838"}}, {"head": {"id": "1ee74a9e-ffb6-48cf-811f-7f55c8e9c662", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440667784500, "endTime": 30440670076400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5156d7bd-4fb2-49bf-9191-1fa57e899c6d", "logId": "58eb3e4f-6e6c-4393-95f4-008ff36871eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5156d7bd-4fb2-49bf-9191-1fa57e899c6d", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440664599400}, "additional": {"logType": "detail", "children": [], "durationId": "1ee74a9e-ffb6-48cf-811f-7f55c8e9c662"}}, {"head": {"id": "2f130ff4-4176-45f7-afeb-294c73ff0679", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440665408300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3b05ede-ebdc-4522-8cba-47d06ee837fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440665533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3445310e-863a-4a8c-ad50-b440c0202137", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440667799200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d994602-44d0-4c3f-8407-2dd43b77236f", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440669284000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2130fdd-8a63-41fd-8fa8-0e34721f4db4", "name": "entry : default@ProcessProfile cost memory 0.06122589111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440669546900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58eb3e4f-6e6c-4393-95f4-008ff36871eb", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440667784500, "endTime": 30440670076400}, "additional": {"logType": "info", "children": [], "durationId": "1ee74a9e-ffb6-48cf-811f-7f55c8e9c662"}}, {"head": {"id": "ea4e7203-e0cc-40a6-9b17-4f02ddba81fb", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440684198800, "endTime": 30440809753900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d99dbae0-2f6d-4aa1-ba37-9e89bffa96f0", "logId": "19eea1b1-f8b9-41d9-87da-3a198022fb87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d99dbae0-2f6d-4aa1-ba37-9e89bffa96f0", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440677476100}, "additional": {"logType": "detail", "children": [], "durationId": "ea4e7203-e0cc-40a6-9b17-4f02ddba81fb"}}, {"head": {"id": "08a3e933-c631-4e96-98ff-8d29f91ff862", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440678353700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ca0656-15dc-4609-8184-aa815730e98e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440678534700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a90a96-851c-4cf8-a306-dd41784fde57", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440684212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c487400a-898b-4c52-8d7e-b414ed0fc2a9", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 93 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440778655900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31702682-ad9c-4b3c-93f8-ccc0b1dd69a6", "name": "entry : default@ProcessRouterMap cost memory 0.2031707763671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440787445700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19eea1b1-f8b9-41d9-87da-3a198022fb87", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440684198800, "endTime": 30440809753900}, "additional": {"logType": "info", "children": [], "durationId": "ea4e7203-e0cc-40a6-9b17-4f02ddba81fb"}}, {"head": {"id": "f7251c8d-e9bc-4088-8dcc-4a7c51a81c93", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440848824300, "endTime": 30440850881600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eb4ff651-fbcd-423c-8e72-5462b08f966d", "logId": "fa5f5fdc-dba9-4d8c-b614-65049b9f4714"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb4ff651-fbcd-423c-8e72-5462b08f966d", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440845702900}, "additional": {"logType": "detail", "children": [], "durationId": "f7251c8d-e9bc-4088-8dcc-4a7c51a81c93"}}, {"head": {"id": "8e9a2692-506e-4177-a495-dcc82b832f2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440847190400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1681f1fc-2ec1-461e-8106-cb7afc320d9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440847312200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f3e61e-8955-4e84-971c-f746670430c9", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440848841900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b1d4d3-f716-43cb-af60-f2352bc33b3d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440849534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12810796-9190-443f-853f-4174e7a29001", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440849743100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77fa46ea-cda5-4814-b645-3163b3116363", "name": "entry : default@BuildNativeWithNinja cost memory 0.05716705322265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440850608700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ee38ba4-6766-4787-aeaa-40b6c21cefbf", "name": "runTaskFromQueue task cost before running: 1 s 119 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440850763000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5f5fdc-dba9-4d8c-b614-65049b9f4714", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440848824300, "endTime": 30440850881600, "totalTime": 1913200}, "additional": {"logType": "info", "children": [], "durationId": "f7251c8d-e9bc-4088-8dcc-4a7c51a81c93"}}, {"head": {"id": "b17427e4-4a79-4b9b-9ce1-9864c283feaf", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440863300400, "endTime": 30440902439900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8812def3-b6b7-484e-bb16-b241956dbd5b", "logId": "5dc1e584-dff6-4176-8531-29d841bd1bf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8812def3-b6b7-484e-bb16-b241956dbd5b", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440855477900}, "additional": {"logType": "detail", "children": [], "durationId": "b17427e4-4a79-4b9b-9ce1-9864c283feaf"}}, {"head": {"id": "12b6de6c-e1ef-4961-918f-c626f2c8e94d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440855992800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a97b580-835c-4da5-bd62-b432ce1e8ef3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440856337900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e1bf53-8739-4c8f-abcb-377e5a1ab9df", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440859620900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60be30e7-4d17-426f-91a9-cde117d4c5b3", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440866004900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac96201-f206-4426-927b-df4508c4e9f7", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440873185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82a85068-768b-49c5-aed4-8e71573e90e2", "name": "entry : default@ProcessResource cost memory 0.1703948974609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440877232800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc1e584-dff6-4176-8531-29d841bd1bf0", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440863300400, "endTime": 30440902439900}, "additional": {"logType": "info", "children": [], "durationId": "b17427e4-4a79-4b9b-9ce1-9864c283feaf"}}, {"head": {"id": "f025bc3a-77bc-4b56-916b-42ca25373710", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440964206700, "endTime": 30441031157000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "914009a2-f55f-49eb-9625-af673999be3b", "logId": "1df298a0-ae98-40bc-b6cf-aba23e1282df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "914009a2-f55f-49eb-9625-af673999be3b", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440948855400}, "additional": {"logType": "detail", "children": [], "durationId": "f025bc3a-77bc-4b56-916b-42ca25373710"}}, {"head": {"id": "c252f7bc-96e2-4d01-85ee-509760b043b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440951976600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "475faba6-2829-4a2b-b2a1-c59b90c16bf9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440953147100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba1b44a-c000-450d-ab9b-6b8257441bbc", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440964219600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27294335-f483-40d3-b719-4037200b189e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441030752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac8a3b64-ac5b-45c0-81ba-233a0193168c", "name": "entry : default@GenerateLoaderJson cost memory 0.7688217163085938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441030946400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df298a0-ae98-40bc-b6cf-aba23e1282df", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30440964206700, "endTime": 30441031157000}, "additional": {"logType": "info", "children": [], "durationId": "f025bc3a-77bc-4b56-916b-42ca25373710"}}, {"head": {"id": "0641afe7-567f-452e-ad4c-e61a062c1547", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441039365200, "endTime": 30441043004400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4a010cc2-6f2a-43c0-ba88-f143e657c4ec", "logId": "ff208a21-9870-42ff-8e10-ce89d3a1dcb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a010cc2-6f2a-43c0-ba88-f143e657c4ec", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441037771300}, "additional": {"logType": "detail", "children": [], "durationId": "0641afe7-567f-452e-ad4c-e61a062c1547"}}, {"head": {"id": "25c49f1f-7ffe-4712-86f2-22c5220982a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441038103500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d0d3a5-073f-4ed0-a5c3-cd21f402f40f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441038472900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "561b2155-3ab8-4eb8-b893-3b047b358b1b", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441039379200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b545a3e0-9311-41fe-9247-55c02533e24d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441041839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479dbafe-1c27-4b6a-8419-9bdd873162c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441041947700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af9cf16-c107-41a2-8663-5aab6cfeae86", "name": "entry : default@ProcessLibs cost memory 0.127410888671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441042588800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c669c29b-92f4-497f-bd51-21724d998b22", "name": "runTaskFromQueue task cost before running: 1 s 311 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441042882100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff208a21-9870-42ff-8e10-ce89d3a1dcb0", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441039365200, "endTime": 30441043004400, "totalTime": 3465000}, "additional": {"logType": "info", "children": [], "durationId": "0641afe7-567f-452e-ad4c-e61a062c1547"}}, {"head": {"id": "7ed8bb7d-c7d1-498c-8198-a90a4178bf93", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441111720600, "endTime": 30441209297200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6bf91791-db0d-4ad0-baf0-23520ac17bd0", "logId": "b7fcdeb4-ae68-4bda-b9fd-590e2567fa2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bf91791-db0d-4ad0-baf0-23520ac17bd0", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441045490700}, "additional": {"logType": "detail", "children": [], "durationId": "7ed8bb7d-c7d1-498c-8198-a90a4178bf93"}}, {"head": {"id": "7cdd9fc5-9248-4fe6-ad04-cb847751650a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441045863900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e21eb34-3511-454a-8ff4-46a9353e8c62", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441045965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e95bd92e-97fb-41d2-b98f-26f14fcc9ff7", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441100264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fd7d7f-1c72-45b9-b3c4-8bf09910b489", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441112079700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "083a5dfa-84ce-4550-90bd-3773b77bb56f", "name": "Incremental task entry:default@CompileResource pre-execution cost: 93 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441206570400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5604292-ca64-4f86-b77f-a3245b016680", "name": "entry : default@CompileResource cost memory 1.6383132934570312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441209061100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7fcdeb4-ae68-4bda-b9fd-590e2567fa2f", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441111720600, "endTime": 30441209297200}, "additional": {"logType": "info", "children": [], "durationId": "7ed8bb7d-c7d1-498c-8198-a90a4178bf93"}}, {"head": {"id": "80e4db01-f21d-4b85-9fc6-ef1b574855b5", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441218818100, "endTime": 30441220314400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "96239595-c774-40eb-adcc-f99ca8e215bc", "logId": "d019f0f8-b9b0-476f-9cb5-e5b9b75fbdf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96239595-c774-40eb-adcc-f99ca8e215bc", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441215275400}, "additional": {"logType": "detail", "children": [], "durationId": "80e4db01-f21d-4b85-9fc6-ef1b574855b5"}}, {"head": {"id": "08539224-0210-4fa1-9fae-bc0fcae09ba2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441216003200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cea66e50-cde7-460f-a505-8339471fd699", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441216144100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfec18f5-fa29-4720-8cac-06bb49b71aca", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441218830200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5620e3-8c02-493f-b221-b882a14c29d5", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441219072700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914fc168-b44e-474d-bd72-ff18300a7896", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441220134900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c30a9a1-ce72-4c9f-9ba5-c0c990f8222e", "name": "entry : default@DoNativeStrip cost memory 0.07698822021484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441220240400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d019f0f8-b9b0-476f-9cb5-e5b9b75fbdf1", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441218818100, "endTime": 30441220314400}, "additional": {"logType": "info", "children": [], "durationId": "80e4db01-f21d-4b85-9fc6-ef1b574855b5"}}, {"head": {"id": "2280c951-4a7f-49dc-9e8b-bdc1ecea557d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441229732600, "endTime": 30441265429700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0190d311-4272-4480-96f5-6a50d028da49", "logId": "38693913-13e0-49fd-a32c-f782a0c2c611"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0190d311-4272-4480-96f5-6a50d028da49", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441224462800}, "additional": {"logType": "detail", "children": [], "durationId": "2280c951-4a7f-49dc-9e8b-bdc1ecea557d"}}, {"head": {"id": "ee87408f-9212-4927-88fd-41b76629175a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441224965200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e924510d-6c66-44e6-8224-4916c4f8f6fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441225270200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd3fae2e-6bba-4474-8abc-407a74d5d889", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441229752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d7bbd1-c2a6-4354-a27f-a6b14cbf9a17", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 26 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441259229600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579c6968-a937-4be8-8dd3-5473d9007d84", "name": "entry : default@CompileArkTS cost memory 0.6801223754882812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441262752600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38693913-13e0-49fd-a32c-f782a0c2c611", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441229732600, "endTime": 30441265429700}, "additional": {"logType": "info", "children": [], "durationId": "2280c951-4a7f-49dc-9e8b-bdc1ecea557d"}}, {"head": {"id": "7a953784-c0a9-4382-83fd-fdf54c18b910", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441312353200, "endTime": 30441318867600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "010feac1-2b5e-49d3-bd3d-972f7be736c3", "logId": "b4dfdda6-2406-4e5e-a06c-3ffe0dfa92f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "010feac1-2b5e-49d3-bd3d-972f7be736c3", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441301275900}, "additional": {"logType": "detail", "children": [], "durationId": "7a953784-c0a9-4382-83fd-fdf54c18b910"}}, {"head": {"id": "e30679d9-c96e-456d-b077-473aa81cab24", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441301683400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "790adaa3-707f-48c8-9533-689352520499", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441302008700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e100c385-6bb5-46ac-9eed-1aa9a9693bd8", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441312367300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b8e9e7-19f8-4a8e-8288-bfcb1384c9da", "name": "entry : default@BuildJS cost memory 0.12837982177734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441316946800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b6fa93-2d28-4296-bf2e-22b8744e6cf9", "name": "runTaskFromQueue task cost before running: 1 s 587 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441318760900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4dfdda6-2406-4e5e-a06c-3ffe0dfa92f4", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441312353200, "endTime": 30441318867600, "totalTime": 6366000}, "additional": {"logType": "info", "children": [], "durationId": "7a953784-c0a9-4382-83fd-fdf54c18b910"}}, {"head": {"id": "50259ec0-b02c-44f0-aae7-09ad3cd6ebd3", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441324591300, "endTime": 30441327075000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8af9e998-37b3-409a-adb9-c882e7186f74", "logId": "3975ce76-e844-4348-9ab1-ef785de16f2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8af9e998-37b3-409a-adb9-c882e7186f74", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441320548200}, "additional": {"logType": "detail", "children": [], "durationId": "50259ec0-b02c-44f0-aae7-09ad3cd6ebd3"}}, {"head": {"id": "062f9788-4fc4-41fe-bd94-1f88fc358b5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441320955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39b82fff-27f7-47d4-9a59-97cd2740c027", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441321055200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e11742f-ac9d-4df6-9546-33efa5032eb4", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441324603200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e6c914f-6301-4dae-9976-13ed230af9c7", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441325459900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2b014c8-8442-416b-9f92-d876af978630", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441326582200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f8d15d-3d86-40a1-b260-7d5e88f3d93e", "name": "entry : default@CacheNativeLibs cost memory 0.0910491943359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441326764200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3975ce76-e844-4348-9ab1-ef785de16f2b", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441324591300, "endTime": 30441327075000}, "additional": {"logType": "info", "children": [], "durationId": "50259ec0-b02c-44f0-aae7-09ad3cd6ebd3"}}, {"head": {"id": "644b7e2a-dc2d-4676-923e-a0a909eb9022", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441331506500, "endTime": 30441332541400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "c7c6a874-10db-4f8c-9c0f-493a74a1b749", "logId": "c0221186-f731-4ffd-9712-40348e55e107"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7c6a874-10db-4f8c-9c0f-493a74a1b749", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441329439600}, "additional": {"logType": "detail", "children": [], "durationId": "644b7e2a-dc2d-4676-923e-a0a909eb9022"}}, {"head": {"id": "ab1413d4-9be4-449d-b33f-4573e09820d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441330017900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b6f1e1-7709-4420-a4c7-be75a9acb883", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441330385800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60c30735-b7dd-4089-b030-313b6efb6845", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441331519100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3622c70c-cdbb-4215-87cc-a88b787a0085", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441331753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4307c1da-0066-4fe2-8267-6a791209e21c", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441332385200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0f8c78-ad2d-4916-8cca-6ebd0f67db5f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07335662841796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441332478100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0221186-f731-4ffd-9712-40348e55e107", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441331506500, "endTime": 30441332541400}, "additional": {"logType": "info", "children": [], "durationId": "644b7e2a-dc2d-4676-923e-a0a909eb9022"}}, {"head": {"id": "f1bad60d-3ae9-420b-aa4c-cab486e74b38", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441346088900, "endTime": 30441501061100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "1bde2275-8ed8-4a4e-9ec9-437b732440ff", "logId": "a78f8463-9293-40c1-b603-fec77ebd4a17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bde2275-8ed8-4a4e-9ec9-437b732440ff", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441334356700}, "additional": {"logType": "detail", "children": [], "durationId": "f1bad60d-3ae9-420b-aa4c-cab486e74b38"}}, {"head": {"id": "2512e302-6109-49c1-9a95-10489a133346", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441334768000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c54fddc-1c97-4479-917b-6189dc7c2cca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441334853700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f1c301a-51e4-4357-8f34-0dfc5fbdb5d7", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441346099900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf36ccba-70cc-4a66-8f0b-7de30db0b9f6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 148 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441496004000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3da1688-33e0-420a-85f0-cdd514696268", "name": "entry : default@PackageHap cost memory 0.839141845703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441496914200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78f8463-9293-40c1-b603-fec77ebd4a17", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441346088900, "endTime": 30441501061100}, "additional": {"logType": "info", "children": [], "durationId": "f1bad60d-3ae9-420b-aa4c-cab486e74b38"}}, {"head": {"id": "84874fe4-5c69-460d-9cf1-6f8fbef1565d", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441576877000, "endTime": 30441602786400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "12ab696d-2150-453a-a425-ce857009bd0d", "logId": "9e32537c-f809-42e9-9376-8a3766a17f1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12ab696d-2150-453a-a425-ce857009bd0d", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441515094600}, "additional": {"logType": "detail", "children": [], "durationId": "84874fe4-5c69-460d-9cf1-6f8fbef1565d"}}, {"head": {"id": "f613c6e9-8c0d-4215-925f-9d967e26f373", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441516374600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33e6453-9a16-425d-b384-f83923b87a97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441516976800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "610252d1-2a3c-4e73-b495-20206aa6b798", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441577071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "315dc73f-14a8-49a4-a14f-1a8e31524d12", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441580321300}, "additional": {"logType": "warn", "children": [], "durationId": "84874fe4-5c69-460d-9cf1-6f8fbef1565d"}}, {"head": {"id": "eb24b2f1-ad93-42f8-8e4f-2fa16a79b487", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441582756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94aaae55-1fb6-4796-bd91-fbd12373e540", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441583149400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85503560-060c-46ae-a617-ce5ec5c57df2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441583494000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b21111c-798b-4bbf-a320-0030b01ce971", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441583802700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7334b3-5793-4d64-8e00-85a2fad91933", "name": "entry : default@SignHap cost memory 0.1209564208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441598983400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bae5718f-049b-4888-bd65-456a5e1df8a3", "name": "runTaskFromQueue task cost before running: 1 s 868 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441599619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e32537c-f809-42e9-9376-8a3766a17f1f", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441576877000, "endTime": 30441602786400, "totalTime": 22622700}, "additional": {"logType": "info", "children": [], "durationId": "84874fe4-5c69-460d-9cf1-6f8fbef1565d"}}, {"head": {"id": "f7b0bf97-8744-4337-b7ed-b0fa22339713", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441636575600, "endTime": 30441665276400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "595ca245-c11e-4877-807a-8b1b84f41769", "logId": "150fdd85-2fee-4c97-9ab6-0387a3c857eb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "595ca245-c11e-4877-807a-8b1b84f41769", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441634255700}, "additional": {"logType": "detail", "children": [], "durationId": "f7b0bf97-8744-4337-b7ed-b0fa22339713"}}, {"head": {"id": "8a55f28a-fd58-4a87-950c-423543626805", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441634831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f085fba5-1257-458f-a07d-4ddb7250d87f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441635189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c51867c-fa3d-4e06-a6b1-7bd39a589baf", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441636588600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6a3b33b-dc3d-4b9b-b276-e5593955268f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441663505200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ea1c9a-cc59-4d71-af81-12f743956f93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441663713900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7754a81-d1fa-41a5-b1d5-a9058150341f", "name": "entry : default@CollectDebugSymbol cost memory 0.2410736083984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441663947000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb9ccf60-50a1-4d22-8fe8-146f2ff98fc5", "name": "runTaskFromQueue task cost before running: 1 s 934 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441665104200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "150fdd85-2fee-4c97-9ab6-0387a3c857eb", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441636575600, "endTime": 30441665276400, "totalTime": 28497800}, "additional": {"logType": "info", "children": [], "durationId": "f7b0bf97-8744-4337-b7ed-b0fa22339713"}}, {"head": {"id": "60690fc3-a701-4a77-a6ac-ff9198cfe7a5", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441667588300, "endTime": 30441668107800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1817f774-6c70-43fd-920d-7f24efba5f4f", "logId": "aef0a02c-5f0d-40a2-9bff-af74b8bdc3ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1817f774-6c70-43fd-920d-7f24efba5f4f", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441667550800}, "additional": {"logType": "detail", "children": [], "durationId": "60690fc3-a701-4a77-a6ac-ff9198cfe7a5"}}, {"head": {"id": "b73e03fe-bdf3-415d-b904-1cd0fafc9bb2", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441667596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63234c2e-ef62-4e14-8b80-c3620ae56910", "name": "entry : assembleHap cost memory 0.0115509033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441667875200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541f9e8a-5479-46ce-9253-95588df3b417", "name": "runTaskFromQueue task cost before running: 1 s 936 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441668021300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef0a02c-5f0d-40a2-9bff-af74b8bdc3ae", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441667588300, "endTime": 30441668107800, "totalTime": 361200}, "additional": {"logType": "info", "children": [], "durationId": "60690fc3-a701-4a77-a6ac-ff9198cfe7a5"}}, {"head": {"id": "c3ee7182-6f16-4af6-82d9-a8708da3cabe", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441770468700, "endTime": 30441770493900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "258e0ff7-8c9b-4e18-8bfe-659cf7c9a802", "logId": "26aa6a61-8ca5-4071-b7ab-a3926f831ef3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26aa6a61-8ca5-4071-b7ab-a3926f831ef3", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441770468700, "endTime": 30441770493900}, "additional": {"logType": "info", "children": [], "durationId": "c3ee7182-6f16-4af6-82d9-a8708da3cabe"}}, {"head": {"id": "66e47663-9117-4d4a-b238-de47c9509edf", "name": "BUILD SUCCESSFUL in 2 s 44 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441776115500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "22513d33-7c0a-4a60-b0c6-5b71531a7e74", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30439732039000, "endTime": 30441785165200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "2d0c6b27-b923-42e0-8695-fb8f85a14fdf", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441785190700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d04b6db-9ff8-4fd3-8280-ca6ec0628b63", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441785546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ef6bd66-7e92-44f8-a287-8c2d29c0e8cf", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441785718800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b6f6d48-6a7d-447a-812a-a934261aecb9", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441786172200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57bd1f99-2064-4c50-931a-1efbfcdfd096", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441786449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de49844-3e4f-4fa9-9611-4dcfa9facacc", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441787096200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8f3fcb-3c85-4e46-aed7-ae6dfd19566d", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441796812600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38eb7240-76ef-48dc-bd18-dcf537b94bae", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441800720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68969bb-19a7-4c47-84b5-85c507e1ca07", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441802568600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e86d1e83-f674-481b-bd38-2516503aaa1f", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441803252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd77cab-59c8-4a46-9b87-296b58a73eb5", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441816344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c434cbd-ba8e-47ad-b7fb-51280fda93af", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441850643900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a47f12-ef2b-4d65-8851-697092ca2f89", "name": "Incremental task entry:default@SyscapTransform post-execution cost:49 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441851248000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7a7c98-c4ed-47c6-9df1-1514d0ec8967", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441851521900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2f4db0-25f3-4a15-aaf8-0bc0da057ae3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441851797400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16bc6f9f-af43-427a-ad28-f1e3e54e64e5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441851872700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a0e267-8366-420a-8231-bab151598619", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441851921100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b1ffc3e-d24b-4e9b-9558-4b6d466c0c57", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441852264800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd71bdb-1558-4fd7-abd6-a3955ccf2e91", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441852483900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e74f9f57-8709-4375-89c0-4c6ed73328e6", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441852673500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c627e7d9-edc6-4df2-ae4f-06ea9fd6b0be", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441852924700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc1e62f1-27ed-41d5-bdff-5f232ac19d41", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441852988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "963bdc02-7a10-44ed-b16e-ae37a4c35765", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441853040200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ffcd909-73a6-4b7b-9d8c-d51c124f3f7e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441853082000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd654e72-0ee9-473b-a09d-0a4dfc8ccf75", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441854564300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b326b5d-6097-4beb-82b8-34e0cc49e2c5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441855773200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed49745-e75d-456c-9f90-2e2a2183371c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441858778000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a88972-e0ad-4e1c-8ed7-17d519c4a8ca", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441859050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5748d095-d646-4d3d-9b50-300d7d71ec18", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441859444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917e7afa-39d8-4636-a7ba-da2634d460cd", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441860080300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b735f51f-8751-4376-82a4-f7e4c8a14787", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441860442800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86ad5c69-d61b-430b-be1a-3fcdfe545422", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441860693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7abe307-8189-4c49-a9be-16808e9a49fe", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441861095200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77ec597e-dbfb-47ba-9730-16613924d678", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441861263100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61c958d-dba9-46fe-a845-7543f2499d8b", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441861547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f622265d-5179-47f0-bc16-4a2d70051498", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441861886400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e190ae65-2a07-4119-9574-f88852823a95", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441862251800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ddf1c18-e11f-44b9-ad6b-16509f63dd26", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441878206900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "331e34c5-5836-4058-a87b-d5c0bac5348f", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441887105900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00c5cc4-2543-4e03-8d64-356789adee5c", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441889432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d7c043-6b57-4ab7-aceb-f4c41f1601d7", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:33 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30441895003500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}