{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "d6f32d07-5375-4ec1-bfcb-d0f5febd78a4", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188203766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee9073a-fcb6-4e53-a9e7-ba0108b6d71d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188209155900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c0270e-e8a4-4586-bcea-5a3bb7c460a7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188209479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54c96cb-1679-479f-8aec-631a142e0c61", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188215768800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "194a9739-7faf-44c8-8bea-4003fc765cf4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028272669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028278741800, "endTime": 29028546964700}, "additional": {"children": ["12580235-93e5-4e1f-9bea-5e28f451ce9e", "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "68df82e5-93cf-4086-a37b-56355e2f9b7c", "b7053218-6499-4046-8246-5ac59ff254ac", "73c629da-eebd-406f-8d85-29314edf5564", "af9252be-8674-4be1-b697-60bc8cc1d127", "acb78681-1c80-41e6-a5f9-343ae91c92e3"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12580235-93e5-4e1f-9bea-5e28f451ce9e", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028278743900, "endTime": 29028291134100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "b43b0401-8544-4bc2-be37-9fab464d6094"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028291158200, "endTime": 29028544758100}, "additional": {"children": ["456f1126-db7c-48a0-8521-8dd043561958", "70f863b8-6f40-4f7c-801a-c299dadca94e", "8fc8f106-070f-401e-9293-6e5a846dfffc", "549ddf5f-c2ae-4523-bf9b-0ef72f215548", "383a83b1-8aef-4b50-8a3f-a8ad74ef7581", "9ca3fd7d-fe85-4086-b665-df2428acac54", "3c9fcfb0-64de-41ee-ac6e-6c3e1cdbfc18", "396d33c0-f43d-4973-95fb-2c466188cfff", "864774f7-691f-43a4-937e-87eed520ba2b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68df82e5-93cf-4086-a37b-56355e2f9b7c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028544820000, "endTime": 29028546954300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "24d061f5-0110-4593-852b-79c0b6c685b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7053218-6499-4046-8246-5ac59ff254ac", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028546959200, "endTime": 29028546960600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "38f2724a-7852-408a-9944-1da2cadb9689"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73c629da-eebd-406f-8d85-29314edf5564", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028281654400, "endTime": 29028281689000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "6b4bac49-4aa1-40c8-8637-4cee91d3dd12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b4bac49-4aa1-40c8-8637-4cee91d3dd12", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028281654400, "endTime": 29028281689000}, "additional": {"logType": "info", "children": [], "durationId": "73c629da-eebd-406f-8d85-29314edf5564", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "af9252be-8674-4be1-b697-60bc8cc1d127", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028286403200, "endTime": 29028286420900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "85532d47-24b9-41b5-a6cb-6d4f1d26c865"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85532d47-24b9-41b5-a6cb-6d4f1d26c865", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028286403200, "endTime": 29028286420900}, "additional": {"logType": "info", "children": [], "durationId": "af9252be-8674-4be1-b697-60bc8cc1d127", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "5a96dbe2-58db-4340-b68f-814f693139fb", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028286547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce8216eb-c8b3-43e6-84cc-04344bbb53b0", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028290981300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b43b0401-8544-4bc2-be37-9fab464d6094", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028278743900, "endTime": 29028291134100}, "additional": {"logType": "info", "children": [], "durationId": "12580235-93e5-4e1f-9bea-5e28f451ce9e", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "456f1126-db7c-48a0-8521-8dd043561958", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028296528000, "endTime": 29028296538200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "5c01aac1-5535-4e0a-abdd-8124e0cce8ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70f863b8-6f40-4f7c-801a-c299dadca94e", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028296552400, "endTime": 29028299750600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "98020e33-fe72-401b-8adb-3c91149ed0fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fc8f106-070f-401e-9293-6e5a846dfffc", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028299774300, "endTime": 29028389214200}, "additional": {"children": ["e9b015aa-0d96-4e9a-9fdd-093a4f1d5e4d", "29491cb1-f4b2-44e1-a1c4-5281809ed4c1", "5d5789d3-6668-4d51-9b20-5c142e3a93cd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "60ed5ab5-da4d-448f-905a-6c6a7d693514"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "549ddf5f-c2ae-4523-bf9b-0ef72f215548", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028389228500, "endTime": 29028412615300}, "additional": {"children": ["a8149b9c-fb72-4b15-ae9b-a602fa8940a2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "dd95564f-66a5-459c-bcc6-3ee933f3ea33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "383a83b1-8aef-4b50-8a3f-a8ad74ef7581", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028412623800, "endTime": 29028500253600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "86f6a35f-cc39-46a6-b6da-f8fdcc13bc28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ca3fd7d-fe85-4086-b665-df2428acac54", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028501212300, "endTime": 29028519226600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "d373128c-0ab6-4c26-90ac-a5d3abb3e573"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c9fcfb0-64de-41ee-ac6e-6c3e1cdbfc18", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028519251100, "endTime": 29028544579900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "2fe79f78-eb3c-4105-b74f-2c04863f1edc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "396d33c0-f43d-4973-95fb-2c466188cfff", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028544602000, "endTime": 29028544747900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "f8cf298b-61fb-4fcb-989f-518f380e8368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c01aac1-5535-4e0a-abdd-8124e0cce8ae", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028296528000, "endTime": 29028296538200}, "additional": {"logType": "info", "children": [], "durationId": "456f1126-db7c-48a0-8521-8dd043561958", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "98020e33-fe72-401b-8adb-3c91149ed0fe", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028296552400, "endTime": 29028299750600}, "additional": {"logType": "info", "children": [], "durationId": "70f863b8-6f40-4f7c-801a-c299dadca94e", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "e9b015aa-0d96-4e9a-9fdd-093a4f1d5e4d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028300455500, "endTime": 29028300482600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fc8f106-070f-401e-9293-6e5a846dfffc", "logId": "fb4ceab0-b2f9-47ca-a3a7-d1a475361465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb4ceab0-b2f9-47ca-a3a7-d1a475361465", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028300455500, "endTime": 29028300482600}, "additional": {"logType": "info", "children": [], "durationId": "e9b015aa-0d96-4e9a-9fdd-093a4f1d5e4d", "parent": "60ed5ab5-da4d-448f-905a-6c6a7d693514"}}, {"head": {"id": "29491cb1-f4b2-44e1-a1c4-5281809ed4c1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028302713600, "endTime": 29028388193700}, "additional": {"children": ["00b71c46-f52b-478f-886a-12e2adf3eeb5", "0b309e13-5cdf-49ab-b044-634e82512180"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fc8f106-070f-401e-9293-6e5a846dfffc", "logId": "611dffb8-c22f-4c0a-bed0-0757d0296419"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00b71c46-f52b-478f-886a-12e2adf3eeb5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028302715000, "endTime": 29028307894800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "29491cb1-f4b2-44e1-a1c4-5281809ed4c1", "logId": "c078aed6-aae0-4e14-8cd3-02267196f76c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b309e13-5cdf-49ab-b044-634e82512180", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028307911400, "endTime": 29028388174700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "29491cb1-f4b2-44e1-a1c4-5281809ed4c1", "logId": "a9a718e2-3572-4520-807b-cb2f38e70bfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbdd399c-1ef8-46d4-b84c-26a380266b42", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028302720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718b181e-0f56-4af0-8f34-2dad7a8dfcce", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028307759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c078aed6-aae0-4e14-8cd3-02267196f76c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028302715000, "endTime": 29028307894800}, "additional": {"logType": "info", "children": [], "durationId": "00b71c46-f52b-478f-886a-12e2adf3eeb5", "parent": "611dffb8-c22f-4c0a-bed0-0757d0296419"}}, {"head": {"id": "1c06d682-78f1-45b8-b55d-f4a266223e6e", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028307919700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e909c7a8-dc09-4f81-9423-b7f5ac65323f", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028314548100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0316d7-cd19-4faf-9dcc-d1e928f10755", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028314655800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e262d081-2983-4a31-b794-56f9f43d17b9", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028314778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b678b42-86ed-4cae-bd87-5ddcb9e78b7e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028314926400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d110e98-b6b1-41b6-ad35-359e7c89d59c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028316227100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a9dbfaa-2b9c-4263-ab36-0ede6adc2f0b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028321030900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50333c68-a788-4e77-a0b7-6e8da9f714d7", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028331264000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bab4991-d7ea-46d3-af5b-1e720d83f157", "name": "Sdk init in 40 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028361888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb962929-ac14-49a9-9925-02324c627a27", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028362034800}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 33}, "markType": "other"}}, {"head": {"id": "50bea70e-90d7-4572-923f-07b2f291b1f9", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028362049400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 33}, "markType": "other"}}, {"head": {"id": "4d85e15e-d574-4374-985e-3584ff692e8a", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028387614300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c337be4-2b61-4db2-bba3-cbc7df49f24b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028387763900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de788bc-c6ca-4b60-9b73-22e83bc8454b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028387998500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3759812a-f51c-4a81-9468-689b9db93aa9", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028388114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a718e2-3572-4520-807b-cb2f38e70bfd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028307911400, "endTime": 29028388174700}, "additional": {"logType": "info", "children": [], "durationId": "0b309e13-5cdf-49ab-b044-634e82512180", "parent": "611dffb8-c22f-4c0a-bed0-0757d0296419"}}, {"head": {"id": "611dffb8-c22f-4c0a-bed0-0757d0296419", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028302713600, "endTime": 29028388193700}, "additional": {"logType": "info", "children": ["c078aed6-aae0-4e14-8cd3-02267196f76c", "a9a718e2-3572-4520-807b-cb2f38e70bfd"], "durationId": "29491cb1-f4b2-44e1-a1c4-5281809ed4c1", "parent": "60ed5ab5-da4d-448f-905a-6c6a7d693514"}}, {"head": {"id": "5d5789d3-6668-4d51-9b20-5c142e3a93cd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028389180200, "endTime": 29028389198600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fc8f106-070f-401e-9293-6e5a846dfffc", "logId": "a499d431-b85a-4d54-b95d-27ce290d2dfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a499d431-b85a-4d54-b95d-27ce290d2dfb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028389180200, "endTime": 29028389198600}, "additional": {"logType": "info", "children": [], "durationId": "5d5789d3-6668-4d51-9b20-5c142e3a93cd", "parent": "60ed5ab5-da4d-448f-905a-6c6a7d693514"}}, {"head": {"id": "60ed5ab5-da4d-448f-905a-6c6a7d693514", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028299774300, "endTime": 29028389214200}, "additional": {"logType": "info", "children": ["fb4ceab0-b2f9-47ca-a3a7-d1a475361465", "611dffb8-c22f-4c0a-bed0-0757d0296419", "a499d431-b85a-4d54-b95d-27ce290d2dfb"], "durationId": "8fc8f106-070f-401e-9293-6e5a846dfffc", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "a8149b9c-fb72-4b15-ae9b-a602fa8940a2", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028389884600, "endTime": 29028412603900}, "additional": {"children": ["582fa434-3c82-4c15-ba80-04a8c58c583d", "998ea750-a3a3-4c62-a6e6-893829a5fdf0", "7d4d69df-ff98-4c79-9e10-6aa3570a02ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "549ddf5f-c2ae-4523-bf9b-0ef72f215548", "logId": "cd7f1eda-90c8-4ac8-8d7e-0f9f382499e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "582fa434-3c82-4c15-ba80-04a8c58c583d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028392756500, "endTime": 29028392775700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8149b9c-fb72-4b15-ae9b-a602fa8940a2", "logId": "71b3c752-58da-4a99-8e91-3e1e481764ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71b3c752-58da-4a99-8e91-3e1e481764ce", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028392756500, "endTime": 29028392775700}, "additional": {"logType": "info", "children": [], "durationId": "582fa434-3c82-4c15-ba80-04a8c58c583d", "parent": "cd7f1eda-90c8-4ac8-8d7e-0f9f382499e4"}}, {"head": {"id": "998ea750-a3a3-4c62-a6e6-893829a5fdf0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028394502200, "endTime": 29028411056200}, "additional": {"children": ["17a0311b-dd75-45b1-ac7c-cbd3935da04a", "f369d834-7ead-4585-b976-d5b460d91966"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8149b9c-fb72-4b15-ae9b-a602fa8940a2", "logId": "5c10d6e9-997f-4027-8477-3f9cd1d56b0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17a0311b-dd75-45b1-ac7c-cbd3935da04a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028394503800, "endTime": 29028397355800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "998ea750-a3a3-4c62-a6e6-893829a5fdf0", "logId": "52e9e3bc-6388-4b7b-8ed4-9a0f1359f172"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f369d834-7ead-4585-b976-d5b460d91966", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028397390200, "endTime": 29028411041200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "998ea750-a3a3-4c62-a6e6-893829a5fdf0", "logId": "4b98e5a8-8b53-4410-91b1-1da42d5c00ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0409aa8-9b77-429e-838c-5ac0f8302802", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028394508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f8d5da-a703-4be4-9b40-82472e60c374", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028397226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e9e3bc-6388-4b7b-8ed4-9a0f1359f172", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028394503800, "endTime": 29028397355800}, "additional": {"logType": "info", "children": [], "durationId": "17a0311b-dd75-45b1-ac7c-cbd3935da04a", "parent": "5c10d6e9-997f-4027-8477-3f9cd1d56b0b"}}, {"head": {"id": "e2c98cbd-482a-4427-9689-6ee0ac55a83d", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028397407000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd73983e-2f5c-4093-a18c-60686ae2de4c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028405046300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b8d6617-7874-4451-ba16-7bd0d61e91f7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028405181300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37a8b674-8c65-42c6-aae8-f94cc7bc9437", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028406683400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "becd5a55-5f07-4f96-abea-4fa9ff20365b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028406875800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99fd742-f018-4674-b472-ef622ed6993a", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028406938400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374c483e-050e-4e5f-b522-5cc3a1ae0b7f", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028406985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1f1379-14cd-4c8e-a84f-7d58e0e7b666", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028407044100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b101e5-1565-4c85-92eb-7289d3b04588", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028410636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd5afcb-26d4-4a1a-8bef-3e63ec0ac1bc", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028410847200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed29ff12-0c86-4803-81dd-5b031150bd4a", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028410934500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3896bc53-73e0-493e-bdd7-0160428fd4d1", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028410989900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b98e5a8-8b53-4410-91b1-1da42d5c00ba", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028397390200, "endTime": 29028411041200}, "additional": {"logType": "info", "children": [], "durationId": "f369d834-7ead-4585-b976-d5b460d91966", "parent": "5c10d6e9-997f-4027-8477-3f9cd1d56b0b"}}, {"head": {"id": "5c10d6e9-997f-4027-8477-3f9cd1d56b0b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028394502200, "endTime": 29028411056200}, "additional": {"logType": "info", "children": ["52e9e3bc-6388-4b7b-8ed4-9a0f1359f172", "4b98e5a8-8b53-4410-91b1-1da42d5c00ba"], "durationId": "998ea750-a3a3-4c62-a6e6-893829a5fdf0", "parent": "cd7f1eda-90c8-4ac8-8d7e-0f9f382499e4"}}, {"head": {"id": "7d4d69df-ff98-4c79-9e10-6aa3570a02ed", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028412572500, "endTime": 29028412589400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a8149b9c-fb72-4b15-ae9b-a602fa8940a2", "logId": "567123b8-bfa5-4d32-80cc-5bc7bbb45698"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "567123b8-bfa5-4d32-80cc-5bc7bbb45698", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028412572500, "endTime": 29028412589400}, "additional": {"logType": "info", "children": [], "durationId": "7d4d69df-ff98-4c79-9e10-6aa3570a02ed", "parent": "cd7f1eda-90c8-4ac8-8d7e-0f9f382499e4"}}, {"head": {"id": "cd7f1eda-90c8-4ac8-8d7e-0f9f382499e4", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028389884600, "endTime": 29028412603900}, "additional": {"logType": "info", "children": ["71b3c752-58da-4a99-8e91-3e1e481764ce", "5c10d6e9-997f-4027-8477-3f9cd1d56b0b", "567123b8-bfa5-4d32-80cc-5bc7bbb45698"], "durationId": "a8149b9c-fb72-4b15-ae9b-a602fa8940a2", "parent": "dd95564f-66a5-459c-bcc6-3ee933f3ea33"}}, {"head": {"id": "dd95564f-66a5-459c-bcc6-3ee933f3ea33", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028389228500, "endTime": 29028412615300}, "additional": {"logType": "info", "children": ["cd7f1eda-90c8-4ac8-8d7e-0f9f382499e4"], "durationId": "549ddf5f-c2ae-4523-bf9b-0ef72f215548", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "ea2d561e-e2ab-420f-b9c6-782094a4db00", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028443457800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851db1d5-9865-4ad0-bca2-35303b1ecca5", "name": "hvigorfile, resolve hvigorfile dependencies in 88 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028500123800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f6a35f-cc39-46a6-b6da-f8fdcc13bc28", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028412623800, "endTime": 29028500253600}, "additional": {"logType": "info", "children": [], "durationId": "383a83b1-8aef-4b50-8a3f-a8ad74ef7581", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "864774f7-691f-43a4-937e-87eed520ba2b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028500950000, "endTime": 29028501194200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "logId": "7fc50f13-2826-46f5-ab79-89c07013a013"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e91b820-5d2c-4296-a008-d6749eddbb2b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028500977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fc50f13-2826-46f5-ab79-89c07013a013", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028500950000, "endTime": 29028501194200}, "additional": {"logType": "info", "children": [], "durationId": "864774f7-691f-43a4-937e-87eed520ba2b", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "05491f3b-7250-4a32-a371-ba9cc87580fe", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028502393600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c75ad8f-1bcd-48f7-97c2-6681934c2b27", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028518507100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d373128c-0ab6-4c26-90ac-a5d3abb3e573", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028501212300, "endTime": 29028519226600}, "additional": {"logType": "info", "children": [], "durationId": "9ca3fd7d-fe85-4086-b665-df2428acac54", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "1fb7f6a0-2b08-4cc9-9ece-8cf6bf613438", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028528509900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1da075-5237-4fa3-92f0-8008babbb7df", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028528670900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f27294f-da12-424f-a889-becb564df974", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028531033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c53f61c1-605c-4b6e-9c61-9759647825c3", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028531239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fe79f78-eb3c-4105-b74f-2c04863f1edc", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028519251100, "endTime": 29028544579900}, "additional": {"logType": "info", "children": [], "durationId": "3c9fcfb0-64de-41ee-ac6e-6c3e1cdbfc18", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "1e005825-728b-4be2-852c-fcfec6bbdd79", "name": "Configuration phase cost:249 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028544626200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8cf298b-61fb-4fcb-989f-518f380e8368", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028544602000, "endTime": 29028544747900}, "additional": {"logType": "info", "children": [], "durationId": "396d33c0-f43d-4973-95fb-2c466188cfff", "parent": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7"}}, {"head": {"id": "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028291158200, "endTime": 29028544758100}, "additional": {"logType": "info", "children": ["5c01aac1-5535-4e0a-abdd-8124e0cce8ae", "98020e33-fe72-401b-8adb-3c91149ed0fe", "60ed5ab5-da4d-448f-905a-6c6a7d693514", "dd95564f-66a5-459c-bcc6-3ee933f3ea33", "86f6a35f-cc39-46a6-b6da-f8fdcc13bc28", "d373128c-0ab6-4c26-90ac-a5d3abb3e573", "2fe79f78-eb3c-4105-b74f-2c04863f1edc", "f8cf298b-61fb-4fcb-989f-518f380e8368", "7fc50f13-2826-46f5-ab79-89c07013a013"], "durationId": "0c0238e4-647b-4224-8ce6-3f56ef7f3c1c", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "acb78681-1c80-41e6-a5f9-343ae91c92e3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028546915100, "endTime": 29028546938700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38d474aa-caff-451d-9ced-7ba7fa8d5d91", "logId": "56e001e3-4519-4491-a6ac-d519a762539b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56e001e3-4519-4491-a6ac-d519a762539b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028546915100, "endTime": 29028546938700}, "additional": {"logType": "info", "children": [], "durationId": "acb78681-1c80-41e6-a5f9-343ae91c92e3", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "24d061f5-0110-4593-852b-79c0b6c685b2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028544820000, "endTime": 29028546954300}, "additional": {"logType": "info", "children": [], "durationId": "68df82e5-93cf-4086-a37b-56355e2f9b7c", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "38f2724a-7852-408a-9944-1da2cadb9689", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028546959200, "endTime": 29028546960600}, "additional": {"logType": "info", "children": [], "durationId": "b7053218-6499-4046-8246-5ac59ff254ac", "parent": "08f244dd-4c65-43d4-92d3-d8549c7e14bc"}}, {"head": {"id": "08f244dd-4c65-43d4-92d3-d8549c7e14bc", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028278741800, "endTime": 29028546964700}, "additional": {"logType": "info", "children": ["b43b0401-8544-4bc2-be37-9fab464d6094", "db036722-2d25-4ac7-b5e9-3bf3ccac2fb7", "24d061f5-0110-4593-852b-79c0b6c685b2", "38f2724a-7852-408a-9944-1da2cadb9689", "6b4bac49-4aa1-40c8-8637-4cee91d3dd12", "85532d47-24b9-41b5-a6cb-6d4f1d26c865", "56e001e3-4519-4491-a6ac-d519a762539b"], "durationId": "38d474aa-caff-451d-9ced-7ba7fa8d5d91"}}, {"head": {"id": "0338d023-3ac3-4227-bb39-1065a77b1258", "name": "Configuration task cost before running: 272 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028547182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4ca76bb-c30f-4de6-b85a-662895659a87", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028554866200, "endTime": 29028563828300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3a4276a7-3609-4e85-8548-132b6aa2c82c", "logId": "8a1c2e17-8cd5-455d-a9fd-f25da4ee9dc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a4276a7-3609-4e85-8548-132b6aa2c82c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028549617800}, "additional": {"logType": "detail", "children": [], "durationId": "c4ca76bb-c30f-4de6-b85a-662895659a87"}}, {"head": {"id": "6bc7d3e2-8de7-4a23-ae7d-d04544d6234b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028550137900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37af8a0f-0ac2-4f63-b520-71e9dcdcfabe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028550441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73daa125-209e-43cb-b1c4-49d8b076b802", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028554885500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad816aad-0a9b-410f-8ebd-fdaa867218e5", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028563569500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa2959b-af98-4473-b3f3-af42881d0916", "name": "entry : default@PreBuild cost memory 0.30910491943359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028563719600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1c2e17-8cd5-455d-a9fd-f25da4ee9dc5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028554866200, "endTime": 29028563828300}, "additional": {"logType": "info", "children": [], "durationId": "c4ca76bb-c30f-4de6-b85a-662895659a87"}}, {"head": {"id": "f07b4607-5cad-4c1d-a389-833a51fc537e", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028571455200, "endTime": 29028575671600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "607b28d1-8332-4fd2-a59e-6ae1756bd187", "logId": "c81c6776-5337-4fca-b2fe-fd27775fe5f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "607b28d1-8332-4fd2-a59e-6ae1756bd187", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028568605400}, "additional": {"logType": "detail", "children": [], "durationId": "f07b4607-5cad-4c1d-a389-833a51fc537e"}}, {"head": {"id": "915279ad-5fec-429e-8a02-c809d35f0064", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028569499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897e27ba-0d4f-436a-a7ea-86f1ca156391", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028569619600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "066aee7c-e42f-4cb8-8617-58366c5ed208", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028571475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3f4263-a321-4d07-a699-566a161ed33b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028573196400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d362419-411d-48b3-8b80-0c3756bcdb3b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028575136600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ede350-8053-4f8d-ba1d-815084ca6273", "name": "entry : default@GenerateMetadata cost memory 0.0968170166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028575411000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c81c6776-5337-4fca-b2fe-fd27775fe5f9", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028571455200, "endTime": 29028575671600}, "additional": {"logType": "info", "children": [], "durationId": "f07b4607-5cad-4c1d-a389-833a51fc537e"}}, {"head": {"id": "45586612-3ce0-4109-8931-7d1ce47353c5", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578727400, "endTime": 29028579214100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5358fc73-2bd0-4083-843f-976864ba41ae", "logId": "0b658dd2-57f4-48bf-bd47-5b25530ee5f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5358fc73-2bd0-4083-843f-976864ba41ae", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028577715500}, "additional": {"logType": "detail", "children": [], "durationId": "45586612-3ce0-4109-8931-7d1ce47353c5"}}, {"head": {"id": "a89c3634-89d5-43ea-a1bb-9510f99b824d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2616176a-343c-4ccf-9d55-ee4ab06c1c2e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578429200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155f0cda-95f4-4087-9606-741e5c141307", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578748500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4ac186d-8000-440e-89a8-1004d571a12c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19298e33-8b1b-4e3c-ba07-409a05ed87e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b9cc27-2b2e-416e-b486-cd5705cc6af0", "name": "entry : default@ConfigureCmake cost memory 0.0366058349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578992300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67c06454-74d2-4ca5-b28d-824c8aee6bb5", "name": "runTaskFromQueue task cost before running: 304 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028579102400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b658dd2-57f4-48bf-bd47-5b25530ee5f4", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028578727400, "endTime": 29028579214100, "totalTime": 358600}, "additional": {"logType": "info", "children": [], "durationId": "45586612-3ce0-4109-8931-7d1ce47353c5"}}, {"head": {"id": "46f5da04-8ac8-495a-833e-aef7a989fba0", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028583782200, "endTime": 29028585714700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dc6ae3bb-9daf-4624-a0d0-db0aeb6997f6", "logId": "9c9f1600-956e-4798-b666-7f43c95f78a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc6ae3bb-9daf-4624-a0d0-db0aeb6997f6", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028581796000}, "additional": {"logType": "detail", "children": [], "durationId": "46f5da04-8ac8-495a-833e-aef7a989fba0"}}, {"head": {"id": "28bf8e72-94b2-4314-944d-874cbb99ef46", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028582762700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcab0ec-5948-416f-afa9-c78d62dc23d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028582924700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94253c45-1954-4e8d-a1d0-822d3518dc8d", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028583816000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ed0f59d-c024-4afc-8bff-7ed54e410f8c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028585475400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c3777ee-c8e0-4f4f-bdb0-43b3854585d8", "name": "entry : default@MergeProfile cost memory 0.106964111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028585625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c9f1600-956e-4798-b666-7f43c95f78a6", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028583782200, "endTime": 29028585714700}, "additional": {"logType": "info", "children": [], "durationId": "46f5da04-8ac8-495a-833e-aef7a989fba0"}}, {"head": {"id": "00703b79-fffe-4ee2-b636-c86e1fff62a8", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028592632000, "endTime": 29028595581400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cd27aaba-3ec2-4f54-9fbd-8d8084da0325", "logId": "dd06a73c-86fa-40eb-91fb-a2e6f6aa40a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd27aaba-3ec2-4f54-9fbd-8d8084da0325", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028590903500}, "additional": {"logType": "detail", "children": [], "durationId": "00703b79-fffe-4ee2-b636-c86e1fff62a8"}}, {"head": {"id": "d3720549-d101-45d7-96a0-593aeb288f3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028591511300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bcf94e-2bef-4f51-b0ff-74bd64271317", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028591631800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a713f75e-ed60-42cc-9feb-d5426c9e01a0", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028592647600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a007e316-a22e-458b-aba3-14fdd049bc1e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028593817800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0acaba61-152e-4803-bfe9-6afe13fcf83d", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028595136100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b72c652-13fe-4ad7-8f9c-69c52c6200a9", "name": "entry : default@CreateBuildProfile cost memory 0.105987548828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028595332300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd06a73c-86fa-40eb-91fb-a2e6f6aa40a6", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028592632000, "endTime": 29028595581400}, "additional": {"logType": "info", "children": [], "durationId": "00703b79-fffe-4ee2-b636-c86e1fff62a8"}}, {"head": {"id": "d637592f-5cda-4217-b33e-0ca0f10ce69c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028599464400, "endTime": 29028600164800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f0fafda8-eefb-4ebd-aa4f-063bc8ed75d4", "logId": "7405d88a-9a44-40d7-a782-f7488e4807d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0fafda8-eefb-4ebd-aa4f-063bc8ed75d4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028597895500}, "additional": {"logType": "detail", "children": [], "durationId": "d637592f-5cda-4217-b33e-0ca0f10ce69c"}}, {"head": {"id": "f615c002-5cd4-40f5-b7c1-4756f9ce745f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028598524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c8d753-a163-4933-950c-6c1357b0d02a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028598639700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a219acc0-5807-4e2e-b882-89a7644bcabd", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028599476000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60916e63-8b71-48f9-8862-b6cd02193afe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028599614000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f53b1f0-11c7-487c-90f2-9e26fd2b0af7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028599744200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7fd94c4-821e-4b7b-af85-5d9213994283", "name": "entry : default@PreCheckSyscap cost memory 0.0368499755859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028599913900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "047e5bb5-f644-49ec-9ede-084685523f8c", "name": "runTaskFromQueue task cost before running: 325 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028600081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7405d88a-9a44-40d7-a782-f7488e4807d5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028599464400, "endTime": 29028600164800, "totalTime": 588700}, "additional": {"logType": "info", "children": [], "durationId": "d637592f-5cda-4217-b33e-0ca0f10ce69c"}}, {"head": {"id": "2ba35806-1e8b-4737-a694-53fcf668b583", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028613981900, "endTime": 29028616479700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3064283d-01be-45f0-9598-4804e083e205", "logId": "3c2888a3-45e3-421f-94cd-81f4de9e736d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3064283d-01be-45f0-9598-4804e083e205", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028602541100}, "additional": {"logType": "detail", "children": [], "durationId": "2ba35806-1e8b-4737-a694-53fcf668b583"}}, {"head": {"id": "5fff98f1-853a-41dd-b3d4-f83bba98f600", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028603112500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf58ad5-3127-491a-856a-defa4b9aa684", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028603699600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0f0f72-3e61-432d-91c7-a8c00f221a18", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028614002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9630b4a-f41d-49d5-9e55-63138b852d6c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028614525000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a9d2bd-a65b-495c-a584-1a84c84b7a50", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0391845703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028615836100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4243be62-b632-490b-804e-2583630feb68", "name": "runTaskFromQueue task cost before running: 341 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028615992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c2888a3-45e3-421f-94cd-81f4de9e736d", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028613981900, "endTime": 29028616479700, "totalTime": 1985700}, "additional": {"logType": "info", "children": [], "durationId": "2ba35806-1e8b-4737-a694-53fcf668b583"}}, {"head": {"id": "639b1473-2ab9-4e36-aa7e-33d6ef40e62b", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028640244000, "endTime": 29028642990500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "a1b9a04c-17b6-4285-b6e4-848cee21e218", "logId": "3140a2a8-86f1-4832-832a-d1381af2e70c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1b9a04c-17b6-4285-b6e4-848cee21e218", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028626981900}, "additional": {"logType": "detail", "children": [], "durationId": "639b1473-2ab9-4e36-aa7e-33d6ef40e62b"}}, {"head": {"id": "1f0bff44-34a3-41f0-842c-b1efe2845df0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028631341400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a7e76ca-796b-45d3-899e-b4b2aaa345d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028631748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c07b85c-e23a-4058-a7b0-4aad2900b27c", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028640261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93334383-df9f-48a9-9954-6a3646530723", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028642208100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d76bb315-f29f-48f5-82c9-4b780abf1b55", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028642493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4e09a4-6f6c-4406-984b-b8f5435646e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028642660200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60f252b9-6a05-49af-9ee1-4f2918d7b721", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028642726200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c714cf-17c0-4e68-8816-7bc71d36523c", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1188812255859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028642829700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c0b718f-aa33-49f4-8b90-3d687c265692", "name": "runTaskFromQueue task cost before running: 368 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028642929600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3140a2a8-86f1-4832-832a-d1381af2e70c", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028640244000, "endTime": 29028642990500, "totalTime": 2664300}, "additional": {"logType": "info", "children": [], "durationId": "639b1473-2ab9-4e36-aa7e-33d6ef40e62b"}}, {"head": {"id": "cc054d2c-a3e9-4470-97bd-78015fbe235c", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028647613200, "endTime": 29028648608700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b3c3ab42-9f1e-4127-a36f-ff5ea6a35d7f", "logId": "29139f8a-361a-42ea-9372-5fcd63e5f5af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3c3ab42-9f1e-4127-a36f-ff5ea6a35d7f", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028645794300}, "additional": {"logType": "detail", "children": [], "durationId": "cc054d2c-a3e9-4470-97bd-78015fbe235c"}}, {"head": {"id": "e92c38a1-141d-46c8-a399-7fe537a85679", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028646296600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cd37133-ea0c-4ef8-af8a-b3a72cc5ad53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028646432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964d2a04-3ce3-4781-bc50-28b5f61a7a7a", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028647631900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c33bcb97-af09-4bd3-af2d-eb9605f2f56d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028647891200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4b63054-cc86-4d9c-acfd-2307fe294586", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028647978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "222c74d8-308c-4d82-b376-8fd41ddd9487", "name": "entry : default@BuildNativeWithCmake cost memory 0.03765869140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028648283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd381c47-26f5-471e-b4bc-bcd6e06ce8b0", "name": "runTaskFromQueue task cost before running: 374 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028648494000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29139f8a-361a-42ea-9372-5fcd63e5f5af", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028647613200, "endTime": 29028648608700, "totalTime": 790900}, "additional": {"logType": "info", "children": [], "durationId": "cc054d2c-a3e9-4470-97bd-78015fbe235c"}}, {"head": {"id": "2b0549ff-2a13-4596-814d-2e5f97e54dab", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028652661200, "endTime": 29028659860300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1bc21347-f30d-4e92-9fb4-ea78cd26088c", "logId": "a3dc5116-b762-4363-a24b-e81056b75981"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bc21347-f30d-4e92-9fb4-ea78cd26088c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028650878000}, "additional": {"logType": "detail", "children": [], "durationId": "2b0549ff-2a13-4596-814d-2e5f97e54dab"}}, {"head": {"id": "90d95392-63f8-41b2-81a4-bd87d92f1fe6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028651302200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9fb91a-f7c8-470e-a420-4fc31e44e561", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028651530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63805307-96cf-4e00-8e19-80df8149e33d", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028652676000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed6123f1-1533-4780-9914-059ee52df7dd", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028659624300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f86831b-67b5-43a4-9e36-4a2fc4548594", "name": "entry : default@MakePackInfo cost memory 0.13982391357421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028659760800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3dc5116-b762-4363-a24b-e81056b75981", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028652661200, "endTime": 29028659860300}, "additional": {"logType": "info", "children": [], "durationId": "2b0549ff-2a13-4596-814d-2e5f97e54dab"}}, {"head": {"id": "63537a84-8889-4c01-a4a6-90d8cb94e410", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028688732800, "endTime": 29028716920300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "54a58746-9628-4b6a-ae68-afc4e6d2bb43", "logId": "950f4401-1d41-48cd-a714-08014c858618"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54a58746-9628-4b6a-ae68-afc4e6d2bb43", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028664112900}, "additional": {"logType": "detail", "children": [], "durationId": "63537a84-8889-4c01-a4a6-90d8cb94e410"}}, {"head": {"id": "46238b2d-8925-43a5-a51f-615ad841b1f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028664779200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66e4754a-ded2-4ffc-bbfd-392b8adb771f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028675942500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a87056a-8921-4f9e-bc31-7f5802463d38", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028688752900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca8e70b-9dce-45bc-a8c9-5efa0631b23a", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028691162400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "284a5b5b-615b-4a48-9c9b-3889809f3010", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028695547900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526500be-0553-4ba7-beaa-74edd0a065bf", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028705391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e7ff541-50be-4e5e-afa2-307be8894982", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028709781100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "608e5c82-c258-4872-a54f-8d64caed9b0d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028709996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9b2df7-00ad-4473-b8b3-21bd8376e75a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028710939200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98a383fb-f150-40cc-8951-5d6357a4d994", "name": "entry : default@SyscapTransform cost memory 0.15512847900390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028711530700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e772ffb-6f49-4664-96b6-7e1fcf13879f", "name": "runTaskFromQueue task cost before running: 437 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028711855200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "950f4401-1d41-48cd-a714-08014c858618", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028688732800, "endTime": 29028716920300, "totalTime": 23046900}, "additional": {"logType": "info", "children": [], "durationId": "63537a84-8889-4c01-a4a6-90d8cb94e410"}}, {"head": {"id": "33375a60-dfde-4ce2-ae7b-7d1080e554dd", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028738810300, "endTime": 29028743016800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "94c49393-0511-4aab-9ba6-ae775bc1ab35", "logId": "4803ad39-eb3b-4018-9fdc-4267a3d81b92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94c49393-0511-4aab-9ba6-ae775bc1ab35", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028729638100}, "additional": {"logType": "detail", "children": [], "durationId": "33375a60-dfde-4ce2-ae7b-7d1080e554dd"}}, {"head": {"id": "230ec018-31d5-468f-b7a5-2230b5182ea5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028734681500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2a63c5e-c5d7-436c-abfd-dbf1849a6329", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028734924700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916df3dc-2260-40b8-8dab-062157043591", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028738874500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e9843c-5548-4308-adf6-655b95083920", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028742492800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e02f787-508a-41a7-9981-b812095abeac", "name": "entry : default@ProcessProfile cost memory 0.0614471435546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028742625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4803ad39-eb3b-4018-9fdc-4267a3d81b92", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028738810300, "endTime": 29028743016800}, "additional": {"logType": "info", "children": [], "durationId": "33375a60-dfde-4ce2-ae7b-7d1080e554dd"}}, {"head": {"id": "898203ec-2235-4a67-b481-eba7ef83340b", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028751980400, "endTime": 29028760337200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "be9e958c-7592-4181-b468-0d2b61a2a3f2", "logId": "8d4b0638-3a33-4624-8d75-73f0cfed4ca5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be9e958c-7592-4181-b468-0d2b61a2a3f2", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028747058000}, "additional": {"logType": "detail", "children": [], "durationId": "898203ec-2235-4a67-b481-eba7ef83340b"}}, {"head": {"id": "4049ac99-0578-4d41-8738-3c54af328fbc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028747973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1132757e-9d36-48b9-9ae9-255ac4908e6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028748601600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc97218-8aa1-465b-967f-f0c2cf6d79be", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028751996100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d091bd-f0a3-4a0c-b2b7-45147b6e5a6a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028759891900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c885b9-be81-475b-9d2d-efefcd3af911", "name": "entry : default@ProcessRouterMap cost memory 0.44672393798828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028760095300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4b0638-3a33-4624-8d75-73f0cfed4ca5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028751980400, "endTime": 29028760337200}, "additional": {"logType": "info", "children": [], "durationId": "898203ec-2235-4a67-b481-eba7ef83340b"}}, {"head": {"id": "88220254-6334-42f7-afc9-15a827cccda0", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028770596300, "endTime": 29028773423500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4d169c06-fd57-4bae-8e7c-d549a77ded0c", "logId": "aaf677a2-aeca-495a-8541-fb0208489a40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d169c06-fd57-4bae-8e7c-d549a77ded0c", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028765904300}, "additional": {"logType": "detail", "children": [], "durationId": "88220254-6334-42f7-afc9-15a827cccda0"}}, {"head": {"id": "0e1a4549-6b4c-4a4b-95a2-0571b8d75bfc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028767568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfca4a49-0536-41c0-974d-2d1098e63f6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028767939100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8be7914-ab9a-432f-a704-e4ded716dce4", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028770612500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67bb1a05-84ed-40a2-a60e-5bd848649000", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028771484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ccd716-6c55-4bec-b9f7-4c9a9dc24982", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028771903000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "325d2788-5b52-4164-959d-270bdab8b5a3", "name": "entry : default@BuildNativeWithNinja cost memory 0.05725860595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028772992100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8861d5-cb2f-4081-90fc-b2e9b54daf33", "name": "runTaskFromQueue task cost before running: 498 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028773169200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf677a2-aeca-495a-8541-fb0208489a40", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028770596300, "endTime": 29028773423500, "totalTime": 2532200}, "additional": {"logType": "info", "children": [], "durationId": "88220254-6334-42f7-afc9-15a827cccda0"}}, {"head": {"id": "06f732c1-9852-4aea-80c6-2b56adde75dd", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028781746300, "endTime": 29028789536900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a315a869-1fec-421d-bae4-209a805ce410", "logId": "53e4000e-3182-43bd-9480-019eea56186e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a315a869-1fec-421d-bae4-209a805ce410", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028777711200}, "additional": {"logType": "detail", "children": [], "durationId": "06f732c1-9852-4aea-80c6-2b56adde75dd"}}, {"head": {"id": "46efebaf-18ac-4841-89ef-99ea5dd81d8a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028778181300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd878422-19f0-47e4-b6b6-48a8c4792130", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028778345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd683eb-4a57-4e07-b361-1fc7354e274e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028780015400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b17c170-30a1-4bf0-8f02-40412e549adb", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028783739000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170a8416-26d0-476f-8112-378cfe9d8665", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028785942100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a225ae1-9648-44d3-9da1-a91ea3374fb3", "name": "entry : default@ProcessResource cost memory 0.1713104248046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028786111100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e4000e-3182-43bd-9480-019eea56186e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028781746300, "endTime": 29028789536900}, "additional": {"logType": "info", "children": [], "durationId": "06f732c1-9852-4aea-80c6-2b56adde75dd"}}, {"head": {"id": "1f7992cf-5c4c-44f4-875d-5e2167882a5d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028797546600, "endTime": 29028814481600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6ad54ebd-2b39-416b-aba0-8db2d47043b1", "logId": "c9e6e442-ed74-4712-b922-668cd1d1f417"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ad54ebd-2b39-416b-aba0-8db2d47043b1", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028793471200}, "additional": {"logType": "detail", "children": [], "durationId": "1f7992cf-5c4c-44f4-875d-5e2167882a5d"}}, {"head": {"id": "0119dd8f-d4ea-47a5-a482-2cfd1d845484", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028793865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368f8d34-693c-46d2-87f9-465079981817", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028793977200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d606cec-fc61-4fb2-ae1c-c5a71dc05bd7", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028797563500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85b2309e-2b06-44b4-a353-14b635cb5a0e", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028814269100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32716696-2dde-4d2c-adf0-982208d5a329", "name": "entry : default@GenerateLoaderJson cost memory 0.7618560791015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028814406900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9e6e442-ed74-4712-b922-668cd1d1f417", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028797546600, "endTime": 29028814481600}, "additional": {"logType": "info", "children": [], "durationId": "1f7992cf-5c4c-44f4-875d-5e2167882a5d"}}, {"head": {"id": "3cd394d4-e67c-45c2-9b23-2ee11bcb595a", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028824796000, "endTime": 29028829411100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d0ba8c27-9765-43f5-a401-314a1d2f9df3", "logId": "48e0728f-e3dc-4c82-afd3-5963cf4f950b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0ba8c27-9765-43f5-a401-314a1d2f9df3", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028822200700}, "additional": {"logType": "detail", "children": [], "durationId": "3cd394d4-e67c-45c2-9b23-2ee11bcb595a"}}, {"head": {"id": "51b1f964-4b8f-4993-8661-e04f1b002131", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028823629000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc1227d5-c524-4b43-b70b-c678d77c5a18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028823754100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a26de5-5bab-45ef-8122-5d8f0b573d7d", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028824812600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "641c5e63-caeb-4974-a9a8-27b482d104e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028828086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e8e5cd-b3ef-4a4d-8069-e18f55c9072b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028828239200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49b57f3c-fc87-419e-afee-02fe50fb601e", "name": "entry : default@ProcessLibs cost memory 0.12631988525390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028828964200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a2534ad-b4d4-45e7-9ee4-f2e13aec60bd", "name": "runTaskFromQueue task cost before running: 554 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028829139300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e0728f-e3dc-4c82-afd3-5963cf4f950b", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028824796000, "endTime": 29028829411100, "totalTime": 4306600}, "additional": {"logType": "info", "children": [], "durationId": "3cd394d4-e67c-45c2-9b23-2ee11bcb595a"}}, {"head": {"id": "4a85c4fe-078c-4e5e-a2b7-fc918f606ea1", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028841820700, "endTime": 29028879552400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ee948c7d-4cfe-4cfa-9ffa-e1eba1f035d7", "logId": "ad7146c4-b364-4ace-a4aa-5d02ea176d5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee948c7d-4cfe-4cfa-9ffa-e1eba1f035d7", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028832240400}, "additional": {"logType": "detail", "children": [], "durationId": "4a85c4fe-078c-4e5e-a2b7-fc918f606ea1"}}, {"head": {"id": "28bcd5cb-a692-4cd1-8ac6-6ddd0ea48f50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028832841300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba12ce91-7dbf-4467-a852-a31b1247fd7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028832978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc26ee41-f933-4bdf-bbb1-ed4ff5fb0c38", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028836927500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b941bee-9f44-4c5a-a2e9-8dcb3fc1dde4", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028841877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4fc363b-cb89-4523-b1b5-7a3741c8f66b", "name": "Incremental task entry:default@CompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028879131500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25c18a4e-7584-4587-a0b5-b2bc82c39fae", "name": "entry : default@CompileResource cost memory -4.440025329589844", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028879343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7146c4-b364-4ace-a4aa-5d02ea176d5f", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028841820700, "endTime": 29028879552400}, "additional": {"logType": "info", "children": [], "durationId": "4a85c4fe-078c-4e5e-a2b7-fc918f606ea1"}}, {"head": {"id": "2904eb35-13a4-47f4-bc73-cfc4ab0492b5", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028891908000, "endTime": 29028894114900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b8d9e797-259f-485f-a42b-955e6163b6a0", "logId": "d21c1ec9-f10e-4380-99c2-ca3ada526ae1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d9e797-259f-485f-a42b-955e6163b6a0", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028884298000}, "additional": {"logType": "detail", "children": [], "durationId": "2904eb35-13a4-47f4-bc73-cfc4ab0492b5"}}, {"head": {"id": "bed58759-64f3-47bf-9b28-7d0672e1d1a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028884864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b375517d-af60-4124-b48d-d25ae6cb62b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028884985500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cfabacf-3502-4f3d-98c1-e83847718679", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028891922500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3756a29c-ee14-4788-a322-e11e06e30edf", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028892490200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e68314-b5d7-42b9-8062-10a41718bed2", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028893840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add75223-3d87-4069-8aa1-17c8bf777a7f", "name": "entry : default@DoNativeStrip cost memory 0.0778045654296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028894026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21c1ec9-f10e-4380-99c2-ca3ada526ae1", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028891908000, "endTime": 29028894114900}, "additional": {"logType": "info", "children": [], "durationId": "2904eb35-13a4-47f4-bc73-cfc4ab0492b5"}}, {"head": {"id": "183f6b83-43ea-41ab-ab4e-6a0d885d66eb", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028904334700, "endTime": 29028959097200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "69239471-eb70-4df1-b329-35cfe92f4deb", "logId": "726fb5a9-0e0c-4171-b922-5f4077e74453"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69239471-eb70-4df1-b329-35cfe92f4deb", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028896517600}, "additional": {"logType": "detail", "children": [], "durationId": "183f6b83-43ea-41ab-ab4e-6a0d885d66eb"}}, {"head": {"id": "1d5004d8-cdfb-436e-b254-6e19799ba1c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028896964400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f527ea7-db57-431f-b029-40aa6cc08a56", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028897072200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4f1a83-c1e1-4959-a65e-e3e0e620f5fc", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028904370400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf66da06-6d3e-4497-8634-3294f6850e55", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 47 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028957382700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd819101-7d39-4e4d-8115-cbd85fd6804c", "name": "entry : default@CompileArkTS cost memory 0.6815643310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028957663100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726fb5a9-0e0c-4171-b922-5f4077e74453", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028904334700, "endTime": 29028959097200}, "additional": {"logType": "info", "children": [], "durationId": "183f6b83-43ea-41ab-ab4e-6a0d885d66eb"}}, {"head": {"id": "14b8058b-e235-47b4-b45a-37c1c174d1ba", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028985317700, "endTime": 29028992653000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "23929751-92fb-4344-8226-eb4e1c117079", "logId": "fd47fef1-06cc-4320-bc15-fd44f4e481f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23929751-92fb-4344-8226-eb4e1c117079", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028979387600}, "additional": {"logType": "detail", "children": [], "durationId": "14b8058b-e235-47b4-b45a-37c1c174d1ba"}}, {"head": {"id": "20206d46-fb7f-467f-b957-f17d6238d993", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028980038400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4985daea-856f-4a35-bdc1-6aecc1fb59c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028980201800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696c2831-b676-40fc-a934-302ee<PERSON><PERSON>c474", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028985338000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aed3cc9-25fb-4697-93f5-6e44b082b8ad", "name": "entry : default@BuildJS cost memory 0.1284942626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028991808900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3844ccdd-de6f-435c-a4f8-f36777a001fe", "name": "runTaskFromQueue task cost before running: 717 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028992270700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd47fef1-06cc-4320-bc15-fd44f4e481f5", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028985317700, "endTime": 29028992653000, "totalTime": 6838100}, "additional": {"logType": "info", "children": [], "durationId": "14b8058b-e235-47b4-b45a-37c1c174d1ba"}}, {"head": {"id": "15bef491-0ba3-48ab-9131-a1db3a282a71", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028999349700, "endTime": 29029001608500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a948e087-9830-470a-8bdf-73a1975e13d8", "logId": "7f26a25d-5a29-4d13-8995-43ef2de93e89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a948e087-9830-470a-8bdf-73a1975e13d8", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028995505300}, "additional": {"logType": "detail", "children": [], "durationId": "15bef491-0ba3-48ab-9131-a1db3a282a71"}}, {"head": {"id": "d707d46a-f337-40a6-baa7-54708dc03d7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028996007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4f3b41-445b-4439-8c5a-e65847bb3570", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028996132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d536f5-8f70-4c03-baa5-8620fccdbfaf", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028999364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53973cd7-3cef-4ef0-b078-3ec4ad471ad1", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028999723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd264886-392f-4407-abed-5cd3489e109d", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029001066400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c0fe8b-8340-46eb-b39f-048388e5750a", "name": "entry : default@CacheNativeLibs cost memory 0.092010498046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029001396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f26a25d-5a29-4d13-8995-43ef2de93e89", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028999349700, "endTime": 29029001608500}, "additional": {"logType": "info", "children": [], "durationId": "15bef491-0ba3-48ab-9131-a1db3a282a71"}}, {"head": {"id": "17747a02-3254-4287-8a6f-35e46fb3bf20", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029008347900, "endTime": 29029009476900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e1d17cd0-f09a-4434-a0e1-4422b9ec4ef4", "logId": "0201d7be-9c0d-4363-9136-61c2c1840ee7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1d17cd0-f09a-4434-a0e1-4422b9ec4ef4", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029004713700}, "additional": {"logType": "detail", "children": [], "durationId": "17747a02-3254-4287-8a6f-35e46fb3bf20"}}, {"head": {"id": "bca58e67-b751-48c0-a32c-968ba692a609", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029005242900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "383223b4-80ca-4b6f-8ada-2e5e73259ab0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029006884900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20fb56c-60dd-467f-82c5-4481e5b7dafe", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029008362400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5590adc9-90b3-4a63-aab6-23db10fb5558", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029008596200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbf5aa86-64a0-4d7f-ad88-ed561030fd71", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029009280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516af3c3-3786-4019-b42d-5c22126c853c", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0733795166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029009400500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0201d7be-9c0d-4363-9136-61c2c1840ee7", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029008347900, "endTime": 29029009476900}, "additional": {"logType": "info", "children": [], "durationId": "17747a02-3254-4287-8a6f-35e46fb3bf20"}}, {"head": {"id": "e03ee2b6-7e3f-4897-9899-94b143b3b604", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029021469600, "endTime": 29029041512100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "4dbcc8fa-99ed-4de6-97a8-fac534235060", "logId": "d0b90667-0193-4230-a05b-d812b9bd7d51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dbcc8fa-99ed-4de6-97a8-fac534235060", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029012173500}, "additional": {"logType": "detail", "children": [], "durationId": "e03ee2b6-7e3f-4897-9899-94b143b3b604"}}, {"head": {"id": "8a3e4861-3361-4f2f-9610-8e5bba09bb33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029012610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "907cd7b2-bfe9-4d09-9b66-25b962ba82d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029012720500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806ab93b-3bb9-4886-8a3c-ac5c1e73e89a", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029021513600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e05c0d11-c5ff-46f8-ab3c-4fcc940835b2", "name": "Incremental task entry:default@PackageHap pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029040623700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f888f2a0-6d17-43f7-b5e0-e806fa69742a", "name": "entry : default@PackageHap cost memory 0.840911865234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029041148600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b90667-0193-4230-a05b-d812b9bd7d51", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029021469600, "endTime": 29029041512100}, "additional": {"logType": "info", "children": [], "durationId": "e03ee2b6-7e3f-4897-9899-94b143b3b604"}}, {"head": {"id": "b7233276-1bae-4af8-9d4a-f20b635fbd7f", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029065741100, "endTime": 29029070179500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "0f1620e5-9c94-43ee-b711-d97669d230fc", "logId": "b02aa204-ac2f-440e-930f-49035734e1bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f1620e5-9c94-43ee-b711-d97669d230fc", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029058626800}, "additional": {"logType": "detail", "children": [], "durationId": "b7233276-1bae-4af8-9d4a-f20b635fbd7f"}}, {"head": {"id": "ebddd1a1-e69f-4c14-95c8-f35736a8ec95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029059963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71930796-0229-4578-a570-3f55015d925f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029060166900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0baab2cc-1d19-4f29-89ea-962d7c5597be", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029065765800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "931ca31a-8ffa-4f58-8353-536a4cb71279", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029066787600}, "additional": {"logType": "warn", "children": [], "durationId": "b7233276-1bae-4af8-9d4a-f20b635fbd7f"}}, {"head": {"id": "8a7ae9c4-aa42-4c68-8477-6a2434aa622b", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029068266800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f89b1d3-2f4b-4378-bf2e-3fb0e465ad50", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029068484400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a30fc9c-c608-432a-8ead-0e829e799771", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029068832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "146f9f3b-4535-472e-89da-63e8623b44bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029069015800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6919a75-0ce8-4d50-9c85-ca8c5fceb7e2", "name": "entry : default@SignHap cost memory 0.121185302734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029069669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d79e1d22-127d-4ea3-be00-f97cfba99dab", "name": "runTaskFromQueue task cost before running: 795 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029069858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b02aa204-ac2f-440e-930f-49035734e1bf", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029065741100, "endTime": 29029070179500, "totalTime": 4068200}, "additional": {"logType": "info", "children": [], "durationId": "b7233276-1bae-4af8-9d4a-f20b635fbd7f"}}, {"head": {"id": "dcdfd7da-6aec-40ad-9323-a085dafc870d", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029078112100, "endTime": 29029089617400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ab7690fa-487c-43c8-80d7-8c57b6915656", "logId": "41ad22a4-46e7-47e1-95b6-e9a7627ec962"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab7690fa-487c-43c8-80d7-8c57b6915656", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029074980200}, "additional": {"logType": "detail", "children": [], "durationId": "dcdfd7da-6aec-40ad-9323-a085dafc870d"}}, {"head": {"id": "1a91e48d-7aca-48ff-9775-f8cdea08c7ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029075716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6b088b-11b0-44dc-b65f-56c90fa98140", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029075860800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf66f99d-4b72-4157-a505-b85baa4047c6", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029078130800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1751816-7814-410c-b427-bc788d9dd6be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029089129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ce857e-2644-43ff-bc5d-597caac0a69b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029089260800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ec44dd-1e0d-425e-862e-f5c15e6adef0", "name": "entry : default@CollectDebugSymbol cost memory 0.24077606201171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029089416600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7193866b-4488-424a-9de7-64a376db22d9", "name": "runTaskFromQueue task cost before running: 815 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029089531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ad22a4-46e7-47e1-95b6-e9a7627ec962", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029078112100, "endTime": 29029089617400, "totalTime": 11396800}, "additional": {"logType": "info", "children": [], "durationId": "dcdfd7da-6aec-40ad-9323-a085dafc870d"}}, {"head": {"id": "6d328eb0-5d11-4ae7-975a-46ed26381d07", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029092120100, "endTime": 29029092448800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a2bd79ee-faf8-4e71-9266-fe3946a57b9b", "logId": "5150844d-e1e0-4a96-b193-5b62218162a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2bd79ee-faf8-4e71-9266-fe3946a57b9b", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029092068300}, "additional": {"logType": "detail", "children": [], "durationId": "6d328eb0-5d11-4ae7-975a-46ed26381d07"}}, {"head": {"id": "57c57a27-72d4-4126-93ec-7bca75ca76e3", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029092130100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe75cac-2f88-457c-8619-e82ea2be74cb", "name": "entry : assembleHap cost memory 0.0115814208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029092260900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c030e73d-240e-4763-9d5a-99ad7dc8bcd4", "name": "runTaskFromQueue task cost before running: 817 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029092380500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5150844d-e1e0-4a96-b193-5b62218162a2", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029092120100, "endTime": 29029092448800, "totalTime": 240800}, "additional": {"logType": "info", "children": [], "durationId": "6d328eb0-5d11-4ae7-975a-46ed26381d07"}}, {"head": {"id": "968b3ca4-9d84-4674-abf9-c1ddfa81c4cf", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029101739700, "endTime": 29029101761200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49224050-4a56-45ae-90b7-dc80eb578dab", "logId": "2431a80d-7461-421a-b723-7001de0d2dea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2431a80d-7461-421a-b723-7001de0d2dea", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029101739700, "endTime": 29029101761200}, "additional": {"logType": "info", "children": [], "durationId": "968b3ca4-9d84-4674-abf9-c1ddfa81c4cf"}}, {"head": {"id": "138e6a1e-e166-41db-8e3b-2c1a6c17319a", "name": "BUILD SUCCESSFUL in 827 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029101806200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "fda371c5-2a72-4030-8d66-71c67e294ec1", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29028275362400, "endTime": 29029102052300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 33}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "809730e4-86d7-4a01-ada8-651ce2a972f0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029102083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97630ef9-ad43-4fc5-bc22-46e8db2bbafc", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029102679300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a13686c0-1898-454c-97be-ad5110367e4f", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029102798200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59eb1c76-3d98-4574-b38a-f67986cba6d8", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029102903800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ed2844-a0be-4a6d-bf2b-58f2e7fbf8c0", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029102968700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46cf7b0-087a-4e8a-81cf-d8bf1b1e56a9", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029103342600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5ec253-9c2c-4ee5-88c2-9c3f1c6f7568", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029104602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e524953-6cf7-4215-93a8-73e0b39de4ea", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029105066400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c435e8f1-0c9a-4a6b-9411-9e3ca55c9cf3", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029105262700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bc71b0-2d3e-4f0d-9d23-b3775de5c75d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029105517500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8a6403-d390-448c-b96d-25aa45897999", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029106083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4317fb42-9e03-4aa8-8e57-bda342824260", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029106962800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "781a63fe-4be4-4ebe-8d02-35b9fa658c17", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029107248300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2443cef6-e9e8-47d3-a8b7-48cd4375c19b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029107353600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c774249a-de3f-4a96-8b7d-77e60148432d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029107477300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e483ffca-d7a4-4360-a98f-b0ece071d49b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029107573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3363eacb-1f90-4d5d-a5f4-ac3ee5981fc7", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029107714600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff6859eb-41e6-4adc-ba56-ce2b889c85fe", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029108169900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58eb5dc3-7447-4337-9172-54ca862c39f4", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029108600700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46afb02b-1e3b-4db3-b7e2-9ffbca1355ce", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029108922600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ab2721-0e37-4246-9281-5b4143bbc533", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029109227400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e6d250-c10f-4440-9c6f-bcd388f5c4eb", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029109384800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b48cc9f-b877-407b-b047-bcfe768e017b", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029109445700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7a881e-b799-41d6-8587-4eafa4bcba5e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029109492500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2eb17b6-272a-4914-afe0-baaef45ae59e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029110853800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe11fc4-9cbb-46ea-b2aa-f7fdf32f0d04", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029111993200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2455114e-e2c5-44c7-a432-4c0eb5ea5f5e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029115062300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b0efd50-44cb-4d8e-972c-a2a1daee02f4", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029115611800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c300ce2a-ab31-456a-b58f-dd7baf08ef31", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029116042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7431af1-435b-4854-8fec-d4a0809c7e0c", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029117107100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61f69e7-b630-40d0-a282-93c20ed1becf", "name": "Incremental task entry:default@BuildJS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029117670900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20e56dd-524c-406d-8425-83f71135c2f6", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029117766500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e410c4-82d8-45d9-89f9-22e718afa7e8", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029117890700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64dbb373-fd8c-4cc0-b11f-05b4c44c104c", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029117967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e19e21dd-1804-41bd-8461-68e0f4849e6f", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029118254000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b522b765-12df-4548-849c-33f83e0a90ea", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029118591600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b4698d8-f1f5-424a-a40b-a134e2b46f45", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029118928600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3801eb9c-25d2-4f5f-bbf5-e8aae00c7528", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029123512000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d675c8c8-9c35-4541-beb7-d1a9e9701a52", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029123840000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42edac12-1133-481d-9278-d07642c4fb7d", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029124170900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1667c38-f741-451e-b2fb-22545e9db615", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029124413900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}