{"routerMap": [{"name": "s<PERSON><PERSON><PERSON><PERSON>", "pageSourceFile": "src/main/ets/view/ShuduGame.ets", "buildFunction": "ShuduGameBuilder", "data": {"description": "this is <PERSON><PERSON><PERSON><PERSON>"}, "ohmurl": "@bundle:com.atomicservice.6917576098804821135/entry/ets/view/ShuduGame", "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry"}, {"name": "personalization", "pageSourceFile": "src/main/ets/view/Personalization/Personalization.ets", "buildFunction": "PersonalizationBuilder", "data": {"description": "this is personalization"}, "ohmurl": "@bundle:com.atomicservice.6917576098804821135/entry/ets/view/Personalization/Personalization", "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry"}, {"name": "gameRules", "pageSourceFile": "src/main/ets/view/Personalization/GameRules.ets", "buildFunction": "GameRulesBuilder", "data": {"description": "this is game rules"}, "ohmurl": "@bundle:com.atomicservice.6917576098804821135/entry/ets/view/Personalization/GameRules", "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry"}, {"name": "gameSettings", "pageSourceFile": "src/main/ets/view/Personalization/GameSettings.ets", "buildFunction": "GameSettingsBuilder", "data": {"description": "this is game settings"}, "ohmurl": "@bundle:com.atomicservice.6917576098804821135/entry/ets/view/Personalization/GameSettings", "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry"}]}