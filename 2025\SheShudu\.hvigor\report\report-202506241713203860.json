{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "eca9667a-b57c-47fe-9022-6d8899814065", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149211934800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e50c4ddb-dfb1-421d-be3e-e9ba5ea77e5a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149274691500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb98b6e8-8121-465f-8415-481aeeee76ba", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149275011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b02749d-292c-499f-9617-ce2037f9433d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432279402300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9038e08-0561-4766-b5a7-99d6548416c4", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432287654800, "endTime": 31432851996700}, "additional": {"children": ["dc5e0059-ec40-4aa9-b8f8-8b71ad1be6dc", "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "370ba1f1-0800-4ed9-93de-d7d0318bed43", "040aba5d-20ae-4683-856d-8c037a014a96", "98ed0827-a4d3-4ea6-b59d-f4c9a8c3a1e3", "c2d4ddf0-214d-4ccc-8281-c7490dc4d07f", "4233b4c1-b196-43d1-b0ed-f6a7e30fd295"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc5e0059-ec40-4aa9-b8f8-8b71ad1be6dc", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432287656700, "endTime": 31432304498900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "e300f732-6f66-4d58-a76a-60f6a2901cc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432304513200, "endTime": 31432849754200}, "additional": {"children": ["3269d526-f411-40bc-a59b-8366e9a9b0b9", "5e065186-4d0e-4545-87f7-0de6e44b7f57", "00139c1f-313c-4063-9788-fd47599fe160", "342638be-cd4a-48b8-a496-610396862932", "f0d4f58f-ba0b-40d4-92f4-96a217453a7e", "5b2abd5e-a2b6-4c7e-8797-e43c557dcf02", "0fcdc5b8-66ae-4356-9905-eeb98af9e36a", "55580a5d-3b71-42c1-8de5-b0bf62c02365", "48376373-a8be-4109-a859-07ac5fc3f284"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "370ba1f1-0800-4ed9-93de-d7d0318bed43", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432849783200, "endTime": 31432851982400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "83fb2fd4-a8fc-4171-b5a0-6ca764b059aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "040aba5d-20ae-4683-856d-8c037a014a96", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432851990300, "endTime": 31432851991400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "b4d9a654-6659-4e46-b7e5-e543060e2c7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98ed0827-a4d3-4ea6-b59d-f4c9a8c3a1e3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432291925500, "endTime": 31432291979100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "fe80f24d-bc87-441b-ae89-41159c7a51be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe80f24d-bc87-441b-ae89-41159c7a51be", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432291925500, "endTime": 31432291979100}, "additional": {"logType": "info", "children": [], "durationId": "98ed0827-a4d3-4ea6-b59d-f4c9a8c3a1e3", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "c2d4ddf0-214d-4ccc-8281-c7490dc4d07f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432299020800, "endTime": 31432299046200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "1a7e6779-3317-4e3c-a676-fb97b20ebc58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a7e6779-3317-4e3c-a676-fb97b20ebc58", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432299020800, "endTime": 31432299046200}, "additional": {"logType": "info", "children": [], "durationId": "c2d4ddf0-214d-4ccc-8281-c7490dc4d07f", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "e92af756-78db-4444-bbe7-1bf6098d05ff", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432299104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d6b3cc2-13a6-44c2-9723-f2b7b50315df", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432304386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e300f732-6f66-4d58-a76a-60f6a2901cc0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432287656700, "endTime": 31432304498900}, "additional": {"logType": "info", "children": [], "durationId": "dc5e0059-ec40-4aa9-b8f8-8b71ad1be6dc", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "3269d526-f411-40bc-a59b-8366e9a9b0b9", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432311980500, "endTime": 31432311988200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "552a5f2c-bfa3-4b1e-86a3-9f0f6cef7be3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e065186-4d0e-4545-87f7-0de6e44b7f57", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432312003200, "endTime": 31432317934800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "32c0c231-4c54-4da0-8551-21f6fc30421c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00139c1f-313c-4063-9788-fd47599fe160", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432317949900, "endTime": 31432618301200}, "additional": {"children": ["b928280f-d85e-4e90-8035-0802f894b134", "dff2d57f-e26a-4d01-a4e1-0cfaf13e2737", "cd341d5c-5dbd-48ba-8120-1c7116d8c959"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "4d2a5be5-d6d3-46a1-8253-3facfc822b19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "342638be-cd4a-48b8-a496-610396862932", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432618314700, "endTime": 31432657050500}, "additional": {"children": ["b346443a-0709-4d5f-ae76-d023b3230bcd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "4841d5eb-ac54-4ab7-9a89-cbea7a1056f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0d4f58f-ba0b-40d4-92f4-96a217453a7e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432657060800, "endTime": 31432805512300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "9db4e533-53c2-4b6c-976b-18b88dffb063"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b2abd5e-a2b6-4c7e-8797-e43c557dcf02", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432807464900, "endTime": 31432828804400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "9b91edd6-d35d-42cf-8b25-e0740ef282ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fcdc5b8-66ae-4356-9905-eeb98af9e36a", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432828827800, "endTime": 31432849295700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "63d8cffc-cc16-4a93-a0cb-bbe403a7ea8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55580a5d-3b71-42c1-8de5-b0bf62c02365", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432849324500, "endTime": 31432849644100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "ca080b47-517c-4972-95cc-77c59824bd59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "552a5f2c-bfa3-4b1e-86a3-9f0f6cef7be3", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432311980500, "endTime": 31432311988200}, "additional": {"logType": "info", "children": [], "durationId": "3269d526-f411-40bc-a59b-8366e9a9b0b9", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "32c0c231-4c54-4da0-8551-21f6fc30421c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432312003200, "endTime": 31432317934800}, "additional": {"logType": "info", "children": [], "durationId": "5e065186-4d0e-4545-87f7-0de6e44b7f57", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "b928280f-d85e-4e90-8035-0802f894b134", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432318615200, "endTime": 31432318634000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00139c1f-313c-4063-9788-fd47599fe160", "logId": "4b82f77e-1805-4e71-bb0a-c2e09c445f85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b82f77e-1805-4e71-bb0a-c2e09c445f85", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432318615200, "endTime": 31432318634000}, "additional": {"logType": "info", "children": [], "durationId": "b928280f-d85e-4e90-8035-0802f894b134", "parent": "4d2a5be5-d6d3-46a1-8253-3facfc822b19"}}, {"head": {"id": "dff2d57f-e26a-4d01-a4e1-0cfaf13e2737", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432320441700, "endTime": 31432617234500}, "additional": {"children": ["b603076d-6f03-4e5b-ba9a-893c94137fe3", "aac97611-cb1c-4f89-88f4-58e642a12e44"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00139c1f-313c-4063-9788-fd47599fe160", "logId": "b224c88b-93c1-41eb-971c-3e4e13bbe3ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b603076d-6f03-4e5b-ba9a-893c94137fe3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432320443500, "endTime": 31432335945000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dff2d57f-e26a-4d01-a4e1-0cfaf13e2737", "logId": "7f905ad3-4906-4157-ac92-db4a0454f862"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aac97611-cb1c-4f89-88f4-58e642a12e44", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432335967700, "endTime": 31432617220200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dff2d57f-e26a-4d01-a4e1-0cfaf13e2737", "logId": "7557bff1-1025-4faa-baa4-ff82aa26a712"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbb122ca-073b-4423-b6c3-752336ebfee4", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432320447700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0c785f-22d4-47b2-a038-8de887cd67da", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432335216000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f905ad3-4906-4157-ac92-db4a0454f862", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432320443500, "endTime": 31432335945000}, "additional": {"logType": "info", "children": [], "durationId": "b603076d-6f03-4e5b-ba9a-893c94137fe3", "parent": "b224c88b-93c1-41eb-971c-3e4e13bbe3ea"}}, {"head": {"id": "e1d0a9f7-e2e2-4629-a320-ea6904f75767", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432335987500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36e8c6b7-7ae7-4f13-bd95-7fa242f04d12", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432344105700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0e5c54-8685-4d42-b529-4d780520b7b8", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432344341900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb6119f-e17e-46ca-9a2f-35f5d3ad80d7", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432344498000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ebddd61-68e0-4dd7-acea-0b927d09839b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432344592200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82e003a-dab9-4b0e-a455-06535d342a46", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432348831900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316d9acf-e634-4d1e-bfc2-c40155628054", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432354069100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6401939b-35d8-445a-8002-f80197459bed", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432381294100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f774459b-0136-471b-897a-aff9f1c2e101", "name": "Sdk init in 184 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432538451600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b223b32-1487-4b4e-82fb-011fee233850", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432538750900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 13}, "markType": "other"}}, {"head": {"id": "1fd9d179-a1db-4df8-ad30-a2fa8cbc2788", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432538850600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 13}, "markType": "other"}}, {"head": {"id": "a032c6c9-3f2b-485e-9652-84d44d6a852b", "name": "Project task initialization takes 76 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432616552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "139bc807-2eb3-4153-b783-5136f5074b0e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432616860600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e95ab326-afa7-4cb4-9620-bb5ef455ac9f", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432617038600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24e75df-ff92-4f04-967a-dc4625560fab", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432617154800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7557bff1-1025-4faa-baa4-ff82aa26a712", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432335967700, "endTime": 31432617220200}, "additional": {"logType": "info", "children": [], "durationId": "aac97611-cb1c-4f89-88f4-58e642a12e44", "parent": "b224c88b-93c1-41eb-971c-3e4e13bbe3ea"}}, {"head": {"id": "b224c88b-93c1-41eb-971c-3e4e13bbe3ea", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432320441700, "endTime": 31432617234500}, "additional": {"logType": "info", "children": ["7f905ad3-4906-4157-ac92-db4a0454f862", "7557bff1-1025-4faa-baa4-ff82aa26a712"], "durationId": "dff2d57f-e26a-4d01-a4e1-0cfaf13e2737", "parent": "4d2a5be5-d6d3-46a1-8253-3facfc822b19"}}, {"head": {"id": "cd341d5c-5dbd-48ba-8120-1c7116d8c959", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432618269500, "endTime": 31432618287500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "00139c1f-313c-4063-9788-fd47599fe160", "logId": "9cc59010-bd54-4127-9ea2-5ee28f4e08b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cc59010-bd54-4127-9ea2-5ee28f4e08b0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432618269500, "endTime": 31432618287500}, "additional": {"logType": "info", "children": [], "durationId": "cd341d5c-5dbd-48ba-8120-1c7116d8c959", "parent": "4d2a5be5-d6d3-46a1-8253-3facfc822b19"}}, {"head": {"id": "4d2a5be5-d6d3-46a1-8253-3facfc822b19", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432317949900, "endTime": 31432618301200}, "additional": {"logType": "info", "children": ["4b82f77e-1805-4e71-bb0a-c2e09c445f85", "b224c88b-93c1-41eb-971c-3e4e13bbe3ea", "9cc59010-bd54-4127-9ea2-5ee28f4e08b0"], "durationId": "00139c1f-313c-4063-9788-fd47599fe160", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "b346443a-0709-4d5f-ae76-d023b3230bcd", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432619096900, "endTime": 31432657038800}, "additional": {"children": ["1722a599-e656-4d53-b55e-42c805bb45ec", "177aa0c8-3410-4fa6-a218-adfc15e57b9b", "543bb8dc-7f5d-4462-bf08-133d9e9f6840"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342638be-cd4a-48b8-a496-610396862932", "logId": "fb518d65-6bb5-4be1-a643-9f4e6067e331"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1722a599-e656-4d53-b55e-42c805bb45ec", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432624720500, "endTime": 31432624864600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b346443a-0709-4d5f-ae76-d023b3230bcd", "logId": "3d044cfe-74fe-41ce-b977-f809b48ececb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d044cfe-74fe-41ce-b977-f809b48ececb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432624720500, "endTime": 31432624864600}, "additional": {"logType": "info", "children": [], "durationId": "1722a599-e656-4d53-b55e-42c805bb45ec", "parent": "fb518d65-6bb5-4be1-a643-9f4e6067e331"}}, {"head": {"id": "177aa0c8-3410-4fa6-a218-adfc15e57b9b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432631354200, "endTime": 31432653908200}, "additional": {"children": ["be85a18f-60ef-4b9d-97fa-cfff165a07ba", "8f0b2de5-5a42-4c60-866c-0ff7582e27f0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b346443a-0709-4d5f-ae76-d023b3230bcd", "logId": "aacf979d-307a-4f66-9303-e149e8a52ac5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be85a18f-60ef-4b9d-97fa-cfff165a07ba", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432631357400, "endTime": 31432636955900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "177aa0c8-3410-4fa6-a218-adfc15e57b9b", "logId": "fd9b5410-4516-4c4c-839b-7c0999d08dbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f0b2de5-5a42-4c60-866c-0ff7582e27f0", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432636974500, "endTime": 31432653890400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "177aa0c8-3410-4fa6-a218-adfc15e57b9b", "logId": "38568fb3-5edf-4b52-bfda-2c75a297126e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6b9ed91-efeb-433b-8bcb-2841acd52445", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432631365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be48b864-f1a2-4466-a466-a1e749c6a2d1", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432636808600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd9b5410-4516-4c4c-839b-7c0999d08dbe", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432631357400, "endTime": 31432636955900}, "additional": {"logType": "info", "children": [], "durationId": "be85a18f-60ef-4b9d-97fa-cfff165a07ba", "parent": "aacf979d-307a-4f66-9303-e149e8a52ac5"}}, {"head": {"id": "8edc6055-9f0b-4a73-848f-bdc6c2c06bd4", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432636984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57de73d7-17ba-4fd4-9e9c-8df1313da0a5", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432645962600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa57b48c-4edc-434f-8cc6-524263c04216", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432647148100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e1fdf41-55f0-4b31-9c84-7753852ca179", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432647400900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ed7b8a-1d21-4abd-aa10-4e1d07734db7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432647612000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5928641-ee19-46d2-b8e4-28e0003fadd7", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432647705800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c7c2fc-f488-4c69-b7cd-5d321e16ba40", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432647782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f432f09-b496-4081-a48e-4712d64e0cfe", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432647853300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1139d802-c4c6-4bdc-afed-950cccea5137", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432653122700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "227e5adf-3811-4fe0-9923-7256158587fe", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432653430800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa02dda7-36e9-4261-bf0e-c34f8d900d4e", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432653533600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ee6602-fa96-4229-b7b4-9634c2d742d3", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432653598800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38568fb3-5edf-4b52-bfda-2c75a297126e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432636974500, "endTime": 31432653890400}, "additional": {"logType": "info", "children": [], "durationId": "8f0b2de5-5a42-4c60-866c-0ff7582e27f0", "parent": "aacf979d-307a-4f66-9303-e149e8a52ac5"}}, {"head": {"id": "aacf979d-307a-4f66-9303-e149e8a52ac5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432631354200, "endTime": 31432653908200}, "additional": {"logType": "info", "children": ["fd9b5410-4516-4c4c-839b-7c0999d08dbe", "38568fb3-5edf-4b52-bfda-2c75a297126e"], "durationId": "177aa0c8-3410-4fa6-a218-adfc15e57b9b", "parent": "fb518d65-6bb5-4be1-a643-9f4e6067e331"}}, {"head": {"id": "543bb8dc-7f5d-4462-bf08-133d9e9f6840", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432657003600, "endTime": 31432657023800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b346443a-0709-4d5f-ae76-d023b3230bcd", "logId": "b76546ae-0bc2-4a6a-8e1f-6d1a2e914f24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b76546ae-0bc2-4a6a-8e1f-6d1a2e914f24", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432657003600, "endTime": 31432657023800}, "additional": {"logType": "info", "children": [], "durationId": "543bb8dc-7f5d-4462-bf08-133d9e9f6840", "parent": "fb518d65-6bb5-4be1-a643-9f4e6067e331"}}, {"head": {"id": "fb518d65-6bb5-4be1-a643-9f4e6067e331", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432619096900, "endTime": 31432657038800}, "additional": {"logType": "info", "children": ["3d044cfe-74fe-41ce-b977-f809b48ececb", "aacf979d-307a-4f66-9303-e149e8a52ac5", "b76546ae-0bc2-4a6a-8e1f-6d1a2e914f24"], "durationId": "b346443a-0709-4d5f-ae76-d023b3230bcd", "parent": "4841d5eb-ac54-4ab7-9a89-cbea7a1056f5"}}, {"head": {"id": "4841d5eb-ac54-4ab7-9a89-cbea7a1056f5", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432618314700, "endTime": 31432657050500}, "additional": {"logType": "info", "children": ["fb518d65-6bb5-4be1-a643-9f4e6067e331"], "durationId": "342638be-cd4a-48b8-a496-610396862932", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "d7c23e8f-4b9d-4262-8989-07f3648260b1", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432694176400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17e43285-7981-4a26-b276-4f731663fa48", "name": "hvigorfile, resolve hvigorfile dependencies in 149 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432805152900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db4e533-53c2-4b6c-976b-18b88dffb063", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432657060800, "endTime": 31432805512300}, "additional": {"logType": "info", "children": [], "durationId": "f0d4f58f-ba0b-40d4-92f4-96a217453a7e", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "48376373-a8be-4109-a859-07ac5fc3f284", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432807080200, "endTime": 31432807436100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "logId": "fba0b95a-b20b-4bb6-9e12-2a56e3f1a1e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f500958-cd8d-461e-b276-dcde507c7cfe", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432807135400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba0b95a-b20b-4bb6-9e12-2a56e3f1a1e0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432807080200, "endTime": 31432807436100}, "additional": {"logType": "info", "children": [], "durationId": "48376373-a8be-4109-a859-07ac5fc3f284", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "c25060a5-07f5-4fd0-ace3-715620c2608d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432809904700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d839c5-0d36-4a2b-ae2b-6017eb7e4d8d", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432816626700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b91edd6-d35d-42cf-8b25-e0740ef282ee", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432807464900, "endTime": 31432828804400}, "additional": {"logType": "info", "children": [], "durationId": "5b2abd5e-a2b6-4c7e-8797-e43c557dcf02", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "b35fc92e-d8ac-4cb6-a197-941e2d27180d", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432836806600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ba65a33-b98f-4ab0-9ac3-4da1ac03438e", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432837363700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b266dca7-c22d-4a25-a8a0-e68dbc95c81e", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432843364300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28007d3f-ed84-41fc-a1de-88fdef9e7e9b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432843573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d8cffc-cc16-4a93-a0cb-bbe403a7ea8c", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432828827800, "endTime": 31432849295700}, "additional": {"logType": "info", "children": [], "durationId": "0fcdc5b8-66ae-4356-9905-eeb98af9e36a", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "a233c3f6-d511-43a0-8b8c-4aeb3da42e78", "name": "Configuration phase cost:538 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432849352500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca080b47-517c-4972-95cc-77c59824bd59", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432849324500, "endTime": 31432849644100}, "additional": {"logType": "info", "children": [], "durationId": "55580a5d-3b71-42c1-8de5-b0bf62c02365", "parent": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff"}}, {"head": {"id": "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432304513200, "endTime": 31432849754200}, "additional": {"logType": "info", "children": ["552a5f2c-bfa3-4b1e-86a3-9f0f6cef7be3", "32c0c231-4c54-4da0-8551-21f6fc30421c", "4d2a5be5-d6d3-46a1-8253-3facfc822b19", "4841d5eb-ac54-4ab7-9a89-cbea7a1056f5", "9db4e533-53c2-4b6c-976b-18b88dffb063", "9b91edd6-d35d-42cf-8b25-e0740ef282ee", "63d8cffc-cc16-4a93-a0cb-bbe403a7ea8c", "ca080b47-517c-4972-95cc-77c59824bd59", "fba0b95a-b20b-4bb6-9e12-2a56e3f1a1e0"], "durationId": "fce69be3-2a72-42c0-a9a2-2f16d0b4c99e", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "4233b4c1-b196-43d1-b0ed-f6a7e30fd295", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432851909600, "endTime": 31432851934400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9038e08-0561-4766-b5a7-99d6548416c4", "logId": "b09ae720-d605-44a8-8883-a46e1e295809"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b09ae720-d605-44a8-8883-a46e1e295809", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432851909600, "endTime": 31432851934400}, "additional": {"logType": "info", "children": [], "durationId": "4233b4c1-b196-43d1-b0ed-f6a7e30fd295", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "83fb2fd4-a8fc-4171-b5a0-6ca764b059aa", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432849783200, "endTime": 31432851982400}, "additional": {"logType": "info", "children": [], "durationId": "370ba1f1-0800-4ed9-93de-d7d0318bed43", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "b4d9a654-6659-4e46-b7e5-e543060e2c7e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432851990300, "endTime": 31432851991400}, "additional": {"logType": "info", "children": [], "durationId": "040aba5d-20ae-4683-856d-8c037a014a96", "parent": "01e25fe7-68ec-40a7-bb19-e946731aadf6"}}, {"head": {"id": "01e25fe7-68ec-40a7-bb19-e946731aadf6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432287654800, "endTime": 31432851996700}, "additional": {"logType": "info", "children": ["e300f732-6f66-4d58-a76a-60f6a2901cc0", "f76c6cb9-9bc0-4ac1-90be-78b14f3103ff", "83fb2fd4-a8fc-4171-b5a0-6ca764b059aa", "b4d9a654-6659-4e46-b7e5-e543060e2c7e", "fe80f24d-bc87-441b-ae89-41159c7a51be", "1a7e6779-3317-4e3c-a676-fb97b20ebc58", "b09ae720-d605-44a8-8883-a46e1e295809"], "durationId": "b9038e08-0561-4766-b5a7-99d6548416c4"}}, {"head": {"id": "7761494b-3bcc-4fb4-bf8d-f424d515cdab", "name": "Configuration task cost before running: 570 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432852200900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59dfab31-d70e-4b77-85b5-49e96ebfb23c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432865483700, "endTime": 31432898890000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ab8962fc-120b-41bf-8315-e405e85c36ab", "logId": "6a751022-a448-496b-a318-51764c0ad7a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab8962fc-120b-41bf-8315-e405e85c36ab", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432857505800}, "additional": {"logType": "detail", "children": [], "durationId": "59dfab31-d70e-4b77-85b5-49e96ebfb23c"}}, {"head": {"id": "c77ab3d0-bc3b-4aca-8648-2c613fbb132e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432859952600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dcf6dd9-47ab-4e8b-a9ea-581a73c39b7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432860414200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a2b739-f552-4df9-8b86-692d888b80a3", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432865498300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b28f1461-3f71-46a5-9b25-29c6d7022182", "name": "Incremental task entry:default@PreBuild pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432898647200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49677ea6-ec0b-4ca9-8cee-0b867ad3969e", "name": "entry : default@PreBuild cost memory 0.315460205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432898816400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a751022-a448-496b-a318-51764c0ad7a2", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432865483700, "endTime": 31432898890000}, "additional": {"logType": "info", "children": [], "durationId": "59dfab31-d70e-4b77-85b5-49e96ebfb23c"}}, {"head": {"id": "b9e3a7db-b525-4d80-985b-87dea9656621", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432908903200, "endTime": 31432912079500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d2be1f09-cbd3-4a6b-b7d1-7f986195a135", "logId": "1ca4a1eb-67df-4610-b1a4-d5a3bb168b55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2be1f09-cbd3-4a6b-b7d1-7f986195a135", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432906324700}, "additional": {"logType": "detail", "children": [], "durationId": "b9e3a7db-b525-4d80-985b-87dea9656621"}}, {"head": {"id": "8175aba9-4985-4426-a261-f989a9ac8c90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432907362300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a80d3b-6d8e-4ae2-b38e-6e24ba9b307f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432907504400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c03ae90c-2c89-4104-a98e-0a91e9991d33", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432908917800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659ae1ec-a634-4cc6-9782-5f16ffafebd0", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432910478200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d8d3c50-988d-4246-af32-61558e206977", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432911847900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0962706-d8fa-4a6f-bbe1-b927149dee50", "name": "entry : default@GenerateMetadata cost memory 0.09615325927734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432912003200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca4a1eb-67df-4610-b1a4-d5a3bb168b55", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432908903200, "endTime": 31432912079500}, "additional": {"logType": "info", "children": [], "durationId": "b9e3a7db-b525-4d80-985b-87dea9656621"}}, {"head": {"id": "1eb581e8-40a5-4018-b082-9f798a59907a", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432915805400, "endTime": 31432916448200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e5d4a348-9d63-45e4-93cd-4b9fd4a46207", "logId": "832981b6-5af6-4663-954e-ecd89eb917e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5d4a348-9d63-45e4-93cd-4b9fd4a46207", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432914899900}, "additional": {"logType": "detail", "children": [], "durationId": "1eb581e8-40a5-4018-b082-9f798a59907a"}}, {"head": {"id": "2a9b9a55-616b-40e4-9e5d-e1fde2550231", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432915520800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411531ce-10df-4ff0-bc79-ba1052f76fe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432915644900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0241ca9d-a318-4c6f-b2a1-c092d8f25081", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432915812300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63146789-041f-4358-a070-0d7f244f53d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432915937600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e332c2c-bbd5-42ed-a9b2-8656e5d4a876", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432916018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82fc19a-a3e8-4b56-ac57-3323681102e1", "name": "entry : default@ConfigureCmake cost memory 0.036041259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432916101400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52d14f92-58a5-4723-91e8-3e40dfb481e8", "name": "runTaskFromQueue task cost before running: 634 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432916377300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "832981b6-5af6-4663-954e-ecd89eb917e6", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432915805400, "endTime": 31432916448200, "totalTime": 551700}, "additional": {"logType": "info", "children": [], "durationId": "1eb581e8-40a5-4018-b082-9f798a59907a"}}, {"head": {"id": "6ac86842-9dba-43c6-8459-9a57dcf1d538", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432924040400, "endTime": 31432927268000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "ea2f514e-3da4-4959-bf6d-638e3eb32557", "logId": "85762f13-790c-4eb2-96f1-da7528fa4ba7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea2f514e-3da4-4959-bf6d-638e3eb32557", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432921332300}, "additional": {"logType": "detail", "children": [], "durationId": "6ac86842-9dba-43c6-8459-9a57dcf1d538"}}, {"head": {"id": "344c2968-eabf-4bcc-985b-d753d0e7e268", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432923042900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9353d2fb-2f45-40fa-925e-67fb56ab5f2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432923192900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5da819-1077-4dbf-b3c4-357f6dca48e1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432924052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "805bb9d3-0c6f-4851-8998-eccc94f65e4c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432926959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade21cc5-ec14-4036-bb65-c494eef379bc", "name": "entry : default@MergeProfile cost memory 0.1064605712890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432927128200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85762f13-790c-4eb2-96f1-da7528fa4ba7", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432924040400, "endTime": 31432927268000}, "additional": {"logType": "info", "children": [], "durationId": "6ac86842-9dba-43c6-8459-9a57dcf1d538"}}, {"head": {"id": "7c8bbca4-75bf-41fa-b1d1-71adfbe13f1c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432932458500, "endTime": 31432938394200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8815044b-af25-4780-a85b-408fd8e2e557", "logId": "102feda7-d27a-4761-92bd-c517b286ed9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8815044b-af25-4780-a85b-408fd8e2e557", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432929838100}, "additional": {"logType": "detail", "children": [], "durationId": "7c8bbca4-75bf-41fa-b1d1-71adfbe13f1c"}}, {"head": {"id": "5eeff44d-bc3a-4881-b3e3-c6562c0f2421", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432930470800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44cfbb92-55d0-4091-9fc0-6f76fd815ecc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432930591200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26286999-df2f-4fbd-85fd-c3d7687079cd", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432932475000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca97cce1-a431-4985-b7ff-ed94f5a64639", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432935294200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11ea526-109d-4100-9e62-4cc5c7a5d0e5", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432938147700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eb13f5c-a879-4bee-956f-b6023b68fbab", "name": "entry : default@CreateBuildProfile cost memory 0.1052093505859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432938315600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "102feda7-d27a-4761-92bd-c517b286ed9b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432932458500, "endTime": 31432938394200}, "additional": {"logType": "info", "children": [], "durationId": "7c8bbca4-75bf-41fa-b1d1-71adfbe13f1c"}}, {"head": {"id": "81b616ea-bd05-4d8f-9931-08ddd2ef74cf", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432949968700, "endTime": 31432951667700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f17308fd-8306-438c-a314-780882e62228", "logId": "698f1ae8-1979-48fd-80af-bde53a8f0bf2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f17308fd-8306-438c-a314-780882e62228", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432942786000}, "additional": {"logType": "detail", "children": [], "durationId": "81b616ea-bd05-4d8f-9931-08ddd2ef74cf"}}, {"head": {"id": "a91fcec5-7633-41c3-a847-a36bab69c918", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432944561600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caea90f2-d06f-4a24-9063-a05a0e168c7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432945415200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42a7eb37-847c-4d6d-881d-52c98fb9afeb", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432949986200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb39378-63b9-4baa-833e-cc0879f58d27", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432950316400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a27c26b-122b-43ef-b382-2481d9e65efc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432950407900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9aa9ebe-8dba-4a9d-a099-ddf725de34a7", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432950502300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae24ac1-41c7-403a-af67-dcda89141998", "name": "runTaskFromQueue task cost before running: 668 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432950583900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "698f1ae8-1979-48fd-80af-bde53a8f0bf2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432949968700, "endTime": 31432951667700, "totalTime": 596500}, "additional": {"logType": "info", "children": [], "durationId": "81b616ea-bd05-4d8f-9931-08ddd2ef74cf"}}, {"head": {"id": "77f1bc13-85fe-4430-a8a9-3a01dd344be8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432972160100, "endTime": 31432981326800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "48772d96-d38b-4972-bdf0-665a30f45f98", "logId": "c24eca7d-fa63-4c52-bc5a-4ffd8676a6f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48772d96-d38b-4972-bdf0-665a30f45f98", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432956572500}, "additional": {"logType": "detail", "children": [], "durationId": "77f1bc13-85fe-4430-a8a9-3a01dd344be8"}}, {"head": {"id": "c89a80bf-2865-42dd-bfae-7b97cbe1b2b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432960171400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e4230d9-c80b-4f3c-8280-adc304ea1e82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432960669600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e766a70-4768-45cf-af05-dc416d05f89e", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432972176800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71abb7d8-104e-433d-a48a-76b6d5a2ad83", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432973430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884bba3c-f2e2-4eab-b28f-8b9238fe3e70", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03856658935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432974879800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2695ffa0-20ea-4a5a-9472-7d615598c2ad", "name": "runTaskFromQueue task cost before running: 693 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432975361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24eca7d-fa63-4c52-bc5a-4ffd8676a6f0", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432972160100, "endTime": 31432981326800, "totalTime": 3126000}, "additional": {"logType": "info", "children": [], "durationId": "77f1bc13-85fe-4430-a8a9-3a01dd344be8"}}, {"head": {"id": "17e57587-9152-415e-bbb0-bbaea05edbb4", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432993373500, "endTime": 31433000197500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "79cd465a-92df-4ce6-b4f8-e2eec2d7f5d9", "logId": "bd764248-d3b0-4a93-a83c-c02292571987"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79cd465a-92df-4ce6-b4f8-e2eec2d7f5d9", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432986372500}, "additional": {"logType": "detail", "children": [], "durationId": "17e57587-9152-415e-bbb0-bbaea05edbb4"}}, {"head": {"id": "c54a161f-59f0-4c16-b469-d1d364dd28f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432987477800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22787637-1c32-4b4b-a565-85833e0c24cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432987792700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d25f1ce5-433e-4f8f-a4f0-385c382f4ad1", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432993409600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd78689-ae6b-46da-8b92-a346bed78bff", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432998908400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdf7547-f7c0-46cb-be4d-90936a215477", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432999281300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763c7a2f-d5cd-4540-89ef-d4d1533b423a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432999392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1be0b6-ad24-4baf-8ed1-1352d4759256", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432999448400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48439045-9b8e-425b-8462-2cd3251bd909", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11814117431640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432999530200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f4bf8d-4ebb-40f4-942f-67baa5a3d331", "name": "runTaskFromQueue task cost before running: 717 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432999677300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd764248-d3b0-4a93-a83c-c02292571987", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432993373500, "endTime": 31433000197500, "totalTime": 6224800}, "additional": {"logType": "info", "children": [], "durationId": "17e57587-9152-415e-bbb0-bbaea05edbb4"}}, {"head": {"id": "5b6f338f-f4f7-4527-bd2d-e2fea5e1261a", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433012341100, "endTime": 31433014952200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4c858dcd-02e3-4103-855e-3f6f2f381ec5", "logId": "592067a8-c1ce-4f63-9e31-1480808c6b9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c858dcd-02e3-4103-855e-3f6f2f381ec5", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433007469300}, "additional": {"logType": "detail", "children": [], "durationId": "5b6f338f-f4f7-4527-bd2d-e2fea5e1261a"}}, {"head": {"id": "8ce0465c-63aa-4dcc-8300-7a9cea1ab6c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433009459900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89c94520-24a7-4240-b1b2-dc6fdd45d65f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433009585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b61314f-55d3-49b6-9e93-62a547e372fb", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433012356100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b2ebf24-f015-432f-a2a1-9638b9958ae0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433013486700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058a8634-e2de-4054-9b34-db0c59743b97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433014017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd1bf5b-642a-453e-8a32-dfdb2ceb8ab9", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433014291600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f3a4da-1ae0-418d-92da-0ae951d5facb", "name": "runTaskFromQueue task cost before running: 732 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433014548100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592067a8-c1ce-4f63-9e31-1480808c6b9e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433012341100, "endTime": 31433014952200, "totalTime": 2154600}, "additional": {"logType": "info", "children": [], "durationId": "5b6f338f-f4f7-4527-bd2d-e2fea5e1261a"}}, {"head": {"id": "09e02215-2178-4cfb-96d4-665b44247592", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433020736900, "endTime": 31433025968400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "69d1bf1f-91af-415b-916c-303d10abef6e", "logId": "5b45fe4f-f49c-4b65-887d-9bae83745ee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69d1bf1f-91af-415b-916c-303d10abef6e", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433018522400}, "additional": {"logType": "detail", "children": [], "durationId": "09e02215-2178-4cfb-96d4-665b44247592"}}, {"head": {"id": "bbc613ef-eb25-4605-94bd-212426865dab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433019470500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74eaf2c8-5a0e-47d0-95b4-3e795c7d0dce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433019599900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7dc2e46-07c5-48c4-a06d-7b2e58a3cc60", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433020748100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "022da466-445b-4655-aaa4-b9aff521028f", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433025709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99dfb8e1-b1e1-44c8-b2cb-97494ab70c1c", "name": "entry : default@MakePackInfo cost memory 0.1394805908203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433025862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b45fe4f-f49c-4b65-887d-9bae83745ee9", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433020736900, "endTime": 31433025968400}, "additional": {"logType": "info", "children": [], "durationId": "09e02215-2178-4cfb-96d4-665b44247592"}}, {"head": {"id": "5fd429f8-387b-476a-80ec-f898dc2dc61f", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433035791600, "endTime": 31433049871100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "65ae78e6-c67a-4fae-a786-6c75905541d5", "logId": "48a0b7a1-6da7-4dd6-bbc5-37446303ad22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65ae78e6-c67a-4fae-a786-6c75905541d5", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433030216800}, "additional": {"logType": "detail", "children": [], "durationId": "5fd429f8-387b-476a-80ec-f898dc2dc61f"}}, {"head": {"id": "6e279795-ea63-4062-b7c5-36f57154fc28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433031047300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cf1981b-718f-4ac6-b0c5-71f85369d71f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433031561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d505e95-d53f-4442-be9c-eca74c107c9b", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433035805700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a4b288-de8d-4ff4-b2d3-ff9993adc806", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433036124800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f212248c-82a3-40ea-9834-d0ba66636ced", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433040528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eb2dd6b-6170-4c6d-b40a-f271f6f7ff4a", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433046702100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc76d45-60dd-4062-ac82-0ef7ea7973a1", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433047174600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50455d5-cbe8-4d50-9fc9-e8186cdfb741", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433047658700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4bc54dc-0f5f-498a-9590-36199adedb89", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433047880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adebe740-e5b7-479f-9da2-ef5cd2659301", "name": "entry : default@SyscapTransform cost memory 0.15402984619140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433048446800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1870480d-b633-45d3-b0e1-08b6a5e20141", "name": "runTaskFromQueue task cost before running: 766 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433048873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48a0b7a1-6da7-4dd6-bbc5-37446303ad22", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433035791600, "endTime": 31433049871100, "totalTime": 13042100}, "additional": {"logType": "info", "children": [], "durationId": "5fd429f8-387b-476a-80ec-f898dc2dc61f"}}, {"head": {"id": "dbb227f2-eaa0-4501-b52d-ea2f879b499a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433064314200, "endTime": 31433069104300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0d58a6b0-3d84-4a99-ad9c-df074d13f43a", "logId": "c9a4281e-2961-4998-91ab-9779cb7c1cd7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d58a6b0-3d84-4a99-ad9c-df074d13f43a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433057478200}, "additional": {"logType": "detail", "children": [], "durationId": "dbb227f2-eaa0-4501-b52d-ea2f879b499a"}}, {"head": {"id": "5837545b-24f2-479f-871f-a8728789fa8a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433060431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b0f31f-21bd-47c2-8587-eb0a7bc1b6ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433060791000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb791b9c-7ffb-48c2-84c6-bf8ac5cc9cb7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433064329000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917ed7c9-a81a-4a29-afb6-b90285c0b9d7", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433068485200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff8f9b1-d847-43fa-8e42-c4a9d890f5a5", "name": "entry : default@ProcessProfile cost memory 0.0607147216796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433068985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9a4281e-2961-4998-91ab-9779cb7c1cd7", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433064314200, "endTime": 31433069104300}, "additional": {"logType": "info", "children": [], "durationId": "dbb227f2-eaa0-4501-b52d-ea2f879b499a"}}, {"head": {"id": "12a509f5-c88c-4ab6-9ca5-850537b8ed85", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433088477600, "endTime": 31433102406200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aeefbaa2-b24d-4a2e-a5eb-0ca056fd1a90", "logId": "e4616b37-8ca1-46fe-a58a-7aa601840192"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aeefbaa2-b24d-4a2e-a5eb-0ca056fd1a90", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433083891600}, "additional": {"logType": "detail", "children": [], "durationId": "12a509f5-c88c-4ab6-9ca5-850537b8ed85"}}, {"head": {"id": "4405571d-8f3b-45fd-b748-18fca1a87c97", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433084477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9a83f13-568d-42d5-bf0c-294110d454e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433084778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf7f1566-7d1c-49f2-8d45-2f9cc034fbca", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433088490900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d32357e8-20ca-49d0-85b0-094ef8a717a8", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433101724700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d8cb5c-90ef-40ed-8c44-66aedcfaf486", "name": "entry : default@ProcessRouterMap cost memory 0.20296478271484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433102267600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4616b37-8ca1-46fe-a58a-7aa601840192", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433088477600, "endTime": 31433102406200}, "additional": {"logType": "info", "children": [], "durationId": "12a509f5-c88c-4ab6-9ca5-850537b8ed85"}}, {"head": {"id": "7005093f-b4c7-4ad1-a0a9-b3a2f49a1e0f", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433114590600, "endTime": 31433117855700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1f9b7b2e-a542-4c14-b192-72ce1727babe", "logId": "77f45822-e63b-4fb9-96aa-c0e57be6cf25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f9b7b2e-a542-4c14-b192-72ce1727babe", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433111081600}, "additional": {"logType": "detail", "children": [], "durationId": "7005093f-b4c7-4ad1-a0a9-b3a2f49a1e0f"}}, {"head": {"id": "5aa82fa1-95a6-4745-8db3-48354bed0d67", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433112447400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f6923e1-a99b-4808-ad6f-3e94a90cea8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433112826100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd7c61c-d5b0-4f68-9cb8-156ce0a800a5", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433114603700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e090f3b-408c-477e-8a3c-3843961ddafc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433115439300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc23cc4f-2114-4f92-90b9-4d238a90fe97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433115711800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045ad424-7b8a-4bfb-a70b-3e4d81d13741", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433117217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b18db4-7802-46ac-9c94-1145c23946bb", "name": "runTaskFromQueue task cost before running: 835 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433117476800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f45822-e63b-4fb9-96aa-c0e57be6cf25", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433114590600, "endTime": 31433117855700, "totalTime": 2783200}, "additional": {"logType": "info", "children": [], "durationId": "7005093f-b4c7-4ad1-a0a9-b3a2f49a1e0f"}}, {"head": {"id": "0346bfaa-3722-4722-b7ae-043f59794124", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433127479000, "endTime": 31433135780400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b672d043-499c-4175-aebe-7940d0e73edc", "logId": "b5317e5c-3981-4f1e-bda5-716e79dcdaaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b672d043-499c-4175-aebe-7940d0e73edc", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433122994200}, "additional": {"logType": "detail", "children": [], "durationId": "0346bfaa-3722-4722-b7ae-043f59794124"}}, {"head": {"id": "07eef2d9-b219-4368-847d-15806645ea7e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433123539400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2852463-966a-4093-a23c-1c442063196e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433123731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "088385a9-33d6-4298-9332-42b3473254eb", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433125089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7467d8ee-ac7c-4974-9bbc-a4ca73d906da", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433129539000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9562e2-2711-46e5-b139-be94380644f4", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433132379900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1601847-e403-4933-a145-6d2bd86b8107", "name": "entry : default@ProcessResource cost memory 0.16979217529296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433132553300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5317e5c-3981-4f1e-bda5-716e79dcdaaf", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433127479000, "endTime": 31433135780400}, "additional": {"logType": "info", "children": [], "durationId": "0346bfaa-3722-4722-b7ae-043f59794124"}}, {"head": {"id": "11650043-1d49-400e-80af-4be9f6b83c56", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433150282900, "endTime": 31433170397700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0414f05b-7448-4842-ae09-ffcf1019e07d", "logId": "1f5fe0dc-3969-49a0-a80f-87ccb0cca3b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0414f05b-7448-4842-ae09-ffcf1019e07d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433144597000}, "additional": {"logType": "detail", "children": [], "durationId": "11650043-1d49-400e-80af-4be9f6b83c56"}}, {"head": {"id": "d1d2b63f-e423-44ed-abfc-f3869c260fbe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433145776200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df76faa3-d9a1-4183-aebc-312b9374847c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433145967000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "125357c3-ab84-4a4b-b610-13a129a2221e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433150295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f0a559-0d8f-453f-aa03-ca939c0fee2d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433170092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f88a50-62e8-42f2-94a5-8f4449cfcded", "name": "entry : default@GenerateLoaderJson cost memory 0.7668914794921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433170317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f5fe0dc-3969-49a0-a80f-87ccb0cca3b3", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433150282900, "endTime": 31433170397700}, "additional": {"logType": "info", "children": [], "durationId": "11650043-1d49-400e-80af-4be9f6b83c56"}}, {"head": {"id": "fd5c3c8d-f155-4bd8-8f89-58e794c06c78", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433180858900, "endTime": 31433185021200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "79eefeac-c726-46c7-9f11-5e5c60759e56", "logId": "96f7dd61-7c0c-406f-8d68-a974e5fc98c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79eefeac-c726-46c7-9f11-5e5c60759e56", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433178373700}, "additional": {"logType": "detail", "children": [], "durationId": "fd5c3c8d-f155-4bd8-8f89-58e794c06c78"}}, {"head": {"id": "50f18a0d-f2d6-4fb4-a986-311e45daff2d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433179090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d987dae6-aa96-4051-92a4-5c2299df11dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433179481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00371079-cd9b-4777-98e3-cdc9f0c1e9e5", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433180874400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dbe316d-0af4-42a6-b754-514e6e0c9ecd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433183656800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d50d8a-e28c-4096-bab8-02c1ee1fc944", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433183788000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ac12f9-e381-4a71-b7b8-c52e36a24629", "name": "entry : default@ProcessLibs cost memory 0.1262359619140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433184734900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a91239-0770-4c21-b748-dfb5913fe05c", "name": "runTaskFromQueue task cost before running: 902 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433184947900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96f7dd61-7c0c-406f-8d68-a974e5fc98c5", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433180858900, "endTime": 31433185021200, "totalTime": 4055300}, "additional": {"logType": "info", "children": [], "durationId": "fd5c3c8d-f155-4bd8-8f89-58e794c06c78"}}, {"head": {"id": "f49bf15b-813b-4bbb-9d48-4bc74d529ac7", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433195461800, "endTime": 31433261768900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ffc9042e-e95f-4955-932b-7086d8e4fc75", "logId": "36d2f405-b146-4093-b616-e0909ec813da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffc9042e-e95f-4955-932b-7086d8e4fc75", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433188289500}, "additional": {"logType": "detail", "children": [], "durationId": "f49bf15b-813b-4bbb-9d48-4bc74d529ac7"}}, {"head": {"id": "78f58801-83e1-4647-9c24-666856d4e1d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433188692200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8494f36d-7484-4f51-86c6-b2f71098e8e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433189074300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e13a7c66-dd2d-48c0-aa27-b15440ba278a", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433190398000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "046a386d-d6b2-4e76-a11c-ae244c44c493", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433195491200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e905e7d3-096b-4e08-ad4e-271e57a06354", "name": "Incremental task entry:default@CompileResource pre-execution cost: 65 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433261504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36aae81d-060b-4ca0-926c-1879acef9edf", "name": "entry : default@CompileResource cost memory -4.3591156005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433261657000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d2f405-b146-4093-b616-e0909ec813da", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433195461800, "endTime": 31433261768900}, "additional": {"logType": "info", "children": [], "durationId": "f49bf15b-813b-4bbb-9d48-4bc74d529ac7"}}, {"head": {"id": "957f521a-cf41-4792-bcb3-d74374cc841c", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433268986300, "endTime": 31433270947100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d90b12ee-05ab-4171-9ea0-a7653da878c6", "logId": "2b286c89-38ef-43d8-a690-c184f1ec9e20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d90b12ee-05ab-4171-9ea0-a7653da878c6", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433264869700}, "additional": {"logType": "detail", "children": [], "durationId": "957f521a-cf41-4792-bcb3-d74374cc841c"}}, {"head": {"id": "d85255df-ba54-424a-8ce9-e906d8d77b68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433265413300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4753ba-335a-413b-971d-c166b298cac1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433265553300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e5342d-44ad-4739-9295-1b591ee15d81", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433269003500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cba9fd1-7312-435e-931f-9820c75063be", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433269414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3179763-cac5-4e87-85f3-6cd8bc9c7fbb", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433270746900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64eee1f5-8dae-4c92-8de2-6b9945a9c000", "name": "entry : default@DoNativeStrip cost memory 0.0769500732421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433270878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b286c89-38ef-43d8-a690-c184f1ec9e20", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433268986300, "endTime": 31433270947100}, "additional": {"logType": "info", "children": [], "durationId": "957f521a-cf41-4792-bcb3-d74374cc841c"}}, {"head": {"id": "d4651c10-fcef-4000-97c1-8adf9c823152", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433280219400, "endTime": 31436667417100}, "additional": {"children": ["380b235b-b1ce-41f5-afbd-e4858487623d", "ddc6f0e3-8a97-4b4f-9c4e-cd996042e8a6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "cfaf75fa-25f6-46ee-9e39-ca95d8b0b3e7", "logId": "9ec83abd-be57-46e5-a387-f9c58f716895"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfaf75fa-25f6-46ee-9e39-ca95d8b0b3e7", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433275815000}, "additional": {"logType": "detail", "children": [], "durationId": "d4651c10-fcef-4000-97c1-8adf9c823152"}}, {"head": {"id": "06e5445b-df10-490e-8fef-aec93514bce1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433276285900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b333a884-635e-4f36-8dff-38bff3abbebc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433276391500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc20a802-d836-483a-a0fb-6e12ff019225", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433280230800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "445e6559-5901-49a1-a79b-3ed5ba1ac11c", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433294399900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15b24f34-de6d-4e74-816c-082f78f66c5e", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433294541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca200975-af27-42fc-8439-b1879196c7ff", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433325538600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b73fb5-3560-41df-b986-ec520564b8f4", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433326358500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0834cedb-9879-4097-b6f9-208eeccf511e", "name": "default@CompileArkTS work[63] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433344694200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "380b235b-b1ce-41f5-afbd-e4858487623d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31433584048500, "endTime": 31436644936800}, "additional": {"children": ["d2aa6822-1eed-426d-b760-5fef15197e1a", "9a16c151-8a1c-4e34-af29-d5bb667abec6", "80e299e1-99aa-4b5f-857f-51197370525e", "8995be96-794f-4f21-9e91-2d036c60d2cc", "1112860f-9f15-4945-9c6d-2e26e69cbb4f", "73668f4d-7067-4f88-b54a-ed970b8b4367"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d4651c10-fcef-4000-97c1-8adf9c823152", "logId": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09dbf98f-0242-455d-a574-ec7a40c39797", "name": "default@CompileArkTS work[63] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433348742600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aecb8ce-03ea-44a8-80b2-24d7f96c1053", "name": "default@CompileArkTS work[63] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433349249900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ab5e39-7c5b-4323-ac0e-ce55c6bd1210", "name": "CopyResources startTime: 31433349803800", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433349809200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47186177-eee2-4b8d-aa2e-82f8fada1d8a", "name": "default@CompileArkTS work[64] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433350371200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc6f0e3-8a97-4b4f-9c4e-cd996042e8a6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31436101866800, "endTime": 31436160673600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d4651c10-fcef-4000-97c1-8adf9c823152", "logId": "11027644-8ffd-4af7-a3d8-bca24c772fab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cec940a6-a3f2-41af-97f5-d145a243860c", "name": "default@CompileArkTS work[64] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433351938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e61d824c-a4fe-4473-b473-bb82166bd7e6", "name": "default@CompileArkTS work[64] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433352057500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c27dc78f-0780-4a40-9666-7ce66616d852", "name": "entry : default@CompileArkTS cost memory 1.5964813232421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433352459200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa6b7e51-7915-428f-9720-3f3b8b4f8051", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433360566500, "endTime": 31433363029000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "b4a91fd7-5fb3-43a3-902a-a01541bd3c4d", "logId": "f062b08d-448a-422e-b88f-df7c47cd1130"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4a91fd7-5fb3-43a3-902a-a01541bd3c4d", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433354502900}, "additional": {"logType": "detail", "children": [], "durationId": "fa6b7e51-7915-428f-9720-3f3b8b4f8051"}}, {"head": {"id": "b093660f-7f9c-4d14-b990-c1a3935adc8b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433355405700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287d941c-c347-4daa-b6d1-605dc7c5af3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433355531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29cdef5b-abe2-4b92-977d-06da5b13d7df", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433360578200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2226523-19aa-4be9-8642-c877e6bfdfe6", "name": "entry : default@BuildJS cost memory 0.12808990478515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433362847100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757786ae-2cc9-475d-8dc1-47cd200cc515", "name": "runTaskFromQueue task cost before running: 1 s 80 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433362967700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f062b08d-448a-422e-b88f-df7c47cd1130", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433360566500, "endTime": 31433363029000, "totalTime": 2381100}, "additional": {"logType": "info", "children": [], "durationId": "fa6b7e51-7915-428f-9720-3f3b8b4f8051"}}, {"head": {"id": "30eebe01-99bb-49cc-84c6-c2b7f8a529ec", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433369483900, "endTime": 31433370946100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4c65c587-711f-4528-93f8-495918f19808", "logId": "4075653e-f7a8-40e8-9f4b-0a38cc367ce8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c65c587-711f-4528-93f8-495918f19808", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433366609200}, "additional": {"logType": "detail", "children": [], "durationId": "30eebe01-99bb-49cc-84c6-c2b7f8a529ec"}}, {"head": {"id": "3ec23405-ad6e-4513-a64c-fbaa7132ac13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433367029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d3972a-0c58-4050-830b-eb3257ec64eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433367159200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bd8a29-21d1-4a15-93df-9703bf9ca96f", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433369493800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd2ce944-37a5-47ba-8af1-27b6146c7370", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433369810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8889092-76b7-4c64-b538-6942455e6ea9", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433370747400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e3b53cc-85e1-4879-8898-057e19c43472", "name": "entry : default@CacheNativeLibs cost memory 0.0898590087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433370880000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4075653e-f7a8-40e8-9f4b-0a38cc367ce8", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433369483900, "endTime": 31433370946100}, "additional": {"logType": "info", "children": [], "durationId": "30eebe01-99bb-49cc-84c6-c2b7f8a529ec"}}, {"head": {"id": "e7ce5c1e-8cd2-447d-8efb-93b7a9f9e61f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433582308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdfdbb5-577b-4a23-a7d5-6585c85fecaa", "name": "default@CompileArkTS work[63] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433583300500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0db64fc-1c11-441b-b6f4-becfb72b3f0a", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31434140097500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72cf3bd-12ea-4b52-b138-f8b43fdf4af4", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31434140293800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae10d12d-dcf7-4f73-9ef5-fdfbf4ffb061", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31434140738400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa1a5bd-6964-4d4e-97bc-329a3457e554", "name": "default@CompileArkTS work[64] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31434144770200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e4c5747-5402-4237-9663-864e844105b8", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436160949500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6017688a-3d96-4e39-8705-ecff6e87ba95", "name": "CopyResources is end, endTime: 31436161464700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436161472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f2d6dd-5096-484b-88b3-ea551bdbf356", "name": "default@CompileArkTS work[64] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436161841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11027644-8ffd-4af7-a3d8-bca24c772fab", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31436101866800, "endTime": 31436160673600}, "additional": {"logType": "info", "children": [], "durationId": "ddc6f0e3-8a97-4b4f-9c4e-cd996042e8a6", "parent": "9ec83abd-be57-46e5-a387-f9c58f716895"}}, {"head": {"id": "023a32f0-2b48-40e3-9f12-502c4f738ae8", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436650440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2aa6822-1eed-426d-b760-5fef15197e1a", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31433584284200, "endTime": 31433592044700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "380b235b-b1ce-41f5-afbd-e4858487623d", "logId": "6dc746a5-ffd8-4e75-8cc1-f52dd5f721cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dc746a5-ffd8-4e75-8cc1-f52dd5f721cd", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433584284200, "endTime": 31433592044700}, "additional": {"logType": "info", "children": [], "durationId": "d2aa6822-1eed-426d-b760-5fef15197e1a", "parent": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}}, {"head": {"id": "9a16c151-8a1c-4e34-af29-d5bb667abec6", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31433592086200, "endTime": 31433592872000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "380b235b-b1ce-41f5-afbd-e4858487623d", "logId": "164bd932-d0bd-4cde-aa34-14755d1d791a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "164bd932-d0bd-4cde-aa34-14755d1d791a", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433592086200, "endTime": 31433592872000}, "additional": {"logType": "info", "children": [], "durationId": "9a16c151-8a1c-4e34-af29-d5bb667abec6", "parent": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}}, {"head": {"id": "80e299e1-99aa-4b5f-857f-51197370525e", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31433592972200, "endTime": 31433593042500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "380b235b-b1ce-41f5-afbd-e4858487623d", "logId": "895be1a6-474f-4665-8d3d-0d5cce7030ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "895be1a6-474f-4665-8d3d-0d5cce7030ed", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433592972200, "endTime": 31433593042500}, "additional": {"logType": "info", "children": [], "durationId": "80e299e1-99aa-4b5f-857f-51197370525e", "parent": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}}, {"head": {"id": "8995be96-794f-4f21-9e91-2d036c60d2cc", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31433593097700, "endTime": 31436363888100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "380b235b-b1ce-41f5-afbd-e4858487623d", "logId": "62d01db7-cd33-4463-b51b-fcb7b92b6737"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62d01db7-cd33-4463-b51b-fcb7b92b6737", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433593097700, "endTime": 31436363888100}, "additional": {"logType": "info", "children": [], "durationId": "8995be96-794f-4f21-9e91-2d036c60d2cc", "parent": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}}, {"head": {"id": "1112860f-9f15-4945-9c6d-2e26e69cbb4f", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31436363908300, "endTime": 31436381385000}, "additional": {"children": ["cee9807f-a91b-44e8-8fe7-f0d69eef05cf", "7c4236f9-3778-4fc5-831a-4711b61d96d2", "69380fa6-ca90-4c75-83f5-ab196bdff390"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "380b235b-b1ce-41f5-afbd-e4858487623d", "logId": "6b3429ff-f424-4668-a1dd-4ca94f5ba466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b3429ff-f424-4668-a1dd-4ca94f5ba466", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436363908300, "endTime": 31436381385000}, "additional": {"logType": "info", "children": ["57ea354b-9277-41c2-98d7-4cdf2e1e5460", "fae9b22f-ba90-4af0-b5fe-c709854a7238", "c1af625b-70b1-4b17-9ca2-d151a353d82f"], "durationId": "1112860f-9f15-4945-9c6d-2e26e69cbb4f", "parent": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}}, {"head": {"id": "cee9807f-a91b-44e8-8fe7-f0d69eef05cf", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31436363921800, "endTime": 31436363929900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1112860f-9f15-4945-9c6d-2e26e69cbb4f", "logId": "57ea354b-9277-41c2-98d7-4cdf2e1e5460"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57ea354b-9277-41c2-98d7-4cdf2e1e5460", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436363921800, "endTime": 31436363929900}, "additional": {"logType": "info", "children": [], "durationId": "cee9807f-a91b-44e8-8fe7-f0d69eef05cf", "parent": "6b3429ff-f424-4668-a1dd-4ca94f5ba466"}}, {"head": {"id": "7c4236f9-3778-4fc5-831a-4711b61d96d2", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31436363933200, "endTime": 31436369505700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1112860f-9f15-4945-9c6d-2e26e69cbb4f", "logId": "fae9b22f-ba90-4af0-b5fe-c709854a7238"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fae9b22f-ba90-4af0-b5fe-c709854a7238", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436363933200, "endTime": 31436369505700}, "additional": {"logType": "info", "children": [], "durationId": "7c4236f9-3778-4fc5-831a-4711b61d96d2", "parent": "6b3429ff-f424-4668-a1dd-4ca94f5ba466"}}, {"head": {"id": "69380fa6-ca90-4c75-83f5-ab196bdff390", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31436369513200, "endTime": 31436381371600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1112860f-9f15-4945-9c6d-2e26e69cbb4f", "logId": "c1af625b-70b1-4b17-9ca2-d151a353d82f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1af625b-70b1-4b17-9ca2-d151a353d82f", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436369513200, "endTime": 31436381371600}, "additional": {"logType": "info", "children": [], "durationId": "69380fa6-ca90-4c75-83f5-ab196bdff390", "parent": "6b3429ff-f424-4668-a1dd-4ca94f5ba466"}}, {"head": {"id": "73668f4d-7067-4f88-b54a-ed970b8b4367", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31436381398500, "endTime": 31436644654400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "380b235b-b1ce-41f5-afbd-e4858487623d", "logId": "caf9ed12-b66c-419c-b3ac-4399c3d0b416"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caf9ed12-b66c-419c-b3ac-4399c3d0b416", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436381398500, "endTime": 31436644654400}, "additional": {"logType": "info", "children": [], "durationId": "73668f4d-7067-4f88-b54a-ed970b8b4367", "parent": "652ce380-e0ea-4cca-8e9c-1263fd79715c"}}, {"head": {"id": "975bcc46-5be2-4d8b-b326-956a2a81b79c", "name": "default@CompileArkTS work[63] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436666023700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "652ce380-e0ea-4cca-8e9c-1263fd79715c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31433584048500, "endTime": 31436644936800}, "additional": {"logType": "info", "children": ["6dc746a5-ffd8-4e75-8cc1-f52dd5f721cd", "164bd932-d0bd-4cde-aa34-14755d1d791a", "895be1a6-474f-4665-8d3d-0d5cce7030ed", "62d01db7-cd33-4463-b51b-fcb7b92b6737", "6b3429ff-f424-4668-a1dd-4ca94f5ba466", "caf9ed12-b66c-419c-b3ac-4399c3d0b416"], "durationId": "380b235b-b1ce-41f5-afbd-e4858487623d", "parent": "9ec83abd-be57-46e5-a387-f9c58f716895"}}, {"head": {"id": "008b7cc6-7adb-4854-ae16-68641b49ddec", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436667327400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec83abd-be57-46e5-a387-f9c58f716895", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31433280219400, "endTime": 31436667417100, "totalTime": 3133228300}, "additional": {"logType": "info", "children": ["652ce380-e0ea-4cca-8e9c-1263fd79715c", "11027644-8ffd-4af7-a3d8-bca24c772fab"], "durationId": "d4651c10-fcef-4000-97c1-8adf9c823152"}}, {"head": {"id": "b6bb45f2-55f3-4011-a7ba-9dcd172fbfae", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436686559300, "endTime": 31436689588200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e99835b2-82d7-44f7-a89b-aeaaed40fb0b", "logId": "dd1d83fe-d8c4-49ea-850b-f53b21b8163f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e99835b2-82d7-44f7-a89b-aeaaed40fb0b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436683305800}, "additional": {"logType": "detail", "children": [], "durationId": "b6bb45f2-55f3-4011-a7ba-9dcd172fbfae"}}, {"head": {"id": "ebf20fce-8cd4-46a7-9053-201e84ad908d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436684436300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fbf912e-f4d7-4d3d-9a99-cbf6dbf33ecf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436684863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1a943fc-5596-4553-b145-0ce08c5177f4", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436686571400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54f43931-2d7a-4110-ba99-51a791c78a6b", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436687072300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a862901-53ec-461c-90c3-54a74e6c72c1", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436688520900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0753d9-79c6-4b4b-9890-9293c56764dc", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0786895751953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436688921100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1d83fe-d8c4-49ea-850b-f53b21b8163f", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436686559300, "endTime": 31436689588200}, "additional": {"logType": "info", "children": [], "durationId": "b6bb45f2-55f3-4011-a7ba-9dcd172fbfae"}}, {"head": {"id": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436719923600, "endTime": 31437920437300}, "additional": {"children": ["3b231b84-3b21-4264-8103-f1d72eddb543", "c619b68b-2163-4902-8813-f2237cf6f1bf", "34a6e3e9-64c1-46ae-81f9-ece512889b09"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "e47a13f5-da8a-4adc-b1b4-779ccf24e892", "logId": "a3eca2a6-ec63-425c-99d8-d6e95fbc154a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e47a13f5-da8a-4adc-b1b4-779ccf24e892", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436694606000}, "additional": {"logType": "detail", "children": [], "durationId": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6"}}, {"head": {"id": "5347050f-c42e-4fe9-a12a-e640e0a6aafb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436695386000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "301051f2-d6e9-4791-bfc5-aa6c8d1958a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436695550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e326d7-b233-4257-bbd6-c8fdfc143523", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436719938100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1187fdad-ec23-4a6b-939b-458800a120f4", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436771499700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827e8157-71e8-4a5e-aad1-deb916bd3021", "name": "Incremental task entry:default@PackageHap pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436771890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1cf522-03b7-4dd6-8c09-d7dd41752e5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436772009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37976f4c-0bf5-4573-962c-fca60c7e64e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436772071100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b231b84-3b21-4264-8103-f1d72eddb543", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436774540900, "endTime": 31436778990300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6", "logId": "62079be2-d439-4750-a625-ff623e404889"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a4e23b7-29bc-4b4c-bef4-8b098382a324", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436778498800}, "additional": {"logType": "debug", "children": [], "durationId": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6"}}, {"head": {"id": "62079be2-d439-4750-a625-ff623e404889", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436774540900, "endTime": 31436778990300}, "additional": {"logType": "info", "children": [], "durationId": "3b231b84-3b21-4264-8103-f1d72eddb543", "parent": "a3eca2a6-ec63-425c-99d8-d6e95fbc154a"}}, {"head": {"id": "c619b68b-2163-4902-8813-f2237cf6f1bf", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436779961900, "endTime": 31436783539100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6", "logId": "726a76cc-2c3b-48ac-a72f-ad38891926b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "feb32313-febb-4e89-b5df-31aafb26ca8d", "name": "default@PackageHap work[65] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436781034400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34a6e3e9-64c1-46ae-81f9-ece512889b09", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31437300825800, "endTime": 31437919821800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6", "logId": "95305b39-4ae4-4078-bdeb-db73d2d10f8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61f71eff-910e-4801-baa9-f893f5b29f4b", "name": "default@PackageHap work[65] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436783303200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293f0613-4d9e-4159-b180-f1f52a0988bb", "name": "default@PackageHap work[65] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436783468000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726a76cc-2c3b-48ac-a72f-ad38891926b8", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436779961900, "endTime": 31436783539100}, "additional": {"logType": "info", "children": [], "durationId": "c619b68b-2163-4902-8813-f2237cf6f1bf", "parent": "a3eca2a6-ec63-425c-99d8-d6e95fbc154a"}}, {"head": {"id": "2520440d-f098-4fd0-8a0a-503d04826eba", "name": "entry : default@PackageHap cost memory 1.2835922241210938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436796290400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecdfa42e-5200-423a-8511-aa913e771581", "name": "default@PackageHap work[65] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437043516800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f3616d-17f6-4c66-b9ce-d480da05f348", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437813676200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e25960-820a-4c9c-9fef-78b6547b9b4e", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437813842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "384939a5-02af-4134-ab66-b9d97db664e5", "name": "A work dispatched to worker[3] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437813921500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97adbe5-d55a-45d2-82af-7c01529e41cf", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437813975100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b48f71f-bf87-49c8-a6f5-1f945e6add33", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437814017100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de6e6efd-199f-4e0f-b5c3-6a17d7f44ab9", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437814058700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82e4859-faf2-458b-9b2d-159b573fa710", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437919915300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953c4130-c8c0-45ec-a3c5-adcf5b8db69d", "name": "default@PackageHap work[65] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437920218300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95305b39-4ae4-4078-bdeb-db73d2d10f8d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31437300825800, "endTime": 31437919821800}, "additional": {"logType": "info", "children": [], "durationId": "34a6e3e9-64c1-46ae-81f9-ece512889b09", "parent": "a3eca2a6-ec63-425c-99d8-d6e95fbc154a"}}, {"head": {"id": "a3eca2a6-ec63-425c-99d8-d6e95fbc154a", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31436719923600, "endTime": 31437920437300, "totalTime": 695503300}, "additional": {"logType": "info", "children": ["62079be2-d439-4750-a625-ff623e404889", "726a76cc-2c3b-48ac-a72f-ad38891926b8", "95305b39-4ae4-4078-bdeb-db73d2d10f8d"], "durationId": "fcd3b9af-03ff-4a98-b1f1-6f712f6610a6"}}, {"head": {"id": "6c269acc-dde0-4972-a4ec-53bb83bbd04b", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437930511000, "endTime": 31437932739200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "3f24d4c2-41a4-4906-8888-b43847ddf401", "logId": "11c767d5-2f5b-4e80-a48f-fc2dab10b4b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f24d4c2-41a4-4906-8888-b43847ddf401", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437927141000}, "additional": {"logType": "detail", "children": [], "durationId": "6c269acc-dde0-4972-a4ec-53bb83bbd04b"}}, {"head": {"id": "33775eef-9dcd-49cf-bcc6-6f35cd070942", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437927537700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c545270d-6ee9-4176-95bf-4706ccbb46fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437927675500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba07a13-ec72-470d-a739-d740004f0d95", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437930524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d905b2-75f2-4192-bced-2fcc3b1aa820", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437930949800}, "additional": {"logType": "warn", "children": [], "durationId": "6c269acc-dde0-4972-a4ec-53bb83bbd04b"}}, {"head": {"id": "dfdbcc17-91cb-487a-9ba2-1c65e700beff", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437931642300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c8cf25-f608-43b3-8b0e-8e3f634dfb38", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437931750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8041f861-fe2a-4684-ad01-5727f8cd1d73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437931839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c80642-9b83-4370-a332-60653020f8d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437931898500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6babe14b-1505-410f-b2bd-ff71e288e499", "name": "entry : default@SignHap cost memory 0.12108612060546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437932450100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ac7219-d505-428c-9a11-139562dfadb0", "name": "runTaskFromQueue task cost before running: 5 s 650 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437932619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c767d5-2f5b-4e80-a48f-fc2dab10b4b5", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437930511000, "endTime": 31437932739200, "totalTime": 2055700}, "additional": {"logType": "info", "children": [], "durationId": "6c269acc-dde0-4972-a4ec-53bb83bbd04b"}}, {"head": {"id": "a376402c-6aac-46cf-902d-bd6aad522898", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437936260500, "endTime": 31437943894600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f9734d4d-e4a0-42a3-b025-c9728fa0c2bf", "logId": "68aaeb7c-6a73-47c4-bb82-6c2042826fba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9734d4d-e4a0-42a3-b025-c9728fa0c2bf", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437934948800}, "additional": {"logType": "detail", "children": [], "durationId": "a376402c-6aac-46cf-902d-bd6aad522898"}}, {"head": {"id": "6c34cff8-4e7f-4d87-b7d1-a1e068d182c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437935352100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "736d37ee-ca2f-4730-a2f9-f7984c02ccc6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437935458300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3aff890-1600-4def-8bb8-2b08d95c45a6", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437936271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c0b5a47-95a9-4712-89b9-e11f5b25a49e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437943556900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4577fbaa-c010-4761-953e-21fbb5938302", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437943679700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7932204-53dc-4268-883f-893ee028aadb", "name": "entry : default@CollectDebugSymbol cost memory 0.24016571044921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437943761100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac396bc6-d2ce-460c-b484-4cecda111be3", "name": "runTaskFromQueue task cost before running: 5 s 661 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437943838800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68aaeb7c-6a73-47c4-bb82-6c2042826fba", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437936260500, "endTime": 31437943894600, "totalTime": 7558500}, "additional": {"logType": "info", "children": [], "durationId": "a376402c-6aac-46cf-902d-bd6aad522898"}}, {"head": {"id": "06ec60ee-9053-436a-a19f-5469e79533ed", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437945544600, "endTime": 31437945828300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "94cef8c9-3ac2-4307-840b-96a8dd6a1704", "logId": "8ef2f16c-2f23-4e87-9982-9e2631aee366"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94cef8c9-3ac2-4307-840b-96a8dd6a1704", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437945505100}, "additional": {"logType": "detail", "children": [], "durationId": "06ec60ee-9053-436a-a19f-5469e79533ed"}}, {"head": {"id": "104eb1dd-bc64-4bd6-bfd6-5f8291956fc1", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437945549900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9b6171-26dc-47df-b688-15d0f8bc0342", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437945695500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e13305-5962-48ad-903f-b544cc0c1642", "name": "runTaskFromQueue task cost before running: 5 s 663 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437945774800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ef2f16c-2f23-4e87-9982-9e2631aee366", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437945544600, "endTime": 31437945828300, "totalTime": 211200}, "additional": {"logType": "info", "children": [], "durationId": "06ec60ee-9053-436a-a19f-5469e79533ed"}}, {"head": {"id": "a5b32642-2be3-4ff7-9fc3-c45d83438bca", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437954294500, "endTime": 31437954315400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0a0aef10-634a-4ec4-b451-e1226b5789bd", "logId": "dbe2bd88-eb18-437c-8384-13993dc6ed9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbe2bd88-eb18-437c-8384-13993dc6ed9c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437954294500, "endTime": 31437954315400}, "additional": {"logType": "info", "children": [], "durationId": "a5b32642-2be3-4ff7-9fc3-c45d83438bca"}}, {"head": {"id": "bac3c5e1-3fc9-4fd0-a64f-ad7d3f207261", "name": "BUILD SUCCESSFUL in 5 s 672 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437954387800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "e895eba5-bf31-4004-b197-80c13b531aa4", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31432283166800, "endTime": 31437955305100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 13}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "5a3d5980-3812-4975-bf8a-56b6ffa65a56", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437955483200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4116b3d1-5cb9-42b2-bf06-b5eeaa4a9edb", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437955781500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211e9e31-2dd5-4635-8730-2edee4ef573e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437955931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb7103d0-e3b1-4263-9e31-394b95ea7ee4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437956013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a92e58-2953-4af9-a6cd-ed20939f734d", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437956094900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "206f53b0-0794-4708-acd6-c1d6d78bf766", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437956578100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74d0805-9927-4591-a358-7a36625ab212", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437957174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4174bc62-d337-48b5-ae07-09400956cf48", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437957389900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18dc726a-8947-4275-953c-4bb58911fb52", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437957455300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb2e3e14-2952-4f85-ab4b-a003d89d66c0", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437957518400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b18578e-360e-4ff5-b1a6-b06c1a6bba2c", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437957890700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "903379bc-e51f-4280-890e-6e6bc70fdb28", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437959810500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514165b1-ebad-4b0f-a60e-bcbe60c4e7a3", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437960475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f96ca02-45f8-49ff-98b5-9fb230be48ea", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437960573200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3eda18f-9adc-42c2-b0ee-58f5b5b46384", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437960647200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b41b415-80ad-4ca2-828b-cbbfa1c08f67", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437960706900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce89ecb-1e74-4a3a-a673-69e32ab44f7d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437960756700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b74c965f-8f3a-4628-a716-3af9f6205b40", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437961100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c8d38b-ab42-46d0-b900-1b732ff397b6", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437961372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0a9424-9918-4d1c-8c7e-904d51cf2580", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437961569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f173b50-4a9e-4da0-b19b-c5b20a667790", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437961980500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84e975b2-38fa-458f-b3da-dfba45356859", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437962058000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92aa1ac6-14bc-4b5e-aba5-aa08bb34eeeb", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437962122800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7c5e81-f661-4bf9-8b02-1529ec5951dc", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437963955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6869a152-3a77-4ba4-b4a9-1f3751d0164a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437964361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca7e812-188c-4a8e-8acb-63dcc8d8e909", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437964977700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c1a514-1868-40aa-a939-15f6ce3babc1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437965151500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "535b5358-5aba-46a9-9833-0afcb9ebc043", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437965316200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b04b7ef-257e-4415-a376-7cb326050296", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437965862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55426c26-39c1-42dd-980a-0d94c0607996", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437965936400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1814dd70-6241-4f6c-9ef5-5a3bbb64bfa8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437966118400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb7832d7-fe00-43ed-9db0-923ed7dab7a9", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437966314700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b79a1f-c717-4690-bfaa-c71268c5e068", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437966922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a4719c-ab06-43ea-ae58-609932342ca0", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437967827000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ebb28b5-573b-4bbe-a2e6-4cbac2e6b2ae", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437968545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dbe0f8e-27eb-4a97-96ea-e067eec22282", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437969430800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74b752c0-84e6-4ea4-a529-3a4816533219", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437969632700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bdd41fe-3e23-4cfb-8a50-820893e72a66", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437969799700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4053329-96c3-445e-98bc-b0ddd05e8262", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437970244300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c7a4476-3ada-4a6e-a3ac-ca6183f9adee", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437970453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf0f1e1a-e329-4696-a941-2483f792b512", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437970518200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfe5ad81-d8d4-482c-9bda-5daf6b961c24", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437970564500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4591cc0c-21b1-47f4-a368-1edc6490898a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437971466100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef75a013-5deb-44de-933f-30fccd9f81c0", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437971871000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c368fe-3383-4fab-9066-47a2a482e879", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437972098500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e1a930b-364b-458d-a99f-6a23b8d4f416", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437981149200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb064794-caf9-45c1-840f-3346f42b57f0", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437981384000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9c9041-a6c3-4040-825d-9b2c9b63ca7a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437981552600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d1c3e3-0bc8-42ec-9f00-1ba145280361", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437981637600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a160e72-013b-4a54-8daf-b48c444f1d7a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437981811700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a56ddeb2-9a06-4db8-9ab6-d0a6afab2683", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437982373700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f369779e-6a2c-4b57-9b74-fcfe79a5ff36", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437982597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d0c4c3-1a72-4a6b-899c-d5de57b5fa88", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437982846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "709bebb2-0440-4864-b055-38cce48e3031", "name": "Incremental task entry:default@PackageHap post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437983101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0324fe6-fa21-4f40-b0ee-55f006bd587f", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437983275300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea4e833-e01c-4b80-a6c5-63bd928505b5", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437983344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1d6522-cd0a-4f92-84ac-e4fb14115931", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437983531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c7fda5-2e82-4735-a1e3-9ce30051d091", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437986053400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d8272f-25a7-4367-bef7-2d6c90dbf15e", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437986454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a30c26-8bbc-4eb4-8096-372526c27d71", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437986738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d03f65e-78b5-449b-ac51-e7b0653d26b2", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31437986959400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}