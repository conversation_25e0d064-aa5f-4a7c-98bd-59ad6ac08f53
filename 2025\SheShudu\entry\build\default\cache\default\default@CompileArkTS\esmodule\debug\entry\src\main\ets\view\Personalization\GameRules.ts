if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface GameRules_Params {
    pageInfos?: NavPathStack;
    scroller?: Scroller;
}
import CommonConstants from "@bundle:com.atomicservice.6917576098804821135/entry/ets/common/CommonConstants";
import Logger from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/Logger";
export function GameRulesBuilder(name: string, param: Object, parent = null) {
    {
        (parent ? parent : this).observeComponentCreation2((elmtId, isInitialRender) => {
            if (isInitialRender) {
                let componentCall = new GameRules(parent ? parent : this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/Personalization/GameRules.ets", line: 6, col: 3 });
                ViewPU.create(componentCall);
                let paramsLambda = () => {
                    return {};
                };
                componentCall.paramsGenerator_ = paramsLambda;
            }
            else {
                (parent ? parent : this).updateStateVarsOfChildByElmtId(elmtId, {});
            }
        }, { name: "GameRules" });
    }
}
export class GameRules extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.pageInfos = new NavPathStack();
        this.scroller = new Scroller();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: GameRules_Params) {
        if (params.pageInfos !== undefined) {
            this.pageInfos = params.pageInfos;
        }
        if (params.scroller !== undefined) {
            this.scroller = params.scroller;
        }
    }
    updateStateVars(params: GameRules_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private pageInfos: NavPathStack;
    private scroller: Scroller;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            NavDestination.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.height(CommonConstants.FULL_PARENT);
                    Column.width(CommonConstants.FULL_PARENT);
                    Column.backgroundColor('#F5F5F5');
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Scroll.create(this.scroller);
                    Scroll.scrollable(ScrollDirection.Vertical);
                    Scroll.scrollBar(BarState.Off);
                    Scroll.edgeEffect(EdgeEffect.Spring);
                }, Scroll);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 20 });
                    Column.width(CommonConstants.FULL_PARENT);
                    Column.padding({ bottom: 20 });
                    Column.justifyContent(FlexAlign.Center);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('数独游戏规则');
                    Text.fontSize(24);
                    Text.fontWeight(FontWeight.Bold);
                    Text.margin({ top: 20, bottom: 10 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 10 });
                    Column.width('90%');
                    Column.backgroundColor('#FFFFFF');
                    Column.padding(15);
                    Column.borderRadius(16);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('基本规则:');
                    Text.fontSize(18);
                    Text.fontWeight(FontWeight.Medium);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('1. 数独是一种9×9的网格填数游戏。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('2. 每一行、每一列和每个3×3的小九宫格内都必须包含1到9的数字，且不能重复。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('3. 游戏开始时，部分格子已经填有数字，这些是提示数字，不能更改。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('4. 玩家需要根据逻辑推理，填充所有空白格子。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 10 });
                    Column.width('90%');
                    Column.backgroundColor('#FFFFFF');
                    Column.padding(15);
                    Column.borderRadius(16);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('游戏技巧:');
                    Text.fontSize(18);
                    Text.fontWeight(FontWeight.Medium);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('1. 排除法: 通过已知数字排除某格子的可能性。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('2. 唯一候选数法: 找出某格子唯一可能的数字。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('3. 区块摒除法: 分析小九宫格内数字的分布规律。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('4. 从简单难度开始，逐步提高挑战。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 10 });
                    Column.width('90%');
                    Column.backgroundColor('#FFFFFF');
                    Column.padding(15);
                    Column.borderRadius(16);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('游戏模式:');
                    Text.fontSize(18);
                    Text.fontWeight(FontWeight.Medium);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('1. 数独经典模式: 使用1-9数字的传统数独。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('2. 卡通模式: 使用可爱动物图标代替数字，规则相同。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('3. 难度分为简单、中等和困难三个级别。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 10 });
                    Column.width('90%');
                    Column.backgroundColor('#FFF8F0');
                    Column.padding(15);
                    Column.borderRadius(16);
                    Column.border({ width: 1, color: '#FFE4B5' });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('自在模式:');
                    Text.fontSize(18);
                    Text.fontWeight(FontWeight.Medium);
                    Text.fontColor('#FF6B35');
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('自在模式是一种特殊的数独游戏模式，为玩家提供更自由的游戏体验。');
                    Text.fontSize(16);
                    Text.fontWeight(FontWeight.Medium);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('特点:');
                    Text.fontSize(16);
                    Text.fontWeight(FontWeight.Medium);
                    Text.margin({ top: 8 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 空白棋盘: 游戏开始时提供一个完全空白的9×9棋盘，没有任何预设数字。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 自由填写: 玩家可以在任意位置填入1-9的数字或动物图标。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 多人协作: 支持两人或多人轮流填写，共同完成数独挑战。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 单人练习: 也可以一个人慢慢思考，按照数独规则尝试填满整个棋盘。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 创意发挥: 可以先设计一个有趣的数独布局，再让朋友来解答。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('游戏规则:');
                    Text.fontSize(16);
                    Text.fontWeight(FontWeight.Medium);
                    Text.margin({ top: 8 });
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 遵循标准数独规则：每行、每列、每个3×3小九宫格内数字不重复。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 可以随时修改已填入的数字，直到找到正确的解答。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('• 系统会实时检测填入的数字是否符合数独规则。');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                Column.pop();
                Column.pop();
                Scroll.pop();
                Column.pop();
            }, { moduleName: "entry", pagePath: "entry/src/main/ets/view/Personalization/GameRules" });
            NavDestination.title('玩法介绍');
            NavDestination.linearGradient({
                angle: 180,
                colors: [['#FFF6F6', 0.0], ['#FFF0F0', 0.5], ['#FFFAFA', 1.0]]
            });
            NavDestination.onReady((context: NavDestinationContext) => {
                this.pageInfos = context.pathStack;
                Logger.info("GameRules page ready");
            });
        }, NavDestination);
        NavDestination.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
(function () {
    if (typeof NavigationBuilderRegister === "function") {
        NavigationBuilderRegister("gameRules", wrapBuilder(GameRulesBuilder));
    }
})();
