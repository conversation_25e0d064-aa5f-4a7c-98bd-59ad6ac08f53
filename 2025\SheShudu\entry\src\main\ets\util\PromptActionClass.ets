import { BusinessError } from '@kit.BasicServicesKit';
import { ComponentContent, promptAction, window } from '@kit.ArkUI';
import { UIContext } from '@ohos.arkui.UIContext';

export class PromptActionClass {
   ctx: UIContext|undefined;
   contentNode: ComponentContent<Object>|undefined;
   options: promptAction.BaseDialogOptions|undefined;

   setContext(context: UIContext) {
    this.ctx = context;
  }

   setContentNode(node: ComponentContent<Object>) {
    this.contentNode = node;
  }

   setOptions(options: promptAction.BaseDialogOptions) {
    this.options = options;
  }

   openDialog() {
    if (this.contentNode !== null && this.ctx!=null) {
      this.ctx.getPromptAction().openCustomDialog(this.contentNode, this.options)
        .then(() => {
          console.info('OpenCustomDialog complete.')
        })
        .catch((error: BusinessError) => {
          let message = (error as BusinessError).message;
          let code = (error as BusinessError).code;
          console.error(`OpenCustomDialog args error code is ${code}, message is ${message}`);
        })
    }
  }

   closeDialog() {
    if (this.contentNode !== null &&this.ctx!=null) {
      this.ctx.getPromptAction().closeCustomDialog(this.contentNode)
        .then(() => {
          console.info('CloseCustomDialog complete.')
        })
        .catch((error: BusinessError) => {
          let message = (error as BusinessError).message;
          let code = (error as BusinessError).code;
          console.error(`CloseCustomDialog args error code is ${code}, message is ${message}`);
        })
    }
  }

   updateDialog(options: promptAction.BaseDialogOptions) {
    if (this.contentNode !== null && this.ctx!=null) {
      this.ctx.getPromptAction().updateCustomDialog(this.contentNode, options)
        .then(() => {
          console.info('UpdateCustomDialog complete.')
        })
        .catch((error: BusinessError) => {
          let message = (error as BusinessError).message;
          let code = (error as BusinessError).code;
          console.error(`UpdateCustomDialog args error code is ${code}, message is ${message}`);
        })
    }
  }
}