{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "a9f7128d-54b4-4c45-8f80-546798173ee3", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078170911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfe1b9b-9f68-4737-9b01-8a69fa29d09f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078175191800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f805a9-8976-4881-aedf-7a4353fc5736", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078175451700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d996d1d7-3f55-4298-9f54-776184e50bdc", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078179104400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b806a89a-32dd-4b05-bbb0-a48202c9ab3d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148342035300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148350620800, "endTime": 31148873680800}, "additional": {"children": ["140898f6-297f-42d1-970b-32d4dd329f65", "6aa4c172-978c-4173-82e4-8c6c083c8763", "72d83f98-aa71-464f-97de-aaf70a6a107a", "52bc7ea4-3a27-44ed-a1be-164c095ec04b", "eca986b7-77d9-4687-becf-e02afe819062", "9e082db5-c7d8-46f5-b107-c9634d04d8b5", "1f6b85f1-89f4-46d8-96c9-8b883189a87b"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "140898f6-297f-42d1-970b-32d4dd329f65", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148350624300, "endTime": 31148363892200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "c5fbfe96-abbe-4026-9b80-83b5c<PERSON>be48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6aa4c172-978c-4173-82e4-8c6c083c8763", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148363919000, "endTime": 31148870522700}, "additional": {"children": ["6bfaa701-b523-4478-b9f9-e20bf7626da5", "09cff122-2f7e-46e7-9529-8b954d68d953", "715b9d69-94d3-485c-bcfc-8175f5a72029", "a450d9d1-1b94-4d82-a5db-b94f024986a3", "8483bee6-da2a-4400-906a-d917460e7cb7", "4af3134e-1a8c-4f3e-91ee-60751c9e27eb", "0ed67e07-dfe1-41ad-8886-ddc4471ed1f4", "561f3ccd-ebc0-459b-9856-988026ca2a77", "6975687b-26b1-4a25-b0c4-4bbd39b555db"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72d83f98-aa71-464f-97de-aaf70a6a107a", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148870587000, "endTime": 31148873671500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "b286d0a5-0b1c-45e9-a0a7-9bb0aef2e3a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52bc7ea4-3a27-44ed-a1be-164c095ec04b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148873676800, "endTime": 31148873678200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "3cd28ba7-843f-435b-bbc0-24255529bcb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eca986b7-77d9-4687-becf-e02afe819062", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148354410300, "endTime": 31148354445900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "e19d0a9c-839c-4d17-bb88-743f7efa1e43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e19d0a9c-839c-4d17-bb88-743f7efa1e43", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148354410300, "endTime": 31148354445900}, "additional": {"logType": "info", "children": [], "durationId": "eca986b7-77d9-4687-becf-e02afe819062", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "9e082db5-c7d8-46f5-b107-c9634d04d8b5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148358843600, "endTime": 31148358864900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "84101103-9d10-4f7b-bb52-e0b55b0bbbe5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84101103-9d10-4f7b-bb52-e0b55b0bbbe5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148358843600, "endTime": 31148358864900}, "additional": {"logType": "info", "children": [], "durationId": "9e082db5-c7d8-46f5-b107-c9634d04d8b5", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "2e71102c-ff93-41b9-b4aa-2a69904429bc", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148358921500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee953334-2ba3-4ef8-8b0d-e7d57a562450", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148363646100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5fbfe96-abbe-4026-9b80-83b5c<PERSON>be48", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148350624300, "endTime": 31148363892200}, "additional": {"logType": "info", "children": [], "durationId": "140898f6-297f-42d1-970b-32d4dd329f65", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "6bfaa701-b523-4478-b9f9-e20bf7626da5", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148369739200, "endTime": 31148369746500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "f67a0348-b1db-4a0d-9a6f-f340c1741548"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09cff122-2f7e-46e7-9529-8b954d68d953", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148369762000, "endTime": 31148373978800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "71a14e55-f618-4317-b0cf-7c31fe6dfd7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "715b9d69-94d3-485c-bcfc-8175f5a72029", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148374006400, "endTime": 31148571373300}, "additional": {"children": ["bdaff3e9-cc75-4d23-841e-60688334a65e", "2bd5bff5-9080-42b7-bb72-b7abc783136f", "8a5da076-962c-46ac-a7cf-086ab756851f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "db1009cf-678e-4aff-a325-fdf646ccba1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a450d9d1-1b94-4d82-a5db-b94f024986a3", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148571391900, "endTime": 31148677351300}, "additional": {"children": ["aa5e9427-4bd6-4c8f-a318-1009773c79d7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "4416e603-8d75-428a-a24b-345880c2b41f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8483bee6-da2a-4400-906a-d917460e7cb7", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148677360400, "endTime": 31148825735900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "80020c9b-c5f8-427a-9be7-dad7df49e273"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4af3134e-1a8c-4f3e-91ee-60751c9e27eb", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148826763900, "endTime": 31148859156700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "8f8d740e-6203-48a0-a126-91bd59ee116d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ed67e07-dfe1-41ad-8886-ddc4471ed1f4", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148859182800, "endTime": 31148870184100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "eaae3a0d-1220-4b57-b8cb-d7b04824fc5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "561f3ccd-ebc0-459b-9856-988026ca2a77", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148870206000, "endTime": 31148870505400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "49bd6095-0da1-4175-8f26-86d748d81910"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f67a0348-b1db-4a0d-9a6f-f340c1741548", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148369739200, "endTime": 31148369746500}, "additional": {"logType": "info", "children": [], "durationId": "6bfaa701-b523-4478-b9f9-e20bf7626da5", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "71a14e55-f618-4317-b0cf-7c31fe6dfd7e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148369762000, "endTime": 31148373978800}, "additional": {"logType": "info", "children": [], "durationId": "09cff122-2f7e-46e7-9529-8b954d68d953", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "bdaff3e9-cc75-4d23-841e-60688334a65e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148374885300, "endTime": 31148374925100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "715b9d69-94d3-485c-bcfc-8175f5a72029", "logId": "c0178f62-efc3-4138-a9db-26bcc7862cd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0178f62-efc3-4138-a9db-26bcc7862cd3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148374885300, "endTime": 31148374925100}, "additional": {"logType": "info", "children": [], "durationId": "bdaff3e9-cc75-4d23-841e-60688334a65e", "parent": "db1009cf-678e-4aff-a325-fdf646ccba1e"}}, {"head": {"id": "2bd5bff5-9080-42b7-bb72-b7abc783136f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148377949600, "endTime": 31148570394800}, "additional": {"children": ["9d5aef7a-d325-43c5-8533-582d706560cf", "35af8fe9-b028-4fac-a814-61ac2e0cb374"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "715b9d69-94d3-485c-bcfc-8175f5a72029", "logId": "8fd74e68-0845-49fd-a969-37fe2320219f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d5aef7a-d325-43c5-8533-582d706560cf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148377951000, "endTime": 31148383818300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bd5bff5-9080-42b7-bb72-b7abc783136f", "logId": "115d5e35-ee00-4092-a90e-8bdf266cccea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35af8fe9-b028-4fac-a814-61ac2e0cb374", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148383839800, "endTime": 31148570383100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2bd5bff5-9080-42b7-bb72-b7abc783136f", "logId": "4ceda024-1559-48f9-948b-04acd77919a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad1e1017-8270-4829-82fd-51d985a36368", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148377955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34298623-8625-41e3-af5d-3d39ebca3867", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148383662200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115d5e35-ee00-4092-a90e-8bdf266cccea", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148377951000, "endTime": 31148383818300}, "additional": {"logType": "info", "children": [], "durationId": "9d5aef7a-d325-43c5-8533-582d706560cf", "parent": "8fd74e68-0845-49fd-a969-37fe2320219f"}}, {"head": {"id": "3810d578-78ed-48db-a89e-12b1b53aee59", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148383849600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6afe538-a733-48b0-824c-1f1f1045f940", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148390859700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0f0dd87-7f19-45bc-8a22-46ee79e1ba50", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148391012800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542b68ad-9f29-4df0-9c94-f310578c27b4", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148391161700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e46926a-dd9e-4df0-838b-d4d208d5b1d9", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148391248800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c9d5a6c-ff8b-4e37-84f4-1c7b9aa55a97", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148393020600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63bd98f9-9d2a-4117-8992-3d19e968e475", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148398493700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f1f03be-fe15-4251-b9f5-3f8ce4aa6f13", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148410858700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20454a18-3d3f-4f68-9ab6-135e101b1e60", "name": "Sdk init in 75 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148474069900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48cd2db1-d09e-4f84-9d44-8c5256462b2f", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148474363000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 8}, "markType": "other"}}, {"head": {"id": "5854e0d2-3665-4200-9ac3-21c849bb6d97", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148474379600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 8}, "markType": "other"}}, {"head": {"id": "810c6ce8-6e99-41bd-9214-c5e14963a38e", "name": "Project task initialization takes 94 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148570095300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ef0566-cdab-40ab-a03d-1ca24a50af31", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148570222200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89523491-5678-43ad-9c33-b8d714110e53", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148570281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f22b1dd-a4c5-4a5c-8014-b45b1c69beaa", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148570330700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ceda024-1559-48f9-948b-04acd77919a8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148383839800, "endTime": 31148570383100}, "additional": {"logType": "info", "children": [], "durationId": "35af8fe9-b028-4fac-a814-61ac2e0cb374", "parent": "8fd74e68-0845-49fd-a969-37fe2320219f"}}, {"head": {"id": "8fd74e68-0845-49fd-a969-37fe2320219f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148377949600, "endTime": 31148570394800}, "additional": {"logType": "info", "children": ["115d5e35-ee00-4092-a90e-8bdf266cccea", "4ceda024-1559-48f9-948b-04acd77919a8"], "durationId": "2bd5bff5-9080-42b7-bb72-b7abc783136f", "parent": "db1009cf-678e-4aff-a325-fdf646ccba1e"}}, {"head": {"id": "8a5da076-962c-46ac-a7cf-086ab756851f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148571320800, "endTime": 31148571350700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "715b9d69-94d3-485c-bcfc-8175f5a72029", "logId": "f110b0d8-ca02-4ce3-9fc8-c28060a1560f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f110b0d8-ca02-4ce3-9fc8-c28060a1560f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148571320800, "endTime": 31148571350700}, "additional": {"logType": "info", "children": [], "durationId": "8a5da076-962c-46ac-a7cf-086ab756851f", "parent": "db1009cf-678e-4aff-a325-fdf646ccba1e"}}, {"head": {"id": "db1009cf-678e-4aff-a325-fdf646ccba1e", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148374006400, "endTime": 31148571373300}, "additional": {"logType": "info", "children": ["c0178f62-efc3-4138-a9db-26bcc7862cd3", "8fd74e68-0845-49fd-a969-37fe2320219f", "f110b0d8-ca02-4ce3-9fc8-c28060a1560f"], "durationId": "715b9d69-94d3-485c-bcfc-8175f5a72029", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "aa5e9427-4bd6-4c8f-a318-1009773c79d7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148573271200, "endTime": 31148677338400}, "additional": {"children": ["b64e6313-2c43-41bd-b355-0c1afc7c3a5c", "659cb5a6-1342-41c8-b67e-48e0a17812d7", "7c722c3e-3524-4a55-a14b-c1a69752851a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a450d9d1-1b94-4d82-a5db-b94f024986a3", "logId": "603e5040-5c47-41cb-9afd-41e00f9fca64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b64e6313-2c43-41bd-b355-0c1afc7c3a5c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148598265800, "endTime": 31148598298600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa5e9427-4bd6-4c8f-a318-1009773c79d7", "logId": "38e5dcd2-7b57-4441-9c16-40105f4b0d15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "38e5dcd2-7b57-4441-9c16-40105f4b0d15", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148598265800, "endTime": 31148598298600}, "additional": {"logType": "info", "children": [], "durationId": "b64e6313-2c43-41bd-b355-0c1afc7c3a5c", "parent": "603e5040-5c47-41cb-9afd-41e00f9fca64"}}, {"head": {"id": "659cb5a6-1342-41c8-b67e-48e0a17812d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148623137900, "endTime": 31148674445700}, "additional": {"children": ["ab2d86b9-f287-49a7-94e0-e094b3288c9a", "fd189c22-c5a7-4ce8-9cd0-ddd888c922e3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa5e9427-4bd6-4c8f-a318-1009773c79d7", "logId": "8a507f28-ac87-43a0-a6c2-fe6f267b0561"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab2d86b9-f287-49a7-94e0-e094b3288c9a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148623143100, "endTime": 31148630916400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "659cb5a6-1342-41c8-b67e-48e0a17812d7", "logId": "163dd6d8-b4a2-4e95-98a6-69e2f8c9fe90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd189c22-c5a7-4ce8-9cd0-ddd888c922e3", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148630949300, "endTime": 31148674419900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "659cb5a6-1342-41c8-b67e-48e0a17812d7", "logId": "7ffa531e-0533-4c8a-8675-0b932752bb36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09542ce0-6dee-4c23-9a3d-3b8e7712bae6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148623152800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f2c6d6-dd32-4f07-b828-16cbaacf2b8c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148629784300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "163dd6d8-b4a2-4e95-98a6-69e2f8c9fe90", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148623143100, "endTime": 31148630916400}, "additional": {"logType": "info", "children": [], "durationId": "ab2d86b9-f287-49a7-94e0-e094b3288c9a", "parent": "8a507f28-ac87-43a0-a6c2-fe6f267b0561"}}, {"head": {"id": "741a9f6f-6945-4649-9bd9-a6f8df746588", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148630969800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a45a03cf-dc02-4b3a-b09d-fe89c818ab85", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148647315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c80553-d1a1-4d7b-960c-df44902eb0ea", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148647497400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eaf7041-35c5-461f-bebc-e830c6234db3", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148647819100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38778551-64db-4a9d-8d25-7ce3d18f9cc6", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148648062300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47096904-7ffa-4e87-b9b8-02a3afdb306f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148648207500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9033d6cd-1981-4e52-9c72-77f88f9e4019", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148648266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37a3f4e-d2f3-4eb0-93ea-915a0f2257b7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148648333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f52597-d8a5-4e2f-9746-e6c12f2ca68d", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148657027200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33b28c34-96a4-47e9-ac60-bf7329f240b8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148657772500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f5ff6cc-fc15-490d-a18a-2f6d0ffb337d", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148657853200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2775b85-8967-4112-8716-5ee183e916ac", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148658032000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ffa531e-0533-4c8a-8675-0b932752bb36", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148630949300, "endTime": 31148674419900}, "additional": {"logType": "info", "children": [], "durationId": "fd189c22-c5a7-4ce8-9cd0-ddd888c922e3", "parent": "8a507f28-ac87-43a0-a6c2-fe6f267b0561"}}, {"head": {"id": "8a507f28-ac87-43a0-a6c2-fe6f267b0561", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148623137900, "endTime": 31148674445700}, "additional": {"logType": "info", "children": ["163dd6d8-b4a2-4e95-98a6-69e2f8c9fe90", "7ffa531e-0533-4c8a-8675-0b932752bb36"], "durationId": "659cb5a6-1342-41c8-b67e-48e0a17812d7", "parent": "603e5040-5c47-41cb-9afd-41e00f9fca64"}}, {"head": {"id": "7c722c3e-3524-4a55-a14b-c1a69752851a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148677285300, "endTime": 31148677305800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa5e9427-4bd6-4c8f-a318-1009773c79d7", "logId": "83d5494d-66af-447e-9bc7-0d6e61c516a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83d5494d-66af-447e-9bc7-0d6e61c516a9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148677285300, "endTime": 31148677305800}, "additional": {"logType": "info", "children": [], "durationId": "7c722c3e-3524-4a55-a14b-c1a69752851a", "parent": "603e5040-5c47-41cb-9afd-41e00f9fca64"}}, {"head": {"id": "603e5040-5c47-41cb-9afd-41e00f9fca64", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148573271200, "endTime": 31148677338400}, "additional": {"logType": "info", "children": ["38e5dcd2-7b57-4441-9c16-40105f4b0d15", "8a507f28-ac87-43a0-a6c2-fe6f267b0561", "83d5494d-66af-447e-9bc7-0d6e61c516a9"], "durationId": "aa5e9427-4bd6-4c8f-a318-1009773c79d7", "parent": "4416e603-8d75-428a-a24b-345880c2b41f"}}, {"head": {"id": "4416e603-8d75-428a-a24b-345880c2b41f", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148571391900, "endTime": 31148677351300}, "additional": {"logType": "info", "children": ["603e5040-5c47-41cb-9afd-41e00f9fca64"], "durationId": "a450d9d1-1b94-4d82-a5db-b94f024986a3", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "57a450b6-a5cd-4e82-a4e6-c6263cdadcc3", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148716181900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea45afab-b21d-4420-a32b-8d304751455e", "name": "hvigorfile, resolve hvigorfile dependencies in 149 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148825525200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80020c9b-c5f8-427a-9be7-dad7df49e273", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148677360400, "endTime": 31148825735900}, "additional": {"logType": "info", "children": [], "durationId": "8483bee6-da2a-4400-906a-d917460e7cb7", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "6975687b-26b1-4a25-b0c4-4bbd39b555db", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148826436300, "endTime": 31148826747100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6aa4c172-978c-4173-82e4-8c6c083c8763", "logId": "a5322687-a4c3-4504-8771-cdfff0f02aae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "862d16de-afa9-482a-8629-2440198963b2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148826464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5322687-a4c3-4504-8771-cdfff0f02aae", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148826436300, "endTime": 31148826747100}, "additional": {"logType": "info", "children": [], "durationId": "6975687b-26b1-4a25-b0c4-4bbd39b555db", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "abb349de-f038-424c-a2ee-8ad4ae2c0406", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148828842100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8596dcfa-e8c1-45dd-880f-79d01a91dd04", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148858279600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8d740e-6203-48a0-a126-91bd59ee116d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148826763900, "endTime": 31148859156700}, "additional": {"logType": "info", "children": [], "durationId": "4af3134e-1a8c-4f3e-91ee-60751c9e27eb", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "503f1693-a7c3-44fb-8dcb-7e0392c3d490", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148862787700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89fc1f28-abbf-406f-920c-c749921630cd", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148862909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31feb43f-4737-453e-9e2b-f5040d41709c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148864986400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28ae13b0-d4e5-40d1-9770-e2d067f9dcea", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148865124500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaae3a0d-1220-4b57-b8cb-d7b04824fc5f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148859182800, "endTime": 31148870184100}, "additional": {"logType": "info", "children": [], "durationId": "0ed67e07-dfe1-41ad-8886-ddc4471ed1f4", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "b096ab96-87ba-4179-ab30-cf93a28e3c5b", "name": "Configuration phase cost:501 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148870230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49bd6095-0da1-4175-8f26-86d748d81910", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148870206000, "endTime": 31148870505400}, "additional": {"logType": "info", "children": [], "durationId": "561f3ccd-ebc0-459b-9856-988026ca2a77", "parent": "3c00687d-0a13-4ea4-b40a-f74da37f3465"}}, {"head": {"id": "3c00687d-0a13-4ea4-b40a-f74da37f3465", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148363919000, "endTime": 31148870522700}, "additional": {"logType": "info", "children": ["f67a0348-b1db-4a0d-9a6f-f340c1741548", "71a14e55-f618-4317-b0cf-7c31fe6dfd7e", "db1009cf-678e-4aff-a325-fdf646ccba1e", "4416e603-8d75-428a-a24b-345880c2b41f", "80020c9b-c5f8-427a-9be7-dad7df49e273", "8f8d740e-6203-48a0-a126-91bd59ee116d", "eaae3a0d-1220-4b57-b8cb-d7b04824fc5f", "49bd6095-0da1-4175-8f26-86d748d81910", "a5322687-a4c3-4504-8771-cdfff0f02aae"], "durationId": "6aa4c172-978c-4173-82e4-8c6c083c8763", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "1f6b85f1-89f4-46d8-96c9-8b883189a87b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148873627300, "endTime": 31148873651800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4bce6aee-d2e5-434c-af19-f5859f8e3c51", "logId": "078a6b3b-9d60-4355-81cf-474ddc95cd90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "078a6b3b-9d60-4355-81cf-474ddc95cd90", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148873627300, "endTime": 31148873651800}, "additional": {"logType": "info", "children": [], "durationId": "1f6b85f1-89f4-46d8-96c9-8b883189a87b", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "b286d0a5-0b1c-45e9-a0a7-9bb0aef2e3a6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148870587000, "endTime": 31148873671500}, "additional": {"logType": "info", "children": [], "durationId": "72d83f98-aa71-464f-97de-aaf70a6a107a", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "3cd28ba7-843f-435b-bbc0-24255529bcb3", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148873676800, "endTime": 31148873678200}, "additional": {"logType": "info", "children": [], "durationId": "52bc7ea4-3a27-44ed-a1be-164c095ec04b", "parent": "93d4c979-8fe4-4935-b586-cff9d4a37f67"}}, {"head": {"id": "93d4c979-8fe4-4935-b586-cff9d4a37f67", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148350620800, "endTime": 31148873680800}, "additional": {"logType": "info", "children": ["c5fbfe96-abbe-4026-9b80-83b5c<PERSON>be48", "3c00687d-0a13-4ea4-b40a-f74da37f3465", "b286d0a5-0b1c-45e9-a0a7-9bb0aef2e3a6", "3cd28ba7-843f-435b-bbc0-24255529bcb3", "e19d0a9c-839c-4d17-bb88-743f7efa1e43", "84101103-9d10-4f7b-bb52-e0b55b0bbbe5", "078a6b3b-9d60-4355-81cf-474ddc95cd90"], "durationId": "4bce6aee-d2e5-434c-af19-f5859f8e3c51"}}, {"head": {"id": "f993129e-1e46-4d13-be3c-2f2fe795cfb2", "name": "Configuration task cost before running: 529 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148873928500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "083afcdd-a063-48db-ae47-a4889a476766", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148880078600, "endTime": 31148899482200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "96e3a436-350d-4efa-ad98-90e30d2a8817", "logId": "83c9113d-2d7a-4504-930d-dd54a2e7358e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96e3a436-350d-4efa-ad98-90e30d2a8817", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148876027500}, "additional": {"logType": "detail", "children": [], "durationId": "083afcdd-a063-48db-ae47-a4889a476766"}}, {"head": {"id": "3ffe88e8-8fd6-45ea-b63f-76d37100c102", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148876705400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b99eea-73e0-4484-9353-26f1e84bfbed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148876864200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa59c06-2fe2-4e68-b638-e81d2d73f063", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148880091100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b156c0-0e7b-4fc1-884d-bde7749fe30c", "name": "Incremental task entry:default@PreBuild pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148899250500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "775fedd9-9d81-41f3-a35a-e83df8c880d5", "name": "entry : default@PreBuild cost memory 0.31343841552734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148899409400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83c9113d-2d7a-4504-930d-dd54a2e7358e", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148880078600, "endTime": 31148899482200}, "additional": {"logType": "info", "children": [], "durationId": "083afcdd-a063-48db-ae47-a4889a476766"}}, {"head": {"id": "d2aa5439-a9c9-47fb-99ac-1e8f15df6409", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148909259000, "endTime": 31148912265800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9e436474-9099-4daf-8ff2-80126121757a", "logId": "9de9e1cd-1fde-4d22-8403-1ff6cd610b3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e436474-9099-4daf-8ff2-80126121757a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148906787200}, "additional": {"logType": "detail", "children": [], "durationId": "d2aa5439-a9c9-47fb-99ac-1e8f15df6409"}}, {"head": {"id": "9f0d0806-4b58-4607-97dc-970898ee26fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148907591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116b9e3b-5b44-46be-b6b8-4f32db06e65e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148907786600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b915c0c1-d4b6-4c7b-951f-9a07b79099b6", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148909275000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d4cb40-d49a-4226-a35d-bdd68416294c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148910605600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26c04da5-7fa3-408e-8749-c000d192f7ad", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148911927900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2183074f-bd74-45aa-9652-ff73d32e85f0", "name": "entry : default@GenerateMetadata cost memory 0.0951995849609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148912068100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9de9e1cd-1fde-4d22-8403-1ff6cd610b3a", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148909259000, "endTime": 31148912265800}, "additional": {"logType": "info", "children": [], "durationId": "d2aa5439-a9c9-47fb-99ac-1e8f15df6409"}}, {"head": {"id": "0d30b11d-a27f-47f3-ab18-24ff16fab25e", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915320900, "endTime": 31148915740500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "82ecea73-7222-4fa6-bbbe-fb1176cb153b", "logId": "2e2cabe2-679e-495a-b380-ec8b4cd768ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82ecea73-7222-4fa6-bbbe-fb1176cb153b", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148914424000}, "additional": {"logType": "detail", "children": [], "durationId": "0d30b11d-a27f-47f3-ab18-24ff16fab25e"}}, {"head": {"id": "f1e1cbc5-f7e7-4567-9781-a2122695a941", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915018900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a43884a-fe66-4914-ba75-8c9b259be41c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915158500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83451858-e3b3-482f-a376-150de50c65a5", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915328600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5c91d9-0d4b-47d2-b4f7-de9c7171e68f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915423600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dedd4d5-8589-4006-913c-4c4a7c71101d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6932aa99-b720-41f3-8ec3-86d4028<PERSON>ee", "name": "entry : default@ConfigureCmake cost memory 0.03604888916015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e343d59e-7160-4150-ad5a-e87924eab75c", "name": "runTaskFromQueue task cost before running: 570 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915664300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2cabe2-679e-495a-b380-ec8b4cd768ff", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148915320900, "endTime": 31148915740500, "totalTime": 276500}, "additional": {"logType": "info", "children": [], "durationId": "0d30b11d-a27f-47f3-ab18-24ff16fab25e"}}, {"head": {"id": "f4051432-9875-433c-abbb-34c4c52de839", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148920907900, "endTime": 31148924917400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "18a75ed4-8fb2-473a-a5b2-93fad436d7ae", "logId": "ccfa02fa-6bd0-423e-9430-eb7ac244ce9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18a75ed4-8fb2-473a-a5b2-93fad436d7ae", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148918405600}, "additional": {"logType": "detail", "children": [], "durationId": "f4051432-9875-433c-abbb-34c4c52de839"}}, {"head": {"id": "33513b22-0861-406c-bcb6-a291277fd80e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148919058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f56ac9-1200-49f9-b7df-667c86e0f8a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148919512800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63437f19-3652-4549-bfef-52682cdece6c", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148920923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d712aea-2a10-4e76-9bee-8fbb0c6b0680", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148924703500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6864cfb-ab5d-41ef-bab5-3f62108f184b", "name": "entry : default@MergeProfile cost memory 0.10651397705078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148924846900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfa02fa-6bd0-423e-9430-eb7ac244ce9d", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148920907900, "endTime": 31148924917400}, "additional": {"logType": "info", "children": [], "durationId": "f4051432-9875-433c-abbb-34c4c52de839"}}, {"head": {"id": "a980d8ba-6315-4251-98ec-4dad3914c7e0", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148929092500, "endTime": 31148932097000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "669f3618-5648-420a-a178-4209c5102b25", "logId": "8c758204-c569-431d-9afb-fa0c299ecb49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "669f3618-5648-420a-a178-4209c5102b25", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148927234800}, "additional": {"logType": "detail", "children": [], "durationId": "a980d8ba-6315-4251-98ec-4dad3914c7e0"}}, {"head": {"id": "c530e483-861f-40de-a474-2f93c5ad55be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148928083900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "034a3d51-0c2b-47f1-847b-6f2d21b6d0da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148928241000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8745afbd-496b-4ba8-9ded-8e1d89746a0f", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148929110000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e99f92c-fa08-4a0a-b555-f8c22713c0e1", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148930270800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e32b5ad-e20d-41e3-8ae1-27ca113d346d", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148931737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460c3dfb-81af-484e-a8c9-27c09b833c69", "name": "entry : default@CreateBuildProfile cost memory 0.10443878173828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148931996400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c758204-c569-431d-9afb-fa0c299ecb49", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148929092500, "endTime": 31148932097000}, "additional": {"logType": "info", "children": [], "durationId": "a980d8ba-6315-4251-98ec-4dad3914c7e0"}}, {"head": {"id": "0aba6ed7-204c-4f94-a229-2ff70a6a2c72", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938583500, "endTime": 31148938931100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6c38292d-c5cc-4fb9-bad1-555f70ca2f92", "logId": "a0d8d094-0632-4267-ac0b-429b8d20a175"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c38292d-c5cc-4fb9-bad1-555f70ca2f92", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148937420900}, "additional": {"logType": "detail", "children": [], "durationId": "0aba6ed7-204c-4f94-a229-2ff70a6a2c72"}}, {"head": {"id": "073895aa-5adb-47b3-94e4-6856caef0c10", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148937856500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c9dd98a-28bc-4b7a-8177-754c942b1e41", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148937966500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "375403e0-cc33-4796-afc9-76d2fc1a4154", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed899f4-4bd6-498d-b4dc-55aab4f29ad8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938699200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d2becfd-26cd-4fde-868d-3a0ad4a1a400", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b9da10b-1e20-455a-b998-f2675fb9aec1", "name": "entry : default@PreCheckSyscap cost memory 0.03630828857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938812100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "984ed6b5-c706-4309-a885-e8d8ae922389", "name": "runTaskFromQueue task cost before running: 594 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938883000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d8d094-0632-4267-ac0b-429b8d20a175", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148938583500, "endTime": 31148938931100, "totalTime": 282400}, "additional": {"logType": "info", "children": [], "durationId": "0aba6ed7-204c-4f94-a229-2ff70a6a2c72"}}, {"head": {"id": "2827d580-2a23-4bbe-9f2d-445e7c55a987", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148944351100, "endTime": 31148944996900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "850978bc-86da-499f-9750-460df49fb120", "logId": "2f0d4254-795b-44d1-a029-7f156ebe3942"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "850978bc-86da-499f-9750-460df49fb120", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148940364000}, "additional": {"logType": "detail", "children": [], "durationId": "2827d580-2a23-4bbe-9f2d-445e7c55a987"}}, {"head": {"id": "abfbb435-ce49-4e93-aa66-01a6672537c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148940694600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52bbb525-0a8a-4fe4-875f-a47834980a03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148940790500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d93a391-e1f1-47bf-9795-724da4bdf45d", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148944365500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d9f92f5-19ac-474b-ba0f-ee1b23bf913f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148944607400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976cd040-c22d-4fc6-9088-bf56e018c735", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03878021240234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148944852000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7555cf-a8e7-4240-8e54-2025d3c2afc9", "name": "runTaskFromQueue task cost before running: 600 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148944939600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0d4254-795b-44d1-a029-7f156ebe3942", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148944351100, "endTime": 31148944996900, "totalTime": 572100}, "additional": {"logType": "info", "children": [], "durationId": "2827d580-2a23-4bbe-9f2d-445e7c55a987"}}, {"head": {"id": "f711b7c4-dd47-4f41-83a0-07a90d14895e", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148949028200, "endTime": 31148950807800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "074ea298-c47d-4f49-b342-b33eeb609836", "logId": "5ad727d9-e58d-41b2-8986-e16f33ebaa45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "074ea298-c47d-4f49-b342-b33eeb609836", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148946769700}, "additional": {"logType": "detail", "children": [], "durationId": "f711b7c4-dd47-4f41-83a0-07a90d14895e"}}, {"head": {"id": "f6578402-9538-44f5-95b0-a38d1a9a9418", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148947234000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a2e86a0-0b2e-4e06-8f82-5cf5c23f72c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148947341300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364f73a8-2a99-4045-92ba-58516d3ae2fe", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148949037700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2465fe-b426-4ed1-8a97-8da92bc29e12", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148950224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a89d41-0a93-486b-9e91-419ed0be0b91", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148950321300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8c6368-6b3c-4a85-92ac-386bb84acc31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148950395000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "587be2f0-7bc0-48fc-afaf-49431458fa38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148950440000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64d7354-b8d2-462a-ad3e-dea9f7d0dd06", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11688232421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148950512700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "828964cc-516b-46cf-9759-762bf13d85c4", "name": "runTaskFromQueue task cost before running: 605 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148950572200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad727d9-e58d-41b2-8986-e16f33ebaa45", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148949028200, "endTime": 31148950807800, "totalTime": 1533500}, "additional": {"logType": "info", "children": [], "durationId": "f711b7c4-dd47-4f41-83a0-07a90d14895e"}}, {"head": {"id": "27fe4183-5856-4a21-b1c4-048fbe0529ce", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954244000, "endTime": 31148954611500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c0668107-2792-4a12-bf11-e09a1f957c2d", "logId": "dacbcaf9-1c66-4b20-a740-ad1179ade543"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0668107-2792-4a12-bf11-e09a1f957c2d", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148953138900}, "additional": {"logType": "detail", "children": [], "durationId": "27fe4183-5856-4a21-b1c4-048fbe0529ce"}}, {"head": {"id": "c8f51e99-eb69-4e3a-a685-59d4760388be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148953496500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a7cd228-3be1-4e22-a152-8b54338efae3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148953597300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de3588d-b00c-448a-866b-355c47a4c53d", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd7598f0-35fd-44a8-a858-03c6f0d20332", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f819c56-6921-42f8-b2ed-99f574a51ceb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954430500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80efd1b6-78fd-42eb-a9cc-603e1c9b148a", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954491200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240dd907-c2c4-41a2-a668-e65d9f21c664", "name": "runTaskFromQueue task cost before running: 609 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954560200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dacbcaf9-1c66-4b20-a740-ad1179ade543", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148954244000, "endTime": 31148954611500, "totalTime": 300000}, "additional": {"logType": "info", "children": [], "durationId": "27fe4183-5856-4a21-b1c4-048fbe0529ce"}}, {"head": {"id": "4d33cb09-5f0e-4c83-8137-fd560e747a0b", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148957216300, "endTime": 31148959714300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "42fc1544-9406-484a-8260-bf43e7961d33", "logId": "49eae2a1-d9e5-4385-84a6-b3244119a7e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42fc1544-9406-484a-8260-bf43e7961d33", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148956037200}, "additional": {"logType": "detail", "children": [], "durationId": "4d33cb09-5f0e-4c83-8137-fd560e747a0b"}}, {"head": {"id": "077aaa7b-2995-412d-acd4-4a10292d3c75", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148956484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9e4a9a-677f-4f69-b28e-93a1a18b98c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148956585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b626a6-7e74-40c4-9bbd-842b019a0e8e", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148957223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59bf69b0-cb2e-4eb9-a6a9-725d6f650d72", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148959555800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b9c1b4-120e-44ce-a774-e79e9cca4c6d", "name": "entry : default@MakePackInfo cost memory 0.1392822265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148959652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49eae2a1-d9e5-4385-84a6-b3244119a7e0", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148957216300, "endTime": 31148959714300}, "additional": {"logType": "info", "children": [], "durationId": "4d33cb09-5f0e-4c83-8137-fd560e747a0b"}}, {"head": {"id": "df58d126-9a54-451a-a34a-7d048e7697e6", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148962714000, "endTime": 31148965032200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "24e62ab3-38ea-4c51-b256-a6b66f8b74f5", "logId": "f84a962f-3ae6-4843-af5d-8e27051fa8e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24e62ab3-38ea-4c51-b256-a6b66f8b74f5", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148961352900}, "additional": {"logType": "detail", "children": [], "durationId": "df58d126-9a54-451a-a34a-7d048e7697e6"}}, {"head": {"id": "561a5563-91f0-4b72-9b21-b3a780a953e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148961689800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f85207-77f8-4ac0-97c5-a820b210710e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148961778000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c72ae618-6294-41c8-8fc8-03afa3bab73a", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148962721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71581316-34c4-4302-b370-f918f63a883c", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148962841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55949fa9-e3cb-4745-8e75-4c3a93ec65aa", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148963365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c7aa93-b7e6-4765-bccb-45fd6f03d3fe", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148964527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e8e833-22bc-4750-9d7c-0279b2684500", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148964645600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9123c95d-b23e-4270-b3bd-6c2e719e2b06", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148964765000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57909e41-ea3e-4d88-b725-5227fa81a143", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148964843700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9160395b-3ef0-49e7-a3f2-329610cff235", "name": "entry : default@SyscapTransform cost memory 0.15236663818359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148964914000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c185927a-27c4-4ac1-a797-6a133602f1eb", "name": "runTaskFromQueue task cost before running: 620 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148964983000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84a962f-3ae6-4843-af5d-8e27051fa8e4", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148962714000, "endTime": 31148965032200, "totalTime": 2254400}, "additional": {"logType": "info", "children": [], "durationId": "df58d126-9a54-451a-a34a-7d048e7697e6"}}, {"head": {"id": "6c985097-bbd3-4939-814c-8cce2446c29d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148968597600, "endTime": 31148969875800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a71473dc-6847-47f5-a336-12a124886a7c", "logId": "90460bd3-9a01-4b18-b4f4-16bb1a419266"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a71473dc-6847-47f5-a336-12a124886a7c", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148966504600}, "additional": {"logType": "detail", "children": [], "durationId": "6c985097-bbd3-4939-814c-8cce2446c29d"}}, {"head": {"id": "4d9a92eb-905b-4440-bf6d-f06a92682aca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148966968500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed66d212-b3a4-45d9-bdd3-dbb53fc15291", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148967066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01099f4-6456-4fe8-b143-fe0d0e5a4651", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148968614000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318f5ec3-141c-4787-9d0e-65858252d9a5", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148969496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc520eb-8770-46a5-b80c-9b3f1579a5d2", "name": "entry : default@ProcessProfile cost memory 0.05995941162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148969753400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90460bd3-9a01-4b18-b4f4-16bb1a419266", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148968597600, "endTime": 31148969875800}, "additional": {"logType": "info", "children": [], "durationId": "6c985097-bbd3-4939-814c-8cce2446c29d"}}, {"head": {"id": "19f2e83f-ded7-4258-9251-5cf32f60bf23", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148973035700, "endTime": 31148976841800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "956af9d8-20eb-4dbe-a281-2bea55fb30d8", "logId": "845c9bdb-bb56-44a3-bf70-5a8aaf08bf5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "956af9d8-20eb-4dbe-a281-2bea55fb30d8", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148971373600}, "additional": {"logType": "detail", "children": [], "durationId": "19f2e83f-ded7-4258-9251-5cf32f60bf23"}}, {"head": {"id": "9fdb42d4-2db1-41de-9e43-cca3ab0975b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148971709100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e63f1ee-dbaf-4a4b-924a-b5fdc4113b0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148971799200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92152fd1-86da-4556-a91f-31c6d43d35e9", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148973044200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a66fd1-94f0-4c9e-beac-78d97f8140e9", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148976657100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1198ca34-25ef-461a-99c0-e143cfe4fca6", "name": "entry : default@ProcessRouterMap cost memory 0.202911376953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148976779000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "845c9bdb-bb56-44a3-bf70-5a8aaf08bf5d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148973035700, "endTime": 31148976841800}, "additional": {"logType": "info", "children": [], "durationId": "19f2e83f-ded7-4258-9251-5cf32f60bf23"}}, {"head": {"id": "19c61f6f-f433-441e-9af5-073c33074aac", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980153500, "endTime": 31148980990700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "41e75378-a923-486a-9da9-2bb6f77a559e", "logId": "11cb7f79-75e9-4e4e-8eb1-c49a281a03e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41e75378-a923-486a-9da9-2bb6f77a559e", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148979092900}, "additional": {"logType": "detail", "children": [], "durationId": "19c61f6f-f433-441e-9af5-073c33074aac"}}, {"head": {"id": "525b1c96-99b3-44bd-9c3e-7182174fca61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148979474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "946ce474-7d15-4cb4-8402-dfde49632103", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148979566300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a972fe13-2dfc-4593-963e-50030916b1e8", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980162400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca023551-9835-4220-835c-3f6c5a5057c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980265400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e2b02e-fc39-412f-9cbb-a372a078eb71", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980319000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9fe558-3cd3-4421-9a27-324fb21546ea", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980846300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ea30fb-dd88-499a-9010-c1c1c8752f9e", "name": "runTaskFromQueue task cost before running: 636 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11cb7f79-75e9-4e4e-8eb1-c49a281a03e5", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148980153500, "endTime": 31148980990700, "totalTime": 763000}, "additional": {"logType": "info", "children": [], "durationId": "19c61f6f-f433-441e-9af5-073c33074aac"}}, {"head": {"id": "9bd4a51b-b54f-4359-bc8f-b3a0d402ce2a", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148986056900, "endTime": 31148990869900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4d43496a-ff59-4c2f-9562-215b588be155", "logId": "c67531b2-8853-462e-a44b-6eef76ccaaee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d43496a-ff59-4c2f-9562-215b588be155", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148983198000}, "additional": {"logType": "detail", "children": [], "durationId": "9bd4a51b-b54f-4359-bc8f-b3a0d402ce2a"}}, {"head": {"id": "b2e3e88f-4758-4ab6-88fe-d39c0fe7f15b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148983521200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395a1681-e77d-4dac-bdc2-7813c509b043", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148983609600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd45409-cc70-4047-a0fd-31d9de180ba4", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148984894800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dffe881-cee5-4646-97fb-614581c74329", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148987582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5cdaa1e-a969-4eb7-8244-1d7bb8772005", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148989473300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d256fc-bdae-4e5c-b1dd-6a497a7958a3", "name": "entry : default@ProcessResource cost memory 0.1697540283203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148989580200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c67531b2-8853-462e-a44b-6eef76ccaaee", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148986056900, "endTime": 31148990869900}, "additional": {"logType": "info", "children": [], "durationId": "9bd4a51b-b54f-4359-bc8f-b3a0d402ce2a"}}, {"head": {"id": "1775c303-5840-45f4-b841-147d742f067c", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148996902000, "endTime": 31149010330900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "733e591c-cef3-4feb-93fe-4ae79ea52c4a", "logId": "27dd7e4f-bc6f-4612-a687-e8b9a97f9ef3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "733e591c-cef3-4feb-93fe-4ae79ea52c4a", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148993869900}, "additional": {"logType": "detail", "children": [], "durationId": "1775c303-5840-45f4-b841-147d742f067c"}}, {"head": {"id": "c7821895-5484-45bc-ae6f-4d9ec6f5716b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148994247000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf2a49b-6ab6-4aaa-ae95-102c7439126b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148994335000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33bcc7f9-271a-451d-95b1-e7d6b7aab9c5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148996911500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49929b1b-5cdd-45ef-8e20-a161ec947ca4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149010121700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16a096d-c767-4d98-bfd6-74db0b838795", "name": "entry : default@GenerateLoaderJson cost memory 0.7697219848632812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149010261600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27dd7e4f-bc6f-4612-a687-e8b9a97f9ef3", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148996902000, "endTime": 31149010330900}, "additional": {"logType": "info", "children": [], "durationId": "1775c303-5840-45f4-b841-147d742f067c"}}, {"head": {"id": "e04b4126-c4b7-4be6-92f8-2190111a8f96", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149019077900, "endTime": 31149021905700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5523cc27-0277-4506-9714-c282b30a153b", "logId": "7cbbd097-4ced-4989-957e-3a0e299cfe15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5523cc27-0277-4506-9714-c282b30a153b", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149017573600}, "additional": {"logType": "detail", "children": [], "durationId": "e04b4126-c4b7-4be6-92f8-2190111a8f96"}}, {"head": {"id": "7d3a130c-3a74-4c5c-9bc7-c9d4176f9e87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149018141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94dd5a39-84e0-4a75-a041-f36c46<PERSON><PERSON>d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149018287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "003bb36d-3d69-4bc2-94da-3737f8910a56", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149019090100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a424b9e-30b4-40fd-b35b-47dadcda137c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149020970800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d72b6715-c7de-4892-99e4-9c5ae90a03e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149021078400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a32e31a-3eb8-4c4e-bd9d-5d46c87dd579", "name": "entry : default@ProcessLibs cost memory 0.1257171630859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149021720900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2afc769-dc19-4665-9108-4cc0d06c3ba4", "name": "runTaskFromQueue task cost before running: 676 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149021845200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cbbd097-4ced-4989-957e-3a0e299cfe15", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149019077900, "endTime": 31149021905700, "totalTime": 2744100}, "additional": {"logType": "info", "children": [], "durationId": "e04b4126-c4b7-4be6-92f8-2190111a8f96"}}, {"head": {"id": "1cc35747-4994-4a7f-b331-5e258e39f410", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149026885900, "endTime": 31149046019200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3d674afd-c9ef-4a50-b495-a2eb27e9fa14", "logId": "f9fbfefe-77c1-43aa-80ab-23a2848d468f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d674afd-c9ef-4a50-b495-a2eb27e9fa14", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149023774400}, "additional": {"logType": "detail", "children": [], "durationId": "1cc35747-4994-4a7f-b331-5e258e39f410"}}, {"head": {"id": "643a6e6e-e0c6-4f02-80ae-6a55323b12bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149024089600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60c4795-4fcc-4c93-ad5d-b6b68ae7977d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149024187300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74804508-a2d7-4dfe-99df-48c89b1b8418", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149024830400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68bd2803-4054-452c-a808-7ee36e199582", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149026908800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a1e1f44-7d3b-4371-b136-21c946da066a", "name": "Incremental task entry:default@CompileResource pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149045816000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bf4edd8-222c-41be-8217-4052d23d56f8", "name": "entry : default@CompileResource cost memory 1.4083709716796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149045938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fbfefe-77c1-43aa-80ab-23a2848d468f", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149026885900, "endTime": 31149046019200}, "additional": {"logType": "info", "children": [], "durationId": "1cc35747-4994-4a7f-b331-5e258e39f410"}}, {"head": {"id": "2d005ece-2092-470a-af6f-f6da8243ab3d", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149050810200, "endTime": 31149052150900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "486a97b5-caaf-4f79-be1b-0235489a956b", "logId": "56fbbf58-3161-4725-9e36-4089a008fa55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "486a97b5-caaf-4f79-be1b-0235489a956b", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149048389000}, "additional": {"logType": "detail", "children": [], "durationId": "2d005ece-2092-470a-af6f-f6da8243ab3d"}}, {"head": {"id": "8a9f8e5e-dc07-4cc8-805e-4c5f3f630293", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149048719300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6586ce1-c941-4db9-8515-6d090b49d39b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149048802300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aacc43a-642f-4725-be22-7cf45a3c143c", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149050824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7737f0-2ca7-4755-93ba-1aeba132df17", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149051160100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c51ec8-088c-47d4-90fa-eccb83dfa00a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149051989200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d36a7c7-ecd5-49bc-a8fd-4ece82220253", "name": "entry : default@DoNativeStrip cost memory 0.0851593017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149052074200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56fbbf58-3161-4725-9e36-4089a008fa55", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149050810200, "endTime": 31149052150900}, "additional": {"logType": "info", "children": [], "durationId": "2d005ece-2092-470a-af6f-f6da8243ab3d"}}, {"head": {"id": "4636f6c0-2874-47ac-a0a7-d5d5475e51aa", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149057240700, "endTime": 31149069454700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "dc2048fa-0e27-4fd0-9cbe-2a1faa603c8a", "logId": "113e7097-c62c-4d7f-9dab-b66421cb6278"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc2048fa-0e27-4fd0-9cbe-2a1faa603c8a", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149053492400}, "additional": {"logType": "detail", "children": [], "durationId": "4636f6c0-2874-47ac-a0a7-d5d5475e51aa"}}, {"head": {"id": "e1675984-d871-410a-ad0b-628881785724", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149053812900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "143ab2ef-b86f-4cbe-a9ed-0603258e2936", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149053895400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf044ed8-5668-4944-a090-cdb54fe822df", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149057250800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f723bd4-753e-4618-8cb2-b412b6b13584", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149069260100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b274b545-e65e-4a2b-95bc-3fdf66864e08", "name": "entry : default@CompileArkTS cost memory 0.7406997680664062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149069387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113e7097-c62c-4d7f-9dab-b66421cb6278", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149057240700, "endTime": 31149069454700}, "additional": {"logType": "info", "children": [], "durationId": "4636f6c0-2874-47ac-a0a7-d5d5475e51aa"}}, {"head": {"id": "02a25ed2-c80d-43c3-a4ef-da750ae98be5", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149076493700, "endTime": 31149079016900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "d0034425-0c0e-45c1-b597-e83baa50b02d", "logId": "888dd7c0-a1a9-44a3-be02-251fea50bf2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0034425-0c0e-45c1-b597-e83baa50b02d", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149072638600}, "additional": {"logType": "detail", "children": [], "durationId": "02a25ed2-c80d-43c3-a4ef-da750ae98be5"}}, {"head": {"id": "15d702cf-2e5f-4052-97b4-31c5bcb25adc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149072996900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a396d0a-efe8-4f9c-a2f1-eac5a5d6aa95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149073092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7082a643-6c61-4a1c-b540-d482ba0521fe", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149076504000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474e4689-830b-4b91-b3b7-9134def291ae", "name": "entry : default@BuildJS cost memory 0.12808990478515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149078857200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73cf592a-510d-4932-b436-3611445ac864", "name": "runTaskFromQueue task cost before running: 734 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149078963100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "888dd7c0-a1a9-44a3-be02-251fea50bf2d", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149076493700, "endTime": 31149079016900, "totalTime": 2453300}, "additional": {"logType": "info", "children": [], "durationId": "02a25ed2-c80d-43c3-a4ef-da750ae98be5"}}, {"head": {"id": "479c22ec-10fd-4772-9c52-ec3acfdc4085", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149083187000, "endTime": 31149085426500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "63f04c9f-7789-4c7d-8d8c-9910b1886591", "logId": "c35ab179-50bb-4843-a5da-82c9e60b2bcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63f04c9f-7789-4c7d-8d8c-9910b1886591", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149080440600}, "additional": {"logType": "detail", "children": [], "durationId": "479c22ec-10fd-4772-9c52-ec3acfdc4085"}}, {"head": {"id": "84a4f957-c339-487f-a92d-c9ce1e47d9a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149080781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d40044-cb8e-402f-be4f-f1b92b2021a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149080871700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51d2bf5-2946-4280-82e6-3d91456f9870", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149083197200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d07c1f0-8567-4004-acf2-5b9b333bf51f", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149083482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d791fa55-d936-460c-84ba-c99f06be20ca", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149085210300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d547ccae-6932-4599-a35d-32eabcdf2425", "name": "entry : default@CacheNativeLibs cost memory 0.09110260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149085353400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c35ab179-50bb-4843-a5da-82c9e60b2bcb", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149083187000, "endTime": 31149085426500}, "additional": {"logType": "info", "children": [], "durationId": "479c22ec-10fd-4772-9c52-ec3acfdc4085"}}, {"head": {"id": "5c16acd3-879d-4dc5-9e79-e85ac1ad6d83", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149089431400, "endTime": 31149090876700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "4241fca5-41da-4a20-bf8d-b5b7a7b0c2f1", "logId": "8f51c7a3-b093-487c-83b3-896e762db368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4241fca5-41da-4a20-bf8d-b5b7a7b0c2f1", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149087500800}, "additional": {"logType": "detail", "children": [], "durationId": "5c16acd3-879d-4dc5-9e79-e85ac1ad6d83"}}, {"head": {"id": "5359a9b1-2765-4664-b80c-df9ce81f8b56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149088085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19cbd774-419c-48a2-83fe-cdff63f78f25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149088195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d006311-ea2f-49cb-8da5-b40e72ad18d1", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149089440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e1e6e0-81b7-45d6-8510-873ef0fc509c", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149089666600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eb7f53f-06da-43ba-8a78-e69180b0dec5", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149090601000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e5c0a96-89f2-4e9a-a3f9-f826b4b85e62", "name": "entry : default@GeneratePkgModuleJson cost memory 0.072509765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149090800600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f51c7a3-b093-487c-83b3-896e762db368", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149089431400, "endTime": 31149090876700}, "additional": {"logType": "info", "children": [], "durationId": "5c16acd3-879d-4dc5-9e79-e85ac1ad6d83"}}, {"head": {"id": "05c7b26b-22c8-486e-b863-71b4f04aaccd", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149104848800, "endTime": 31149124204300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "65ee3e40-c3c4-4bce-850f-b5504630a197", "logId": "b5207eb6-12a7-4f47-8518-9f357efb7878"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65ee3e40-c3c4-4bce-850f-b5504630a197", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149093008400}, "additional": {"logType": "detail", "children": [], "durationId": "05c7b26b-22c8-486e-b863-71b4f04aaccd"}}, {"head": {"id": "f1622dfa-095e-4c76-9b56-09bdb19a3aa6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149093516800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab3b1675-032a-4f7e-9a7f-21b0f2b5fcdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149093646500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0d8511-6a92-4303-b200-492c937eaa93", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149104860900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "312ffed5-29f9-48d9-aff5-5faa7daa4d01", "name": "Incremental task entry:default@PackageHap pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149123927900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37268cd6-f1db-4e40-90f6-4e247b144418", "name": "entry : default@PackageHap cost memory 0.8403701782226562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149124086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5207eb6-12a7-4f47-8518-9f357efb7878", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149104848800, "endTime": 31149124204300}, "additional": {"logType": "info", "children": [], "durationId": "05c7b26b-22c8-486e-b863-71b4f04aaccd"}}, {"head": {"id": "8fad1b56-f88f-4891-a68c-a2db9b96b33c", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149132066200, "endTime": 31149139433200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "edaea274-19f2-496b-8dfe-67a427415080", "logId": "0fe1f353-6397-488f-8c8b-bc84b5f30dc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edaea274-19f2-496b-8dfe-67a427415080", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149129090800}, "additional": {"logType": "detail", "children": [], "durationId": "8fad1b56-f88f-4891-a68c-a2db9b96b33c"}}, {"head": {"id": "d6626f92-bffd-4468-8831-389d2ce1b430", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149129542400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717c38ef-9240-4a77-aa0b-30eebc88722a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149129772900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d95ca03-8ebe-4537-ad77-28c6b4162936", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149132081800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659882fb-fed6-4f87-b569-3038fdb33c66", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149132664700}, "additional": {"logType": "warn", "children": [], "durationId": "8fad1b56-f88f-4891-a68c-a2db9b96b33c"}}, {"head": {"id": "e5b1f2be-0ae9-4158-b62b-779355f01db5", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149138019500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ccd4b61-0732-493d-8a84-0bb9bdd0415f", "name": "Incremental task entry:default@SignHap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149138400000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59fe8730-0a92-49a2-b3ba-268f61a590c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149138518200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b66fef-aac3-4179-b085-ad1d90f51a88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149138575800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb599f3b-e123-4d3e-97ea-47e70544bb45", "name": "entry : default@SignHap cost memory 0.12005615234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149139213100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e6aef11-0ba6-4c63-8b0b-d791de63d88a", "name": "runTaskFromQueue task cost before running: 794 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149139361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fe1f353-6397-488f-8c8b-bc84b5f30dc7", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149132066200, "endTime": 31149139433200, "totalTime": 7271700}, "additional": {"logType": "info", "children": [], "durationId": "8fad1b56-f88f-4891-a68c-a2db9b96b33c"}}, {"head": {"id": "25a0aa2e-76de-4a3c-9281-bb7567c88b17", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149146092200, "endTime": 31149153759000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "038c367b-a574-415a-b883-eb08f8384b8e", "logId": "5a5d72de-be4c-441d-a717-4c6a88ce058c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "038c367b-a574-415a-b883-eb08f8384b8e", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149144517500}, "additional": {"logType": "detail", "children": [], "durationId": "25a0aa2e-76de-4a3c-9281-bb7567c88b17"}}, {"head": {"id": "94d20b88-687c-45ca-8166-ca9f242be61e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149145203000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6eed8f6-206e-4a5a-b023-38f937daea9b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149145315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "034fdacd-4840-4638-a740-7055a6721dd8", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149146208500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a5957f-8865-40fa-89d3-809469996398", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149153273500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e0e2359-c9cf-47e2-a4b9-d429cc1099dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149153433500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cbebd94-558c-49de-b690-9c761f18d9de", "name": "entry : default@CollectDebugSymbol cost memory 0.2401123046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149153543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c690b4d-573f-4e9d-a0ed-5febb8baad5a", "name": "runTaskFromQueue task cost before running: 808 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149153678100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a5d72de-be4c-441d-a717-4c6a88ce058c", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149146092200, "endTime": 31149153759000, "totalTime": 7557100}, "additional": {"logType": "info", "children": [], "durationId": "25a0aa2e-76de-4a3c-9281-bb7567c88b17"}}, {"head": {"id": "619c6b36-3bb1-4c11-acd5-1aa13113f68a", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149156770900, "endTime": 31149157128400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d3787042-edc7-458e-950c-e056cd5c342e", "logId": "44226e67-4a5f-4bb1-9504-617ff09b73dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3787042-edc7-458e-950c-e056cd5c342e", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149156712100}, "additional": {"logType": "detail", "children": [], "durationId": "619c6b36-3bb1-4c11-acd5-1aa13113f68a"}}, {"head": {"id": "3887dc9e-c5f9-4b7a-9221-14272904e498", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149156779500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac3d1a0-57d6-441c-9469-f1c79a2a1277", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149156945900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb99ebc3-1be3-4926-9dd8-278e9c805c60", "name": "runTaskFromQueue task cost before running: 812 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149157037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44226e67-4a5f-4bb1-9504-617ff09b73dd", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149156770900, "endTime": 31149157128400, "totalTime": 243500}, "additional": {"logType": "info", "children": [], "durationId": "619c6b36-3bb1-4c11-acd5-1aa13113f68a"}}, {"head": {"id": "4fecd8d5-db39-461f-b478-57f9ce248087", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149171512700, "endTime": 31149171540400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d325a749-8527-46bf-be1e-d72324121f74", "logId": "a891acd3-8dd0-45b1-b05b-81713685d3fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a891acd3-8dd0-45b1-b05b-81713685d3fa", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149171512700, "endTime": 31149171540400}, "additional": {"logType": "info", "children": [], "durationId": "4fecd8d5-db39-461f-b478-57f9ce248087"}}, {"head": {"id": "66079ed6-bf46-449e-a5b3-f46f403eab7c", "name": "BUILD SUCCESSFUL in 826 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149171584700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d0f32a78-2d16-49c2-9777-1783a312c1c1", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31148345829700, "endTime": 31149172903200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 8}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d5656ef4-4523-4cc8-b35f-4e331caf6f2f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149172959100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2347049c-cf57-47fd-8f0b-ce14dfe238d2", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149173746600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea109025-5a63-43a7-a598-7416a8a63b7a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149174084400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25ff6e32-b4cf-4e5f-bc1c-04f69e33bc2f", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149174240400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd066dd3-1516-4e1f-aa40-4ccb2aabd21c", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149174336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317c51a0-1a59-4084-9ca1-152b281fc998", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149174860800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13098c4d-a41b-4106-bc92-23b8bb1ab031", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149175846700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d9016f4-8159-42cb-8a61-e6c7984ccc70", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149176284300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8550a4f1-be21-4e14-8453-278633506d29", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149176380300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdbf93ca-c55e-4310-9a47-647a22b14e5f", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149176448900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd4272b-226d-493b-a387-190d39fe687a", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149177009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da13c8b2-f953-4180-a41d-bed1adb71ee2", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149179377600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec227eee-0879-472c-a813-a6f744a5f55a", "name": "Incremental task entry:default@SyscapTransform post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149179691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833de551-6999-4444-bed2-e701ae563759", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149179770100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dc6149d-f83d-4879-9783-2cb8dc9e9e14", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149179826600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e86bc1-5325-4b53-afb2-8443a8701239", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149179878200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c942e23-3eda-4de4-997c-6b3b99af4d92", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149179923100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c969f26-e12c-4865-8d17-ce0681fced65", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149180421700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566b4791-4fe8-4c5f-adb7-1a3619637608", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149180669100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ce6f161-4937-490c-a60f-2fa51d7a63e7", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149181101500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c1c614-cb5f-4f9d-bcb0-bddeac9e16a5", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149181444000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9884578-cd2d-4518-a472-692c31f42acb", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149181533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96409a4f-104a-41ca-b4f4-37d14c1557a0", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149181699700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ac0893-1d8e-489f-8480-058e5a9831c4", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149181806600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f454d2d-5155-4f02-a5ff-028525d56e31", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149183444600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423a8a4e-b399-46ea-8981-5565fa1773bb", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149184644800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349dc84a-5564-41be-94c7-50d6b9d8127d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149186528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacd98f2-9e8d-47b7-993d-93e92737bbc1", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149187049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a89a89-5d5b-46a1-924c-c4a4d3560b38", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149187301000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad8a011b-283e-4957-9899-0e6a6d9b4087", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149188362300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c56944-c0a4-409a-a9b4-5b28cb9ab8c4", "name": "Incremental task entry:default@BuildJS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149188668800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "517aaffd-6eb7-4d99-ab2e-0a52f83fe113", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149188744500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d57a2b8-40c6-49aa-ae0c-7fe4fc785da5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149188799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40b82b6-55ea-4fcc-baed-5846e64eb266", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149188876700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3802e5c8-14a9-4ff0-b185-f89cca388fec", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149189362200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6004ee29-198c-4907-9306-dbca386bd5ce", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149189699000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ca7ff72-d214-4fe8-b9de-e9c582c0017c", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149189974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3105f65a-6b2a-4eca-8ca4-22c0e8dd2507", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149192356100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c234bc7-bf88-49aa-8ca1-d14aabcd59ce", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149192733200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0509a67-4cc0-4750-96fd-274ba7569d62", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149193035700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45087e57-3eba-4c58-ad01-57b943e11cc1", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31149193260800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}