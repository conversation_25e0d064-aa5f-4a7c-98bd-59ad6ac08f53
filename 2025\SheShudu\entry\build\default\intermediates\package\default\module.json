{"app": {"bundleName": "com.atomicservice.6917576098804821135", "bundleType": "atomicService", "vendor": "example", "versionCode": 1000000, "versionName": "1.0.0", "icon": "$media:app_icon", "label": "$string:app_name", "apiReleaseType": "Release", "compileSdkVersion": "*********", "targetAPIVersion": 50002014, "minAPIVersion": 50000012, "compileSdkType": "HarmonyOS", "appEnvironments": [], "buildMode": "debug", "debug": true, "iconId": 16777217, "labelId": 16777216}, "module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet"], "deliveryWithInstall": true, "installationFree": true, "pages": "$profile:main_pages", "routerMap": "$profile:router_map", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:icon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}], "descriptionId": 16777218, "iconId": 16777223, "labelId": 16777219, "startWindowIconId": 16777224, "startWindowBackgroundId": 16777221}], "packageName": "entry", "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": [], "descriptionId": 16777220}}