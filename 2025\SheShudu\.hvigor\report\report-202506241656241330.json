{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "395a2022-0e61-435e-8b95-22d1f51c9a01", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342512365000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df4bad75-7920-4c4d-9b53-8e3d5b4d7351", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342517129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a056cc78-0f34-4916-a9e0-a3da3e77ec1c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342517507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d7900b-eaa4-430f-8b10-8e6f48ceda94", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342520309500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64acc39d-8726-45cc-999e-d9eda5fb36ce", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416026350900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "109d4550-f997-459a-a46f-f074158b3442", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416033888200, "endTime": 30416567942600}, "additional": {"children": ["e445b040-fca1-4af0-8f35-78ca33f14cc8", "8e9cbad0-15ae-4771-aeac-c7e51af90832", "fa2eb781-65d2-4f78-8a34-a42d5667d550", "23796aa7-52c5-4f29-8433-670cfbe3e4e6", "44f09298-87a3-4e1e-a795-da2faef11c27", "6a877d5e-828e-4996-b0ef-aa307ffa5afb", "d358b518-5bb0-43cf-92e5-075f6b3b0d55"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e445b040-fca1-4af0-8f35-78ca33f14cc8", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416033890000, "endTime": 30416050996100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "9acf300c-e1d1-4380-a234-39689e7f6dd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416051019900, "endTime": 30416566800100}, "additional": {"children": ["23c328e0-0733-4bbe-9399-4d08cdd7c0cc", "4a49a63b-959e-4af1-badc-9b9c1cdb649b", "cc5a7987-6308-4066-8890-7886f1fd83a4", "1fc629a5-0dd8-4e8b-a7bd-78c064828f7c", "9fc855bb-ab8a-46f4-bf2a-cc79128ead0e", "87c67dd2-840f-487a-8e01-98d0e5b0636a", "8b96366c-605e-451a-992b-81885a1fae36", "4329f992-dc81-4bad-bc5e-0a78e61d386a", "bd430d22-95a9-4bcc-8565-dea48836b6d3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "e737e273-ff11-4323-b1da-8ff46f433b03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa2eb781-65d2-4f78-8a34-a42d5667d550", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416566824900, "endTime": 30416567933600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "59ab798c-2e87-40a7-8cb1-9da2cbcbd97b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23796aa7-52c5-4f29-8433-670cfbe3e4e6", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416567937000, "endTime": 30416567937800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "4c1017ba-e108-446b-b420-b7b09b852dc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44f09298-87a3-4e1e-a795-da2faef11c27", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416037307400, "endTime": 30416037345100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "d5b98fc7-0898-478f-b779-e13940b997f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5b98fc7-0898-478f-b779-e13940b997f5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416037307400, "endTime": 30416037345100}, "additional": {"logType": "info", "children": [], "durationId": "44f09298-87a3-4e1e-a795-da2faef11c27", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "6a877d5e-828e-4996-b0ef-aa307ffa5afb", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416044030500, "endTime": 30416044049600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "418cebfc-068e-4bc3-bc73-cfd9d0b82096"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "418cebfc-068e-4bc3-bc73-cfd9d0b82096", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416044030500, "endTime": 30416044049600}, "additional": {"logType": "info", "children": [], "durationId": "6a877d5e-828e-4996-b0ef-aa307ffa5afb", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "89cf8cc7-9df9-4267-856a-6db7638c7db3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416044332300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656eb671-323f-42bf-8903-74d507cb0844", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416050751600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9acf300c-e1d1-4380-a234-39689e7f6dd2", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416033890000, "endTime": 30416050996100}, "additional": {"logType": "info", "children": [], "durationId": "e445b040-fca1-4af0-8f35-78ca33f14cc8", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "23c328e0-0733-4bbe-9399-4d08cdd7c0cc", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416059105600, "endTime": 30416059119400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "37bb4d19-ad64-44d6-a83e-427cdeb9a0ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a49a63b-959e-4af1-badc-9b9c1cdb649b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416059192100, "endTime": 30416064785300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "b6688a3e-5bcf-4037-a461-b995f6fd084d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc5a7987-6308-4066-8890-7886f1fd83a4", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416064804400, "endTime": 30416347047500}, "additional": {"children": ["7a41bd3b-12c3-44d6-b0e6-9087de75580d", "3ccb2fa6-927c-4a02-b1f9-6d9144fe6d62", "2b4168f7-173a-474b-be24-bfd80c3e0675"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "7a3a212a-4202-4870-b4f6-2d8eb7d17f31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fc629a5-0dd8-4e8b-a7bd-78c064828f7c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416347114100, "endTime": 30416399195700}, "additional": {"children": ["cbfe9166-b1c0-41c0-a7e8-af4481cd0551"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "e45b7598-92f4-4fbd-81c0-9c00b4baa2c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fc855bb-ab8a-46f4-bf2a-cc79128ead0e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416399274800, "endTime": 30416538346000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "edea5d24-7249-4347-a7b9-0c0bb17beb81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87c67dd2-840f-487a-8e01-98d0e5b0636a", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416539850500, "endTime": 30416554115000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "33a18d48-624b-4417-aa9a-9c5895013798"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b96366c-605e-451a-992b-81885a1fae36", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416554139200, "endTime": 30416566619700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "4662ffce-4408-49a9-96e5-c193b3fcf8c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4329f992-dc81-4bad-bc5e-0a78e61d386a", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416566652000, "endTime": 30416566786400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "d6b70b9f-923f-4c97-a921-6c90beda49ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37bb4d19-ad64-44d6-a83e-427cdeb9a0ce", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416059105600, "endTime": 30416059119400}, "additional": {"logType": "info", "children": [], "durationId": "23c328e0-0733-4bbe-9399-4d08cdd7c0cc", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "b6688a3e-5bcf-4037-a461-b995f6fd084d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416059192100, "endTime": 30416064785300}, "additional": {"logType": "info", "children": [], "durationId": "4a49a63b-959e-4af1-badc-9b9c1cdb649b", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "7a41bd3b-12c3-44d6-b0e6-9087de75580d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416065418700, "endTime": 30416065437900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc5a7987-6308-4066-8890-7886f1fd83a4", "logId": "8d92df57-1c25-4d17-9f51-5e40cb2e6698"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d92df57-1c25-4d17-9f51-5e40cb2e6698", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416065418700, "endTime": 30416065437900}, "additional": {"logType": "info", "children": [], "durationId": "7a41bd3b-12c3-44d6-b0e6-9087de75580d", "parent": "7a3a212a-4202-4870-b4f6-2d8eb7d17f31"}}, {"head": {"id": "3ccb2fa6-927c-4a02-b1f9-6d9144fe6d62", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416068151400, "endTime": 30416345385700}, "additional": {"children": ["270d5362-1f28-4978-831d-fdae3f8063a7", "c924d947-a109-4781-9909-7abb95be5542"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc5a7987-6308-4066-8890-7886f1fd83a4", "logId": "fc62d8c3-2e2a-4cfb-85e2-ca04807c922c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "270d5362-1f28-4978-831d-fdae3f8063a7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416068153100, "endTime": 30416114589800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ccb2fa6-927c-4a02-b1f9-6d9144fe6d62", "logId": "38fbfaaa-0dee-4cb8-beaf-eb22dfb78521"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c924d947-a109-4781-9909-7abb95be5542", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416114641800, "endTime": 30416345354500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ccb2fa6-927c-4a02-b1f9-6d9144fe6d62", "logId": "f2bda255-2a55-44dc-b492-02639daa1d55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "426d2e18-7800-4f47-ac5a-ed7b9ff14cbb", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416068158400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c46c23b5-ce1a-43db-a7c1-4e131ab715a5", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416114202300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38fbfaaa-0dee-4cb8-beaf-eb22dfb78521", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416068153100, "endTime": 30416114589800}, "additional": {"logType": "info", "children": [], "durationId": "270d5362-1f28-4978-831d-fdae3f8063a7", "parent": "fc62d8c3-2e2a-4cfb-85e2-ca04807c922c"}}, {"head": {"id": "e4caa4b2-8dc8-482b-8a68-9653b71681b0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416114698300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14cfd512-6ed0-4b34-8eb9-cc266b60cd83", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416149505700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dab16485-3b6b-4799-9d55-7922c2f214f3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416149864500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38411d4-df42-442e-9072-a1a56ce5eb79", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416150083900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ced01221-e88e-48de-a127-d7140863457a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416150289500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60a54b35-fc48-4756-b05e-2952f42f7a49", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416155434500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db89f6b-51e6-4840-9175-07a62ff54078", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416165429000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079d42d4-c821-434c-a0c3-1b7c1b20da6d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416181574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a80ab23-16d6-4425-ae77-f559897496fc", "name": "Sdk init in 65 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416231482500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3f64993-c4fa-4358-b53f-019c40f31cc1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416231761100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 56}, "markType": "other"}}, {"head": {"id": "31c21cdc-8388-444c-818d-e09cf73f770d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416231827000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 56}, "markType": "other"}}, {"head": {"id": "64f4c85f-ac7a-46e5-aee3-d84d2d4afae3", "name": "Project task initialization takes 110 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416344527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcfce67a-d6dd-4b30-bcea-100e580c1ce0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416344801000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177273c8-7214-42f6-b9f9-e696484de20b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416345021700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a30b957-e3a1-474d-990d-ee4b69bdc1e3", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416345211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2bda255-2a55-44dc-b492-02639daa1d55", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416114641800, "endTime": 30416345354500}, "additional": {"logType": "info", "children": [], "durationId": "c924d947-a109-4781-9909-7abb95be5542", "parent": "fc62d8c3-2e2a-4cfb-85e2-ca04807c922c"}}, {"head": {"id": "fc62d8c3-2e2a-4cfb-85e2-ca04807c922c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416068151400, "endTime": 30416345385700}, "additional": {"logType": "info", "children": ["38fbfaaa-0dee-4cb8-beaf-eb22dfb78521", "f2bda255-2a55-44dc-b492-02639daa1d55"], "durationId": "3ccb2fa6-927c-4a02-b1f9-6d9144fe6d62", "parent": "7a3a212a-4202-4870-b4f6-2d8eb7d17f31"}}, {"head": {"id": "2b4168f7-173a-474b-be24-bfd80c3e0675", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416347006000, "endTime": 30416347026300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc5a7987-6308-4066-8890-7886f1fd83a4", "logId": "b5fc59ea-909d-4400-8d2d-a717eb3c7efd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5fc59ea-909d-4400-8d2d-a717eb3c7efd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416347006000, "endTime": 30416347026300}, "additional": {"logType": "info", "children": [], "durationId": "2b4168f7-173a-474b-be24-bfd80c3e0675", "parent": "7a3a212a-4202-4870-b4f6-2d8eb7d17f31"}}, {"head": {"id": "7a3a212a-4202-4870-b4f6-2d8eb7d17f31", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416064804400, "endTime": 30416347047500}, "additional": {"logType": "info", "children": ["8d92df57-1c25-4d17-9f51-5e40cb2e6698", "fc62d8c3-2e2a-4cfb-85e2-ca04807c922c", "b5fc59ea-909d-4400-8d2d-a717eb3c7efd"], "durationId": "cc5a7987-6308-4066-8890-7886f1fd83a4", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "cbfe9166-b1c0-41c0-a7e8-af4481cd0551", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416348299200, "endTime": 30416399183200}, "additional": {"children": ["22b8fc24-3599-4af3-8d9f-3cb619f7d4f8", "21a77026-d08b-495d-bc40-3fe459ccd382", "8e15434e-38de-41e1-b3f3-c5708911e775"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1fc629a5-0dd8-4e8b-a7bd-78c064828f7c", "logId": "353b7287-12d1-4bef-898b-e28d78d3f025"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22b8fc24-3599-4af3-8d9f-3cb619f7d4f8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416353853100, "endTime": 30416353874500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbfe9166-b1c0-41c0-a7e8-af4481cd0551", "logId": "099b1296-3b46-45a6-b9b6-88d065876721"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "099b1296-3b46-45a6-b9b6-88d065876721", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416353853100, "endTime": 30416353874500}, "additional": {"logType": "info", "children": [], "durationId": "22b8fc24-3599-4af3-8d9f-3cb619f7d4f8", "parent": "353b7287-12d1-4bef-898b-e28d78d3f025"}}, {"head": {"id": "21a77026-d08b-495d-bc40-3fe459ccd382", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416356459800, "endTime": 30416397424600}, "additional": {"children": ["cdaca38c-96cb-4a0e-8bca-164f70baa6c5", "f1a72163-772f-49a5-8721-6b56bc5eb369"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbfe9166-b1c0-41c0-a7e8-af4481cd0551", "logId": "2c8af1b7-f924-49f9-be69-197de3a8fc34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdaca38c-96cb-4a0e-8bca-164f70baa6c5", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416356462700, "endTime": 30416362217800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21a77026-d08b-495d-bc40-3fe459ccd382", "logId": "ecf14a60-89f7-4d9e-8ce1-3b0c480cfdb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1a72163-772f-49a5-8721-6b56bc5eb369", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416362235600, "endTime": 30416397404900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21a77026-d08b-495d-bc40-3fe459ccd382", "logId": "47b88375-6701-4e35-90d9-afafcc85a104"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50447e7f-b623-41c6-872d-ee19dab7cd2f", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416356468400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56699c3c-9a52-4d57-ba60-651d38969798", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416362079400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecf14a60-89f7-4d9e-8ce1-3b0c480cfdb0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416356462700, "endTime": 30416362217800}, "additional": {"logType": "info", "children": [], "durationId": "cdaca38c-96cb-4a0e-8bca-164f70baa6c5", "parent": "2c8af1b7-f924-49f9-be69-197de3a8fc34"}}, {"head": {"id": "8f312fcf-8227-45a6-b0d3-bba75ec1d2c8", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416362244500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8bca4aa-3285-4c47-8f9b-c8a8803701ad", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416379905500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53510d08-8f92-43c1-81db-da7a2f79fae5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416380052400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1add4eab-9b4e-4464-bf58-98fb64f0fd76", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416380487100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a09ada0-8dc1-4532-afd3-422ace18b9da", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416380869000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9702c4b2-fd9f-4b76-b0f8-dbe2a3c069bb", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416381005600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b822d87d-d16f-4a47-94cd-bd7e8b902f5d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416381075300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3601a84-91b5-4a49-8092-2f4ce5f55d85", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416381352000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0610df1-2b4f-495b-93f3-a304dbfd24d6", "name": "Module entry task initialization takes 9 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416396957800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b80d7fa-ddef-47b5-8b07-bc7327f55533", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416397219200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f3d71f8-c095-49b5-a26a-e51f9fa2f60c", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416397294300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01cd0d9e-6792-4e2e-9cf6-54ab1a6fe147", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416397350800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b88375-6701-4e35-90d9-afafcc85a104", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416362235600, "endTime": 30416397404900}, "additional": {"logType": "info", "children": [], "durationId": "f1a72163-772f-49a5-8721-6b56bc5eb369", "parent": "2c8af1b7-f924-49f9-be69-197de3a8fc34"}}, {"head": {"id": "2c8af1b7-f924-49f9-be69-197de3a8fc34", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416356459800, "endTime": 30416397424600}, "additional": {"logType": "info", "children": ["ecf14a60-89f7-4d9e-8ce1-3b0c480cfdb0", "47b88375-6701-4e35-90d9-afafcc85a104"], "durationId": "21a77026-d08b-495d-bc40-3fe459ccd382", "parent": "353b7287-12d1-4bef-898b-e28d78d3f025"}}, {"head": {"id": "8e15434e-38de-41e1-b3f3-c5708911e775", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416399143300, "endTime": 30416399164900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbfe9166-b1c0-41c0-a7e8-af4481cd0551", "logId": "24948cb4-0e5f-4df6-9f34-09bdbf539e04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24948cb4-0e5f-4df6-9f34-09bdbf539e04", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416399143300, "endTime": 30416399164900}, "additional": {"logType": "info", "children": [], "durationId": "8e15434e-38de-41e1-b3f3-c5708911e775", "parent": "353b7287-12d1-4bef-898b-e28d78d3f025"}}, {"head": {"id": "353b7287-12d1-4bef-898b-e28d78d3f025", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416348299200, "endTime": 30416399183200}, "additional": {"logType": "info", "children": ["099b1296-3b46-45a6-b9b6-88d065876721", "2c8af1b7-f924-49f9-be69-197de3a8fc34", "24948cb4-0e5f-4df6-9f34-09bdbf539e04"], "durationId": "cbfe9166-b1c0-41c0-a7e8-af4481cd0551", "parent": "e45b7598-92f4-4fbd-81c0-9c00b4baa2c5"}}, {"head": {"id": "e45b7598-92f4-4fbd-81c0-9c00b4baa2c5", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416347114100, "endTime": 30416399195700}, "additional": {"logType": "info", "children": ["353b7287-12d1-4bef-898b-e28d78d3f025"], "durationId": "1fc629a5-0dd8-4e8b-a7bd-78c064828f7c", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "5669d22d-6d19-49ff-9df4-e1257c8f19a1", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416479985300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ed8b57d-f333-4c49-8cc6-769676a50bbc", "name": "hvigorfile, resolve hvigorfile dependencies in 139 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416537982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edea5d24-7249-4347-a7b9-0c0bb17beb81", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416399274800, "endTime": 30416538346000}, "additional": {"logType": "info", "children": [], "durationId": "9fc855bb-ab8a-46f4-bf2a-cc79128ead0e", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "bd430d22-95a9-4bcc-8565-dea48836b6d3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416539241800, "endTime": 30416539828000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "logId": "821bd602-561f-44ce-88fc-70c05d217a9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3dcdbcf-bb08-44c4-b479-b3f3eb8ac9c9", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416539424500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821bd602-561f-44ce-88fc-70c05d217a9d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416539241800, "endTime": 30416539828000}, "additional": {"logType": "info", "children": [], "durationId": "bd430d22-95a9-4bcc-8565-dea48836b6d3", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "7033e54c-457e-4803-b5d8-63ed4995624c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416546374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb26654-14a4-4733-8121-58db858ed28a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416553267500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a18d48-624b-4417-aa9a-9c5895013798", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416539850500, "endTime": 30416554115000}, "additional": {"logType": "info", "children": [], "durationId": "87c67dd2-840f-487a-8e01-98d0e5b0636a", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "2d5f3573-d08c-4102-82bf-54b94ccdef96", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416559548400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db767a44-d442-499b-bc49-013ef76b2793", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416559702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626cddff-3d77-4f3e-8ea6-220f756d8450", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416562035600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c2e930e-77aa-498f-9ce0-5cb00ba68882", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416562153200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4662ffce-4408-49a9-96e5-c193b3fcf8c8", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416554139200, "endTime": 30416566619700}, "additional": {"logType": "info", "children": [], "durationId": "8b96366c-605e-451a-992b-81885a1fae36", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "54b52e67-6e4a-41e5-8530-c68c6f83faad", "name": "Configuration phase cost:508 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416566673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b70b9f-923f-4c97-a921-6c90beda49ef", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416566652000, "endTime": 30416566786400}, "additional": {"logType": "info", "children": [], "durationId": "4329f992-dc81-4bad-bc5e-0a78e61d386a", "parent": "e737e273-ff11-4323-b1da-8ff46f433b03"}}, {"head": {"id": "e737e273-ff11-4323-b1da-8ff46f433b03", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416051019900, "endTime": 30416566800100}, "additional": {"logType": "info", "children": ["37bb4d19-ad64-44d6-a83e-427cdeb9a0ce", "b6688a3e-5bcf-4037-a461-b995f6fd084d", "7a3a212a-4202-4870-b4f6-2d8eb7d17f31", "e45b7598-92f4-4fbd-81c0-9c00b4baa2c5", "edea5d24-7249-4347-a7b9-0c0bb17beb81", "33a18d48-624b-4417-aa9a-9c5895013798", "4662ffce-4408-49a9-96e5-c193b3fcf8c8", "d6b70b9f-923f-4c97-a921-6c90beda49ef", "821bd602-561f-44ce-88fc-70c05d217a9d"], "durationId": "8e9cbad0-15ae-4771-aeac-c7e51af90832", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "d358b518-5bb0-43cf-92e5-075f6b3b0d55", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416567912600, "endTime": 30416567925600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "109d4550-f997-459a-a46f-f074158b3442", "logId": "9123d3a4-23be-4420-a60a-4b4c56581ad5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9123d3a4-23be-4420-a60a-4b4c56581ad5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416567912600, "endTime": 30416567925600}, "additional": {"logType": "info", "children": [], "durationId": "d358b518-5bb0-43cf-92e5-075f6b3b0d55", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "59ab798c-2e87-40a7-8cb1-9da2cbcbd97b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416566824900, "endTime": 30416567933600}, "additional": {"logType": "info", "children": [], "durationId": "fa2eb781-65d2-4f78-8a34-a42d5667d550", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "4c1017ba-e108-446b-b420-b7b09b852dc9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416567937000, "endTime": 30416567937800}, "additional": {"logType": "info", "children": [], "durationId": "23796aa7-52c5-4f29-8433-670cfbe3e4e6", "parent": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982"}}, {"head": {"id": "e9fc4ba4-cba9-4c3d-a44e-9b37618e4982", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416033888200, "endTime": 30416567942600}, "additional": {"logType": "info", "children": ["9acf300c-e1d1-4380-a234-39689e7f6dd2", "e737e273-ff11-4323-b1da-8ff46f433b03", "59ab798c-2e87-40a7-8cb1-9da2cbcbd97b", "4c1017ba-e108-446b-b420-b7b09b852dc9", "d5b98fc7-0898-478f-b779-e13940b997f5", "418cebfc-068e-4bc3-bc73-cfd9d0b82096", "9123d3a4-23be-4420-a60a-4b4c56581ad5"], "durationId": "109d4550-f997-459a-a46f-f074158b3442"}}, {"head": {"id": "c9671546-3d45-4de7-aedf-20a062d9a47f", "name": "Configuration task cost before running: 538 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416568441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57891bcc-a243-4cb6-bdbf-9483f1e5c089", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416573627500, "endTime": 30416581174500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "585b57b7-8b3d-43a7-a921-87be08a6480e", "logId": "ae5a5d17-c893-48c9-8181-f25002f1d19e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "585b57b7-8b3d-43a7-a921-87be08a6480e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416569921300}, "additional": {"logType": "detail", "children": [], "durationId": "57891bcc-a243-4cb6-bdbf-9483f1e5c089"}}, {"head": {"id": "2c4ebfca-ead2-4ab8-af3f-95fa7e2b20f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416570255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6907edda-6720-41d3-946c-81112e64c5de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416570335600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1ea5c37-251a-4b47-8c0f-f67f0250d44e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416573644700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6413c667-0e03-4e62-b019-e6e00d687970", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416580911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff46f528-c4b8-40ae-8d7e-531d47c48bae", "name": "entry : default@PreBuild cost memory 0.31601715087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416581049400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae5a5d17-c893-48c9-8181-f25002f1d19e", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416573627500, "endTime": 30416581174500}, "additional": {"logType": "info", "children": [], "durationId": "57891bcc-a243-4cb6-bdbf-9483f1e5c089"}}, {"head": {"id": "c5d6eb73-edff-49f3-9a07-8b6893fefe15", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416585804600, "endTime": 30416588205100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a4784c8e-9aa8-4249-addf-c03a217fa8f2", "logId": "254c3857-8209-481c-8edf-1f587db98776"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4784c8e-9aa8-4249-addf-c03a217fa8f2", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416584434300}, "additional": {"logType": "detail", "children": [], "durationId": "c5d6eb73-edff-49f3-9a07-8b6893fefe15"}}, {"head": {"id": "ff537579-57bd-4b06-ab49-5fc21484ccb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416584834500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d948a4f-59d5-4fa9-8c97-7ef0e847d8d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416584933700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70811a9-995b-4138-9dea-4ab89bc8b050", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416585815200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f605cb-8b80-498a-bee7-3d276a3ad3dc", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416586789000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a3a087f-d52c-4632-b3e2-bd7fb3c0918b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416587745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0572e796-3699-44e9-a7c0-ca5d8d9d8667", "name": "entry : default@GenerateMetadata cost memory 0.095245361328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416587924500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254c3857-8209-481c-8edf-1f587db98776", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416585804600, "endTime": 30416588205100}, "additional": {"logType": "info", "children": [], "durationId": "c5d6eb73-edff-49f3-9a07-8b6893fefe15"}}, {"head": {"id": "34baa42a-da9c-4770-ac99-de6b40eb68c2", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590526100, "endTime": 30416590927300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5a743e95-ccd8-4b03-af04-fab58bb6b78c", "logId": "238d7f43-f0ea-40f7-a926-e471332460c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a743e95-ccd8-4b03-af04-fab58bb6b78c", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416589842500}, "additional": {"logType": "detail", "children": [], "durationId": "34baa42a-da9c-4770-ac99-de6b40eb68c2"}}, {"head": {"id": "4a2879bf-d6ff-4100-b47c-842eafe21fe4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590173900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5cb8b0c-afc5-4952-9474-973525959bcd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590267100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d57ff7-06c3-48b2-9d40-b9b0ca90af89", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590533800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d764fc2c-80f9-45e6-88da-04c5b968ab6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590662800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75627e9-13bc-4175-9442-aaff18dc039e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590725900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "792a87d8-9275-480c-a0fd-bf7b5e0d895d", "name": "entry : default@ConfigureCmake cost memory 0.0365142822265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca2e976c-b621-4013-a90e-dae5148c110e", "name": "runTaskFromQueue task cost before running: 561 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590874400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "238d7f43-f0ea-40f7-a926-e471332460c6", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416590526100, "endTime": 30416590927300, "totalTime": 331600}, "additional": {"logType": "info", "children": [], "durationId": "34baa42a-da9c-4770-ac99-de6b40eb68c2"}}, {"head": {"id": "e520dbb0-9d83-41ab-a9dd-b6612c92ca88", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416594310600, "endTime": 30416595941000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "bb36c546-d450-4eb2-9327-7bf04cac028d", "logId": "a7e85eea-d9e9-4565-b4da-6d6fbd4dbc44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb36c546-d450-4eb2-9327-7bf04cac028d", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416593179000}, "additional": {"logType": "detail", "children": [], "durationId": "e520dbb0-9d83-41ab-a9dd-b6612c92ca88"}}, {"head": {"id": "1da254bf-662d-4350-9ed3-318c88d88a5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416593582300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29e2598-f76f-4968-9ff5-bc40d03736a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416593684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60c6ab75-cb72-4e2e-b2d8-4d7d3971dc18", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416594340000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ed71f7-bd2c-4e74-9840-e12fd5b9c9e6", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416595780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d620dd18-1cbb-4567-b9ce-e86f07e5b983", "name": "entry : default@MergeProfile cost memory 0.1069793701171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416595877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e85eea-d9e9-4565-b4da-6d6fbd4dbc44", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416594310600, "endTime": 30416595941000}, "additional": {"logType": "info", "children": [], "durationId": "e520dbb0-9d83-41ab-a9dd-b6612c92ca88"}}, {"head": {"id": "e1a871fb-4ae0-47d2-8cfb-456f5524e72d", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416598778000, "endTime": 30416600883000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a6768a59-4b81-43c3-8243-2c0d579a0d71", "logId": "267d1961-eae4-4d3b-965e-cf7c9baa14b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6768a59-4b81-43c3-8243-2c0d579a0d71", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416597498800}, "additional": {"logType": "detail", "children": [], "durationId": "e1a871fb-4ae0-47d2-8cfb-456f5524e72d"}}, {"head": {"id": "b9fc8027-e399-45b6-a576-d2b2d6c7faf0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416597829000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "765d487f-3a76-4b31-82fc-68ec94b98ddc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416597925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd940fb-f465-4aca-a305-ebd4b5dc8d56", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416598791900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18446ed-b6a5-43cd-b992-24c311ff9680", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416599798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4002a5f4-65a8-4ff9-9671-88973a7c7e29", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416600702000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ecadaee-36f9-405f-bf23-f7d5379e5290", "name": "entry : default@CreateBuildProfile cost memory 0.1044769287109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416600811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "267d1961-eae4-4d3b-965e-cf7c9baa14b1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416598778000, "endTime": 30416600883000}, "additional": {"logType": "info", "children": [], "durationId": "e1a871fb-4ae0-47d2-8cfb-456f5524e72d"}}, {"head": {"id": "76ed0c51-26d7-426c-a2d1-7e512f4d28d1", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607001900, "endTime": 30416608000300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "eab6e282-fc85-4dfd-b1c2-b9dde5c1ef99", "logId": "f553067a-4d23-481c-af96-2029e3c38b57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eab6e282-fc85-4dfd-b1c2-b9dde5c1ef99", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416602808900}, "additional": {"logType": "detail", "children": [], "durationId": "76ed0c51-26d7-426c-a2d1-7e512f4d28d1"}}, {"head": {"id": "fa6a6c6c-ef7b-43f5-97e8-dd91f907aad1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416603513400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e203466-c359-41cf-895b-3dac4f343321", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416603649000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b800bb8f-891e-4f4a-958a-040664e91539", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607014300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06905dec-f5b3-486f-8bb2-c119122147a1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e41694-d96c-4e60-be19-05340412dd33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607283200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "980a1758-98a5-41d1-819c-90678bce81cc", "name": "entry : default@PreCheckSyscap cost memory 0.03673553466796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607533300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23978bf8-c1bd-4b87-abe0-65edb828d2f6", "name": "runTaskFromQueue task cost before running: 578 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607889700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f553067a-4d23-481c-af96-2029e3c38b57", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416607001900, "endTime": 30416608000300, "totalTime": 614200}, "additional": {"logType": "info", "children": [], "durationId": "76ed0c51-26d7-426c-a2d1-7e512f4d28d1"}}, {"head": {"id": "12d00bae-98bc-4d07-9c01-fd524acdea2d", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416615715100, "endTime": 30416616479200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "70fccf01-69b7-48eb-9d87-24a8625dcc4a", "logId": "bdea12a1-c10b-4dfa-ad45-8361bfe7e15b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70fccf01-69b7-48eb-9d87-24a8625dcc4a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416610266000}, "additional": {"logType": "detail", "children": [], "durationId": "12d00bae-98bc-4d07-9c01-fd524acdea2d"}}, {"head": {"id": "f993cfb4-fa97-445d-8805-127bdadba9ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416610750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f8cafd1-0d7f-4c8d-932d-3eb28dd5ecb5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416610873900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dccb4b91-0bbf-46c9-9121-a1c285d024b6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416615742500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6d3040e-c970-467e-914b-e23eaca0eded", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416616039200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46974dbe-d3a9-4dcc-866d-ff6de6423473", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03911590576171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416616319800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce9b59b-2f64-4484-872a-dd9b9e47a747", "name": "runTaskFromQueue task cost before running: 586 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416616419600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdea12a1-c10b-4dfa-ad45-8361bfe7e15b", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416615715100, "endTime": 30416616479200, "totalTime": 689600}, "additional": {"logType": "info", "children": [], "durationId": "12d00bae-98bc-4d07-9c01-fd524acdea2d"}}, {"head": {"id": "a5cd4da9-fe7a-4050-89ef-527b802c595f", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416621288700, "endTime": 30416623503000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "fbcfc3cb-0819-43df-b98a-c4033f3ec6da", "logId": "b9e058f6-a14d-4a69-a161-bdcafa972d2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fbcfc3cb-0819-43df-b98a-c4033f3ec6da", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416618142000}, "additional": {"logType": "detail", "children": [], "durationId": "a5cd4da9-fe7a-4050-89ef-527b802c595f"}}, {"head": {"id": "5d81fb93-1ea1-43f2-bda1-0f6fe47c9e5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416618454700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3456a24-f065-4624-8476-4d869b907b80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416618542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2da441-1315-4c58-8239-c7fa634a4c8d", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416621301200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff43a79c-338c-4be1-a61f-e433e54a81a4", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416622947300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf7ea32-8ae0-409c-a963-1832a6fa3e14", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416623088500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca3985ad-b346-416d-a4cd-d36c3db1afd7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416623214800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f914a3-b44e-4777-8fda-ba70331da2ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416623278600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb5f73ce-179b-4455-b57e-dc0889fdee0e", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1187286376953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416623360200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15809c64-babf-48c1-b729-2480ef8743eb", "name": "runTaskFromQueue task cost before running: 593 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416623433800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9e058f6-a14d-4a69-a161-bdcafa972d2e", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416621288700, "endTime": 30416623503000, "totalTime": 2133300}, "additional": {"logType": "info", "children": [], "durationId": "a5cd4da9-fe7a-4050-89ef-527b802c595f"}}, {"head": {"id": "75721ef7-8474-4902-8adf-ba1a4df47a74", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628154700, "endTime": 30416628546100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5d594389-5abc-435c-87b2-d309e78c9cce", "logId": "161d1dc8-35cf-410d-a454-a38a56b5113f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d594389-5abc-435c-87b2-d309e78c9cce", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416627058300}, "additional": {"logType": "detail", "children": [], "durationId": "75721ef7-8474-4902-8adf-ba1a4df47a74"}}, {"head": {"id": "4572ea0f-40ce-4bf3-bb1f-b6d3d7198d62", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416627438300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40ecfa90-be14-4e91-a525-89ff9e1ce5d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416627540700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a50b76-7a00-4e0d-a541-99f4775b48fc", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628173200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc60835-b612-4e45-8923-99a5b7c20b68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628291500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa12dfc3-c4ee-42fc-90ad-14c127e9f7e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628345500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea3dd2d-1eb9-432f-a682-5120a8dc7ce8", "name": "entry : default@BuildNativeWithCmake cost memory 0.037567138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c222710-9b85-4465-bff6-e9cc93bf1991", "name": "runTaskFromQueue task cost before running: 598 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628492500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "161d1dc8-35cf-410d-a454-a38a56b5113f", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416628154700, "endTime": 30416628546100, "totalTime": 322700}, "additional": {"logType": "info", "children": [], "durationId": "75721ef7-8474-4902-8adf-ba1a4df47a74"}}, {"head": {"id": "3825ebeb-5621-4436-b4d6-7aef8a62d81c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416631388400, "endTime": 30416634294200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7a685262-ff77-435b-a39f-e6eaafaf8cbc", "logId": "03bccf58-6d93-49a5-b7f5-53a3e20f6c40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a685262-ff77-435b-a39f-e6eaafaf8cbc", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416630245900}, "additional": {"logType": "detail", "children": [], "durationId": "3825ebeb-5621-4436-b4d6-7aef8a62d81c"}}, {"head": {"id": "4a2a6bcc-26e1-4919-9486-69a7405ffe30", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416630584600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2354e84c-f7f3-460b-93a0-424b910c57dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416630733700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d9f8aa1-b727-4b07-bf1e-2a12f3769bf4", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416631397700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e798b94-f702-4c7d-9bf5-8bd51722e932", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416633983900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7d9365d-3c58-4f32-9fb2-641df59f2727", "name": "entry : default@MakePackInfo cost memory 0.1397247314453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416634090300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03bccf58-6d93-49a5-b7f5-53a3e20f6c40", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416631388400, "endTime": 30416634294200}, "additional": {"logType": "info", "children": [], "durationId": "3825ebeb-5621-4436-b4d6-7aef8a62d81c"}}, {"head": {"id": "e1ebee8b-a0c5-4b41-acb0-9f2facb3ce9f", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416638041700, "endTime": 30416642421700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "fad74c5c-4815-4244-b50d-1a7faaca61a7", "logId": "09716671-252c-444c-8a5b-b038d2131ff1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fad74c5c-4815-4244-b50d-1a7faaca61a7", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416636420000}, "additional": {"logType": "detail", "children": [], "durationId": "e1ebee8b-a0c5-4b41-acb0-9f2facb3ce9f"}}, {"head": {"id": "65767fd5-d7e7-44f7-9988-68dc8140840b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416636760900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096536e6-3e33-4682-8538-5ce0c51f78c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416636861300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a32fef97-0f49-47a9-9071-18ec124257a1", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416638053000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "422d10a3-0ff2-4e59-bc6a-1b2939298c97", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416638195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcabf6e6-9dcd-49eb-9290-f5b04bc42e33", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416639277900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feab5afa-1fcb-4b52-84f4-34b1aac69130", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416640853500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7896114-5ec8-4544-ab47-d2e2f330c754", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416641477700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa5c2b0-5b59-42c4-8beb-5e0b6cd136fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416641711300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9650ef7-7100-49c3-ad7a-7acae4f87cb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416641822600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36fe71b7-80c4-4c94-a4ca-57e990d671c6", "name": "entry : default@SyscapTransform cost memory 0.1551361083984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416642069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c06556-d189-400f-8008-82046c41dda3", "name": "runTaskFromQueue task cost before running: 612 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416642224800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09716671-252c-444c-8a5b-b038d2131ff1", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416638041700, "endTime": 30416642421700, "totalTime": 4119300}, "additional": {"logType": "info", "children": [], "durationId": "e1ebee8b-a0c5-4b41-acb0-9f2facb3ce9f"}}, {"head": {"id": "e8964354-653e-4120-a51d-268e38069e4a", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416647537300, "endTime": 30416648850700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "96447960-3e02-4c17-818a-81014c29e7a4", "logId": "c706e7fb-6a43-4b57-88d9-141c56bd992b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96447960-3e02-4c17-818a-81014c29e7a4", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416644479800}, "additional": {"logType": "detail", "children": [], "durationId": "e8964354-653e-4120-a51d-268e38069e4a"}}, {"head": {"id": "364fd0f2-bf30-439a-a094-4ce49cc58635", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416645231000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8589d807-ddf2-4eb4-b474-4c62f895c140", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416645366200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041b98a4-6d58-45b1-907b-4ee96fe28402", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416647553000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb51a78-767d-401e-a72f-647dbf4dfdc9", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416648650300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29ac6cd-148d-49f9-aa45-e40e38c730b1", "name": "entry : default@ProcessProfile cost memory 0.06046295166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416648779500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c706e7fb-6a43-4b57-88d9-141c56bd992b", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416647537300, "endTime": 30416648850700}, "additional": {"logType": "info", "children": [], "durationId": "e8964354-653e-4120-a51d-268e38069e4a"}}, {"head": {"id": "09641e9f-6946-4055-bc80-ca003d0fc32d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416654298200, "endTime": 30416660320700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b8f2ee84-4eaa-4f79-9f46-e70da774fb4b", "logId": "2f4edad1-4d53-4d10-a512-3b09ec725924"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8f2ee84-4eaa-4f79-9f46-e70da774fb4b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416651190200}, "additional": {"logType": "detail", "children": [], "durationId": "09641e9f-6946-4055-bc80-ca003d0fc32d"}}, {"head": {"id": "50dde098-ce36-416c-b251-d8a575aec6fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416651613000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8decfc72-b9fe-4830-be6a-b0186cce46e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416651990700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9282db9-4d34-4719-9efb-6c2f8a4cac49", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416654310900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f2543d-d0d7-4c2f-b643-b8051831c655", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416660096500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb937e09-4a5b-4595-aeac-849baed6cc4f", "name": "entry : default@ProcessRouterMap cost memory 0.24993896484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416660245200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f4edad1-4d53-4d10-a512-3b09ec725924", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416654298200, "endTime": 30416660320700}, "additional": {"logType": "info", "children": [], "durationId": "09641e9f-6946-4055-bc80-ca003d0fc32d"}}, {"head": {"id": "c3111595-dd68-4506-8085-be632bb7b336", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416664787200, "endTime": 30416666829100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f8a6c643-d2a8-4095-b5b7-d7a47a0b49c9", "logId": "9c8ab332-5b30-48b6-8841-d7205e875c9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8a6c643-d2a8-4095-b5b7-d7a47a0b49c9", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416663349600}, "additional": {"logType": "detail", "children": [], "durationId": "c3111595-dd68-4506-8085-be632bb7b336"}}, {"head": {"id": "f4a6c377-7181-469d-8b28-1c28217c65a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416663920300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "282f7f98-5467-4045-8921-e077e3f4532a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416664029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1275c7-51f1-48e7-bf8a-b844494b9e4c", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416664798300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b123ec8f-1b00-4423-b78e-2f3361189631", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416664916500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f05a9452-15e5-4f5c-ba71-e14cd76e88b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416665011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d845ae0-dd77-4b74-a5a6-ce676e22cc17", "name": "entry : default@BuildNativeWithNinja cost memory 0.05716705322265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416666462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3ea55e5-4cb0-4e78-afe1-b044e034b808", "name": "runTaskFromQueue task cost before running: 637 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416666672200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c8ab332-5b30-48b6-8841-d7205e875c9a", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416664787200, "endTime": 30416666829100, "totalTime": 1827200}, "additional": {"logType": "info", "children": [], "durationId": "c3111595-dd68-4506-8085-be632bb7b336"}}, {"head": {"id": "60fc9db5-b5c4-4806-b1a9-f7d1b48ef155", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416673826500, "endTime": 30416679893700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7d49fa03-6feb-45d8-9fca-7a076c09d863", "logId": "dd3c0264-ce52-4f21-9fb4-4fdb6d7b7b2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d49fa03-6feb-45d8-9fca-7a076c09d863", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416669672600}, "additional": {"logType": "detail", "children": [], "durationId": "60fc9db5-b5c4-4806-b1a9-f7d1b48ef155"}}, {"head": {"id": "2235ab8d-24c9-4a7e-b6c5-34885b602d35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416670191600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5a984e-4f3f-477a-aedf-a0a2ebdb87e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416670459700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f80b13-eb68-4a09-966c-dd61f2c74696", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416672278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6b585e-e2f7-4902-8579-df07b42c0c30", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416676330700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd0eab1-1966-42c4-8858-06d45693777f", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416678164600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3252a9fe-a289-4212-bf51-49761056700f", "name": "entry : default@ProcessResource cost memory 0.1695556640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416678290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd3c0264-ce52-4f21-9fb4-4fdb6d7b7b2d", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416673826500, "endTime": 30416679893700}, "additional": {"logType": "info", "children": [], "durationId": "60fc9db5-b5c4-4806-b1a9-f7d1b48ef155"}}, {"head": {"id": "e26ef98e-661a-49f3-8051-5530c4c40064", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416688005700, "endTime": 30416705566000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "21f44dca-ea4a-4c38-a7af-7028cc7cfe73", "logId": "016048cb-4458-4160-b49e-cf137116fe90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21f44dca-ea4a-4c38-a7af-7028cc7cfe73", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416684181400}, "additional": {"logType": "detail", "children": [], "durationId": "e26ef98e-661a-49f3-8051-5530c4c40064"}}, {"head": {"id": "049fd79e-20bb-460b-81ff-2714c5a4ef0d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416684528500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c569aa2e-0691-4a13-ad2a-f1710b7ecf95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416684671900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7cd749-e758-46b4-a9ef-f7f180a21640", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416688018000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f675688-e88b-48d4-b9b8-84ca277b2875", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416705352300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16dda56a-2d3d-483a-9c47-a3095be348ea", "name": "entry : default@GenerateLoaderJson cost memory 0.9866409301757812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416705489500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "016048cb-4458-4160-b49e-cf137116fe90", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416688005700, "endTime": 30416705566000}, "additional": {"logType": "info", "children": [], "durationId": "e26ef98e-661a-49f3-8051-5530c4c40064"}}, {"head": {"id": "a425c1da-ea01-44cc-849c-a35bcb7b1326", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416712906300, "endTime": 30416716160000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "959290c7-9ca3-4ab7-92ad-5d9953263697", "logId": "8fec9919-df06-4110-b360-b55087395f80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "959290c7-9ca3-4ab7-92ad-5d9953263697", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416711477600}, "additional": {"logType": "detail", "children": [], "durationId": "a425c1da-ea01-44cc-849c-a35bcb7b1326"}}, {"head": {"id": "497781ca-8304-4be6-87f5-21c347a35fcd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416712049900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b00f170-faab-4655-9b6d-6d151123eea5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416712202800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df223e66-dc17-4837-8af6-ca050aa36189", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416712922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c52f6c2-1144-44f8-872f-ac8e73243ae6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416715145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c4ad59e-bbb6-41c3-a5c4-147ee21f0e3f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416715279600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539a96a9-a774-4a40-b4fc-1b7a6016b802", "name": "entry : default@ProcessLibs cost memory 0.126251220703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416715940400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff6efa92-486e-425e-bb39-182c5f4f9daa", "name": "runTaskFromQueue task cost before running: 686 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416716054100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fec9919-df06-4110-b360-b55087395f80", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416712906300, "endTime": 30416716160000, "totalTime": 3122600}, "additional": {"logType": "info", "children": [], "durationId": "a425c1da-ea01-44cc-849c-a35bcb7b1326"}}, {"head": {"id": "1f9563a2-5d1b-4a07-a1d5-b8979dbc64dd", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416723645100, "endTime": 30416744252700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c52798df-e437-4c22-adce-7c130efb42d5", "logId": "082d307d-5e76-40ef-83b3-d0b633da6e92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c52798df-e437-4c22-adce-7c130efb42d5", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416720083100}, "additional": {"logType": "detail", "children": [], "durationId": "1f9563a2-5d1b-4a07-a1d5-b8979dbc64dd"}}, {"head": {"id": "4d5a1a81-dd44-47db-b28f-79467747106c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416720469400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bef9c224-db9f-4e19-8b6e-eae4a8b51e92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416720572800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ac1b65-1ace-47a2-a996-624d6bbfe72b", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416721404100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc6f6393-2361-4a14-b35c-4f45c396cfdd", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416723669400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0450f45f-30c1-48ff-bc86-f78126eadbef", "name": "Incremental task entry:default@CompileResource pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416743932500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b010404-595f-47f0-9a14-ad85b0de4679", "name": "entry : default@CompileResource cost memory 1.4095001220703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416744142900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "082d307d-5e76-40ef-83b3-d0b633da6e92", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416723645100, "endTime": 30416744252700}, "additional": {"logType": "info", "children": [], "durationId": "1f9563a2-5d1b-4a07-a1d5-b8979dbc64dd"}}, {"head": {"id": "b47a264b-b2d2-4ac8-be45-b7cde008c465", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416751027300, "endTime": 30416752469000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "25673b41-9822-48db-9f2d-bf3adc70e4d9", "logId": "99610355-75d5-4cf1-a846-610ca63d4175"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25673b41-9822-48db-9f2d-bf3adc70e4d9", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416747675700}, "additional": {"logType": "detail", "children": [], "durationId": "b47a264b-b2d2-4ac8-be45-b7cde008c465"}}, {"head": {"id": "75c92340-51b0-4b22-924c-35c38c7a795f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416748311000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdeb28a9-5680-4d98-ad79-9d5c0cf89c67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416748435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e67bcd-c63b-4339-bb0f-28d94a88e9c6", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416751039400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f0b9b0-c506-45c2-9ad6-ec0c026eba82", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416751326300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ba0a56-2188-4cc5-a763-a7d79adaeea4", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416752284300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d21bbf-2085-488b-ba37-1e54298a761d", "name": "entry : default@DoNativeStrip cost memory 0.07672882080078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416752397800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99610355-75d5-4cf1-a846-610ca63d4175", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416751027300, "endTime": 30416752469000}, "additional": {"logType": "info", "children": [], "durationId": "b47a264b-b2d2-4ac8-be45-b7cde008c465"}}, {"head": {"id": "85649067-e96b-4ae4-afed-19f37cd492ef", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416758886900, "endTime": 30418410395700}, "additional": {"children": ["ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "7e9b69bb-2207-43bd-bb2f-724187ca07bd"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "22e7f467-8768-4323-bf6a-d405d2b1762f", "logId": "c69a1d5d-a42d-4f67-a396-b7ce432d10c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22e7f467-8768-4323-bf6a-d405d2b1762f", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416754032400}, "additional": {"logType": "detail", "children": [], "durationId": "85649067-e96b-4ae4-afed-19f37cd492ef"}}, {"head": {"id": "c6dfc1a8-082e-4d65-9a38-145cf4996b34", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416754604400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad050fde-68e6-4b36-ad29-1c2ed3160d9d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416754713500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512ba966-c71b-4564-a0bb-d1dc92c7a183", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416758901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f10d499d-0fbb-4139-a5b3-440010c36049", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416770062100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b39e97b9-cd98-47da-9995-67cbbcd658e1", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416770218300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd958e06-e59d-43a1-baf3-9ab907347faa", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416783585000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71dac2e5-5d6a-430a-a156-32853507adb5", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416784152000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "869b3fcc-b7ce-42ab-8f88-23c685f1d5f5", "name": "default@CompileArkTS work[51] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416785408800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30416898395100, "endTime": 30418015403900}, "additional": {"children": ["a5f8619d-55b6-46a6-981f-411b868397df", "2ca69018-681b-4b19-b248-58e05316ea2e", "caf09d22-7d22-4ff0-bde6-f48265a207ed", "e1516bab-78dc-4556-a1b0-3b91ac69c3d7", "c49372dc-7db0-42ba-b352-f3d2e749b847", "6d639659-e513-4ab4-a351-fd59eda6247e"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "85649067-e96b-4ae4-afed-19f37cd492ef", "logId": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1bf9668-029e-4a97-95e6-fb32e3b5c058", "name": "default@CompileArkTS work[51] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416786608500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ce24ac-7c27-4536-b44b-7099718a6fd0", "name": "default@CompileArkTS work[51] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416786857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f3ee0a-c211-4624-a77d-48f89bd028ea", "name": "CopyResources startTime: 30416786938200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416786940400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86283136-68c2-42a4-9c90-9b166ad80f91", "name": "default@CompileArkTS work[52] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416787002100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e9b69bb-2207-43bd-bb2f-724187ca07bd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30418396714300, "endTime": 30418409747200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "85649067-e96b-4ae4-afed-19f37cd492ef", "logId": "83331385-7501-4849-a901-a1f02e19690b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6c281c5-49c4-421a-9ac5-f1499a619c8e", "name": "default@CompileArkTS work[52] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416787746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3055ab1-5066-4b43-9487-01dd18531c34", "name": "default@CompileArkTS work[52] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416787838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae5b64e0-a9eb-49a8-a0b9-a9d5b3ff8fa4", "name": "entry : default@CompileArkTS cost memory 1.5934829711914062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416787993100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04341f1-2bf8-46eb-8410-3a2ab3fc4e7e", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416795297100, "endTime": 30416798337300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "a1648afa-4bcd-413b-a416-78c312d04578", "logId": "d92c978e-bc79-451d-bc07-dde45c58b200"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1648afa-4bcd-413b-a416-78c312d04578", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416789980500}, "additional": {"logType": "detail", "children": [], "durationId": "b04341f1-2bf8-46eb-8410-3a2ab3fc4e7e"}}, {"head": {"id": "152411ff-c615-4908-88ed-1a508fc6bec8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416790491700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "190b7c1f-ab56-4198-857a-91e908575aed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416790616100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2067623c-2b80-4356-b4a5-b85da7218056", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416795310100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1fa573-9497-4cf4-b72f-f12899d9489a", "name": "entry : default@BuildJS cost memory 0.12860107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416798128000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d8379ad-ea0e-4847-893b-39a45d7b09e7", "name": "runTaskFromQueue task cost before running: 768 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416798272200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d92c978e-bc79-451d-bc07-dde45c58b200", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416795297100, "endTime": 30416798337300, "totalTime": 2953800}, "additional": {"logType": "info", "children": [], "durationId": "b04341f1-2bf8-46eb-8410-3a2ab3fc4e7e"}}, {"head": {"id": "063209db-5247-454c-9925-0252d45411e6", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416803373400, "endTime": 30416805510400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7ba8f5f8-fb8e-430f-830f-ac41840dc3d4", "logId": "b4e505db-8205-41a0-8743-08622e45bd8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ba8f5f8-fb8e-430f-830f-ac41840dc3d4", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416800379900}, "additional": {"logType": "detail", "children": [], "durationId": "063209db-5247-454c-9925-0252d45411e6"}}, {"head": {"id": "9c7c9018-6d93-435b-8716-03c17aae8c44", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416800770900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee35cd96-badc-444e-b2f4-00b69d52e32a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416800920100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "654d2faf-ce33-4ecc-8d3c-5ed02c2a5bbd", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416803384800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df811006-ea9f-4427-9058-b38a4e19fdcf", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416803863000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512866a2-a9fc-4d9e-abd9-8efa6125b751", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416805308000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "076313a6-2cfb-4585-bba3-e143568bd714", "name": "entry : default@CacheNativeLibs cost memory 0.09455108642578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416805436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e505db-8205-41a0-8743-08622e45bd8c", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416803373400, "endTime": 30416805510400}, "additional": {"logType": "info", "children": [], "durationId": "063209db-5247-454c-9925-0252d45411e6"}}, {"head": {"id": "416ff064-a626-4641-b9b1-d690ceb44424", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416897929700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e952f364-c0a9-4f18-9924-825820875184", "name": "default@CompileArkTS work[51] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416898269500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d6cec8-f98a-425f-8a4e-69c31d252ec0", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416898360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ba76f07-6b49-4767-b3e8-26a3bf534dc4", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416898408600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6049ad-f94c-4494-b06d-beb45322e314", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416898728700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c121c77-073e-4023-bfe7-d3f5feb44b74", "name": "default@CompileArkTS work[52] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416901344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8abd9396-0c71-4479-9ec3-f937a93ca80c", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418015818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f8619d-55b6-46a6-981f-411b868397df", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30416898483900, "endTime": 30416902636100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "logId": "bdba89f2-f33e-4fd4-b4d5-a69164ff88da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdba89f2-f33e-4fd4-b4d5-a69164ff88da", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416898483900, "endTime": 30416902636100}, "additional": {"logType": "info", "children": [], "durationId": "a5f8619d-55b6-46a6-981f-411b868397df", "parent": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}}, {"head": {"id": "2ca69018-681b-4b19-b248-58e05316ea2e", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30416902682300, "endTime": 30416902963100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "logId": "4139ecb8-951d-4df4-b9d7-d6bda0defbea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4139ecb8-951d-4df4-b9d7-d6bda0defbea", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416902682300, "endTime": 30416902963100}, "additional": {"logType": "info", "children": [], "durationId": "2ca69018-681b-4b19-b248-58e05316ea2e", "parent": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}}, {"head": {"id": "caf09d22-7d22-4ff0-bde6-f48265a207ed", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30416902981000, "endTime": 30416903023600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "logId": "a8acaef9-8698-445f-8102-4a79b35dd2e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8acaef9-8698-445f-8102-4a79b35dd2e3", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416902981000, "endTime": 30416903023600}, "additional": {"logType": "info", "children": [], "durationId": "caf09d22-7d22-4ff0-bde6-f48265a207ed", "parent": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}}, {"head": {"id": "e1516bab-78dc-4556-a1b0-3b91ac69c3d7", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30416903040400, "endTime": 30417885188700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "logId": "fd90744e-7ceb-43ab-8982-36c681299d4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd90744e-7ceb-43ab-8982-36c681299d4c", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416903040400, "endTime": 30417885188700}, "additional": {"logType": "info", "children": [], "durationId": "e1516bab-78dc-4556-a1b0-3b91ac69c3d7", "parent": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}}, {"head": {"id": "c49372dc-7db0-42ba-b352-f3d2e749b847", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30417885211900, "endTime": 30417894864900}, "additional": {"children": ["08ac779c-b90e-4eef-9586-8df41d136fb4", "a0f9844c-2327-432e-a374-0f2c4ccdf463", "ea9ff2eb-8224-4c06-b356-955310baefc3"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "logId": "0ab2274f-0dc5-4562-b470-ef69cefe16af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ab2274f-0dc5-4562-b470-ef69cefe16af", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30417885211900, "endTime": 30417894864900}, "additional": {"logType": "info", "children": ["dcaffece-7ed6-435a-b991-81e616a4b511", "d3f3b23b-77f6-4bd7-a1bf-c17a1a496b56", "485f950b-202e-4816-92b6-7e9161e8b8f8"], "durationId": "c49372dc-7db0-42ba-b352-f3d2e749b847", "parent": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}}, {"head": {"id": "08ac779c-b90e-4eef-9586-8df41d136fb4", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30417885232300, "endTime": 30417885239700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c49372dc-7db0-42ba-b352-f3d2e749b847", "logId": "dcaffece-7ed6-435a-b991-81e616a4b511"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcaffece-7ed6-435a-b991-81e616a4b511", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30417885232300, "endTime": 30417885239700}, "additional": {"logType": "info", "children": [], "durationId": "08ac779c-b90e-4eef-9586-8df41d136fb4", "parent": "0ab2274f-0dc5-4562-b470-ef69cefe16af"}}, {"head": {"id": "a0f9844c-2327-432e-a374-0f2c4ccdf463", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30417885243200, "endTime": 30417885796700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c49372dc-7db0-42ba-b352-f3d2e749b847", "logId": "d3f3b23b-77f6-4bd7-a1bf-c17a1a496b56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3f3b23b-77f6-4bd7-a1bf-c17a1a496b56", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30417885243200, "endTime": 30417885796700}, "additional": {"logType": "info", "children": [], "durationId": "a0f9844c-2327-432e-a374-0f2c4ccdf463", "parent": "0ab2274f-0dc5-4562-b470-ef69cefe16af"}}, {"head": {"id": "ea9ff2eb-8224-4c06-b356-955310baefc3", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30417885800500, "endTime": 30417894850800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c49372dc-7db0-42ba-b352-f3d2e749b847", "logId": "485f950b-202e-4816-92b6-7e9161e8b8f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "485f950b-202e-4816-92b6-7e9161e8b8f8", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30417885800500, "endTime": 30417894850800}, "additional": {"logType": "info", "children": [], "durationId": "ea9ff2eb-8224-4c06-b356-955310baefc3", "parent": "0ab2274f-0dc5-4562-b470-ef69cefe16af"}}, {"head": {"id": "6d639659-e513-4ab4-a351-fd59eda6247e", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30417894879300, "endTime": 30418015219000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "logId": "6358ea25-73d3-49b5-bca4-568aa66495e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6358ea25-73d3-49b5-bca4-568aa66495e4", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30417894879300, "endTime": 30418015219000}, "additional": {"logType": "info", "children": [], "durationId": "6d639659-e513-4ab4-a351-fd59eda6247e", "parent": "8f64c794-8068-4a89-a94d-48f419e4bb6c"}}, {"head": {"id": "ab12009d-21c0-484c-a61d-355b79ff4aed", "name": "default@CompileArkTS work[51] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418027925700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f64c794-8068-4a89-a94d-48f419e4bb6c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30416898395100, "endTime": 30418015403900}, "additional": {"logType": "info", "children": ["bdba89f2-f33e-4fd4-b4d5-a69164ff88da", "4139ecb8-951d-4df4-b9d7-d6bda0defbea", "a8acaef9-8698-445f-8102-4a79b35dd2e3", "fd90744e-7ceb-43ab-8982-36c681299d4c", "0ab2274f-0dc5-4562-b470-ef69cefe16af", "6358ea25-73d3-49b5-bca4-568aa66495e4"], "durationId": "ffe362c3-7de5-4530-8d18-5ef47c9ceb56", "parent": "c69a1d5d-a42d-4f67-a396-b7ce432d10c3"}}, {"head": {"id": "aa7ec123-2d29-4c9e-96c1-ed46e01095d8", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418409923100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2ce72a6-987d-4234-80a0-47eba860747e", "name": "CopyResources is end, endTime: 30418410086200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418410090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98d00d0d-61cd-4aa0-b81e-d56c30ed45d0", "name": "default@CompileArkTS work[52] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418410280600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83331385-7501-4849-a901-a1f02e19690b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30418396714300, "endTime": 30418409747200}, "additional": {"logType": "info", "children": [], "durationId": "7e9b69bb-2207-43bd-bb2f-724187ca07bd", "parent": "c69a1d5d-a42d-4f67-a396-b7ce432d10c3"}}, {"head": {"id": "c69a1d5d-a42d-4f67-a396-b7ce432d10c3", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416758886900, "endTime": 30418410395700, "totalTime": 1159215700}, "additional": {"logType": "info", "children": ["8f64c794-8068-4a89-a94d-48f419e4bb6c", "83331385-7501-4849-a901-a1f02e19690b"], "durationId": "85649067-e96b-4ae4-afed-19f37cd492ef"}}, {"head": {"id": "34781234-791d-4ff7-8afc-246b9d1a9cc4", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418415631900, "endTime": 30418416866400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "1a276c4f-a32e-4a89-af3f-8dde754d041d", "logId": "da6ffc36-bdd7-4a20-8fd3-6d4e6733a0f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a276c4f-a32e-4a89-af3f-8dde754d041d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418414278100}, "additional": {"logType": "detail", "children": [], "durationId": "34781234-791d-4ff7-8afc-246b9d1a9cc4"}}, {"head": {"id": "89679753-bf43-45a0-9a64-d405ae3ba4e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418414625700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7191f04d-0a2d-4f51-9667-edf6d3edec17", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418414720200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e67405-db9d-41d4-aefe-cdb440c7d9a5", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418415641200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "532f8c11-293a-46cc-92f2-db9937e29b2d", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418415846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a495dffb-03a2-4eef-93cb-30987c0705e8", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418416533700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "857fcfeb-4cfe-4baf-b143-85162d4171d3", "name": "entry : default@GeneratePkgModuleJson cost memory 0.075286865234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418416721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6ffc36-bdd7-4a20-8fd3-6d4e6733a0f0", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418415631900, "endTime": 30418416866400}, "additional": {"logType": "info", "children": [], "durationId": "34781234-791d-4ff7-8afc-246b9d1a9cc4"}}, {"head": {"id": "3f501ea0-144c-4745-8681-a2f4877c0ab0", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418427456000, "endTime": 30418895770600}, "additional": {"children": ["9440052c-5693-4caa-b0e3-6fca3fb7f0ee", "6c64b6ad-659d-4647-a0ed-846071ee056a", "4f23618b-efae-46a6-bdd9-dda720650fc0"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "638a203e-6876-4671-a1eb-9418c3322a9d", "logId": "ebf3c063-3a6d-4422-800d-68af41b473b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "638a203e-6876-4671-a1eb-9418c3322a9d", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418419926200}, "additional": {"logType": "detail", "children": [], "durationId": "3f501ea0-144c-4745-8681-a2f4877c0ab0"}}, {"head": {"id": "fb2c7501-005d-4664-a4ee-34a7fe4b829c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418420321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2128a709-2fb3-4617-9ddd-7f4c12f5aec4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418420426300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d90086-db80-4f8f-bb3b-bf698f1b6470", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418427468900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f861c0d9-51e9-41ed-92fc-c51bbb52e644", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418438861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a48df6f1-2d26-4a63-9f9e-04b4116fccfd", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418439014800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f367b82-fcd9-4290-b929-3baff5ef26bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418439212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3a923b-35b1-4aae-b0df-5ecc0608af7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418439294700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9440052c-5693-4caa-b0e3-6fca3fb7f0ee", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418440175900, "endTime": 30418441587400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f501ea0-144c-4745-8681-a2f4877c0ab0", "logId": "bd058cff-c41c-48e8-84b6-e8304a14c477"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "661b92af-7441-4754-bc3c-27baeb0e48e8", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418441419200}, "additional": {"logType": "debug", "children": [], "durationId": "3f501ea0-144c-4745-8681-a2f4877c0ab0"}}, {"head": {"id": "bd058cff-c41c-48e8-84b6-e8304a14c477", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418440175900, "endTime": 30418441587400}, "additional": {"logType": "info", "children": [], "durationId": "9440052c-5693-4caa-b0e3-6fca3fb7f0ee", "parent": "ebf3c063-3a6d-4422-800d-68af41b473b0"}}, {"head": {"id": "6c64b6ad-659d-4647-a0ed-846071ee056a", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418442237100, "endTime": 30418444042000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f501ea0-144c-4745-8681-a2f4877c0ab0", "logId": "e4690496-1e01-4e1b-9463-0eda80541079"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e55498ac-355f-4397-afe1-f9ddc92fbac3", "name": "default@PackageHap work[53] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418443105200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f23618b-efae-46a6-bdd9-dda720650fc0", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30418444005700, "endTime": 30418895302100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3f501ea0-144c-4745-8681-a2f4877c0ab0", "logId": "79ad68ce-dd22-4f65-987e-0dc8fcb7fe19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9d5e4e5-2758-4795-840f-2e4e1d75b0a2", "name": "default@PackageHap work[53] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418443776600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8e959e-2fdf-468e-ab3d-a819a389b703", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418443850600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d50a830d-31ea-4994-bf7a-6527ab855a13", "name": "default@PackageHap work[53] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418443940100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43327bf4-6888-412e-b337-ec703d3f532c", "name": "default@PackageHap work[53] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418443992800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4690496-1e01-4e1b-9463-0eda80541079", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418442237100, "endTime": 30418444042000}, "additional": {"logType": "info", "children": [], "durationId": "6c64b6ad-659d-4647-a0ed-846071ee056a", "parent": "ebf3c063-3a6d-4422-800d-68af41b473b0"}}, {"head": {"id": "099685d3-f1cb-4000-afb4-ff911d24d49e", "name": "entry : default@PackageHap cost memory 1.2819061279296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418448016000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678f4d30-3d2b-4dbe-ade0-323f86a6d3e9", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418463210600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adcd712b-19ea-448d-8310-24d940789233", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418525266300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c259152d-3c77-415b-a1fc-6c2b7e8d7277", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418895397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e4a44c-02c4-4852-8af0-ea340d9dc6bf", "name": "default@PackageHap work[53] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418895625200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ad68ce-dd22-4f65-987e-0dc8fcb7fe19", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30418444005700, "endTime": 30418895302100}, "additional": {"logType": "info", "children": [], "durationId": "4f23618b-efae-46a6-bdd9-dda720650fc0", "parent": "ebf3c063-3a6d-4422-800d-68af41b473b0"}}, {"head": {"id": "ebf3c063-3a6d-4422-800d-68af41b473b0", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418427456000, "endTime": 30418895770600, "totalTime": 467853000}, "additional": {"logType": "info", "children": ["bd058cff-c41c-48e8-84b6-e8304a14c477", "e4690496-1e01-4e1b-9463-0eda80541079", "79ad68ce-dd22-4f65-987e-0dc8fcb7fe19"], "durationId": "3f501ea0-144c-4745-8681-a2f4877c0ab0"}}, {"head": {"id": "4e33d768-7072-4c5e-8c8a-07b937ef9d89", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418905482300, "endTime": 30418910321300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "fd164fc1-26a5-49ad-b75b-de5f727fd2bc", "logId": "3163ca56-3de7-43e4-bf94-9364ee59ec65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd164fc1-26a5-49ad-b75b-de5f727fd2bc", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418900836900}, "additional": {"logType": "detail", "children": [], "durationId": "4e33d768-7072-4c5e-8c8a-07b937ef9d89"}}, {"head": {"id": "dc05756a-9d9a-4977-b805-81b3059bbef0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418901446000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5864ee52-9ecb-423b-9637-582c8ff58819", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418901601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da4d1f6-26a4-497f-9356-57cb6d38960d", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418905494900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240c3fec-f326-490f-8c56-d9df3defed50", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418906441900}, "additional": {"logType": "warn", "children": [], "durationId": "4e33d768-7072-4c5e-8c8a-07b937ef9d89"}}, {"head": {"id": "008005b1-54a4-449f-9a61-2b7e435dcd9e", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418907451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "943362d4-8d00-46ad-9754-78d8f82443d9", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418907581200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfab62f1-2358-42a3-9465-69ab7c7e602c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418908059100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36bc294d-fbc2-4b3f-8119-0bd7c7aaa548", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418908820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a539a23-adbc-4d98-b5aa-b5a37f817650", "name": "entry : default@SignHap cost memory 0.13103485107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418909679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4f7342-7d45-476d-86c1-a2358f135bba", "name": "runTaskFromQueue task cost before running: 2 s 880 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418910052400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3163ca56-3de7-43e4-bf94-9364ee59ec65", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418905482300, "endTime": 30418910321300, "totalTime": 4398100}, "additional": {"logType": "info", "children": [], "durationId": "4e33d768-7072-4c5e-8c8a-07b937ef9d89"}}, {"head": {"id": "f9a2813b-53e4-4689-9f56-81b404f8f556", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418915008100, "endTime": 30418921798400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4364bf7a-9a08-47ca-b945-382b63986194", "logId": "d52236f7-ca8e-4f5a-b2f2-2d275b5f71b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4364bf7a-9a08-47ca-b945-382b63986194", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418912460800}, "additional": {"logType": "detail", "children": [], "durationId": "f9a2813b-53e4-4689-9f56-81b404f8f556"}}, {"head": {"id": "54fa9dc2-6f9e-4022-89a0-1314506a8d32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418913686800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f7d803-05e8-4122-a242-da4c940c233e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418913854400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5915f164-3924-4e8f-a007-49ca91771bf5", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418915022500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7146a472-de35-488c-a0d1-2baf93420d40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418920522200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278ef416-16e7-477f-8666-7f1cf6ac6209", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418921319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8046f79a-503e-482b-9cbc-d1b7027c2cdc", "name": "entry : default@CollectDebugSymbol cost memory 0.2534332275390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418921437400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "931b1e64-e87e-49a9-94af-061d5bde8adf", "name": "runTaskFromQueue task cost before running: 2 s 891 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418921525800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d52236f7-ca8e-4f5a-b2f2-2d275b5f71b2", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418915008100, "endTime": 30418921798400, "totalTime": 6498000}, "additional": {"logType": "info", "children": [], "durationId": "f9a2813b-53e4-4689-9f56-81b404f8f556"}}, {"head": {"id": "7ebee3ff-12ec-4d05-b30c-770e8e009280", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418924450400, "endTime": 30418925813100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "43134226-fd20-485e-b873-ac0bc5fd144b", "logId": "90eb336f-1d41-45d5-8c57-38e51804cfb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43134226-fd20-485e-b873-ac0bc5fd144b", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418924085000}, "additional": {"logType": "detail", "children": [], "durationId": "7ebee3ff-12ec-4d05-b30c-770e8e009280"}}, {"head": {"id": "eea229c9-ce47-47aa-b731-d54a7a12e558", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418924468400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97c4a8e-9073-4916-b28d-e9034256b1bc", "name": "entry : assembleHap cost memory 0.0115509033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418925558600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f888fff-1ad0-4682-bca9-95e1c74d13a6", "name": "runTaskFromQueue task cost before running: 2 s 896 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418925729200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90eb336f-1d41-45d5-8c57-38e51804cfb9", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418924450400, "endTime": 30418925813100, "totalTime": 1557700}, "additional": {"logType": "info", "children": [], "durationId": "7ebee3ff-12ec-4d05-b30c-770e8e009280"}}, {"head": {"id": "872c872e-c133-4fbb-bffd-90c988c1bc35", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418934048400, "endTime": 30418934073900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a40af2d2-b9b2-4ffd-b8f9-f0c072441c4b", "logId": "3037b8d2-dea5-4d63-aae9-346c604b1f9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3037b8d2-dea5-4d63-aae9-346c604b1f9c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418934048400, "endTime": 30418934073900}, "additional": {"logType": "info", "children": [], "durationId": "872c872e-c133-4fbb-bffd-90c988c1bc35"}}, {"head": {"id": "6386f471-ba3b-46dd-aff1-12318875092e", "name": "BUILD SUCCESSFUL in 2 s 904 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418934171600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d8cbe410-b339-4466-81a4-6f5861b4c97a", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30416030564400, "endTime": 30418934733000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9eef37a8-9e65-47fe-ab8e-0d4ba053836f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418934867200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "471f0573-08ce-4e61-9ce2-f9a93d3428bf", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418934962500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e34516-3fe4-4ce1-bdbb-25949bc8d4c6", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418935020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7275ed4b-35a9-4c4f-808d-1c3cba7a2555", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418935068200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d822817-54cf-4933-bbea-d9afbf7d173c", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418935166400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8644c461-7cfb-42c8-b69d-19347229e771", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418935481000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399a75a2-ea8b-4134-a13f-8e96f115a382", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418936093900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f689c0c-2082-4750-a5e6-804ee65202e5", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418936467500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3d8d1d-1c5c-4fee-8109-d3898798da45", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418936553200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f27214c-5793-4d5d-a225-c6702827d35d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418936803600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc6a2fd-8bd5-45fd-8132-78d9d6c0ade0", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418937072800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eaa9063-1efc-482a-8322-ac280b7eae9d", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418937858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6ff5dbd-82a2-4e27-a947-871dbd96f47b", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418938145200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70623e1a-1f42-4dba-a775-f9926f060d07", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418938227000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ca32f6-18fc-4cca-9e65-c92a4ccac657", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418938282100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f656b7-8729-4e9f-bd96-a74f25173b60", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418938330500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30aafe4a-3bd9-4403-be8b-0339934791b5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418938377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac9b692-bc30-44d7-8962-5877d0ea1f45", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418938918900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95ec80af-e0c4-432e-8b16-e2cc5c52f9e8", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418939436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88bc4126-1922-451a-8703-28ae90097733", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418939919900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06e5d0fc-57e6-41fc-99b9-480336822b47", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418940279200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e633c226-82ae-4907-920d-09b041fe126b", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418940357700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe0d27b-58a6-4c36-99f9-efe6fd3818c9", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418940417300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21014222-05fe-4438-a98c-a1e2d73ee97c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418943301100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d72abb0d-275c-492e-b2c8-cab0a9aafd79", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418944394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82bfd144-7439-42d5-9420-ddd5ead2d5c2", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418945455100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d9e0da-787b-429a-adca-5b6286f26c43", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418945882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f83d5f91-12b9-4bdb-a6e7-a04184b6dcab", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418946111700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f5ebb7f-2c7a-40cb-9a86-d6d117270ebb", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418946983300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9efa388-4c9e-403e-838d-ff82dd31894d", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418947294400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2506ec-314a-4270-ba18-d53e50fd6183", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418947697600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566050f1-e574-47ab-8160-a7741029f21f", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418948203500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07241b04-6c3d-4c83-8b9f-a2567daa43c7", "name": "Incremental task entry:default@CompileArkTS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418949129700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb14eea-304e-4adc-9115-f7434b4f39a4", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418950374600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010a69b4-68c1-4ab8-8c13-0492e3a5c633", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418950808900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3821529-1d03-45a2-99e9-fe5ec9241e26", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418951811900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67212588-7ecd-4b43-b18b-4a24dcd0ce29", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418952030500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3248e993-81a3-4ff7-bcf0-9ddb10b0f94f", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418952248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6161d8af-b55c-48b8-a4bd-b9ce4519f5d7", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418952770000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c2dd5d6-fe23-43cc-9d87-e262e8f00e11", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418953016400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18dd9db5-6ea4-4afd-af38-a9102e6a5228", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418953097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04b1fdc-5589-48e3-9fb9-50b5eb36047d", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418953214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e01fa4a-766a-4eaf-85c1-c8a63d5c0234", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418954101200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678f28df-9f17-4a82-adc5-b2af848ea16d", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418954367300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bd97d4f-6f63-4311-89ab-6c3b13b0c47c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418954573700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3317f5-712b-453c-932e-a82949ad7109", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418964981200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b9d78c6-b937-4421-87bc-0a3bf3a7b68e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418965354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d0060c2-b11d-481b-b036-2f56a591d318", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418965728500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f1446f0-8b66-476b-9d0c-a6a04a9deb93", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418965825100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a523b6f-f8a8-4090-9f49-a85857fcfa83", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418966032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb7ccc7-3ab9-4845-b394-0db4e4933782", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418966761100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e153de6-bdc2-4e30-a4bf-a8c4d83a414c", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418967094300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d531f5c7-5f0c-4e68-ae70-fe0645dfe5bb", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418967421600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6beafddc-666a-4d2e-ab01-211a712909a0", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418967803400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7586d1f0-469c-4270-ae7e-2e28246e4c8b", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418968036000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edfb5f2a-20cd-4795-90f6-91dc3d42d296", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418968155100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50dc42b-1f31-4467-881b-fa7ba2c89789", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418968484900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f769bb7b-5e66-4df8-8cea-832dd48b0199", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418970983900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37fcbbd-3d86-485e-a1cf-966652d89a6a", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418971418000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c55d47-0e1f-4cda-a0ba-3cbee2c9cbb5", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418972015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e76b46-3bd3-423f-bd11-4b44c8f9ad92", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30418972414700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}