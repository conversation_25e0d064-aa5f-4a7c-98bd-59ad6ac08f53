{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "58045e4b-172a-478b-afa9-41a791cbb6a2", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186538556700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db1b2f7-a345-4d77-9bf3-15dd0e5ba7ed", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186542654000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1688c56-7512-41b5-ac41-6d575015a97c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186542920800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbbf9c03-df7b-4ed1-9a2e-86608adddcdd", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186548282700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f931c0d-2486-46f9-9ce7-9c3ae7128330", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427443770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427452002300, "endTime": 34427825034700}, "additional": {"children": ["589a79dd-6c70-4f21-977e-fc2014eb6fde", "6546a7ec-640c-472d-aa26-920caedc0a62", "b7f54515-bce0-411f-8cbb-bee826dcbf01", "ca488376-9e3a-48a7-95c3-e4ec3cee7189", "f82963bd-1565-4d81-8321-1afbf2061f98", "d0cb7120-20b3-4c86-b776-ef1a103bdc8d", "c5486ee2-5efd-4d06-aa27-7a4bd557c49a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "589a79dd-6c70-4f21-977e-fc2014eb6fde", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427452004400, "endTime": 34427468906900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "a4992e06-f23f-4470-a8f9-090eef6078da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6546a7ec-640c-472d-aa26-920caedc0a62", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427468925200, "endTime": 34427823897800}, "additional": {"children": ["50c086da-b3e9-458d-9296-8ac0f3f57188", "62eeac09-377e-41f5-8aa6-cd728346788c", "c6c991ed-f1c3-4622-ae39-f2db08456f07", "dc44e67f-6407-41d7-843b-6a48f6500aeb", "49dded61-ae10-483f-8acb-cdd17ea80e41", "dd44b818-e6c8-47ce-b7bc-5e272bd92823", "7cdc5fd9-ce9b-4d03-977f-a0f51ed7b529", "d6e5a346-9ee6-44af-95d9-4008814d8c4c", "a2ffd742-f43a-48fc-be8b-39e25ac9f954"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "cfb5065b-660f-476b-a030-bd8789eb3d71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7f54515-bce0-411f-8cbb-bee826dcbf01", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427823917700, "endTime": 34427825026200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "d75e8dc0-7fea-428e-9e82-70c5b524733e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca488376-9e3a-48a7-95c3-e4ec3cee7189", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427825030100, "endTime": 34427825031000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "b12ffa32-6e76-4664-8fc4-acbffeba4868"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f82963bd-1565-4d81-8321-1afbf2061f98", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427455764200, "endTime": 34427455794800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "99397519-6c62-46af-aaf4-ef8d6b93854b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99397519-6c62-46af-aaf4-ef8d6b93854b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427455764200, "endTime": 34427455794800}, "additional": {"logType": "info", "children": [], "durationId": "f82963bd-1565-4d81-8321-1afbf2061f98", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "d0cb7120-20b3-4c86-b776-ef1a103bdc8d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427461075300, "endTime": 34427461092400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "31aa49c7-713b-4b23-b103-103ba2b200d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31aa49c7-713b-4b23-b103-103ba2b200d9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427461075300, "endTime": 34427461092400}, "additional": {"logType": "info", "children": [], "durationId": "d0cb7120-20b3-4c86-b776-ef1a103bdc8d", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "43a1a4fa-c1b3-421f-a5c5-c90ca93aca09", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427461650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e4d9e5-eaea-4a6d-a817-b8b926d36d45", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427468775300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4992e06-f23f-4470-a8f9-090eef6078da", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427452004400, "endTime": 34427468906900}, "additional": {"logType": "info", "children": [], "durationId": "589a79dd-6c70-4f21-977e-fc2014eb6fde", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "50c086da-b3e9-458d-9296-8ac0f3f57188", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427475071300, "endTime": 34427475079700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "d2493815-f65a-4f75-8218-0eff75219d56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62eeac09-377e-41f5-8aa6-cd728346788c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427475093100, "endTime": 34427480811900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "97e66c80-1fdf-4a45-9ecb-c0254c466ca0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6c991ed-f1c3-4622-ae39-f2db08456f07", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427480833000, "endTime": 34427632615700}, "additional": {"children": ["645cdbbf-faea-4498-ab3a-3c5ee2061173", "ed3bd73a-5637-4a8d-a523-7175e1a8fb22", "5785bddd-a7a4-4d65-b1bb-3962e9687cac"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "6210610d-464b-40c9-b098-883d0714ff50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc44e67f-6407-41d7-843b-6a48f6500aeb", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427632629700, "endTime": 34427673304000}, "additional": {"children": ["95bfc656-ff70-47b3-8a0c-c3268940cd1c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "9771fb03-3b56-49c7-947d-270d629407c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49dded61-ae10-483f-8acb-cdd17ea80e41", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427673311100, "endTime": 34427793689400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "2277a6de-4b93-4843-a5a6-06de450fea2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd44b818-e6c8-47ce-b7bc-5e272bd92823", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427795820600, "endTime": 34427814557700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "0d65248a-e8d7-4cc9-baa1-73f628716f07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cdc5fd9-ce9b-4d03-977f-a0f51ed7b529", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427814587200, "endTime": 34427823724300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "9fe6849e-9661-425a-8fef-c499147fb64e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6e5a346-9ee6-44af-95d9-4008814d8c4c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427823744900, "endTime": 34427823888100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "99140cfa-9090-4ed8-871d-c18db4a1fe74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2493815-f65a-4f75-8218-0eff75219d56", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427475071300, "endTime": 34427475079700}, "additional": {"logType": "info", "children": [], "durationId": "50c086da-b3e9-458d-9296-8ac0f3f57188", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "97e66c80-1fdf-4a45-9ecb-c0254c466ca0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427475093100, "endTime": 34427480811900}, "additional": {"logType": "info", "children": [], "durationId": "62eeac09-377e-41f5-8aa6-cd728346788c", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "645cdbbf-faea-4498-ab3a-3c5ee2061173", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427484804500, "endTime": 34427484966700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6c991ed-f1c3-4622-ae39-f2db08456f07", "logId": "466b6c82-dae3-4c94-aa0b-0f73c5c33d6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "466b6c82-dae3-4c94-aa0b-0f73c5c33d6a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427484804500, "endTime": 34427484966700}, "additional": {"logType": "info", "children": [], "durationId": "645cdbbf-faea-4498-ab3a-3c5ee2061173", "parent": "6210610d-464b-40c9-b098-883d0714ff50"}}, {"head": {"id": "ed3bd73a-5637-4a8d-a523-7175e1a8fb22", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427490686100, "endTime": 34427631863000}, "additional": {"children": ["f584e740-9480-44a3-9f37-9f8dd23f793e", "0861420c-55cc-4cc9-a332-67d482695784"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6c991ed-f1c3-4622-ae39-f2db08456f07", "logId": "e87c6928-344b-4d25-b73e-3f1fd81abf5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f584e740-9480-44a3-9f37-9f8dd23f793e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427490689100, "endTime": 34427499316900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed3bd73a-5637-4a8d-a523-7175e1a8fb22", "logId": "f0fb1bb0-086d-4171-bcce-fa5982c97c7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0861420c-55cc-4cc9-a332-67d482695784", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427499342800, "endTime": 34427631845300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ed3bd73a-5637-4a8d-a523-7175e1a8fb22", "logId": "427d8f3c-9a8d-44b9-bb73-ee0c32237507"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30409bdf-d8ba-4256-b602-c7810239021e", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427490695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65250ffa-869a-4524-b2a7-9b08bb1a634a", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427498530800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0fb1bb0-086d-4171-bcce-fa5982c97c7e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427490689100, "endTime": 34427499316900}, "additional": {"logType": "info", "children": [], "durationId": "f584e740-9480-44a3-9f37-9f8dd23f793e", "parent": "e87c6928-344b-4d25-b73e-3f1fd81abf5d"}}, {"head": {"id": "eb2ae33a-bd4e-4bbd-b64c-0a5dfd769fb1", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427499359800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5746e855-8945-4f9e-8d95-085731494481", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427508935200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6683f577-a755-4f46-aef2-c4197c7fe1c0", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427509335200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f36687fe-b381-4235-87d9-12a10a1f7b51", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427509493500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc68cb5-3f52-4ff9-a712-265d46bc92c1", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427509582700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893b12d7-da1d-41db-8ff8-c1c41473c811", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427512455100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a73804e8-e563-4376-b387-559b8694ff9d", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427518279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf22edc-2b3c-4803-a592-236d27a7a6a4", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427535222900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3719cbd3-fef7-40d9-baf4-903b5a0231f1", "name": "Sdk init in 78 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427597336900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "036113f6-4f83-4fbc-bbce-0b661259acab", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427597685900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 18, "minute": 3}, "markType": "other"}}, {"head": {"id": "705ff804-9693-45c1-bc14-47aa203d0c6c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427597705900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 18, "minute": 3}, "markType": "other"}}, {"head": {"id": "fcf53a80-aacd-4a32-b704-9049eced7023", "name": "Project task initialization takes 33 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427631465900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3edf8c-0fd3-4a18-99c7-a7c2fa8c01ad", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427631613900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d84f501-5a93-4a9f-b766-b76742fbfea4", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427631754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6199ed-1180-48d5-86a7-32271b6859b0", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427631800900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427d8f3c-9a8d-44b9-bb73-ee0c32237507", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427499342800, "endTime": 34427631845300}, "additional": {"logType": "info", "children": [], "durationId": "0861420c-55cc-4cc9-a332-67d482695784", "parent": "e87c6928-344b-4d25-b73e-3f1fd81abf5d"}}, {"head": {"id": "e87c6928-344b-4d25-b73e-3f1fd81abf5d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427490686100, "endTime": 34427631863000}, "additional": {"logType": "info", "children": ["f0fb1bb0-086d-4171-bcce-fa5982c97c7e", "427d8f3c-9a8d-44b9-bb73-ee0c32237507"], "durationId": "ed3bd73a-5637-4a8d-a523-7175e1a8fb22", "parent": "6210610d-464b-40c9-b098-883d0714ff50"}}, {"head": {"id": "5785bddd-a7a4-4d65-b1bb-3962e9687cac", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427632581300, "endTime": 34427632597900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6c991ed-f1c3-4622-ae39-f2db08456f07", "logId": "5ed0f212-a4e7-4032-b608-fc45135c91ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ed0f212-a4e7-4032-b608-fc45135c91ff", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427632581300, "endTime": 34427632597900}, "additional": {"logType": "info", "children": [], "durationId": "5785bddd-a7a4-4d65-b1bb-3962e9687cac", "parent": "6210610d-464b-40c9-b098-883d0714ff50"}}, {"head": {"id": "6210610d-464b-40c9-b098-883d0714ff50", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427480833000, "endTime": 34427632615700}, "additional": {"logType": "info", "children": ["466b6c82-dae3-4c94-aa0b-0f73c5c33d6a", "e87c6928-344b-4d25-b73e-3f1fd81abf5d", "5ed0f212-a4e7-4032-b608-fc45135c91ff"], "durationId": "c6c991ed-f1c3-4622-ae39-f2db08456f07", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "95bfc656-ff70-47b3-8a0c-c3268940cd1c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427633453900, "endTime": 34427673294300}, "additional": {"children": ["752d054f-bee2-43b1-89af-f2cda7c511a3", "b60bc342-7bf4-428f-9407-7fc5fc9f5b65", "21653422-f985-4d2f-8810-3a82676bbc2d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc44e67f-6407-41d7-843b-6a48f6500aeb", "logId": "6a09a515-56ce-4555-a84c-86329c81917a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "752d054f-bee2-43b1-89af-f2cda7c511a3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427637773200, "endTime": 34427637793600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "95bfc656-ff70-47b3-8a0c-c3268940cd1c", "logId": "1c41bf18-9333-4053-8386-7b22bb8defa9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c41bf18-9333-4053-8386-7b22bb8defa9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427637773200, "endTime": 34427637793600}, "additional": {"logType": "info", "children": [], "durationId": "752d054f-bee2-43b1-89af-f2cda7c511a3", "parent": "6a09a515-56ce-4555-a84c-86329c81917a"}}, {"head": {"id": "b60bc342-7bf4-428f-9407-7fc5fc9f5b65", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427641119200, "endTime": 34427669854600}, "additional": {"children": ["0b4d7f4d-6f58-458e-99c0-ee9298b6ebfe", "52e9f99e-fd1b-4cc8-9201-50671b71b1c8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "95bfc656-ff70-47b3-8a0c-c3268940cd1c", "logId": "528fa56a-6e0f-421e-ac16-d4dc4ab99703"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b4d7f4d-6f58-458e-99c0-ee9298b6ebfe", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427641122600, "endTime": 34427650202400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b60bc342-7bf4-428f-9407-7fc5fc9f5b65", "logId": "742945a7-1882-4872-ae1c-a71d05b54732"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52e9f99e-fd1b-4cc8-9201-50671b71b1c8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427650219300, "endTime": 34427669839800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b60bc342-7bf4-428f-9407-7fc5fc9f5b65", "logId": "1ae14b24-613f-44eb-93ca-8cb5c8e89240"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f571aad-1ca9-4182-8e9d-54e11de68af6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427641130900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dbc9c16-057b-4465-93ed-777083636526", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427650068600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742945a7-1882-4872-ae1c-a71d05b54732", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427641122600, "endTime": 34427650202400}, "additional": {"logType": "info", "children": [], "durationId": "0b4d7f4d-6f58-458e-99c0-ee9298b6ebfe", "parent": "528fa56a-6e0f-421e-ac16-d4dc4ab99703"}}, {"head": {"id": "67911305-085c-4bed-ade6-0759c795b7b8", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427650229300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c83104b8-357e-45d5-8d62-cfb92a073f1c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427664690900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70724ba9-7149-4ff1-b3e3-fdd1c1405390", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427664826400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d891e40-a31e-4607-b00c-42ae83c2ae15", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427665394100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a9013a3-be0c-43b0-b97b-72653961bb50", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427665699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff189152-197c-4ecc-8e0d-f63e10d1e704", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427665788900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65bac571-efe9-4be3-a4fd-bcc4c44439ec", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427665842000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cbc3fd2-1952-4616-a333-83cf0b40fe52", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427665898700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceed2adb-088d-407a-acd6-9278c419282f", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427669456400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "182f10b0-c1d1-47e6-b014-8603820779fe", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427669666100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52e139b-68b5-4941-aefa-17a509db40ba", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427669746100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8db49831-edbe-44ad-8a02-2194e08b3e9e", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427669795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae14b24-613f-44eb-93ca-8cb5c8e89240", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427650219300, "endTime": 34427669839800}, "additional": {"logType": "info", "children": [], "durationId": "52e9f99e-fd1b-4cc8-9201-50671b71b1c8", "parent": "528fa56a-6e0f-421e-ac16-d4dc4ab99703"}}, {"head": {"id": "528fa56a-6e0f-421e-ac16-d4dc4ab99703", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427641119200, "endTime": 34427669854600}, "additional": {"logType": "info", "children": ["742945a7-1882-4872-ae1c-a71d05b54732", "1ae14b24-613f-44eb-93ca-8cb5c8e89240"], "durationId": "b60bc342-7bf4-428f-9407-7fc5fc9f5b65", "parent": "6a09a515-56ce-4555-a84c-86329c81917a"}}, {"head": {"id": "21653422-f985-4d2f-8810-3a82676bbc2d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427673255600, "endTime": 34427673276900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "95bfc656-ff70-47b3-8a0c-c3268940cd1c", "logId": "c15e8a3b-9abc-482d-8652-c6a4e2f6bef9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c15e8a3b-9abc-482d-8652-c6a4e2f6bef9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427673255600, "endTime": 34427673276900}, "additional": {"logType": "info", "children": [], "durationId": "21653422-f985-4d2f-8810-3a82676bbc2d", "parent": "6a09a515-56ce-4555-a84c-86329c81917a"}}, {"head": {"id": "6a09a515-56ce-4555-a84c-86329c81917a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427633453900, "endTime": 34427673294300}, "additional": {"logType": "info", "children": ["1c41bf18-9333-4053-8386-7b22bb8defa9", "528fa56a-6e0f-421e-ac16-d4dc4ab99703", "c15e8a3b-9abc-482d-8652-c6a4e2f6bef9"], "durationId": "95bfc656-ff70-47b3-8a0c-c3268940cd1c", "parent": "9771fb03-3b56-49c7-947d-270d629407c8"}}, {"head": {"id": "9771fb03-3b56-49c7-947d-270d629407c8", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427632629700, "endTime": 34427673304000}, "additional": {"logType": "info", "children": ["6a09a515-56ce-4555-a84c-86329c81917a"], "durationId": "dc44e67f-6407-41d7-843b-6a48f6500aeb", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "23b8505d-aed1-451b-9180-8b18d3014cf2", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427702723500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c25091-9afb-4066-b19d-05b044532f55", "name": "hvigorfile, resolve hvigorfile dependencies in 121 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427793568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2277a6de-4b93-4843-a5a6-06de450fea2e", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427673311100, "endTime": 34427793689400}, "additional": {"logType": "info", "children": [], "durationId": "49dded61-ae10-483f-8acb-cdd17ea80e41", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "a2ffd742-f43a-48fc-be8b-39e25ac9f954", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427794348700, "endTime": 34427795789600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6546a7ec-640c-472d-aa26-920caedc0a62", "logId": "bc89d61c-9a22-4a04-93cf-73e7d30c4369"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6105f828-d490-41fc-8838-fcfca656b57f", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427794371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc89d61c-9a22-4a04-93cf-73e7d30c4369", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427794348700, "endTime": 34427795789600}, "additional": {"logType": "info", "children": [], "durationId": "a2ffd742-f43a-48fc-be8b-39e25ac9f954", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "b14def4a-553a-4526-927f-9217ddee6530", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427797391900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c0263f0-7b7c-402a-a7ff-dc56eb003edc", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427813874000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d65248a-e8d7-4cc9-baa1-73f628716f07", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427795820600, "endTime": 34427814557700}, "additional": {"logType": "info", "children": [], "durationId": "dd44b818-e6c8-47ce-b7bc-5e272bd92823", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "fb105912-0e87-4272-9fd7-7c36c4e94add", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427818815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db8ce48-e22d-4857-a111-2cf4223ab716", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427818943300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba7b9da-5449-4a0e-b1ad-450bdc037706", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427820967100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5164ee8f-929d-4db3-a181-2d076abec4e5", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427821089800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fe6849e-9661-425a-8fef-c499147fb64e", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427814587200, "endTime": 34427823724300}, "additional": {"logType": "info", "children": [], "durationId": "7cdc5fd9-ce9b-4d03-977f-a0f51ed7b529", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "068c346f-fd8d-4ab3-8701-f7879c153fcd", "name": "Configuration phase cost:349 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427823765700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99140cfa-9090-4ed8-871d-c18db4a1fe74", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427823744900, "endTime": 34427823888100}, "additional": {"logType": "info", "children": [], "durationId": "d6e5a346-9ee6-44af-95d9-4008814d8c4c", "parent": "cfb5065b-660f-476b-a030-bd8789eb3d71"}}, {"head": {"id": "cfb5065b-660f-476b-a030-bd8789eb3d71", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427468925200, "endTime": 34427823897800}, "additional": {"logType": "info", "children": ["d2493815-f65a-4f75-8218-0eff75219d56", "97e66c80-1fdf-4a45-9ecb-c0254c466ca0", "6210610d-464b-40c9-b098-883d0714ff50", "9771fb03-3b56-49c7-947d-270d629407c8", "2277a6de-4b93-4843-a5a6-06de450fea2e", "0d65248a-e8d7-4cc9-baa1-73f628716f07", "9fe6849e-9661-425a-8fef-c499147fb64e", "99140cfa-9090-4ed8-871d-c18db4a1fe74", "bc89d61c-9a22-4a04-93cf-73e7d30c4369"], "durationId": "6546a7ec-640c-472d-aa26-920caedc0a62", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "c5486ee2-5efd-4d06-aa27-7a4bd557c49a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427825002300, "endTime": 34427825016300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa7cf006-8eb7-41de-a98d-b5f07477cb76", "logId": "f855cc88-fef9-49ad-a995-dec4457f6ddb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f855cc88-fef9-49ad-a995-dec4457f6ddb", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427825002300, "endTime": 34427825016300}, "additional": {"logType": "info", "children": [], "durationId": "c5486ee2-5efd-4d06-aa27-7a4bd557c49a", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "d75e8dc0-7fea-428e-9e82-70c5b524733e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427823917700, "endTime": 34427825026200}, "additional": {"logType": "info", "children": [], "durationId": "b7f54515-bce0-411f-8cbb-bee826dcbf01", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "b12ffa32-6e76-4664-8fc4-acbffeba4868", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427825030100, "endTime": 34427825031000}, "additional": {"logType": "info", "children": [], "durationId": "ca488376-9e3a-48a7-95c3-e4ec3cee7189", "parent": "98cb295e-5fe5-45bb-a2f2-82b2500314ca"}}, {"head": {"id": "98cb295e-5fe5-45bb-a2f2-82b2500314ca", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427452002300, "endTime": 34427825034700}, "additional": {"logType": "info", "children": ["a4992e06-f23f-4470-a8f9-090eef6078da", "cfb5065b-660f-476b-a030-bd8789eb3d71", "d75e8dc0-7fea-428e-9e82-70c5b524733e", "b12ffa32-6e76-4664-8fc4-acbffeba4868", "99397519-6c62-46af-aaf4-ef8d6b93854b", "31aa49c7-713b-4b23-b103-103ba2b200d9", "f855cc88-fef9-49ad-a995-dec4457f6ddb"], "durationId": "aa7cf006-8eb7-41de-a98d-b5f07477cb76"}}, {"head": {"id": "b721f866-0951-4085-b957-e010d496e9e6", "name": "Configuration task cost before running: 377 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427825167100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae87260e-267c-41f3-b5af-233277c56f74", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427829352400, "endTime": 34427835655000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "03c831ae-1915-497f-8e68-c129fbc8a454", "logId": "c7e5aedd-d36d-4685-8074-f2ab358fb09a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03c831ae-1915-497f-8e68-c129fbc8a454", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427826392600}, "additional": {"logType": "detail", "children": [], "durationId": "ae87260e-267c-41f3-b5af-233277c56f74"}}, {"head": {"id": "aa2fb8f7-617f-43b7-b455-e70e6afd17a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427826721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7bb70d0-34bf-4c93-a71a-bf27dfe99bdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427826807400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c62f42e-a555-407c-bcf9-bbae923b7afb", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427829365900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c3a168-8abb-452e-9d04-6a66c1c831d1", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427835432600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070dc700-fbf9-43da-a1c2-6f56155fc315", "name": "entry : default@PreBuild cost memory 0.447052001953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427835571900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e5aedd-d36d-4685-8074-f2ab358fb09a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427829352400, "endTime": 34427835655000}, "additional": {"logType": "info", "children": [], "durationId": "ae87260e-267c-41f3-b5af-233277c56f74"}}, {"head": {"id": "108891a2-6618-4227-9ad0-5f30d9e95c49", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427840795200, "endTime": 34427842836800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b8cb816a-9aa7-4f6f-9b3d-ea64a33bc4a9", "logId": "88dbfa3b-9fd0-44ed-b8ca-fe4c6c628482"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8cb816a-9aa7-4f6f-9b3d-ea64a33bc4a9", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427839016500}, "additional": {"logType": "detail", "children": [], "durationId": "108891a2-6618-4227-9ad0-5f30d9e95c49"}}, {"head": {"id": "14da038a-62d1-46e2-98e9-1495d4e55e81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427839513000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad35282c-80de-4a74-845b-b28ee68b1838", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427839680500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75f97d32-eaa0-4732-a854-227a8bd3e74f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427840804700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a771fb3a-d674-46da-a033-db5b8ce5d450", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427841579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae9be5c-c3db-4b34-aae9-dc3a25c843b7", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427842566100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5cbd61a-8990-4be1-bf71-56a2a1702424", "name": "entry : default@GenerateMetadata cost memory 0.0905914306640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427842751500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88dbfa3b-9fd0-44ed-b8ca-fe4c6c628482", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427840795200, "endTime": 34427842836800}, "additional": {"logType": "info", "children": [], "durationId": "108891a2-6618-4227-9ad0-5f30d9e95c49"}}, {"head": {"id": "e55b7d59-2e14-49dc-94c8-847850d51a11", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845242100, "endTime": 34427845709600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "536a67c8-7f48-458f-b24e-b958be67e4ed", "logId": "1f9ad359-7923-475c-b27f-acd4833d3851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "536a67c8-7f48-458f-b24e-b958be67e4ed", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427844405900}, "additional": {"logType": "detail", "children": [], "durationId": "e55b7d59-2e14-49dc-94c8-847850d51a11"}}, {"head": {"id": "0eae992f-c4ea-4653-9c77-875a11543d01", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427844877300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad209a4-84b8-49e9-97d9-3be43b0c07f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427844995500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449a1c8c-4c64-4f64-9d1f-d5c0596bb8e2", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f6dbf4-a777-42b0-813c-7b067e644194", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b3e65d-f09f-44c3-94af-fe6787aa0ce5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845503000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64292121-678a-4c48-9f2e-65388e06a6c3", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845571700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8970ad7-55d2-4a28-98e8-e8f6f84b8a4d", "name": "runTaskFromQueue task cost before running: 397 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845657700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9ad359-7923-475c-b27f-acd4833d3851", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427845242100, "endTime": 34427845709600, "totalTime": 399400}, "additional": {"logType": "info", "children": [], "durationId": "e55b7d59-2e14-49dc-94c8-847850d51a11"}}, {"head": {"id": "b2ec1b9e-48d8-41c7-9d8d-c60cea43bc22", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427849731500, "endTime": 34427851856000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "344df808-0d57-4d5d-b1d1-29de30fd63fa", "logId": "d872f5b4-859c-4b22-8a94-ee18604f96b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "344df808-0d57-4d5d-b1d1-29de30fd63fa", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427848094400}, "additional": {"logType": "detail", "children": [], "durationId": "b2ec1b9e-48d8-41c7-9d8d-c60cea43bc22"}}, {"head": {"id": "08252ced-4bf7-4577-84dc-b53376381fdb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427848573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ab0e91-b06d-4ef7-aa89-1216c8fdd23b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427848970000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d63633fb-6fb5-49cd-a07c-f91e8ee0260e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427849740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55cff193-67d7-49fb-9c31-b77e6d16a63f", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427851645600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3899bea6-5b18-437d-9291-89e60f806965", "name": "entry : default@MergeProfile cost memory 0.10521697998046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427851782500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d872f5b4-859c-4b22-8a94-ee18604f96b3", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427849731500, "endTime": 34427851856000}, "additional": {"logType": "info", "children": [], "durationId": "b2ec1b9e-48d8-41c7-9d8d-c60cea43bc22"}}, {"head": {"id": "f6cc6cf1-d746-4e39-bbdd-e4aab89f7eec", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427855524800, "endTime": 34427858235400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5fba9876-5ba1-4657-9f96-5779acdc770a", "logId": "9fdf3dd6-8bd4-4ef5-9543-e456611ea1a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fba9876-5ba1-4657-9f96-5779acdc770a", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427853807100}, "additional": {"logType": "detail", "children": [], "durationId": "f6cc6cf1-d746-4e39-bbdd-e4aab89f7eec"}}, {"head": {"id": "726156b1-ae5c-484b-8152-354e46f52b43", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427854195800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ea87308-7819-487e-93ec-c486affdf5a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427854325400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae07029b-7025-49a8-a079-854a203ac1c6", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427855536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fb0a965-535c-4bb3-9ff9-a74a190476f9", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427856662100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace1bc9e-b76e-465b-8ac6-1c431e388417", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427857984100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3f0237-15ed-41b7-a121-9bc1d0cf9729", "name": "entry : default@CreateBuildProfile cost memory 0.09960174560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427858137000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fdf3dd6-8bd4-4ef5-9543-e456611ea1a7", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427855524800, "endTime": 34427858235400}, "additional": {"logType": "info", "children": [], "durationId": "f6cc6cf1-d746-4e39-bbdd-e4aab89f7eec"}}, {"head": {"id": "3eb8d13d-3bf5-44cc-999d-81d1f0485824", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427861962900, "endTime": 34427862413700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "f8f10a65-4191-49fe-b8da-ba3e8139db45", "logId": "9bf78c29-e4ad-4996-b266-8a2437b5f9a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8f10a65-4191-49fe-b8da-ba3e8139db45", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427859911800}, "additional": {"logType": "detail", "children": [], "durationId": "3eb8d13d-3bf5-44cc-999d-81d1f0485824"}}, {"head": {"id": "25688c1c-1c6c-4164-bc0a-9b691ec89f6c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427860392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f595ed-daf1-4cd0-8445-ff6c8c2901ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427860491600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61628f80-8fd1-4dce-aae9-c54cee6a8f75", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427861978100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2a2380-1108-48eb-a82b-63b306d44519", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427862155900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f27de7d-aef2-4e81-8296-632a0d7d1a40", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427862216200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c67de0b1-e2c9-4c13-8720-76ff46dea08d", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427862288600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd8a03e0-5a39-4f01-ac7e-7c623cb0488b", "name": "runTaskFromQueue task cost before running: 414 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427862360000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf78c29-e4ad-4996-b266-8a2437b5f9a2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427861962900, "endTime": 34427862413700, "totalTime": 381600}, "additional": {"logType": "info", "children": [], "durationId": "3eb8d13d-3bf5-44cc-999d-81d1f0485824"}}, {"head": {"id": "fcd65700-29a6-4e23-b1c6-581395531804", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427869980300, "endTime": 34427870660500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "308d47d9-f4a3-4ac5-a9e8-ed69978a335a", "logId": "f7674b72-19e6-4cd6-ad0f-4defbbc0b335"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "308d47d9-f4a3-4ac5-a9e8-ed69978a335a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427864394200}, "additional": {"logType": "detail", "children": [], "durationId": "fcd65700-29a6-4e23-b1c6-581395531804"}}, {"head": {"id": "3739c52c-5332-448d-8bc9-22560c1cfbc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427865422600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a879d11d-a8ee-4d91-86a7-7ef304b0506f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427865538200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f2b5eee-3a7a-4b4f-8367-6eeb273dce73", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427869991000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c998b93-1b54-47fd-9ed6-b9027bc34754", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427870240800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e68f227-95d7-4641-8952-0ceb2a64ba47", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03711700439453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427870462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72c516e1-efbd-4f62-b347-eb4a0e58a3c5", "name": "runTaskFromQueue task cost before running: 422 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427870551600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7674b72-19e6-4cd6-ad0f-4defbbc0b335", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427869980300, "endTime": 34427870660500, "totalTime": 554600}, "additional": {"logType": "info", "children": [], "durationId": "fcd65700-29a6-4e23-b1c6-581395531804"}}, {"head": {"id": "569b1b90-2909-43e1-a13d-a08ca4eac5ac", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427875232100, "endTime": 34427879838200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "3c383104-21c1-4ec9-aed2-c48462781f1f", "logId": "8e49d248-0aaf-4c16-8a15-676de4e6a87a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c383104-21c1-4ec9-aed2-c48462781f1f", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427872883000}, "additional": {"logType": "detail", "children": [], "durationId": "569b1b90-2909-43e1-a13d-a08ca4eac5ac"}}, {"head": {"id": "36984d01-1da6-46f7-8f98-2fe3b4696cc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427873322400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6378be-a619-453a-9cf9-4b46f9c3f436", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427873446500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5242e670-d15d-4e5a-905e-9a031b206f1c", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427875239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f9413b1-0207-44c0-9cc8-56448f36a6f0", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427877169400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1262af7-e6be-40ec-8167-5b1fd88fd762", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427877388800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27b596c0-0cb0-4750-bc92-b978ff1e7f2a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427877558100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3d063f-3377-41ff-a84d-cb2b77747332", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427879432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c630318-89b8-4bd1-ab4c-7009bba8a65c", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11754608154296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427879583900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06f1686f-e390-4952-8864-7ea30a797c17", "name": "runTaskFromQueue task cost before running: 431 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427879766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e49d248-0aaf-4c16-8a15-676de4e6a87a", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427875232100, "endTime": 34427879838200, "totalTime": 4508900}, "additional": {"logType": "info", "children": [], "durationId": "569b1b90-2909-43e1-a13d-a08ca4eac5ac"}}, {"head": {"id": "d8054096-e522-421b-93f4-cfb9bb5fc634", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427883511200, "endTime": 34427884183000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d81517f4-57c8-46b1-b61c-de0f091c53d1", "logId": "dfa2894f-df34-45b1-920c-a0c18b6dcce2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d81517f4-57c8-46b1-b61c-de0f091c53d1", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427882186400}, "additional": {"logType": "detail", "children": [], "durationId": "d8054096-e522-421b-93f4-cfb9bb5fc634"}}, {"head": {"id": "067bef8e-4496-4ef9-8761-dc0d163f34d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427882524700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d1064ec-4059-43b5-9922-8885aaa6dd16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427882651800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35af405a-e3c6-4c0c-8019-ae496ca35e67", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427883526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2159278-f95c-4633-b6a5-61d25d2f0e9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427883820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b691d93e-7bbc-48ce-a68e-b584a188a7a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427883957700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f61219ce-2672-4b97-961d-92daa8aa8de1", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427884043700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb15b39-07ce-4237-8441-e27e7c7f9395", "name": "runTaskFromQueue task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427884125000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfa2894f-df34-45b1-920c-a0c18b6dcce2", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427883511200, "endTime": 34427884183000, "totalTime": 591600}, "additional": {"logType": "info", "children": [], "durationId": "d8054096-e522-421b-93f4-cfb9bb5fc634"}}, {"head": {"id": "3074fa72-9106-4905-b077-0a7fc8039ee9", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427887395100, "endTime": 34427891070600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "05c350b7-f51c-4656-a9c1-c9cb4d3ef181", "logId": "84180ca6-1eee-41cb-92b2-766f12f481df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05c350b7-f51c-4656-a9c1-c9cb4d3ef181", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427886137100}, "additional": {"logType": "detail", "children": [], "durationId": "3074fa72-9106-4905-b077-0a7fc8039ee9"}}, {"head": {"id": "5e48d10a-df12-470f-abaa-9acd8ee76779", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427886516100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc274241-b611-453c-91af-8f7924d9c864", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427886671900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa6d6130-80c9-4568-8048-357a39c35115", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427887445300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a5a0a4-f30a-4d91-a21a-71a6cb6521b2", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427890770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd9818c-947a-4de1-81d5-47efed80b015", "name": "entry : default@MakePackInfo cost memory 0.1381072998046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427890958300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84180ca6-1eee-41cb-92b2-766f12f481df", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427887395100, "endTime": 34427891070600}, "additional": {"logType": "info", "children": [], "durationId": "3074fa72-9106-4905-b077-0a7fc8039ee9"}}, {"head": {"id": "b8559bab-b3fa-43ff-9d34-208185166569", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427896442200, "endTime": 34427900062800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "ac287128-6eac-4d91-b845-330673e2adf6", "logId": "9b740cbb-54e5-4f4a-80c7-49e1539860f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac287128-6eac-4d91-b845-330673e2adf6", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427893883900}, "additional": {"logType": "detail", "children": [], "durationId": "b8559bab-b3fa-43ff-9d34-208185166569"}}, {"head": {"id": "4a81b84e-727a-43fb-9a5a-733c5f0a28e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427894254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b41029d-09db-43ac-8dfe-9a2f193e55d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427894357300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0372ed7-7f43-4de9-b4f6-1ce11fe1b6a6", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427896455900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5abfa20-004c-42de-a6c9-4ff7a47d65c6", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427896685100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ec2777f-4f2d-487d-9c5b-30c59fcfc0d3", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427898070200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6517dcde-9acd-4274-94a1-818b909a4ea9", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427899564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e80163fe-df4a-449a-9c9e-97ac232b0f68", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427899693400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd18f210-7700-449e-b8d2-984e1f004877", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427899774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ce5100-12b8-4af1-b5bf-2f1c41306526", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427899829100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b2614d-a656-438d-9cae-7fb7b39da7a3", "name": "entry : default@SyscapTransform cost memory 0.1509246826171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427899926000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0868e55b-351b-456d-b3c7-584b31f7b171", "name": "runTaskFromQueue task cost before running: 452 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427900012800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b740cbb-54e5-4f4a-80c7-49e1539860f0", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427896442200, "endTime": 34427900062800, "totalTime": 3551200}, "additional": {"logType": "info", "children": [], "durationId": "b8559bab-b3fa-43ff-9d34-208185166569"}}, {"head": {"id": "b3c5127e-773b-4ed1-ba57-3fe5ae8afef5", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427903021400, "endTime": 34427904530600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "de35463d-193e-495c-9134-e67529d802f6", "logId": "f49d9817-c9fe-4cfd-8155-4151a18de9fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de35463d-193e-495c-9134-e67529d802f6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427901579900}, "additional": {"logType": "detail", "children": [], "durationId": "b3c5127e-773b-4ed1-ba57-3fe5ae8afef5"}}, {"head": {"id": "375edd68-a0ff-4e20-9783-1e0a69200726", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427901958100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "291ab5c8-3c91-4b1d-a66f-0c06e5ad6203", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427902061700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49f354cd-c902-43be-ad70-f26beefaea77", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427903032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca973d18-e06b-44f9-a56a-70b3303f10fa", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427904266200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0160c21-b78b-4342-8958-9198074ed1a7", "name": "entry : default@ProcessProfile cost memory 0.0596771240234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427904386100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f49d9817-c9fe-4cfd-8155-4151a18de9fb", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427903021400, "endTime": 34427904530600}, "additional": {"logType": "info", "children": [], "durationId": "b3c5127e-773b-4ed1-ba57-3fe5ae8afef5"}}, {"head": {"id": "bb8d9c21-d955-4919-a01a-d61eff73cb97", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427909087000, "endTime": 34427916071700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2921c8cb-badf-4b8a-a57a-f98882bc2bd5", "logId": "9851c738-7a48-4af8-93fa-167eb01febd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2921c8cb-badf-4b8a-a57a-f98882bc2bd5", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427906855400}, "additional": {"logType": "detail", "children": [], "durationId": "bb8d9c21-d955-4919-a01a-d61eff73cb97"}}, {"head": {"id": "f91b082a-0a47-481f-81c6-92f39bf9f872", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427907278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d934d788-4588-4924-8ba4-e9c2793f8622", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427907386300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d07e471-f298-42f8-b1ad-c927aba924a7", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427909098000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aaf3bbf-c645-4022-b0ae-28f5aa4f45a7", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427913993400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c650ac7-2a65-4eff-adac-52ddfec2c826", "name": "entry : default@ProcessRouterMap cost memory -5.3865814208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427915960800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9851c738-7a48-4af8-93fa-167eb01febd0", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427909087000, "endTime": 34427916071700}, "additional": {"logType": "info", "children": [], "durationId": "bb8d9c21-d955-4919-a01a-d61eff73cb97"}}, {"head": {"id": "1f9db4b2-7237-4c19-8a55-30e8a87ccb77", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427919629600, "endTime": 34427920649600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "94e8d4d0-3923-4d3d-912e-34f4ccb3266b", "logId": "d2509097-cd47-467d-84d8-1a2da4fae674"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94e8d4d0-3923-4d3d-912e-34f4ccb3266b", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427918612400}, "additional": {"logType": "detail", "children": [], "durationId": "1f9db4b2-7237-4c19-8a55-30e8a87ccb77"}}, {"head": {"id": "86e6e382-d979-471f-9a07-f68dbd8d9ff4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427918962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "094bef79-f3d7-4797-924e-233a4eeae273", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427919062100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "049db39e-21a1-4495-a57f-970a7e78e0e7", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427919637400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1030289a-050a-4e1c-ab0d-b9fbd3332870", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427919772200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad12404-aa42-4a4b-a350-2d7d5226dfdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427919847500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb48aa05-5945-4424-87ad-cbac41e190bd", "name": "entry : default@BuildNativeWithNinja cost memory 0.05648040771484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427920466100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96aad438-c3ad-4b22-91d7-8ea23a47b21d", "name": "runTaskFromQueue task cost before running: 472 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427920562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2509097-cd47-467d-84d8-1a2da4fae674", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427919629600, "endTime": 34427920649600, "totalTime": 913300}, "additional": {"logType": "info", "children": [], "durationId": "1f9db4b2-7237-4c19-8a55-30e8a87ccb77"}}, {"head": {"id": "1f82336e-638d-4c92-a3b1-e659726881c4", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427925335700, "endTime": 34427932211200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0b31807b-5b1f-4eb9-a5e4-73c156887575", "logId": "820eda7f-efc8-474a-94e8-5993a61529f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b31807b-5b1f-4eb9-a5e4-73c156887575", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427923038400}, "additional": {"logType": "detail", "children": [], "durationId": "1f82336e-638d-4c92-a3b1-e659726881c4"}}, {"head": {"id": "9508d34f-b7ee-4b0e-ab82-ad403563a8e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427923448300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dd9e379-b785-4aff-beb8-83e1ba2f16da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427923542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bbf459c-0c1a-41ba-acc0-92bc7a823346", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427924269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7760ff13-5043-431d-897e-0d71de655e82", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427927171100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58708e9-c6c3-457d-8075-25b378b21dd6", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427930318900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcdeebbd-7ec8-414e-b4f3-7828bdfdd738", "name": "entry : default@ProcessResource cost memory 0.16870880126953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427930451500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "820eda7f-efc8-474a-94e8-5993a61529f5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427925335700, "endTime": 34427932211200}, "additional": {"logType": "info", "children": [], "durationId": "1f82336e-638d-4c92-a3b1-e659726881c4"}}, {"head": {"id": "0ac9e5b5-7d7a-4b25-b80e-8c90a25e529e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427940796300, "endTime": 34427952983700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a4dea410-8075-4e63-bdcd-4609d54c6021", "logId": "4a87ea07-90fc-4c94-8023-a3415bd3523a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4dea410-8075-4e63-bdcd-4609d54c6021", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427936729100}, "additional": {"logType": "detail", "children": [], "durationId": "0ac9e5b5-7d7a-4b25-b80e-8c90a25e529e"}}, {"head": {"id": "e7353464-780f-4a35-aee8-2e5989444d12", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427937131500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c171e1c-6884-4b47-af7a-50a6374cd96d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427937253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eab1610-2867-4b05-8b74-84f40fbe7325", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427940808200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f662b71d-fd36-4c7e-bcc8-bae9a1f8abd7", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427952796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04a3c43-d3fa-41d0-8553-314c9a246f91", "name": "entry : default@GenerateLoaderJson cost memory 0.7565078735351562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427952916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a87ea07-90fc-4c94-8023-a3415bd3523a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427940796300, "endTime": 34427952983700}, "additional": {"logType": "info", "children": [], "durationId": "0ac9e5b5-7d7a-4b25-b80e-8c90a25e529e"}}, {"head": {"id": "b07756df-8185-493d-8d31-81ff0cae4b68", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427960277600, "endTime": 34427963828000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "425ae5e0-fa0e-464a-9016-805d38630084", "logId": "7cfe43e5-edbf-4722-b5c5-fc19e57add44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "425ae5e0-fa0e-464a-9016-805d38630084", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427959080500}, "additional": {"logType": "detail", "children": [], "durationId": "b07756df-8185-493d-8d31-81ff0cae4b68"}}, {"head": {"id": "deab727c-a241-41b3-ac3b-9e96b0420b7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427959551200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae215944-c65e-4f6b-9c97-4e63d3486fc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427959656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a392467-2d1c-46c1-88dc-def96877fc1c", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427960291100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58484c74-acab-4c5b-9c01-09729dfed19a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427962626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab49991-9e0f-4341-9ac6-0c775d55a1a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427962733800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1261706a-6586-4d58-9d68-da27697a9bc2", "name": "entry : default@ProcessLibs cost memory 0.12483978271484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427963650100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "350c9d8e-c5f7-40b9-9587-33b9e81297ec", "name": "runTaskFromQueue task cost before running: 515 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427963769600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cfe43e5-edbf-4722-b5c5-fc19e57add44", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427960277600, "endTime": 34427963828000, "totalTime": 3468200}, "additional": {"logType": "info", "children": [], "durationId": "b07756df-8185-493d-8d31-81ff0cae4b68"}}, {"head": {"id": "2077a59a-2867-4e6e-bd9e-9097bd5fe9d5", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427969893000, "endTime": 34428002075800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5b65c0d8-857e-4d89-925e-684607a7f9f1", "logId": "f9568ee0-29ef-414b-ba6d-b24c6d63ad01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b65c0d8-857e-4d89-925e-684607a7f9f1", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427965994800}, "additional": {"logType": "detail", "children": [], "durationId": "2077a59a-2867-4e6e-bd9e-9097bd5fe9d5"}}, {"head": {"id": "4cf7c8cf-af19-4439-b379-b8707ecb8263", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427966317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ea4d3b1-2cd0-42f8-9475-8900e8761dad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427966400900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65289821-b4a7-4fc4-97d1-1fb25033e972", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427967214000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0d707f-3d5d-474c-8e5d-67f69b6657ad", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427969916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9842ecaa-f273-4de0-b030-629175c8fcbf", "name": "Incremental task entry:default@CompileResource pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428001283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4977d041-8d71-485e-8642-251ef1779fcc", "name": "entry : default@CompileResource cost memory 1.40484619140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428001516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9568ee0-29ef-414b-ba6d-b24c6d63ad01", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427969893000, "endTime": 34428002075800}, "additional": {"logType": "info", "children": [], "durationId": "2077a59a-2867-4e6e-bd9e-9097bd5fe9d5"}}, {"head": {"id": "008e41a7-8e25-4178-a1a7-bc539bfc06e7", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428010487300, "endTime": 34428012371400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a860a75a-3afe-4c87-a74a-18ff4c6b0fe7", "logId": "478515fe-3868-47f1-b59e-a6bfad5b2255"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a860a75a-3afe-4c87-a74a-18ff4c6b0fe7", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428006523100}, "additional": {"logType": "detail", "children": [], "durationId": "008e41a7-8e25-4178-a1a7-bc539bfc06e7"}}, {"head": {"id": "b8b0e6b9-cd5e-46f1-bbb6-bda3f10f0eeb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428007101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bbe69e4-2043-4f75-81fc-ea5f0ec0440f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428007271700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c536c5a2-a799-4903-81df-5f9c01c00a8c", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428010497600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f4ff5fc-e27b-430e-8069-0b0f5929924d", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428010823600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6df8b0-aa24-42a2-99ce-7d77d9a3da78", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428012043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40041d1e-170e-4152-87d9-1a0debf4ee7c", "name": "entry : default@DoNativeStrip cost memory 0.07482147216796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428012269800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "478515fe-3868-47f1-b59e-a6bfad5b2255", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428010487300, "endTime": 34428012371400}, "additional": {"logType": "info", "children": [], "durationId": "008e41a7-8e25-4178-a1a7-bc539bfc06e7"}}, {"head": {"id": "cbfcbba8-eefe-4981-b9ab-7c6510364320", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428020501500, "endTime": 34430074889200}, "additional": {"children": ["d9c24e72-2bc6-4276-aafd-72f3283a9e14", "eb57b676-7675-4dff-b7c7-8181284ba3b8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "fe757270-2da5-4c03-8e5a-60566d7c57c6", "logId": "33ddcc38-7cf4-4143-8216-025771bdeff6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe757270-2da5-4c03-8e5a-60566d7c57c6", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428015274800}, "additional": {"logType": "detail", "children": [], "durationId": "cbfcbba8-eefe-4981-b9ab-7c6510364320"}}, {"head": {"id": "6094b65e-8a38-426f-b7e3-9f89af9f1b28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428015758600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ae3385-e00c-4256-8052-3837ed7cffb7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428015878100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee23a4cf-7862-4c2d-b499-a66c35896071", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428020514500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e677fa1-7f15-4e3e-9cdc-e568a74aacff", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428033477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9a7cd4-4ba1-4857-b890-c8f840082dc7", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428033869400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad13388-cdb6-41bc-b1fa-c3133094af66", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428046811300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f491b0ac-4d46-4217-b2b5-4b66f653f207", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428047321100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d870a53e-3de9-474f-a80f-545eb17e4288", "name": "default@CompileArkTS work[124] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428048600800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34428252483700, "endTime": 34430067767100}, "additional": {"children": ["224bef1f-afa7-4128-bfa0-d3b57666caae", "983b5f51-f62c-47d6-83f7-eaab0a8579a8", "bf5a5aad-87cb-4b57-937f-5763aba8382f", "105f9e31-6b8a-46a9-bcd2-52bf378f53a8", "1ba7b557-18ec-4641-8c2b-2da4a61b6ca3", "8214d795-aa21-4f2c-bf49-8d4967d9f491"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cbfcbba8-eefe-4981-b9ab-7c6510364320", "logId": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cef6ec11-24f8-4984-a8fa-bd98544de113", "name": "default@CompileArkTS work[124] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428049898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ece90c5-1584-47db-a463-2475cf5b088e", "name": "default@CompileArkTS work[124] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428050016900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874816df-b561-482e-b458-f71c11c30ddd", "name": "CopyResources startTime: 34428050077700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428050080500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "085edd87-e400-47c1-be3f-95217ab9111c", "name": "default@CompileArkTS work[125] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428050168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb57b676-7675-4dff-b7c7-8181284ba3b8", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34429436327300, "endTime": 34429456824600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cbfcbba8-eefe-4981-b9ab-7c6510364320", "logId": "da9a779c-d88a-45b5-8c14-586ee532407b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48eb7877-7608-493d-9219-a7e56d2f4074", "name": "default@CompileArkTS work[125] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428051285200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd088149-1c0e-4c5f-b647-a0b18b35a4f7", "name": "default@CompileArkTS work[125] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428051404700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01c4d3b8-fe3c-409e-8e3c-79a0d0d81b35", "name": "entry : default@CompileArkTS cost memory 1.5616455078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428051496500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c1df75-c0ce-471b-bf1e-87a3e45a703e", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428059943200, "endTime": 34428064491400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "7e2dadee-7e8d-40af-9f50-07b7b0d3842c", "logId": "dcd681c7-a0e2-4752-843f-39c0524b58e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e2dadee-7e8d-40af-9f50-07b7b0d3842c", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428054566100}, "additional": {"logType": "detail", "children": [], "durationId": "65c1df75-c0ce-471b-bf1e-87a3e45a703e"}}, {"head": {"id": "2aa81c8b-2115-48be-bf0f-87b2ce8e5e5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428055047300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efcbe33a-711c-419f-8f9e-45b995c4e021", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428055207400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a05c2e5-240e-41bd-a03e-487d6eb99acd", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428059955700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd29d62e-171f-478d-af0f-3a6f3910cf95", "name": "entry : default@BuildJS cost memory 0.126190185546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428064290900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a3693b-57b8-4612-80a9-d5f9b936548f", "name": "runTaskFromQueue task cost before running: 616 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428064433400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcd681c7-a0e2-4752-843f-39c0524b58e8", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428059943200, "endTime": 34428064491400, "totalTime": 4468100}, "additional": {"logType": "info", "children": [], "durationId": "65c1df75-c0ce-471b-bf1e-87a3e45a703e"}}, {"head": {"id": "930b9184-4495-481e-8650-3388380688fa", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428070424800, "endTime": 34428072951600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8756db22-f9e8-47cd-93df-71bd108bbe34", "logId": "6ab5d654-f4fd-4768-ba97-d2fbfb3f6acd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8756db22-f9e8-47cd-93df-71bd108bbe34", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428067024700}, "additional": {"logType": "detail", "children": [], "durationId": "930b9184-4495-481e-8650-3388380688fa"}}, {"head": {"id": "6ad462bf-0fc0-4b04-8580-43f2a316359f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428067625100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3662090e-2000-4b6c-a8a1-b15bb6c5d111", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428067743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75efed9c-1523-4691-96d0-f7dc132377b6", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428070443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f3ff43-57c5-4b32-af9b-4a494d8f16a7", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428071017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d70370-ffe5-44e3-b7e5-631943e2f3ff", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428072554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897308de-afad-4a82-b97b-e71d8f0deccf", "name": "entry : default@CacheNativeLibs cost memory 0.09508514404296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428072856300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab5d654-f4fd-4768-ba97-d2fbfb3f6acd", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428070424800, "endTime": 34428072951600}, "additional": {"logType": "info", "children": [], "durationId": "930b9184-4495-481e-8650-3388380688fa"}}, {"head": {"id": "584fc75b-0a84-4f7c-99a9-f4302ece9d03", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428251771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7120f1c-1219-46d5-9b85-df94a8647fb5", "name": "default@CompileArkTS work[124] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428252151200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5ed98ee-2cda-46e0-8f76-f368e13528d2", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428252289300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75d9af4d-471e-450a-8147-df94db12003a", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428252364100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3fd4c67-f9a7-42bd-bbc0-8fda3aa26cc0", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428252424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef325e80-d807-45f1-b6ca-8148ed9e6576", "name": "default@CompileArkTS work[125] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428253430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "608f5996-6df0-486d-aa35-b9c85d02bc10", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429457735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8239b721-fdaf-4aa0-9797-139fd0ee0040", "name": "CopyResources is end, endTime: 34429457860500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429457864000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59492148-6733-4169-bd37-2eab32a923ac", "name": "default@CompileArkTS work[125] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429457939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da9a779c-d88a-45b5-8c14-586ee532407b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34429436327300, "endTime": 34429456824600}, "additional": {"logType": "info", "children": [], "durationId": "eb57b676-7675-4dff-b7c7-8181284ba3b8", "parent": "33ddcc38-7cf4-4143-8216-025771bdeff6"}}, {"head": {"id": "7af6f640-7c9b-454d-8ab2-0385cf31baa6", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429569004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001e4268-0e4d-4eb2-bbd9-5f26586a2897", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430068425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224bef1f-afa7-4128-bfa0-d3b57666caae", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34428252586200, "endTime": 34428256163300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "logId": "813f9040-b7a9-4988-8b33-c8bb79fc0fd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "813f9040-b7a9-4988-8b33-c8bb79fc0fd6", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428252586200, "endTime": 34428256163300}, "additional": {"logType": "info", "children": [], "durationId": "224bef1f-afa7-4128-bfa0-d3b57666caae", "parent": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}}, {"head": {"id": "983b5f51-f62c-47d6-83f7-eaab0a8579a8", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34428256182100, "endTime": 34428256315900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "logId": "f7aadc28-5ede-47af-a8c5-b56d04807435"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7aadc28-5ede-47af-a8c5-b56d04807435", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428256182100, "endTime": 34428256315900}, "additional": {"logType": "info", "children": [], "durationId": "983b5f51-f62c-47d6-83f7-eaab0a8579a8", "parent": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}}, {"head": {"id": "bf5a5aad-87cb-4b57-937f-5763aba8382f", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34428256328400, "endTime": 34428256365400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "logId": "db6d6774-e966-4ffe-80d0-c22d49714379"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db6d6774-e966-4ffe-80d0-c22d49714379", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428256328400, "endTime": 34428256365400}, "additional": {"logType": "info", "children": [], "durationId": "bf5a5aad-87cb-4b57-937f-5763aba8382f", "parent": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}}, {"head": {"id": "105f9e31-6b8a-46a9-bcd2-52bf378f53a8", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34428256384400, "endTime": 34429969461000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "logId": "89abb828-7741-4a88-9628-968c3b924c3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89abb828-7741-4a88-9628-968c3b924c3d", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428256384400, "endTime": 34429969461000}, "additional": {"logType": "info", "children": [], "durationId": "105f9e31-6b8a-46a9-bcd2-52bf378f53a8", "parent": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}}, {"head": {"id": "1ba7b557-18ec-4641-8c2b-2da4a61b6ca3", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34429969482700, "endTime": 34429983558700}, "additional": {"children": ["cb055caf-dbcf-4c6d-8c2d-fa6276b114fa", "97bae148-3632-4764-a09e-a3aaee3d6566", "1911ce34-cd72-4c1d-85ba-dbec0412f8a8"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "logId": "587ddb15-07fa-4d45-8095-183011a25325"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "587ddb15-07fa-4d45-8095-183011a25325", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429969482700, "endTime": 34429983558700}, "additional": {"logType": "info", "children": ["cc6845b1-f726-4eeb-aa03-3e6d0394cccc", "fcb19f7e-6021-4b6c-9caa-6b4150649e15", "a69b9f44-1a58-42d1-b647-ac88ec03edc6"], "durationId": "1ba7b557-18ec-4641-8c2b-2da4a61b6ca3", "parent": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}}, {"head": {"id": "cb055caf-dbcf-4c6d-8c2d-fa6276b114fa", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34429969497500, "endTime": 34429969504400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1ba7b557-18ec-4641-8c2b-2da4a61b6ca3", "logId": "cc6845b1-f726-4eeb-aa03-3e6d0394cccc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc6845b1-f726-4eeb-aa03-3e6d0394cccc", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429969497500, "endTime": 34429969504400}, "additional": {"logType": "info", "children": [], "durationId": "cb055caf-dbcf-4c6d-8c2d-fa6276b114fa", "parent": "587ddb15-07fa-4d45-8095-183011a25325"}}, {"head": {"id": "97bae148-3632-4764-a09e-a3aaee3d6566", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34429969507800, "endTime": 34429977261800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1ba7b557-18ec-4641-8c2b-2da4a61b6ca3", "logId": "fcb19f7e-6021-4b6c-9caa-6b4150649e15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcb19f7e-6021-4b6c-9caa-6b4150649e15", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429969507800, "endTime": 34429977261800}, "additional": {"logType": "info", "children": [], "durationId": "97bae148-3632-4764-a09e-a3aaee3d6566", "parent": "587ddb15-07fa-4d45-8095-183011a25325"}}, {"head": {"id": "1911ce34-cd72-4c1d-85ba-dbec0412f8a8", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34429977267300, "endTime": 34429983500800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1ba7b557-18ec-4641-8c2b-2da4a61b6ca3", "logId": "a69b9f44-1a58-42d1-b647-ac88ec03edc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a69b9f44-1a58-42d1-b647-ac88ec03edc6", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429977267300, "endTime": 34429983500800}, "additional": {"logType": "info", "children": [], "durationId": "1911ce34-cd72-4c1d-85ba-dbec0412f8a8", "parent": "587ddb15-07fa-4d45-8095-183011a25325"}}, {"head": {"id": "8214d795-aa21-4f2c-bf49-8d4967d9f491", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34429983583900, "endTime": 34430067619700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "logId": "ad2c21ec-0730-4f14-99f6-1c13a2c5bd5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad2c21ec-0730-4f14-99f6-1c13a2c5bd5f", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34429983583900, "endTime": 34430067619700}, "additional": {"logType": "info", "children": [], "durationId": "8214d795-aa21-4f2c-bf49-8d4967d9f491", "parent": "3e57e927-96d6-495a-9efb-d55eb85aa4e5"}}, {"head": {"id": "24efc412-44e8-4210-8561-24b81d0cc5f3", "name": "default@CompileArkTS work[124] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430074684900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e57e927-96d6-495a-9efb-d55eb85aa4e5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34428252483700, "endTime": 34430067767100}, "additional": {"logType": "info", "children": ["813f9040-b7a9-4988-8b33-c8bb79fc0fd6", "f7aadc28-5ede-47af-a8c5-b56d04807435", "db6d6774-e966-4ffe-80d0-c22d49714379", "89abb828-7741-4a88-9628-968c3b924c3d", "587ddb15-07fa-4d45-8095-183011a25325", "ad2c21ec-0730-4f14-99f6-1c13a2c5bd5f"], "durationId": "d9c24e72-2bc6-4276-aafd-72f3283a9e14", "parent": "33ddcc38-7cf4-4143-8216-025771bdeff6"}}, {"head": {"id": "33ddcc38-7cf4-4143-8216-025771bdeff6", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34428020501500, "endTime": 34430074889200, "totalTime": 1846343800}, "additional": {"logType": "info", "children": ["3e57e927-96d6-495a-9efb-d55eb85aa4e5", "da9a779c-d88a-45b5-8c14-586ee532407b"], "durationId": "cbfcbba8-eefe-4981-b9ab-7c6510364320"}}, {"head": {"id": "54757773-009a-4ff1-929e-a9083be1865e", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430080437100, "endTime": 34430082457100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "ed60e221-f437-42dd-b467-d137d64c5139", "logId": "0d930e8c-cf84-45f3-9613-527db94207ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed60e221-f437-42dd-b467-d137d64c5139", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430078693200}, "additional": {"logType": "detail", "children": [], "durationId": "54757773-009a-4ff1-929e-a9083be1865e"}}, {"head": {"id": "ed05fe3a-7747-484f-b90b-ac3ee2ba8e74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430079067100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4549127e-d65c-4d74-b430-f24f9d7fe3fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430079170200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b434e18-15c7-4f50-b990-961ec94b4cf9", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430080448200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd9e298-3e53-466a-897c-44efb6bc88ae", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430081246900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc1b2a3-c045-446e-9cda-97c94c3ff255", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430082273800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af55b63-a2aa-44b1-92b2-1f1192ba2838", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07253265380859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430082390700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d930e8c-cf84-45f3-9613-527db94207ef", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430080437100, "endTime": 34430082457100}, "additional": {"logType": "info", "children": [], "durationId": "54757773-009a-4ff1-929e-a9083be1865e"}}, {"head": {"id": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430093426800, "endTime": 34430635987800}, "additional": {"children": ["d498c05f-28c6-4c7d-8cd4-d88bb5692328", "ea169cb0-f11c-4320-b28c-581a7bf5a9c3", "d8fed788-233d-488d-99bb-c37e72f95427"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "e80e43bb-5538-4f33-a653-5411a9cceccc", "logId": "a94fbe85-709b-400f-88b4-69f8b38bba7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e80e43bb-5538-4f33-a653-5411a9cceccc", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430086192100}, "additional": {"logType": "detail", "children": [], "durationId": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7"}}, {"head": {"id": "0543bb55-2990-42e1-be32-ea11a5a471b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430086499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb39c91a-ff7c-4a01-a903-400ddf6a5519", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430086595400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f519722-0032-4332-994f-d51e7aadcc56", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430093436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1333d3-be63-4d0c-bc3e-d36ae61ef1ca", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430105989800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71b52335-c14a-45bb-9180-b04ab5f84ed0", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430106142100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58aed2b4-71f4-45b8-bc05-6950931c27df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430106257000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77c4f248-44ab-40d4-9d36-ca206edae6c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430106316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d498c05f-28c6-4c7d-8cd4-d88bb5692328", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430106985400, "endTime": 34430108312600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7", "logId": "7c8b52e4-86b0-42ac-81e4-2e5ec71e17bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caba0ed9-a059-4c72-80cc-430b44e5b150", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430108185600}, "additional": {"logType": "debug", "children": [], "durationId": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7"}}, {"head": {"id": "7c8b52e4-86b0-42ac-81e4-2e5ec71e17bc", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430106985400, "endTime": 34430108312600}, "additional": {"logType": "info", "children": [], "durationId": "d498c05f-28c6-4c7d-8cd4-d88bb5692328", "parent": "a94fbe85-709b-400f-88b4-69f8b38bba7f"}}, {"head": {"id": "ea169cb0-f11c-4320-b28c-581a7bf5a9c3", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430108812900, "endTime": 34430110110800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7", "logId": "7b2641e9-30e7-4dc3-aef5-cebb142c36ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5eeece65-ffda-483f-bd9d-b487c26db8c4", "name": "default@PackageHap work[126] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430109373300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fed788-233d-488d-99bb-c37e72f95427", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34430189189400, "endTime": 34430635668000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7", "logId": "c8c8ba7e-5dde-436d-a605-321658811168"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3046022d-67d6-40f3-bebf-d94e53ae539b", "name": "default@PackageHap work[126] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430109972200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "055201ff-710d-44a9-8d09-67f8e2fe5fcb", "name": "default@PackageHap work[126] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430110052600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2641e9-30e7-4dc3-aef5-cebb142c36ac", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430108812900, "endTime": 34430110110800}, "additional": {"logType": "info", "children": [], "durationId": "ea169cb0-f11c-4320-b28c-581a7bf5a9c3", "parent": "a94fbe85-709b-400f-88b4-69f8b38bba7f"}}, {"head": {"id": "d6437f9d-b825-4533-a72f-9a2f96476557", "name": "entry : default@PackageHap cost memory 1.3337936401367188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430113834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b64f68e-ab2d-4210-a608-9a2c2406c2d3", "name": "default@PackageHap work[126] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430189212200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03103563-2415-466f-9cdf-24e232496e63", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430219890700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac8f8058-0c89-463f-842f-2f4dbad7a4d8", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430220089300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6713fa4f-5ee4-4cea-9bb7-fdfece445065", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430220173600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c66fed0-64a0-48bd-8c6b-1f8de91462e3", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430220227600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a3d61d-b223-4284-954e-af88e954848c", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430220276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f45d9380-0256-4cd9-8792-4b630d546bc3", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430220953000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9888feba-292a-4d55-9537-38290eff91e9", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430635739900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4ae4d3-69d2-4417-adc1-81d6735fc9f2", "name": "default@PackageHap work[126] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430635887000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c8ba7e-5dde-436d-a605-321658811168", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34430189189400, "endTime": 34430635668000}, "additional": {"logType": "info", "children": [], "durationId": "d8fed788-233d-488d-99bb-c37e72f95427", "parent": "a94fbe85-709b-400f-88b4-69f8b38bba7f"}}, {"head": {"id": "a94fbe85-709b-400f-88b4-69f8b38bba7f", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430093426800, "endTime": 34430635987800, "totalTime": 467010400}, "additional": {"logType": "info", "children": ["7c8b52e4-86b0-42ac-81e4-2e5ec71e17bc", "7b2641e9-30e7-4dc3-aef5-cebb142c36ac", "c8c8ba7e-5dde-436d-a605-321658811168"], "durationId": "7ce3ed0f-4f48-4ff0-b085-c2e2862da1d7"}}, {"head": {"id": "b1a1b142-ff31-419f-9ff5-0b6f6693aad5", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430641470700, "endTime": 34430643078500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "d60c19bb-4e1e-4676-9909-1de439721a1c", "logId": "ba2f99a8-4b6a-46c1-860c-6aa677b9aa41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d60c19bb-4e1e-4676-9909-1de439721a1c", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430638866800}, "additional": {"logType": "detail", "children": [], "durationId": "b1a1b142-ff31-419f-9ff5-0b6f6693aad5"}}, {"head": {"id": "120e3434-8155-4ca0-ade5-352e0d5218fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430639369500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "778ff6be-9ff1-4651-a42e-298d943cbc45", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430639468700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bb7ce58-ea4d-4c5f-8449-adcb29ed3d13", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430641480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11d2bf2-95a2-4a3c-86e2-511b1d096df2", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430641883900}, "additional": {"logType": "warn", "children": [], "durationId": "b1a1b142-ff31-419f-9ff5-0b6f6693aad5"}}, {"head": {"id": "0378834f-35ea-42b3-865f-c65cc3363f16", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430642400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1741769b-8cc7-4b38-9b31-8a375d577f1b", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430642486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621332cc-5be5-4f61-8b23-a760987e3231", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430642560300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cca694bc-cea4-4e48-bd1e-3272c8f3e6f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430642614100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6e54d76-c4b1-4cd3-828e-9ed7d268f0db", "name": "entry : default@SignHap cost memory 0.11452484130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430642922800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9408d613-0ce0-4687-b3f3-baa8e6c13bfe", "name": "runTaskFromQueue task cost before running: 3 s 195 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430643017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2f99a8-4b6a-46c1-860c-6aa677b9aa41", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430641470700, "endTime": 34430643078500, "totalTime": 1526700}, "additional": {"logType": "info", "children": [], "durationId": "b1a1b142-ff31-419f-9ff5-0b6f6693aad5"}}, {"head": {"id": "12d449d6-95d8-4cd3-89d9-e2359710d15b", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430646661300, "endTime": 34430651115600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "86da7dbe-e81f-44b0-b99f-f7a2b8f8269c", "logId": "fa74acf4-988e-4bc3-9361-208a6b0ae85c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86da7dbe-e81f-44b0-b99f-f7a2b8f8269c", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430645274500}, "additional": {"logType": "detail", "children": [], "durationId": "12d449d6-95d8-4cd3-89d9-e2359710d15b"}}, {"head": {"id": "7615c0e5-a8ce-44a7-8e45-006f51b2b460", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430645730900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a20586-4b62-4b2a-8933-ec57da6e4ad9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430645837400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a98496-40b5-442a-aaf7-1b80924c2368", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430646672000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fcf6399-2df9-4b98-9fc2-12cc6c0e4c0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430650806500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffdb9677-74b5-4759-919e-95b280203a05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430650908300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c58570d2-dcdf-4950-9cfe-bde903c3686b", "name": "entry : default@CollectDebugSymbol cost memory 0.2381134033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430650983800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "529c4014-6e5c-41d4-aade-1971bf777cae", "name": "runTaskFromQueue task cost before running: 3 s 203 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430651059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa74acf4-988e-4bc3-9361-208a6b0ae85c", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430646661300, "endTime": 34430651115600, "totalTime": 4379300}, "additional": {"logType": "info", "children": [], "durationId": "12d449d6-95d8-4cd3-89d9-e2359710d15b"}}, {"head": {"id": "2706ef78-d736-4bce-83f2-bd510b622b18", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430652529200, "endTime": 34430652848100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d0f350de-7f46-479a-84cc-b1cfe9e07501", "logId": "b2757ad1-cf71-4440-9810-f6f63071f9cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0f350de-7f46-479a-84cc-b1cfe9e07501", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430652487800}, "additional": {"logType": "detail", "children": [], "durationId": "2706ef78-d736-4bce-83f2-bd510b622b18"}}, {"head": {"id": "5d3427d9-b777-4ed0-a4fb-cca67a510138", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430652555500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab68c2eb-676e-4b4a-9c1b-aeb6ce9be2dd", "name": "entry : assembleHap cost memory 0.01137542724609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430652706200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc05d17-305b-4910-882f-11bd96de90b9", "name": "runTaskFromQueue task cost before running: 3 s 204 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430652791000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2757ad1-cf71-4440-9810-f6f63071f9cb", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430652529200, "endTime": 34430652848100, "totalTime": 243500}, "additional": {"logType": "info", "children": [], "durationId": "2706ef78-d736-4bce-83f2-bd510b622b18"}}, {"head": {"id": "47707357-acae-4da5-8212-210a3ebdc9c2", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660357600, "endTime": 34430660380200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "73891220-a399-46c9-845b-93aae8f94607", "logId": "614b4f5f-24b0-4120-a42c-f2b0a6e53eb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "614b4f5f-24b0-4120-a42c-f2b0a6e53eb8", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660357600, "endTime": 34430660380200}, "additional": {"logType": "info", "children": [], "durationId": "47707357-acae-4da5-8212-210a3ebdc9c2"}}, {"head": {"id": "e67e5ed1-52b5-47e2-a27c-65bcea4f3822", "name": "BUILD SUCCESSFUL in 3 s 212 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660417500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "e5079d7a-d349-454c-89ba-b8f8067e2a53", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34427448952400, "endTime": 34430660661100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 18, "minute": 3}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "77a87526-8b0a-44c6-8050-24be199fb70f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da75c865-7de6-43fe-84e0-7e6373d00662", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660748600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f780b63-9cd4-4833-8150-192b14dfb341", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660798600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bce0162f-f76e-4cc3-aa29-0e1f55fda0f5", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660843500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "445233b4-fd2f-421a-8a40-94ae9807504a", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430660924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e9b6fb3-6692-40e8-a81a-d9548bdf22a1", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430661992700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53d4dec2-b50c-4414-9c30-92e83c3a54a1", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430662500800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a24f3f10-d3d7-48c6-903a-ad5f6301c5d8", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430662704800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94207bfc-cc36-4fe7-bde6-41bb34bb84a2", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430662769100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec971271-fc36-417d-a67f-6e75c3c3504b", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430662824300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de8cebe-409a-4fb4-8659-3ab548760f1b", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430663043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7acb090a-318f-4379-b51e-91d55dfa9316", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430663910200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a648f071-274c-4503-addb-e367c5d22333", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430664247900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b7874a2-64f9-484f-8f9b-0a07e8f3b8a9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430664345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c2c908d-bb3b-4bd9-8327-67dd34d78ea7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430664402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23cfbcd1-ee9f-431f-9476-2bf5ef7c6d76", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430664453900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56a778f2-7e7e-497c-93ae-8a730ea78dcc", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430664501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "225ae7ab-bc84-4824-bd8e-83ccd1f231ce", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430664852000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07158888-c515-45f1-a4fe-5254a7f72fc2", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430665081700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84bf4bb8-7de6-4e8d-b90c-54f4b17354ee", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430665265900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e33aa1a-38bc-4fae-afe0-db835792e700", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430665502700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e667d6-d11f-49c1-9737-ec92c692d1ab", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430665573900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99969587-6f98-49f7-a08e-73b5772c0f2b", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430665629000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34bd50b0-a900-4613-9a60-3fe70a35946f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430667573300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a6e208-f687-4898-be0c-5a036776ff77", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430668020200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3888d80a-03e0-44f6-b2f8-a73b3fd38c70", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430669091600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825fa976-5d52-4565-9be4-a2ef0ec6f3dd", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430669486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084e309a-d256-44fb-a115-b080db44ecc1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430669855700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "375e4bff-1d6a-4bfc-8f99-dfa5fe6a0a33", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430670466500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9120a2a5-b0f5-4472-b7e3-ab0da9ee9841", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430670536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd50c2d-b35a-42a5-aa3a-c3f0d46b04d3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430670791100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b804838-6b0e-408d-a2c5-b523988d3498", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430671037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "986f554f-737e-4e0d-bc51-3cf707ff153e", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430671616200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27ca31f7-2807-4fae-8d4f-689a9128ae38", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430673548500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2eefc67-85f9-4b30-8010-e0674b20a8dc", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430674074300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2055d55b-1c81-425e-abba-d1bf9c20e7d5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430674978700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d6d419b-02ca-41de-b19d-c880d24b98b5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430675185600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3e1fd2-11bf-4690-83ce-a6175e10cf9c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430675370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f34f377-c6aa-4463-8b75-282e177589ed", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430675931100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8929e84-08de-4d90-9776-c2065bd6ad58", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430676250200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f85df3f-52c7-4fd6-b25b-ca12825d3b18", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430676355800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e447b5b7-ea0a-473c-8772-d26f1e343448", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430676410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "089cad1a-ee4c-4180-bf01-8519aa876c19", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430677319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1e7557c-5852-4c0b-b5b8-f22358c3487f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430677663600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7dc3a1-a71e-4398-a7b5-33fdf7538dd5", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430678442400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d490aeb-eb41-4279-81c5-df93667e9e47", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430686395000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8af2ff6-50ef-4b4f-84ee-4a02e3d60bb9", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430686661900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd765999-06cc-47e5-a44b-eb7148137fc3", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430688838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd8ad729-a365-4887-8171-3a5ef9064dca", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430688930100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdc8b0d-9ef9-479b-9d47-b22721cc0a2e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430689271500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72e31ad6-64b0-44b1-8522-4a5132650360", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430690119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f22ea2c-6c09-448d-968c-7772a223120e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430690446200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93302b31-6600-45fd-af12-0575172cd63e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430690661700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42315163-ddf8-4302-acf9-a3f257995efb", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430690888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7cfe06-0706-4e6a-b38b-1fb9eb7e6d9c", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430691035600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4e9ba1-40b4-4586-9f87-bc7de3b8dc2d", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430691101800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4baf9ad-dbef-4192-aa4a-a5c388535bc6", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430691367400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449e851d-cb50-41c5-94cf-628d77e81ba0", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430693490200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0c31e4-e85a-452f-b205-d07669e5aef4", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430693753600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0588fe28-d68a-4dca-b129-bffb5b813228", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430694018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12b24232-1c2d-458b-8d04-d338cdd1f722", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34430694246900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}