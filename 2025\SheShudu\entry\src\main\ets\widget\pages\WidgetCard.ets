@Entry
@Component
struct WidgetCard {
  /*
   * The title.
   */
  readonly title: string = '趣味数独，锻炼逻辑思维。';
  /*
   * The action type.
   */
  readonly actionType: string = 'router';
  /*
   * The ability name.
   */
  readonly abilityName: string = 'EntryAbility';
  /*
   * The message.
   */
  readonly message: string = 'add detail';
  /*
   * The width percentage setting.
   */
  readonly fullWidthPercent: string = '100%';
  /*
   * The height percentage setting.
   */
  readonly fullHeightPercent: string = '100%';

  build() {
    FormLink({
      action: this.actionType,
      abilityName: this.abilityName,
      params: {
        message: this.message
      }
    }) {
      Row() {
        Column() {
          Row(){
            Text(this.title)
              .fontSize($r('app.float.font_size'))
              .fontWeight(FontWeight.Medium)
              .fontColor($r('sys.color.font_primary'))
          }.width('90%')

          Button("点击开始").backgroundColor(Color.Pink).width('60%')

        }
        .width(this.fullWidthPercent)
        .height(this.fullHeightPercent)
        .justifyContent(FlexAlign.SpaceAround)
      }
      .height(this.fullHeightPercent)
    }
  }
}