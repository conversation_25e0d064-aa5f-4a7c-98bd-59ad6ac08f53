if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    message?: string;
    pageInfos?: NavPathStack;
    currentTime?: string;
    scroller?: Scroller;
}
import authentication from "@hms:core.authentication";
import type { BusinessError } from "@ohos:base";
import hilog from "@ohos:hilog";
const DOMAIN = 0x0000;
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__message = new ObservedPropertySimplePU('Hello World', this, "message");
        this.__pageInfos = new ObservedPropertyObjectPU(new NavPathStack(), this, "pageInfos");
        this.addProvidedVar("naviStack", this.__pageInfos, false);
        this.addProvidedVar("pageInfos", this.__pageInfos, false);
        this.__currentTime = new ObservedPropertySimplePU('', this, "currentTime");
        this.scroller = new Scroller();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.message !== undefined) {
            this.message = params.message;
        }
        if (params.pageInfos !== undefined) {
            this.pageInfos = params.pageInfos;
        }
        if (params.currentTime !== undefined) {
            this.currentTime = params.currentTime;
        }
        if (params.scroller !== undefined) {
            this.scroller = params.scroller;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__message.purgeDependencyOnElmtId(rmElmtId);
        this.__pageInfos.purgeDependencyOnElmtId(rmElmtId);
        this.__currentTime.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        this.__pageInfos.aboutToBeDeleted();
        this.__currentTime.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __message: ObservedPropertySimplePU<string>;
    get message() {
        return this.__message.get();
    }
    set message(newValue: string) {
        this.__message.set(newValue);
    }
    private __pageInfos: ObservedPropertyObjectPU<NavPathStack>;
    get pageInfos() {
        return this.__pageInfos.get();
    }
    set pageInfos(newValue: NavPathStack) {
        this.__pageInfos.set(newValue);
    }
    private __currentTime: ObservedPropertySimplePU<string>;
    get currentTime() {
        return this.__currentTime.get();
    }
    set currentTime(newValue: string) {
        this.__currentTime.set(newValue);
    }
    private scroller: Scroller;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Navigation.create(this.pageInfos, { moduleName: "entry", pagePath: "entry/src/main/ets/pages/Index", isUserCreateStack: true });
            Navigation.hideTitleBar(true);
            Navigation.mode(NavigationMode.Stack);
            Navigation.backgroundColor('#F8F9FA');
        }, Navigation);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create(this.scroller);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
            Scroll.edgeEffect(EdgeEffect.Spring);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 0 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部欢迎区域
            Column.create();
            // 顶部欢迎区域
            Column.width('100%');
            // 顶部欢迎区域
            Column.linearGradient({
                angle: 135,
                colors: [['#E8F4FD', 0.0], ['#F0F8FF', 0.5], ['#FFFFFF', 1.0]]
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 16 });
            Column.width('90%');
            Column.padding({ top: 40, bottom: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 游戏Logo和标题
            Row.create({ space: 12 });
            // 游戏Logo和标题
            Row.width('100%');
            // 游戏Logo和标题
            Row.justifyContent(FlexAlign.Start);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777223, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(60);
            Image.height(60);
            Image.borderRadius(12);
            Image.shadow({
                radius: 8,
                color: '#1F000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 4 });
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('数海鹅独');
            Text.fontSize(32);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#2C3E50');
            Text.letterSpacing(2);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('智慧数独，趣味无穷');
            Text.fontSize(14);
            Text.fontColor('#7F8C8D');
            Text.opacity(0.8);
        }, Text);
        Text.pop();
        Column.pop();
        // 游戏Logo和标题
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 欢迎文字
            Text.create('欢迎来到数独世界，挑战你的逻辑思维！');
            // 欢迎文字
            Text.fontSize(16);
            // 欢迎文字
            Text.fontColor('#34495E');
            // 欢迎文字
            Text.textAlign(TextAlign.Center);
            // 欢迎文字
            Text.lineHeight(24);
            // 欢迎文字
            Text.margin({ top: 8 });
        }, Text);
        // 欢迎文字
        Text.pop();
        Column.pop();
        // 顶部欢迎区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主要功能区域
            Column.create({ space: 20 });
            // 主要功能区域
            Column.width('100%');
            // 主要功能区域
            Column.backgroundColor('#F8F9FA');
            // 主要功能区域
            Column.padding({ top: 30 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 开始游戏按钮
            Button.createWithChild();
            // 开始游戏按钮
            Button.width('85%');
            // 开始游戏按钮
            Button.height(56);
            // 开始游戏按钮
            Button.backgroundColor('#3498DB');
            // 开始游戏按钮
            Button.borderRadius(28);
            // 开始游戏按钮
            Button.shadow({
                radius: 12,
                color: '#1F3498DB',
                offsetX: 0,
                offsetY: 4
            });
            // 开始游戏按钮
            Button.onClick(() => {
                this.pageInfos.pushPathByName('shuduGame', null);
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777259, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(24);
            Image.height(24);
            Image.fillColor('#FFFFFF');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('开始游戏');
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        Row.pop();
        // 开始游戏按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 功能卡片区域
            Column.create({ space: 16 });
            // 功能卡片区域
            Column.width('90%');
            // 功能卡片区域
            Column.margin({ top: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('游戏特色');
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#2C3E50');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第一行卡片
            Row.create({ space: 12 });
            // 第一行卡片
            Row.width('100%');
            // 第一行卡片
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 经典模式卡片
            Column.create({ space: 8 });
            // 经典模式卡片
            Column.width('48%');
            // 经典模式卡片
            Column.height(130);
            // 经典模式卡片
            Column.backgroundColor('#FFFFFF');
            // 经典模式卡片
            Column.borderRadius(16);
            // 经典模式卡片
            Column.padding(16);
            // 经典模式卡片
            Column.justifyContent(FlexAlign.Center);
            // 经典模式卡片
            Column.shadow({
                radius: 8,
                color: '#0F000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777243, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(40);
            Image.height(40);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('经典模式');
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#2C3E50');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('传统1-9数字\n逻辑推理挑战');
            Text.fontSize(12);
            Text.fontColor('#7F8C8D');
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(16);
        }, Text);
        Text.pop();
        // 经典模式卡片
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡通模式卡片
            Column.create({ space: 8 });
            // 卡通模式卡片
            Column.width('48%');
            // 卡通模式卡片
            Column.height(130);
            // 卡通模式卡片
            Column.backgroundColor('#FFFFFF');
            // 卡通模式卡片
            Column.borderRadius(16);
            // 卡通模式卡片
            Column.padding(16);
            // 卡通模式卡片
            Column.justifyContent(FlexAlign.Center);
            // 卡通模式卡片
            Column.shadow({
                radius: 8,
                color: '#0F000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777234, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(40);
            Image.height(40);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('卡通模式');
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#2C3E50');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('可爱动物图标\n趣味数独体验');
            Text.fontSize(12);
            Text.fontColor('#7F8C8D');
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(16);
        }, Text);
        Text.pop();
        // 卡通模式卡片
        Column.pop();
        // 第一行卡片
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 第二行卡片
            Row.create({ space: 12 });
            // 第二行卡片
            Row.width('100%');
            // 第二行卡片
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 自在模式卡片
            Column.create({ space: 8 });
            // 自在模式卡片
            Column.width('48%');
            // 自在模式卡片
            Column.height(130);
            // 自在模式卡片
            Column.backgroundColor('#FFFFFF');
            // 自在模式卡片
            Column.borderRadius(16);
            // 自在模式卡片
            Column.padding(16);
            // 自在模式卡片
            Column.justifyContent(FlexAlign.Center);
            // 自在模式卡片
            Column.shadow({
                radius: 8,
                color: '#0F000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777244, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(40);
            Image.height(40);
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('自在模式');
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#2C3E50');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('空白棋盘开始\n自由创作挑战');
            Text.fontSize(12);
            Text.fontColor('#7F8C8D');
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(16);
        }, Text);
        Text.pop();
        // 自在模式卡片
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 多人协作卡片
            Column.create({ space: 8 });
            // 多人协作卡片
            Column.width('48%');
            // 多人协作卡片
            Column.height(130);
            // 多人协作卡片
            Column.backgroundColor('#FFFFFF');
            // 多人协作卡片
            Column.borderRadius(16);
            // 多人协作卡片
            Column.padding(16);
            // 多人协作卡片
            Column.justifyContent(FlexAlign.Center);
            // 多人协作卡片
            Column.shadow({
                radius: 8,
                color: '#0F000000',
                offsetX: 0,
                offsetY: 2
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777253, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(40);
            Image.height(40);
            Image.fillColor('#E74C3C');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('多人协作');
            Text.fontSize(16);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#2C3E50');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('双人轮流填写\n共同完成挑战');
            Text.fontSize(12);
            Text.fontColor('#7F8C8D');
            Text.textAlign(TextAlign.Center);
            Text.lineHeight(16);
        }, Text);
        Text.pop();
        // 多人协作卡片
        Column.pop();
        // 第二行卡片
        Row.pop();
        // 功能卡片区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快捷操作区域
            Column.create({ space: 16 });
            // 快捷操作区域
            Column.width('90%');
            // 快捷操作区域
            Column.margin({ top: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('快捷操作');
            Text.fontSize(20);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#2C3E50');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 12 });
            Row.width('100%');
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 玩法介绍按钮
            Button.createWithChild();
            // 玩法介绍按钮
            Button.width('48%');
            // 玩法介绍按钮
            Button.height(48);
            // 玩法介绍按钮
            Button.backgroundColor('#27AE60');
            // 玩法介绍按钮
            Button.borderRadius(24);
            // 玩法介绍按钮
            Button.onClick(() => {
                this.pageInfos.pushPathByName('gameRules', null);
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777251, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor('#FFFFFF');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('玩法介绍');
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        Row.pop();
        // 玩法介绍按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 个人中心按钮
            Button.createWithChild();
            // 个人中心按钮
            Button.width('48%');
            // 个人中心按钮
            Button.height(48);
            // 个人中心按钮
            Button.backgroundColor('#9B59B6');
            // 个人中心按钮
            Button.borderRadius(24);
            // 个人中心按钮
            Button.onClick(() => {
                this.pageInfos.pushPathByName('personalization', null);
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777253, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            Image.width(20);
            Image.height(20);
            Image.fillColor('#FFFFFF');
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('个人中心');
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        Row.pop();
        // 个人中心按钮
        Button.pop();
        Row.pop();
        // 快捷操作区域
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 底部装饰区域
            Column.create({ space: 12 });
            // 底部装饰区域
            Column.width('100%');
            // 底部装饰区域
            Column.padding({ top: 30, bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create({ space: 8 });
            Row.justifyContent(FlexAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": -1, "type": -1, params: [`app.media.animal${item}`], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(32);
                    Image.height(32);
                    Image.opacity(0.6);
                }, Image);
            };
            this.forEachUpdateFunction(elmtId, [1, 2, 3, 4, 5], forEachItemGenFunction, (item: number) => item.toString(), false, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('享受数独的乐趣，锻炼逻辑思维');
            Text.fontSize(14);
            Text.fontColor('#95A5A6');
            Text.textAlign(TextAlign.Center);
            Text.opacity(0.8);
        }, Text);
        Text.pop();
        // 底部装饰区域
        Column.pop();
        // 主要功能区域
        Column.pop();
        Column.pop();
        Scroll.pop();
        Navigation.pop();
    }
    aboutToAppear() {
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
        this.loginWithHuaweiID();
    }
    /**
     * Sample code for using HUAWEI ID to log in to atomic service.
     * According to the Atomic Service Review Guide, when a atomic service has an account system,
     * the option to log in with a HUAWEI ID must be provided.
     * The following presets the atomic service to use the HUAWEI ID silent login function.
     * To enable the atomic service to log in successfully using the HUAWEI ID, please refer
     * to the HarmonyOS HUAWEI ID Access Guide to configure the client ID and fingerprint certificate.
     */
    private loginWithHuaweiID() {
        // Create a login request and set parameters
        const loginRequest = new authentication.HuaweiIDProvider().createLoginWithHuaweiIDRequest();
        // Whether to forcibly launch the HUAWEI ID login page when the user is not logged in with the HUAWEI ID
        loginRequest.forceLogin = false;
        // Execute login request
        const controller = new authentication.AuthenticationController();
        controller.executeRequest(loginRequest).then((data) => {
            const loginWithHuaweiIDResponse = data as authentication.LoginWithHuaweiIDResponse;
            const authCode = loginWithHuaweiIDResponse.data?.authorizationCode;
            // Send authCode to the backend in exchange for unionID, session
        }).catch((error: BusinessError) => {
            hilog.error(DOMAIN, 'testTag', 'error: %{public}s', JSON.stringify(error));
            if (error.code === authentication.AuthenticationErrorCode.ACCOUNT_NOT_LOGGED_IN) {
                // HUAWEI ID is not logged in, it is recommended to jump to the login guide page
            }
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.atomicservice.6917576098804821135", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
