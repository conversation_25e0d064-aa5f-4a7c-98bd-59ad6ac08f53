if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    message?: string;
    pageInfos?: NavPathStack;
}
import authentication from "@hms:core.authentication";
import type { BusinessError } from "@ohos:base";
import hilog from "@ohos:hilog";
const DOMAIN = 0x0000;
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__message = new ObservedPropertySimplePU('Hello World', this, "message");
        this.__pageInfos = new ObservedPropertyObjectPU(new NavPathStack(), this, "pageInfos");
        this.addProvidedVar("naviStack", this.__pageInfos, false);
        this.addProvidedVar("pageInfos", this.__pageInfos, false);
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.message !== undefined) {
            this.message = params.message;
        }
        if (params.pageInfos !== undefined) {
            this.pageInfos = params.pageInfos;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__message.purgeDependencyOnElmtId(rmElmtId);
        this.__pageInfos.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        this.__pageInfos.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __message: ObservedPropertySimplePU<string>;
    get message() {
        return this.__message.get();
    }
    set message(newValue: string) {
        this.__message.set(newValue);
    }
    private __pageInfos: ObservedPropertyObjectPU<NavPathStack>;
    get pageInfos() {
        return this.__pageInfos.get();
    }
    set pageInfos(newValue: NavPathStack) {
        this.__pageInfos.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Navigation.create(this.pageInfos, { moduleName: "entry", pagePath: "entry/src/main/ets/pages/Index", isUserCreateStack: true });
            Navigation.hideTitleBar(true);
            Navigation.mode(NavigationMode.Stack);
        }, Navigation);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            GridRow.create({
                columns: {
                    sm: 1,
                    md: 2,
                    lg: 4 // 大屏4列
                },
                gutter: { x: 12, y: 12 }
            });
        }, GridRow);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            GridCol.create({
                span: {
                    sm: 1,
                    md: 2,
                    lg: 4 // 大屏占1格
                }
            });
        }, GridCol);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('数海鹅独');
        }, Text);
        Text.pop();
        Column.pop();
        GridCol.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            GridCol.create({
                span: {
                    sm: 1,
                    md: 1,
                    lg: 1 // 大屏占1格
                }
            });
        }, GridCol);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('游戏开始');
            Text.onClick(() => {
                //跳转到游戏界面
                this.pageInfos.pushPathByName('shuduGame', null);
            });
        }, Text);
        Text.pop();
        Column.pop();
        GridCol.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            GridCol.create({
                span: {
                    sm: 1,
                    md: 1,
                    lg: 1 // 大屏占1格
                }
            });
        }, GridCol);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.height(200);
            Column.justifyContent(FlexAlign.Center);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('玩法介绍');
            Text.onClick(() => {
                //跳转到游戏界面
                this.pageInfos.pushPathByName('gameRules', null);
            });
        }, Text);
        Text.pop();
        Column.pop();
        GridCol.pop();
        GridRow.pop();
        Navigation.pop();
    }
    aboutToAppear() {
        hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
        this.loginWithHuaweiID();
    }
    /**
     * Sample code for using HUAWEI ID to log in to atomic service.
     * According to the Atomic Service Review Guide, when a atomic service has an account system,
     * the option to log in with a HUAWEI ID must be provided.
     * The following presets the atomic service to use the HUAWEI ID silent login function.
     * To enable the atomic service to log in successfully using the HUAWEI ID, please refer
     * to the HarmonyOS HUAWEI ID Access Guide to configure the client ID and fingerprint certificate.
     */
    private loginWithHuaweiID() {
        // Create a login request and set parameters
        const loginRequest = new authentication.HuaweiIDProvider().createLoginWithHuaweiIDRequest();
        // Whether to forcibly launch the HUAWEI ID login page when the user is not logged in with the HUAWEI ID
        loginRequest.forceLogin = false;
        // Execute login request
        const controller = new authentication.AuthenticationController();
        controller.executeRequest(loginRequest).then((data) => {
            const loginWithHuaweiIDResponse = data as authentication.LoginWithHuaweiIDResponse;
            const authCode = loginWithHuaweiIDResponse.data?.authorizationCode;
            // Send authCode to the backend in exchange for unionID, session
        }).catch((error: BusinessError) => {
            hilog.error(DOMAIN, 'testTag', 'error: %{public}s', JSON.stringify(error));
            if (error.code === authentication.AuthenticationErrorCode.ACCOUNT_NOT_LOGGED_IN) {
                // HUAWEI ID is not logged in, it is recommended to jump to the login guide page
            }
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.atomicservice.6917576098804821135", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
