{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "e47aaef2-e0bd-4874-ba02-b3a92149dd55", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809036799300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e39aae50-79ab-43c5-a282-5497b25ae0e5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809044072600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd5773a-d602-4166-8d85-65f7f143f469", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809044462200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a1085d-7c9c-40cb-93ca-c3a1f0763186", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809050474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c015915c-030f-4fd4-b43a-8829357bebac", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959441169900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959447687700, "endTime": 26959951998900}, "additional": {"children": ["47541469-c367-476f-babe-d8e062b70f2f", "1da6dabe-e56d-4825-9b50-fcc106883c53", "bc0c4529-d3a1-45ac-9a01-018b821df2f6", "3f70abaa-1f16-47b6-a3dc-43cc9140b48f", "73e8779e-7750-4a97-a014-46e0a5c946c2", "edd2e185-94e1-4a2e-a7ed-05c1901d4fdb", "8a912f4c-685d-48f0-8369-78bd856bc14a"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "1a6706aa-026f-4aad-9237-a1d659a7272c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "47541469-c367-476f-babe-d8e062b70f2f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959447690100, "endTime": 26959463868500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "958a1812-f8ce-4bfc-b53a-216154fef520"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1da6dabe-e56d-4825-9b50-fcc106883c53", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959463891300, "endTime": 26959950088700}, "additional": {"children": ["39e067b3-b4a3-4215-9788-2e19128d92a1", "ddd95b20-adf1-4811-9f7b-07ffc941d111", "86267a0e-10b8-4e1b-be1b-075637998b3f", "09441e1a-fee3-497c-a713-f075e93c4e95", "8c767da0-aa54-4c67-a042-151a4a24d7f3", "9ea2e26b-ea12-4531-9928-3e73aa128f38", "2542527f-1a1c-4547-bd77-d68d0e8bc764", "ce060ede-f0dd-4d63-a600-857170c46f5c", "d9e66986-e6f9-4264-bc2e-196b4a5ffc59"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc0c4529-d3a1-45ac-9a01-018b821df2f6", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959950114300, "endTime": 26959951953600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "5ab4cb79-d833-401d-82ed-a3ead87876c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f70abaa-1f16-47b6-a3dc-43cc9140b48f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959951958000, "endTime": 26959951992800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "17a2439b-55a1-408e-ad88-47d3aa0334ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73e8779e-7750-4a97-a014-46e0a5c946c2", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959451055700, "endTime": 26959451104800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "7c5878d0-5399-4a30-be6c-7856b07bd8c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c5878d0-5399-4a30-be6c-7856b07bd8c7", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959451055700, "endTime": 26959451104800}, "additional": {"logType": "info", "children": [], "durationId": "73e8779e-7750-4a97-a014-46e0a5c946c2", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "edd2e185-94e1-4a2e-a7ed-05c1901d4fdb", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959456923400, "endTime": 26959456953200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "cfcb6382-808e-4c54-8706-5b76666c5e9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfcb6382-808e-4c54-8706-5b76666c5e9f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959456923400, "endTime": 26959456953200}, "additional": {"logType": "info", "children": [], "durationId": "edd2e185-94e1-4a2e-a7ed-05c1901d4fdb", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "8ab4ee8b-306b-4131-bf4f-14f93369396e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959457011500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a663cd-2937-4d2a-91f6-c8e024fbc4c7", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959463678500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958a1812-f8ce-4bfc-b53a-216154fef520", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959447690100, "endTime": 26959463868500}, "additional": {"logType": "info", "children": [], "durationId": "47541469-c367-476f-babe-d8e062b70f2f", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "39e067b3-b4a3-4215-9788-2e19128d92a1", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959470078200, "endTime": 26959470096000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "95f9e5b5-9768-4a15-84a1-6c3d0c84346b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddd95b20-adf1-4811-9f7b-07ffc941d111", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959470116200, "endTime": 26959476397700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "57d05caa-57de-4ef0-8e28-8de3a0f74e3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86267a0e-10b8-4e1b-be1b-075637998b3f", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959476429400, "endTime": 26959681174400}, "additional": {"children": ["9519af8f-00c1-4a46-b8d5-2de5a3838373", "98e3fd17-b7a1-44de-be02-c469ed3684d3", "fc88af65-bc37-436c-962e-2a7ddd3ad79d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "913f5d72-33f2-4dd0-87d5-34e6d9ce54fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09441e1a-fee3-497c-a713-f075e93c4e95", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959681232700, "endTime": 26959783732600}, "additional": {"children": ["13fba7d3-599b-46e6-8318-c64a4193db74"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "caeebbf4-f4a4-4566-994a-b5cd494e4dc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c767da0-aa54-4c67-a042-151a4a24d7f3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959783912400, "endTime": 26959908302800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "47e8ec90-d7cc-4919-a897-79fa510d13c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ea2e26b-ea12-4531-9928-3e73aa128f38", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959909440000, "endTime": 26959935119400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "e94776c4-869b-4950-ad34-19b01424c9a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2542527f-1a1c-4547-bd77-d68d0e8bc764", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959935149100, "endTime": 26959949874400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "efeaa945-291f-40bd-b31e-5e48fd636a14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce060ede-f0dd-4d63-a600-857170c46f5c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959949896100, "endTime": 26959950073500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "09c16a6f-348a-4109-b6c8-7ec1e44d579d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95f9e5b5-9768-4a15-84a1-6c3d0c84346b", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959470078200, "endTime": 26959470096000}, "additional": {"logType": "info", "children": [], "durationId": "39e067b3-b4a3-4215-9788-2e19128d92a1", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "57d05caa-57de-4ef0-8e28-8de3a0f74e3e", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959470116200, "endTime": 26959476397700}, "additional": {"logType": "info", "children": [], "durationId": "ddd95b20-adf1-4811-9f7b-07ffc941d111", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "9519af8f-00c1-4a46-b8d5-2de5a3838373", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959477145700, "endTime": 26959477164800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86267a0e-10b8-4e1b-be1b-075637998b3f", "logId": "bccb26c0-829b-42c8-811e-633ffe4726d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bccb26c0-829b-42c8-811e-633ffe4726d0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959477145700, "endTime": 26959477164800}, "additional": {"logType": "info", "children": [], "durationId": "9519af8f-00c1-4a46-b8d5-2de5a3838373", "parent": "913f5d72-33f2-4dd0-87d5-34e6d9ce54fc"}}, {"head": {"id": "98e3fd17-b7a1-44de-be02-c469ed3684d3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959479022400, "endTime": 26959678245900}, "additional": {"children": ["0aaf8eb7-6c4b-46cb-88ed-c3a06b777615", "1dcb1aa6-df51-45df-b639-b24e6786fdd6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86267a0e-10b8-4e1b-be1b-075637998b3f", "logId": "540c162d-fa1e-45cc-8762-9366f75967f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aaf8eb7-6c4b-46cb-88ed-c3a06b777615", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959479025000, "endTime": 26959486144000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98e3fd17-b7a1-44de-be02-c469ed3684d3", "logId": "2d11074d-db0e-4f64-aeac-66603b699c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dcb1aa6-df51-45df-b639-b24e6786fdd6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959486169000, "endTime": 26959678169800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "98e3fd17-b7a1-44de-be02-c469ed3684d3", "logId": "d37b1594-35e7-432f-92c7-e229ebc6a8c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "244cc765-3a7d-4a05-88e0-6e151593d87e", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959479029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effabaff-402e-4b7e-a7b5-d26462619cc9", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959485888500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d11074d-db0e-4f64-aeac-66603b699c8d", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959479025000, "endTime": 26959486144000}, "additional": {"logType": "info", "children": [], "durationId": "0aaf8eb7-6c4b-46cb-88ed-c3a06b777615", "parent": "540c162d-fa1e-45cc-8762-9366f75967f7"}}, {"head": {"id": "eb96cda4-8d07-4cfe-b5ab-764ee592aa91", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959486191600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc3ea5ed-f555-4d73-b044-e78be4c63899", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959494023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "518bd730-a106-48cd-a197-1fde5defc182", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959494163800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7650bbb6-d713-4b7f-aa77-a187f1ffe49c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959494313400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4693d084-f411-4067-a457-00668118a385", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959494406500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "581913b5-aa4c-4015-b3a8-06385c0b4e53", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959496042600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80c708cb-c741-4f18-8162-ffcf866f1f96", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959507384000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "908c9419-e7a7-4ec4-a104-069c9e61ee14", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959548607900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28ad123c-cc90-4ee0-baf4-f903381c0be0", "name": "Sdk init in 102 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959610653300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af8db2b-908e-459b-9c39-6d34fa2a9ae1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959611219500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 58}, "markType": "other"}}, {"head": {"id": "bf4b0727-610e-47be-8181-f35da8dfbbc3", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959611446600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 58}, "markType": "other"}}, {"head": {"id": "be4bd87e-e2ca-4d97-ae4c-32c89a607921", "name": "Project task initialization takes 64 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959677442800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbddff9b-d813-47c8-bf8b-f369db00e8be", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959677943700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c37ae67-35ca-432d-b0d1-64ef7a0268ed", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959678032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be2ede12-9012-4975-9029-a24dd9d5c4e1", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959678087400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d37b1594-35e7-432f-92c7-e229ebc6a8c8", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959486169000, "endTime": 26959678169800}, "additional": {"logType": "info", "children": [], "durationId": "1dcb1aa6-df51-45df-b639-b24e6786fdd6", "parent": "540c162d-fa1e-45cc-8762-9366f75967f7"}}, {"head": {"id": "540c162d-fa1e-45cc-8762-9366f75967f7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959479022400, "endTime": 26959678245900}, "additional": {"logType": "info", "children": ["2d11074d-db0e-4f64-aeac-66603b699c8d", "d37b1594-35e7-432f-92c7-e229ebc6a8c8"], "durationId": "98e3fd17-b7a1-44de-be02-c469ed3684d3", "parent": "913f5d72-33f2-4dd0-87d5-34e6d9ce54fc"}}, {"head": {"id": "fc88af65-bc37-436c-962e-2a7ddd3ad79d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959680417500, "endTime": 26959681111400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86267a0e-10b8-4e1b-be1b-075637998b3f", "logId": "8162feb4-86fb-4e30-b6ce-956ea7890393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8162feb4-86fb-4e30-b6ce-956ea7890393", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959680417500, "endTime": 26959681111400}, "additional": {"logType": "info", "children": [], "durationId": "fc88af65-bc37-436c-962e-2a7ddd3ad79d", "parent": "913f5d72-33f2-4dd0-87d5-34e6d9ce54fc"}}, {"head": {"id": "913f5d72-33f2-4dd0-87d5-34e6d9ce54fc", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959476429400, "endTime": 26959681174400}, "additional": {"logType": "info", "children": ["bccb26c0-829b-42c8-811e-633ffe4726d0", "540c162d-fa1e-45cc-8762-9366f75967f7", "8162feb4-86fb-4e30-b6ce-956ea7890393"], "durationId": "86267a0e-10b8-4e1b-be1b-075637998b3f", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "13fba7d3-599b-46e6-8318-c64a4193db74", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959682964100, "endTime": 26959783643800}, "additional": {"children": ["11368dd3-987f-4188-b4cc-c5a7ee7fd191", "c6d19555-363a-489b-b549-b2ef80c97723", "42eb840e-6084-4180-9c54-5d961cd6b415"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09441e1a-fee3-497c-a713-f075e93c4e95", "logId": "58207eb1-54b6-4993-b901-78991ea3f184"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11368dd3-987f-4188-b4cc-c5a7ee7fd191", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959688229600, "endTime": 26959688254200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13fba7d3-599b-46e6-8318-c64a4193db74", "logId": "bba9512d-5347-4396-b823-17fdc0199598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bba9512d-5347-4396-b823-17fdc0199598", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959688229600, "endTime": 26959688254200}, "additional": {"logType": "info", "children": [], "durationId": "11368dd3-987f-4188-b4cc-c5a7ee7fd191", "parent": "58207eb1-54b6-4993-b901-78991ea3f184"}}, {"head": {"id": "c6d19555-363a-489b-b549-b2ef80c97723", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959693721000, "endTime": 26959779430100}, "additional": {"children": ["720ffe6e-ea63-462d-a332-7c33942a39de", "f425e847-579c-4cb0-82e1-f25bb098f379"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13fba7d3-599b-46e6-8318-c64a4193db74", "logId": "754bc153-16f5-498e-b179-114de257e1a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "720ffe6e-ea63-462d-a332-7c33942a39de", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959693723000, "endTime": 26959701128000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6d19555-363a-489b-b549-b2ef80c97723", "logId": "78aa898f-6e23-4094-94c9-9b1acb50f221"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f425e847-579c-4cb0-82e1-f25bb098f379", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959701165400, "endTime": 26959779401900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6d19555-363a-489b-b549-b2ef80c97723", "logId": "76feba28-2380-441f-a86c-530b2e0df668"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2be517f8-a3d1-4228-8a4f-ded97c26af5f", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959693728200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f05e53d-3fb4-447c-8fe7-e10298f4e5c1", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959700405400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78aa898f-6e23-4094-94c9-9b1acb50f221", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959693723000, "endTime": 26959701128000}, "additional": {"logType": "info", "children": [], "durationId": "720ffe6e-ea63-462d-a332-7c33942a39de", "parent": "754bc153-16f5-498e-b179-114de257e1a4"}}, {"head": {"id": "230278b3-517b-432a-89c8-396561562d39", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959701267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6249e539-1f74-4bcb-8323-425d911461c2", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959756679800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d71e6eb7-354d-455f-9fa9-95d8b379e833", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959756921500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1004f7e-08d7-45c0-8c81-e88a9ae7c1a5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959757397000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f13044ac-923a-48ec-8c6c-ff7135af8588", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959758672800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70cb408d-5228-475a-9c72-5da7cd32174e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959758943200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de4ee8a0-2a50-4fc5-a0db-5b231dd18cd4", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959759080200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d59f013-f687-4e8d-9549-cfe2797e7e9e", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959759199800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9fe008b-8bb0-4e85-959b-016920032639", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959765122200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc2fcadd-0582-4c63-9edf-36acb1fe869d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959765302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bef727b-8040-491e-a781-594143f0348e", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959765400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d281c1-6325-49f4-915c-40dffa9657bc", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959778897800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76feba28-2380-441f-a86c-530b2e0df668", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959701165400, "endTime": 26959779401900}, "additional": {"logType": "info", "children": [], "durationId": "f425e847-579c-4cb0-82e1-f25bb098f379", "parent": "754bc153-16f5-498e-b179-114de257e1a4"}}, {"head": {"id": "754bc153-16f5-498e-b179-114de257e1a4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959693721000, "endTime": 26959779430100}, "additional": {"logType": "info", "children": ["78aa898f-6e23-4094-94c9-9b1acb50f221", "76feba28-2380-441f-a86c-530b2e0df668"], "durationId": "c6d19555-363a-489b-b549-b2ef80c97723", "parent": "58207eb1-54b6-4993-b901-78991ea3f184"}}, {"head": {"id": "42eb840e-6084-4180-9c54-5d961cd6b415", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959783597900, "endTime": 26959783621200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13fba7d3-599b-46e6-8318-c64a4193db74", "logId": "89678554-1fb2-4da6-88c3-f92a4a79b6de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89678554-1fb2-4da6-88c3-f92a4a79b6de", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959783597900, "endTime": 26959783621200}, "additional": {"logType": "info", "children": [], "durationId": "42eb840e-6084-4180-9c54-5d961cd6b415", "parent": "58207eb1-54b6-4993-b901-78991ea3f184"}}, {"head": {"id": "58207eb1-54b6-4993-b901-78991ea3f184", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959682964100, "endTime": 26959783643800}, "additional": {"logType": "info", "children": ["bba9512d-5347-4396-b823-17fdc0199598", "754bc153-16f5-498e-b179-114de257e1a4", "89678554-1fb2-4da6-88c3-f92a4a79b6de"], "durationId": "13fba7d3-599b-46e6-8318-c64a4193db74", "parent": "caeebbf4-f4a4-4566-994a-b5cd494e4dc2"}}, {"head": {"id": "caeebbf4-f4a4-4566-994a-b5cd494e4dc2", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959681232700, "endTime": 26959783732600}, "additional": {"logType": "info", "children": ["58207eb1-54b6-4993-b901-78991ea3f184"], "durationId": "09441e1a-fee3-497c-a713-f075e93c4e95", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "f45332bf-fa18-43ac-98c4-af1b3be05324", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959843824500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0acbd3d-4146-41c0-89f8-bb368dfb931d", "name": "hvigorfile, resolve hvigorfile dependencies in 125 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959908078600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e8ec90-d7cc-4919-a897-79fa510d13c2", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959783912400, "endTime": 26959908302800}, "additional": {"logType": "info", "children": [], "durationId": "8c767da0-aa54-4c67-a042-151a4a24d7f3", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "d9e66986-e6f9-4264-bc2e-196b4a5ffc59", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959909175500, "endTime": 26959909424300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1da6dabe-e56d-4825-9b50-fcc106883c53", "logId": "d585fbda-5613-4904-80e7-23ba2e58dd08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61c17e0e-eccb-4aae-bf65-a44cc8162d50", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959909214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d585fbda-5613-4904-80e7-23ba2e58dd08", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959909175500, "endTime": 26959909424300}, "additional": {"logType": "info", "children": [], "durationId": "d9e66986-e6f9-4264-bc2e-196b4a5ffc59", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "f7d4d52e-cece-4bb7-9a67-b31f26772a98", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959910624300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f054c8d-03c7-4c78-a5d0-62a83a669b7d", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959933966000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e94776c4-869b-4950-ad34-19b01424c9a1", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959909440000, "endTime": 26959935119400}, "additional": {"logType": "info", "children": [], "durationId": "9ea2e26b-ea12-4531-9928-3e73aa128f38", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "53a86f12-9bc9-49e4-82a4-de6eb5fcd7e8", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959943157200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d16aeff5-a00b-40ea-b7b3-9a93159038aa", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959943384500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eea3a768-7ad1-4aa1-9f07-9c343225545b", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959945767500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0737c9b6-4fe4-4a30-aef8-c20deece866f", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959945956300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efeaa945-291f-40bd-b31e-5e48fd636a14", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959935149100, "endTime": 26959949874400}, "additional": {"logType": "info", "children": [], "durationId": "2542527f-1a1c-4547-bd77-d68d0e8bc764", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "29e962cb-9b91-46dc-ab1a-57e8cd49bba4", "name": "Configuration phase cost:480 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959949923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09c16a6f-348a-4109-b6c8-7ec1e44d579d", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959949896100, "endTime": 26959950073500}, "additional": {"logType": "info", "children": [], "durationId": "ce060ede-f0dd-4d63-a600-857170c46f5c", "parent": "a443b2c1-8031-4771-9dc4-47bff850bc5c"}}, {"head": {"id": "a443b2c1-8031-4771-9dc4-47bff850bc5c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959463891300, "endTime": 26959950088700}, "additional": {"logType": "info", "children": ["95f9e5b5-9768-4a15-84a1-6c3d0c84346b", "57d05caa-57de-4ef0-8e28-8de3a0f74e3e", "913f5d72-33f2-4dd0-87d5-34e6d9ce54fc", "caeebbf4-f4a4-4566-994a-b5cd494e4dc2", "47e8ec90-d7cc-4919-a897-79fa510d13c2", "e94776c4-869b-4950-ad34-19b01424c9a1", "efeaa945-291f-40bd-b31e-5e48fd636a14", "09c16a6f-348a-4109-b6c8-7ec1e44d579d", "d585fbda-5613-4904-80e7-23ba2e58dd08"], "durationId": "1da6dabe-e56d-4825-9b50-fcc106883c53", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "8a912f4c-685d-48f0-8369-78bd856bc14a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959951916800, "endTime": 26959951940100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3c578593-c38f-41ca-9e27-9563dd5b81c8", "logId": "105a6c19-86db-43cc-b8e8-942276bba07a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "105a6c19-86db-43cc-b8e8-942276bba07a", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959951916800, "endTime": 26959951940100}, "additional": {"logType": "info", "children": [], "durationId": "8a912f4c-685d-48f0-8369-78bd856bc14a", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "5ab4cb79-d833-401d-82ed-a3ead87876c5", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959950114300, "endTime": 26959951953600}, "additional": {"logType": "info", "children": [], "durationId": "bc0c4529-d3a1-45ac-9a01-018b821df2f6", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "17a2439b-55a1-408e-ad88-47d3aa0334ca", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959951958000, "endTime": 26959951992800}, "additional": {"logType": "info", "children": [], "durationId": "3f70abaa-1f16-47b6-a3dc-43cc9140b48f", "parent": "1a6706aa-026f-4aad-9237-a1d659a7272c"}}, {"head": {"id": "1a6706aa-026f-4aad-9237-a1d659a7272c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959447687700, "endTime": 26959951998900}, "additional": {"logType": "info", "children": ["958a1812-f8ce-4bfc-b53a-216154fef520", "a443b2c1-8031-4771-9dc4-47bff850bc5c", "5ab4cb79-d833-401d-82ed-a3ead87876c5", "17a2439b-55a1-408e-ad88-47d3aa0334ca", "7c5878d0-5399-4a30-be6c-7856b07bd8c7", "cfcb6382-808e-4c54-8706-5b76666c5e9f", "105a6c19-86db-43cc-b8e8-942276bba07a"], "durationId": "3c578593-c38f-41ca-9e27-9563dd5b81c8"}}, {"head": {"id": "3877ef04-ff19-4e5e-b1c9-db3d67d78a46", "name": "Configuration task cost before running: 509 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959952429000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5107d148-0202-4590-97e0-dd0479c306f9", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959963190400, "endTime": 26959983846300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "01b8f14f-22c4-44c0-83f7-85e460bb04a3", "logId": "0c460efd-3518-427a-9d74-7389c94104a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01b8f14f-22c4-44c0-83f7-85e460bb04a3", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959955260000}, "additional": {"logType": "detail", "children": [], "durationId": "5107d148-0202-4590-97e0-dd0479c306f9"}}, {"head": {"id": "c3edcc17-b86f-4778-9f41-8ffdbb1dc8c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959957017600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abc95ef1-99c2-4651-8dc8-b7e06248575c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959957394300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232472e5-367b-4fcf-9601-caefbc15c5ef", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959963214200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "683dfdfd-6552-4ac2-80ef-ded261ebd7e5", "name": "Incremental task entry:default@PreBuild pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959983311000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d8beef-1764-4856-9f2d-9c558a0a7762", "name": "entry : default@PreBuild cost memory 0.31655120849609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959983576000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c460efd-3518-427a-9d74-7389c94104a4", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959963190400, "endTime": 26959983846300}, "additional": {"logType": "info", "children": [], "durationId": "5107d148-0202-4590-97e0-dd0479c306f9"}}, {"head": {"id": "aa2ec323-2492-4da0-b52c-9c0e2874366f", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959993933100, "endTime": 26959998632500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c034162d-c2a3-4749-a47f-02e6995d3c47", "logId": "602c0a16-319b-463e-a0e9-0142c9e26e38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c034162d-c2a3-4749-a47f-02e6995d3c47", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959990463900}, "additional": {"logType": "detail", "children": [], "durationId": "aa2ec323-2492-4da0-b52c-9c0e2874366f"}}, {"head": {"id": "08c06c93-5f5a-4595-8b06-3add722c2cce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959991669800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0405136c-0769-498a-a7fb-df4dc5f2b660", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959992310600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6d26610-d7d7-460e-837e-8dc3a80125e9", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959993955300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9003eaf-1e1f-4893-8ddd-7653d184053d", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959996001400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177e4db4-449f-47bd-a221-90893814ab6c", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959998390000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "795dfb8a-0554-4cd0-9652-98175266d027", "name": "entry : default@GenerateMetadata cost memory 0.09722900390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959998547300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602c0a16-319b-463e-a0e9-0142c9e26e38", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959993933100, "endTime": 26959998632500}, "additional": {"logType": "info", "children": [], "durationId": "aa2ec323-2492-4da0-b52c-9c0e2874366f"}}, {"head": {"id": "581c1a9c-b682-45bf-ada4-6c176ab3cbc1", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960004683900, "endTime": 26960015130400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d3e5b59d-5aaf-4dab-a781-475ab26618d1", "logId": "c1499a96-c913-4008-8208-5b39e629720f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3e5b59d-5aaf-4dab-a781-475ab26618d1", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960002157800}, "additional": {"logType": "detail", "children": [], "durationId": "581c1a9c-b682-45bf-ada4-6c176ab3cbc1"}}, {"head": {"id": "42ec1dbe-94a8-486e-8046-96652c2fc740", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960003552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d93e0663-f848-4f86-bd29-71ae181497a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960003738700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f73cc91-011d-4e46-8131-c545f5fb9c25", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960004699900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94937032-9c56-4d9a-a33f-fbd74814b7c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960008996500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b2bef27-c703-4c96-9d50-a7ca419f9ca5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960014679300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7ffadd-0391-4baa-85ba-9134f19c3329", "name": "entry : default@ConfigureCmake cost memory 0.03692626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960014917400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec35754e-1c25-48f3-b17d-db96b4101604", "name": "runTaskFromQueue task cost before running: 571 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960015045900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1499a96-c913-4008-8208-5b39e629720f", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960004683900, "endTime": 26960015130400, "totalTime": 10331600}, "additional": {"logType": "info", "children": [], "durationId": "581c1a9c-b682-45bf-ada4-6c176ab3cbc1"}}, {"head": {"id": "dfed72b7-f249-4d08-9ee7-a2fce384fcd1", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960027359000, "endTime": 26960032443000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0bf6614d-9cb5-4647-ba86-79a6b8f33ea9", "logId": "9530692c-6bf5-4349-8c2b-f17738a1c3c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0bf6614d-9cb5-4647-ba86-79a6b8f33ea9", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960023234400}, "additional": {"logType": "detail", "children": [], "durationId": "dfed72b7-f249-4d08-9ee7-a2fce384fcd1"}}, {"head": {"id": "e152452a-7bdf-4462-b403-3e64fd1530dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960025247900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d40eb5e-649d-491f-9fdf-bbe50dab87ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960025481700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb193c1-f383-4e89-9ead-4b477cd3725a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960027386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8045f2d9-461c-4f38-9c37-6ec4ef14203a", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960030774000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af6a796-9b7d-4e5f-984f-c60035ff65b8", "name": "entry : default@MergeProfile cost memory 0.107208251953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960031957200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9530692c-6bf5-4349-8c2b-f17738a1c3c8", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960027359000, "endTime": 26960032443000}, "additional": {"logType": "info", "children": [], "durationId": "dfed72b7-f249-4d08-9ee7-a2fce384fcd1"}}, {"head": {"id": "92fc6c89-e0fa-4196-bc08-3d940a305a18", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960041066700, "endTime": 26960044614100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "84d33c6e-451f-43e7-8d9e-b4cbb503e1ed", "logId": "d78d7527-1a8a-49ff-8c38-78e9993ee3e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84d33c6e-451f-43e7-8d9e-b4cbb503e1ed", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960036098900}, "additional": {"logType": "detail", "children": [], "durationId": "92fc6c89-e0fa-4196-bc08-3d940a305a18"}}, {"head": {"id": "a229f888-0460-4fff-a005-ff4c85257558", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960036774500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace01ff2-b11d-40a1-bb0f-5f04cdfb1e14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960037024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f378f617-b434-4e5c-8146-aad25b3964b3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960041089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8887b950-ea9b-4bc4-9057-ab87debf9000", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960043021600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a905889-aaea-4877-b207-3b122eb5bdb7", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960044226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205694e3-ff89-4902-9a69-68649ca24c6a", "name": "entry : default@CreateBuildProfile cost memory 0.10570526123046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960044441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d78d7527-1a8a-49ff-8c38-78e9993ee3e3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960041066700, "endTime": 26960044614100}, "additional": {"logType": "info", "children": [], "durationId": "92fc6c89-e0fa-4196-bc08-3d940a305a18"}}, {"head": {"id": "07e73d69-c9a7-4584-bc0b-302ed4824d4c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048389200, "endTime": 26960048878200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "66e37d74-9629-418c-a125-236d1dd16ea3", "logId": "224e475c-ca6a-43f1-9523-72abf0a916b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66e37d74-9629-418c-a125-236d1dd16ea3", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960046996500}, "additional": {"logType": "detail", "children": [], "durationId": "07e73d69-c9a7-4584-bc0b-302ed4824d4c"}}, {"head": {"id": "1d1b1b23-1358-4c5e-ab76-9d3856c411ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960047450500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b73d6a8e-d743-484b-8a30-718da8460d49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960047573900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293c1a43-9daa-4fde-a0d9-4e3530d3ff89", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048404300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6267f2a1-70ae-433d-9474-edd94d397f05", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048542100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e1c3665-237c-4faf-a675-9e4f477b4378", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048607500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a7373fe-574a-4dab-8f40-665d3f1bfef3", "name": "entry : default@PreCheckSyscap cost memory 0.03714752197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048703800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1347602-07d4-4279-b8f9-41c2e813a1ad", "name": "runTaskFromQueue task cost before running: 605 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "224e475c-ca6a-43f1-9523-72abf0a916b4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960048389200, "endTime": 26960048878200, "totalTime": 382800}, "additional": {"logType": "info", "children": [], "durationId": "07e73d69-c9a7-4584-bc0b-302ed4824d4c"}}, {"head": {"id": "d14f7d8a-d139-449e-bb2e-5ff1f17e16b6", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960063154300, "endTime": 26960063886200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e1baec83-4a75-4f60-bcb0-b1958b93a5aa", "logId": "bc91b09f-b0e2-4f7c-ab01-2e9d331f13de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1baec83-4a75-4f60-bcb0-b1958b93a5aa", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960053464400}, "additional": {"logType": "detail", "children": [], "durationId": "d14f7d8a-d139-449e-bb2e-5ff1f17e16b6"}}, {"head": {"id": "fe5fc1af-fde8-496b-9e83-d4f57d3a1e68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960058380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5c6312e-a665-4890-ba73-20c6b550af05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960058546600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cbf4b9f-25d1-4f90-89cf-6df776a3f93f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960063170300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1cdc4f-5a92-48a1-9b2b-98e879cd2895", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960063392800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bf03aac-66ed-4dad-b19d-611bcf8b3594", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03936767578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960063598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af7a569e-7915-404a-b568-f36cc9ad6557", "name": "runTaskFromQueue task cost before running: 620 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960063727500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc91b09f-b0e2-4f7c-ab01-2e9d331f13de", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960063154300, "endTime": 26960063886200, "totalTime": 553100}, "additional": {"logType": "info", "children": [], "durationId": "d14f7d8a-d139-449e-bb2e-5ff1f17e16b6"}}, {"head": {"id": "c770b1f0-0557-40b5-89d5-9b8ef124a384", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960074087300, "endTime": 26960079607900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "0ebd9e94-0af8-464e-9c17-e207ba0e8bc8", "logId": "8a4280b8-bc27-4fbc-a051-d96aa5b3d94d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ebd9e94-0af8-464e-9c17-e207ba0e8bc8", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960066015200}, "additional": {"logType": "detail", "children": [], "durationId": "c770b1f0-0557-40b5-89d5-9b8ef124a384"}}, {"head": {"id": "49f8cb46-42f3-4881-a41d-f387a2b9fab5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960066517500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9eea0ad-5089-4ee4-aaae-c50059a895df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960066646200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "388e54c0-f3bc-4332-bc1e-4c7020ce00cb", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960074210100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "855ab9bb-5f5d-4659-b6f3-8c3f8c4b4b75", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960077883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff6e3144-7eaf-4da0-bfe3-b83647140b2a", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960078122200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "482b735a-162c-4f3b-9ed4-8d43a2d1745f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960078315500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b346fe3-7dde-4da6-8241-4921c286f58b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960078731000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f4497d6-160d-4cdc-9e30-ca486ad85498", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11989593505859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960079360300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35287554-7568-48a6-83d1-aab5bb56b52e", "name": "runTaskFromQueue task cost before running: 636 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960079531600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a4280b8-bc27-4fbc-a051-d96aa5b3d94d", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960074087300, "endTime": 26960079607900, "totalTime": 5421000}, "additional": {"logType": "info", "children": [], "durationId": "c770b1f0-0557-40b5-89d5-9b8ef124a384"}}, {"head": {"id": "9b5059e0-1c91-45fc-9511-b3dd91e4ae54", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084322900, "endTime": 26960084755000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6f800f5e-379a-476d-be10-8c96b3ba3139", "logId": "71b7b00e-ee28-4cdd-9856-6abc706791e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f800f5e-379a-476d-be10-8c96b3ba3139", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960082995100}, "additional": {"logType": "detail", "children": [], "durationId": "9b5059e0-1c91-45fc-9511-b3dd91e4ae54"}}, {"head": {"id": "f03ecba1-fd5a-4188-9678-07a2f300040c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960083385300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a8063f6-cc90-4511-9829-cc02699b620a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960083493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78666b49-9f0c-42b7-b9fa-cca5846d7020", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084340600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1335461-2323-4257-9bfb-8b325a3e40e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084478100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b51503-0ff0-455c-9415-3554f3c45579", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084540100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6a766e-4b0a-4950-a992-9ab8f4a4c2bf", "name": "entry : default@BuildNativeWithCmake cost memory 0.0379791259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084618700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d6081bd-85d0-4e28-9b7b-0c9517aa3e3b", "name": "runTaskFromQueue task cost before running: 641 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084700200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71b7b00e-ee28-4cdd-9856-6abc706791e1", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960084322900, "endTime": 26960084755000, "totalTime": 360000}, "additional": {"logType": "info", "children": [], "durationId": "9b5059e0-1c91-45fc-9511-b3dd91e4ae54"}}, {"head": {"id": "e029ae2b-a7fd-4d4a-9c81-70c8bef1aa55", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960088966100, "endTime": 26960092645300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "13114fd9-56a2-4319-b124-f85640536167", "logId": "041a99b5-7104-4a21-9628-a5b38b400b6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13114fd9-56a2-4319-b124-f85640536167", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960086710500}, "additional": {"logType": "detail", "children": [], "durationId": "e029ae2b-a7fd-4d4a-9c81-70c8bef1aa55"}}, {"head": {"id": "347c8dee-f83e-4230-a0d5-32e361fe4a97", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960087615300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c1dca0-8d23-4ed3-9fec-a732d7c4b407", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960087925000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a40737d-ec1e-4569-917d-dcc3326f2e83", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960088985500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c9d697-65ae-44be-b9f9-8a47a30e4660", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960092421500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00fe9395-5216-48e2-8384-45b0d5f0c308", "name": "entry : default@MakePackInfo cost memory 0.140228271484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960092566600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "041a99b5-7104-4a21-9628-a5b38b400b6d", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960088966100, "endTime": 26960092645300}, "additional": {"logType": "info", "children": [], "durationId": "e029ae2b-a7fd-4d4a-9c81-70c8bef1aa55"}}, {"head": {"id": "06aad042-7681-4880-a600-1ea5fabdbd26", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960096539100, "endTime": 26960099687300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "c2b87404-2474-4b1a-9f47-f01ed6188209", "logId": "c6f6aa74-83c9-4d6e-9175-277a965696aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2b87404-2474-4b1a-9f47-f01ed6188209", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960094987500}, "additional": {"logType": "detail", "children": [], "durationId": "06aad042-7681-4880-a600-1ea5fabdbd26"}}, {"head": {"id": "74c2bd6c-1a4e-497a-ab6a-2a3e542a4ab2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960095326400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "027f16ad-b460-4f4c-9fdd-cb226b8c23e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960095426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a944bf06-b5f6-40cc-9598-3fa2f6e485ea", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960096552000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea7c414c-632d-488f-aa48-9cb5bf9c2318", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960096698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16817a51-4055-4738-8f43-32896f0a6c65", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960097475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb206705-328b-4399-abaa-a09cec5fc447", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960098835500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462d7baa-49c1-49dc-93cd-262971d71969", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960098954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75afb3ea-c596-477a-bb23-ef3f54b11c3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960099041700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5da7fe50-849e-4b38-9b00-d991581a134a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960099097900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d1419fa-a240-427d-95e8-8981d8d134e2", "name": "entry : default@SyscapTransform cost memory 0.1552886962890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960099225600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f640af8f-88d6-4864-aba5-c2d1ab408fd9", "name": "runTaskFromQueue task cost before running: 656 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960099576200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f6aa74-83c9-4d6e-9175-277a965696aa", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960096539100, "endTime": 26960099687300, "totalTime": 3002100}, "additional": {"logType": "info", "children": [], "durationId": "06aad042-7681-4880-a600-1ea5fabdbd26"}}, {"head": {"id": "3264a4c7-e326-442a-bc9a-9698e164590f", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960105672500, "endTime": 26960111947900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "68c55695-aac4-458f-bb0c-3bda9cad44c9", "logId": "1c2f41b5-07ac-42cf-b1da-af1f0c508078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68c55695-aac4-458f-bb0c-3bda9cad44c9", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960103144300}, "additional": {"logType": "detail", "children": [], "durationId": "3264a4c7-e326-442a-bc9a-9698e164590f"}}, {"head": {"id": "b05adfb6-d45d-4e55-b7e8-31a62a4a6f8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960103620300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "021b0171-5c1f-4255-a7a3-c63104a72868", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960103738000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4286fe30-5805-41f6-9538-22810b6a9b9e", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960105693300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd40f189-1987-415e-9e5e-5fe89a90a4c8", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960110381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c559f0-13f5-4f86-b623-00be92051e9e", "name": "entry : default@ProcessProfile cost memory 0.0622406005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960111190700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c2f41b5-07ac-42cf-b1da-af1f0c508078", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960105672500, "endTime": 26960111947900}, "additional": {"logType": "info", "children": [], "durationId": "3264a4c7-e326-442a-bc9a-9698e164590f"}}, {"head": {"id": "c514a8a5-bc0b-4a9a-9298-d2f6a93b8316", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960122849300, "endTime": 26960133395300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ceef6174-eb2a-4d50-83fc-af60a32dafdd", "logId": "e9119fe7-a0cb-4da6-ad25-8f729de3edae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ceef6174-eb2a-4d50-83fc-af60a32dafdd", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960115126500}, "additional": {"logType": "detail", "children": [], "durationId": "c514a8a5-bc0b-4a9a-9298-d2f6a93b8316"}}, {"head": {"id": "b076d153-d6cc-4ab6-87c6-9949118a0225", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960117104300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539518a9-3cd6-42fe-bccc-602925806d03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960117319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad2a600-d197-447b-875e-b7df2c58de52", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960122905100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd549b6b-ac10-4e45-9016-44905910ced1", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960133120600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a83fc0-a8a7-44a5-b3ac-4162b7ccdc1e", "name": "entry : default@ProcessRouterMap cost memory 0.20389556884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960133261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9119fe7-a0cb-4da6-ad25-8f729de3edae", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960122849300, "endTime": 26960133395300}, "additional": {"logType": "info", "children": [], "durationId": "c514a8a5-bc0b-4a9a-9298-d2f6a93b8316"}}, {"head": {"id": "18f1be4c-6e8c-40d8-8575-85d4bfd094fa", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960137093600, "endTime": 26960138534700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "34295db4-22ee-480d-81ab-39dbe6faf22b", "logId": "c42a4185-94da-47e7-bb50-2569678b7bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34295db4-22ee-480d-81ab-39dbe6faf22b", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960135915200}, "additional": {"logType": "detail", "children": [], "durationId": "18f1be4c-6e8c-40d8-8575-85d4bfd094fa"}}, {"head": {"id": "d1ac3e4f-eef6-4444-812e-d15710a0f97b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960136301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a55a802-b664-4cd4-8ba4-34021e73d960", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960136412500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e376f1de-9e04-449d-a423-13a30ef83210", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960137106100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3da61bdb-a32f-4f5c-a260-46d08c781f91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960137239700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae9ee173-5ac2-4b35-9c17-2795a8be67b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960137326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae376953-648d-4986-ba5b-bf6b00c8c4a3", "name": "entry : default@BuildNativeWithNinja cost memory 0.057647705078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960138153200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9abbe77d-5065-4d38-845a-4d867d4cea5e", "name": "runTaskFromQueue task cost before running: 695 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960138434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c42a4185-94da-47e7-bb50-2569678b7bef", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960137093600, "endTime": 26960138534700, "totalTime": 1298200}, "additional": {"logType": "info", "children": [], "durationId": "18f1be4c-6e8c-40d8-8575-85d4bfd094fa"}}, {"head": {"id": "660fa49c-1155-43ac-ad9e-3a3345128ad4", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960144492200, "endTime": 26960152488500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "99ac2669-4337-4d43-bfc4-fa150a035a1a", "logId": "13fe5045-1598-4d34-820a-830d15dec1a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99ac2669-4337-4d43-bfc4-fa150a035a1a", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960142214400}, "additional": {"logType": "detail", "children": [], "durationId": "660fa49c-1155-43ac-ad9e-3a3345128ad4"}}, {"head": {"id": "37f97b48-a987-4809-83d2-27688ddf1082", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960142605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec341809-da62-46d2-9c57-1ae7b5fa31e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960142703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a903d7d9-9937-411b-819e-a89caf9a3bd6", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960143464900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64cee5b9-8d7e-4784-9681-b76d19aae891", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960145888100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04df1558-3ba5-4b12-a833-076fd5437474", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960150070300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0042068d-5d4a-4c36-b8dc-09540c7eae14", "name": "entry : default@ProcessResource cost memory 0.1709136962890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960150224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13fe5045-1598-4d34-820a-830d15dec1a1", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960144492200, "endTime": 26960152488500}, "additional": {"logType": "info", "children": [], "durationId": "660fa49c-1155-43ac-ad9e-3a3345128ad4"}}, {"head": {"id": "e4e712b6-bc4f-4924-a240-17f46c6f59bf", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960161023900, "endTime": 26960173104500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5c98bff8-c1c7-4411-ba1e-ff17332915c7", "logId": "6648b73e-d1e9-4947-9030-95c5d0dc6a0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c98bff8-c1c7-4411-ba1e-ff17332915c7", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960157706300}, "additional": {"logType": "detail", "children": [], "durationId": "e4e712b6-bc4f-4924-a240-17f46c6f59bf"}}, {"head": {"id": "67cd3301-b98b-42b9-9ab4-a0af57265fca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960158098100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f0b288-3b0b-4911-886b-25cdd2d37880", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960158197300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "210f14e4-4551-448a-bfa5-8ca6d24ccf25", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960161037200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c0522a-8e66-4b7c-92fe-931309b80a22", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960172843300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2b17bd2-b6d0-41ef-8bcd-02e4dcc4d8b7", "name": "entry : default@GenerateLoaderJson cost memory 0.7690811157226562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960173004800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6648b73e-d1e9-4947-9030-95c5d0dc6a0c", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960161023900, "endTime": 26960173104500}, "additional": {"logType": "info", "children": [], "durationId": "e4e712b6-bc4f-4924-a240-17f46c6f59bf"}}, {"head": {"id": "0cc5e2bc-d489-4828-9769-730219476bec", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960180297600, "endTime": 26960183460800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "63da9c6d-b33b-4e8f-897c-31798a661a98", "logId": "90fb0144-e15f-4885-8f75-f12d362d8b64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63da9c6d-b33b-4e8f-897c-31798a661a98", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960179069800}, "additional": {"logType": "detail", "children": [], "durationId": "0cc5e2bc-d489-4828-9769-730219476bec"}}, {"head": {"id": "799c66fc-163a-4105-8622-60eb1a1b4ff4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960179487400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cca1ff5-2ee9-4b55-abd6-c99b4c644944", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960179610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ec73f9-28f6-4e1f-a253-fa5f17075b09", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960180314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55c2cab-3d2f-4a29-b9f0-7d93f210382d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960182415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb9dce6e-ece0-4f67-99ee-609681b1477f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960182535500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "309c0948-f7dc-4b5d-b244-a63b3d1526e1", "name": "entry : default@ProcessLibs cost memory 0.1269989013671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960183217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa009c6-85f4-4ba2-8af0-f4499a3d222f", "name": "runTaskFromQueue task cost before running: 740 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960183382300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90fb0144-e15f-4885-8f75-f12d362d8b64", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960180297600, "endTime": 26960183460800, "totalTime": 3055700}, "additional": {"logType": "info", "children": [], "durationId": "0cc5e2bc-d489-4828-9769-730219476bec"}}, {"head": {"id": "9dfb4685-a006-4357-99ae-30b237b69f39", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960192531100, "endTime": 26960246731500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ea65aee2-305e-47a1-950b-ec5d43d469a6", "logId": "e914d8d4-e487-41b3-b123-9b5f30734176"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea65aee2-305e-47a1-950b-ec5d43d469a6", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960186199900}, "additional": {"logType": "detail", "children": [], "durationId": "9dfb4685-a006-4357-99ae-30b237b69f39"}}, {"head": {"id": "7def6d30-7d01-4a24-8a87-8dabce050881", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960186563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d92238-4f54-4654-af05-36523a00f8a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960186672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fdbb839-7449-4673-8fe9-49a903af1425", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960188021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4baeca48-b597-40a3-9352-20fae6ceae87", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960192561100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f566bfe-af37-425e-9f47-c8a55ee100cf", "name": "Incremental task entry:default@CompileResource pre-execution cost: 53 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960246491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcabd8e5-1ba1-4b40-a45c-fb8d9899350d", "name": "entry : default@CompileResource cost memory 1.41046142578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960246638900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e914d8d4-e487-41b3-b123-9b5f30734176", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960192531100, "endTime": 26960246731500}, "additional": {"logType": "info", "children": [], "durationId": "9dfb4685-a006-4357-99ae-30b237b69f39"}}, {"head": {"id": "48d0f022-5968-418b-89a5-dfa282f01434", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960253533800, "endTime": 26960257535400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1a8f6f40-cefc-4f59-8aac-4d7540573565", "logId": "5c4c7840-06f0-4ab0-a03f-96c64b73ccb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a8f6f40-cefc-4f59-8aac-4d7540573565", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960250221100}, "additional": {"logType": "detail", "children": [], "durationId": "48d0f022-5968-418b-89a5-dfa282f01434"}}, {"head": {"id": "93ea262f-c9ef-45fb-a81d-b15d35da4d91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960250621200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af79f68-b2b9-463d-aec9-9ee8eae48c54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960250732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3df39c1-28ac-4cf8-aaec-476993fe565c", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960253547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60277999-1084-4a4c-aff8-5703c9dafa36", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960254127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0cc3a4-443b-4741-8e4f-1584f41ccad2", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960257190600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b782c2-29b6-438b-9d02-9c9a698e6ce2", "name": "entry : default@DoNativeStrip cost memory 0.078094482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960257393200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c4c7840-06f0-4ab0-a03f-96c64b73ccb8", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960253533800, "endTime": 26960257535400}, "additional": {"logType": "info", "children": [], "durationId": "48d0f022-5968-418b-89a5-dfa282f01434"}}, {"head": {"id": "079a97e9-c20f-48b4-a697-c28e96c3296e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960263589500, "endTime": 26961931599600}, "additional": {"children": ["a0ade34a-1333-4958-8e51-e22c4eaa24b0", "7f2c32ae-ab90-4569-9420-cac2b793aa3f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "b72ae06e-22ce-40b3-9853-81659b117593", "logId": "fe72f210-a7f7-47a8-ae64-5abd7b4d33d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b72ae06e-22ce-40b3-9853-81659b117593", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960259482500}, "additional": {"logType": "detail", "children": [], "durationId": "079a97e9-c20f-48b4-a697-c28e96c3296e"}}, {"head": {"id": "b440713e-a4eb-43f7-886d-0b2dd1e2ceb1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960259858600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba7d823-9ff8-4505-b939-37db7db90ac0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960259971500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df4f46ca-0de9-42c4-8400-d61bd685494f", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960263602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c117bf85-74c2-4d3e-af0e-71c76116373a", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960274888200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73af7366-7c07-4ede-8de3-30efffc0e67a", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960275038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33e86e61-0557-40cf-b21a-055fb4ea068d", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960286093000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56201cfb-f248-4c68-887b-88e959ebbae5", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960288997700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1266f99a-4725-4800-a1e1-9a02d18ef86f", "name": "default@CompileArkTS work[27] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960291071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26960395434000, "endTime": 26961834429500}, "additional": {"children": ["ae16ab94-e0c4-4bc9-a621-339179cfeb29", "45f01d6e-5893-41f0-900a-86e7e7f45176", "ad7f5171-5da8-458c-ad09-2f101f82a42e", "a3f899a7-ce0f-4e16-9ddc-653b357063a6", "77b3ba89-4b7d-4ca4-a6f7-92c108f41435", "2680b87d-34df-44d8-989b-1f28df51c8e5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "079a97e9-c20f-48b4-a697-c28e96c3296e", "logId": "fd4ce170-9586-42d9-a84e-73fac88cd557"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e71a0f-550d-4ce4-8518-b1a7a64ecd73", "name": "default@CompileArkTS work[27] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960291956000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b24efcc-08a0-4f12-b591-fd9d44c88770", "name": "default@CompileArkTS work[27] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960292173700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2743eff0-813b-4e53-ac2a-3e267ee293fd", "name": "CopyResources startTime: 26960292244500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960292247500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b3680b-acb0-4200-9449-ca43a8007f01", "name": "default@CompileArkTS work[28] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960292379900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f2c32ae-ab90-4569-9420-cac2b793aa3f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26961917137000, "endTime": 26961930615400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "079a97e9-c20f-48b4-a697-c28e96c3296e", "logId": "69b75063-ddbc-44c4-a7bb-3e3ddbd969f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52f304d0-7226-4099-9489-abdeda9959d4", "name": "default@CompileArkTS work[28] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960293054500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "942df717-f74b-4844-8033-54f43554858f", "name": "default@CompileArkTS work[28] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960293129400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38eb21b-c160-46cf-814d-a06c9dd8d1db", "name": "entry : default@CompileArkTS cost memory -5.0823822021484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960293215100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdad87ae-8380-4fe9-a87e-2614d4fe54d0", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960298647200, "endTime": 26960301473700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "d191eed7-c465-4d96-a592-3c0391a357ae", "logId": "f53d66b0-0dd8-44f3-86bc-2203f0a14b42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d191eed7-c465-4d96-a592-3c0391a357ae", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960294539300}, "additional": {"logType": "detail", "children": [], "durationId": "bdad87ae-8380-4fe9-a87e-2614d4fe54d0"}}, {"head": {"id": "a054a0c3-3303-415e-a188-740eeb2f3687", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960294887300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e8ff68-5a4c-4714-8979-d6a9a66bbbe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960294984200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d01a9fe-483d-4c56-b1fb-ec87196f6d34", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960298660700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "913d4c53-f982-465a-9c2a-993695fee52f", "name": "entry : default@BuildJS cost memory 0.1283416748046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960301110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18db3682-843d-4e10-9612-7ae2c8a88a7d", "name": "runTaskFromQueue task cost before running: 858 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960301233500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f53d66b0-0dd8-44f3-86bc-2203f0a14b42", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960298647200, "endTime": 26960301473700, "totalTime": 2568600}, "additional": {"logType": "info", "children": [], "durationId": "bdad87ae-8380-4fe9-a87e-2614d4fe54d0"}}, {"head": {"id": "af4d4091-eb45-4b1e-b626-7002b77dce28", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960308927300, "endTime": 26960310744800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4ca7e0f4-3568-4fe9-aa2d-c540538767c2", "logId": "e0e4267b-ade5-4650-88f2-95c2ebc6db8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ca7e0f4-3568-4fe9-aa2d-c540538767c2", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960303639300}, "additional": {"logType": "detail", "children": [], "durationId": "af4d4091-eb45-4b1e-b626-7002b77dce28"}}, {"head": {"id": "042125f3-c165-4cb9-892e-95de6a5093fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960304434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf0db00-098c-4be7-bb3d-b91b1bba1cbd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960305151600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32086653-ce3c-4fb9-a55c-f02ec767d376", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960308940000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c88bce8-95e5-4b15-afa2-d9832a0938db", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960309266200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785913c6-3ce0-4a3f-a017-633b663d069c", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960310442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1085b42-579a-4f15-b7ce-b0578a69c0a3", "name": "entry : default@CacheNativeLibs cost memory 0.0936737060546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960310658400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e4267b-ade5-4650-88f2-95c2ebc6db8f", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960308927300, "endTime": 26960310744800}, "additional": {"logType": "info", "children": [], "durationId": "af4d4091-eb45-4b1e-b626-7002b77dce28"}}, {"head": {"id": "84530bad-9ba9-4b19-b495-32d8902ac124", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960394979200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3416298e-52f0-4060-9ec8-0c72e49c3ddc", "name": "default@CompileArkTS work[27] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960395317900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3e62304-1e42-40db-bbe3-2204004b3e79", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960395429600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "705a8e57-cb26-4462-9bdd-628d141aa0bd", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960395486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2036f4f1-e782-42da-872e-2ef72a27016f", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960395544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70a5bc7b-06b0-4a40-815e-c17e39ddca61", "name": "default@CompileArkTS work[28] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960396575600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a5187e-a23b-45fe-806e-4e52d313cfd1", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961835007900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae16ab94-e0c4-4bc9-a621-339179cfeb29", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26960395534400, "endTime": 26960398446500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "logId": "7584d945-a229-412b-8511-b4fdc4f3e285"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7584d945-a229-412b-8511-b4fdc4f3e285", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960395534400, "endTime": 26960398446500}, "additional": {"logType": "info", "children": [], "durationId": "ae16ab94-e0c4-4bc9-a621-339179cfeb29", "parent": "fd4ce170-9586-42d9-a84e-73fac88cd557"}}, {"head": {"id": "45f01d6e-5893-41f0-900a-86e7e7f45176", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26960398463300, "endTime": 26960398553400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "logId": "1ef81153-9c01-4b88-a4ac-93c6b89675a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ef81153-9c01-4b88-a4ac-93c6b89675a5", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960398463300, "endTime": 26960398553400}, "additional": {"logType": "info", "children": [], "durationId": "45f01d6e-5893-41f0-900a-86e7e7f45176", "parent": "fd4ce170-9586-42d9-a84e-73fac88cd557"}}, {"head": {"id": "ad7f5171-5da8-458c-ad09-2f101f82a42e", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26960398560800, "endTime": 26960398591500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "logId": "caea8d80-11cc-45e4-83d8-ff9fc5158598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caea8d80-11cc-45e4-83d8-ff9fc5158598", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960398560800, "endTime": 26960398591500}, "additional": {"logType": "info", "children": [], "durationId": "ad7f5171-5da8-458c-ad09-2f101f82a42e", "parent": "fd4ce170-9586-42d9-a84e-73fac88cd557"}}, {"head": {"id": "a3f899a7-ce0f-4e16-9ddc-653b357063a6", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26960398604100, "endTime": 26961750793700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "logId": "105cfb48-73c3-4009-abbc-b36e580b53c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "105cfb48-73c3-4009-abbc-b36e580b53c7", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960398604100, "endTime": 26961750793700}, "additional": {"logType": "info", "children": [], "durationId": "a3f899a7-ce0f-4e16-9ddc-653b357063a6", "parent": "fd4ce170-9586-42d9-a84e-73fac88cd557"}}, {"head": {"id": "77b3ba89-4b7d-4ca4-a6f7-92c108f41435", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26961750829100, "endTime": 26961764297600}, "additional": {"children": ["add04afd-0572-4ed2-ada5-8c0e1584b7ec", "cc8cc39a-02c9-4334-b06a-39877db7e6ab", "ece8539f-d2ab-4f5d-bc17-daadc3804357"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "logId": "9d681e90-21fd-43b3-872b-b9b81f75e1d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d681e90-21fd-43b3-872b-b9b81f75e1d8", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961750829100, "endTime": 26961764297600}, "additional": {"logType": "info", "children": ["cbca9be6-eb21-4158-892b-92ca8ea9cfa4", "016d7083-fb61-4a44-9723-eb400869d9cd", "9178cc0a-63f8-40dc-b892-1ba1f5cccebf"], "durationId": "77b3ba89-4b7d-4ca4-a6f7-92c108f41435", "parent": "fd4ce170-9586-42d9-a84e-73fac88cd557"}}, {"head": {"id": "add04afd-0572-4ed2-ada5-8c0e1584b7ec", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26961750865300, "endTime": 26961750872400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "77b3ba89-4b7d-4ca4-a6f7-92c108f41435", "logId": "cbca9be6-eb21-4158-892b-92ca8ea9cfa4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbca9be6-eb21-4158-892b-92ca8ea9cfa4", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961750865300, "endTime": 26961750872400}, "additional": {"logType": "info", "children": [], "durationId": "add04afd-0572-4ed2-ada5-8c0e1584b7ec", "parent": "9d681e90-21fd-43b3-872b-b9b81f75e1d8"}}, {"head": {"id": "cc8cc39a-02c9-4334-b06a-39877db7e6ab", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26961750876000, "endTime": 26961753730500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "77b3ba89-4b7d-4ca4-a6f7-92c108f41435", "logId": "016d7083-fb61-4a44-9723-eb400869d9cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "016d7083-fb61-4a44-9723-eb400869d9cd", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961750876000, "endTime": 26961753730500}, "additional": {"logType": "info", "children": [], "durationId": "cc8cc39a-02c9-4334-b06a-39877db7e6ab", "parent": "9d681e90-21fd-43b3-872b-b9b81f75e1d8"}}, {"head": {"id": "ece8539f-d2ab-4f5d-bc17-daadc3804357", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26961755412900, "endTime": 26961764282300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "77b3ba89-4b7d-4ca4-a6f7-92c108f41435", "logId": "9178cc0a-63f8-40dc-b892-1ba1f5cccebf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9178cc0a-63f8-40dc-b892-1ba1f5cccebf", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961755412900, "endTime": 26961764282300}, "additional": {"logType": "info", "children": [], "durationId": "ece8539f-d2ab-4f5d-bc17-daadc3804357", "parent": "9d681e90-21fd-43b3-872b-b9b81f75e1d8"}}, {"head": {"id": "2680b87d-34df-44d8-989b-1f28df51c8e5", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26961764311500, "endTime": 26961834257100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "logId": "854908f0-17a5-45f9-87ec-4600fa9868e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "854908f0-17a5-45f9-87ec-4600fa9868e0", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961764311500, "endTime": 26961834257100}, "additional": {"logType": "info", "children": [], "durationId": "2680b87d-34df-44d8-989b-1f28df51c8e5", "parent": "fd4ce170-9586-42d9-a84e-73fac88cd557"}}, {"head": {"id": "177646d3-5173-4caf-a6e0-70033ea44a0a", "name": "default@CompileArkTS work[27] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961848580900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd4ce170-9586-42d9-a84e-73fac88cd557", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26960395434000, "endTime": 26961834429500}, "additional": {"logType": "info", "children": ["7584d945-a229-412b-8511-b4fdc4f3e285", "1ef81153-9c01-4b88-a4ac-93c6b89675a5", "caea8d80-11cc-45e4-83d8-ff9fc5158598", "105cfb48-73c3-4009-abbc-b36e580b53c7", "9d681e90-21fd-43b3-872b-b9b81f75e1d8", "854908f0-17a5-45f9-87ec-4600fa9868e0"], "durationId": "a0ade34a-1333-4958-8e51-e22c4eaa24b0", "parent": "fe72f210-a7f7-47a8-ae64-5abd7b4d33d2"}}, {"head": {"id": "5096707c-3333-4cc0-915c-c49d0c626eb9", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961931148700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcceaa3e-e25c-4905-84f7-75e63f5635d5", "name": "CopyResources is end, endTime: 26961931340100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961931348400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee3f08cd-f387-447e-8b40-c89414f27fdb", "name": "default@CompileArkTS work[28] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961931455800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69b75063-ddbc-44c4-a7bb-3e3ddbd969f4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26961917137000, "endTime": 26961930615400}, "additional": {"logType": "info", "children": [], "durationId": "7f2c32ae-ab90-4569-9420-cac2b793aa3f", "parent": "fe72f210-a7f7-47a8-ae64-5abd7b4d33d2"}}, {"head": {"id": "fe72f210-a7f7-47a8-ae64-5abd7b4d33d2", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26960263589500, "endTime": 26961931599600, "totalTime": 1482167300}, "additional": {"logType": "info", "children": ["fd4ce170-9586-42d9-a84e-73fac88cd557", "69b75063-ddbc-44c4-a7bb-3e3ddbd969f4"], "durationId": "079a97e9-c20f-48b4-a697-c28e96c3296e"}}, {"head": {"id": "d4f07644-2254-47d8-a031-4a7b7ebc3779", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961941419200, "endTime": 26961942734000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "3ff62987-ec94-4c82-9774-861b03dd0b5b", "logId": "cd8221bc-d582-444a-8c5f-ea13ec44667a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ff62987-ec94-4c82-9774-861b03dd0b5b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961936815400}, "additional": {"logType": "detail", "children": [], "durationId": "d4f07644-2254-47d8-a031-4a7b7ebc3779"}}, {"head": {"id": "19faac45-042a-4927-8fea-bfeceeb26171", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961939699700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d680467-e106-4de0-89d4-94524f86dc0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961940228300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56dd7001-17ef-4278-872d-e9463fa9f094", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961941437200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82de07f-c1fe-442e-a06f-718fd90c3864", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961941771500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9849d24f-759a-4be6-81b7-f930281099bf", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961942568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa89f47-8fa9-49b6-9dc2-3a959258f650", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0797119140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961942665300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd8221bc-d582-444a-8c5f-ea13ec44667a", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961941419200, "endTime": 26961942734000}, "additional": {"logType": "info", "children": [], "durationId": "d4f07644-2254-47d8-a031-4a7b7ebc3779"}}, {"head": {"id": "19a98610-6c73-474e-8939-c81831e301c7", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961952181400, "endTime": 26962719221900}, "additional": {"children": ["cdbd9919-e7bb-46f3-a0d4-18732e29368e", "16cde1da-0fb1-447a-b2d3-8e3a17077f2a", "eb9b3e66-c588-4a35-b969-370924d3dde1"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "5781f069-9a90-46af-8636-915b0185a2c9", "logId": "634652b2-b384-4ea0-af44-9900fbbe6f3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5781f069-9a90-46af-8636-915b0185a2c9", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961945201900}, "additional": {"logType": "detail", "children": [], "durationId": "19a98610-6c73-474e-8939-c81831e301c7"}}, {"head": {"id": "5d6f69d8-cb4b-458c-a717-877ad2635ea5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961945680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4d01599-a39a-47c7-903a-4815070be906", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961945833000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "593ea300-62a8-4b64-8184-cee5dc05b6c0", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961952241600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "892cfeba-a9ac-463d-aee1-aaef2ce34421", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961965212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b6d985c-524f-4645-a56a-24b211d16bc5", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961965390300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee3079e-0a37-4300-9c0c-133fd736e328", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961965492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "844d9a8a-7057-4a65-b9f1-8130ea90599d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961965545200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdbd9919-e7bb-46f3-a0d4-18732e29368e", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961966355000, "endTime": 26961967582800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19a98610-6c73-474e-8939-c81831e301c7", "logId": "4c3c44e4-d1c7-4e11-a9ef-7e8f6c679840"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4f99c3f-7c55-4ab5-bcab-f3e3bdd3938a", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961967424900}, "additional": {"logType": "debug", "children": [], "durationId": "19a98610-6c73-474e-8939-c81831e301c7"}}, {"head": {"id": "4c3c44e4-d1c7-4e11-a9ef-7e8f6c679840", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961966355000, "endTime": 26961967582800}, "additional": {"logType": "info", "children": [], "durationId": "cdbd9919-e7bb-46f3-a0d4-18732e29368e", "parent": "634652b2-b384-4ea0-af44-9900fbbe6f3c"}}, {"head": {"id": "16cde1da-0fb1-447a-b2d3-8e3a17077f2a", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961968171900, "endTime": 26961969894300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19a98610-6c73-474e-8939-c81831e301c7", "logId": "e0ff854e-13f1-4c0c-8ca3-262992e21358"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f4abfb8-40f2-473c-a060-fd443c59b372", "name": "default@PackageHap work[29] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961968865200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb9b3e66-c588-4a35-b969-370924d3dde1", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26962185475700, "endTime": 26962718906800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "19a98610-6c73-474e-8939-c81831e301c7", "logId": "2c7fc4a5-a41e-4bdb-82b6-00fb306b6bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d89accf-a650-414c-93c2-55f8a1776f54", "name": "default@PackageHap work[29] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961969561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "998d9916-2c6b-4b79-a81e-f9add0378717", "name": "default@PackageHap work[29] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961969673200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0ff854e-13f1-4c0c-8ca3-262992e21358", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961968171900, "endTime": 26961969894300}, "additional": {"logType": "info", "children": [], "durationId": "16cde1da-0fb1-447a-b2d3-8e3a17077f2a", "parent": "634652b2-b384-4ea0-af44-9900fbbe6f3c"}}, {"head": {"id": "ed144d12-aa0b-4cb8-83d6-460331c63069", "name": "entry : default@PackageHap cost memory 1.2944259643554688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961976041400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c5e142-d14b-4982-9b6c-1ed0c6df4765", "name": "default@PackageHap work[29] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962039459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2861ec21-7065-4624-a382-07c1cd66bf62", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962084249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b8d98e-758b-46c0-81ac-f4d6c540fe25", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962084529100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4314108-715a-4c30-afd3-379e6b1b1f87", "name": "A work dispatched to worker[3] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962084616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf79908-b700-4d39-a29d-bb791ef07798", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962084674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b945c68-c00e-4de1-8104-97a678176bf7", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962084725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13595106-7d72-4a5b-a318-b2c1624bfda4", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962084774200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e923ede2-8498-4371-8c7d-9d61bc63d1cb", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962718989500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c05067b-ea9a-4501-99d7-47429ede82de", "name": "default@PackageHap work[29] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962719131200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7fc4a5-a41e-4bdb-82b6-00fb306b6bef", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26962185475700, "endTime": 26962718906800}, "additional": {"logType": "info", "children": [], "durationId": "eb9b3e66-c588-4a35-b969-370924d3dde1", "parent": "634652b2-b384-4ea0-af44-9900fbbe6f3c"}}, {"head": {"id": "634652b2-b384-4ea0-af44-9900fbbe6f3c", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26961952181400, "endTime": 26962719221900, "totalTime": 557441400}, "additional": {"logType": "info", "children": ["4c3c44e4-d1c7-4e11-a9ef-7e8f6c679840", "e0ff854e-13f1-4c0c-8ca3-262992e21358", "2c7fc4a5-a41e-4bdb-82b6-00fb306b6bef"], "durationId": "19a98610-6c73-474e-8939-c81831e301c7"}}, {"head": {"id": "2eb6f4ed-6d4f-4622-b5a7-bc069f2bf8e5", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962728036400, "endTime": 26962729637800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "73a342e7-7f5c-45d2-b4af-1f5bc3bd4190", "logId": "6740c724-2cb1-4ad7-888f-8dc5d992f959"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73a342e7-7f5c-45d2-b4af-1f5bc3bd4190", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962725214000}, "additional": {"logType": "detail", "children": [], "durationId": "2eb6f4ed-6d4f-4622-b5a7-bc069f2bf8e5"}}, {"head": {"id": "d573da83-7a96-4fbb-8a6e-d16bba516791", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962725641500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b85c847-0537-4301-9820-4dd0c1113549", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962725747300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77820ff6-0e22-4948-8aac-6c32ba229fe5", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962728049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fca616f-4c30-4a50-8be6-8dc1b4359598", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962728389100}, "additional": {"logType": "warn", "children": [], "durationId": "2eb6f4ed-6d4f-4622-b5a7-bc069f2bf8e5"}}, {"head": {"id": "47e77745-137f-42c2-94fc-a0aa5dd643aa", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962728982900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb11ae2f-69ca-46cb-af84-ce64ca0a45d5", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962729077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2b2be8-cde7-4456-84b3-2e9ee1331f6c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962729163100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da32cc37-7408-4ebc-87e3-74971ad4ea86", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962729219900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0846b80f-e852-472d-9e29-79371d49027a", "name": "entry : default@SignHap cost memory 0.118560791015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962729469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd6f62d-f2b7-463b-9b2e-bc061e49c50b", "name": "runTaskFromQueue task cost before running: 3 s 286 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962729575000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6740c724-2cb1-4ad7-888f-8dc5d992f959", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962728036400, "endTime": 26962729637800, "totalTime": 1521600}, "additional": {"logType": "info", "children": [], "durationId": "2eb6f4ed-6d4f-4622-b5a7-bc069f2bf8e5"}}, {"head": {"id": "de8d87a4-747c-4f24-9172-ae98e70f4245", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962732541400, "endTime": 26962741467700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8a38bf4f-0346-4b9f-97c5-fa8443f80848", "logId": "cc456952-d668-40e9-9436-ceb51e3158f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a38bf4f-0346-4b9f-97c5-fa8443f80848", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962731278000}, "additional": {"logType": "detail", "children": [], "durationId": "de8d87a4-747c-4f24-9172-ae98e70f4245"}}, {"head": {"id": "2ed355f2-c8f0-4145-8241-29f5c47135c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962731693900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f173786-640b-4e79-8810-30ce383d6bfd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962731790200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "504b34fa-9627-4a9e-bc97-1e1cfb1cd34b", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962732550500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c668f5d-9041-4985-94ae-d0035e2e4492", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962741137000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5752dd1-e407-48a5-a99e-6e9a5cb3d5b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962741240100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8203e9ea-ed0e-4e1f-93f0-e3d0c1ca4998", "name": "entry : default@CollectDebugSymbol cost memory 0.2440948486328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962741328300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03cedd7-0e32-4ab8-8df5-a076d9e62402", "name": "runTaskFromQueue task cost before running: 3 s 298 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962741411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc456952-d668-40e9-9436-ceb51e3158f3", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962732541400, "endTime": 26962741467700, "totalTime": 8849200}, "additional": {"logType": "info", "children": [], "durationId": "de8d87a4-747c-4f24-9172-ae98e70f4245"}}, {"head": {"id": "f8bacfa4-81c5-4c26-8799-ae3687e4abd4", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962743899600, "endTime": 26962744331200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "5a16644c-c2ea-4d41-ba0f-5224b912bab4", "logId": "5cf57bfc-7b33-44f1-84de-523c9642df7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a16644c-c2ea-4d41-ba0f-5224b912bab4", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962743842800}, "additional": {"logType": "detail", "children": [], "durationId": "f8bacfa4-81c5-4c26-8799-ae3687e4abd4"}}, {"head": {"id": "afdf7c0e-8526-4a92-8929-b2f43d2d30e6", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962743907900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27484719-cb92-4a51-b7ac-b2821ae7d374", "name": "entry : assembleHap cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962744139700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0caa58c8-ca52-4b2e-8c98-461192829d0f", "name": "runTaskFromQueue task cost before running: 3 s 301 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962744260200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf57bfc-7b33-44f1-84de-523c9642df7a", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962743899600, "endTime": 26962744331200, "totalTime": 335600}, "additional": {"logType": "info", "children": [], "durationId": "f8bacfa4-81c5-4c26-8799-ae3687e4abd4"}}, {"head": {"id": "5b2bb359-c47b-4ddb-85b9-4f7968f7ed18", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751165900, "endTime": 26962751189700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d51746e4-639b-489d-8f00-d08d163064f9", "logId": "44f41b3f-efa3-4d4c-8522-4b4715e43013"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44f41b3f-efa3-4d4c-8522-4b4715e43013", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751165900, "endTime": 26962751189700}, "additional": {"logType": "info", "children": [], "durationId": "5b2bb359-c47b-4ddb-85b9-4f7968f7ed18"}}, {"head": {"id": "cc8c4ce8-4492-41eb-a5a6-4a8487090910", "name": "BUILD SUCCESSFUL in 3 s 308 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751233300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8bfdd36f-40c7-413b-8c21-c2c4115d1425", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26959444070800, "endTime": 26962751489200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 58}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "4962e507-7000-45e5-a0ec-006d28c312d0", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb2ee65-8523-4e7d-a049-aac736f1d17d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751586400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "373276ef-550e-4358-9c34-b0d9a0d802ba", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751643000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c679780-7770-4052-8111-e37b282bb832", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751694100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69858561-52e2-45e6-9c5e-2ecd71369c50", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962751756200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b6a81d-5962-48b9-b757-793865a000cc", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962752043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebb9357-6f4e-471b-8487-8f47ff6e3d34", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962752590100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277f0545-7947-4564-b75b-e675b6db950b", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962752821700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad2fca32-4fa0-417f-8100-fa2e1d7ea8a6", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962752888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb62d2e-4b21-4c9a-a9ba-7e035384fd2d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962752948900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e03f2bc-11a6-49a7-a451-e2b2feae04ce", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962753172800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12bb8a0-fbdb-4e9c-944c-0ec327ca8386", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754027800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a031b55b-010a-4db8-ab5b-585321b90b35", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d54c0a20-31f3-45d9-9d4e-2113236978be", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754387100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "772d79db-a066-4411-9ebd-5d90bd0f8ee3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754476600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b42ee547-43a4-4288-8985-9036310cb199", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754532300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ee1f9d2-17ac-4cd0-a211-f167356f3fc0", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba7c788-85d7-4ec4-b2b2-0d3481d7d7a9", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962754945700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843d1eca-3cdf-4f07-93fe-58638c0a9858", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962755176600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6784375-160f-4807-abbb-baf50aa0e4eb", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962755422400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc60d97c-7357-4538-b059-331db3ffc21f", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962755721400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6450ea7b-dcf4-4627-8fcb-d93270ed6254", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962755806000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a81dc6f-a0c5-4e02-8a3f-42d256de8519", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962755906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db7c523-4f15-4a9b-bafa-9414743288ce", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962758085700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "560b2e55-e5a4-4475-95b3-f4891825a950", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962758508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa048ae8-6a8a-4e8e-bf48-f2075f0c74de", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962759121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17c4a970-b89a-4a99-b17f-0039eda4eb68", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962759318800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835a37ff-60ab-4af5-8a9b-e72aa8c41e07", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962759486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70dc0a8-8863-485d-bbbf-9e71f9b8b8b4", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962759903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b4d2c6-1346-4b3d-bec5-c60942133583", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962759969800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f453ad0-79d5-4af8-a71a-b3ea5ac6fde6", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962760140600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "674c8552-be24-4e70-83de-e203bb651609", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962760345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc0c483e-57f3-4c95-961c-4d6bd45d633c", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962760787900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "166a963b-60c6-4b8e-89ec-3389f8793c15", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962761762700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8339f751-1c8b-48fc-b226-a72d570b0460", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962762157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506e751b-44ad-497e-a7b1-cc9272f5802b", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962762857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfea78cb-d72e-4be6-ad4d-e2bfe9de5127", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962763065400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6872203-05c7-4ada-a4ad-a733dd66e6a5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962763242400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d2fa5c-5fb3-438d-829d-4ee32772694b", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962763677700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af072c60-5edb-48c6-9d1e-a3541390fec3", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962763911000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ee667a-ee45-4194-a2b1-9d661789ac70", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962763991000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b6ca2a7-01a1-464c-9d79-e5e5fb33fc86", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962764041700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41479fe-e093-42a3-bad9-672b7389fec2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962764847700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763ed076-2c10-4c08-aa32-45675b327366", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962765078000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e6992a2-c652-4ed7-8f68-b86d5c5cbd85", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962765259200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7586c73c-48fd-44b9-9e3c-c84495e70703", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962771373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "501cf904-1f04-4e41-830f-f7e0469db367", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962771643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a13f062-ec77-4b6c-aa57-d2a88b4a7a1a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962771842600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e722d3-ef6b-433f-8956-f50c7810a3b7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962771910500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de28089-bf94-4843-8d6f-fc57b2647507", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962772085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7d0143c-da41-4baf-9fd8-ffae6739ddbf", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962772799600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789138c8-03b4-4ed4-ba21-ef77bf142196", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962773265300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c613b885-ff86-423e-a07a-84259a434da0", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962773574800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfc1d7a1-74ac-4c3d-9082-161d7ea6e9b9", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962773820500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e03c53-382f-417c-9bb6-90b41530f80d", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962774034800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf16d948-702b-4024-9f7e-33792abbe925", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962774115700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01682ebd-77fb-4f69-9549-7c5098f18587", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962774329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dac4e9ef-df2b-4ce1-b2ba-7bbbcbe9d75d", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962776495200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9adebd2-579c-48f9-aaea-5704ac3dc6cd", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962776736400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a101c6b-212e-4f24-bc6b-163bf56908ea", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962777010400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fead3679-ce1c-4768-8270-8ec881dea0b8", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962777233000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}