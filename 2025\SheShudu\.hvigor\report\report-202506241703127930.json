{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "24175258-79b5-4018-b55b-b9bf1083ee42", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30492910968300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a785e3a-2734-4606-8601-ab338f313f76", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30493002509600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15aa214e-23a7-4b82-b5a0-e6aade35549d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30493002957500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b943d8cd-b087-4f77-a13e-ea52f79faa92", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824686812000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9efa21a1-aaba-46a2-b105-d811f3226176", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824693123500, "endTime": 30825335662200}, "additional": {"children": ["68ed9661-37cf-48c3-a918-c2158c818de1", "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "c36bdc70-8611-4e70-bb6c-128a966030f1", "1a0e4479-2d40-4139-ab0c-61c9684903ca", "c3880133-e7b6-49a4-aa4b-3a1c06de969c", "cc32bb87-49e1-49ee-98a9-e79022daab64", "4ed1e17a-1c45-4401-8bc3-966b45715065"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68ed9661-37cf-48c3-a918-c2158c818de1", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824693126700, "endTime": 30824707456200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "c08582f2-2388-43d4-b17c-3649df15acf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824707481300, "endTime": 30825333596900}, "additional": {"children": ["8c473218-ab3b-4ab0-a5d8-c906996c479e", "1800242b-9ff2-41b2-91bc-4722d3e1cb79", "f8102912-7784-429c-8e5e-06c6a029d42d", "da011937-64bc-4dc2-ab16-479b12d7babc", "081d3c37-991e-4f04-821d-4e89fc687616", "df6afe14-2846-4d26-9d87-2aebc8ef1c38", "957010d9-b2ac-4c72-bd1b-d9bc06d82add", "cbf0e0c9-765b-40be-acbf-f4f97499349e", "bae941ef-0348-422e-8d55-e1950d843840"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c36bdc70-8611-4e70-bb6c-128a966030f1", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825333836400, "endTime": 30825335649400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "30ec7a89-350f-4dd4-9219-77f33132c58e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a0e4479-2d40-4139-ab0c-61c9684903ca", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825335655300, "endTime": 30825335656700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "7d819244-f9a1-47c5-a2ec-7c00435f8159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3880133-e7b6-49a4-aa4b-3a1c06de969c", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824697422700, "endTime": 30824697471500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "1604f9c4-b124-4b74-a1f9-9e897a42e60d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1604f9c4-b124-4b74-a1f9-9e897a42e60d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824697422700, "endTime": 30824697471500}, "additional": {"logType": "info", "children": [], "durationId": "c3880133-e7b6-49a4-aa4b-3a1c06de969c", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "cc32bb87-49e1-49ee-98a9-e79022daab64", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824701837500, "endTime": 30824701855400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "922da515-8dae-42c6-91f8-e8fe80c487b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "922da515-8dae-42c6-91f8-e8fe80c487b4", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824701837500, "endTime": 30824701855400}, "additional": {"logType": "info", "children": [], "durationId": "cc32bb87-49e1-49ee-98a9-e79022daab64", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "a651d1e9-ad6d-4f66-bb7e-1a01ae8f042e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824701900700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63dd298e-ffd2-497b-b292-d5a8aa9eb6d3", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824707291000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c08582f2-2388-43d4-b17c-3649df15acf0", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824693126700, "endTime": 30824707456200}, "additional": {"logType": "info", "children": [], "durationId": "68ed9661-37cf-48c3-a918-c2158c818de1", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "8c473218-ab3b-4ab0-a5d8-c906996c479e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824715892600, "endTime": 30824715907100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "cff07528-1ccc-4037-bc41-6038ab89ee38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1800242b-9ff2-41b2-91bc-4722d3e1cb79", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824715939700, "endTime": 30824722679400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "0f1eb84f-696f-4062-af64-d86fe77c41a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8102912-7784-429c-8e5e-06c6a029d42d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824722694100, "endTime": 30824898676100}, "additional": {"children": ["cdaa2b64-0515-4ee6-b026-e6dd061f86c5", "b9de7af2-138b-4e68-98d1-690a457a243d", "55f3af92-4443-4064-874c-9a45b9fcf424"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "af2bced8-8d20-466b-9a0f-be45a8f436f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da011937-64bc-4dc2-ab16-479b12d7babc", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824898694800, "endTime": 30824993385400}, "additional": {"children": ["5340e490-77ad-44d9-8dd0-09cc31a444a9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "9efc8113-a738-4b5c-ac6d-0de99584642c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "081d3c37-991e-4f04-821d-4e89fc687616", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824993395600, "endTime": 30825218571200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "3291039d-c7b8-4695-9a99-aaa1473b59fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df6afe14-2846-4d26-9d87-2aebc8ef1c38", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825225124100, "endTime": 30825299514900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "62f9e4b9-03ad-46a3-b8fb-9051904ad8ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "957010d9-b2ac-4c72-bd1b-d9bc06d82add", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825299544900, "endTime": 30825333397200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "f4b68416-3339-4b14-af64-40085653f485"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbf0e0c9-765b-40be-acbf-f4f97499349e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825333419300, "endTime": 30825333562400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "83406311-3137-4e2b-8085-7be4664a4d1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cff07528-1ccc-4037-bc41-6038ab89ee38", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824715892600, "endTime": 30824715907100}, "additional": {"logType": "info", "children": [], "durationId": "8c473218-ab3b-4ab0-a5d8-c906996c479e", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "0f1eb84f-696f-4062-af64-d86fe77c41a0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824715939700, "endTime": 30824722679400}, "additional": {"logType": "info", "children": [], "durationId": "1800242b-9ff2-41b2-91bc-4722d3e1cb79", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "cdaa2b64-0515-4ee6-b026-e6dd061f86c5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824723785100, "endTime": 30824723807700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8102912-7784-429c-8e5e-06c6a029d42d", "logId": "c4d032ce-1d1c-4ac8-b6cd-aa88c452c8de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4d032ce-1d1c-4ac8-b6cd-aa88c452c8de", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824723785100, "endTime": 30824723807700}, "additional": {"logType": "info", "children": [], "durationId": "cdaa2b64-0515-4ee6-b026-e6dd061f86c5", "parent": "af2bced8-8d20-466b-9a0f-be45a8f436f4"}}, {"head": {"id": "b9de7af2-138b-4e68-98d1-690a457a243d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824726287400, "endTime": 30824896023400}, "additional": {"children": ["36343247-6902-4237-9ca1-6f019af4b1bf", "2c41053a-e3a2-406e-a5a9-1b3ccc68bca9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8102912-7784-429c-8e5e-06c6a029d42d", "logId": "47a7a77c-f384-4ba6-ae03-bbbb6fc12147"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36343247-6902-4237-9ca1-6f019af4b1bf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824726289600, "endTime": 30824733509300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9de7af2-138b-4e68-98d1-690a457a243d", "logId": "76d84433-33e3-4f89-af30-0d67876cca73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c41053a-e3a2-406e-a5a9-1b3ccc68bca9", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824733526500, "endTime": 30824896001300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b9de7af2-138b-4e68-98d1-690a457a243d", "logId": "c06bb8b4-38a4-46d2-b597-a20c5979c87f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cda12311-029a-457e-acf3-68786057ffb5", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824726296000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bdd2c28-a6f9-48f2-ba1f-fd358fdaa7e2", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824733367100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76d84433-33e3-4f89-af30-0d67876cca73", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824726289600, "endTime": 30824733509300}, "additional": {"logType": "info", "children": [], "durationId": "36343247-6902-4237-9ca1-6f019af4b1bf", "parent": "47a7a77c-f384-4ba6-ae03-bbbb6fc12147"}}, {"head": {"id": "aee99093-51f0-4ec3-bab0-368defe1f708", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824733538500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f3f335-902d-4026-abfd-20e71ee567dd", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824740828600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b89fa93-ae77-48cc-a638-c418ed6afda5", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824740948600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79597605-403f-4265-b5a7-12d158609748", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824741083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2837181-fa64-4b5b-99d5-e33366f6873b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824741182900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66a2c52-59a3-4ae9-b298-b45afc8601ab", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824743810900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8773c79-ece7-4296-9b98-42bb1a4724e3", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824748877600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17a85834-8545-4dab-baa2-8728fd912e25", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824783697300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf0d9ec-a7c5-45a6-93a0-53c589f8dae8", "name": "Sdk init in 97 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824846312300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13111680-09ea-4ace-91c5-4cfcd2862902", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824846757200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 3}, "markType": "other"}}, {"head": {"id": "bf4e6158-7a01-4fb5-bd52-22b4600554ee", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824846828300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 3}, "markType": "other"}}, {"head": {"id": "2ff06dfb-0ae6-448b-b1f8-e50a13515f40", "name": "Project task initialization takes 47 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824894952600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9be8d45-630a-4cce-b273-9b60f0b34677", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824895649700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c53aaa5-5bff-46d1-b83f-a041db645456", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824895763400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e86da6-f3d0-4ac2-9682-7e3ff64cefb4", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824895873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c06bb8b4-38a4-46d2-b597-a20c5979c87f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824733526500, "endTime": 30824896001300}, "additional": {"logType": "info", "children": [], "durationId": "2c41053a-e3a2-406e-a5a9-1b3ccc68bca9", "parent": "47a7a77c-f384-4ba6-ae03-bbbb6fc12147"}}, {"head": {"id": "47a7a77c-f384-4ba6-ae03-bbbb6fc12147", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824726287400, "endTime": 30824896023400}, "additional": {"logType": "info", "children": ["76d84433-33e3-4f89-af30-0d67876cca73", "c06bb8b4-38a4-46d2-b597-a20c5979c87f"], "durationId": "b9de7af2-138b-4e68-98d1-690a457a243d", "parent": "af2bced8-8d20-466b-9a0f-be45a8f436f4"}}, {"head": {"id": "55f3af92-4443-4064-874c-9a45b9fcf424", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824898594800, "endTime": 30824898649300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8102912-7784-429c-8e5e-06c6a029d42d", "logId": "50c4d4b6-fd21-4d5d-8108-b3821b235460"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50c4d4b6-fd21-4d5d-8108-b3821b235460", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824898594800, "endTime": 30824898649300}, "additional": {"logType": "info", "children": [], "durationId": "55f3af92-4443-4064-874c-9a45b9fcf424", "parent": "af2bced8-8d20-466b-9a0f-be45a8f436f4"}}, {"head": {"id": "af2bced8-8d20-466b-9a0f-be45a8f436f4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824722694100, "endTime": 30824898676100}, "additional": {"logType": "info", "children": ["c4d032ce-1d1c-4ac8-b6cd-aa88c452c8de", "47a7a77c-f384-4ba6-ae03-bbbb6fc12147", "50c4d4b6-fd21-4d5d-8108-b3821b235460"], "durationId": "f8102912-7784-429c-8e5e-06c6a029d42d", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "5340e490-77ad-44d9-8dd0-09cc31a444a9", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824901514800, "endTime": 30824993368600}, "additional": {"children": ["b7ad6534-486b-4e95-98c5-c7648e093d11", "beadc567-95c6-4957-8543-db0b9e9590ff", "b8bfeefc-b887-4d7e-8ba9-561b8d368004"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "da011937-64bc-4dc2-ab16-479b12d7babc", "logId": "9c9752ec-8747-443c-978b-a2e362ac43e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7ad6534-486b-4e95-98c5-c7648e093d11", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824907892000, "endTime": 30824907926400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5340e490-77ad-44d9-8dd0-09cc31a444a9", "logId": "31de64df-c37f-4990-8a97-1d7ec946cfe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31de64df-c37f-4990-8a97-1d7ec946cfe8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824907892000, "endTime": 30824907926400}, "additional": {"logType": "info", "children": [], "durationId": "b7ad6534-486b-4e95-98c5-c7648e093d11", "parent": "9c9752ec-8747-443c-978b-a2e362ac43e2"}}, {"head": {"id": "beadc567-95c6-4957-8543-db0b9e9590ff", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824910231200, "endTime": 30824988924300}, "additional": {"children": ["a0c8035d-9510-498c-a12c-afebc08e2faa", "5966a9d0-a61a-4709-ac19-dcf86182770d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5340e490-77ad-44d9-8dd0-09cc31a444a9", "logId": "6b192cf9-2df8-4647-a7e0-87f1a5f13176"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0c8035d-9510-498c-a12c-afebc08e2faa", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824910232900, "endTime": 30824925744300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "beadc567-95c6-4957-8543-db0b9e9590ff", "logId": "f01493f3-cee6-4beb-88c3-3c7b7d986d8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5966a9d0-a61a-4709-ac19-dcf86182770d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824925774900, "endTime": 30824988900700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "beadc567-95c6-4957-8543-db0b9e9590ff", "logId": "ad3b2f92-e491-4899-8762-e069701b48d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af394089-0daf-45c4-9258-d54a57aabbe2", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824910237500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "874f6516-4ecf-47cf-94f8-6c1a97220296", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824925498900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f01493f3-cee6-4beb-88c3-3c7b7d986d8e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824910232900, "endTime": 30824925744300}, "additional": {"logType": "info", "children": [], "durationId": "a0c8035d-9510-498c-a12c-afebc08e2faa", "parent": "6b192cf9-2df8-4647-a7e0-87f1a5f13176"}}, {"head": {"id": "7a843741-9199-4fd5-bb0c-699f0493a310", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824925794200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea242d0e-de7f-455c-a4d8-bdbeb6a37aa7", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824934344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f13fee85-7b50-4bc2-9302-eb1852741646", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824935515000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4cdd48-112d-4a38-b77f-a49c7260f72f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824940675000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6338fc51-3a2e-43ad-87a2-0b0af83779cb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824942932200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e6840c-d44b-4975-9c1f-d968b3eb69da", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824943388000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba26d3c-b7ef-48c1-8a50-2f6d41d44f87", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824943484800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "723f8f0a-7a33-4302-ab21-bf3f5b468fc6", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824943651700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad310c8b-e759-43dc-b1e0-37d8a0c041f7", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824987467400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f28de7d4-1a15-4be0-a862-469b65c8969b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824988049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7453ca-fee4-4846-92b3-fa7aa20518ba", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824988537600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd564c7-c0db-4505-b365-3a14d73d87e7", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824988830300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad3b2f92-e491-4899-8762-e069701b48d0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824925774900, "endTime": 30824988900700}, "additional": {"logType": "info", "children": [], "durationId": "5966a9d0-a61a-4709-ac19-dcf86182770d", "parent": "6b192cf9-2df8-4647-a7e0-87f1a5f13176"}}, {"head": {"id": "6b192cf9-2df8-4647-a7e0-87f1a5f13176", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824910231200, "endTime": 30824988924300}, "additional": {"logType": "info", "children": ["f01493f3-cee6-4beb-88c3-3c7b7d986d8e", "ad3b2f92-e491-4899-8762-e069701b48d0"], "durationId": "beadc567-95c6-4957-8543-db0b9e9590ff", "parent": "9c9752ec-8747-443c-978b-a2e362ac43e2"}}, {"head": {"id": "b8bfeefc-b887-4d7e-8ba9-561b8d368004", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824993321200, "endTime": 30824993348400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5340e490-77ad-44d9-8dd0-09cc31a444a9", "logId": "c5a57005-971b-4306-876e-a4dcff53184b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5a57005-971b-4306-876e-a4dcff53184b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824993321200, "endTime": 30824993348400}, "additional": {"logType": "info", "children": [], "durationId": "b8bfeefc-b887-4d7e-8ba9-561b8d368004", "parent": "9c9752ec-8747-443c-978b-a2e362ac43e2"}}, {"head": {"id": "9c9752ec-8747-443c-978b-a2e362ac43e2", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824901514800, "endTime": 30824993368600}, "additional": {"logType": "info", "children": ["31de64df-c37f-4990-8a97-1d7ec946cfe8", "6b192cf9-2df8-4647-a7e0-87f1a5f13176", "c5a57005-971b-4306-876e-a4dcff53184b"], "durationId": "5340e490-77ad-44d9-8dd0-09cc31a444a9", "parent": "9efc8113-a738-4b5c-ac6d-0de99584642c"}}, {"head": {"id": "9efc8113-a738-4b5c-ac6d-0de99584642c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824898694800, "endTime": 30824993385400}, "additional": {"logType": "info", "children": ["9c9752ec-8747-443c-978b-a2e362ac43e2"], "durationId": "da011937-64bc-4dc2-ab16-479b12d7babc", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "5bb8ab28-467e-434b-8c4f-4b2e6bc52607", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825042644000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed10d0e-f89a-473b-94ea-abd1785720d9", "name": "hvigorfile, resolve hvigorfile dependencies in 225 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825218315500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3291039d-c7b8-4695-9a99-aaa1473b59fd", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824993395600, "endTime": 30825218571200}, "additional": {"logType": "info", "children": [], "durationId": "081d3c37-991e-4f04-821d-4e89fc687616", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "bae941ef-0348-422e-8d55-e1950d843840", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825220967200, "endTime": 30825225089800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "logId": "c1be3148-f24f-4a7b-a94a-bfa63e828e62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2980afc-2e65-4824-b964-7dc7c6f0e439", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825221031500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1be3148-f24f-4a7b-a94a-bfa63e828e62", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825220967200, "endTime": 30825225089800}, "additional": {"logType": "info", "children": [], "durationId": "bae941ef-0348-422e-8d55-e1950d843840", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "a4ea0a17-b36a-405c-84f8-4cd4f546b219", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825227548300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a130fd-b4f7-426d-9015-cc5b11f36470", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825298496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f9e4b9-03ad-46a3-b8fb-9051904ad8ae", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825225124100, "endTime": 30825299514900}, "additional": {"logType": "info", "children": [], "durationId": "df6afe14-2846-4d26-9d87-2aebc8ef1c38", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "be957770-db37-4752-a9cf-7e4fbebb2568", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825310366700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "990e1309-c78b-47f4-9440-1bc5a33c4502", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825310678300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed75141-5428-40c4-9844-770f6b75e6e0", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825328961500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecd3f21d-b4fc-4944-922e-1a88ec8fd566", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825329093400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b68416-3339-4b14-af64-40085653f485", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825299544900, "endTime": 30825333397200}, "additional": {"logType": "info", "children": [], "durationId": "957010d9-b2ac-4c72-bd1b-d9bc06d82add", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "70f5f387-f634-46ce-b163-47732dca8908", "name": "Configuration phase cost:618 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825333447000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83406311-3137-4e2b-8085-7be4664a4d1a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825333419300, "endTime": 30825333562400}, "additional": {"logType": "info", "children": [], "durationId": "cbf0e0c9-765b-40be-acbf-f4f97499349e", "parent": "3bdf5712-e0d0-4eb3-bf78-803888e74dce"}}, {"head": {"id": "3bdf5712-e0d0-4eb3-bf78-803888e74dce", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824707481300, "endTime": 30825333596900}, "additional": {"logType": "info", "children": ["cff07528-1ccc-4037-bc41-6038ab89ee38", "0f1eb84f-696f-4062-af64-d86fe77c41a0", "af2bced8-8d20-466b-9a0f-be45a8f436f4", "9efc8113-a738-4b5c-ac6d-0de99584642c", "3291039d-c7b8-4695-9a99-aaa1473b59fd", "62f9e4b9-03ad-46a3-b8fb-9051904ad8ae", "f4b68416-3339-4b14-af64-40085653f485", "83406311-3137-4e2b-8085-7be4664a4d1a", "c1be3148-f24f-4a7b-a94a-bfa63e828e62"], "durationId": "757ca4fc-ca8e-4c40-8dc7-40d1c03f2bfd", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "4ed1e17a-1c45-4401-8bc3-966b45715065", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825335578900, "endTime": 30825335599500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9efa21a1-aaba-46a2-b105-d811f3226176", "logId": "7f2d08f8-3c42-46b3-9b58-c944c868bf30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f2d08f8-3c42-46b3-9b58-c944c868bf30", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825335578900, "endTime": 30825335599500}, "additional": {"logType": "info", "children": [], "durationId": "4ed1e17a-1c45-4401-8bc3-966b45715065", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "30ec7a89-350f-4dd4-9219-77f33132c58e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825333836400, "endTime": 30825335649400}, "additional": {"logType": "info", "children": [], "durationId": "c36bdc70-8611-4e70-bb6c-128a966030f1", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "7d819244-f9a1-47c5-a2ec-7c00435f8159", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825335655300, "endTime": 30825335656700}, "additional": {"logType": "info", "children": [], "durationId": "1a0e4479-2d40-4139-ab0c-61c9684903ca", "parent": "168f8b19-9957-40f7-a544-6fe4947a4d8b"}}, {"head": {"id": "168f8b19-9957-40f7-a544-6fe4947a4d8b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824693123500, "endTime": 30825335662200}, "additional": {"logType": "info", "children": ["c08582f2-2388-43d4-b17c-3649df15acf0", "3bdf5712-e0d0-4eb3-bf78-803888e74dce", "30ec7a89-350f-4dd4-9219-77f33132c58e", "7d819244-f9a1-47c5-a2ec-7c00435f8159", "1604f9c4-b124-4b74-a1f9-9e897a42e60d", "922da515-8dae-42c6-91f8-e8fe80c487b4", "7f2d08f8-3c42-46b3-9b58-c944c868bf30"], "durationId": "9efa21a1-aaba-46a2-b105-d811f3226176"}}, {"head": {"id": "d78acbc0-9c90-4b37-81aa-72c4f63c2233", "name": "Configuration task cost before running: 647 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825335877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e2af3e-9d3e-4098-9fdd-c48db8c21c99", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825342453700, "endTime": 30825354327200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9792dbd0-3ed8-4a7c-875c-f420c581e116", "logId": "214964e6-de72-4692-bd4a-5fd162d79cb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9792dbd0-3ed8-4a7c-875c-f420c581e116", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825337654100}, "additional": {"logType": "detail", "children": [], "durationId": "63e2af3e-9d3e-4098-9fdd-c48db8c21c99"}}, {"head": {"id": "024a7a39-1229-479a-9c28-b3b10c3a3a90", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825337998600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13d0382-f9c4-42e5-b08d-e775f63692c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825338198000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152afbac-0a90-40b1-b90d-a91497526438", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825342471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc3fe480-2073-4ba1-943f-4060fcc97157", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825353520900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f760acb0-1cb2-4536-90c6-59f9d1ad1ee1", "name": "entry : default@PreBuild cost memory 0.34990692138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825354032700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "214964e6-de72-4692-bd4a-5fd162d79cb3", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825342453700, "endTime": 30825354327200}, "additional": {"logType": "info", "children": [], "durationId": "63e2af3e-9d3e-4098-9fdd-c48db8c21c99"}}, {"head": {"id": "f27ad8fe-284c-4e33-baf4-226cf6d5a4d1", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825362491800, "endTime": 30825366897100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b0c7a4d2-7335-4adb-9d67-a9ea53337cc3", "logId": "235da2b5-025f-4cc0-b991-f521c4e225b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0c7a4d2-7335-4adb-9d67-a9ea53337cc3", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825360135100}, "additional": {"logType": "detail", "children": [], "durationId": "f27ad8fe-284c-4e33-baf4-226cf6d5a4d1"}}, {"head": {"id": "03c5ab1c-8e69-43ed-9fb0-92724f3a8da0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825360505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c53b65d-7afb-46a3-804d-00f78a8dff00", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825360860400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee12d99c-da9b-48b4-9223-0ddd060ba3b4", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825362507700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad58c7f-9ef6-4ad4-b5e7-178a8b14f46c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825365396300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0d7944-bacc-4646-b886-1723630cd961", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825366452400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798e9644-b91d-40ae-8d99-e2b88669d573", "name": "entry : default@GenerateMetadata cost memory 0.09616851806640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825366581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235da2b5-025f-4cc0-b991-f521c4e225b6", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825362491800, "endTime": 30825366897100}, "additional": {"logType": "info", "children": [], "durationId": "f27ad8fe-284c-4e33-baf4-226cf6d5a4d1"}}, {"head": {"id": "89d67f6b-2aeb-4e89-bbc4-09b9f2de405a", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825369959600, "endTime": 30825370424900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4b28a043-b172-44a1-9c84-4a4c93c39963", "logId": "fe9acf37-4194-4790-a6c0-c15fff261f6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b28a043-b172-44a1-9c84-4a4c93c39963", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825369087800}, "additional": {"logType": "detail", "children": [], "durationId": "89d67f6b-2aeb-4e89-bbc4-09b9f2de405a"}}, {"head": {"id": "3574f252-57f9-4e0a-86e4-d604f7ea94df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825369693000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2562287f-029e-4d95-9dfe-f83aa4c56370", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825369810000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec15f2c-e9aa-40e9-8489-c229c507272d", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825369966400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fde8b049-5202-4696-91f9-309a9e3873bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825370060000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71ffaab-6917-48e6-a334-7e6e62f50193", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825370173500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af66ba2d-2f67-4afb-b773-154cc00fa956", "name": "entry : default@ConfigureCmake cost memory 0.03607177734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825370280500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7affa182-9fdf-4731-b314-da69d3fe51d7", "name": "runTaskFromQueue task cost before running: 681 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825370368100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe9acf37-4194-4790-a6c0-c15fff261f6e", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825369959600, "endTime": 30825370424900, "totalTime": 386300}, "additional": {"logType": "info", "children": [], "durationId": "89d67f6b-2aeb-4e89-bbc4-09b9f2de405a"}}, {"head": {"id": "e5d08e8f-6063-4319-a478-96e242879524", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825376445800, "endTime": 30825378579400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "9e849e9f-f466-48db-91df-476c86444100", "logId": "ac54a0d8-f02b-419d-b1af-e96f72d38458"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e849e9f-f466-48db-91df-476c86444100", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825374181500}, "additional": {"logType": "detail", "children": [], "durationId": "e5d08e8f-6063-4319-a478-96e242879524"}}, {"head": {"id": "4e1a004a-dbcf-443e-b7fb-b1c678e8e49c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825374783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539e88c8-3c42-444f-a062-23ec68dc6bb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825375077800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e56999-c950-45a3-9a26-649c34c997cd", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825376461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75d8b046-e0ea-4891-a0b7-02194bfa446b", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825378223400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c1a939-f4f9-4953-a03d-62e845dadb09", "name": "entry : default@MergeProfile cost memory 0.1065826416015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825378502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac54a0d8-f02b-419d-b1af-e96f72d38458", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825376445800, "endTime": 30825378579400}, "additional": {"logType": "info", "children": [], "durationId": "e5d08e8f-6063-4319-a478-96e242879524"}}, {"head": {"id": "4ad27ea8-2d78-4efd-99d5-e95ce8e00945", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825383066100, "endTime": 30825385299500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "44190f8e-c9d7-439e-8963-2cbabc095cff", "logId": "4ef4174f-cdd6-4506-b71e-06093481042f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44190f8e-c9d7-439e-8963-2cbabc095cff", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825381014100}, "additional": {"logType": "detail", "children": [], "durationId": "4ad27ea8-2d78-4efd-99d5-e95ce8e00945"}}, {"head": {"id": "3ad4ade2-efd8-4049-8c44-f6ecc83f2eee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825381525300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86787357-4549-4d84-92ec-fc8b88e150a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825381908800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7075821-74f5-4f6e-a753-963df08f7f81", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825383080500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c9b399-5561-4a67-bcb9-2bb29fd65ef9", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825383988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acccfdcc-87df-4bde-9139-c1e9b7eb3d70", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825385087700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1718b4-9f20-4748-ab36-ca5e247349c2", "name": "entry : default@CreateBuildProfile cost memory 0.1039581298828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825385220400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ef4174f-cdd6-4506-b71e-06093481042f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825383066100, "endTime": 30825385299500}, "additional": {"logType": "info", "children": [], "durationId": "4ad27ea8-2d78-4efd-99d5-e95ce8e00945"}}, {"head": {"id": "c742b850-ffe3-4a59-ba42-527b712a7edd", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825388589800, "endTime": 30825389456100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0ad4c3e8-b7de-46dc-9e83-71e84589113b", "logId": "be524280-d019-431f-9897-9686ca1ecf50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ad4c3e8-b7de-46dc-9e83-71e84589113b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825387441000}, "additional": {"logType": "detail", "children": [], "durationId": "c742b850-ffe3-4a59-ba42-527b712a7edd"}}, {"head": {"id": "41a40bb2-c0b6-41b0-b829-28ee005825ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825387827100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa11d9f8-9d2b-45d9-9612-d01f78c75e48", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825387936400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5625d966-0cf7-4980-be99-fdd2571fc2e4", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825388598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eff8597-b538-498f-8e3f-aad59c409f1d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825388923000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030e8a61-fb09-4691-9f51-5841297904f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825389029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427679b5-7857-4071-aade-c6aaefc868fd", "name": "entry : default@PreCheckSyscap cost memory 0.03629302978515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825389179300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4ed106c-ebeb-4fe5-a127-c804593edca5", "name": "runTaskFromQueue task cost before running: 700 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825389359100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be524280-d019-431f-9897-9686ca1ecf50", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825388589800, "endTime": 30825389456100, "totalTime": 737400}, "additional": {"logType": "info", "children": [], "durationId": "c742b850-ffe3-4a59-ba42-527b712a7edd"}}, {"head": {"id": "7d66964d-6237-49d5-b6a0-7b17bafdd52c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825398340700, "endTime": 30825398950200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "72f92cb7-d6ef-41ef-8817-4b0aee00c40d", "logId": "b95e7ed0-2374-4c6f-a0c8-8008a3dded5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72f92cb7-d6ef-41ef-8817-4b0aee00c40d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825391403400}, "additional": {"logType": "detail", "children": [], "durationId": "7d66964d-6237-49d5-b6a0-7b17bafdd52c"}}, {"head": {"id": "b02ce3ac-6479-4991-9642-4fe80c02470c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825391798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa8f5c34-94c8-4c65-b90e-368502afcba5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825391904700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042132ca-7466-4e56-9dc3-1fb9416c09d0", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825398351800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c939d1-400e-4d10-bfc3-72e1f9488ede", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825398583000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19311f0f-ead1-4bc8-85f7-f62806bdbab1", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03863525390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825398802200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d1be655-a30f-4391-9655-aa4359f3dd8e", "name": "runTaskFromQueue task cost before running: 710 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825398889400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95e7ed0-2374-4c6f-a0c8-8008a3dded5d", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825398340700, "endTime": 30825398950200, "totalTime": 531600}, "additional": {"logType": "info", "children": [], "durationId": "7d66964d-6237-49d5-b6a0-7b17bafdd52c"}}, {"head": {"id": "dd29253b-ac09-409c-91a2-eb2836288ccd", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825404090800, "endTime": 30825407069400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "58b970fa-feb3-4791-86f9-7014c6067922", "logId": "48dbcd72-3bda-4b23-8925-4ef181fe7db9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58b970fa-feb3-4791-86f9-7014c6067922", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825401280600}, "additional": {"logType": "detail", "children": [], "durationId": "dd29253b-ac09-409c-91a2-eb2836288ccd"}}, {"head": {"id": "1fc3fbb1-3cf4-4c51-81cb-3500485010e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825401937900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "838c989b-c786-4a2a-8e4d-f329294e23f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825402068100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e3169fa-d76b-4275-8b5c-5bbb9ac1ac9f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825404147700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41de7781-eb59-4032-985d-d1c8259cc876", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825406232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f77250-1745-4ae8-8562-965d385b784b", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825406411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ac6479-d8a4-4f02-b206-600997a19fbf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825406507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8880f18-0341-444f-89d0-c50af1ea4f14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825406564100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65dbeeae-8b27-4519-a492-397c394a7dbe", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11859130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825406841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf901f1-3512-497b-b56e-8300272a46ec", "name": "runTaskFromQueue task cost before running: 718 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825406946500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48dbcd72-3bda-4b23-8925-4ef181fe7db9", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825404090800, "endTime": 30825407069400, "totalTime": 2836200}, "additional": {"logType": "info", "children": [], "durationId": "dd29253b-ac09-409c-91a2-eb2836288ccd"}}, {"head": {"id": "e6778ba2-1c63-42cf-880f-5f3ec11a5591", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825413558900, "endTime": 30825414931700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0cdb7984-3864-4e68-b790-ebde99f54e41", "logId": "daa1c9d2-33a1-49c9-a6c8-fa4d13135055"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0cdb7984-3864-4e68-b790-ebde99f54e41", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825412099300}, "additional": {"logType": "detail", "children": [], "durationId": "e6778ba2-1c63-42cf-880f-5f3ec11a5591"}}, {"head": {"id": "11d214bc-2e03-4ba2-91bf-17f9bf3d3223", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825412738600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bd2c8e-95aa-4047-affd-a002a11312cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825412852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1c40cbc-155f-470b-be72-4bf67f4f7f07", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825413570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f127b15d-ce0f-4808-a0c9-75d68f29c2bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825413918200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a413eaa7-33da-471b-b49f-6e2d7e300f5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825414010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0302526-9792-4fef-b9c5-1e6e177f2d51", "name": "entry : default@BuildNativeWithCmake cost memory 0.0371246337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825414187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9380973-4e75-4b77-b877-6c6101aee772", "name": "runTaskFromQueue task cost before running: 725 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825414563000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa1c9d2-33a1-49c9-a6c8-fa4d13135055", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825413558900, "endTime": 30825414931700, "totalTime": 941700}, "additional": {"logType": "info", "children": [], "durationId": "e6778ba2-1c63-42cf-880f-5f3ec11a5591"}}, {"head": {"id": "5d356caa-d34e-4196-88b1-bf20c4f427a0", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825419298800, "endTime": 30825422476300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "263cd425-b212-4ce7-8507-d19971e05740", "logId": "a2db948c-3358-4ba9-ba94-8738002d256f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "263cd425-b212-4ce7-8507-d19971e05740", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825417987400}, "additional": {"logType": "detail", "children": [], "durationId": "5d356caa-d34e-4196-88b1-bf20c4f427a0"}}, {"head": {"id": "1b32401f-1961-4b5b-ac91-7fbdc3dc2bfd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825418430800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6bdd8b9-3aee-44c7-851c-965bdb4d9e6e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825418550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "363a5d4c-06b8-4066-9ed7-dae4735d63dc", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825419307200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f8b1e9-169a-412b-807e-336c70190590", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825422277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1953e0f-3f8d-4fb8-9d8c-a3d5411bcda6", "name": "entry : default@MakePackInfo cost memory 0.1396331787109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825422407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2db948c-3358-4ba9-ba94-8738002d256f", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825419298800, "endTime": 30825422476300}, "additional": {"logType": "info", "children": [], "durationId": "5d356caa-d34e-4196-88b1-bf20c4f427a0"}}, {"head": {"id": "3211a7c1-efab-43db-937f-7eb7300ae5c1", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825426278400, "endTime": 30825430347100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "1e2e1219-b795-4e4c-8ca2-e2d1af4a83f4", "logId": "d5a7b70b-aaad-4d2d-a1c7-9b4799a4dd66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e2e1219-b795-4e4c-8ca2-e2d1af4a83f4", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825424386500}, "additional": {"logType": "detail", "children": [], "durationId": "3211a7c1-efab-43db-937f-7eb7300ae5c1"}}, {"head": {"id": "2cde6583-cb41-4b7f-84ec-237b94c51059", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825424756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a37b8bcd-8671-4bac-975f-2604038ad9e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825424853100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "322f03cb-da40-41c2-8772-ee4d78cffde3", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825426289400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b8a0cd2-1b64-430d-b09f-4a5126a3cd60", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825426470200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bcf1c28-2feb-4eaa-8133-403ac414ad0c", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825427078600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a8212dd-5002-4ada-be4e-93abbfe6e9d7", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825429839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "567be2e7-050d-4420-8587-9558d70107a0", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825429980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27e37deb-c946-4f65-a3fc-376e71a4e9b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825430075600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd64883c-b9f9-4e79-b138-d6f2d8c6b30c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825430138100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f193dd5f-d59c-4063-9fd6-2303e77785c1", "name": "entry : default@SyscapTransform cost memory 0.15412139892578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825430217200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c0d2f68-db3e-4014-9fbf-173d073e54f4", "name": "runTaskFromQueue task cost before running: 741 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825430291200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a7b70b-aaad-4d2d-a1c7-9b4799a4dd66", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825426278400, "endTime": 30825430347100, "totalTime": 3998700}, "additional": {"logType": "info", "children": [], "durationId": "3211a7c1-efab-43db-937f-7eb7300ae5c1"}}, {"head": {"id": "cefc8b20-571b-4010-81e2-0691c18d1229", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825434200100, "endTime": 30825435829800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3386b283-a1ad-4f6f-99e2-4baa38868276", "logId": "4c0a4822-526b-40c4-af67-99be002b18d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3386b283-a1ad-4f6f-99e2-4baa38868276", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825432451000}, "additional": {"logType": "detail", "children": [], "durationId": "cefc8b20-571b-4010-81e2-0691c18d1229"}}, {"head": {"id": "4884430f-9b8c-46bd-992f-7d8f346554b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825432976800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17bbe20-2d7b-43c8-a94b-c587f564c3e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825433089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eb54338-3d6f-48c1-a1ff-ee1a9382d1e0", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825434211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d7089c6-068f-46ae-94bc-794f3a7ca544", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825435451400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41ba303-533a-4fad-8e0c-7740f1d396dd", "name": "entry : default@ProcessProfile cost memory 0.06090545654296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825435575700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0a4822-526b-40c4-af67-99be002b18d0", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825434200100, "endTime": 30825435829800}, "additional": {"logType": "info", "children": [], "durationId": "cefc8b20-571b-4010-81e2-0691c18d1229"}}, {"head": {"id": "d79fbc0a-f6af-4110-a41f-5ac21628ba8c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825441566100, "endTime": 30825447964400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a13d7504-abec-421b-8b7b-32a6be4ba575", "logId": "2ac17255-6fe9-4c83-8635-25902f79396e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a13d7504-abec-421b-8b7b-32a6be4ba575", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825437951500}, "additional": {"logType": "detail", "children": [], "durationId": "d79fbc0a-f6af-4110-a41f-5ac21628ba8c"}}, {"head": {"id": "d22f97ca-4566-4165-a963-cf68b7a3eb5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825438452200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd8735c-2586-44ea-b7c8-508d0e8ad553", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825438564500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d89b0fd-87e7-487a-b947-0864510eff88", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825441580700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47404e73-b077-491a-bc06-4275f4839638", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825447745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a649a453-2521-406b-a7ed-d10dc254215d", "name": "entry : default@ProcessRouterMap cost memory -5.3106842041015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825447895100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac17255-6fe9-4c83-8635-25902f79396e", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825441566100, "endTime": 30825447964400}, "additional": {"logType": "info", "children": [], "durationId": "d79fbc0a-f6af-4110-a41f-5ac21628ba8c"}}, {"head": {"id": "9a9dc71e-2f53-48df-b2ba-5f6b88c0fc92", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825450974800, "endTime": 30825451888000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d37a2800-15b8-4f08-bc54-6a9c0c7bbe5b", "logId": "b8648fa5-c14d-4630-9b51-f219cb4d6c20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d37a2800-15b8-4f08-bc54-6a9c0c7bbe5b", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825450009200}, "additional": {"logType": "detail", "children": [], "durationId": "9a9dc71e-2f53-48df-b2ba-5f6b88c0fc92"}}, {"head": {"id": "df4d7cd7-d93a-4502-9e51-020810a13414", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825450343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86de8f71-dcae-4c67-945e-77719f258ac7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825450428600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed340413-5b60-409e-8ab2-0ed33622511d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825450981800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7cc0a15-c036-4ff1-8db2-a035c70a258d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825451084000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a0488c0-658f-4266-8841-be41c2a7b7bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825451179100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42311a85-b837-4a6f-aac8-9f5da9afb031", "name": "entry : default@BuildNativeWithNinja cost memory 0.05672454833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825451735500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e0d613-94e6-4209-b1f7-a428fc241089", "name": "runTaskFromQueue task cost before running: 763 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825451830800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8648fa5-c14d-4630-9b51-f219cb4d6c20", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825450974800, "endTime": 30825451888000, "totalTime": 834700}, "additional": {"logType": "info", "children": [], "durationId": "9a9dc71e-2f53-48df-b2ba-5f6b88c0fc92"}}, {"head": {"id": "7078a3b3-0faf-41a8-b355-835ae2be39ae", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825455820100, "endTime": 30825461437800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f719aba6-87b9-42a2-a1a2-5b4228c9d14a", "logId": "659d0a76-1970-47e8-9ec8-4b20ac4502a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f719aba6-87b9-42a2-a1a2-5b4228c9d14a", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825453735200}, "additional": {"logType": "detail", "children": [], "durationId": "7078a3b3-0faf-41a8-b355-835ae2be39ae"}}, {"head": {"id": "b8560fcc-fbea-4ef7-bb9c-83896dac143c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825454053200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea956eb-d91c-4539-ba99-9d3c7a6af2e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825454147100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5a2af4-2129-4ea1-8a4d-c49a673b4565", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825454882400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff6dc13-14ab-4164-9586-32a830016fdc", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825457210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9894d8f-a9a4-4a35-831e-570578b4002d", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825459399800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69ee9da6-f20c-4bbd-a97d-84f18d8ecf3a", "name": "entry : default@ProcessResource cost memory 0.16977691650390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825459536200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659d0a76-1970-47e8-9ec8-4b20ac4502a7", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825455820100, "endTime": 30825461437800}, "additional": {"logType": "info", "children": [], "durationId": "7078a3b3-0faf-41a8-b355-835ae2be39ae"}}, {"head": {"id": "5e920749-7c8b-4237-8df0-f336c20c11ce", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825467874200, "endTime": 30825480995200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7873e2ad-f95e-4fee-9d6d-500e3f243011", "logId": "0050a171-633e-4bfe-968f-8532905ae8ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7873e2ad-f95e-4fee-9d6d-500e3f243011", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825464814500}, "additional": {"logType": "detail", "children": [], "durationId": "5e920749-7c8b-4237-8df0-f336c20c11ce"}}, {"head": {"id": "7772f935-f97d-44ee-b595-86f56240eed9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825465203200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf27a54b-f686-4677-ad13-8dc50c41169a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825465303600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "109c677d-782c-4f6c-a676-ab4709d840e0", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825467882300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea612a32-1a8e-4fe9-bf1f-abca7268cd97", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825480808900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29347c0-550f-47d5-9828-c07fb8bf523d", "name": "entry : default@GenerateLoaderJson cost memory 0.7648162841796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825480930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0050a171-633e-4bfe-968f-8532905ae8ab", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825467874200, "endTime": 30825480995200}, "additional": {"logType": "info", "children": [], "durationId": "5e920749-7c8b-4237-8df0-f336c20c11ce"}}, {"head": {"id": "744675ca-12c0-46ed-bfff-a1fda2d29cf3", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825488020700, "endTime": 30825490820600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c46983b9-fa8d-4252-95ef-cf646678a281", "logId": "e7977424-7271-486f-9bd3-19f2772686a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c46983b9-fa8d-4252-95ef-cf646678a281", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825486759400}, "additional": {"logType": "detail", "children": [], "durationId": "744675ca-12c0-46ed-bfff-a1fda2d29cf3"}}, {"head": {"id": "5e83d032-888e-4e2c-9543-7e25407c77e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825487108900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4775fac7-9277-47c5-8b89-e7227dee01cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825487323400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "981c77e6-eb08-4607-ba19-14440d71f5ca", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825488031700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80ed994-8f25-4fd1-8114-937f18d7392e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825489898300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e3f270b-a473-4a33-b181-1b798c33333e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825489994800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51cc1bce-2e59-4930-9b36-68f6669abd21", "name": "entry : default@ProcessLibs cost memory 0.125579833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825490640800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8902e722-5a15-4286-bc0a-b658c3ab062b", "name": "runTaskFromQueue task cost before running: 801 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825490756300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7977424-7271-486f-9bd3-19f2772686a4", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825488020700, "endTime": 30825490820600, "totalTime": 2708100}, "additional": {"logType": "info", "children": [], "durationId": "744675ca-12c0-46ed-bfff-a1fda2d29cf3"}}, {"head": {"id": "686cc406-fb16-4b16-b047-c1580fa73c88", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825496917400, "endTime": 30825516094800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a1243a43-076b-4df0-a4d8-1e150f6dec16", "logId": "d96b1aab-cdba-446a-877a-60a4c3eb5c28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1243a43-076b-4df0-a4d8-1e150f6dec16", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825492834100}, "additional": {"logType": "detail", "children": [], "durationId": "686cc406-fb16-4b16-b047-c1580fa73c88"}}, {"head": {"id": "5a78ec1d-0463-49b2-a3d9-bd79ed7139c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825493161800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa139173-5cac-4c49-b380-49e2309a9a94", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825493249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92a8206-2bac-4d4f-8c14-ee1fdfe36a5d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825493978800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b701ec-3363-4407-ac0d-f61c457a76e9", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825496942300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167aac9b-b66b-472b-974b-4e1c3441bae3", "name": "Incremental task entry:default@CompileResource pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825515884000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e15baf3-1182-47af-bb84-5325bbe0b078", "name": "entry : default@CompileResource cost memory 1.4058303833007812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825516010000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d96b1aab-cdba-446a-877a-60a4c3eb5c28", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825496917400, "endTime": 30825516094800}, "additional": {"logType": "info", "children": [], "durationId": "686cc406-fb16-4b16-b047-c1580fa73c88"}}, {"head": {"id": "f3a52dcf-ac80-4bc8-9140-cd11498ddf25", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825522227200, "endTime": 30825523408300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eb0f09ff-1799-4546-b86e-9b72815b6f1a", "logId": "62991a4a-5d6b-4ca1-a017-5eea47624459"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb0f09ff-1799-4546-b86e-9b72815b6f1a", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825519416500}, "additional": {"logType": "detail", "children": [], "durationId": "f3a52dcf-ac80-4bc8-9140-cd11498ddf25"}}, {"head": {"id": "ec2984db-5d1e-4beb-8744-5682a7b9fe10", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825519840800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "120244c6-19c7-458b-af32-152030032bc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825519947400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c2ec7b3-3c1c-4f0c-bb42-bc56e6eb5604", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825522236200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b03c70-3084-44ee-a20f-284233ab92ac", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825522458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7427f267-7bb2-4404-81f0-6f77c4a034cb", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825523255300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b3bb251-35d1-42d2-a5a6-bb69b1c432bf", "name": "entry : default@DoNativeStrip cost memory 0.076904296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825523344000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62991a4a-5d6b-4ca1-a017-5eea47624459", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825522227200, "endTime": 30825523408300}, "additional": {"logType": "info", "children": [], "durationId": "f3a52dcf-ac80-4bc8-9140-cd11498ddf25"}}, {"head": {"id": "5dd9a12c-e397-44a0-99c2-98cca7aff31a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825528893400, "endTime": 30827040959200}, "additional": {"children": ["c3d32852-0e0a-4108-b2aa-87ac613e87fd", "bb23d427-b1c2-49a1-84c8-83b8f17fa284"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "69c94ec7-ea31-43be-9ca1-a5c23e2d8fe6", "logId": "049906d4-75a1-4136-8947-8d6b25db4a15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69c94ec7-ea31-43be-9ca1-a5c23e2d8fe6", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825524699900}, "additional": {"logType": "detail", "children": [], "durationId": "5dd9a12c-e397-44a0-99c2-98cca7aff31a"}}, {"head": {"id": "eadd5f48-e7e5-4a88-b27a-9eb56d44aeb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825525019400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0118cb0-a99b-48d5-9096-a953bea551cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825525145600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf116b6-a3f0-4435-a011-4ac6efa74b51", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825528905400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5ed2e5-c3d7-4508-aad8-33942952c112", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825539526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "450942c6-ffcc-4818-803e-6551e36cd858", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825539676900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fef40291-e4b5-40f2-881c-94f82be43f78", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825553127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c5b5672-9238-422e-a67a-cce4df989dce", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825554717600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68395304-dae9-4e1a-961f-cdf4d4628644", "name": "default@CompileArkTS work[54] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825555981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30825658465100, "endTime": 30827031200500}, "additional": {"children": ["7e82148c-3e05-4009-b75f-32cfbc375945", "fdcd1f9b-3d7f-4788-a191-59327bf91276", "763f0b9a-ee5a-4358-963e-943484b1c599", "2aa6d724-7245-4b8f-b1e3-1f868ce2c645", "0de55833-06f2-467b-99fa-f86d7b9aa07b", "8f8988fb-973d-435b-9d97-28f962535066"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5dd9a12c-e397-44a0-99c2-98cca7aff31a", "logId": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bc8a742-9ff3-4364-b17a-1e0311a619ab", "name": "default@CompileArkTS work[54] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825556764800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a54398-ae0c-4069-b833-e265245aad63", "name": "default@CompileArkTS work[54] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825556860400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2bfe5f7-c807-442b-b91d-09fe4251f2b8", "name": "CopyResources startTime: 30825556917400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825556919100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52cefbc-7cc4-4ff2-9da9-8c6e823aa48e", "name": "default@CompileArkTS work[55] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825556972300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb23d427-b1c2-49a1-84c8-83b8f17fa284", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30826782605700, "endTime": 30826815972400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5dd9a12c-e397-44a0-99c2-98cca7aff31a", "logId": "42b38e99-b844-4219-83ec-16464ecb2108"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "677831b5-137c-4fea-afcc-da3346460271", "name": "default@CompileArkTS work[55] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825557590600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d376dee-72a7-44b8-949f-fce3595a82b5", "name": "default@CompileArkTS work[55] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825557681400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c6d9af3-310b-4e4d-8edd-7e905be062cd", "name": "entry : default@CompileArkTS cost memory -4.715400695800781", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825557825000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "503a411c-ae9e-4b55-bd83-f687b1bb6c1a", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825563208100, "endTime": 30825566383000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "7a4c7b95-2b65-4c25-8a34-a0719b573430", "logId": "7a3ac052-82b8-4b98-895f-f5ff8af77747"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a4c7b95-2b65-4c25-8a34-a0719b573430", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825559028200}, "additional": {"logType": "detail", "children": [], "durationId": "503a411c-ae9e-4b55-bd83-f687b1bb6c1a"}}, {"head": {"id": "e5914fc7-f4d5-40b7-9d52-2fb8f23141f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825559391000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f013f0d-e100-4c06-9ad3-7042ab0546e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825559465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c97ae7-346b-4074-b9b4-8a63bfdd9205", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825563220900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2419b942-313c-47ab-aa79-f5f0c4c7595a", "name": "entry : default@BuildJS cost memory 0.12812042236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825566182400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "453684fc-0a53-4d29-be2f-db59fef4c38d", "name": "runTaskFromQueue task cost before running: 877 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825566317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a3ac052-82b8-4b98-895f-f5ff8af77747", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825563208100, "endTime": 30825566383000, "totalTime": 3087000}, "additional": {"logType": "info", "children": [], "durationId": "503a411c-ae9e-4b55-bd83-f687b1bb6c1a"}}, {"head": {"id": "6c55dec3-edb1-45f1-b45a-f7cbb1b4c547", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825570667800, "endTime": 30825572352800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "adb15648-5d92-4abf-829d-eed09bb7a883", "logId": "89919e90-9c07-4363-8ce7-2402480a2b6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adb15648-5d92-4abf-829d-eed09bb7a883", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825567955500}, "additional": {"logType": "detail", "children": [], "durationId": "6c55dec3-edb1-45f1-b45a-f7cbb1b4c547"}}, {"head": {"id": "6684790b-6bdf-4e29-ba33-2efbab1b5d00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825568409600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23099453-b965-420c-9388-bc4c492f6619", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825568508100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c5021d-8377-4789-a29b-05fe363a5d24", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825570677400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9dc2f05-f7e6-4c64-a0bf-1149f16b4e4c", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825570966300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "166688cb-372b-46da-ae3c-34d1aeb2ab73", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825572171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03795a1-c5e1-4b47-a50f-2fa0737e42c6", "name": "entry : default@CacheNativeLibs cost memory 0.0912933349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825572280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89919e90-9c07-4363-8ce7-2402480a2b6a", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825570667800, "endTime": 30825572352800}, "additional": {"logType": "info", "children": [], "durationId": "6c55dec3-edb1-45f1-b45a-f7cbb1b4c547"}}, {"head": {"id": "b79712fd-b160-469c-a61c-80112fb1db0b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825657878400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc18ed1-a746-4185-9fd5-6748be7bf505", "name": "default@CompileArkTS work[54] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825658253200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "807f84e0-c838-4376-a157-2ae3b1b3f18e", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825658346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3a4e08-b8f3-4087-987b-8ef0f54e06c7", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825658400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e61a802-c87d-4276-8b39-d4c254541ea6", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825658729200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d536358-b314-4496-b6be-4a4162bb3967", "name": "default@CompileArkTS work[55] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825660557700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12eb0f24-e0cb-4514-97af-d35f33144c08", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826835325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1516067f-f9ee-46a9-9478-ad01f965cc9a", "name": "CopyResources is end, endTime: 30826836508600", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826836533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77d8e491-ac94-4a14-ac12-46849caa2ad0", "name": "default@CompileArkTS work[55] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826837156200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42b38e99-b844-4219-83ec-16464ecb2108", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30826782605700, "endTime": 30826815972400}, "additional": {"logType": "info", "children": [], "durationId": "bb23d427-b1c2-49a1-84c8-83b8f17fa284", "parent": "049906d4-75a1-4136-8947-8d6b25db4a15"}}, {"head": {"id": "c151525b-395d-4972-b426-789523285b95", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827031527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e82148c-3e05-4009-b75f-32cfbc375945", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30825658584600, "endTime": 30825662388600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "logId": "d4c19dc8-64de-44b4-8785-796268f98d9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4c19dc8-64de-44b4-8785-796268f98d9e", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825658584600, "endTime": 30825662388600}, "additional": {"logType": "info", "children": [], "durationId": "7e82148c-3e05-4009-b75f-32cfbc375945", "parent": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}}, {"head": {"id": "fdcd1f9b-3d7f-4788-a191-59327bf91276", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30825662405400, "endTime": 30825662500800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "logId": "ce94239f-f013-4a94-b1a7-8b486e7288e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce94239f-f013-4a94-b1a7-8b486e7288e5", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825662405400, "endTime": 30825662500800}, "additional": {"logType": "info", "children": [], "durationId": "fdcd1f9b-3d7f-4788-a191-59327bf91276", "parent": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}}, {"head": {"id": "763f0b9a-ee5a-4358-963e-943484b1c599", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30825662704900, "endTime": 30825662745100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "logId": "7de8316c-a92f-408f-b8e9-2f5c60386769"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7de8316c-a92f-408f-b8e9-2f5c60386769", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825662704900, "endTime": 30825662745100}, "additional": {"logType": "info", "children": [], "durationId": "763f0b9a-ee5a-4358-963e-943484b1c599", "parent": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}}, {"head": {"id": "2aa6d724-7245-4b8f-b1e3-1f868ce2c645", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30825662758900, "endTime": 30826934708200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "logId": "dfeb45a3-7714-4be0-b815-c1c70de3cb24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfeb45a3-7714-4be0-b815-c1c70de3cb24", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825662758900, "endTime": 30826934708200}, "additional": {"logType": "info", "children": [], "durationId": "2aa6d724-7245-4b8f-b1e3-1f868ce2c645", "parent": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}}, {"head": {"id": "0de55833-06f2-467b-99fa-f86d7b9aa07b", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30826934731800, "endTime": 30826948998500}, "additional": {"children": ["d69d3c04-4a6f-48e6-ac45-853c23fe2c7d", "a7f17ef7-72e4-40e1-af7b-67e6f905a614", "158aa959-8ebb-475f-940b-3c03d052a7ec"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "logId": "cb3966c6-dde2-4c1a-b868-4b6e7b3a985c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb3966c6-dde2-4c1a-b868-4b6e7b3a985c", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826934731800, "endTime": 30826948998500}, "additional": {"logType": "info", "children": ["2e7655d7-eb6b-4ee6-9fae-3069492ed0cb", "caaa6977-3688-4642-8dc2-e2d7578c2e9e", "b502f5b4-53a9-4fd2-9e93-be8e78fb7b0c"], "durationId": "0de55833-06f2-467b-99fa-f86d7b9aa07b", "parent": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}}, {"head": {"id": "d69d3c04-4a6f-48e6-ac45-853c23fe2c7d", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30826934756200, "endTime": 30826934764200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0de55833-06f2-467b-99fa-f86d7b9aa07b", "logId": "2e7655d7-eb6b-4ee6-9fae-3069492ed0cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e7655d7-eb6b-4ee6-9fae-3069492ed0cb", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826934756200, "endTime": 30826934764200}, "additional": {"logType": "info", "children": [], "durationId": "d69d3c04-4a6f-48e6-ac45-853c23fe2c7d", "parent": "cb3966c6-dde2-4c1a-b868-4b6e7b3a985c"}}, {"head": {"id": "a7f17ef7-72e4-40e1-af7b-67e6f905a614", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30826934768500, "endTime": 30826937320900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0de55833-06f2-467b-99fa-f86d7b9aa07b", "logId": "caaa6977-3688-4642-8dc2-e2d7578c2e9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "caaa6977-3688-4642-8dc2-e2d7578c2e9e", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826934768500, "endTime": 30826937320900}, "additional": {"logType": "info", "children": [], "durationId": "a7f17ef7-72e4-40e1-af7b-67e6f905a614", "parent": "cb3966c6-dde2-4c1a-b868-4b6e7b3a985c"}}, {"head": {"id": "158aa959-8ebb-475f-940b-3c03d052a7ec", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30826937325200, "endTime": 30826948979400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "0de55833-06f2-467b-99fa-f86d7b9aa07b", "logId": "b502f5b4-53a9-4fd2-9e93-be8e78fb7b0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b502f5b4-53a9-4fd2-9e93-be8e78fb7b0c", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826937325200, "endTime": 30826948979400}, "additional": {"logType": "info", "children": [], "durationId": "158aa959-8ebb-475f-940b-3c03d052a7ec", "parent": "cb3966c6-dde2-4c1a-b868-4b6e7b3a985c"}}, {"head": {"id": "8f8988fb-973d-435b-9d97-28f962535066", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30826949021200, "endTime": 30827030918700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "logId": "110826d8-6852-421e-b41d-16b096c243ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "110826d8-6852-421e-b41d-16b096c243ab", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30826949021200, "endTime": 30827030918700}, "additional": {"logType": "info", "children": [], "durationId": "8f8988fb-973d-435b-9d97-28f962535066", "parent": "3f5da13b-3afa-4cda-ac64-9998ebf9e850"}}, {"head": {"id": "336a29c3-128e-4a13-a063-67d02d7fdf86", "name": "default@CompileArkTS work[54] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827040756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5da13b-3afa-4cda-ac64-9998ebf9e850", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30825658465100, "endTime": 30827031200500}, "additional": {"logType": "info", "children": ["d4c19dc8-64de-44b4-8785-796268f98d9e", "ce94239f-f013-4a94-b1a7-8b486e7288e5", "7de8316c-a92f-408f-b8e9-2f5c60386769", "dfeb45a3-7714-4be0-b815-c1c70de3cb24", "cb3966c6-dde2-4c1a-b868-4b6e7b3a985c", "110826d8-6852-421e-b41d-16b096c243ab"], "durationId": "c3d32852-0e0a-4108-b2aa-87ac613e87fd", "parent": "049906d4-75a1-4136-8947-8d6b25db4a15"}}, {"head": {"id": "049906d4-75a1-4136-8947-8d6b25db4a15", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30825528893400, "endTime": 30827040959200, "totalTime": 1401728800}, "additional": {"logType": "info", "children": ["3f5da13b-3afa-4cda-ac64-9998ebf9e850", "42b38e99-b844-4219-83ec-16464ecb2108"], "durationId": "5dd9a12c-e397-44a0-99c2-98cca7aff31a"}}, {"head": {"id": "896ce186-61a4-4f72-bbd4-bb7e4b7f0898", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827048596300, "endTime": 30827050686400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "9726871b-2a22-44b7-8beb-7b18d0156ff0", "logId": "fec77934-4a04-4700-9256-903ee20a346a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9726871b-2a22-44b7-8beb-7b18d0156ff0", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827046263400}, "additional": {"logType": "detail", "children": [], "durationId": "896ce186-61a4-4f72-bbd4-bb7e4b7f0898"}}, {"head": {"id": "13d5f9dd-cd49-418f-9e4a-8ee9320e88cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827046784800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e731ae5-9c78-4440-9491-5fb2e24cc6b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827046889500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11548dbf-8822-44b2-b237-8816ec00c2c5", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827048646700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90985aeb-b777-4145-96d6-a75cd6094ef5", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827048986300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2e6249-3e4b-4e02-a1e8-78e1718de275", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827050251200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18da0bd0-ae6e-4ae9-8d3f-c04fdedd3c0f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07956695556640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827050543700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec77934-4a04-4700-9256-903ee20a346a", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827048596300, "endTime": 30827050686400}, "additional": {"logType": "info", "children": [], "durationId": "896ce186-61a4-4f72-bbd4-bb7e4b7f0898"}}, {"head": {"id": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827064879700, "endTime": 30827701236800}, "additional": {"children": ["6101dcff-971e-47aa-8c02-9a62b43982d5", "d5ad2f9d-d43c-4884-88c9-d7b83adf8282", "1c782c35-1d88-4ac1-bd81-2d1f115374c8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "d12dd7fb-cae5-4a63-ad4a-0595b9f52c1e", "logId": "76905818-89e4-4b92-a070-726a090a1255"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d12dd7fb-cae5-4a63-ad4a-0595b9f52c1e", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827054878300}, "additional": {"logType": "detail", "children": [], "durationId": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28"}}, {"head": {"id": "60757365-fc60-4df7-9ea4-6032ff8023a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827055376400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f69620-4098-4438-9c01-73b846dba26a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827055483900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370af077-8ae8-4b63-91f6-ecc9345eaa6c", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827064889900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7893ac-8ae4-4186-90ae-5d0b7b78960e", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827076860300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91ba0bbb-beae-408c-911f-4a73ea7684d3", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827076999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "225ff3ba-fd0d-4a06-9703-73f8632fbbca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827077086700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "867cb210-15cc-434c-aa1e-79964d0bd23b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827077262100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6101dcff-971e-47aa-8c02-9a62b43982d5", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827078493400, "endTime": 30827079998200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28", "logId": "ed8e569d-1571-4b33-9b47-881b6053dae6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bc1dda1-ca7e-4be3-b6e3-9c5a84c725e2", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827079840300}, "additional": {"logType": "debug", "children": [], "durationId": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28"}}, {"head": {"id": "ed8e569d-1571-4b33-9b47-881b6053dae6", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827078493400, "endTime": 30827079998200}, "additional": {"logType": "info", "children": [], "durationId": "6101dcff-971e-47aa-8c02-9a62b43982d5", "parent": "76905818-89e4-4b92-a070-726a090a1255"}}, {"head": {"id": "d5ad2f9d-d43c-4884-88c9-d7b83adf8282", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827080587200, "endTime": 30827082262700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28", "logId": "859c416a-38f3-4b0a-9fc6-db7f1c762833"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "610b68cd-11fc-403c-8bc9-50d93dde6dfb", "name": "default@PackageHap work[56] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827081326400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c782c35-1d88-4ac1-bd81-2d1f115374c8", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30827146087100, "endTime": 30827700571400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28", "logId": "9f5b915a-5704-4fa2-b3fb-7becab0b4598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c29e2971-9d79-4050-86ef-e88a891a44a2", "name": "default@PackageHap work[56] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827082106000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c5acedc-4082-4bed-8ee1-cb0a78bcb4bf", "name": "default@PackageHap work[56] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827082202400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "859c416a-38f3-4b0a-9fc6-db7f1c762833", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827080587200, "endTime": 30827082262700}, "additional": {"logType": "info", "children": [], "durationId": "d5ad2f9d-d43c-4884-88c9-d7b83adf8282", "parent": "76905818-89e4-4b92-a070-726a090a1255"}}, {"head": {"id": "e830fdc2-0f2a-43c0-a1dd-c81166d98af4", "name": "entry : default@PackageHap cost memory 1.2851486206054688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827086263900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4493fd1-db03-4f60-9aca-5e456fea7c58", "name": "default@PackageHap work[56] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827146031500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f44a4b-ad10-403d-88a1-2220d47363a3", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827192476600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e659fc-bafe-4a84-8678-35a27880d9df", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827192625300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaadaaf2-7aa5-40dc-9c3c-b2060d690d2b", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827192689300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b66c934-8f31-46f2-83cb-d8a779e401b5", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827192740100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "380de290-d082-46a9-bc14-c15e8b860ce3", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827192787300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a6543e-301b-4d33-8730-d263036d4708", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827192832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50dda63a-8397-4480-b4ab-b0f2a530a6ab", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827700777500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bd1dd9e-5142-4e94-9df6-c59fe86fc8a0", "name": "default@PackageHap work[56] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827701068800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f5b915a-5704-4fa2-b3fb-7becab0b4598", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30827146087100, "endTime": 30827700571400}, "additional": {"logType": "info", "children": [], "durationId": "1c782c35-1d88-4ac1-bd81-2d1f115374c8", "parent": "76905818-89e4-4b92-a070-726a090a1255"}}, {"head": {"id": "76905818-89e4-4b92-a070-726a090a1255", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827064879700, "endTime": 30827701236800, "totalTime": 576018400}, "additional": {"logType": "info", "children": ["ed8e569d-1571-4b33-9b47-881b6053dae6", "859c416a-38f3-4b0a-9fc6-db7f1c762833", "9f5b915a-5704-4fa2-b3fb-7becab0b4598"], "durationId": "03b5e00d-1e7a-49dc-b553-e5fd4b778f28"}}, {"head": {"id": "e2692fb0-feef-4152-bddd-b8ce9568a409", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827708481400, "endTime": 30827713937100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "ed925de1-7d9d-4541-8993-ade264652053", "logId": "b46b0069-0c53-4162-b5e7-fc6bb5f06abc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed925de1-7d9d-4541-8993-ade264652053", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827705285500}, "additional": {"logType": "detail", "children": [], "durationId": "e2692fb0-feef-4152-bddd-b8ce9568a409"}}, {"head": {"id": "0ed8d02a-593b-48c7-afa1-5e23312989da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827705899600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d98913-9f5a-4c99-a08c-e3240bfee78c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827706069500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88de6ae-4aeb-40c3-9922-73da6e7947e5", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827708491500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad00217-ca1f-4c7a-b8c9-5871989e9c82", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827708948300}, "additional": {"logType": "warn", "children": [], "durationId": "e2692fb0-feef-4152-bddd-b8ce9568a409"}}, {"head": {"id": "83e8d6ae-f0f5-494e-b1bc-6787c1401416", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827710070500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28c3acf7-04b1-4591-ac53-8dde27e0a108", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827710414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef5942b0-e670-4fbb-bb1d-6bf3b22881f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827710767400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "485cca47-d323-40ab-ae3f-8665be64e42d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827710853900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9ac7cf5-2810-4b0b-93d5-5ba28efa5b5a", "name": "entry : default@SignHap cost memory 0.120330810546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827712962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916df936-9759-4c23-aacc-e5f6ed0c37fa", "name": "runTaskFromQueue task cost before running: 3 s 24 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827713536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b46b0069-0c53-4162-b5e7-fc6bb5f06abc", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827708481400, "endTime": 30827713937100, "totalTime": 4895300}, "additional": {"logType": "info", "children": [], "durationId": "e2692fb0-feef-4152-bddd-b8ce9568a409"}}, {"head": {"id": "8e414feb-a094-4a77-bdb7-60d2e6f6f8af", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827725374200, "endTime": 30827734005000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b340d873-d583-47a6-a62b-e01c668b6914", "logId": "3d1d2800-c429-48e1-9fc6-837c875012ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b340d873-d583-47a6-a62b-e01c668b6914", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827720966500}, "additional": {"logType": "detail", "children": [], "durationId": "8e414feb-a094-4a77-bdb7-60d2e6f6f8af"}}, {"head": {"id": "d1e9eec8-ec27-4d41-b1da-5997f9ecb8ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827721564000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2914e3bc-31fa-4f7f-b3da-a1a7de0b96e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827722862400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48d8d7c-5433-4ed2-8807-92e18df4034e", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827725385800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e99046-2ee7-4eef-aba5-d2c6aecb40c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827733410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16d42292-c107-4fae-b77d-66914f6cc1c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827733532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81f936f-2e0e-4c5c-b08e-6c35c782456b", "name": "entry : default@CollectDebugSymbol cost memory 0.24011993408203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827733636500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9f2332-ee49-4fb9-8fbc-8e46b1a3a8eb", "name": "runTaskFromQueue task cost before running: 3 s 45 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827733885900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1d2800-c429-48e1-9fc6-837c875012ce", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827725374200, "endTime": 30827734005000, "totalTime": 8486100}, "additional": {"logType": "info", "children": [], "durationId": "8e414feb-a094-4a77-bdb7-60d2e6f6f8af"}}, {"head": {"id": "d6d1fb36-0a2c-46ae-9229-e4e824cc7f6b", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827737444800, "endTime": 30827737852200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "dbb82fc5-11e4-41a6-b110-aed1855e86b5", "logId": "5b1dabf1-da3e-4607-b475-06efad44d412"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbb82fc5-11e4-41a6-b110-aed1855e86b5", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827737378900}, "additional": {"logType": "detail", "children": [], "durationId": "d6d1fb36-0a2c-46ae-9229-e4e824cc7f6b"}}, {"head": {"id": "dbd76813-2c5e-44aa-96f9-fd1a7f8dce39", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827737452600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6607abe-9a1b-4c97-b2c3-c4df25d633f0", "name": "entry : assembleHap cost memory 0.011322021484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827737577700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c851a402-6151-40ea-879c-055bc968deb0", "name": "runTaskFromQueue task cost before running: 3 s 48 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827737781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b1dabf1-da3e-4607-b475-06efad44d412", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827737444800, "endTime": 30827737852200, "totalTime": 205900}, "additional": {"logType": "info", "children": [], "durationId": "d6d1fb36-0a2c-46ae-9229-e4e824cc7f6b"}}, {"head": {"id": "a5392015-3c6e-48c9-9f1d-6d02f76c8a7c", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827749652500, "endTime": 30827749698900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58b6113d-6274-4b72-a629-dd970dcc01fc", "logId": "81864f7b-e04c-47d2-a956-83e6e4e8514d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81864f7b-e04c-47d2-a956-83e6e4e8514d", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827749652500, "endTime": 30827749698900}, "additional": {"logType": "info", "children": [], "durationId": "a5392015-3c6e-48c9-9f1d-6d02f76c8a7c"}}, {"head": {"id": "85c90606-6e79-4e8b-93a2-f0fa029ad8a3", "name": "BUILD SUCCESSFUL in 3 s 61 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827749887700}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "be18c071-6e32-44dc-983f-556abca764eb", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30824689755800, "endTime": 30827751516100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 3}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f2bde2e2-5d59-4fc6-a90d-1046968d2fdb", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827751566300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1a00aa-0b39-48e3-a91a-e9dd506f1a90", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827751734300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdae36ef-cf61-489a-ba36-94ce22b3263e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827751795100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e7a64f2-98c2-4f1b-a430-359b8d345aa7", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827751847300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b808f787-bd48-4f97-8c5e-888541df29de", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827751917000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f01d6e-e396-4b45-82c4-175404daea25", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827752724600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9da7e9aa-3aba-44c2-a69a-b2828cacdb97", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827753934800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365eeb21-2891-42db-8b0b-7151cfcab981", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827754474300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c4fe6b-62d5-4166-8fa3-5e6e36f3704f", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827754563900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fbbdba5-b532-4f54-8083-f59ed105aebd", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827754867900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f76a49ce-fd86-4119-a20b-efc28b934350", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827755351500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e99e5c2-a531-4d82-84dc-7bc1d3f0e552", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827757372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b97d55-3d74-4228-8cf6-7e10eb9407f3", "name": "Incremental task entry:default@SyscapTransform post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827757928100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab981090-6fc1-4a32-8049-b470226b17e0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827758035900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bc6c33-d851-4d10-8107-ab651b560779", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827758221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d1d069d-5c4d-471b-b71c-f643987b0e65", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827758316600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8bd7bcd-c178-407d-bd20-bbc1e13d0c2e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827758402700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e666b6e-ed04-4bd3-86d4-93a6ba7556e5", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827760534000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a992796-2e8c-4e24-a8f6-d643dc66ece6", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827760874800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f3e802-1368-424b-9ac6-397cee8ff453", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827761256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b38642d-4d36-4868-8b7f-2cec4167d90d", "name": "Incremental task entry:default@ProcessLibs post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827761969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc3136f-0729-438d-b597-659c67b5ea53", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827762073200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d59771-27aa-46ea-ab58-9c4d27e24c0e", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827762152000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ce6eb1-b021-4caa-ae23-756f648f6db4", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827764620300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2910335c-b4a2-4ab3-bcf2-b59d7feed9e7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827765102700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08a567f5-f3f5-412f-bb2d-ae1810048e35", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827767478200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fd552da-b763-49a9-8f9a-8e9719e4a72a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827767785500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30754240-88f8-4d70-98cd-5cb4b5b3ed38", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827768189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3d9b730-bab2-4f32-b842-a999b33375c2", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827768775200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386bbdda-4841-4eec-a51d-a7c2d6f71ab0", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827768855400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0238c04a-e65c-499d-9a86-7a11f4b46be0", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827769098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53ae431c-3ec2-4832-a101-ec4ed54922dc", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827769357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ebbbd9-111d-4225-861b-86aeba3d4295", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827770039800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9054e6ba-3413-4d38-9246-b6a63b39726c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827771446600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f0ece60-5cf1-4cbc-926d-c53f11b54a7d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827772191200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352cc934-886b-4d53-a3a0-68a7fb577a25", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827773068500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db3822ea-eebd-4e4a-a515-1821f7f6fe21", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827773413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f0c8f5-c5b8-46a6-8fd4-93205300fc13", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827773624900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9dca8c2-d04b-4207-8511-2c5554dd0844", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827774222100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "975da6e9-e345-488b-b28c-b13e546a536b", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827774536700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2035d8ed-aa47-40da-b421-746062933c0d", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827774648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6696e460-075f-4a2c-a29c-62bb330e1d0a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827774827600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35a55927-a0bf-420e-a156-736164af9305", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827775653000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48200258-1fb8-44fc-8135-b416c8b2387f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827775917700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "436c95ba-898d-48e9-a9e9-e2f5808ec918", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827776135300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d137ea-dc47-4473-84fa-a4019ce0c550", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827785495500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb70d6e-1164-4ee2-b0c1-4c68a27b5894", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827785932200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fdd1378-33e4-4453-ad6a-57797070ffb4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827786157600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca290d3-8073-4145-9753-136a433e59cd", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827786230000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "999fe647-2122-4a54-a127-c7a59f6c4032", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827786415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c50e3ac7-f602-40e7-8e6d-3e46630625c1", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827787046400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e55a61c-8434-48c9-bed9-ce4bad8aa5b2", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827787386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff73636c-1773-4f3b-9650-c40ee207444c", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827787617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e4d09a-9da1-4a6d-9152-e7d3fc7f6487", "name": "Incremental task entry:default@PackageHap post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827788009900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4dffac8-6d46-4855-94e8-61a12bf2639b", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827788236100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aa017db-cb42-4611-9950-3fd26fd22456", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827788365800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c76c629-fcfb-4454-8a66-ba67d75ce6dc", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827788572400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bc2f514-8406-4f1e-b8a0-ec24eb28b851", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827800044900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a5fb58-1387-4e71-8a9f-a0067fbdeb3e", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827800779800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74efb30f-5ea9-4371-9cb4-3f54f6053b81", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827801095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4825d5d0-f453-4c22-b9ad-7bb14c233a1b", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30827801323100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}