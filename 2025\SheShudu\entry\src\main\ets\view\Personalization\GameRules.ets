import CommonConstants from "../../common/CommonConstants";
import Logger from "../../util/Logger";

@Builder
export function GameRulesBuilder(name: string, param: Object) {
  GameRules()
}

@Component
export struct GameRules {
  pageInfos: NavPathStack = new NavPathStack();
  scroller: Scroller = new Scroller();

  build() {
    NavDestination() {
      Column() {
        Scroll(this.scroller) {
          Column({ space: 20 }) {
            Text('数独游戏规则')
              .fontSize(24)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 20, bottom: 10 })
              
            Column({ space: 10 }) {
              Text('基本规则:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                
              Text('1. 数独是一种9×9的网格填数游戏。')
                .fontSize(16)
                
              Text('2. 每一行、每一列和每个3×3的小九宫格内都必须包含1到9的数字，且不能重复。')
                .fontSize(16)
                
              Text('3. 游戏开始时，部分格子已经填有数字，这些是提示数字，不能更改。')
                .fontSize(16)
                
              Text('4. 玩家需要根据逻辑推理，填充所有空白格子。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFFFFF')
            .padding(15)
            .borderRadius(16)
            
            Column({ space: 10 }) {
              Text('游戏技巧:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                
              Text('1. 排除法: 通过已知数字排除某格子的可能性。')
                .fontSize(16)
                
              Text('2. 唯一候选数法: 找出某格子唯一可能的数字。')
                .fontSize(16)
                
              Text('3. 区块摒除法: 分析小九宫格内数字的分布规律。')
                .fontSize(16)
                
              Text('4. 从简单难度开始，逐步提高挑战。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFFFFF')
            .padding(15)
            .borderRadius(16)
            
            Column({ space: 10 }) {
              Text('游戏模式:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)

              Text('1. 数独经典模式: 使用1-9数字的传统数独。')
                .fontSize(16)

              Text('2. 卡通模式: 使用可爱动物图标代替数字，规则相同。')
                .fontSize(16)

              Text('3. 难度分为简单、中等和困难三个级别。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFFFFF')
            .padding(15)
            .borderRadius(16)

            Column({ space: 10 }) {
              Text('自在模式:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                .fontColor('#FF6B35')

              Text('自在模式是一种特殊的数独游戏模式，为玩家提供更自由的游戏体验。')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)

              Text('特点:')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .margin({ top: 8 })

              Text('• 空白棋盘: 游戏开始时提供一个完全空白的9×9棋盘，没有任何预设数字。')
                .fontSize(16)

              Text('• 自由填写: 玩家可以在任意位置填入1-9的数字或动物图标。')
                .fontSize(16)

              Text('• 多人协作: 支持两人或多人轮流填写，共同完成数独挑战。')
                .fontSize(16)

              Text('• 单人练习: 也可以一个人慢慢思考，按照数独规则尝试填满整个棋盘。')
                .fontSize(16)

              Text('• 创意发挥: 可以先设计一个有趣的数独布局，再让朋友来解答。')
                .fontSize(16)

              Text('游戏规则:')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .margin({ top: 8 })

              Text('• 遵循标准数独规则：每行、每列、每个3×3小九宫格内数字不重复。')
                .fontSize(16)

              Text('• 可以随时修改已填入的数字，直到找到正确的解答。')
                .fontSize(16)

              Text('• 系统会实时检测填入的数字是否符合数独规则。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFF8F0')
            .padding(15)
            .borderRadius(16)
            .border({ width: 1, color: '#FFE4B5' })
          }
          .width(CommonConstants.FULL_PARENT)
          .padding({ bottom: 20 })
          .justifyContent(FlexAlign.Center)
        }
        .scrollable(ScrollDirection.Vertical)
        .scrollBar(BarState.Off)
        .edgeEffect(EdgeEffect.Spring)
      }
      .height(CommonConstants.FULL_PARENT)
      .width(CommonConstants.FULL_PARENT)
      .backgroundColor('#F5F5F5')
      
    }.title('玩法介绍')
    .onReady((context: NavDestinationContext) => {
      this.pageInfos = context.pathStack;
      Logger.info("GameRules page ready");
    })
  }
}