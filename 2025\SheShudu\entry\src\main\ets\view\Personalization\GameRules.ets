import CommonConstants from "../../common/CommonConstants";
import Logger from "../../util/Logger";

@Builder
export function GameRulesBuilder(name: string, param: Object) {
  GameRules()
}

@Component
export struct GameRules {
  pageInfos: NavPathStack = new NavPathStack();
  scroller: Scroller = new Scroller();

  build() {
    NavDestination() {
      Column() {
        Scroll(this.scroller) {
          Column({ space: 20 }) {
            Text('数独游戏规则')
              .fontSize(24)
              .fontWeight(FontWeight.Bold)
              .margin({ top: 20, bottom: 10 })
              
            Column({ space: 10 }) {
              Text('基本规则:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                
              Text('1. 数独是一种9×9的网格填数游戏。')
                .fontSize(16)
                
              Text('2. 每一行、每一列和每个3×3的小九宫格内都必须包含1到9的数字，且不能重复。')
                .fontSize(16)
                
              Text('3. 游戏开始时，部分格子已经填有数字，这些是提示数字，不能更改。')
                .fontSize(16)
                
              Text('4. 玩家需要根据逻辑推理，填充所有空白格子。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFFFFF')
            .padding(15)
            .borderRadius(16)
            
            Column({ space: 10 }) {
              Text('游戏技巧:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                
              Text('1. 排除法: 通过已知数字排除某格子的可能性。')
                .fontSize(16)
                
              Text('2. 唯一候选数法: 找出某格子唯一可能的数字。')
                .fontSize(16)
                
              Text('3. 区块摒除法: 分析小九宫格内数字的分布规律。')
                .fontSize(16)
                
              Text('4. 从简单难度开始，逐步提高挑战。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFFFFF')
            .padding(15)
            .borderRadius(16)
            
            Column({ space: 10 }) {
              Text('游戏模式:')
                .fontSize(18)
                .fontWeight(FontWeight.Medium)
                
              Text('1. 数独经典模式: 使用1-9数字的传统数独。')
                .fontSize(16)
                
              Text('2. 卡通模式: 使用可爱动物图标代替数字，规则相同。')
                .fontSize(16)
                
              Text('3. 难度分为简单、中等和困难三个级别。')
                .fontSize(16)
            }
            .width('90%')
            .backgroundColor('#FFFFFF')
            .padding(15)
            .borderRadius(16)
          }
          .width(CommonConstants.FULL_PARENT)
          .padding({ bottom: 20 })
          .justifyContent(FlexAlign.Center)
        }
        .scrollable(ScrollDirection.Vertical)
        .scrollBar(BarState.Off)
        .edgeEffect(EdgeEffect.Spring)
      }
      .height(CommonConstants.FULL_PARENT)
      .width(CommonConstants.FULL_PARENT)
      .backgroundColor('#F5F5F5')
      
    }.title('玩法介绍')
    .linearGradient({
      angle: 180,
      colors: [['#FFF6F6', 0.0], ['#FFF0F0', 0.5], ['#FFFAFA', 1.0]]
    }).onReady((context: NavDestinationContext) => {
      this.pageInfos = context.pathStack;
      Logger.info("GameRules page ready");
    })
  }
}