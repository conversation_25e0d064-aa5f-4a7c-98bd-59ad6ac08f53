{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "5c58443e-c85a-4b8e-93f6-9b309d0f9f25", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794106868000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5532d86-febc-405c-88bd-6f20958298c0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794111161100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ebe58f7-68f8-4138-90a2-6381302e800b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794111501100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a49f29-06d4-47eb-a97a-83f50363ea70", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794121414500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a623004-b328-4991-8022-6206ce21d69b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801349883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801356605900, "endTime": 33801707180400}, "additional": {"children": ["e8f53662-a12a-467f-9974-24b4e3b24989", "3169f271-a040-4c69-8348-281c40be574a", "c3542091-2bec-4e91-9760-a2fcc5bff799", "e4837c8b-b4bb-48a5-a48b-3331739d2cce", "4af6b135-c8ba-4902-a149-b8f9efa0a78d", "bf5cbe88-6d40-4e54-ae03-dc2041d4675c", "d080bba3-36bc-4819-82bb-c3640c8c6384"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9272e1fc-488d-4e59-b42a-551e99d8004f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8f53662-a12a-467f-9974-24b4e3b24989", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801356613000, "endTime": 33801370238300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "51304685-31f6-4d25-a345-6c02fb78cb46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3169f271-a040-4c69-8348-281c40be574a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801370258800, "endTime": 33801702712800}, "additional": {"children": ["5fee5a0c-127c-431c-bb02-bdfdfa104b37", "4c6d1110-79ff-4ccb-b612-bb630473f741", "5ce49748-1e95-49e9-9ed2-63717d5f4c7e", "1707e5b6-022c-46c3-8f28-c56fd81d7b06", "49a6c466-d9c5-4294-a4ca-8585b89d09de", "17940e90-d042-4cbd-9749-b49a0d45c8e4", "6ea8610b-b24d-4374-bc62-f8f5c4924ce6", "c7f42c9c-3e48-44fc-a15b-962b662f4c79", "7dd1a851-9568-4bc9-ac39-ed255073f611"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3542091-2bec-4e91-9760-a2fcc5bff799", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801702768200, "endTime": 33801707135100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "2128dd8f-2078-48aa-b084-cccc56075d84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4837c8b-b4bb-48a5-a48b-3331739d2cce", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801707143000, "endTime": 33801707170200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "5fa5cd41-8f3c-4173-bcd6-c5bad8ed24f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4af6b135-c8ba-4902-a149-b8f9efa0a78d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801360112200, "endTime": 33801360151000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "0fcfd94d-22b9-43ca-9b74-7f8ed9f658a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fcfd94d-22b9-43ca-9b74-7f8ed9f658a6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801360112200, "endTime": 33801360151000}, "additional": {"logType": "info", "children": [], "durationId": "4af6b135-c8ba-4902-a149-b8f9efa0a78d", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "bf5cbe88-6d40-4e54-ae03-dc2041d4675c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801365354000, "endTime": 33801365376200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "9f5c46f6-b345-4a5c-9c46-1208219db803"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f5c46f6-b345-4a5c-9c46-1208219db803", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801365354000, "endTime": 33801365376200}, "additional": {"logType": "info", "children": [], "durationId": "bf5cbe88-6d40-4e54-ae03-dc2041d4675c", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "c4035be5-4819-4532-9ef6-62f0aa83d953", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801365425300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1bfba3a-76e1-4b72-923a-db809d7aa41d", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801370115400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51304685-31f6-4d25-a345-6c02fb78cb46", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801356613000, "endTime": 33801370238300}, "additional": {"logType": "info", "children": [], "durationId": "e8f53662-a12a-467f-9974-24b4e3b24989", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "5fee5a0c-127c-431c-bb02-bdfdfa104b37", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801375226300, "endTime": 33801375233700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "64f71cb6-229a-4618-b852-7bd6f6485857"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c6d1110-79ff-4ccb-b612-bb630473f741", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801375248900, "endTime": 33801378921800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "bf6998a5-9d05-46bc-b552-39837e1370e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ce49748-1e95-49e9-9ed2-63717d5f4c7e", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801378940800, "endTime": 33801490869400}, "additional": {"children": ["a33ab234-d545-4f55-9a2d-8a34f08afd0f", "afd4981f-13dd-47ca-a68f-f773b7263e38", "3f14244a-a4a8-455d-93af-3fc4efbdba0b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "f9bcc14a-03f8-49bf-bee3-9c3b118fe748"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1707e5b6-022c-46c3-8f28-c56fd81d7b06", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801490883500, "endTime": 33801535144500}, "additional": {"children": ["1486aede-6cc2-4eac-851d-4a7084376902"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "9e5b1587-409a-41e4-99f8-61bb2e088cfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49a6c466-d9c5-4294-a4ca-8585b89d09de", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801535154400, "endTime": 33801651552700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "1bb00dc1-4d31-48b7-b02d-4f182ebc7c5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17940e90-d042-4cbd-9749-b49a0d45c8e4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801652905500, "endTime": 33801677772600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "3d9b1edb-76c1-4918-a289-bf993116b720"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ea8610b-b24d-4374-bc62-f8f5c4924ce6", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801677797400, "endTime": 33801701447100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "c792881f-5d9a-4e4a-b85f-f8255a4619a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7f42c9c-3e48-44fc-a15b-962b662f4c79", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801702361400, "endTime": 33801702680500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "50b77559-07b4-477d-9d8d-72cfe84fc4dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64f71cb6-229a-4618-b852-7bd6f6485857", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801375226300, "endTime": 33801375233700}, "additional": {"logType": "info", "children": [], "durationId": "5fee5a0c-127c-431c-bb02-bdfdfa104b37", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "bf6998a5-9d05-46bc-b552-39837e1370e5", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801375248900, "endTime": 33801378921800}, "additional": {"logType": "info", "children": [], "durationId": "4c6d1110-79ff-4ccb-b612-bb630473f741", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "a33ab234-d545-4f55-9a2d-8a34f08afd0f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801379618500, "endTime": 33801379639400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5ce49748-1e95-49e9-9ed2-63717d5f4c7e", "logId": "919f3e2d-eeaa-4b2d-9b4c-7883378d114e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "919f3e2d-eeaa-4b2d-9b4c-7883378d114e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801379618500, "endTime": 33801379639400}, "additional": {"logType": "info", "children": [], "durationId": "a33ab234-d545-4f55-9a2d-8a34f08afd0f", "parent": "f9bcc14a-03f8-49bf-bee3-9c3b118fe748"}}, {"head": {"id": "afd4981f-13dd-47ca-a68f-f773b7263e38", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801381969000, "endTime": 33801489942000}, "additional": {"children": ["3ac03b9b-3455-430a-884f-e7380dcb3576", "413daa95-ea89-4cc5-b3bd-93a1cd95945c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5ce49748-1e95-49e9-9ed2-63717d5f4c7e", "logId": "cfe8dd75-1dde-43d4-985c-aad793c92af2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ac03b9b-3455-430a-884f-e7380dcb3576", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801381972200, "endTime": 33801388352500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "afd4981f-13dd-47ca-a68f-f773b7263e38", "logId": "6483e741-9dcc-4327-9ee4-1aa8eb4663d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "413daa95-ea89-4cc5-b3bd-93a1cd95945c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801388468400, "endTime": 33801489923600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "afd4981f-13dd-47ca-a68f-f773b7263e38", "logId": "58200161-f6b6-41f2-9976-007e84be2393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e53e3731-da8f-4c0e-8f8e-c70bedfab141", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801381979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d9a285d-8d98-4ac5-b9fa-e61951b306c1", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801388097600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6483e741-9dcc-4327-9ee4-1aa8eb4663d8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801381972200, "endTime": 33801388352500}, "additional": {"logType": "info", "children": [], "durationId": "3ac03b9b-3455-430a-884f-e7380dcb3576", "parent": "cfe8dd75-1dde-43d4-985c-aad793c92af2"}}, {"head": {"id": "454b1bcc-c43c-4f16-8274-1835be8a5d82", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801388486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9cb23e4-c37a-4452-b37c-62c29bbda5f8", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801396142600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b6e0762-d51c-4855-bdb8-f8161b6d5009", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801396269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58cc23e-cedf-48c5-9163-4e7add29efc6", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801396400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bb62b26-501c-4452-b97f-89e27d7df131", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801396484400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f616524-facf-4823-919e-6bd3c5613a09", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801397960000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d8291c-70ec-410b-bda2-aadbaf4e503c", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801403864800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5548187-5bc0-46e0-88bb-58f46dadbf7e", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801415461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451a9169-448b-46c4-9657-3132b98f5e2b", "name": "Sdk init in 49 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801454750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b4934b-a012-409a-9314-1c41a5656966", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801454935200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 52}, "markType": "other"}}, {"head": {"id": "f79d26cb-4d19-4917-a0e6-24d935375f37", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801454952300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 52}, "markType": "other"}}, {"head": {"id": "6c617c44-2db2-4f30-813a-9dc5cac636d6", "name": "Project task initialization takes 33 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801489463800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b4def4-aef8-4781-a705-9ae968cf5c9c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801489619400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1415ded7-55ae-4c04-bc66-4efe1b39a1f2", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801489768400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea0f19dc-5c16-45e5-9b7a-333c481433a7", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801489844100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58200161-f6b6-41f2-9976-007e84be2393", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801388468400, "endTime": 33801489923600}, "additional": {"logType": "info", "children": [], "durationId": "413daa95-ea89-4cc5-b3bd-93a1cd95945c", "parent": "cfe8dd75-1dde-43d4-985c-aad793c92af2"}}, {"head": {"id": "cfe8dd75-1dde-43d4-985c-aad793c92af2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801381969000, "endTime": 33801489942000}, "additional": {"logType": "info", "children": ["6483e741-9dcc-4327-9ee4-1aa8eb4663d8", "58200161-f6b6-41f2-9976-007e84be2393"], "durationId": "afd4981f-13dd-47ca-a68f-f773b7263e38", "parent": "f9bcc14a-03f8-49bf-bee3-9c3b118fe748"}}, {"head": {"id": "3f14244a-a4a8-455d-93af-3fc4efbdba0b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801490833200, "endTime": 33801490853300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5ce49748-1e95-49e9-9ed2-63717d5f4c7e", "logId": "755fda1d-15bd-48e9-bf08-8f1c7b79ea4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "755fda1d-15bd-48e9-bf08-8f1c7b79ea4c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801490833200, "endTime": 33801490853300}, "additional": {"logType": "info", "children": [], "durationId": "3f14244a-a4a8-455d-93af-3fc4efbdba0b", "parent": "f9bcc14a-03f8-49bf-bee3-9c3b118fe748"}}, {"head": {"id": "f9bcc14a-03f8-49bf-bee3-9c3b118fe748", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801378940800, "endTime": 33801490869400}, "additional": {"logType": "info", "children": ["919f3e2d-eeaa-4b2d-9b4c-7883378d114e", "cfe8dd75-1dde-43d4-985c-aad793c92af2", "755fda1d-15bd-48e9-bf08-8f1c7b79ea4c"], "durationId": "5ce49748-1e95-49e9-9ed2-63717d5f4c7e", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "1486aede-6cc2-4eac-851d-4a7084376902", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801491646100, "endTime": 33801535048600}, "additional": {"children": ["855c7af4-05fc-48e6-b084-3edcadfd72d3", "c0fe9a1d-e31c-404c-bb2e-93a25cabfa63", "aa485b52-b9fb-438b-bb56-11b945380518"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1707e5b6-022c-46c3-8f28-c56fd81d7b06", "logId": "1ef2ba8f-7f51-4f87-99cd-9fac3016d444"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "855c7af4-05fc-48e6-b084-3edcadfd72d3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801494995600, "endTime": 33801495016700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1486aede-6cc2-4eac-851d-4a7084376902", "logId": "5c4437ac-3268-4951-9d8b-3cb7d6a01926"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c4437ac-3268-4951-9d8b-3cb7d6a01926", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801494995600, "endTime": 33801495016700}, "additional": {"logType": "info", "children": [], "durationId": "855c7af4-05fc-48e6-b084-3edcadfd72d3", "parent": "1ef2ba8f-7f51-4f87-99cd-9fac3016d444"}}, {"head": {"id": "c0fe9a1d-e31c-404c-bb2e-93a25cabfa63", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801498499500, "endTime": 33801532871000}, "additional": {"children": ["7ec49b84-9dbb-4b2f-9cbf-09dd12444132", "bd86b549-ebb6-4ea8-9a97-ced5c6477f9f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1486aede-6cc2-4eac-851d-4a7084376902", "logId": "d2959104-0eb9-4dcb-be85-22dcefaf96a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ec49b84-9dbb-4b2f-9cbf-09dd12444132", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801498505400, "endTime": 33801508777400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe9a1d-e31c-404c-bb2e-93a25cabfa63", "logId": "2aa8d7a3-ffa6-475a-b645-65872ee33ba1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd86b549-ebb6-4ea8-9a97-ced5c6477f9f", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801508803800, "endTime": 33801532854900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0fe9a1d-e31c-404c-bb2e-93a25cabfa63", "logId": "221b5371-edde-4298-9004-a36c8a9ebb7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5846498-08fb-4f9e-9fdc-012d26f5bd05", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801498512100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c76b33-0873-49f4-b5ca-180e75e64bf0", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801508221400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa8d7a3-ffa6-475a-b645-65872ee33ba1", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801498505400, "endTime": 33801508777400}, "additional": {"logType": "info", "children": [], "durationId": "7ec49b84-9dbb-4b2f-9cbf-09dd12444132", "parent": "d2959104-0eb9-4dcb-be85-22dcefaf96a7"}}, {"head": {"id": "34a31f8c-760a-4cdc-ad54-a3216b5b7291", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801508822000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c27562-0fdd-40c9-94ba-2e73af3a9221", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801525483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "062a9ccc-d634-40db-8e22-46590256b1b0", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801525650600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a42105-a30b-4075-82b4-67cc39d8da92", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801525864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "672efbcc-952a-4c1a-98b2-bb6fd95dd1de", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801526038000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea5df81b-a6bd-4a81-88b9-e58094913b14", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801526190900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d299323d-c06b-42c5-85d3-000fa56e8989", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801526269100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cacf984c-6969-463c-9ebe-dd58e2204a3c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801526363500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a76eb21f-5c31-4a89-a627-79ca1809cc3f", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801532473800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8673746-5ddf-423a-b467-b74ba5f1f445", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801532664600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f197fa94-c7ee-4229-835a-a6071be8046c", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801532759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68fc00e-082a-49fc-8ab3-06393cdebba6", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801532809200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "221b5371-edde-4298-9004-a36c8a9ebb7f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801508803800, "endTime": 33801532854900}, "additional": {"logType": "info", "children": [], "durationId": "bd86b549-ebb6-4ea8-9a97-ced5c6477f9f", "parent": "d2959104-0eb9-4dcb-be85-22dcefaf96a7"}}, {"head": {"id": "d2959104-0eb9-4dcb-be85-22dcefaf96a7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801498499500, "endTime": 33801532871000}, "additional": {"logType": "info", "children": ["2aa8d7a3-ffa6-475a-b645-65872ee33ba1", "221b5371-edde-4298-9004-a36c8a9ebb7f"], "durationId": "c0fe9a1d-e31c-404c-bb2e-93a25cabfa63", "parent": "1ef2ba8f-7f51-4f87-99cd-9fac3016d444"}}, {"head": {"id": "aa485b52-b9fb-438b-bb56-11b945380518", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801534966500, "endTime": 33801535027000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1486aede-6cc2-4eac-851d-4a7084376902", "logId": "e39f56cc-0d2b-42af-b331-80ae14036190"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e39f56cc-0d2b-42af-b331-80ae14036190", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801534966500, "endTime": 33801535027000}, "additional": {"logType": "info", "children": [], "durationId": "aa485b52-b9fb-438b-bb56-11b945380518", "parent": "1ef2ba8f-7f51-4f87-99cd-9fac3016d444"}}, {"head": {"id": "1ef2ba8f-7f51-4f87-99cd-9fac3016d444", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801491646100, "endTime": 33801535048600}, "additional": {"logType": "info", "children": ["5c4437ac-3268-4951-9d8b-3cb7d6a01926", "d2959104-0eb9-4dcb-be85-22dcefaf96a7", "e39f56cc-0d2b-42af-b331-80ae14036190"], "durationId": "1486aede-6cc2-4eac-851d-4a7084376902", "parent": "9e5b1587-409a-41e4-99f8-61bb2e088cfa"}}, {"head": {"id": "9e5b1587-409a-41e4-99f8-61bb2e088cfa", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801490883500, "endTime": 33801535144500}, "additional": {"logType": "info", "children": ["1ef2ba8f-7f51-4f87-99cd-9fac3016d444"], "durationId": "1707e5b6-022c-46c3-8f28-c56fd81d7b06", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "34fc8d66-146b-44be-bf8d-10412002f598", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801568246600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea1c0f6-0bd6-4a82-80e6-e15dd136d849", "name": "hvigorfile, resolve hvigorfile dependencies in 117 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801651436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bb00dc1-4d31-48b7-b02d-4f182ebc7c5a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801535154400, "endTime": 33801651552700}, "additional": {"logType": "info", "children": [], "durationId": "49a6c466-d9c5-4294-a4ca-8585b89d09de", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "7dd1a851-9568-4bc9-ac39-ed255073f611", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801652585500, "endTime": 33801652886700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3169f271-a040-4c69-8348-281c40be574a", "logId": "424d3990-02d1-4ff0-8ccc-4c83bc4417bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f13e3326-0fd3-4209-90f0-2ca2cb61228c", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801652630000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424d3990-02d1-4ff0-8ccc-4c83bc4417bd", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801652585500, "endTime": 33801652886700}, "additional": {"logType": "info", "children": [], "durationId": "7dd1a851-9568-4bc9-ac39-ed255073f611", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "20f28600-fa69-4e66-8668-bbdafc503beb", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801654784400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ebad339-2319-4da7-89bc-a06e865078f1", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801676648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d9b1edb-76c1-4918-a289-bf993116b720", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801652905500, "endTime": 33801677772600}, "additional": {"logType": "info", "children": [], "durationId": "17940e90-d042-4cbd-9749-b49a0d45c8e4", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "a960366e-f063-4edc-ae9c-eee65411dfcc", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801688774100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dcab4d9-6c47-4195-8d0e-6da336ea6944", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801688910200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c3f8f2-c9c3-4b82-aff1-f9969f75c1b3", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801693751500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75931991-ea1f-44dc-975d-2259ef6563e9", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801693884400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c792881f-5d9a-4e4a-b85f-f8255a4619a7", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801677797400, "endTime": 33801701447100}, "additional": {"logType": "info", "children": [], "durationId": "6ea8610b-b24d-4374-bc62-f8f5c4924ce6", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "4fd062a2-c2f5-4c0c-808a-208d08f8c71d", "name": "Configuration phase cost:328 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801702436400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b77559-07b4-477d-9d8d-72cfe84fc4dd", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801702361400, "endTime": 33801702680500}, "additional": {"logType": "info", "children": [], "durationId": "c7f42c9c-3e48-44fc-a15b-962b662f4c79", "parent": "eb69d6c4-cab3-422f-9708-7eb263e8b388"}}, {"head": {"id": "eb69d6c4-cab3-422f-9708-7eb263e8b388", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801370258800, "endTime": 33801702712800}, "additional": {"logType": "info", "children": ["64f71cb6-229a-4618-b852-7bd6f6485857", "bf6998a5-9d05-46bc-b552-39837e1370e5", "f9bcc14a-03f8-49bf-bee3-9c3b118fe748", "9e5b1587-409a-41e4-99f8-61bb2e088cfa", "1bb00dc1-4d31-48b7-b02d-4f182ebc7c5a", "3d9b1edb-76c1-4918-a289-bf993116b720", "c792881f-5d9a-4e4a-b85f-f8255a4619a7", "50b77559-07b4-477d-9d8d-72cfe84fc4dd", "424d3990-02d1-4ff0-8ccc-4c83bc4417bd"], "durationId": "3169f271-a040-4c69-8348-281c40be574a", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "d080bba3-36bc-4819-82bb-c3640c8c6384", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801706966400, "endTime": 33801707116900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295", "logId": "4d92492b-250a-42f3-b4ba-ba1ba97e8e59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d92492b-250a-42f3-b4ba-ba1ba97e8e59", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801706966400, "endTime": 33801707116900}, "additional": {"logType": "info", "children": [], "durationId": "d080bba3-36bc-4819-82bb-c3640c8c6384", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "2128dd8f-2078-48aa-b084-cccc56075d84", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801702768200, "endTime": 33801707135100}, "additional": {"logType": "info", "children": [], "durationId": "c3542091-2bec-4e91-9760-a2fcc5bff799", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "5fa5cd41-8f3c-4173-bcd6-c5bad8ed24f6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801707143000, "endTime": 33801707170200}, "additional": {"logType": "info", "children": [], "durationId": "e4837c8b-b4bb-48a5-a48b-3331739d2cce", "parent": "9272e1fc-488d-4e59-b42a-551e99d8004f"}}, {"head": {"id": "9272e1fc-488d-4e59-b42a-551e99d8004f", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801356605900, "endTime": 33801707180400}, "additional": {"logType": "info", "children": ["51304685-31f6-4d25-a345-6c02fb78cb46", "eb69d6c4-cab3-422f-9708-7eb263e8b388", "2128dd8f-2078-48aa-b084-cccc56075d84", "5fa5cd41-8f3c-4173-bcd6-c5bad8ed24f6", "0fcfd94d-22b9-43ca-9b74-7f8ed9f658a6", "9f5c46f6-b345-4a5c-9c46-1208219db803", "4d92492b-250a-42f3-b4ba-ba1ba97e8e59"], "durationId": "1758e0cf-fdbd-47af-8a4d-71b19bcf7295"}}, {"head": {"id": "555a2b66-9094-42e9-a232-89059b349258", "name": "Configuration task cost before running: 355 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801707721100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87612181-e3ae-4101-b02b-cb369e5e23f4", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801726667000, "endTime": 33801745047600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "1e66877a-e65f-4279-9c5c-0d2d16e2e9b6", "logId": "66a03c76-1481-400c-98cb-d36fb3b98fcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e66877a-e65f-4279-9c5c-0d2d16e2e9b6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801715848700}, "additional": {"logType": "detail", "children": [], "durationId": "87612181-e3ae-4101-b02b-cb369e5e23f4"}}, {"head": {"id": "44ae9017-193a-435a-8a79-710857c806b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801718219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4012ec-919c-4975-a89c-2fa21c778db3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801718434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "569ba7f1-fea5-426b-b4f5-80aab5f3c56d", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801726684900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c8651a-5e40-4e6a-ab23-4b9bafaf68cf", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801744845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c424a04-cc83-47e9-9719-233f7628f448", "name": "entry : default@PreBuild cost memory 0.30622100830078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801744980300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a03c76-1481-400c-98cb-d36fb3b98fcc", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801726667000, "endTime": 33801745047600}, "additional": {"logType": "info", "children": [], "durationId": "87612181-e3ae-4101-b02b-cb369e5e23f4"}}, {"head": {"id": "ec4ca9ac-9a70-43e4-bc26-8ac81cea01c4", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801754570600, "endTime": 33801756741900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d0aac614-68ae-4005-862e-f90f802433cc", "logId": "a43d1281-d48e-4cfd-b51e-f5bffb403844"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0aac614-68ae-4005-862e-f90f802433cc", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801750891300}, "additional": {"logType": "detail", "children": [], "durationId": "ec4ca9ac-9a70-43e4-bc26-8ac81cea01c4"}}, {"head": {"id": "b3055741-650e-4fe8-ad82-9d81f729402d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801753322500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2867b557-ab4a-4f63-89de-5a5fa449140b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801753492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "816074af-5ee9-4515-9c4d-461cbb5b604d", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801754582400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c9b2042-b70d-48d0-84bc-619797381e69", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801755365200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06517085-8e2c-4005-bcf4-2ec6f31de065", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801756495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fc89cf5-badc-4444-b9ac-b3591032c8df", "name": "entry : default@GenerateMetadata cost memory 0.0924072265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801756658900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a43d1281-d48e-4cfd-b51e-f5bffb403844", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801754570600, "endTime": 33801756741900}, "additional": {"logType": "info", "children": [], "durationId": "ec4ca9ac-9a70-43e4-bc26-8ac81cea01c4"}}, {"head": {"id": "66722f3b-e14d-4567-ab71-f7dc3d2d1f0a", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759086000, "endTime": 33801759726200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "830140dc-333c-4cbb-8160-207f6f073448", "logId": "d53bf82b-9676-4382-ae10-9e5b9d1c4ff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "830140dc-333c-4cbb-8160-207f6f073448", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801758409200}, "additional": {"logType": "detail", "children": [], "durationId": "66722f3b-e14d-4567-ab71-f7dc3d2d1f0a"}}, {"head": {"id": "f6faa493-bf7f-42a5-81ec-87fcbc758879", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801758841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c25f940e-4cb6-453c-a39c-8dc08d865ab6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801758945500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b8618e8-84fa-45ab-aa93-05eae596296f", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759093700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b058ffa2-7437-455c-b583-626377ae772c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759414700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a92f567a-2dfd-4ca4-9616-0a1cb691de34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70f504d-f1fe-459b-8cb3-9c7c9e595ec9", "name": "entry : default@ConfigureCmake cost memory 0.0359954833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f33e0c-83c9-4930-97b7-99e57e6ed8d4", "name": "runTaskFromQueue task cost before running: 407 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759646900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53bf82b-9676-4382-ae10-9e5b9d1c4ff2", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801759086000, "endTime": 33801759726200, "totalTime": 531600}, "additional": {"logType": "info", "children": [], "durationId": "66722f3b-e14d-4567-ab71-f7dc3d2d1f0a"}}, {"head": {"id": "1442e83b-faf7-4aa2-a01f-df9e2ae6bae9", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801763983800, "endTime": 33801766675800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3cf8c53d-5d65-413a-9b3d-2b159cc9fa25", "logId": "347aa3fc-d868-45e3-b4f9-4dd1c0997913"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cf8c53d-5d65-413a-9b3d-2b159cc9fa25", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801762015700}, "additional": {"logType": "detail", "children": [], "durationId": "1442e83b-faf7-4aa2-a01f-df9e2ae6bae9"}}, {"head": {"id": "8c976457-7a72-404b-abb3-8f622e1bfb49", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801762445900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb456c5-8211-4769-9ac7-4aaef43a43ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801762557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd368d4f-4e37-4ca5-b4dd-5d82d6a8d3b3", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801763996200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b1d8f1a-3888-49f8-a1ff-57d756652099", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801766420600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4facb398-a6ae-4086-ae66-4fcd92a42c4f", "name": "entry : default@MergeProfile cost memory 0.10503387451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801766577500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347aa3fc-d868-45e3-b4f9-4dd1c0997913", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801763983800, "endTime": 33801766675800}, "additional": {"logType": "info", "children": [], "durationId": "1442e83b-faf7-4aa2-a01f-df9e2ae6bae9"}}, {"head": {"id": "d491f557-7579-41d6-bb73-455a1e5b182d", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801769955900, "endTime": 33801772249100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bc26abe2-1ac2-42bd-8cff-d1ddd13abb0c", "logId": "c2a8ced3-7613-4239-84fc-f6b365f5ac23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc26abe2-1ac2-42bd-8cff-d1ddd13abb0c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801768535400}, "additional": {"logType": "detail", "children": [], "durationId": "d491f557-7579-41d6-bb73-455a1e5b182d"}}, {"head": {"id": "20fa0473-6fba-4a2f-8950-27803e942a21", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801769014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6e1ee6a-174c-41e7-9c6d-7a9676f6fbed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801769157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239663fe-9527-4ecb-b814-d914aecbe1b9", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801769969900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53dedb54-4289-492b-9504-6db961307365", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801771033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdcba10-6ecd-451f-b21b-72c2aa7c727b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801772082800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a8ce72-048b-44a1-a746-a9d236250487", "name": "entry : default@CreateBuildProfile cost memory 0.10097503662109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801772188000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a8ced3-7613-4239-84fc-f6b365f5ac23", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801769955900, "endTime": 33801772249100}, "additional": {"logType": "info", "children": [], "durationId": "d491f557-7579-41d6-bb73-455a1e5b182d"}}, {"head": {"id": "76f8c825-12e4-486f-97fd-e21ec2b1ea27", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801775576300, "endTime": 33801776376800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3235ad10-de2d-4266-8278-213704306998", "logId": "cd189449-182e-4b79-8d95-1af971aa6460"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3235ad10-de2d-4266-8278-213704306998", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801774108100}, "additional": {"logType": "detail", "children": [], "durationId": "76f8c825-12e4-486f-97fd-e21ec2b1ea27"}}, {"head": {"id": "6edec3c6-67a0-4f83-a9b1-66964c4588ad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801774453100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0800aa0-113b-422f-9ac5-b0b808b3b881", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801774554800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad3514c6-dd9c-4598-96ef-79c3e73d82b1", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801775590100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9568d0d3-b2a6-41d7-8c7c-7747867c7f6a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801775835600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a8a5bbf-c6e3-4476-b3f9-82be0f4e1f38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801775910100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8067b924-2c73-4477-a349-3b722a2fb610", "name": "entry : default@PreCheckSyscap cost memory 0.03621673583984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801776016800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e608575-7b6f-4928-b501-95535546c05c", "name": "runTaskFromQueue task cost before running: 424 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801776205800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd189449-182e-4b79-8d95-1af971aa6460", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801775576300, "endTime": 33801776376800, "totalTime": 516000}, "additional": {"logType": "info", "children": [], "durationId": "76f8c825-12e4-486f-97fd-e21ec2b1ea27"}}, {"head": {"id": "d7b39a0e-ff52-4a0d-ad72-7090adee007f", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801786785100, "endTime": 33801787451200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "65b82184-bf5d-4306-b1a7-f194953c66ea", "logId": "2d5e008c-b945-4cac-9a0d-4ff4bbebb344"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65b82184-bf5d-4306-b1a7-f194953c66ea", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801779404300}, "additional": {"logType": "detail", "children": [], "durationId": "d7b39a0e-ff52-4a0d-ad72-7090adee007f"}}, {"head": {"id": "2101e88a-8eb3-4048-bb67-67e4899068f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801779796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a129a6f1-00ef-4c81-92d5-bb0f6e20101e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801779895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ef0550-2f76-465a-98a4-11c729f5d364", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801786798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b2c3fc-d6b7-4a04-8377-468c930253c3", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801787085000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adee8948-cf69-4d9c-8553-b1527fa492be", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03850555419921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801787313600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2baa747a-f8e1-4a71-9022-66334a09a80d", "name": "runTaskFromQueue task cost before running: 435 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801787397500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5e008c-b945-4cac-9a0d-4ff4bbebb344", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801786785100, "endTime": 33801787451200, "totalTime": 596500}, "additional": {"logType": "info", "children": [], "durationId": "d7b39a0e-ff52-4a0d-ad72-7090adee007f"}}, {"head": {"id": "a50d123d-9d3f-4115-abc1-7023112365e6", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801793903500, "endTime": 33801796507300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "217f49ff-d2d9-4f4d-8a22-b0e75948d5a0", "logId": "e247085a-f247-47e4-9f34-6f001617b72d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "217f49ff-d2d9-4f4d-8a22-b0e75948d5a0", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801790258400}, "additional": {"logType": "detail", "children": [], "durationId": "a50d123d-9d3f-4115-abc1-7023112365e6"}}, {"head": {"id": "e967145b-85b4-49a0-9675-c202c26d2a56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801790723600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6790c7c9-4712-411a-b9d4-3fa5569087e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801790861500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994d8950-3501-4fe4-9623-9ae6dafd52bd", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801793915900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90876de6-09de-4b5a-a524-5925893ff21b", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801795673300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6518b2de-a62c-4ac0-953f-4d46e03448d0", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801795939400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d33f5236-2a93-431c-8e63-e084709d1834", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801796056500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05d0841a-d4cb-450f-8dfc-fbee31344f85", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801796226600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d6151a5-4e31-49c3-b2ec-8f526e36019d", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1177825927734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801796372800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24de0eb8-2d83-45c6-a699-6e0e45948c7a", "name": "runTaskFromQueue task cost before running: 444 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801796448300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e247085a-f247-47e4-9f34-6f001617b72d", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801793903500, "endTime": 33801796507300, "totalTime": 2528900}, "additional": {"logType": "info", "children": [], "durationId": "a50d123d-9d3f-4115-abc1-7023112365e6"}}, {"head": {"id": "e4c97a12-8795-495d-9c9f-9a9af8e80537", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801304400, "endTime": 33801801782900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "dd996beb-d51c-45b6-a2e8-b256d718a973", "logId": "d87578c6-97bf-417d-922a-b81ec17562cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd996beb-d51c-45b6-a2e8-b256d718a973", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801799115700}, "additional": {"logType": "detail", "children": [], "durationId": "e4c97a12-8795-495d-9c9f-9a9af8e80537"}}, {"head": {"id": "600d5750-9b36-4e10-9e3c-f2a7707b1df4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801800023100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53125ee7-4b43-4a0d-b9d5-575e0bd95f1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801800166900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "718e2ad5-7f57-4687-9862-32a56ac0127a", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801321100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c87a184-42e9-4966-894c-19a6ab25334e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801477200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1456fb7-b1ca-4ca5-9b78-2a4f7278e7ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801537300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53de2bf-99b7-4f1c-bb15-907adad7e64b", "name": "entry : default@BuildNativeWithCmake cost memory 0.03704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801614900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "817e0baf-5451-49a5-87ee-cd0a2ca3d9a3", "name": "runTaskFromQueue task cost before running: 449 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801726200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87578c6-97bf-417d-922a-b81ec17562cc", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801801304400, "endTime": 33801801782900, "totalTime": 401200}, "additional": {"logType": "info", "children": [], "durationId": "e4c97a12-8795-495d-9c9f-9a9af8e80537"}}, {"head": {"id": "b851c909-4534-433b-99e6-1b4cd9041216", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801806039100, "endTime": 33801814525700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb951ce2-5694-4f62-9d57-331652a5b214", "logId": "934eea8d-0741-4a3c-aad0-f4d4944168d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb951ce2-5694-4f62-9d57-331652a5b214", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801803922500}, "additional": {"logType": "detail", "children": [], "durationId": "b851c909-4534-433b-99e6-1b4cd9041216"}}, {"head": {"id": "c3560c2b-2086-4e1a-a681-61aaa5935052", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801804881500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8c3f0d-240d-4897-8112-f5db3fdb32c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801805011300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55488ff3-eb2d-4e06-b9ce-38a3b96b97fa", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801806053700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13646548-cd43-4142-b226-17565562f425", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801814323700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5009e5ec-56d0-435d-a235-fccd96691d2e", "name": "entry : default@MakePackInfo cost memory 0.13838958740234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801814461600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934eea8d-0741-4a3c-aad0-f4d4944168d2", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801806039100, "endTime": 33801814525700}, "additional": {"logType": "info", "children": [], "durationId": "b851c909-4534-433b-99e6-1b4cd9041216"}}, {"head": {"id": "95a6bed5-6268-4171-93ff-e272a8596fad", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801823903200, "endTime": 33801833763700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "2fd5531d-2131-4cfc-9e91-96728cbbb662", "logId": "fb9f1199-e1d6-4d93-a009-ccb369d13e97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fd5531d-2131-4cfc-9e91-96728cbbb662", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801818921200}, "additional": {"logType": "detail", "children": [], "durationId": "95a6bed5-6268-4171-93ff-e272a8596fad"}}, {"head": {"id": "dd4a4cab-56b7-4102-91f5-d3f4230a2f57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801819665800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f786b9e-1656-4886-94ec-abba54770f59", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801819783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "134c87cf-a800-4605-9a4f-8d7ece6a4d8b", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801823919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5878524c-8614-4baa-ba07-5113366a51e2", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801824266300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5880cd4-7fe4-4df4-82b2-9de219cc0213", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801825913100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e342d75-8617-40ec-8f06-5269a23cf8b2", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801830242100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9cd4740-ce09-4d43-9c03-5bebfe84c14f", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801830843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a0f3fb-9666-4a2c-aa74-1e064bb30b38", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801830970400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d958d92-acc9-4144-8d09-02937f90dc57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801831033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "569c6d4d-1fde-4df6-89c5-d983282520de", "name": "entry : default@SyscapTransform cost memory 0.15235137939453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801831171400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69adcf73-a919-4472-a129-9eb584a1642e", "name": "runTaskFromQueue task cost before running: 479 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801831777100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb9f1199-e1d6-4d93-a009-ccb369d13e97", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801823903200, "endTime": 33801833763700, "totalTime": 7734000}, "additional": {"logType": "info", "children": [], "durationId": "95a6bed5-6268-4171-93ff-e272a8596fad"}}, {"head": {"id": "da4e7b40-834e-44f8-bd85-26809a770600", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801841793900, "endTime": 33801844026600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "71cbd784-546a-4bce-b163-52d5c537ffa7", "logId": "232d3739-db16-4c60-aa35-0a6deee4e0a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71cbd784-546a-4bce-b163-52d5c537ffa7", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801838258600}, "additional": {"logType": "detail", "children": [], "durationId": "da4e7b40-834e-44f8-bd85-26809a770600"}}, {"head": {"id": "5616d61b-586c-4276-a132-813393d72ac6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801838986300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "433e9968-072b-43cf-82fc-731b3dba970d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801839658200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148dd393-4236-4f3f-9192-823b230e9140", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801841808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03efa683-5d5b-4a45-ad23-1efcc275f482", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801843822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e942c4-70d5-4421-aea0-3131a99b80b0", "name": "entry : default@ProcessProfile cost memory 0.05950164794921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801843958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232d3739-db16-4c60-aa35-0a6deee4e0a3", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801841793900, "endTime": 33801844026600}, "additional": {"logType": "info", "children": [], "durationId": "da4e7b40-834e-44f8-bd85-26809a770600"}}, {"head": {"id": "63663928-e303-41fb-9e9d-b0cd95acd53c", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801851722500, "endTime": 33801858779700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0816f847-44ef-4a87-9694-ebb96f77a1fb", "logId": "a2c0278f-004f-423a-a382-58ad2273b7d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0816f847-44ef-4a87-9694-ebb96f77a1fb", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801847645100}, "additional": {"logType": "detail", "children": [], "durationId": "63663928-e303-41fb-9e9d-b0cd95acd53c"}}, {"head": {"id": "ef5f7f8a-e575-4a2b-8a10-bc0dda462529", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801848068800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e48257-c548-4742-baa0-d4c4367b551b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801848192300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f10c16-a16e-4b25-a7b9-fa8bdea3794e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801851734800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b023652-3120-494b-9434-8b16b732629d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801858276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413adea1-8b5f-4824-bd46-aba0f247bc9c", "name": "entry : default@ProcessRouterMap cost memory 0.20148468017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801858494300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c0278f-004f-423a-a382-58ad2273b7d0", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801851722500, "endTime": 33801858779700}, "additional": {"logType": "info", "children": [], "durationId": "63663928-e303-41fb-9e9d-b0cd95acd53c"}}, {"head": {"id": "976723c0-9b8e-4e92-9397-4c3b1716fbad", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801863016200, "endTime": 33801864588700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "32e5004b-7c57-43bd-8d36-7c932fcc5dc2", "logId": "0cd6eff1-d28f-4abd-bcf3-a71819b0ed23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32e5004b-7c57-43bd-8d36-7c932fcc5dc2", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801861685500}, "additional": {"logType": "detail", "children": [], "durationId": "976723c0-9b8e-4e92-9397-4c3b1716fbad"}}, {"head": {"id": "e9768afa-722e-4905-9b44-476e9e2a5536", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801862024700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ebd935e-4375-4be0-8159-2d12aca358c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801862168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60f35d29-febd-419f-a0cc-12f3d34aab90", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801863026700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de3a333-0451-4159-9162-97d80e393b79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801863184300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa6413d7-7546-4161-8859-b13dae6068a7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801863351700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7123f9-6215-41cd-8a0f-79749c39693b", "name": "entry : default@BuildNativeWithNinja cost memory 0.0566558837890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801864328900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5f9b7e-1363-4b24-817a-8eb8d226a5c0", "name": "runTaskFromQueue task cost before running: 512 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801864498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd6eff1-d28f-4abd-bcf3-a71819b0ed23", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801863016200, "endTime": 33801864588700, "totalTime": 1431500}, "additional": {"logType": "info", "children": [], "durationId": "976723c0-9b8e-4e92-9397-4c3b1716fbad"}}, {"head": {"id": "8c16941b-42b6-4ab9-bbd1-577fd48d46b8", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801874571100, "endTime": 33801890456600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bb48544d-2b01-4ef0-ae6d-488f63daf9d5", "logId": "7bb66533-f094-4973-bdd8-f146d7137600"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb48544d-2b01-4ef0-ae6d-488f63daf9d5", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801868512900}, "additional": {"logType": "detail", "children": [], "durationId": "8c16941b-42b6-4ab9-bbd1-577fd48d46b8"}}, {"head": {"id": "751b17c4-d906-47d4-b6c3-81167cc29edf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801868963300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bb03d5-ac26-4a34-b218-d0e5b814c11f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801869077900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a55b080-b08d-4d00-87fe-5d8bfcef584a", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801871392300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c56b382-c378-4190-a5ca-3d77907dd6c6", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801878693500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8945b594-c877-4c98-b57b-d79f722a37b0", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801887049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9910a6fe-8569-4c0b-85c4-742b8743c533", "name": "entry : default@ProcessResource cost memory 0.1693267822265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801887211300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb66533-f094-4973-bdd8-f146d7137600", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801874571100, "endTime": 33801890456600}, "additional": {"logType": "info", "children": [], "durationId": "8c16941b-42b6-4ab9-bbd1-577fd48d46b8"}}, {"head": {"id": "85d77146-d9a8-40c5-8651-894f99a2b48a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801905034400, "endTime": 33801928100100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ac7252d0-696f-4e4a-a501-2f2c51769d06", "logId": "d173cc7b-05df-4849-9633-6378066e580d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac7252d0-696f-4e4a-a501-2f2c51769d06", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801896334700}, "additional": {"logType": "detail", "children": [], "durationId": "85d77146-d9a8-40c5-8651-894f99a2b48a"}}, {"head": {"id": "1cd68c8d-2926-4fb0-9288-3858cc001827", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801896899200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81298129-2a69-4297-ac68-da28a0734cc0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801897014700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2daa37d0-eb53-4be2-8bfb-6f6d374de3f8", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801905053800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0c4b3b-1fc8-4555-a258-56bab121bdb3", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801927896300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28eba029-bf94-4fd2-a82b-31e8493ec68d", "name": "entry : default@GenerateLoaderJson cost memory 0.7675857543945312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801928032300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d173cc7b-05df-4849-9633-6378066e580d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801905034400, "endTime": 33801928100100}, "additional": {"logType": "info", "children": [], "durationId": "85d77146-d9a8-40c5-8651-894f99a2b48a"}}, {"head": {"id": "4e54a313-2acc-4496-935e-cc269a7adf0c", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801943704700, "endTime": 33801947596600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "280dc465-e39d-4713-8e65-f697a29fb0fb", "logId": "9a7dbfbc-f82d-4a5f-904b-47244d510099"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "280dc465-e39d-4713-8e65-f697a29fb0fb", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801937333000}, "additional": {"logType": "detail", "children": [], "durationId": "4e54a313-2acc-4496-935e-cc269a7adf0c"}}, {"head": {"id": "96325ee5-58d6-40f1-a100-bf16b27e2024", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801938479900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a470082e-54ac-451c-992f-9bafd89882fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801938612900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46ca411f-3f7f-41c2-84f8-542cad78a48b", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801943720800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac558485-a3db-4b1e-a187-d14df85df6d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801945930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c394dab-222e-4422-940b-cab3e308eeca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801946037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53503e9-56af-40f8-ab63-fdcd4da8e881", "name": "entry : default@ProcessLibs cost memory 0.12546539306640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801947212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506b3ec0-2dd8-40dd-95ad-d9297760012d", "name": "runTaskFromQueue task cost before running: 595 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801947478900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a7dbfbc-f82d-4a5f-904b-47244d510099", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801943704700, "endTime": 33801947596600, "totalTime": 3735200}, "additional": {"logType": "info", "children": [], "durationId": "4e54a313-2acc-4496-935e-cc269a7adf0c"}}, {"head": {"id": "70c741ca-87da-4d9a-8861-c2f86a1ebd6b", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801961334500, "endTime": 33801999404300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7460f374-52e0-4620-b0a0-89729054a949", "logId": "cd9d079e-e951-4c7c-bb7e-6d866cbde2b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7460f374-52e0-4620-b0a0-89729054a949", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801952695500}, "additional": {"logType": "detail", "children": [], "durationId": "70c741ca-87da-4d9a-8861-c2f86a1ebd6b"}}, {"head": {"id": "f944cfe1-a162-49fb-8e9c-267d7750ef99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801954052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b827b7d0-b30d-4205-902b-28192664e86e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801954238200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a475859f-6820-46e0-bbea-2187498b4569", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801956502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27dd5d16-f346-4a46-997d-7c1d90e1fb52", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801961361100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa9ecb71-04df-42cf-a747-9f53addfd8fd", "name": "Incremental task entry:default@CompileResource pre-execution cost: 36 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801998555300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e65a0a-83de-458d-b5af-a17b53e2823a", "name": "entry : default@CompileResource cost memory 1.406097412109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801999142700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd9d079e-e951-4c7c-bb7e-6d866cbde2b4", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801961334500, "endTime": 33801999404300}, "additional": {"logType": "info", "children": [], "durationId": "70c741ca-87da-4d9a-8861-c2f86a1ebd6b"}}, {"head": {"id": "155b05b1-1bc1-4cd5-b114-7ad97289f2fe", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802006406400, "endTime": 33802007990700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "becb8f76-d162-472c-acdd-e1c243d09b15", "logId": "2e4c67df-ed56-4ae9-b216-ef70663ffd65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "becb8f76-d162-472c-acdd-e1c243d09b15", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802003085100}, "additional": {"logType": "detail", "children": [], "durationId": "155b05b1-1bc1-4cd5-b114-7ad97289f2fe"}}, {"head": {"id": "ad003b0a-9d2b-4b24-ae03-c660fd9950d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802003549600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f10d2c1e-e550-4df2-85eb-63ce6bbe06c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802003666900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c6257e-6f9f-404b-8d30-e4945fa4809e", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802006418200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2947e60d-fb3f-4208-a234-d6bde2276161", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802006876800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d645fc5b-a623-4ebf-bd5f-8dfb96356c54", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802007823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0067d43-2290-4d8b-a3c5-8a2928f5427f", "name": "entry : default@DoNativeStrip cost memory 0.07431793212890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802007925000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e4c67df-ed56-4ae9-b216-ef70663ffd65", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802006406400, "endTime": 33802007990700}, "additional": {"logType": "info", "children": [], "durationId": "155b05b1-1bc1-4cd5-b114-7ad97289f2fe"}}, {"head": {"id": "b0452905-19a8-4c10-a7a3-fe1cfaa08245", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802014685700, "endTime": 33803538368900}, "additional": {"children": ["80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "d6f9d3bc-423b-4a66-90a2-a9564dbd9ad2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "81393a8f-e928-4a82-89df-34d2f8d04681", "logId": "99513462-7b51-46d1-aa90-54fcee743bb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81393a8f-e928-4a82-89df-34d2f8d04681", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802010218600}, "additional": {"logType": "detail", "children": [], "durationId": "b0452905-19a8-4c10-a7a3-fe1cfaa08245"}}, {"head": {"id": "a784edd1-5093-454f-87e8-501f52a23322", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802010645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60789cf-f2a5-4b48-9a1b-e93bdfcb0bc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802010783200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "231463f1-0ee6-4e9f-8ffc-390869a20458", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802014726100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df4639b-00ec-4c0e-8ee4-7398786c397e", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802029855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf68107-8ada-4831-a65e-f258c29449d1", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802029984400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a44278d-9b72-4588-aede-10c8ed415cb5", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802046534400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f0e326-2bf9-471d-b891-5dfcb23ce020", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802046978700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfcca259-eca0-449d-98b1-51b46fd4b2a3", "name": "default@CompileArkTS work[106] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802048573100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33802164889100, "endTime": 33803532155100}, "additional": {"children": ["c4a4c572-2555-4029-b6bb-77533650e626", "48569661-a319-428e-aaee-a9b3364d7ca5", "f6580b5a-6522-463b-ad8a-f980c0d04c79", "51c253a9-8128-4352-8fcd-f72652f0e735", "b208e5d9-baa3-4719-bebe-4d4352756519", "1dfdfa49-90b1-4122-ba61-490daf1b712b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b0452905-19a8-4c10-a7a3-fe1cfaa08245", "logId": "d7e5baec-1014-4302-8745-f31552db0764"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9045ee7a-ef7e-47ae-a2c0-f64c32090874", "name": "default@CompileArkTS work[106] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802050043000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f89d6e4-a03b-4229-87bc-a9a500da1166", "name": "default@CompileArkTS work[106] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802050265500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a4bb984-b410-465b-a2c1-7029cf038f6e", "name": "CopyResources startTime: 33802050392200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802050397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad451141-e8a8-41fa-895d-a10f7a67a32a", "name": "default@CompileArkTS work[107] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802050786300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6f9d3bc-423b-4a66-90a2-a9564dbd9ad2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33803184983900, "endTime": 33803196926900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b0452905-19a8-4c10-a7a3-fe1cfaa08245", "logId": "3c5de2f1-3b18-498e-b32c-a6aa1b0cb0d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78fee733-bcb9-461c-b230-bbdc90189df4", "name": "default@CompileArkTS work[107] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802051878800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55443ad-1510-49c9-a0f3-9701041a434c", "name": "default@CompileArkTS work[107] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802051972900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8afb4c-234e-41db-9e89-cc0cdbe4c1c0", "name": "entry : default@CompileArkTS cost memory 1.5851593017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802052071200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfc99480-6a9f-45e0-b89e-e3904952cbfb", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802057942800, "endTime": 33802060633200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "78b74efd-7989-4fea-97d9-90df9bfc5ab6", "logId": "2e02b17b-af45-4c4a-9250-8c9ab227a68f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78b74efd-7989-4fea-97d9-90df9bfc5ab6", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802053531100}, "additional": {"logType": "detail", "children": [], "durationId": "bfc99480-6a9f-45e0-b89e-e3904952cbfb"}}, {"head": {"id": "09b1a1bc-c455-4527-8cd8-2d9e46e33304", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802053984600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c068b0f-6fbc-4deb-801c-dc2903956d4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802054083000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c56cd2-920a-42a2-9c84-55ca642ec988", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802057954000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fbcd02b-c88e-437b-b975-2cd905c1c9ab", "name": "entry : default@BuildJS cost memory 0.126495361328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802060447600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34da348-745c-42f8-bd94-98a85e64eb26", "name": "runTaskFromQueue task cost before running: 708 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802060571400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e02b17b-af45-4c4a-9250-8c9ab227a68f", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802057942800, "endTime": 33802060633200, "totalTime": 2609100}, "additional": {"logType": "info", "children": [], "durationId": "bfc99480-6a9f-45e0-b89e-e3904952cbfb"}}, {"head": {"id": "99c94d3e-c883-4510-9949-49840378f818", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802065948500, "endTime": 33802069449000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "33dcdb16-0ff6-42e8-add2-dec1fde042e4", "logId": "b2e09e47-21d2-4ade-848c-72de4be3097a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33dcdb16-0ff6-42e8-add2-dec1fde042e4", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802062403700}, "additional": {"logType": "detail", "children": [], "durationId": "99c94d3e-c883-4510-9949-49840378f818"}}, {"head": {"id": "08620920-2728-4ed8-a366-7169bda5ce3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802062775000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c67a12-fb3d-4265-aaf8-9993220a98d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802062871900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16befb27-c2c9-4e54-939c-75b2fe0df4e6", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802065960700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d6be76-2089-428a-af00-e3fa95426eef", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802066509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a32ef4-8d32-4776-bda7-5a814aa75ba2", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802069194900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec5add6-de3c-4491-8e0a-e6578ecb8e83", "name": "entry : default@CacheNativeLibs cost memory 0.088287353515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802069354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2e09e47-21d2-4ade-848c-72de4be3097a", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802065948500, "endTime": 33802069449000}, "additional": {"logType": "info", "children": [], "durationId": "99c94d3e-c883-4510-9949-49840378f818"}}, {"head": {"id": "7d047fe0-7c6c-4a00-bdd9-2c5e72ae65c9", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802164175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "006fdb54-7786-45d3-a5d7-55f0ced0725d", "name": "default@CompileArkTS work[106] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802164697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "801f69f1-c545-4e28-841f-7399966cca6e", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802164831700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbcb1e7f-8d48-4688-9039-a83980ef1822", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802164899200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afef1422-3268-4b71-8aa4-9cc8d7a93503", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802164958200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8ffe010-284f-4cd1-a35c-6ef4e63d68f5", "name": "default@CompileArkTS work[107] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802166241500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d376d0-228b-4246-9076-532ec49e31f3", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803197085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a6c7e7-feb4-4e4a-aa46-3313e5accb57", "name": "CopyResources is end, endTime: 33803197387900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803197394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c136806-52b0-41de-a38e-cdccfcfe815b", "name": "default@CompileArkTS work[107] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803197472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c5de2f1-3b18-498e-b32c-a6aa1b0cb0d0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33803184983900, "endTime": 33803196926900}, "additional": {"logType": "info", "children": [], "durationId": "d6f9d3bc-423b-4a66-90a2-a9564dbd9ad2", "parent": "99513462-7b51-46d1-aa90-54fcee743bb4"}}, {"head": {"id": "600f2e6c-0067-42d1-b4fe-6a62bf4310bb", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803197545500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a16885e7-7564-41e5-a22d-5e2c04a6538e", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803532676200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a4c572-2555-4029-b6bb-77533650e626", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33802164971100, "endTime": 33802168892900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "logId": "e7737369-aa8c-4405-8a48-947444d7c60f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7737369-aa8c-4405-8a48-947444d7c60f", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802164971100, "endTime": 33802168892900}, "additional": {"logType": "info", "children": [], "durationId": "c4a4c572-2555-4029-b6bb-77533650e626", "parent": "d7e5baec-1014-4302-8745-f31552db0764"}}, {"head": {"id": "48569661-a319-428e-aaee-a9b3364d7ca5", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33802168908800, "endTime": 33802169010100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "logId": "2e648980-54f7-46f9-b951-3d61222d59ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e648980-54f7-46f9-b951-3d61222d59ba", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802168908800, "endTime": 33802169010100}, "additional": {"logType": "info", "children": [], "durationId": "48569661-a319-428e-aaee-a9b3364d7ca5", "parent": "d7e5baec-1014-4302-8745-f31552db0764"}}, {"head": {"id": "f6580b5a-6522-463b-ad8a-f980c0d04c79", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33802169022400, "endTime": 33802169055000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "logId": "0d7f93c2-10ff-4a27-80e1-3c3a09cbe27d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d7f93c2-10ff-4a27-80e1-3c3a09cbe27d", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802169022400, "endTime": 33802169055000}, "additional": {"logType": "info", "children": [], "durationId": "f6580b5a-6522-463b-ad8a-f980c0d04c79", "parent": "d7e5baec-1014-4302-8745-f31552db0764"}}, {"head": {"id": "51c253a9-8128-4352-8fcd-f72652f0e735", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33802169068500, "endTime": 33803441269100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "logId": "ecf8aa37-d191-4fe2-b274-353ecb55837b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecf8aa37-d191-4fe2-b274-353ecb55837b", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802169068500, "endTime": 33803441269100}, "additional": {"logType": "info", "children": [], "durationId": "51c253a9-8128-4352-8fcd-f72652f0e735", "parent": "d7e5baec-1014-4302-8745-f31552db0764"}}, {"head": {"id": "b208e5d9-baa3-4719-bebe-4d4352756519", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803441287700, "endTime": 33803445856400}, "additional": {"children": ["003021b3-e499-423f-83af-f6278bf34339", "d88147ec-43b9-437a-83cc-e76eaa9a4099", "fc04996f-095b-4d2e-8065-b7f751dcafc4"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "logId": "19b908e9-ba17-4cb5-8604-58091fc01e98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19b908e9-ba17-4cb5-8604-58091fc01e98", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803441287700, "endTime": 33803445856400}, "additional": {"logType": "info", "children": ["ff961d9c-fb64-4cd1-85f7-e164ce895e1c", "ece1fabe-52fc-419d-9ab4-2db3e4faf996", "fee24ad0-6758-4129-8104-5cdf1673d8ae"], "durationId": "b208e5d9-baa3-4719-bebe-4d4352756519", "parent": "d7e5baec-1014-4302-8745-f31552db0764"}}, {"head": {"id": "003021b3-e499-423f-83af-f6278bf34339", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803441306600, "endTime": 33803441312700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b208e5d9-baa3-4719-bebe-4d4352756519", "logId": "ff961d9c-fb64-4cd1-85f7-e164ce895e1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff961d9c-fb64-4cd1-85f7-e164ce895e1c", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803441306600, "endTime": 33803441312700}, "additional": {"logType": "info", "children": [], "durationId": "003021b3-e499-423f-83af-f6278bf34339", "parent": "19b908e9-ba17-4cb5-8604-58091fc01e98"}}, {"head": {"id": "d88147ec-43b9-437a-83cc-e76eaa9a4099", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803441316000, "endTime": 33803442717100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b208e5d9-baa3-4719-bebe-4d4352756519", "logId": "ece1fabe-52fc-419d-9ab4-2db3e4faf996"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ece1fabe-52fc-419d-9ab4-2db3e4faf996", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803441316000, "endTime": 33803442717100}, "additional": {"logType": "info", "children": [], "durationId": "d88147ec-43b9-437a-83cc-e76eaa9a4099", "parent": "19b908e9-ba17-4cb5-8604-58091fc01e98"}}, {"head": {"id": "fc04996f-095b-4d2e-8065-b7f751dcafc4", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803442721000, "endTime": 33803445843300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b208e5d9-baa3-4719-bebe-4d4352756519", "logId": "fee24ad0-6758-4129-8104-5cdf1673d8ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fee24ad0-6758-4129-8104-5cdf1673d8ae", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803442721000, "endTime": 33803445843300}, "additional": {"logType": "info", "children": [], "durationId": "fc04996f-095b-4d2e-8065-b7f751dcafc4", "parent": "19b908e9-ba17-4cb5-8604-58091fc01e98"}}, {"head": {"id": "1dfdfa49-90b1-4122-ba61-490daf1b712b", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803445871000, "endTime": 33803531990500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "logId": "5804a58f-84d7-4f9a-b14b-e9c5125b2155"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5804a58f-84d7-4f9a-b14b-e9c5125b2155", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803445871000, "endTime": 33803531990500}, "additional": {"logType": "info", "children": [], "durationId": "1dfdfa49-90b1-4122-ba61-490daf1b712b", "parent": "d7e5baec-1014-4302-8745-f31552db0764"}}, {"head": {"id": "5c571fce-2bd5-4e4c-be49-e92a2f54cf07", "name": "default@CompileArkTS work[106] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803538135300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e5baec-1014-4302-8745-f31552db0764", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33802164889100, "endTime": 33803532155100}, "additional": {"logType": "info", "children": ["e7737369-aa8c-4405-8a48-947444d7c60f", "2e648980-54f7-46f9-b951-3d61222d59ba", "0d7f93c2-10ff-4a27-80e1-3c3a09cbe27d", "ecf8aa37-d191-4fe2-b274-353ecb55837b", "19b908e9-ba17-4cb5-8604-58091fc01e98", "5804a58f-84d7-4f9a-b14b-e9c5125b2155"], "durationId": "80cd77d3-fe21-4b41-8c1d-32b17a6b3b34", "parent": "99513462-7b51-46d1-aa90-54fcee743bb4"}}, {"head": {"id": "207e481c-404a-4531-9244-6869fe846812", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803538271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99513462-7b51-46d1-aa90-54fcee743bb4", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33802014685700, "endTime": 33803538368900, "totalTime": 1404806400}, "additional": {"logType": "info", "children": ["d7e5baec-1014-4302-8745-f31552db0764", "3c5de2f1-3b18-498e-b32c-a6aa1b0cb0d0"], "durationId": "b0452905-19a8-4c10-a7a3-fe1cfaa08245"}}, {"head": {"id": "c1a81f0a-2f04-4f8e-9b74-a7bd27d6a428", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803543037800, "endTime": 33803544545000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "4258bc4a-381e-44c0-ae77-05b926aaf0ff", "logId": "e4c8d7da-ae34-472a-9e44-4b1d8d09ccc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4258bc4a-381e-44c0-ae77-05b926aaf0ff", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803541781500}, "additional": {"logType": "detail", "children": [], "durationId": "c1a81f0a-2f04-4f8e-9b74-a7bd27d6a428"}}, {"head": {"id": "9b8bf496-fb8f-4e92-8a0a-ff95694685c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803542125500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8b03a4b-1f7c-46cd-b73b-5acc9805ee7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803542217800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1be646-693a-4ae7-83e7-f38cf8dac295", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803543046100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9713bd3f-c85f-4080-a9c6-16e8dc0b8010", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803543341900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ddbb46-eaef-403b-9e9e-616646698003", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803544364900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e872e3a1-2ec0-4b62-8472-aab7fc5d23cd", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07047271728515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803544481300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c8d7da-ae34-472a-9e44-4b1d8d09ccc2", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803543037800, "endTime": 33803544545000}, "additional": {"logType": "info", "children": [], "durationId": "c1a81f0a-2f04-4f8e-9b74-a7bd27d6a428"}}, {"head": {"id": "d9e584b9-e15a-465d-ab66-051e183341ac", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803553891800, "endTime": 33803989501300}, "additional": {"children": ["3e4a202d-ca2b-4aa6-9891-c836a745ff2d", "70ab8be1-2d3b-4a9c-807d-d0f2089405f0", "2f4697f2-b8cf-4a27-bdcb-8917f39a7ba6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "3ceaf0bf-b2e9-462d-a65f-ca08503f5603", "logId": "81f96619-7b46-416c-a737-0a73f69836b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ceaf0bf-b2e9-462d-a65f-ca08503f5603", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803546710900}, "additional": {"logType": "detail", "children": [], "durationId": "d9e584b9-e15a-465d-ab66-051e183341ac"}}, {"head": {"id": "4c879b92-efda-4de6-97d6-cb8ed2632f74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803547330200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c072c2-2633-4f6d-bf61-f0063732261d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803547445400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a195da1-5e6f-4519-a224-6287fd594cb9", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803553901900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b994085-bbca-40f8-b4bb-e162d881c725", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803566807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216cfa20-0a02-4d1a-a3bb-68d835d14227", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803566963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4941a2e-30c6-40a8-bef2-13b9faa77ef3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803567059200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7a965c-df5f-4499-a4e2-c5b005e16976", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803567178800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4a202d-ca2b-4aa6-9891-c836a745ff2d", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803568012100, "endTime": 33803569202400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9e584b9-e15a-465d-ab66-051e183341ac", "logId": "a8478c9b-50de-4ff4-bd8d-6dd414d66253"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24c2e4b3-39d5-494a-98b5-6bc6af365f1f", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803569073000}, "additional": {"logType": "debug", "children": [], "durationId": "d9e584b9-e15a-465d-ab66-051e183341ac"}}, {"head": {"id": "a8478c9b-50de-4ff4-bd8d-6dd414d66253", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803568012100, "endTime": 33803569202400}, "additional": {"logType": "info", "children": [], "durationId": "3e4a202d-ca2b-4aa6-9891-c836a745ff2d", "parent": "81f96619-7b46-416c-a737-0a73f69836b2"}}, {"head": {"id": "70ab8be1-2d3b-4a9c-807d-d0f2089405f0", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803569831500, "endTime": 33803571185200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9e584b9-e15a-465d-ab66-051e183341ac", "logId": "35ec9ee8-2a4a-40ca-93a2-0fb03601a9cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26fdae5b-704e-440d-8e29-b06f25a07f26", "name": "default@PackageHap work[108] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803570367700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f4697f2-b8cf-4a27-bdcb-8917f39a7ba6", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803571166300, "endTime": 33803989044100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d9e584b9-e15a-465d-ab66-051e183341ac", "logId": "4079129d-319b-4a2d-b99b-a04a87f211b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27bdce82-9e63-4121-ac54-9059d25299d6", "name": "default@PackageHap work[108] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803570937200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cca8d1e-9d66-43f1-9600-19211d4dcd9f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803571004000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ee60c8a-0264-4c9b-9849-23afd251dc9c", "name": "default@PackageHap work[108] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803571088800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe1ac55-9578-4f9e-bfe7-f22f667598e2", "name": "default@PackageHap work[108] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803571143400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35ec9ee8-2a4a-40ca-93a2-0fb03601a9cc", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803569831500, "endTime": 33803571185200}, "additional": {"logType": "info", "children": [], "durationId": "70ab8be1-2d3b-4a9c-807d-d0f2089405f0", "parent": "81f96619-7b46-416c-a737-0a73f69836b2"}}, {"head": {"id": "33ea1718-4cfc-4b28-a78c-af63b3cbfc41", "name": "entry : default@PackageHap cost memory 1.265167236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803575320900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d700b15-8bcd-47d9-85ba-11395876c838", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803989170100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6f2db2-c46d-498c-8e51-a6adbf060bd0", "name": "default@PackageHap work[108] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803989358700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4079129d-319b-4a2d-b99b-a04a87f211b8", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33803571166300, "endTime": 33803989044100}, "additional": {"logType": "info", "children": [], "durationId": "2f4697f2-b8cf-4a27-bdcb-8917f39a7ba6", "parent": "81f96619-7b46-416c-a737-0a73f69836b2"}}, {"head": {"id": "4041db89-fca5-4772-9f46-942307983eaa", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803989445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f96619-7b46-416c-a737-0a73f69836b2", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803553891800, "endTime": 33803989501300, "totalTime": 435153800}, "additional": {"logType": "info", "children": ["a8478c9b-50de-4ff4-bd8d-6dd414d66253", "35ec9ee8-2a4a-40ca-93a2-0fb03601a9cc", "4079129d-319b-4a2d-b99b-a04a87f211b8"], "durationId": "d9e584b9-e15a-465d-ab66-051e183341ac"}}, {"head": {"id": "4a1773cc-04dc-4503-84a6-599289eb759a", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803995904500, "endTime": 33803997536000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "0b86a4c8-366e-44fc-b019-836c3a64c9f5", "logId": "fc5b02dc-6629-4031-85cb-617b5a58fe78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b86a4c8-366e-44fc-b019-836c3a64c9f5", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803992562100}, "additional": {"logType": "detail", "children": [], "durationId": "4a1773cc-04dc-4503-84a6-599289eb759a"}}, {"head": {"id": "e9418cfe-5c8f-44ea-9896-9efebb36b53a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803992981500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed82109-fda5-4e5f-aebf-1c75aed8195e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803993085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddcdcc84-b991-4021-9ec2-9ea7050697f9", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803995913700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3fcd6e8-35ac-443f-b8dd-ea2ef5211aaa", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803996209600}, "additional": {"logType": "warn", "children": [], "durationId": "4a1773cc-04dc-4503-84a6-599289eb759a"}}, {"head": {"id": "73c7d15b-b32e-43b5-ab2f-2732444f69cd", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803996743700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd9fe9ad-863e-46b6-99fe-33e8a2b9ef0a", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803996831200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67497e68-f73f-4ef2-8d25-a743829dc8d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803996904100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7dda13a-40d1-47c4-9a18-9f91eea251d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803996959600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d42392-a409-4e2c-9044-cd47cb831b9a", "name": "entry : default@SignHap cost memory 0.11434173583984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803997257800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4959b02-21f7-4538-9d85-b0f23b7c7369", "name": "runTaskFromQueue task cost before running: 2 s 645 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803997451800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5b02dc-6629-4031-85cb-617b5a58fe78", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803995904500, "endTime": 33803997536000, "totalTime": 1482800}, "additional": {"logType": "info", "children": [], "durationId": "4a1773cc-04dc-4503-84a6-599289eb759a"}}, {"head": {"id": "363e5c0b-09b1-45d1-aa4c-28930430bc98", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804000711400, "endTime": 33804005298700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b6ad03b5-d035-4d07-8771-2d4bdacb4ad2", "logId": "98214035-7c2c-49cc-bbb7-57ebc3bf8c83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6ad03b5-d035-4d07-8771-2d4bdacb4ad2", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803999418300}, "additional": {"logType": "detail", "children": [], "durationId": "363e5c0b-09b1-45d1-aa4c-28930430bc98"}}, {"head": {"id": "4beccc2d-eba8-4ab9-95fc-d3dcabba55ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33803999901000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0865fbca-dbac-47ec-926b-f96a907ba16f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804000006100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46078b11-3a36-4f94-9ca6-56332e8c47c9", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804000721300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a06d789-46c6-472a-88a4-9407687246cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804004943700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a95470-46c2-48cb-8286-f6b2485f3bd6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804005040200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0a335b-6520-4a12-aefd-cc4a5519b087", "name": "entry : default@CollectDebugSymbol cost memory 0.23859405517578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804005142400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1781dd59-505a-4d7d-adc7-235f00df2924", "name": "runTaskFromQueue task cost before running: 2 s 653 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804005243200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98214035-7c2c-49cc-bbb7-57ebc3bf8c83", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804000711400, "endTime": 33804005298700, "totalTime": 4508800}, "additional": {"logType": "info", "children": [], "durationId": "363e5c0b-09b1-45d1-aa4c-28930430bc98"}}, {"head": {"id": "03a861e1-54df-4082-a2b0-25cba0b0d3c0", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804006783700, "endTime": 33804007027700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e08758ca-5415-439d-b371-9b8274933243", "logId": "74b67e94-dbe3-47c3-84db-77e9503f8a6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e08758ca-5415-439d-b371-9b8274933243", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804006749000}, "additional": {"logType": "detail", "children": [], "durationId": "03a861e1-54df-4082-a2b0-25cba0b0d3c0"}}, {"head": {"id": "a02c69b2-9eb3-43d9-badd-265efe749a9d", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804006789300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11000f46-d097-4404-b5ff-6752d9d3b501", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804006909900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a58d6e5-81b3-496e-9678-5e70746b74fa", "name": "runTaskFromQueue task cost before running: 2 s 654 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804006979600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74b67e94-dbe3-47c3-84db-77e9503f8a6d", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804006783700, "endTime": 33804007027700, "totalTime": 178500}, "additional": {"logType": "info", "children": [], "durationId": "03a861e1-54df-4082-a2b0-25cba0b0d3c0"}}, {"head": {"id": "e0e99326-e2ee-40fa-8967-a56fd56257b7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804014532300, "endTime": 33804014552500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70cbbaae-51b6-4610-a499-615e96cb5652", "logId": "ed6e9f2f-1043-4f0f-ae3d-1cd41d801155"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed6e9f2f-1043-4f0f-ae3d-1cd41d801155", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804014532300, "endTime": 33804014552500}, "additional": {"logType": "info", "children": [], "durationId": "e0e99326-e2ee-40fa-8967-a56fd56257b7"}}, {"head": {"id": "c1bffaa6-ccea-470c-9ea3-3a3327acc98b", "name": "BUILD SUCCESSFUL in 2 s 662 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804014590600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "5ab46159-9d8a-4c35-834f-f67751135417", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33801352999800, "endTime": 33804014867600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 52}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ce51b157-b1a7-4208-a554-70b69240e45e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804014885800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ef52a4-1a26-446f-97b7-d001f371b8d1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804014941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04548d24-869b-475e-a28c-d9f816b376ad", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804014981900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e60d825-ebb9-4eda-ad93-5eae8b054af3", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804016241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa233a6-2c73-4466-8b50-eb5483135607", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804016389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90238c5f-6c21-4970-9558-73a514fdc223", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804016696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dedac6e8-97fd-447a-8e53-d67d30baa851", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804017269100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b3b258-991e-4414-82cc-df52d1f5be9c", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804017504000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9adaaef-472c-4588-bffa-4755e28d4e4f", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804017570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24282650-80e0-4465-9736-f9eb0e20bd5c", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804017701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ccec6e1-420f-4191-abd4-ca3ba9501d82", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804017988800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d035d17-04b5-47a0-b71f-6be78ac31a98", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804018897700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bca887f-8e31-4705-a851-9b4d5407a942", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019161700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "157b7ae0-8481-4d88-a7cd-15c74f5aca02", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019238800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33913dd1-ec93-4d7c-91a3-f1d3b63c8abd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019293200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "450eaef1-9341-4495-8efe-c5966b037bc2", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019336600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5858f33a-1fb6-43bd-bc2c-339cf1a1bdc1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84dfcd16-5150-4db1-95cc-40848294510d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019704300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22fdfb70-6a42-42ae-aeb7-295b77b178fe", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804019963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87e36746-4706-4a8a-851c-4d04eae807c6", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804020220400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367b07a5-8435-4a11-bdd1-8754831c52ff", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804020486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d4d81a-f6f9-4a23-b85c-3d6698850af3", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804020550000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bb3f7ec-0a92-472e-bfe6-dccbb1dc78b6", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804020613900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6138cdf4-fa58-453d-aeab-2b4e32cb72b8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804022900200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac414f4-9f8e-4ada-a2af-b5aac1107f48", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804023445300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97688474-9ed1-4e61-a075-1e57e6960fd5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804024540000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c43d9f-23e1-463a-9264-48c46f2f1384", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804024765800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a853cd67-373c-4fe4-96ae-c2b79cb44e77", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804024930100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "627b4aaf-9b97-48fc-a75c-1ed8b1b4d36b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804025371000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c037119-b6e7-4a16-948c-2881519c8b68", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804025430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5821faaf-c34e-4512-bebf-ab2d17e24eac", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804025585000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ba1e9d2-b060-4a03-a142-bcca98443ba2", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804025869800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b9ef79-d720-4fba-a1a1-ce1749a9d62e", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804026414400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b3975ef-3206-4c1a-910f-8f246c6c4ff7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804027469100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a15a23-4f4c-4084-b236-85a408d8d942", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804027868600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e792958-3b84-4bc2-bf43-16c38fe45d3a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804028495200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f649ae5-94ac-42ee-8962-27c1accf0fc6", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804028757600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc63e71-4806-49e9-9434-9461a15700c5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804028959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ca43e91-dc26-4f9b-9631-2e83e622f579", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804029491100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c125ca37-f503-4aae-a69d-786d0899ccd0", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804029720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adb33952-49f4-4fa1-a4e6-982d02cf686f", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804029783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d0ffcb6-9098-4153-a31a-2f95dbc9f913", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804029827700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa259ee8-56bc-4330-922d-5041e293219b", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804030533600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41330642-b204-42f2-b9d8-99a177ff6515", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804030755500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6d0862-cb57-4be1-8520-65bb1589ccb2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804030952000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a6e3a15-5d12-4db2-9a59-9a7a3fe07d2e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804037674700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c787c3d5-186b-49e9-a204-95e5e93f4ed4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804037907700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fadb42bc-3136-44b1-a58f-2d16a8a19496", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804038093200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34ac5844-1b77-4cec-a06b-00371c21226c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804038162300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a759c1b7-4fb9-4e56-bbae-d5ca9757cffd", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804038339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "605f90d3-b888-4860-bc8b-9f79ded65f1f", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804039105200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502cdd25-e0c5-4be6-b54f-2aaf1b1bd302", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804039453600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09cad3ec-c346-4dfa-800e-1d8e157d0c6e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804039771200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "180ad8ba-b77c-44d8-ae5b-e8a7f55faa03", "name": "Incremental task entry:default@PackageHap post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804040062700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9bdaa8a-6e5a-46d4-b581-a0333c90246d", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804040239800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a01066-9c7a-46d0-b921-71a3cce681f3", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804040327700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c9299b-e336-4f89-8f7e-e0fb4341bdcf", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804040535100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a906e1ad-817b-40d1-b1b6-8516cda5a9d6", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804042792400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4cc69c-f4b9-40e0-b31e-ef43a9156940", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804043062100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4088c5-31e6-45ae-b49c-0081b0c9b4b5", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804043410400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9540004-29e8-455b-9ad5-7baaf0f5d969", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804043620100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}