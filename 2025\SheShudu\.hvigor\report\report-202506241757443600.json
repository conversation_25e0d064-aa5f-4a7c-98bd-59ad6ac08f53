{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "b6244b31-95d6-4f78-a308-d7d4fd3cbf48", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041563877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c3e6648-5bec-4fa4-8498-812a39aa4105", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041636277500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95df9026-6211-4111-b371-8edd78fec6ce", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041636595400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a37e74e5-d073-4ec8-bbf6-055ba7c877d9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096251886000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b445138-1506-4278-bb20-e0dae3d151b7", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096260938700, "endTime": 34096558736900}, "additional": {"children": ["81c7b16d-c0c9-47fe-88ad-81806145abff", "1f53ed36-3c35-48b0-ac30-39e038018b8f", "21a05848-6dbf-4323-9a43-6b6b5d2a6561", "565de1b4-0f98-4a72-ab03-44e3fd46b758", "b5e10693-e83f-4700-976c-9fb6fed33b0e", "3bd0c72c-6cd9-488f-abe7-b4721296bebb", "a048c0ed-ce3b-4643-b526-0c52655e0516"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e4438424-9b1b-434d-a469-93336e5a9652"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81c7b16d-c0c9-47fe-88ad-81806145abff", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096260940700, "endTime": 34096274518400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "69c51633-ac4b-4259-b80d-5813fb33e8d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096274535300, "endTime": 34096557429200}, "additional": {"children": ["a4254214-2dbe-4ead-9f00-70a490d7e062", "dd7ccdd9-0fb2-4748-bffd-c713801af6a1", "24373850-5ba1-441c-8743-0814806caa2b", "163994a1-6ac6-49d2-a035-2697b1560c81", "27e71387-b0f3-49c5-9fd9-903f587a73c3", "7cb521db-f89a-44de-a00a-ce740e60e01d", "faf724e3-0f5c-4070-a6ca-0dd02fdb3093", "b81bae04-9621-4641-af95-5d62209c95d7", "50a3ce59-87a3-4b6f-a8a3-98d8de83ad03"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "8620343e-79f6-4985-b777-54b14dcc0f8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21a05848-6dbf-4323-9a43-6b6b5d2a6561", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096557452800, "endTime": 34096558725400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "9c3e3830-3b7e-4dde-a8d0-2974c3d9fe6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "565de1b4-0f98-4a72-ab03-44e3fd46b758", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096558729900, "endTime": 34096558731400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "44d8c00e-cdc7-44af-bf9d-efa5ce9302d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5e10693-e83f-4700-976c-9fb6fed33b0e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096264521100, "endTime": 34096264572400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "b12b00e0-cf22-4660-85a0-c0f38161d990"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b12b00e0-cf22-4660-85a0-c0f38161d990", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096264521100, "endTime": 34096264572400}, "additional": {"logType": "info", "children": [], "durationId": "b5e10693-e83f-4700-976c-9fb6fed33b0e", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "3bd0c72c-6cd9-488f-abe7-b4721296bebb", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096269873800, "endTime": 34096269892200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "7418fbc6-6e44-4d49-bcb7-0e9fd8194bf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7418fbc6-6e44-4d49-bcb7-0e9fd8194bf9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096269873800, "endTime": 34096269892200}, "additional": {"logType": "info", "children": [], "durationId": "3bd0c72c-6cd9-488f-abe7-b4721296bebb", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "9d69f5d3-ab90-4bc3-8f11-9a1f391254b1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096269934000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a98a207-53b8-408b-ae4b-d7c64bfa444e", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096274392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c51633-ac4b-4259-b80d-5813fb33e8d9", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096260940700, "endTime": 34096274518400}, "additional": {"logType": "info", "children": [], "durationId": "81c7b16d-c0c9-47fe-88ad-81806145abff", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "a4254214-2dbe-4ead-9f00-70a490d7e062", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096280570200, "endTime": 34096280586300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "a30205bd-955b-43af-93a1-f9b23afff4f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd7ccdd9-0fb2-4748-bffd-c713801af6a1", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096280609300, "endTime": 34096286295200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "a446725c-a52c-4ee9-98bf-5f8ad70ff20f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24373850-5ba1-441c-8743-0814806caa2b", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096286314800, "endTime": 34096402700100}, "additional": {"children": ["325bb575-e188-4301-b6d8-110a1f166755", "8155c953-683a-43f7-8994-01ad76dbb9e7", "7d33902f-ce14-4486-a641-0f92a36297fd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "8583d6d5-4f6a-461a-a896-95b53f5de4ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "163994a1-6ac6-49d2-a035-2697b1560c81", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096402753300, "endTime": 34096430624900}, "additional": {"children": ["2f9e70fb-a6d6-4105-8b6b-9c22c520f5cd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "9acd924b-d424-4246-adb0-d2a9b96a5895"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27e71387-b0f3-49c5-9fd9-903f587a73c3", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096430635000, "endTime": 34096534262500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "4b596e89-414b-417d-8315-11603607788c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cb521db-f89a-44de-a00a-ce740e60e01d", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096535120600, "endTime": 34096548238500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "38ca8738-0d09-4e06-bd06-e80f9049f60d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "faf724e3-0f5c-4070-a6ca-0dd02fdb3093", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096548262200, "endTime": 34096557220000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "d377bbb1-131b-4e8e-b8fb-03c76fffbcc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b81bae04-9621-4641-af95-5d62209c95d7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096557242200, "endTime": 34096557397700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "8d61c92b-f6a8-4608-a3ca-94f13dd2a2c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a30205bd-955b-43af-93a1-f9b23afff4f0", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096280570200, "endTime": 34096280586300}, "additional": {"logType": "info", "children": [], "durationId": "a4254214-2dbe-4ead-9f00-70a490d7e062", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "a446725c-a52c-4ee9-98bf-5f8ad70ff20f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096280609300, "endTime": 34096286295200}, "additional": {"logType": "info", "children": [], "durationId": "dd7ccdd9-0fb2-4748-bffd-c713801af6a1", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "325bb575-e188-4301-b6d8-110a1f166755", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096286997000, "endTime": 34096287016400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "24373850-5ba1-441c-8743-0814806caa2b", "logId": "4e46e200-aef5-41b3-bf2b-6cbb5d9fdb02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e46e200-aef5-41b3-bf2b-6cbb5d9fdb02", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096286997000, "endTime": 34096287016400}, "additional": {"logType": "info", "children": [], "durationId": "325bb575-e188-4301-b6d8-110a1f166755", "parent": "8583d6d5-4f6a-461a-a896-95b53f5de4ac"}}, {"head": {"id": "8155c953-683a-43f7-8994-01ad76dbb9e7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096291339200, "endTime": 34096401748200}, "additional": {"children": ["cc2f540a-7764-45e3-81e4-50fdfb97d79a", "4466bba8-4040-4e19-b42a-c2395b7d6b29"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "24373850-5ba1-441c-8743-0814806caa2b", "logId": "163827ab-4d62-45cf-9b6a-29507cb75fdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc2f540a-7764-45e3-81e4-50fdfb97d79a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096291341700, "endTime": 34096295901200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8155c953-683a-43f7-8994-01ad76dbb9e7", "logId": "4e3bf224-457d-455e-94f0-01a95f298a05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4466bba8-4040-4e19-b42a-c2395b7d6b29", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096295919100, "endTime": 34096401732600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8155c953-683a-43f7-8994-01ad76dbb9e7", "logId": "d978e516-af05-40f0-a29e-4595de557535"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7089906-3d82-4244-a02d-2d332096976d", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096291347300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd475ce6-e4ec-48ba-8cbe-ee1a93f8aa62", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096295743700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e3bf224-457d-455e-94f0-01a95f298a05", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096291341700, "endTime": 34096295901200}, "additional": {"logType": "info", "children": [], "durationId": "cc2f540a-7764-45e3-81e4-50fdfb97d79a", "parent": "163827ab-4d62-45cf-9b6a-29507cb75fdf"}}, {"head": {"id": "744715e8-d6be-465f-b610-d875c8c1bef4", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096295932600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36dffd0f-80e9-40a7-bb14-95e632eb639b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096301797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56fe67da-e671-48eb-8b2c-b0469c462009", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096301950800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ec531a-5a21-4668-a6f6-640a7e422acd", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096302150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdafffbb-3c47-4622-bb51-1d10e238b7ac", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096302246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c926f1-8613-44be-a094-98af1e7956b5", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096303732700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e03f118c-05fe-448d-a71d-d99ca0fc56ec", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096308705600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae7b05cd-32c6-46a5-bdbe-1d2e7405b5fc", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096318792600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1dc33bc-eb0b-4b77-9fdc-eea0870af437", "name": "Sdk init in 58 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096367168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b3d44e0-ff77-48d7-9ee8-d18a7178c579", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096367346400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 57}, "markType": "other"}}, {"head": {"id": "1995db6d-a09c-4b0a-92b0-04bc076e637a", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096367404300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 57}, "markType": "other"}}, {"head": {"id": "9999ba19-9bf0-4dc0-b800-4c69203948bb", "name": "Project task initialization takes 33 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096401337700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee861228-bb6d-4aa1-ae72-2c54384f3f02", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096401497600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99cd016a-7747-4933-a018-8ca6b34297bc", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096401561600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95bd10ca-3842-42a8-b705-7c6e40adbf53", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096401648600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d978e516-af05-40f0-a29e-4595de557535", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096295919100, "endTime": 34096401732600}, "additional": {"logType": "info", "children": [], "durationId": "4466bba8-4040-4e19-b42a-c2395b7d6b29", "parent": "163827ab-4d62-45cf-9b6a-29507cb75fdf"}}, {"head": {"id": "163827ab-4d62-45cf-9b6a-29507cb75fdf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096291339200, "endTime": 34096401748200}, "additional": {"logType": "info", "children": ["4e3bf224-457d-455e-94f0-01a95f298a05", "d978e516-af05-40f0-a29e-4595de557535"], "durationId": "8155c953-683a-43f7-8994-01ad76dbb9e7", "parent": "8583d6d5-4f6a-461a-a896-95b53f5de4ac"}}, {"head": {"id": "7d33902f-ce14-4486-a641-0f92a36297fd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096402610700, "endTime": 34096402670300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "24373850-5ba1-441c-8743-0814806caa2b", "logId": "a0847421-0c75-4339-9ddf-c93881de3edb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0847421-0c75-4339-9ddf-c93881de3edb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096402610700, "endTime": 34096402670300}, "additional": {"logType": "info", "children": [], "durationId": "7d33902f-ce14-4486-a641-0f92a36297fd", "parent": "8583d6d5-4f6a-461a-a896-95b53f5de4ac"}}, {"head": {"id": "8583d6d5-4f6a-461a-a896-95b53f5de4ac", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096286314800, "endTime": 34096402700100}, "additional": {"logType": "info", "children": ["4e46e200-aef5-41b3-bf2b-6cbb5d9fdb02", "163827ab-4d62-45cf-9b6a-29507cb75fdf", "a0847421-0c75-4339-9ddf-c93881de3edb"], "durationId": "24373850-5ba1-441c-8743-0814806caa2b", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "2f9e70fb-a6d6-4105-8b6b-9c22c520f5cd", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096403564500, "endTime": 34096430612200}, "additional": {"children": ["f877d5f6-6379-40b7-bd78-09029aed9c55", "dccd9c52-4ff6-4ae5-a372-b1e85d1dbf6d", "1876ab6b-1c7d-4765-be99-4ddddba7b2bd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "163994a1-6ac6-49d2-a035-2697b1560c81", "logId": "35e53b05-7b08-4e32-ae60-350d8462ab9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f877d5f6-6379-40b7-bd78-09029aed9c55", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096408486500, "endTime": 34096408519700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f9e70fb-a6d6-4105-8b6b-9c22c520f5cd", "logId": "5e68a692-b4ff-4448-ae3c-d1e7210547c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e68a692-b4ff-4448-ae3c-d1e7210547c3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096408486500, "endTime": 34096408519700}, "additional": {"logType": "info", "children": [], "durationId": "f877d5f6-6379-40b7-bd78-09029aed9c55", "parent": "35e53b05-7b08-4e32-ae60-350d8462ab9d"}}, {"head": {"id": "dccd9c52-4ff6-4ae5-a372-b1e85d1dbf6d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096411359500, "endTime": 34096429408800}, "additional": {"children": ["771cacdc-a363-4e4b-b520-cfb76283fc5e", "59b43cfa-7a51-4014-957b-2a109ef1e255"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f9e70fb-a6d6-4105-8b6b-9c22c520f5cd", "logId": "d9cfc0f0-d842-4982-a7d5-e6857392b752"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "771cacdc-a363-4e4b-b520-cfb76283fc5e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096411361800, "endTime": 34096417329000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dccd9c52-4ff6-4ae5-a372-b1e85d1dbf6d", "logId": "e6eaffaf-c40e-48e0-8b84-0b82bbac7103"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59b43cfa-7a51-4014-957b-2a109ef1e255", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096417345000, "endTime": 34096429395100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dccd9c52-4ff6-4ae5-a372-b1e85d1dbf6d", "logId": "68883385-3d56-4948-8230-de0957652608"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e656779f-f0e9-4eab-91e4-7d183720dcbb", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096411367100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "532fdd5b-1c74-4808-9722-792e9a02090a", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096417199800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6eaffaf-c40e-48e0-8b84-0b82bbac7103", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096411361800, "endTime": 34096417329000}, "additional": {"logType": "info", "children": [], "durationId": "771cacdc-a363-4e4b-b520-cfb76283fc5e", "parent": "d9cfc0f0-d842-4982-a7d5-e6857392b752"}}, {"head": {"id": "0b0da9c9-ec29-4748-b989-c5194022dd88", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096417354600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8534a168-e0bf-4e7e-adfa-8069473e0bcb", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096425901500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8210a0-33a2-49a3-9f17-a2fbc47f7ede", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096426041400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d98ac2b-658d-467c-a1c0-8e67f1b02d3a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096426241300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2685356f-67a3-4614-a10e-67efdfe5b097", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096426372700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24474d6b-7eea-41d8-9ebe-5a6f4850eb72", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096426432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8999a8ee-f85f-4832-9745-90c95eaa8c8d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096426486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a180f0a9-a2dc-40bc-93f8-bfe8895494e1", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096426538600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232a7193-e64d-493e-8f71-d53742228266", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096429051800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b8a1b9c-7ba3-4db5-a608-818d3c41d3ab", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096429235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d565dedb-7c1a-4df4-b7e3-6064ac7a1d80", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096429301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d779bfd-d2ba-4edf-ad67-dc32cf19b00a", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096429349800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68883385-3d56-4948-8230-de0957652608", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096417345000, "endTime": 34096429395100}, "additional": {"logType": "info", "children": [], "durationId": "59b43cfa-7a51-4014-957b-2a109ef1e255", "parent": "d9cfc0f0-d842-4982-a7d5-e6857392b752"}}, {"head": {"id": "d9cfc0f0-d842-4982-a7d5-e6857392b752", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096411359500, "endTime": 34096429408800}, "additional": {"logType": "info", "children": ["e6eaffaf-c40e-48e0-8b84-0b82bbac7103", "68883385-3d56-4948-8230-de0957652608"], "durationId": "dccd9c52-4ff6-4ae5-a372-b1e85d1dbf6d", "parent": "35e53b05-7b08-4e32-ae60-350d8462ab9d"}}, {"head": {"id": "1876ab6b-1c7d-4765-be99-4ddddba7b2bd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096430575400, "endTime": 34096430592700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f9e70fb-a6d6-4105-8b6b-9c22c520f5cd", "logId": "6c14f89f-f6ae-443c-a09f-c0052f59f9a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c14f89f-f6ae-443c-a09f-c0052f59f9a1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096430575400, "endTime": 34096430592700}, "additional": {"logType": "info", "children": [], "durationId": "1876ab6b-1c7d-4765-be99-4ddddba7b2bd", "parent": "35e53b05-7b08-4e32-ae60-350d8462ab9d"}}, {"head": {"id": "35e53b05-7b08-4e32-ae60-350d8462ab9d", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096403564500, "endTime": 34096430612200}, "additional": {"logType": "info", "children": ["5e68a692-b4ff-4448-ae3c-d1e7210547c3", "d9cfc0f0-d842-4982-a7d5-e6857392b752", "6c14f89f-f6ae-443c-a09f-c0052f59f9a1"], "durationId": "2f9e70fb-a6d6-4105-8b6b-9c22c520f5cd", "parent": "9acd924b-d424-4246-adb0-d2a9b96a5895"}}, {"head": {"id": "9acd924b-d424-4246-adb0-d2a9b96a5895", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096402753300, "endTime": 34096430624900}, "additional": {"logType": "info", "children": ["35e53b05-7b08-4e32-ae60-350d8462ab9d"], "durationId": "163994a1-6ac6-49d2-a035-2697b1560c81", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "b32778d4-9d2c-45ab-b0e1-03113c3cd008", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096475364500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf9940b-222f-43c8-a3a8-c28a49481d7e", "name": "hvigorfile, resolve hvigorfile dependencies in 104 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096534127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b596e89-414b-417d-8315-11603607788c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096430635000, "endTime": 34096534262500}, "additional": {"logType": "info", "children": [], "durationId": "27e71387-b0f3-49c5-9fd9-903f587a73c3", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "50a3ce59-87a3-4b6f-a8a3-98d8de83ad03", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096534935600, "endTime": 34096535101700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "logId": "14a8bc03-0f98-4898-8371-699ee7592f9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e080f945-5566-4625-a4b9-adb4d7936df2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096534957700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a8bc03-0f98-4898-8371-699ee7592f9b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096534935600, "endTime": 34096535101700}, "additional": {"logType": "info", "children": [], "durationId": "50a3ce59-87a3-4b6f-a8a3-98d8de83ad03", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "67722986-0932-424a-b405-18b6347a09e0", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096536283000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6973cc98-c11e-48ad-8a98-ac02b763e76a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096547656100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ca8738-0d09-4e06-bd06-e80f9049f60d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096535120600, "endTime": 34096548238500}, "additional": {"logType": "info", "children": [], "durationId": "7cb521db-f89a-44de-a00a-ce740e60e01d", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "a94e0868-0fde-4825-9607-41d8ab627e5d", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096551769300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef72d222-bc79-4e74-9648-a27307087ba8", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096551888800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85cd900e-219c-4b26-b2ad-6e3268432e9d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096553482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e273f1d4-b86c-4cb8-b305-146d3c0d21d4", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096553602800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d377bbb1-131b-4e8e-b8fb-03c76fffbcc8", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096548262200, "endTime": 34096557220000}, "additional": {"logType": "info", "children": [], "durationId": "faf724e3-0f5c-4070-a6ca-0dd02fdb3093", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "7303efc0-179e-4758-b348-0f693bda6871", "name": "Configuration phase cost:277 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096557268300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d61c92b-f6a8-4608-a3ca-94f13dd2a2c8", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096557242200, "endTime": 34096557397700}, "additional": {"logType": "info", "children": [], "durationId": "b81bae04-9621-4641-af95-5d62209c95d7", "parent": "8620343e-79f6-4985-b777-54b14dcc0f8c"}}, {"head": {"id": "8620343e-79f6-4985-b777-54b14dcc0f8c", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096274535300, "endTime": 34096557429200}, "additional": {"logType": "info", "children": ["a30205bd-955b-43af-93a1-f9b23afff4f0", "a446725c-a52c-4ee9-98bf-5f8ad70ff20f", "8583d6d5-4f6a-461a-a896-95b53f5de4ac", "9acd924b-d424-4246-adb0-d2a9b96a5895", "4b596e89-414b-417d-8315-11603607788c", "38ca8738-0d09-4e06-bd06-e80f9049f60d", "d377bbb1-131b-4e8e-b8fb-03c76fffbcc8", "8d61c92b-f6a8-4608-a3ca-94f13dd2a2c8", "14a8bc03-0f98-4898-8371-699ee7592f9b"], "durationId": "1f53ed36-3c35-48b0-ac30-39e038018b8f", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "a048c0ed-ce3b-4643-b526-0c52655e0516", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096558690300, "endTime": 34096558710300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b445138-1506-4278-bb20-e0dae3d151b7", "logId": "de4db3ef-b929-4770-9682-272ec9600ebf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de4db3ef-b929-4770-9682-272ec9600ebf", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096558690300, "endTime": 34096558710300}, "additional": {"logType": "info", "children": [], "durationId": "a048c0ed-ce3b-4643-b526-0c52655e0516", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "9c3e3830-3b7e-4dde-a8d0-2974c3d9fe6f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096557452800, "endTime": 34096558725400}, "additional": {"logType": "info", "children": [], "durationId": "21a05848-6dbf-4323-9a43-6b6b5d2a6561", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "44d8c00e-cdc7-44af-bf9d-efa5ce9302d7", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096558729900, "endTime": 34096558731400}, "additional": {"logType": "info", "children": [], "durationId": "565de1b4-0f98-4a72-ab03-44e3fd46b758", "parent": "e4438424-9b1b-434d-a469-93336e5a9652"}}, {"head": {"id": "e4438424-9b1b-434d-a469-93336e5a9652", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096260938700, "endTime": 34096558736900}, "additional": {"logType": "info", "children": ["69c51633-ac4b-4259-b80d-5813fb33e8d9", "8620343e-79f6-4985-b777-54b14dcc0f8c", "9c3e3830-3b7e-4dde-a8d0-2974c3d9fe6f", "44d8c00e-cdc7-44af-bf9d-efa5ce9302d7", "b12b00e0-cf22-4660-85a0-c0f38161d990", "7418fbc6-6e44-4d49-bcb7-0e9fd8194bf9", "de4db3ef-b929-4770-9682-272ec9600ebf"], "durationId": "3b445138-1506-4278-bb20-e0dae3d151b7"}}, {"head": {"id": "becc67a8-673c-4434-ac19-85ac232238f8", "name": "Configuration task cost before running: 302 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096558938300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca7270c6-2c54-4ce3-874b-a7223f0d4dd6", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096563985500, "endTime": 34096572038400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "086275be-5139-49b7-906a-2caf1da5f6e4", "logId": "bf16bbd0-b236-4f6f-858f-66d3207359d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "086275be-5139-49b7-906a-2caf1da5f6e4", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096560709800}, "additional": {"logType": "detail", "children": [], "durationId": "ca7270c6-2c54-4ce3-874b-a7223f0d4dd6"}}, {"head": {"id": "b7c13fdf-476e-40c5-b613-0d8e37e397f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096561124600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "637ff485-b6f6-463b-b437-6a75ec368753", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096561276900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d73719-8336-4cd0-8c03-009462a29c6f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096563999100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ff394a-0b0a-42fb-af1d-609a2b605cd6", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096571820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db9d86db-539b-4d9c-a506-3cdf9ab756ff", "name": "entry : default@PreBuild cost memory 0.3138427734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096571965000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf16bbd0-b236-4f6f-858f-66d3207359d5", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096563985500, "endTime": 34096572038400}, "additional": {"logType": "info", "children": [], "durationId": "ca7270c6-2c54-4ce3-874b-a7223f0d4dd6"}}, {"head": {"id": "a666d0e3-1f39-4606-9b73-7f7ad11394ec", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096582797900, "endTime": 34096584951100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "947267e1-b15a-43cf-9cf7-ab0e1fa3136b", "logId": "0e7af377-26d4-49ba-9fae-adf9c0c4220e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "947267e1-b15a-43cf-9cf7-ab0e1fa3136b", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096579818200}, "additional": {"logType": "detail", "children": [], "durationId": "a666d0e3-1f39-4606-9b73-7f7ad11394ec"}}, {"head": {"id": "3bbf6d07-abcf-4b07-80fa-3e7e9d3f6f96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096580540800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a715891-a45e-4ae0-96ed-89de0aa1f486", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096580943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed770111-ff68-4e2d-b4e8-9c4ba7284891", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096582810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d95b7c42-85aa-4ae8-b073-1334b9e3db27", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096583533700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc549431-4cb6-4893-ac75-91386a0ba6c5", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096584759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11b0a6e-2593-47ad-8e06-29f217488183", "name": "entry : default@GenerateMetadata cost memory 0.09165191650390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096584880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7af377-26d4-49ba-9fae-adf9c0c4220e", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096582797900, "endTime": 34096584951100}, "additional": {"logType": "info", "children": [], "durationId": "a666d0e3-1f39-4606-9b73-7f7ad11394ec"}}, {"head": {"id": "10dac66d-a933-424f-a51b-bf475081afac", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096588903600, "endTime": 34096591832000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "74195fda-fa3c-463c-b60e-f3348e9caabd", "logId": "147c85a8-91c2-4890-8e8e-d51d468f84a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74195fda-fa3c-463c-b60e-f3348e9caabd", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096587812400}, "additional": {"logType": "detail", "children": [], "durationId": "10dac66d-a933-424f-a51b-bf475081afac"}}, {"head": {"id": "83d4914d-882a-4e36-bed4-51762dd88b49", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096588273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b10987-9165-42f8-8f18-e791c0d3f49b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096588434500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0e1794-0487-493a-a130-49da1c01a070", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096588915300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49334f86-7182-45b7-aa73-f9b2a6102850", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096589460300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d359324-268d-4ab1-849d-cde6118680cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096589845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4b84543-b06d-429b-ae32-a964d499840f", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096590944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a7403a-1f9d-49ae-a500-b5b9b0b1e2aa", "name": "runTaskFromQueue task cost before running: 335 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096591600900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147c85a8-91c2-4890-8e8e-d51d468f84a5", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096588903600, "endTime": 34096591832000, "totalTime": 2326600}, "additional": {"logType": "info", "children": [], "durationId": "10dac66d-a933-424f-a51b-bf475081afac"}}, {"head": {"id": "a28f3695-6db0-4d1d-a7eb-91efcc264156", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096597540300, "endTime": 34096599208800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "674e3539-c91a-46dd-8236-bc89e469c715", "logId": "67e96479-fa3b-4163-a082-d1bb05bf062a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "674e3539-c91a-46dd-8236-bc89e469c715", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096595903600}, "additional": {"logType": "detail", "children": [], "durationId": "a28f3695-6db0-4d1d-a7eb-91efcc264156"}}, {"head": {"id": "7480a2cc-e45b-4f96-bfcb-bd062c921422", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096596375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd0de49-9b1f-4346-ae15-83491f2370b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096596532300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c69eb3-0291-4434-be6d-7a0383e5d019", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096597551500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea3dbda8-0768-4a69-a0c5-4e1c220bfa96", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096598953800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3dc0ee0-452d-469f-b710-d7076cfa45f4", "name": "entry : default@MergeProfile cost memory 0.10509490966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096599053800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e96479-fa3b-4163-a082-d1bb05bf062a", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096597540300, "endTime": 34096599208800}, "additional": {"logType": "info", "children": [], "durationId": "a28f3695-6db0-4d1d-a7eb-91efcc264156"}}, {"head": {"id": "6a40d8ac-848f-45f1-92e1-4e6723ad26cc", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096602271500, "endTime": 34096604391400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4c853845-d25d-47f2-b14c-cf0c70ebe1cb", "logId": "95e5de6b-3859-4166-857e-4dd97a82550f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c853845-d25d-47f2-b14c-cf0c70ebe1cb", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096601111000}, "additional": {"logType": "detail", "children": [], "durationId": "6a40d8ac-848f-45f1-92e1-4e6723ad26cc"}}, {"head": {"id": "8d0fe432-41af-4d9d-aa74-ea3de6d2243d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096601529100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d97efa40-144b-4030-8de6-12ad4880f1de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096601639900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b7292a-e554-4135-82b7-a75309d5c242", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096602281700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26d161c-9363-4a8a-bf7c-3dd5b95eda70", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096602993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8465510-cea6-4dbf-81f3-7c6320601355", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096603999900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5932e9b1-2c0f-47d9-81b8-899d0693dd81", "name": "entry : default@CreateBuildProfile cost memory 0.09991455078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096604112700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95e5de6b-3859-4166-857e-4dd97a82550f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096602271500, "endTime": 34096604391400}, "additional": {"logType": "info", "children": [], "durationId": "6a40d8ac-848f-45f1-92e1-4e6723ad26cc"}}, {"head": {"id": "76e8f3c1-d38c-4ebb-bc79-d82388303c7a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096611873200, "endTime": 34096612470600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9cb52ba5-6e46-42c2-8f40-38d4a727daa6", "logId": "54277591-6c40-43d7-a510-364ba41f9a43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cb52ba5-6e46-42c2-8f40-38d4a727daa6", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096610162100}, "additional": {"logType": "detail", "children": [], "durationId": "76e8f3c1-d38c-4ebb-bc79-d82388303c7a"}}, {"head": {"id": "5299b569-da2d-42b7-acc4-ac03d33e187d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096610594200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008dc898-c1c6-48bf-8e90-5e79d5854a06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096610751700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "380c44a3-a371-49d1-9521-4d29b670c931", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096611885800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a6d63d8-026a-46bd-be06-c44a4b9b6341", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096612044500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ccb2630-56e9-472b-bb93-03a777b329b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096612102500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ebbb2db-80e3-4aa9-abb9-ffa6477af28a", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096612295600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d6a8a56-3acf-4277-83c7-2b98fdc227c3", "name": "runTaskFromQueue task cost before running: 356 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096612391800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54277591-6c40-43d7-a510-364ba41f9a43", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096611873200, "endTime": 34096612470600, "totalTime": 498800}, "additional": {"logType": "info", "children": [], "durationId": "76e8f3c1-d38c-4ebb-bc79-d82388303c7a"}}, {"head": {"id": "e2812108-4a1d-4e98-b896-575fcbbed197", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096618756800, "endTime": 34096619671400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ffef22ea-0e19-40e5-b235-4069a8afd8a0", "logId": "e946dff7-473f-465e-bdd2-cf4c95742a59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffef22ea-0e19-40e5-b235-4069a8afd8a0", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096614346700}, "additional": {"logType": "detail", "children": [], "durationId": "e2812108-4a1d-4e98-b896-575fcbbed197"}}, {"head": {"id": "b136c837-8041-435b-8b74-8552e4582474", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096614724000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438396c8-6e5b-4894-b467-d170438be8b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096614823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee086f23-e9ef-484c-abb0-8081f5e4fb29", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096618769900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f64e6ea8-e6e0-4d72-ae35-b74c8e704d9f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096619005800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972c1b34-71ad-4d82-a9d8-f00a2445daeb", "name": "entry : default@GeneratePkgContextInfo cost memory 0.037109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096619436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90fd579c-2eeb-428f-86a9-b1826daf2b31", "name": "runTaskFromQueue task cost before running: 363 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096619593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e946dff7-473f-465e-bdd2-cf4c95742a59", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096618756800, "endTime": 34096619671400, "totalTime": 811200}, "additional": {"logType": "info", "children": [], "durationId": "e2812108-4a1d-4e98-b896-575fcbbed197"}}, {"head": {"id": "1dac3a6f-4b96-42de-a3b9-046a60cdb5ba", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096630837700, "endTime": 34096632912200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "926a0369-3f7a-4df6-90fa-65f62a5f8ab5", "logId": "0122ed49-2ee1-4821-ad79-543cf2faebd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "926a0369-3f7a-4df6-90fa-65f62a5f8ab5", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096625752900}, "additional": {"logType": "detail", "children": [], "durationId": "1dac3a6f-4b96-42de-a3b9-046a60cdb5ba"}}, {"head": {"id": "7ecae120-6936-408e-af23-a4b8e0c3d5e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096626701800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2968b33-0219-4e90-aa34-8cda2ca8db74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096628868900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6272c05-e193-44ce-a541-39389dc7f5f7", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096630849600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec204c53-c0a6-4dbb-8341-78808ad958a1", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096632212400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6c2d45-3026-42ef-8180-89e98730567e", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096632347800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1af07ea-2187-4aac-a804-8076bf458627", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096632435000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52af4638-4ad2-45df-b755-2f1d5250a69e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096632539000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d34dace-ea75-4c2e-a599-5abd680058ff", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11734771728515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096632655600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "908e2371-91d0-4e73-9f40-0e7532560540", "name": "runTaskFromQueue task cost before running: 376 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096632803100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0122ed49-2ee1-4821-ad79-543cf2faebd3", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096630837700, "endTime": 34096632912200, "totalTime": 1913100}, "additional": {"logType": "info", "children": [], "durationId": "1dac3a6f-4b96-42de-a3b9-046a60cdb5ba"}}, {"head": {"id": "b7b2d74f-0d8b-4f3a-8400-67a4ff632af0", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636569000, "endTime": 34096636934000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "58c08a1e-47d3-492e-a50f-9e8ea1429cdf", "logId": "59c7e80e-d77f-437c-9397-81a79d021456"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58c08a1e-47d3-492e-a50f-9e8ea1429cdf", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096635091900}, "additional": {"logType": "detail", "children": [], "durationId": "b7b2d74f-0d8b-4f3a-8400-67a4ff632af0"}}, {"head": {"id": "26481dd8-d325-4244-9e8d-e3e066842e52", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096635659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa1d848-1a50-4172-9fcb-641648149fb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096635805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2a9633e-6709-4676-b55d-ab5eaa554ee4", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636579700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9621bb-95c2-49cd-acab-31f34859dd8b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636700100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e62f13-00bf-4722-a770-1c4c48e29f3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636752400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b438ae8-7b5d-45f6-8475-c4ed1b726a49", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636815100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff8ad52-604d-4b20-a56a-5bc9d85fb24e", "name": "runTaskFromQueue task cost before running: 380 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636883900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c7e80e-d77f-437c-9397-81a79d021456", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096636569000, "endTime": 34096636934000, "totalTime": 300100}, "additional": {"logType": "info", "children": [], "durationId": "b7b2d74f-0d8b-4f3a-8400-67a4ff632af0"}}, {"head": {"id": "1339413c-b821-4ca0-a685-94ee0a7b0f54", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096639817400, "endTime": 34096643020200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9bc3541a-3ac6-4731-825b-eba5cecd04af", "logId": "28a09d8a-3823-43a4-8741-7eb7638cbbfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bc3541a-3ac6-4731-825b-eba5cecd04af", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096638468500}, "additional": {"logType": "detail", "children": [], "durationId": "1339413c-b821-4ca0-a685-94ee0a7b0f54"}}, {"head": {"id": "2234a83f-9994-4201-82d4-a174da824885", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096638891400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c35632fb-e322-4af6-bc59-d9f95837d9e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096639017900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d02e68e-4e08-4ace-8b00-a315c7bc2169", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096639832100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e68c77d6-487b-4e49-834f-5246dd421023", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096642825200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5d64da-8707-45fb-a432-fd141782b590", "name": "entry : default@MakePackInfo cost memory 0.1392974853515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096642951900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28a09d8a-3823-43a4-8741-7eb7638cbbfd", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096639817400, "endTime": 34096643020200}, "additional": {"logType": "info", "children": [], "durationId": "1339413c-b821-4ca0-a685-94ee0a7b0f54"}}, {"head": {"id": "9617ef12-c92c-4023-b1a4-35e2ae6c79da", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096646500900, "endTime": 34096651060400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "67e7ecdf-08f5-4c57-a023-59d242f03f79", "logId": "b68a5a25-549f-458b-9a80-a5908ff61a7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67e7ecdf-08f5-4c57-a023-59d242f03f79", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096644978600}, "additional": {"logType": "detail", "children": [], "durationId": "9617ef12-c92c-4023-b1a4-35e2ae6c79da"}}, {"head": {"id": "91497eff-85a2-4d74-9b2a-5723189af7a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096645319100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac75b42a-c910-4c78-bf29-586a52201a5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096645418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f30d2810-52a8-4520-8e1f-815f7c10ce1e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096646512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d0dd0e-8ce9-470a-be08-820414a53130", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096646750600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28674287-67fa-4c81-b0ed-1417ed9cae61", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096648484800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32a8472f-b6cf-4319-a5cf-af568bf77ff1", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096650517600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c661fd5-858f-4d9c-8cca-ee012e13fd03", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096650684200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d162b28f-ec0d-4344-8b71-c6704ad68902", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096650795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e5fd4e4-6334-4150-9433-f22640899e9a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096650855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af342141-1074-43f6-90c8-89fbe9572ce9", "name": "entry : default@SyscapTransform cost memory 0.1512451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096650934400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa708c4-3d0e-453a-b3b5-1e035f3b8627", "name": "runTaskFromQueue task cost before running: 394 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096651006400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68a5a25-549f-458b-9a80-a5908ff61a7d", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096646500900, "endTime": 34096651060400, "totalTime": 4489800}, "additional": {"logType": "info", "children": [], "durationId": "9617ef12-c92c-4023-b1a4-35e2ae6c79da"}}, {"head": {"id": "ee898664-9a83-40c4-a1dd-72bde7a59398", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096657027900, "endTime": 34096659425200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4b86697f-b5d7-421c-8ee6-86249d8d002a", "logId": "1739524d-229c-4d84-8bc6-1efca3eb7c9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b86697f-b5d7-421c-8ee6-86249d8d002a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096653446300}, "additional": {"logType": "detail", "children": [], "durationId": "ee898664-9a83-40c4-a1dd-72bde7a59398"}}, {"head": {"id": "52dacf92-15e8-4224-b351-0c71523206fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096654490600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34fcbe6-578f-4e84-8ed2-1768e9844737", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096654658200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf512aa-441d-479f-a2e4-42257aa792db", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096657348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e9c8eea-aa3c-49b3-80ef-a280d6b850fa", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096658801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e063dfd-9458-413e-8fdd-654c98c96602", "name": "entry : default@ProcessProfile cost memory 0.0602874755859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096659204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1739524d-229c-4d84-8bc6-1efca3eb7c9d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096657027900, "endTime": 34096659425200}, "additional": {"logType": "info", "children": [], "durationId": "ee898664-9a83-40c4-a1dd-72bde7a59398"}}, {"head": {"id": "cb44bf6d-4e96-4ccf-a4bf-132d890b270b", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096663921300, "endTime": 34096670613800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2c0a4e18-24f3-4e73-aae0-d69ecae9a7dd", "logId": "50b183f2-dbf1-41f2-88f2-cb33153c6f49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c0a4e18-24f3-4e73-aae0-d69ecae9a7dd", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096661418400}, "additional": {"logType": "detail", "children": [], "durationId": "cb44bf6d-4e96-4ccf-a4bf-132d890b270b"}}, {"head": {"id": "c3ee4b38-4eec-487f-b640-a6f63a4f5650", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096661844500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9a8d30-d4d4-4921-9f5e-889b913c2446", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096661943100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99107fb-cb3f-4c35-b4f2-cfd79b707777", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096663934800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efcb31a4-37a8-4bfb-8012-a979aae04d78", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096670375300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee89ab1-ae67-4d08-ad32-def5549de5a1", "name": "entry : default@ProcessRouterMap cost memory 0.34108734130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096670540800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b183f2-dbf1-41f2-88f2-cb33153c6f49", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096663921300, "endTime": 34096670613800}, "additional": {"logType": "info", "children": [], "durationId": "cb44bf6d-4e96-4ccf-a4bf-132d890b270b"}}, {"head": {"id": "bc140f8d-d57c-4d18-acd8-85e8d64c568a", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096676741000, "endTime": 34096678546500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fdf2a8d0-77a0-48b3-8735-4c5a7e6ee886", "logId": "7b29fff9-97c5-4967-b5e3-13c34aad2d21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdf2a8d0-77a0-48b3-8735-4c5a7e6ee886", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096674794200}, "additional": {"logType": "detail", "children": [], "durationId": "bc140f8d-d57c-4d18-acd8-85e8d64c568a"}}, {"head": {"id": "861a6cf2-3dc9-447f-8245-927272f76e53", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096675801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a81649e5-0032-439e-8fee-f2b860fb90a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096675934900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e43f3e6a-e3cd-44a4-8293-0a00f9de3cc1", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096676751700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6daf727-7e57-4b95-9cd7-90bea2f96502", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096676879000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10c1c631-b3ad-4bd6-9825-7033346ff5c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096676934800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4699627b-2920-4085-8e37-f683dec9ee76", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096678114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23da8fce-d9d1-4611-9b95-aa10b734d8b3", "name": "runTaskFromQueue task cost before running: 422 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096678304900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b29fff9-97c5-4967-b5e3-13c34aad2d21", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096676741000, "endTime": 34096678546500, "totalTime": 1533600}, "additional": {"logType": "info", "children": [], "durationId": "bc140f8d-d57c-4d18-acd8-85e8d64c568a"}}, {"head": {"id": "5a746ffa-09af-4ed0-baca-aeebc23173a8", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096685353700, "endTime": 34096695456200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1013a6d4-e296-4f3b-bc18-78e944e72c52", "logId": "3fa50baa-6636-40b6-8f37-8aa8832c48c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1013a6d4-e296-4f3b-bc18-78e944e72c52", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096681651000}, "additional": {"logType": "detail", "children": [], "durationId": "5a746ffa-09af-4ed0-baca-aeebc23173a8"}}, {"head": {"id": "8d8f537d-c936-4a4e-9ebf-076f147796e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096681989200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0e54680-5a5a-485d-87f9-de99d9759338", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096682090800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364d4cc5-77d5-4533-a9c0-0ee3e5e30ece", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096683183600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d7f3f7-22ca-4586-8e78-29c769ebf64e", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096689686600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc4e830f-3d31-401b-86c4-7d30516711e6", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096693639300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c04bba2-c049-435f-9e73-032cd017965f", "name": "entry : default@ProcessResource cost memory 0.172882080078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096693774200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fa50baa-6636-40b6-8f37-8aa8832c48c9", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096685353700, "endTime": 34096695456200}, "additional": {"logType": "info", "children": [], "durationId": "5a746ffa-09af-4ed0-baca-aeebc23173a8"}}, {"head": {"id": "245fb1b5-023a-485a-833c-b949a9436ee0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096703382500, "endTime": 34096716835100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ca96eebc-6f26-4a89-802d-e378bc85957d", "logId": "74b52eb5-6090-4331-9825-c0215f32762e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca96eebc-6f26-4a89-802d-e378bc85957d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096699977600}, "additional": {"logType": "detail", "children": [], "durationId": "245fb1b5-023a-485a-833c-b949a9436ee0"}}, {"head": {"id": "1041e7b1-8f17-4c15-89c1-0c5821f54d9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096700381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5846bb4-647b-4bf7-8558-7cfe1bbc8dac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096700480900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bef23746-152c-4c67-960c-791f3e0b9a96", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096703394800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c653d987-7482-428a-8706-be0cf29f464a", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096716567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db2b572d-6b72-449f-bfaf-1db69d402f1f", "name": "entry : default@GenerateLoaderJson cost memory 0.7633743286132812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096716754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74b52eb5-6090-4331-9825-c0215f32762e", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096703382500, "endTime": 34096716835100}, "additional": {"logType": "info", "children": [], "durationId": "245fb1b5-023a-485a-833c-b949a9436ee0"}}, {"head": {"id": "1f07e536-6bf8-4c4d-9766-c384481a895c", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096726446200, "endTime": 34096730777500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4b809aca-e44a-40dd-a09c-7625b5aaa028", "logId": "1e258518-b8b9-48ac-970e-d085647f068a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b809aca-e44a-40dd-a09c-7625b5aaa028", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096724741100}, "additional": {"logType": "detail", "children": [], "durationId": "1f07e536-6bf8-4c4d-9766-c384481a895c"}}, {"head": {"id": "e7dda450-3e7e-4100-b83d-7431ee888a48", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096725295900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c291b60-0271-440c-84d6-18249c9cc84d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096725407300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "845aa459-d36e-4c76-ae78-e956d4165a23", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096726463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499004f3-2eaa-41b1-adf3-15c8e54ad7d7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096729345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d04b9c-8c71-415b-b6a5-bccb9ba01b57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096729456300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b819779a-39da-4397-80c3-96990c260333", "name": "entry : default@ProcessLibs cost memory 0.1263275146484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096730085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b1348d-bcf4-4a06-b31a-ebde228fe9e0", "name": "runTaskFromQueue task cost before running: 474 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096730654200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e258518-b8b9-48ac-970e-d085647f068a", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096726446200, "endTime": 34096730777500, "totalTime": 4131900}, "additional": {"logType": "info", "children": [], "durationId": "1f07e536-6bf8-4c4d-9766-c384481a895c"}}, {"head": {"id": "f16752bf-f543-4efb-a14a-0ffdf17f0ec7", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096736969500, "endTime": 34096759894300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "18edc6f5-998b-49be-973f-c322cc513162", "logId": "bc00a1a9-6f6d-4973-87ba-3cb77dd0817a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18edc6f5-998b-49be-973f-c322cc513162", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096733194900}, "additional": {"logType": "detail", "children": [], "durationId": "f16752bf-f543-4efb-a14a-0ffdf17f0ec7"}}, {"head": {"id": "3eb6276f-d9ce-4974-803e-cf1bd313dc5f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096733645100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a322dfaa-ea69-41db-81d8-8aa7eac6bca8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096733756200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1189b71c-5dc8-4c4f-9a57-d0cb51cae4a0", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096734522400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf0c2364-2ac0-4ca9-b1d9-85b2451fe184", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096736996900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9796c00c-f099-437c-ba5e-9c17597efd08", "name": "Incremental task entry:default@CompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096759690000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2d103b-8877-41ab-939b-1e665e376a31", "name": "entry : default@CompileResource cost memory -4.420890808105469", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096759815700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc00a1a9-6f6d-4973-87ba-3cb77dd0817a", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096736969500, "endTime": 34096759894300}, "additional": {"logType": "info", "children": [], "durationId": "f16752bf-f543-4efb-a14a-0ffdf17f0ec7"}}, {"head": {"id": "6406066a-3b2d-43ba-bfce-0bbc44e6c189", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096766413000, "endTime": 34096767629400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a3d1b6ca-cd09-4f15-b0d2-11eeddcf359b", "logId": "4819db4c-364b-4a94-ae77-ce384d111c3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3d1b6ca-cd09-4f15-b0d2-11eeddcf359b", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096763318000}, "additional": {"logType": "detail", "children": [], "durationId": "6406066a-3b2d-43ba-bfce-0bbc44e6c189"}}, {"head": {"id": "3fca0b13-8164-4637-88b1-13bf74b50c3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096763692800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1386047-128a-47cd-a719-90130a900909", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096763817200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4120f58a-4c9a-4fdb-92e3-75b94a308ed7", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096766425300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0186d28-1e3a-4123-a956-128f62ab2679", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096766720000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "944c3ee9-1ef6-4234-9507-35d4cc8c7c65", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096767472100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9096ce3-1f7d-409c-9745-fd647ed6efb9", "name": "entry : default@DoNativeStrip cost memory 0.07361602783203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096767552800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4819db4c-364b-4a94-ae77-ce384d111c3b", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096766413000, "endTime": 34096767629400}, "additional": {"logType": "info", "children": [], "durationId": "6406066a-3b2d-43ba-bfce-0bbc44e6c189"}}, {"head": {"id": "cacced82-7110-4c34-803e-04308d39a5d2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096774085900, "endTime": 34098438976700}, "additional": {"children": ["ff9dce6b-9462-415f-a5d5-6378f226188c", "29e20691-8350-4b72-9c1e-12366fca733a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "bfed03ea-3354-4fa8-9a37-29a1b380a9ee", "logId": "075fee08-b168-4c37-8896-e4fecc92f366"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfed03ea-3354-4fa8-9a37-29a1b380a9ee", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096768984400}, "additional": {"logType": "detail", "children": [], "durationId": "cacced82-7110-4c34-803e-04308d39a5d2"}}, {"head": {"id": "f9dbcb23-02f4-472c-99ce-4ac8b4e3285f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096769309300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69a1e122-6792-48b2-ad7c-41cf760bd02a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096769403500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b7ed01-e579-4c0b-b69b-bda0b96d6c03", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096774098100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0476309-7cf7-4fb1-adb8-e35a0e96ed05", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096791676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e0795b6-9c56-447b-bdcf-0554784219d0", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096791838400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6bfe48-828e-4416-a22b-afc64ef67484", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096807389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "743567b6-8049-453f-9f1c-8098b5ad1ee2", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096808374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab7c8c54-d097-45db-a971-ef478fead241", "name": "default@CompileArkTS work[115] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096812924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9dce6b-9462-415f-a5d5-6378f226188c", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34096917277200, "endTime": 34098431705300}, "additional": {"children": ["90b957d9-5a7a-4230-b702-db09a764519d", "bf3e919f-c4f9-4fb2-b7a2-b79c184f867e", "355a7300-5f79-41a8-8c2b-ce348dab19f6", "a394aaec-a90c-49b5-b568-b3b3e4a4b577", "3ce786f7-3123-41d2-b8ae-ab34b7e461bf", "3f527e63-afa3-46a9-83c3-956b3bf6a005"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cacced82-7110-4c34-803e-04308d39a5d2", "logId": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1496c453-4391-481f-a258-4e5a5dd24880", "name": "default@CompileArkTS work[115] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096813810700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0253dd93-6435-4288-af0e-999f7a2d235c", "name": "default@CompileArkTS work[115] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096813920000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10fb43b0-1385-4178-8010-6951592a4672", "name": "CopyResources startTime: 34096813978700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096813980700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28e82608-c9eb-4c5c-b9a7-87576d178e2f", "name": "default@CompileArkTS work[116] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096814031700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e20691-8350-4b72-9c1e-12366fca733a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34097974362100, "endTime": 34097997851200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "cacced82-7110-4c34-803e-04308d39a5d2", "logId": "19bc3ea3-bf13-4901-9307-3d3c380f998a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c70fc79-156c-4acf-90fc-1766953e2ced", "name": "default@CompileArkTS work[116] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096814654900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f724db6-326d-4004-8e07-c275b5f8e012", "name": "default@CompileArkTS work[116] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096814729900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5faa42-94e0-44c2-a6ad-b76f9d6481f6", "name": "entry : default@CompileArkTS cost memory 1.5858154296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096814879000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4960bde-4416-4ea6-9aca-2507cb5fa97d", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096820354700, "endTime": 34096824668400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2685b364-951a-4e56-8437-df7ca8ef92da", "logId": "d7ede06a-af7a-467e-9cc6-c8543fce4ea7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2685b364-951a-4e56-8437-df7ca8ef92da", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096816590800}, "additional": {"logType": "detail", "children": [], "durationId": "f4960bde-4416-4ea6-9aca-2507cb5fa97d"}}, {"head": {"id": "7b3ee0e5-7678-4512-9c36-19ea0991e265", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096816997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0497cdb8-59ad-4537-920a-186ae2db15d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096817101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b8444de-1eec-4da2-87a9-c96600fee701", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096820366400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7acc9b83-c207-4232-808c-aeb9938eb530", "name": "entry : default@BuildJS cost memory 0.12703704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096824305900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f14c2d0-280c-4139-ad85-6a9bdf573d9a", "name": "runTaskFromQueue task cost before running: 568 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096824547800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ede06a-af7a-467e-9cc6-c8543fce4ea7", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096820354700, "endTime": 34096824668400, "totalTime": 4148200}, "additional": {"logType": "info", "children": [], "durationId": "f4960bde-4416-4ea6-9aca-2507cb5fa97d"}}, {"head": {"id": "1884fecb-2a75-4466-ba85-5394a14aeb34", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096830080700, "endTime": 34096833273800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "57d0f624-e66b-404f-9634-d65312d92730", "logId": "a103208a-2b3a-47f5-afd4-881cfff583a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57d0f624-e66b-404f-9634-d65312d92730", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096826558500}, "additional": {"logType": "detail", "children": [], "durationId": "1884fecb-2a75-4466-ba85-5394a14aeb34"}}, {"head": {"id": "1b29c569-5665-4c9c-be12-dbda85170b8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096826976500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d1fd39-e55c-4a7e-a2f6-645de3e914d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096827096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70fdabb2-c1d6-4f72-961f-2498c7478c51", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096830092700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a227737-f34a-4386-b427-0fb8d0fc6c1b", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096830537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "befdd566-a2e0-47d6-83f7-88a40135b1db", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096832651200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "654a0fa3-528a-462f-b4bd-b8e15b260daf", "name": "entry : default@CacheNativeLibs cost memory 0.088043212890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096833029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a103208a-2b3a-47f5-afd4-881cfff583a1", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096830080700, "endTime": 34096833273800}, "additional": {"logType": "info", "children": [], "durationId": "1884fecb-2a75-4466-ba85-5394a14aeb34"}}, {"head": {"id": "5a89e632-ec25-4168-b53b-c3aa02cdb692", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096916541200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e10eab84-2bc1-4101-9911-5d776d0047d3", "name": "default@CompileArkTS work[115] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096917016300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e1f1f6-8a68-48c7-bff9-e1cc27e7fe96", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096917157300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2339078d-0c73-4603-8834-94b36faaab99", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096917330400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a799e8-17a2-4660-ad35-b055fce5eec3", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096917592700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1602dfb2-6655-499f-85d3-c147cbe927ef", "name": "default@CompileArkTS work[116] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096919947300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11cdcf0d-000d-49bb-9e59-a10fe987f90c", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34097998549900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f4f77f-9c69-4203-a7c6-f95929eec1e6", "name": "CopyResources is end, endTime: 34097998815100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34097998823100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "193ca5d5-7bf7-4fd7-916c-4a7e3105005b", "name": "default@CompileArkTS work[116] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34097998972900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19bc3ea3-bf13-4901-9307-3d3c380f998a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34097974362100, "endTime": 34097997851200}, "additional": {"logType": "info", "children": [], "durationId": "29e20691-8350-4b72-9c1e-12366fca733a", "parent": "075fee08-b168-4c37-8896-e4fecc92f366"}}, {"head": {"id": "28eb924c-e3b9-4819-b30c-a3a612e5f046", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34097999150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eeb69ea-9eb8-461c-825c-f49a65fddc2b", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098432008700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b957d9-5a7a-4230-b702-db09a764519d", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34096917360300, "endTime": 34096921053300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ff9dce6b-9462-415f-a5d5-6378f226188c", "logId": "21625d00-837d-4744-9f3b-3641d1c1ad70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21625d00-837d-4744-9f3b-3641d1c1ad70", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096917360300, "endTime": 34096921053300}, "additional": {"logType": "info", "children": [], "durationId": "90b957d9-5a7a-4230-b702-db09a764519d", "parent": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}}, {"head": {"id": "bf3e919f-c4f9-4fb2-b7a2-b79c184f867e", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34096921068500, "endTime": 34096921171400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ff9dce6b-9462-415f-a5d5-6378f226188c", "logId": "09732ee2-446a-4929-85fb-d8b3fdfdb286"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09732ee2-446a-4929-85fb-d8b3fdfdb286", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096921068500, "endTime": 34096921171400}, "additional": {"logType": "info", "children": [], "durationId": "bf3e919f-c4f9-4fb2-b7a2-b79c184f867e", "parent": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}}, {"head": {"id": "355a7300-5f79-41a8-8c2b-ce348dab19f6", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34096921183000, "endTime": 34096921219600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ff9dce6b-9462-415f-a5d5-6378f226188c", "logId": "3fcfa8e4-e281-4bb5-8b72-dd4686a7cde5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fcfa8e4-e281-4bb5-8b72-dd4686a7cde5", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096921183000, "endTime": 34096921219600}, "additional": {"logType": "info", "children": [], "durationId": "355a7300-5f79-41a8-8c2b-ce348dab19f6", "parent": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}}, {"head": {"id": "a394aaec-a90c-49b5-b568-b3b3e4a4b577", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34096921236900, "endTime": 34098341132100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ff9dce6b-9462-415f-a5d5-6378f226188c", "logId": "3a8b8005-a76c-401f-8085-1ed8e8c10710"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a8b8005-a76c-401f-8085-1ed8e8c10710", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096921236900, "endTime": 34098341132100}, "additional": {"logType": "info", "children": [], "durationId": "a394aaec-a90c-49b5-b568-b3b3e4a4b577", "parent": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}}, {"head": {"id": "3ce786f7-3123-41d2-b8ae-ab34b7e461bf", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098341153300, "endTime": 34098346757700}, "additional": {"children": ["154482b2-40f6-4ad0-aecf-0003f68ac3a8", "3aeb7af6-5adb-46f5-8c81-2b51a5fcc814", "59be0a6b-d30a-462c-8466-b1353d5eda95"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ff9dce6b-9462-415f-a5d5-6378f226188c", "logId": "9ea22335-de8c-45c3-bcca-1abb0bf2a994"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ea22335-de8c-45c3-bcca-1abb0bf2a994", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098341153300, "endTime": 34098346757700}, "additional": {"logType": "info", "children": ["542e66f2-ed01-4df1-8514-d6437123effd", "37e606cb-da36-44a9-8c5a-d88a35aec929", "3b005e5a-ba50-4216-a6ce-104a47d2ef30"], "durationId": "3ce786f7-3123-41d2-b8ae-ab34b7e461bf", "parent": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}}, {"head": {"id": "154482b2-40f6-4ad0-aecf-0003f68ac3a8", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098341168000, "endTime": 34098341173400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3ce786f7-3123-41d2-b8ae-ab34b7e461bf", "logId": "542e66f2-ed01-4df1-8514-d6437123effd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "542e66f2-ed01-4df1-8514-d6437123effd", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098341168000, "endTime": 34098341173400}, "additional": {"logType": "info", "children": [], "durationId": "154482b2-40f6-4ad0-aecf-0003f68ac3a8", "parent": "9ea22335-de8c-45c3-bcca-1abb0bf2a994"}}, {"head": {"id": "3aeb7af6-5adb-46f5-8c81-2b51a5fcc814", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098341176400, "endTime": 34098343939000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3ce786f7-3123-41d2-b8ae-ab34b7e461bf", "logId": "37e606cb-da36-44a9-8c5a-d88a35aec929"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37e606cb-da36-44a9-8c5a-d88a35aec929", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098341176400, "endTime": 34098343939000}, "additional": {"logType": "info", "children": [], "durationId": "3aeb7af6-5adb-46f5-8c81-2b51a5fcc814", "parent": "9ea22335-de8c-45c3-bcca-1abb0bf2a994"}}, {"head": {"id": "59be0a6b-d30a-462c-8466-b1353d5eda95", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098343943200, "endTime": 34098346745400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3ce786f7-3123-41d2-b8ae-ab34b7e461bf", "logId": "3b005e5a-ba50-4216-a6ce-104a47d2ef30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b005e5a-ba50-4216-a6ce-104a47d2ef30", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098343943200, "endTime": 34098346745400}, "additional": {"logType": "info", "children": [], "durationId": "59be0a6b-d30a-462c-8466-b1353d5eda95", "parent": "9ea22335-de8c-45c3-bcca-1abb0bf2a994"}}, {"head": {"id": "3f527e63-afa3-46a9-83c3-956b3bf6a005", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098346773900, "endTime": 34098431565100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ff9dce6b-9462-415f-a5d5-6378f226188c", "logId": "66d5b1b3-c7df-4b0b-8fba-a639d4d18769"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66d5b1b3-c7df-4b0b-8fba-a639d4d18769", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098346773900, "endTime": 34098431565100}, "additional": {"logType": "info", "children": [], "durationId": "3f527e63-afa3-46a9-83c3-956b3bf6a005", "parent": "5383ce9b-0e05-49e5-9110-ecef50f833c6"}}, {"head": {"id": "b2652ca6-b191-42dd-9253-0cf2ae9e0823", "name": "default@CompileArkTS work[115] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098438686200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5383ce9b-0e05-49e5-9110-ecef50f833c6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34096917277200, "endTime": 34098431705300}, "additional": {"logType": "info", "children": ["21625d00-837d-4744-9f3b-3641d1c1ad70", "09732ee2-446a-4929-85fb-d8b3fdfdb286", "3fcfa8e4-e281-4bb5-8b72-dd4686a7cde5", "3a8b8005-a76c-401f-8085-1ed8e8c10710", "9ea22335-de8c-45c3-bcca-1abb0bf2a994", "66d5b1b3-c7df-4b0b-8fba-a639d4d18769"], "durationId": "ff9dce6b-9462-415f-a5d5-6378f226188c", "parent": "075fee08-b168-4c37-8896-e4fecc92f366"}}, {"head": {"id": "dfbd7750-b9d4-4712-a193-e7317f10541f", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098438870800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "075fee08-b168-4c37-8896-e4fecc92f366", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096774085900, "endTime": 34098438976700, "totalTime": 1555280800}, "additional": {"logType": "info", "children": ["5383ce9b-0e05-49e5-9110-ecef50f833c6", "19bc3ea3-bf13-4901-9307-3d3c380f998a"], "durationId": "cacced82-7110-4c34-803e-04308d39a5d2"}}, {"head": {"id": "24340964-036f-421b-8222-7bb4c6d59a84", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098444710100, "endTime": 34098445753700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "49e2674a-fe18-4306-b37c-234d348dcf65", "logId": "1e3a4756-bc08-48c3-bd10-d37e181f39a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49e2674a-fe18-4306-b37c-234d348dcf65", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098443305600}, "additional": {"logType": "detail", "children": [], "durationId": "24340964-036f-421b-8222-7bb4c6d59a84"}}, {"head": {"id": "e3d25529-30e2-4cd4-ae21-bdfa9bce3929", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098443660100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74be7033-ef64-4c6e-839b-abcab43a03a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098443756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dbeaa73-44cf-4bea-81fd-26aa2b11cf18", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098444719000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55e4cdf-be27-4cd5-9173-f736a3b1198c", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098444922600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5da816-454b-43ba-85dc-6339be4225d9", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098445571300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbb6af1-3eeb-4c43-912a-3d9000470772", "name": "entry : default@GeneratePkgModuleJson cost memory 0.074981689453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098445676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3a4756-bc08-48c3-bd10-d37e181f39a2", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098444710100, "endTime": 34098445753700}, "additional": {"logType": "info", "children": [], "durationId": "24340964-036f-421b-8222-7bb4c6d59a84"}}, {"head": {"id": "7cf34c46-4399-4e97-bd1a-848f7a552a3b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098455124300, "endTime": 34098897009400}, "additional": {"children": ["c373a2d8-a2cd-4575-a5c2-f6bfe090b8c5", "0c3a0da6-9f49-41d9-871d-6f83efe968de", "4aebb261-e1c0-4209-bc5c-2e9edba519e3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "93c7c82f-35b6-4bc8-bc4c-b605972e429d", "logId": "addc422f-983c-4bc7-851e-1a2328083432"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93c7c82f-35b6-4bc8-bc4c-b605972e429d", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098447556700}, "additional": {"logType": "detail", "children": [], "durationId": "7cf34c46-4399-4e97-bd1a-848f7a552a3b"}}, {"head": {"id": "de91880b-e190-4d7e-9250-8a03ebdef323", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098447853200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd259afe-8751-45d4-859b-fb8469facf8f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098447940200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e28d002-0712-4571-8562-b991e382d80d", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098455137600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba3cd53-ead7-48a1-8f6c-83909fc4688d", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098468006300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de13bfc9-b171-4310-b57c-6f705ae71717", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098468155700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bf0423-5057-434e-a71b-ed75a501d8ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098468246200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2b9ff60-dec0-4789-ad83-b0109e6a6b2d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098468298900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c373a2d8-a2cd-4575-a5c2-f6bfe090b8c5", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098469312000, "endTime": 34098470820200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cf34c46-4399-4e97-bd1a-848f7a552a3b", "logId": "f7206ad2-8cd2-4a03-a3e0-166e61dcf5de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7ac1121-77e1-4bdc-91d8-016b61b1d94f", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098470676700}, "additional": {"logType": "debug", "children": [], "durationId": "7cf34c46-4399-4e97-bd1a-848f7a552a3b"}}, {"head": {"id": "f7206ad2-8cd2-4a03-a3e0-166e61dcf5de", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098469312000, "endTime": 34098470820200}, "additional": {"logType": "info", "children": [], "durationId": "c373a2d8-a2cd-4575-a5c2-f6bfe090b8c5", "parent": "addc422f-983c-4bc7-851e-1a2328083432"}}, {"head": {"id": "0c3a0da6-9f49-41d9-871d-6f83efe968de", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098471350900, "endTime": 34098473810700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7cf34c46-4399-4e97-bd1a-848f7a552a3b", "logId": "ab435a53-e0e3-44d1-b190-c0f244dd3cad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4736243a-5b48-4a65-95d3-750ac9617a82", "name": "default@PackageHap work[117] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098472303900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aebb261-e1c0-4209-bc5c-2e9edba519e3", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098473676900, "endTime": 34098896551100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7cf34c46-4399-4e97-bd1a-848f7a552a3b", "logId": "816bbc32-2bed-4cd4-afdc-ce11952eb8b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c935859-cb6d-4cb6-8aaa-ec0bb4135d1a", "name": "default@PackageHap work[117] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098473373800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b6a2fe1-c7a3-4d48-ae83-48a4b643b423", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098473472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e628efe8-d450-44f0-b841-67f995ef1759", "name": "default@PackageHap work[117] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098473570500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "301fac49-2a63-41d0-ace2-1535978ef705", "name": "default@PackageHap work[117] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098473678800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab435a53-e0e3-44d1-b190-c0f244dd3cad", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098471350900, "endTime": 34098473810700}, "additional": {"logType": "info", "children": [], "durationId": "0c3a0da6-9f49-41d9-871d-6f83efe968de", "parent": "addc422f-983c-4bc7-851e-1a2328083432"}}, {"head": {"id": "de4a971f-41f7-4c25-bd37-1feafe523a5c", "name": "entry : default@PackageHap cost memory 1.385498046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098477593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad69efb-329a-4413-8238-f807085d3970", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098896667900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234996f0-8774-4ff6-9d12-00063afd68f6", "name": "default@PackageHap work[117] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098896858800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "816bbc32-2bed-4cd4-afdc-ce11952eb8b2", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34098473676900, "endTime": 34098896551100}, "additional": {"logType": "info", "children": [], "durationId": "4aebb261-e1c0-4209-bc5c-2e9edba519e3", "parent": "addc422f-983c-4bc7-851e-1a2328083432"}}, {"head": {"id": "959c0c10-9563-4233-ad1d-a18adf7e7e9c", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098896943900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addc422f-983c-4bc7-851e-1a2328083432", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098455124300, "endTime": 34098897009400, "totalTime": 441429300}, "additional": {"logType": "info", "children": ["f7206ad2-8cd2-4a03-a3e0-166e61dcf5de", "ab435a53-e0e3-44d1-b190-c0f244dd3cad", "816bbc32-2bed-4cd4-afdc-ce11952eb8b2"], "durationId": "7cf34c46-4399-4e97-bd1a-848f7a552a3b"}}, {"head": {"id": "1e97fc07-bb4f-40cd-924c-9bc81a25b992", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098902522700, "endTime": 34098904210600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "58affb3f-cce3-49de-b280-e409786350a5", "logId": "1717ba16-dd5e-409b-9c9c-a236670a5d0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58affb3f-cce3-49de-b280-e409786350a5", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098899599900}, "additional": {"logType": "detail", "children": [], "durationId": "1e97fc07-bb4f-40cd-924c-9bc81a25b992"}}, {"head": {"id": "1ec15606-32d7-41fc-82d4-1c618a02de40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098900012700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b89810da-34a3-4851-806b-f24454c533cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098900224700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53973209-90f6-4f98-b3db-6fd1b2ba4791", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098902532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "062d7da7-57f3-4fbb-8ccd-6795db142402", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098902889200}, "additional": {"logType": "warn", "children": [], "durationId": "1e97fc07-bb4f-40cd-924c-9bc81a25b992"}}, {"head": {"id": "2afb0f9b-ce35-4199-9404-07a97e8097fa", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098903421100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a632295e-9779-46db-ad0b-b80be9ded9e7", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098903515500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c1195e3-4162-4a57-ae6e-181c6a7ca03b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098903596500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8514fd72-0cb0-4ed4-9899-3d5dee82ae8f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098903720800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6402872-1786-48ef-856e-18470677faa7", "name": "entry : default@SignHap cost memory 0.11476898193359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098904020100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1dd5810-20d1-497b-b3b3-9befdef2dfa5", "name": "runTaskFromQueue task cost before running: 2 s 647 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098904144000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1717ba16-dd5e-409b-9c9c-a236670a5d0a", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098902522700, "endTime": 34098904210600, "totalTime": 1596600}, "additional": {"logType": "info", "children": [], "durationId": "1e97fc07-bb4f-40cd-924c-9bc81a25b992"}}, {"head": {"id": "86db0d29-f419-409d-b6e2-c6fc2368f89f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098907899500, "endTime": 34098912504100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7a66f208-aba9-4bda-bbbc-65f73554bf80", "logId": "aec01298-f4a0-467b-b489-da1afcf3b8f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a66f208-aba9-4bda-bbbc-65f73554bf80", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098906636400}, "additional": {"logType": "detail", "children": [], "durationId": "86db0d29-f419-409d-b6e2-c6fc2368f89f"}}, {"head": {"id": "abe9adaa-cda6-4da9-8c60-728e1c999aee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098907096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424fbd60-4874-4884-818b-5103101bcf66", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098907213300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b18f02-8480-472f-88aa-3adbc14f3498", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098907908300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ca8fe1-e04d-4c79-9d6b-f9cb11fdb683", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098912048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09fed9e-a52a-4477-b68a-3a7fc89a6655", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098912259800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec7729f-1c99-4655-8ad1-2e46462d9284", "name": "entry : default@CollectDebugSymbol cost memory 0.23958587646484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098912359000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40e74607-5d86-4d92-b40d-f4b708564d65", "name": "runTaskFromQueue task cost before running: 2 s 656 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098912443400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec01298-f4a0-467b-b489-da1afcf3b8f1", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098907899500, "endTime": 34098912504100, "totalTime": 4523300}, "additional": {"logType": "info", "children": [], "durationId": "86db0d29-f419-409d-b6e2-c6fc2368f89f"}}, {"head": {"id": "6f7183b9-5794-4ebd-b52c-656714fb60ce", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098914015700, "endTime": 34098914278800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "eba47b53-3633-4a83-9475-ead0072d4f0d", "logId": "a540199e-4998-4298-8a06-108fc12f1e30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eba47b53-3633-4a83-9475-ead0072d4f0d", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098913975800}, "additional": {"logType": "detail", "children": [], "durationId": "6f7183b9-5794-4ebd-b52c-656714fb60ce"}}, {"head": {"id": "5f693c93-40ba-40af-a372-53c115400ed6", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098914041400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af57f6e8-a75a-47dc-8fcb-2fba628b0821", "name": "entry : assembleHap cost memory 0.01140594482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098914149900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a16fdb5-915a-4f5a-93f2-7660dbf8fb0b", "name": "runTaskFromQueue task cost before running: 2 s 657 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098914223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a540199e-4998-4298-8a06-108fc12f1e30", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098914015700, "endTime": 34098914278800, "totalTime": 191400}, "additional": {"logType": "info", "children": [], "durationId": "6f7183b9-5794-4ebd-b52c-656714fb60ce"}}, {"head": {"id": "820fdd27-649d-41be-ad26-66f539898073", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098922692700, "endTime": 34098922725500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "57df957a-fb93-4877-9ce0-eefcaa871df4", "logId": "60b9b1ed-d7a7-42b0-b5fd-96e87e137775"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60b9b1ed-d7a7-42b0-b5fd-96e87e137775", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098922692700, "endTime": 34098922725500}, "additional": {"logType": "info", "children": [], "durationId": "820fdd27-649d-41be-ad26-66f539898073"}}, {"head": {"id": "55c782b9-ebe7-4723-b4e6-7b9d9aa76855", "name": "BUILD SUCCESSFUL in 2 s 666 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098922788600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "ce062bb9-0eb5-43c0-b0ff-77682d9e3112", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34096257237300, "endTime": 34098923064700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 57}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "bf631c7a-55af-4247-abd6-c72a95d38914", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098923099200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ccbcd89-86bb-4d7b-8ef2-b9c3c92afa25", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098923209400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803b2689-3b4c-47f2-9c43-f77d092c35ac", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098923312900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a72ca7f8-f012-4bb8-a3c3-eae90ac3a71b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098923373200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5689d6-3376-4332-8ece-58abcc80b69f", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098923438900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec4e7c1-725f-4682-973d-6293b814d0d8", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098923793000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c46189f-2bb3-4f1c-9a62-bfeb1fcb8f91", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098924386800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e797308-5db0-4a6e-9a70-dfca958831bb", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098924643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ffd62b-4936-41e3-8a45-0cdb12c803db", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098924722200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9637133e-c00e-43e5-94ba-5fde3caa32f5", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098924783000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "524137f2-ca81-4688-a6fe-c12119cf6eaa", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098925016100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2873e503-f8bb-4d9c-8b62-23bf741b39aa", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098925980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f056429-308c-45ba-b6f1-1a706644b032", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098926320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00277d61-ae8f-4fa2-bd67-75470de6b002", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098926403500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68498f8-8bd2-4045-abfa-7c4948b794d7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098926460200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba32eb8-5626-4db2-8dcf-b2be52e300fb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098926511200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "962c152b-2305-4770-bc9e-f359bb09987d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098926560400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e020d7-9b5e-4a22-83e2-37c1992f1d8e", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098926886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5337d5-f5da-4beb-bad1-4578243eb4f5", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098927112200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "132b98a1-6e99-415a-bd25-20a6f92c5b7e", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098927308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695e231f-2f1f-4431-bcff-3ab953034da1", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098927550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be1a8cd-ef5a-4c32-b84e-19296920a167", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098927623100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1e933c6-221f-449b-884d-3de49f0d632d", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098927675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa73b20-a787-4b09-ab95-9c06e7b89643", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098929494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc83f7b-70be-4527-b9df-a45955a6cefa", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098930000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "248688e0-5782-4821-a98e-c35073ab844f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098930749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "603c9c19-7e2c-45f5-9738-45c3d67ff783", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098930979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4648ee52-9910-4e4f-92b7-1d0639e51ed3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098931361000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "135eac7c-0284-4c08-bb7e-a4016ef8e94e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098932061000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f47588-65b9-472e-bce2-40a2470ec6f7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098932160800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40592f15-278a-4d95-ab0b-9c4cfe5954e3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098932355100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "065d46a5-57e1-410d-b59f-fb011a8c652f", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098932575100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6aa66a2-119f-43f8-820b-d0cd6e997db0", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098933103000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1800d7e-c137-47f8-8801-704d4b85d3b8", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098934087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d79517-7dbe-4114-aee3-ed0b1aba19d2", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098934516800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2976b03c-64dc-4b61-b0d5-1d605733b97e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098935356800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03406738-722c-435a-a490-97a5153d777d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098935562800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "952f86b5-8fcc-47f8-8654-5371fc2c892b", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098935751400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d33734e-373f-4fa2-a093-2a4c9bbd773d", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098936434100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26f2e92d-a6a0-422a-b346-405766e60d9d", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098936725900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "262d7180-c7bb-4040-bb95-657be55eb930", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098936801700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c959b3e6-3d57-4d9f-a532-f04428a773d5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098936852700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6a94de-13ed-4e84-a766-60f25728f92b", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098937714000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7901c0bf-4101-4a61-af3e-60a89c375ba7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098937982500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4c9f354-4e70-47f8-b0da-0333b3c71e7d", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098938202900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d798c9-f313-42ab-a41e-d65508bc396f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098944414300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ab10189-0518-4ccb-9a11-d21afbe45e39", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098944651100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3099e2a-918e-4e7c-a6e4-53365472f051", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098944839300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbae11f-ed27-49df-a56a-916e03d28d4f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098944906000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a92ed8c-9a4b-4355-b6d5-f708c0cc4511", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098945079700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15390067-4b5e-41c5-894a-999e774bfbfe", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098945717500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5fd5bd-46da-4020-9c28-1d43378cc842", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098945982600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bd4ad48-fe23-43e6-bd65-dc95ddad150e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098946272700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f6772a-3ab3-46de-aaac-d6340606a2f3", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098946592400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36321f67-8b5d-4337-b1f2-755d29e1255d", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098947077300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ec20f2-231a-42f5-9454-3eb5f7d6794c", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098947313100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c7d31b-4e61-4938-8906-330234b44a64", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098947598400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ee921a-c27d-46d1-808d-5bf97b201f0d", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098949722200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c51bca6-2b0e-4e21-a972-9f1cf79488f7", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098949962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4de430-e3f8-4eaa-b2df-d277dec17861", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098950241900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "187ae670-521a-48ff-83b0-59fdeaf2c458", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34098950510900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}