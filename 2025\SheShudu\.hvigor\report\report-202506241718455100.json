{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "3cd6d439-9a4b-44af-aaed-178fb3651184", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31438011510300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d9916eb-f6a0-428b-ad81-39e3d6f9e1d5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31438015242800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a486135-53a6-4245-bd04-368a3c2a7d80", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31438015472600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0570d346-3768-49ab-91b9-19dd661e93eb", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31438024623700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116b4f9a-8530-4470-bc45-8ca1241be44e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757403937400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f1ae06-f813-4c82-a12b-d0550241416c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757409639700, "endTime": 31757705214000}, "additional": {"children": ["deea28a7-d468-47c7-8ddc-7c5073c6407d", "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "7e1c4e40-b1ad-4a85-a2a9-8b4859c47a06", "d3a130cc-e1dc-4ffb-9a8b-287500a9288f", "af9f4925-c4c7-4b74-bb2e-73a17f4e9e08", "94507d8c-2c85-4ac0-92ab-93457b6d6eda", "d5712f2e-fb8b-435a-bbef-cbec1b11da8d"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "74d3a917-9f85-4f85-9831-131ebc944ea0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "deea28a7-d468-47c7-8ddc-7c5073c6407d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757409641100, "endTime": 31757423293000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "2c104155-bd9c-4d26-bd35-11f649cf05b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757423320100, "endTime": 31757703159800}, "additional": {"children": ["ba5335b9-27ac-44e9-8ce6-3475e61bf54f", "ba186ed4-33fe-457c-bb36-79c325961dc8", "72d264ab-7489-44d3-a90d-04ed9dfd32be", "3e4e7b87-3a46-4f00-a02a-296f7cfc8f17", "182d1f00-0fe3-4c79-aefd-9f2e713e0ceb", "ca31c749-0669-4c9d-8a4b-0bea80e0c7d2", "5bdc393f-4070-4d7f-8df5-b83bd02011bb", "818239d2-2973-4aef-b08f-3f0efb183297", "1f0d4287-8220-454f-9d7a-5f0514b7d294"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "143fbc9d-8d18-4179-a1df-b061baf980fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e1c4e40-b1ad-4a85-a2a9-8b4859c47a06", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757703181400, "endTime": 31757705205500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "7f5348dc-8a5f-46e0-8923-4d7b1966746e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3a130cc-e1dc-4ffb-9a8b-287500a9288f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757705210300, "endTime": 31757705211200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "5d717504-ac26-433f-9fce-bd27ecd8e90e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af9f4925-c4c7-4b74-bb2e-73a17f4e9e08", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757412316400, "endTime": 31757412357300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "9d9c43bc-1692-45d9-96b0-53c59a7f6f0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d9c43bc-1692-45d9-96b0-53c59a7f6f0d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757412316400, "endTime": 31757412357300}, "additional": {"logType": "info", "children": [], "durationId": "af9f4925-c4c7-4b74-bb2e-73a17f4e9e08", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "94507d8c-2c85-4ac0-92ab-93457b6d6eda", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757418809500, "endTime": 31757418833800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "3ea6f3d8-a53b-4428-a2f8-c7a53d2747bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ea6f3d8-a53b-4428-a2f8-c7a53d2747bd", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757418809500, "endTime": 31757418833800}, "additional": {"logType": "info", "children": [], "durationId": "94507d8c-2c85-4ac0-92ab-93457b6d6eda", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "bf89e0b1-448a-4545-9a6a-32b367f1a99a", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757418888000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473c5831-a895-4e01-92c5-25d160e2d6f3", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757423142800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c104155-bd9c-4d26-bd35-11f649cf05b9", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757409641100, "endTime": 31757423293000}, "additional": {"logType": "info", "children": [], "durationId": "deea28a7-d468-47c7-8ddc-7c5073c6407d", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "ba5335b9-27ac-44e9-8ce6-3475e61bf54f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757429021200, "endTime": 31757429030800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "5ab8c009-01b1-4256-bac0-c6c4480cd930"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba186ed4-33fe-457c-bb36-79c325961dc8", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757429045600, "endTime": 31757433575200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "1886cd9e-af7e-41bd-961b-29ab53b6a58d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72d264ab-7489-44d3-a90d-04ed9dfd32be", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757433593600, "endTime": 31757552520500}, "additional": {"children": ["8ebd1987-8a41-4595-baa6-60f31d185e6d", "445bc969-da9d-45de-a521-55d6bb3b52f5", "e61b39ac-bb4a-4f3f-b36c-082d48cfb774"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "03468c5f-8a9a-4515-bbf2-c1e71845fc6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e4e7b87-3a46-4f00-a02a-296f7cfc8f17", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757552668400, "endTime": 31757585209800}, "additional": {"children": ["2a41683c-3f67-4698-b7bd-fda02b69a349"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "fc1433cd-63d3-409a-974a-8fa941f47a50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "182d1f00-0fe3-4c79-aefd-9f2e713e0ceb", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757585217400, "endTime": 31757669628500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "39eba8ef-bf0c-4a71-8a3e-d4459ae9ad72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca31c749-0669-4c9d-8a4b-0bea80e0c7d2", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757670489300, "endTime": 31757689870100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "bed6e7bf-be8e-4aaa-bcbf-163bd11e8a1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bdc393f-4070-4d7f-8df5-b83bd02011bb", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757689889100, "endTime": 31757702964300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "cecfd8a3-4c8a-4d05-87a9-68e2113ec334"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "818239d2-2973-4aef-b08f-3f0efb183297", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757702990100, "endTime": 31757703147200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "da3be584-ce77-40cc-bad6-c1284a84e703"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ab8c009-01b1-4256-bac0-c6c4480cd930", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757429021200, "endTime": 31757429030800}, "additional": {"logType": "info", "children": [], "durationId": "ba5335b9-27ac-44e9-8ce6-3475e61bf54f", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "1886cd9e-af7e-41bd-961b-29ab53b6a58d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757429045600, "endTime": 31757433575200}, "additional": {"logType": "info", "children": [], "durationId": "ba186ed4-33fe-457c-bb36-79c325961dc8", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "8ebd1987-8a41-4595-baa6-60f31d185e6d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757434257000, "endTime": 31757434278500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72d264ab-7489-44d3-a90d-04ed9dfd32be", "logId": "3a89071a-8b73-4880-a4c9-1a5d6828ef46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a89071a-8b73-4880-a4c9-1a5d6828ef46", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757434257000, "endTime": 31757434278500}, "additional": {"logType": "info", "children": [], "durationId": "8ebd1987-8a41-4595-baa6-60f31d185e6d", "parent": "03468c5f-8a9a-4515-bbf2-c1e71845fc6a"}}, {"head": {"id": "445bc969-da9d-45de-a521-55d6bb3b52f5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757436192000, "endTime": 31757550911800}, "additional": {"children": ["b8d6cbf3-8dc8-4faf-a7a0-cb3417533d78", "09c2ac9a-8d41-46a9-a83e-42609db13061"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72d264ab-7489-44d3-a90d-04ed9dfd32be", "logId": "672a60a8-d5ba-4700-8f42-8247250ff9cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8d6cbf3-8dc8-4faf-a7a0-cb3417533d78", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757436193500, "endTime": 31757440739600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "445bc969-da9d-45de-a521-55d6bb3b52f5", "logId": "78a397a9-d809-4c49-97a8-9033efef31c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09c2ac9a-8d41-46a9-a83e-42609db13061", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757440757700, "endTime": 31757550898800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "445bc969-da9d-45de-a521-55d6bb3b52f5", "logId": "3665958b-9b43-4d8f-b1d5-1c0624d87977"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73abd645-42ab-4f98-8c5d-9513dc45f6c6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757436197700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a39846-ea65-4ffb-aaca-0558c9fd8a08", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757440579900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78a397a9-d809-4c49-97a8-9033efef31c5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757436193500, "endTime": 31757440739600}, "additional": {"logType": "info", "children": [], "durationId": "b8d6cbf3-8dc8-4faf-a7a0-cb3417533d78", "parent": "672a60a8-d5ba-4700-8f42-8247250ff9cc"}}, {"head": {"id": "32203a61-b986-4476-a126-23929cd38028", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757440768500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "477c8c34-6d24-4aa7-a17d-76857825b037", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757449528500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea723b9-f95f-4401-8f85-4a43a79caf77", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757450048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99b8c3e1-cad9-44bf-81d8-3a8de5e5053d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757450253800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be527ca9-654b-449c-8e04-463d1ae3db89", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757450380200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa6e57d-f650-4af9-9a0d-f38628da3e35", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757452557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddac0647-60fe-431c-99ec-570c6812ac7d", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757457767200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6862b4a-8c75-46cc-8628-4965760d36c8", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757466870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f17d344-1bb9-4129-b32f-172026ca6b78", "name": "Sdk init in 51 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757509164400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c816dab1-ee90-4413-a83c-647761e9a962", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757509309900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 18}, "markType": "other"}}, {"head": {"id": "98e1b3c3-6682-4c67-8933-73e2c47b9994", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757509325600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 18}, "markType": "other"}}, {"head": {"id": "391bea11-5f38-4ed2-aca9-3ef9f5e3e0e2", "name": "Project task initialization takes 41 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757550615500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "861e2dff-cdf8-4cf6-a7da-08adbed51a4f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757550744600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4168e52-26d9-4a12-bea4-7996148d5532", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757550801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef9ea0b-45be-46ef-8ddc-eaf3f83d86c0", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757550850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3665958b-9b43-4d8f-b1d5-1c0624d87977", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757440757700, "endTime": 31757550898800}, "additional": {"logType": "info", "children": [], "durationId": "09c2ac9a-8d41-46a9-a83e-42609db13061", "parent": "672a60a8-d5ba-4700-8f42-8247250ff9cc"}}, {"head": {"id": "672a60a8-d5ba-4700-8f42-8247250ff9cc", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757436192000, "endTime": 31757550911800}, "additional": {"logType": "info", "children": ["78a397a9-d809-4c49-97a8-9033efef31c5", "3665958b-9b43-4d8f-b1d5-1c0624d87977"], "durationId": "445bc969-da9d-45de-a521-55d6bb3b52f5", "parent": "03468c5f-8a9a-4515-bbf2-c1e71845fc6a"}}, {"head": {"id": "e61b39ac-bb4a-4f3f-b36c-082d48cfb774", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757552482600, "endTime": 31757552504600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72d264ab-7489-44d3-a90d-04ed9dfd32be", "logId": "3f3d79b7-cd78-448e-a45d-1b532c9b7b70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f3d79b7-cd78-448e-a45d-1b532c9b7b70", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757552482600, "endTime": 31757552504600}, "additional": {"logType": "info", "children": [], "durationId": "e61b39ac-bb4a-4f3f-b36c-082d48cfb774", "parent": "03468c5f-8a9a-4515-bbf2-c1e71845fc6a"}}, {"head": {"id": "03468c5f-8a9a-4515-bbf2-c1e71845fc6a", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757433593600, "endTime": 31757552520500}, "additional": {"logType": "info", "children": ["3a89071a-8b73-4880-a4c9-1a5d6828ef46", "672a60a8-d5ba-4700-8f42-8247250ff9cc", "3f3d79b7-cd78-448e-a45d-1b532c9b7b70"], "durationId": "72d264ab-7489-44d3-a90d-04ed9dfd32be", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "2a41683c-3f67-4698-b7bd-fda02b69a349", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757554363100, "endTime": 31757585198000}, "additional": {"children": ["6e775f2d-5531-4982-b891-b42999a5242e", "780c5db9-aaa0-4c54-ace3-fb3744ba4173", "78b42f43-cf97-49bc-9459-859684e9a1ad"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e4e7b87-3a46-4f00-a02a-296f7cfc8f17", "logId": "5848afe9-595d-4cb7-ab14-4a14ff399429"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e775f2d-5531-4982-b891-b42999a5242e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757558398400, "endTime": 31757558417000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a41683c-3f67-4698-b7bd-fda02b69a349", "logId": "6abcf7b0-d879-4136-a832-84e5dda30d1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6abcf7b0-d879-4136-a832-84e5dda30d1b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757558398400, "endTime": 31757558417000}, "additional": {"logType": "info", "children": [], "durationId": "6e775f2d-5531-4982-b891-b42999a5242e", "parent": "5848afe9-595d-4cb7-ab14-4a14ff399429"}}, {"head": {"id": "780c5db9-aaa0-4c54-ace3-fb3744ba4173", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757560696900, "endTime": 31757580960500}, "additional": {"children": ["23a667ea-7b61-4e05-9fa1-f9bf0cb34689", "0099e06a-7119-4bb5-b42d-b9b334678aa7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a41683c-3f67-4698-b7bd-fda02b69a349", "logId": "57616da3-eb16-4383-b735-12e8fa879a8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23a667ea-7b61-4e05-9fa1-f9bf0cb34689", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757560698700, "endTime": 31757566662300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "780c5db9-aaa0-4c54-ace3-fb3744ba4173", "logId": "5887a24d-e834-41f7-8d31-350b726f8914"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0099e06a-7119-4bb5-b42d-b9b334678aa7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757566696300, "endTime": 31757580944000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "780c5db9-aaa0-4c54-ace3-fb3744ba4173", "logId": "2b6720ff-9357-45a7-b1aa-c5d254bcb66c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "130408d5-7999-405e-972c-a4cf215d2de6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757560704400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec50c40-290b-4065-81d2-944256659604", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757565908100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5887a24d-e834-41f7-8d31-350b726f8914", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757560698700, "endTime": 31757566662300}, "additional": {"logType": "info", "children": [], "durationId": "23a667ea-7b61-4e05-9fa1-f9bf0cb34689", "parent": "57616da3-eb16-4383-b735-12e8fa879a8c"}}, {"head": {"id": "a99dca3f-06dc-4741-bce3-2ab2a06992dd", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757566717400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf18a48-112e-42d0-96f4-fbe7db521412", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757576160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b85be51-9f09-46a9-8ae1-5a1bcfbfd893", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757576279800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b72de3-1fe0-46a2-b08e-45b49f69900e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757576478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb856255-4db0-4f52-985d-ddafa94d8ac5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757576617400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2828045-20ed-4a25-91ff-97d39b7a5130", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757576683200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f04e315b-4141-469f-a4ca-dd7a0441effd", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757576733600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1390df6-504e-429a-ad15-397814a872cf", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757577562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a198b5a-9c89-43c8-b7f5-9885883d6a4a", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757580616300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a774256d-cac6-4cc5-8902-947f25f81b93", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757580784100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6401eab4-06a0-4085-9de3-22cb4f6dcc45", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757580842700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51db024e-074a-4254-9c64-23c76354d931", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757580898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b6720ff-9357-45a7-b1aa-c5d254bcb66c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757566696300, "endTime": 31757580944000}, "additional": {"logType": "info", "children": [], "durationId": "0099e06a-7119-4bb5-b42d-b9b334678aa7", "parent": "57616da3-eb16-4383-b735-12e8fa879a8c"}}, {"head": {"id": "57616da3-eb16-4383-b735-12e8fa879a8c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757560696900, "endTime": 31757580960500}, "additional": {"logType": "info", "children": ["5887a24d-e834-41f7-8d31-350b726f8914", "2b6720ff-9357-45a7-b1aa-c5d254bcb66c"], "durationId": "780c5db9-aaa0-4c54-ace3-fb3744ba4173", "parent": "5848afe9-595d-4cb7-ab14-4a14ff399429"}}, {"head": {"id": "78b42f43-cf97-49bc-9459-859684e9a1ad", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757585161700, "endTime": 31757585180400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a41683c-3f67-4698-b7bd-fda02b69a349", "logId": "084b72e2-c60a-4d23-b990-0ee4b54486d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "084b72e2-c60a-4d23-b990-0ee4b54486d0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757585161700, "endTime": 31757585180400}, "additional": {"logType": "info", "children": [], "durationId": "78b42f43-cf97-49bc-9459-859684e9a1ad", "parent": "5848afe9-595d-4cb7-ab14-4a14ff399429"}}, {"head": {"id": "5848afe9-595d-4cb7-ab14-4a14ff399429", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757554363100, "endTime": 31757585198000}, "additional": {"logType": "info", "children": ["6abcf7b0-d879-4136-a832-84e5dda30d1b", "57616da3-eb16-4383-b735-12e8fa879a8c", "084b72e2-c60a-4d23-b990-0ee4b54486d0"], "durationId": "2a41683c-3f67-4698-b7bd-fda02b69a349", "parent": "fc1433cd-63d3-409a-974a-8fa941f47a50"}}, {"head": {"id": "fc1433cd-63d3-409a-974a-8fa941f47a50", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757552668400, "endTime": 31757585209800}, "additional": {"logType": "info", "children": ["5848afe9-595d-4cb7-ab14-4a14ff399429"], "durationId": "3e4e7b87-3a46-4f00-a02a-296f7cfc8f17", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "caf57990-94b8-499a-ae09-5c9c4565ca04", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757611953100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "505caa0c-1088-47b1-aecd-86982b8d5474", "name": "hvigorfile, resolve hvigorfile dependencies in 85 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757669501600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39eba8ef-bf0c-4a71-8a3e-d4459ae9ad72", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757585217400, "endTime": 31757669628500}, "additional": {"logType": "info", "children": [], "durationId": "182d1f00-0fe3-4c79-aefd-9f2e713e0ceb", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "1f0d4287-8220-454f-9d7a-5f0514b7d294", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757670305400, "endTime": 31757670477600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "logId": "57caedf9-a53d-4598-a144-20264ea3c770"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5e03e60-7564-4d1d-a899-4f55a2c7cf14", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757670329400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57caedf9-a53d-4598-a144-20264ea3c770", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757670305400, "endTime": 31757670477600}, "additional": {"logType": "info", "children": [], "durationId": "1f0d4287-8220-454f-9d7a-5f0514b7d294", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "6eb40ec8-eec4-4e79-88e1-52e1b16c6bfe", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757671380300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed9a1b6-c3a6-431c-854e-081f8acbad51", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757689199100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bed6e7bf-be8e-4aaa-bcbf-163bd11e8a1d", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757670489300, "endTime": 31757689870100}, "additional": {"logType": "info", "children": [], "durationId": "ca31c749-0669-4c9d-8a4b-0bea80e0c7d2", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "f21f4dbd-e51a-4a50-a6e6-b640be868794", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757693888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039d3392-12fc-4262-919f-c9f88b351dfd", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757693991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "108ead02-d4e6-4119-ba61-c057b133c459", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757695851200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434f3c68-f7e0-434f-86a4-8b0623768ba3", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757695949500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cecfd8a3-4c8a-4d05-87a9-68e2113ec334", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757689889100, "endTime": 31757702964300}, "additional": {"logType": "info", "children": [], "durationId": "5bdc393f-4070-4d7f-8df5-b83bd02011bb", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "b919691e-1068-4cae-92de-baa948ab8b81", "name": "Configuration phase cost:274 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757703011900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da3be584-ce77-40cc-bad6-c1284a84e703", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757702990100, "endTime": 31757703147200}, "additional": {"logType": "info", "children": [], "durationId": "818239d2-2973-4aef-b08f-3f0efb183297", "parent": "143fbc9d-8d18-4179-a1df-b061baf980fe"}}, {"head": {"id": "143fbc9d-8d18-4179-a1df-b061baf980fe", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757423320100, "endTime": 31757703159800}, "additional": {"logType": "info", "children": ["5ab8c009-01b1-4256-bac0-c6c4480cd930", "1886cd9e-af7e-41bd-961b-29ab53b6a58d", "03468c5f-8a9a-4515-bbf2-c1e71845fc6a", "fc1433cd-63d3-409a-974a-8fa941f47a50", "39eba8ef-bf0c-4a71-8a3e-d4459ae9ad72", "bed6e7bf-be8e-4aaa-bcbf-163bd11e8a1d", "cecfd8a3-4c8a-4d05-87a9-68e2113ec334", "da3be584-ce77-40cc-bad6-c1284a84e703", "57caedf9-a53d-4598-a144-20264ea3c770"], "durationId": "c1cdcce3-b0b1-4645-b28d-be3a4c01ea21", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "d5712f2e-fb8b-435a-bbef-cbec1b11da8d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757705172900, "endTime": 31757705192300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e6f1ae06-f813-4c82-a12b-d0550241416c", "logId": "54c191da-7744-4132-b975-65ba6f007aa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54c191da-7744-4132-b975-65ba6f007aa2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757705172900, "endTime": 31757705192300}, "additional": {"logType": "info", "children": [], "durationId": "d5712f2e-fb8b-435a-bbef-cbec1b11da8d", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "7f5348dc-8a5f-46e0-8923-4d7b1966746e", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757703181400, "endTime": 31757705205500}, "additional": {"logType": "info", "children": [], "durationId": "7e1c4e40-b1ad-4a85-a2a9-8b4859c47a06", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "5d717504-ac26-433f-9fce-bd27ecd8e90e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757705210300, "endTime": 31757705211200}, "additional": {"logType": "info", "children": [], "durationId": "d3a130cc-e1dc-4ffb-9a8b-287500a9288f", "parent": "74d3a917-9f85-4f85-9831-131ebc944ea0"}}, {"head": {"id": "74d3a917-9f85-4f85-9831-131ebc944ea0", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757409639700, "endTime": 31757705214000}, "additional": {"logType": "info", "children": ["2c104155-bd9c-4d26-bd35-11f649cf05b9", "143fbc9d-8d18-4179-a1df-b061baf980fe", "7f5348dc-8a5f-46e0-8923-4d7b1966746e", "5d717504-ac26-433f-9fce-bd27ecd8e90e", "9d9c43bc-1692-45d9-96b0-53c59a7f6f0d", "3ea6f3d8-a53b-4428-a2f8-c7a53d2747bd", "54c191da-7744-4132-b975-65ba6f007aa2"], "durationId": "e6f1ae06-f813-4c82-a12b-d0550241416c"}}, {"head": {"id": "6ec1cc7c-e873-4e31-9e6c-c9b0a0bb3ed8", "name": "Configuration task cost before running: 299 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757705482200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "845a23af-29b3-4265-a459-8b9f55902805", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757711345700, "endTime": 31757721173200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "da241985-1399-437e-9078-fa7407972bcd", "logId": "d0ec2f37-c5e0-42f8-ab72-40a9fd13b338"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da241985-1399-437e-9078-fa7407972bcd", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757708223400}, "additional": {"logType": "detail", "children": [], "durationId": "845a23af-29b3-4265-a459-8b9f55902805"}}, {"head": {"id": "0b7b5a22-84d6-4cd4-bb39-db1c57c8fa4c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757708725300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bbc24b7-3c98-487e-a769-c3e6001ead3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757708838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a59d8d9-225b-4a2c-8b73-158833a8ea80", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757711357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620d8835-e523-4110-a04f-54e5cb152faf", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757720982100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b46ec345-7023-425a-b2f8-0b1ef5d89153", "name": "entry : default@PreBuild cost memory 0.3084716796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757721108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ec2f37-c5e0-42f8-ab72-40a9fd13b338", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757711345700, "endTime": 31757721173200}, "additional": {"logType": "info", "children": [], "durationId": "845a23af-29b3-4265-a459-8b9f55902805"}}, {"head": {"id": "114b713d-0d68-4ba0-b0fe-61f61de7596d", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757727518300, "endTime": 31757730030600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "836a27b4-6805-41dc-afb0-052ac1f158a7", "logId": "8f78b821-b158-490f-9e37-f8471c094f9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "836a27b4-6805-41dc-afb0-052ac1f158a7", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757726085300}, "additional": {"logType": "detail", "children": [], "durationId": "114b713d-0d68-4ba0-b0fe-61f61de7596d"}}, {"head": {"id": "ce96e19f-17dd-4516-90a7-0952159e6892", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757726458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9db3719-a347-41cf-be93-9e5900cee2ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757726562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b5800d9-6b59-464f-9226-db8186b0148f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757727530000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76b2f955-7dd0-497a-94e2-d9e5a3e41c44", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757728478100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f89af93-109e-4339-8b4f-117fb0a4066b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757729506500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d73c74c-a95c-430b-9f55-444447e22733", "name": "entry : default@GenerateMetadata cost memory 0.09453582763671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757729886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f78b821-b158-490f-9e37-f8471c094f9c", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757727518300, "endTime": 31757730030600}, "additional": {"logType": "info", "children": [], "durationId": "114b713d-0d68-4ba0-b0fe-61f61de7596d"}}, {"head": {"id": "e5edbc74-ed59-48c3-86de-2f87afbf54fd", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734537700, "endTime": 31757734885000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f37d6ec5-cdc6-4689-8294-190bd7884b21", "logId": "b29f91f0-cdfd-49c7-8558-32774c2b0385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f37d6ec5-cdc6-4689-8294-190bd7884b21", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757733481300}, "additional": {"logType": "detail", "children": [], "durationId": "e5edbc74-ed59-48c3-86de-2f87afbf54fd"}}, {"head": {"id": "29018bd4-febe-43cc-9c22-c3695f9ee376", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734022300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b95a68-3baf-4e61-82ab-e6af3ce05be9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c12e98-ecd5-4cde-8599-df613343c844", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058a5be5-23b3-4772-a26f-0c1c0fed0473", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734645500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d5587c5-eb19-4ed8-986c-206cd02f87b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734697400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "470e4e6e-bf4c-4b25-82b7-e2d537aaa1b7", "name": "entry : default@ConfigureCmake cost memory 0.0359954833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734765200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c151d149-018e-41a8-b85a-61eb80759d62", "name": "runTaskFromQueue task cost before running: 328 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734834300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b29f91f0-cdfd-49c7-8558-32774c2b0385", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757734537700, "endTime": 31757734885000, "totalTime": 281000}, "additional": {"logType": "info", "children": [], "durationId": "e5edbc74-ed59-48c3-86de-2f87afbf54fd"}}, {"head": {"id": "8f7b4a24-801e-463c-8fbf-b4d383993635", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757738343000, "endTime": 31757740698500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "c6f069d3-0008-4295-8103-8ab239526267", "logId": "7820a968-3362-478c-9496-89813feb39bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6f069d3-0008-4295-8103-8ab239526267", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757736660500}, "additional": {"logType": "detail", "children": [], "durationId": "8f7b4a24-801e-463c-8fbf-b4d383993635"}}, {"head": {"id": "a6846785-9563-40ec-bc2c-3666e8a188a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757737228400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e28929e-3516-4a39-9c14-1cb50c007ff6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757737335300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "151d0924-e447-40f6-b3de-9b448b4ad29a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757738385000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d2b7335-517b-413b-bb0f-1b89b6c0a42d", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757740495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92d0346c-3cd9-4b48-b962-f897705e90a5", "name": "entry : default@MergeProfile cost memory 0.111724853515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757740632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7820a968-3362-478c-9496-89813feb39bc", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757738343000, "endTime": 31757740698500}, "additional": {"logType": "info", "children": [], "durationId": "8f7b4a24-801e-463c-8fbf-b4d383993635"}}, {"head": {"id": "24020168-c7e7-44f2-a62d-587c10ad057c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757743508100, "endTime": 31757747286500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4e3518f0-a060-442a-b3de-643b888087ad", "logId": "5ad900b7-a0df-4a3a-9f5d-776680962b92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e3518f0-a060-442a-b3de-643b888087ad", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757742412100}, "additional": {"logType": "detail", "children": [], "durationId": "24020168-c7e7-44f2-a62d-587c10ad057c"}}, {"head": {"id": "cb19ff4e-4e97-4be5-8eef-62bdcfbeb7fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757742782000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b33dcb-6e8c-43b8-94b9-d7a1f000eea3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757742876800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d96f42-b140-4cb3-8d14-641b05aea227", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757743517800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f71dff0-5b3d-4a2e-992c-5ba235a44bc0", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757744541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "558b0800-965b-4f4d-b418-2384ea4264cf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757747077200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e858a44-c3a4-4bc9-aabd-0052fc8728a6", "name": "entry : default@CreateBuildProfile cost memory 0.10437774658203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757747211500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ad900b7-a0df-4a3a-9f5d-776680962b92", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757743508100, "endTime": 31757747286500}, "additional": {"logType": "info", "children": [], "durationId": "24020168-c7e7-44f2-a62d-587c10ad057c"}}, {"head": {"id": "31484c18-b0d0-4b3b-b3f4-8e46686fd1e7", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757752949400, "endTime": 31757753532600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "deba0582-6aba-4307-9ccc-eeef9614a40b", "logId": "8cce4df6-b1c4-48ea-a650-96f75c7cf039"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "deba0582-6aba-4307-9ccc-eeef9614a40b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757751183700}, "additional": {"logType": "detail", "children": [], "durationId": "31484c18-b0d0-4b3b-b3f4-8e46686fd1e7"}}, {"head": {"id": "62579a08-3a17-433a-98d8-630c9dc98716", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757752034000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e04e8b-3320-4840-ab79-7095c71a47cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757752174300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d76212-a782-41d5-9532-a344f0223034", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757752958800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b81054e5-8bd9-407c-a233-eb22bc7f62c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757753077900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8695ea4c-f3a3-4128-ad73-4ae0b35d4586", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757753245200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfbfa7e2-8a39-4316-83df-a465e5cd2807", "name": "entry : default@PreCheckSyscap cost memory 0.03685760498046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757753407400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af005e1-d406-4e84-963b-7a27ed6dc76b", "name": "runTaskFromQueue task cost before running: 347 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757753481600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cce4df6-b1c4-48ea-a650-96f75c7cf039", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757752949400, "endTime": 31757753532600, "totalTime": 514100}, "additional": {"logType": "info", "children": [], "durationId": "31484c18-b0d0-4b3b-b3f4-8e46686fd1e7"}}, {"head": {"id": "d6076155-b433-47e1-bc82-266ce4ae935b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757760844900, "endTime": 31757761845900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3ade79fc-70d4-4ae0-881d-54cd3c19c383", "logId": "3db70917-c520-47b1-b10b-e236f50156e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ade79fc-70d4-4ae0-881d-54cd3c19c383", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757756003000}, "additional": {"logType": "detail", "children": [], "durationId": "d6076155-b433-47e1-bc82-266ce4ae935b"}}, {"head": {"id": "d406c4bd-1a52-4833-96f7-aebb7ba8cff8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757756480200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2faa5869-9006-487d-a16d-b6e218c4d3fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757756589800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b70ef3-eddc-4119-9df4-eae9535912dc", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757760855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9068b8d1-a646-4589-9416-f291f6e48462", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757761448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c751624c-8389-4bca-b734-01b1bca53e19", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03851318359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757761702900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69a1dfe8-81ac-4153-9581-924e30e119a0", "name": "runTaskFromQueue task cost before running: 355 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757761791800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3db70917-c520-47b1-b10b-e236f50156e4", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757760844900, "endTime": 31757761845900, "totalTime": 931100}, "additional": {"logType": "info", "children": [], "durationId": "d6076155-b433-47e1-bc82-266ce4ae935b"}}, {"head": {"id": "99b2e600-f17c-4dca-bf92-32b54e9d64ef", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757833897100, "endTime": 31757836747500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "93b77c64-80fa-49e1-93f3-6e1a60aab131", "logId": "37ced807-e294-4666-9937-6f6dc53692fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93b77c64-80fa-49e1-93f3-6e1a60aab131", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757763365300}, "additional": {"logType": "detail", "children": [], "durationId": "99b2e600-f17c-4dca-bf92-32b54e9d64ef"}}, {"head": {"id": "af199712-2eb0-41a0-b86f-abdfb51961a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757763802400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429eb354-9486-4d2c-b06b-3f64195571fc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757763906100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0058c5-3bfd-4711-8872-2fae9023909d", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757833909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2895c5-3430-4638-bd5b-145bc108bccd", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757836231200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f15cd1-e32c-402b-8ce8-83fbc7a18847", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757836389900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863be32e-a724-49a0-b957-2c27fb6520f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757836480800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77d0d564-0a1a-4e1b-b619-8bd128f0487d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757836537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "537ee634-4fcf-436b-b3e4-436f812ec415", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1177825927734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757836617000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dafa237-bc78-4edf-b17d-3534212803e2", "name": "runTaskFromQueue task cost before running: 430 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757836698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37ced807-e294-4666-9937-6f6dc53692fd", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757833897100, "endTime": 31757836747500, "totalTime": 2787200}, "additional": {"logType": "info", "children": [], "durationId": "99b2e600-f17c-4dca-bf92-32b54e9d64ef"}}, {"head": {"id": "fa96157d-71d5-4043-9615-66c56fab4aad", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842096700, "endTime": 31757842948600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4912c056-907f-4516-8df6-30b2193a7990", "logId": "2927225e-abaf-4636-9217-23931610c8c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4912c056-907f-4516-8df6-30b2193a7990", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757840897500}, "additional": {"logType": "detail", "children": [], "durationId": "fa96157d-71d5-4043-9615-66c56fab4aad"}}, {"head": {"id": "c85c2967-193c-4e8c-9f1c-cbe38f3e98bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757841350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c97a85-5a57-4fb2-a470-8ba129eba88b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757841450300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f352b56-9aab-48f4-b2db-11d2e06a72aa", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2413a41f-0f1f-4c9a-a60d-5a3c8b5895bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842251500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d73d377-fc2d-43f2-9ee4-a32a631b879c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68bd7f3a-7beb-47e8-8907-21af24a59664", "name": "entry : default@BuildNativeWithCmake cost memory 0.03704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842523200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c9cb93-6635-466b-8103-c6dbc5efcf78", "name": "runTaskFromQueue task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842785000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2927225e-abaf-4636-9217-23931610c8c6", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757842096700, "endTime": 31757842948600, "totalTime": 668000}, "additional": {"logType": "info", "children": [], "durationId": "fa96157d-71d5-4043-9615-66c56fab4aad"}}, {"head": {"id": "3a9390af-bbc3-4e68-a5d9-a8e5f7834ad8", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757845502500, "endTime": 31757850408400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d1e3200e-d638-4ff0-a88c-1ca172501d01", "logId": "f63c4410-f9cc-4516-ad69-5848749ce047"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1e3200e-d638-4ff0-a88c-1ca172501d01", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757844417200}, "additional": {"logType": "detail", "children": [], "durationId": "3a9390af-bbc3-4e68-a5d9-a8e5f7834ad8"}}, {"head": {"id": "24d4a299-4da1-471a-9f96-1ee296eee9c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757844751300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f071e80c-17c8-45e1-adde-c9816466da5d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757844841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d8c4925-d088-4915-b4e1-18b2c5337fd7", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757845511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d92051-ff46-4a47-9dbf-9ef76307901e", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757849786600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12605b34-cd9b-4c48-a1e8-f19156ff99ed", "name": "entry : default@MakePackInfo cost memory 0.13895416259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757849974800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63c4410-f9cc-4516-ad69-5848749ce047", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757845502500, "endTime": 31757850408400}, "additional": {"logType": "info", "children": [], "durationId": "3a9390af-bbc3-4e68-a5d9-a8e5f7834ad8"}}, {"head": {"id": "43c1caec-3117-41b7-b4f4-f054cdac6c02", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757855582600, "endTime": 31757864161800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "89f5cc74-379d-4b15-ae45-73c7c4050681", "logId": "80acfdf0-4027-45de-bb2d-76a13377e2b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89f5cc74-379d-4b15-ae45-73c7c4050681", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757853414800}, "additional": {"logType": "detail", "children": [], "durationId": "43c1caec-3117-41b7-b4f4-f054cdac6c02"}}, {"head": {"id": "41035db8-5fc8-48ee-a868-0af77e576e22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757853838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def068c0-9900-4609-abc2-5615932f243d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757853984300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f19e22f2-6221-469d-aa36-345d3d4c6b33", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757855592600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69176788-b7c2-4ecb-aee0-8c67e4fab48a", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757855740300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "384517dc-36c0-4379-a47f-2fd8503d4ac8", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757856327000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba3d257c-1201-4751-b0b9-5054301845a0", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757862480300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ef8a93-eba3-433d-b733-c8ba2187e79f", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757862847800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2684db7e-537f-4223-9f77-3d31e2e95023", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757863073500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75fbce8d-ce66-4cf8-ac43-c8d72ce4709d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757863354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424c69cb-9d1c-4a69-9bef-eb6c79f2033a", "name": "entry : default@SyscapTransform cost memory 0.15285491943359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757863722600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9630917e-1a1a-48eb-9cce-b3a22a673820", "name": "runTaskFromQueue task cost before running: 458 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757863961300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80acfdf0-4027-45de-bb2d-76a13377e2b1", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757855582600, "endTime": 31757864161800, "totalTime": 8355300}, "additional": {"logType": "info", "children": [], "durationId": "43c1caec-3117-41b7-b4f4-f054cdac6c02"}}, {"head": {"id": "12d44f54-3596-4c6c-81e6-22af8c943290", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757874218300, "endTime": 31757875985600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b00dea81-79dd-4e5b-b1da-7ef18f0a505f", "logId": "3e47b8c1-937f-4b3e-88e7-88d0f4ea354d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b00dea81-79dd-4e5b-b1da-7ef18f0a505f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757871495400}, "additional": {"logType": "detail", "children": [], "durationId": "12d44f54-3596-4c6c-81e6-22af8c943290"}}, {"head": {"id": "6185e901-2b4e-4634-a4b1-49fd15f369d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757872137600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63816e3a-bfd5-4c4c-b173-54ec9e8e37c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757872474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "481faeba-51a7-4cbc-9f77-59a4d707cff7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757874227700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b6894b-e32b-45fd-ae54-f3a99182abda", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757875488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4375e744-7199-497f-a20a-4bf1896220ed", "name": "entry : default@ProcessProfile cost memory 0.059906005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757875902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e47b8c1-937f-4b3e-88e7-88d0f4ea354d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757874218300, "endTime": 31757875985600}, "additional": {"logType": "info", "children": [], "durationId": "12d44f54-3596-4c6c-81e6-22af8c943290"}}, {"head": {"id": "da566f0a-c865-4f90-acc0-c0ce97a81bf1", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757885764800, "endTime": 31757894813100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0433f54c-650d-48df-b95e-fc30c9593b0b", "logId": "f5ccdeb8-03e3-453a-9958-74214478c2af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0433f54c-650d-48df-b95e-fc30c9593b0b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757877671700}, "additional": {"logType": "detail", "children": [], "durationId": "da566f0a-c865-4f90-acc0-c0ce97a81bf1"}}, {"head": {"id": "d6234782-3227-4b41-8e7f-3b272b04f27f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757878017300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9471f66b-e780-4d63-aa31-dcbebf9f72cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757878108600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f8939d-b5ea-4e43-b40d-1272b055c8f9", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757885776400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f23de8-da77-4033-823d-78d0646d8b43", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757894421000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d3f32ef-19d9-4e19-83e0-5a21e1833114", "name": "entry : default@ProcessRouterMap cost memory 0.2025604248046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757894554900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ccdeb8-03e3-453a-9958-74214478c2af", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757885764800, "endTime": 31757894813100}, "additional": {"logType": "info", "children": [], "durationId": "da566f0a-c865-4f90-acc0-c0ce97a81bf1"}}, {"head": {"id": "704d4d32-9386-43fb-aa95-75cd6f01eb1a", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757902522500, "endTime": 31757903754700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0d31696b-6689-4ba2-b523-92fa8dd6c063", "logId": "ddc89f3a-987f-4a9b-b39f-5905cce6b37e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d31696b-6689-4ba2-b523-92fa8dd6c063", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757900371600}, "additional": {"logType": "detail", "children": [], "durationId": "704d4d32-9386-43fb-aa95-75cd6f01eb1a"}}, {"head": {"id": "24e53b8d-26ba-4a8e-80a5-f4ef6ff9b9d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757900780200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aef35cc-8008-40ab-9a1a-cdbdd8f13d75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757901291600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e244d759-7530-4431-8adb-16db48d82849", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757902534600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6e6a4be-6886-4bd0-be1a-b74e84bc0d33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757902844200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee98434b-aaf8-439f-8fbf-4edb04fbc83c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757902926000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c699d33c-5568-4565-9721-d2349c97c132", "name": "entry : default@BuildNativeWithNinja cost memory 0.05664825439453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757903586900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd1d2ec-1895-47d2-88c4-92adc5688af5", "name": "runTaskFromQueue task cost before running: 497 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757903696100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc89f3a-987f-4a9b-b39f-5905cce6b37e", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757902522500, "endTime": 31757903754700, "totalTime": 1155500}, "additional": {"logType": "info", "children": [], "durationId": "704d4d32-9386-43fb-aa95-75cd6f01eb1a"}}, {"head": {"id": "d3c87882-4080-4292-bb94-cb5dbe80b639", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757908584100, "endTime": 31757917427300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4ca53535-b5da-41da-b64e-7db18af723bb", "logId": "59bc9855-ad12-4b7c-a520-8db693834662"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ca53535-b5da-41da-b64e-7db18af723bb", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757906184700}, "additional": {"logType": "detail", "children": [], "durationId": "d3c87882-4080-4292-bb94-cb5dbe80b639"}}, {"head": {"id": "b29c6548-0ebc-4573-bdbf-8afeb0aa629b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757906546200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6afd16-b381-473e-9bda-bdef7cddc423", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757906647900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8440ec4-d912-4468-ae4a-af8522c8de77", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757907410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c959fc49-7a2c-45c8-a36c-ea7b7f011e4b", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757909952900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3d879c-4f95-4a8f-a570-3a84d5ed12e1", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757911586500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df894ee-9460-46ad-a10e-174a6516fc2a", "name": "entry : default@ProcessResource cost memory 0.169708251953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757911940900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59bc9855-ad12-4b7c-a520-8db693834662", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757908584100, "endTime": 31757917427300}, "additional": {"logType": "info", "children": [], "durationId": "d3c87882-4080-4292-bb94-cb5dbe80b639"}}, {"head": {"id": "07bcccd0-be83-4d29-bf62-efefac3ad570", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757941263500, "endTime": 31757963953900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "acae9008-c068-4e4c-87f0-3d8404b6f53a", "logId": "912e9f60-0620-4c32-994c-204f18c0f2c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acae9008-c068-4e4c-87f0-3d8404b6f53a", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757936427500}, "additional": {"logType": "detail", "children": [], "durationId": "07bcccd0-be83-4d29-bf62-efefac3ad570"}}, {"head": {"id": "0f7e9cb6-8478-4bda-b849-7e704609b2c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757936892900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dcf6073-97a7-4a6b-b0b8-ba8caf65e6d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757937007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a34b27f-4e0f-42ff-87d9-156bde2562e7", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757941274400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee4e9e3-59e7-4b9a-9454-23c6ce344f73", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757963766300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a4c4429-809c-4023-82ee-18bd1214ca07", "name": "entry : default@GenerateLoaderJson cost memory 0.7646026611328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757963888300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912e9f60-0620-4c32-994c-204f18c0f2c1", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757941263500, "endTime": 31757963953900}, "additional": {"logType": "info", "children": [], "durationId": "07bcccd0-be83-4d29-bf62-efefac3ad570"}}, {"head": {"id": "178efb78-b313-4dcf-b78b-0b7216e0e6ec", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757977861100, "endTime": 31757981094900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "407cc8da-be7c-45f7-a7c3-6ff29e7a6587", "logId": "ad1c0658-6629-4068-98ef-2a8a0d0a8403"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "407cc8da-be7c-45f7-a7c3-6ff29e7a6587", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757976020900}, "additional": {"logType": "detail", "children": [], "durationId": "178efb78-b313-4dcf-b78b-0b7216e0e6ec"}}, {"head": {"id": "8f76d1df-20e6-4bfd-8d21-55bb5970dc9d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757976521100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eaf95b6-749e-40c0-b3b0-8a83a47de84f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757976772700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6474ca25-13c8-422f-9c99-45fa3a3cd628", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757977877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29499af4-d198-4ab0-90df-e1924507e695", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757979993400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "021e76d6-7367-47b6-8728-7739b837dbf7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757980093300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b03fa49-3ce1-48a2-9178-09f40485dcd5", "name": "entry : default@ProcessLibs cost memory 0.12566375732421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757980921000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2c889d-892d-48e5-9d5f-185a66a698ea", "name": "runTaskFromQueue task cost before running: 575 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757981032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1c0658-6629-4068-98ef-2a8a0d0a8403", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757977861100, "endTime": 31757981094900, "totalTime": 3145600}, "additional": {"logType": "info", "children": [], "durationId": "178efb78-b313-4dcf-b78b-0b7216e0e6ec"}}, {"head": {"id": "47e48a1e-5e6e-4efe-afb5-3d4d88ad4c9b", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757991101600, "endTime": 31758015854600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c01190b4-cd76-4758-ad62-4da51498a30c", "logId": "a3c1cc78-f9e2-4ffc-ba29-cf6b13be822d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c01190b4-cd76-4758-ad62-4da51498a30c", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757985987400}, "additional": {"logType": "detail", "children": [], "durationId": "47e48a1e-5e6e-4efe-afb5-3d4d88ad4c9b"}}, {"head": {"id": "3ac3be14-f695-4682-b539-9b5e656ab92a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757986416100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88dd1b54-742d-4cc6-a838-832413c08779", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757986526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b685dff-3a1e-431d-8dab-3590028df8f6", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757987832000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d50c2861-e1e2-46f2-8056-99c809d3c27e", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757991354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93502d5f-801d-476c-81a7-97bc939c0bcf", "name": "Incremental task entry:default@CompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758015481900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cf4e2a2-385b-4eea-9a49-7496a2fdea5f", "name": "entry : default@CompileResource cost memory -4.400581359863281", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758015740100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3c1cc78-f9e2-4ffc-ba29-cf6b13be822d", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757991101600, "endTime": 31758015854600}, "additional": {"logType": "info", "children": [], "durationId": "47e48a1e-5e6e-4efe-afb5-3d4d88ad4c9b"}}, {"head": {"id": "94e3da14-7c93-4007-b781-b34211bafcee", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758021471700, "endTime": 31758022984300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ef6698b2-d5d9-4812-bbc4-27903cf5877e", "logId": "66c0cc0e-1bcf-48dd-8678-52becb6644a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef6698b2-d5d9-4812-bbc4-27903cf5877e", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758018848200}, "additional": {"logType": "detail", "children": [], "durationId": "94e3da14-7c93-4007-b781-b34211bafcee"}}, {"head": {"id": "ec073627-17a3-4b05-b852-7152a9c7c044", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758019237800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3cebbd-033d-4de1-bd70-f9364602aa27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758019345300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65fe8aec-62e6-4963-9777-6b76fa6e2353", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758021483200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d771c00-3327-4a1d-9c20-4cffd9b9db6c", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758021867200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540ed7e7-14bd-4f7a-971c-6f365ca1231e", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758022804300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f65cffa-ef6d-4317-bf70-acc1789bf7d1", "name": "entry : default@DoNativeStrip cost memory 0.07445526123046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758022919700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c0cc0e-1bcf-48dd-8678-52becb6644a9", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758021471700, "endTime": 31758022984300}, "additional": {"logType": "info", "children": [], "durationId": "94e3da14-7c93-4007-b781-b34211bafcee"}}, {"head": {"id": "d2d901e4-d656-4d9d-9969-7249b9834838", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758028979700, "endTime": 31760203353700}, "additional": {"children": ["43020d9b-1189-4510-ab81-837490bc8a81", "d41490d4-fec2-4d0e-bef2-f81d60671fe4"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "a0122023-e17a-4ab4-8d40-1499b2e5b859", "logId": "2c8df50e-4b21-4da1-8f5e-1bb34adbcaa1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0122023-e17a-4ab4-8d40-1499b2e5b859", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758025036800}, "additional": {"logType": "detail", "children": [], "durationId": "d2d901e4-d656-4d9d-9969-7249b9834838"}}, {"head": {"id": "7560cf3b-0b1a-42dc-aeea-b383a86e86aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758025529000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f225155b-b6ea-4182-9730-1956d3ec2e38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758025634900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae163a7-30fc-42d7-946a-a55c8d3cc14b", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758028989800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22168b7e-611a-4eb1-b8c4-e7dac586a51b", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758042597100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f2c4e9-060f-4425-a921-9eca951b172a", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758042816900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85e6b766-f1fc-4caa-94a7-9a8a2c003528", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758057361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce261fad-4fa8-4b62-ad56-b7e321572dfe", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758057785600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6b3502a-8834-4db4-894b-4fd2009db637", "name": "default@CompileArkTS work[66] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758059253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43020d9b-1189-4510-ab81-837490bc8a81", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31758163447300, "endTime": 31760203067400}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d2d901e4-d656-4d9d-9969-7249b9834838", "logId": "777268f6-a3b8-4af8-a8aa-f09bc376bc8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6ce8c74-393f-4029-9d8e-44e9ba91594d", "name": "default@CompileArkTS work[66] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758060487500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb9c1d6-b158-478b-b0e6-66a8fa9b99f9", "name": "default@CompileArkTS work[66] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758060597200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aadc593e-640c-448d-8b55-c12b7dc6b9a3", "name": "CopyResources startTime: 31758060851900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758060856500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e6b35d7-171b-45cc-87dc-a8893dc50167", "name": "default@CompileArkTS work[67] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758060930300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d41490d4-fec2-4d0e-bef2-f81d60671fe4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31759499993300, "endTime": 31759525145100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d2d901e4-d656-4d9d-9969-7249b9834838", "logId": "441f6452-b894-4c81-b6f2-8fa73baeb2fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13e32e81-e728-4854-b760-3202212552d5", "name": "default@CompileArkTS work[67] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758061748400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcdb42df-0fb5-4ee8-bfd5-539447833826", "name": "default@CompileArkTS work[67] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758061851800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28857196-a09f-4475-bb59-e0b4d7e34f4e", "name": "entry : default@CompileArkTS cost memory 1.5890655517578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758061973500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45186c82-a709-447e-8a6b-4de130419234", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758069023700, "endTime": 31758073265000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "d8ec7629-e74c-4c97-8669-605f30ee2a00", "logId": "ce723b54-1701-4c7a-a436-05296758885d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8ec7629-e74c-4c97-8669-605f30ee2a00", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758063215300}, "additional": {"logType": "detail", "children": [], "durationId": "45186c82-a709-447e-8a6b-4de130419234"}}, {"head": {"id": "25660b01-ecdc-4063-9840-d5b450311b1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758063539600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f7ec075-a16d-4e2a-9a54-5d1eeab01f26", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758063631300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91074291-bc40-4ed2-bf4e-422c7f1fafd2", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758069035900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1ddb9c-9f57-4503-a19f-30c4076b6e81", "name": "entry : default@BuildJS cost memory 0.1278533935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758073049400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e86be09-f403-4110-b6ba-790a9f048cc5", "name": "runTaskFromQueue task cost before running: 667 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758073198800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce723b54-1701-4c7a-a436-05296758885d", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758069023700, "endTime": 31758073265000, "totalTime": 4151400}, "additional": {"logType": "info", "children": [], "durationId": "45186c82-a709-447e-8a6b-4de130419234"}}, {"head": {"id": "a7a492f7-a0b5-41ee-a1b5-318e4520f28d", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758077765800, "endTime": 31758079286400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "600eb750-5aa9-499d-a1d2-25a85eaa95eb", "logId": "5dfa3ec8-653c-44a7-8fe4-64792945ee73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "600eb750-5aa9-499d-a1d2-25a85eaa95eb", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758074914400}, "additional": {"logType": "detail", "children": [], "durationId": "a7a492f7-a0b5-41ee-a1b5-318e4520f28d"}}, {"head": {"id": "ba53b33c-f897-4b1b-be0b-68b9c143b0e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758075254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8f02fc-3e33-4d4b-8039-bdd08a2e5db5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758075350300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bffa39-d4cd-4fe5-b513-b572cb2a7b62", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758077777200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0526f5b-fef1-416d-9c05-47ffb261f42f", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758078081400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49711f4e-41f3-4875-9be8-8dafe9399ef0", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758079088300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b97edb95-1bcf-450e-8070-6f7845cc0669", "name": "entry : default@CacheNativeLibs cost memory 0.08840179443359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758079220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfa3ec8-653c-44a7-8fe4-64792945ee73", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758077765800, "endTime": 31758079286400}, "additional": {"logType": "info", "children": [], "durationId": "a7a492f7-a0b5-41ee-a1b5-318e4520f28d"}}, {"head": {"id": "486ad321-9998-4dbf-8f69-b30485e86243", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758163100700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff74fc58-5fd3-47f7-bf2d-0ca3cc594333", "name": "default@CompileArkTS work[66] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758163456000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411cf3fa-8385-41ff-ae4e-55eee528f912", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758163537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b02772c4-1383-4f02-a4cd-4b8bf7025d5a", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758163586200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08e8db0e-e4ae-421a-9e2a-1d88bccb4477", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758163680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f4880d-e06c-4393-9afb-e5e0fc55ce4a", "name": "default@CompileArkTS work[67] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758166486700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eeb67a0-ac87-43bf-a21f-e42e54a582f0", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31759527492500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf9ea85-f9af-41f1-bc1c-9b82a033302e", "name": "CopyResources is end, endTime: 31759528230500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31759528241500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a80991a-8eda-4918-9fb2-726dc8927bfb", "name": "default@CompileArkTS work[67] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31759528502000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "441f6452-b894-4c81-b6f2-8fa73baeb2fe", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31759499993300, "endTime": 31759525145100}, "additional": {"logType": "info", "children": [], "durationId": "d41490d4-fec2-4d0e-bef2-f81d60671fe4", "parent": "2c8df50e-4b21-4da1-8f5e-1bb34adbcaa1"}}, {"head": {"id": "8a42840d-6731-437b-91cc-c99201e1353e", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760070099400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de4c5098-4a71-4b3a-b0d2-0d87b0e7255f", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760202868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c27e53-0998-4078-a8b6-e9ea6eb89b4c", "name": "default@CompileArkTS work[66] failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760203202900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777268f6-a3b8-4af8-a8aa-f09bc376bc8b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31758163447300, "endTime": 31760203067400}, "additional": {"logType": "error", "children": [], "durationId": "43020d9b-1189-4510-ab81-837490bc8a81", "parent": "2c8df50e-4b21-4da1-8f5e-1bb34adbcaa1"}}, {"head": {"id": "2c8df50e-4b21-4da1-8f5e-1bb34adbcaa1", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31758028979700, "endTime": 31760203353700}, "additional": {"logType": "error", "children": ["777268f6-a3b8-4af8-a8aa-f09bc376bc8b", "441f6452-b894-4c81-b6f2-8fa73baeb2fe"], "durationId": "d2d901e4-d656-4d9d-9969-7249b9834838"}}, {"head": {"id": "d8d90b69-5616-4c71-bd1e-ec980edbc3f7", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760203579000}, "additional": {"logType": "debug", "children": [], "durationId": "d2d901e4-d656-4d9d-9969-7249b9834838"}}, {"head": {"id": "399ba06b-b4a3-42cc-8321-f5d819198e70", "name": "ERROR: stacktrace = Error: ArkTS Compiler Error\r\n\u001b[31m1 ERROR: \u001b[31mArkTS:ERROR File: D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/ets/view/ShuduGame.ets:146:42\n Property 'TopBottom' does not exist on type 'typeof GradientDirection'. Did you mean 'Bottom'?\n\r\n\u001b[39m\r\n\u001b[31m2 ERROR: \u001b[31mArkTS:ERROR File: D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/ets/view/ShuduGame.ets:298:16\n Property 'onReady' does not exist on type 'ColumnAttribute'.\n\r\n\u001b[39m\r\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:3}\u001b[39m\n    at runArkPack (C:\\Program Files\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor-ohos-plugin\\node_modules\\@ohos\\hvigor-arkts-compose\\dist\\src\\arkts-pack.js:1:5418)", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760206380400}, "additional": {"logType": "debug", "children": [], "durationId": "d2d901e4-d656-4d9d-9969-7249b9834838"}}, {"head": {"id": "dab2e296-013c-4627-8985-f7d8700c8f43", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760213655800, "endTime": 31760213858900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "089fdb10-71f1-43ed-b143-b208232022f4", "logId": "ad67f3a0-8cd5-4d6a-805a-8ece80029ae6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad67f3a0-8cd5-4d6a-805a-8ece80029ae6", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760213655800, "endTime": 31760213858900}, "additional": {"logType": "info", "children": [], "durationId": "dab2e296-013c-4627-8985-f7d8700c8f43"}}, {"head": {"id": "ec4e848f-d712-47b7-8914-6708bf7cc5d2", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31757406925300, "endTime": 31760214050000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 18}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "0f7f22fd-db53-4d94-938b-8c80307e3d8d", "name": "BUILD FAILED in 2 s 808 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760214104000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "e0440fe7-1c0c-463e-8956-dc374b45bc5e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760214276200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386abb4b-0a1e-4ffb-85ad-dd0ae15f9b21", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760214329200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d408f848-7819-4ee9-a8fa-371d2e6c1961", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760214373000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2673669-6948-4183-9ef9-64c4de78e1e4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760214415700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6eb95ef-f071-4e4d-9efd-0f840a221acb", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760214466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee23a88b-35a1-4be0-b018-84162abfb419", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760215438100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f607852f-edf8-497f-90f0-207bd1c4ddaa", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760216397700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0fb5a92-1e86-4cf7-a3d8-ee6487d6c34d", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760216832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a4d5a4-34a6-4d32-86e5-2768aa73be87", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760216928600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b00f299-0809-4d04-9145-814d754df4af", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760217026400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d302c31-97e4-415b-a263-51aadda6d71d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760217583200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ca46d6-5a82-4b86-8f4c-d0ef55f51b87", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760218590600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752e6390-51d1-42ea-9f90-5d3b00256a62", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760218858100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ef3e42e-3560-4294-b8ce-bf4743917a05", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760218927400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26343d6a-11b3-433a-a6b7-0f9d016bfde9", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760218978600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74fb801f-60b6-4991-a30b-bb76efc41d33", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219029300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d720cd-3312-47e3-921f-6d2d9f53ebe2", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4070f9c-f1af-4f83-adab-551631a8d057", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219342100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68e2ad8c-290a-4a21-a63e-86c8f369f833", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219519500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2707ff9f-42c4-45f5-b8f8-b98cfa67b219", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219688400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61868546-bb5d-4378-92cf-93e44fc0e65d", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219911100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "895a644e-1997-406b-8f38-6ecf289ef01d", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760219974900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4efc306b-241d-4fac-b043-0e05bacb996f", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760220025200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706f258c-fdf9-4fab-8bec-91e90a466da1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760221794300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44409658-1dc5-4f89-95cb-4ac5020528fd", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760222212400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017bb7ff-61c9-433f-93b8-9f49a197b863", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760223186000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4887aec1-489f-4f44-a9a1-fbe532d8fe52", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760223403700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae8f5aa-6478-40ab-ae07-7c2452937cf7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760223596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f825ae-f528-4fac-a175-8721383cdf7a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760224249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b34f62a-f2f5-468f-945f-6d45e7683691", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760224323000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b89820b9-662c-45b7-95b1-83e018f7e7d8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760224496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33063266-5904-4d1e-a9b3-d6496474dd98", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760224702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "656c0a27-0b8c-4143-bd03-86e867b24dbe", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760224949100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2298db2-81b0-43c2-bd96-87ec5319c333", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760226081300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0296bcad-f4bb-4184-aa0f-8b6f17f5a18f", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760226674000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "391239fb-c0bc-4f47-94ff-f4d6d869b46d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760227379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e24c8140-161d-4070-9ebd-4dc9fc1d3e9f", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760227570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516924ec-d932-4370-94f1-d6d5a3f5d857", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760227738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e928d4cd-fa0f-4273-b013-c0f7bd5bed4a", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760228201100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b31994-e4f6-47fa-b61f-f887bfe256b2", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760228421200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d5f7ad-92ba-46e7-817f-a233395d34c2", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760228492300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}