if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface GameSettings_Params {
    pageInfos?: NavPathStack;
    scroller?: Scroller;
    ctx?: UIContext;
    diag?: CommonDialog;
    dataPreferences?: preferences.Preferences | null;
    difficulty?: string;
    musicOpen?: boolean;
    isLoading?: boolean;
}
import CommonConstants from "@bundle:com.atomicservice.6917576098804821135/entry/ets/common/CommonConstants";
import Logger from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/Logger";
import preferences from "@ohos:data.preferences";
import { CommonDialog } from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/CommonDialog";
export function GameSettingsBuilder(name: string, param: Object, parent = null) {
    {
        (parent ? parent : this).observeComponentCreation2((elmtId, isInitialRender) => {
            if (isInitialRender) {
                let componentCall = new GameSettings(parent ? parent : this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/Personalization/GameSettings.ets", line: 9, col: 3 });
                ViewPU.create(componentCall);
                let paramsLambda = () => {
                    return {};
                };
                componentCall.paramsGenerator_ = paramsLambda;
            }
            else {
                (parent ? parent : this).updateStateVarsOfChildByElmtId(elmtId, {});
            }
        }, { name: "GameSettings" });
    }
}
export class GameSettings extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.pageInfos = new NavPathStack();
        this.scroller = new Scroller();
        this.ctx = this.getUIContext();
        this.diag = new CommonDialog();
        this.dataPreferences = null;
        this.__difficulty = new ObservedPropertySimplePU('easy', this, "difficulty");
        this.__musicOpen = new ObservedPropertySimplePU(false, this, "musicOpen");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: GameSettings_Params) {
        if (params.pageInfos !== undefined) {
            this.pageInfos = params.pageInfos;
        }
        if (params.scroller !== undefined) {
            this.scroller = params.scroller;
        }
        if (params.ctx !== undefined) {
            this.ctx = params.ctx;
        }
        if (params.diag !== undefined) {
            this.diag = params.diag;
        }
        if (params.dataPreferences !== undefined) {
            this.dataPreferences = params.dataPreferences;
        }
        if (params.difficulty !== undefined) {
            this.difficulty = params.difficulty;
        }
        if (params.musicOpen !== undefined) {
            this.musicOpen = params.musicOpen;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: GameSettings_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__difficulty.purgeDependencyOnElmtId(rmElmtId);
        this.__musicOpen.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__difficulty.aboutToBeDeleted();
        this.__musicOpen.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private pageInfos: NavPathStack;
    private scroller: Scroller;
    private ctx: UIContext;
    private diag: CommonDialog;
    private dataPreferences: preferences.Preferences | null;
    private __difficulty: ObservedPropertySimplePU<string>;
    get difficulty() {
        return this.__difficulty.get();
    }
    set difficulty(newValue: string) {
        this.__difficulty.set(newValue);
    }
    private __musicOpen: ObservedPropertySimplePU<boolean>;
    get musicOpen() {
        return this.__musicOpen.get();
    }
    set musicOpen(newValue: boolean) {
        this.__musicOpen.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            NavDestination.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.height(CommonConstants.FULL_PARENT);
                    Column.width(CommonConstants.FULL_PARENT);
                    Column.backgroundColor('#F5F5F5');
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.isLoading) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                LoadingProgress.create();
                                LoadingProgress.color('#EE7CDF');
                                LoadingProgress.width(50);
                                LoadingProgress.height(50);
                            }, LoadingProgress);
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Scroll.create(this.scroller);
                                Scroll.scrollable(ScrollDirection.Vertical);
                                Scroll.scrollBar(BarState.Off);
                                Scroll.edgeEffect(EdgeEffect.Spring);
                            }, Scroll);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Column.create({ space: 20 });
                                Column.width(CommonConstants.FULL_PARENT);
                                Column.padding({ top: 20, bottom: 20 });
                                Column.justifyContent(FlexAlign.Start);
                                Column.alignItems(HorizontalAlign.Center);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 难度设置
                                Column.create({ space: 10 });
                                // 难度设置
                                Column.width('100%');
                                // 难度设置
                                Column.alignItems(HorizontalAlign.Center);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('难度设置');
                                Text.fontSize(18);
                                Text.fontWeight(FontWeight.Medium);
                                Text.width('90%');
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.width('90%');
                                Row.justifyContent(FlexAlign.SpaceBetween);
                                Row.padding(10);
                                Row.backgroundColor('#FFFFFF');
                                Row.borderRadius(12);
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('简单');
                                Text.fontSize(16);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Radio.create({ value: 'easy', group: 'difficultyGroup' });
                                Radio.checked(this.difficulty === 'easy');
                                Radio.onChange((isChecked: boolean) => {
                                    if (isChecked) {
                                        this.difficulty = 'easy';
                                        this.saveSettings();
                                    }
                                });
                            }, Radio);
                            Row.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.width('90%');
                                Row.justifyContent(FlexAlign.SpaceBetween);
                                Row.padding(10);
                                Row.backgroundColor('#FFFFFF');
                                Row.borderRadius(12);
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('中等');
                                Text.fontSize(16);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Radio.create({ value: 'medium', group: 'difficultyGroup' });
                                Radio.checked(this.difficulty === 'medium');
                                Radio.onChange((isChecked: boolean) => {
                                    if (isChecked) {
                                        this.difficulty = 'medium';
                                        this.saveSettings();
                                    }
                                });
                            }, Radio);
                            Row.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.width('90%');
                                Row.justifyContent(FlexAlign.SpaceBetween);
                                Row.padding(10);
                                Row.backgroundColor('#FFFFFF');
                                Row.borderRadius(12);
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('困难');
                                Text.fontSize(16);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Radio.create({ value: 'hard', group: 'difficultyGroup' });
                                Radio.checked(this.difficulty === 'hard');
                                Radio.onChange((isChecked: boolean) => {
                                    if (isChecked) {
                                        this.difficulty = 'hard';
                                        this.saveSettings();
                                    }
                                });
                            }, Radio);
                            Row.pop();
                            // 难度设置
                            Column.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 音效设置
                                Column.create({ space: 10 });
                                // 音效设置
                                Column.width('100%');
                                // 音效设置
                                Column.alignItems(HorizontalAlign.Center);
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('音效设置');
                                Text.fontSize(18);
                                Text.fontWeight(FontWeight.Medium);
                                Text.width('90%');
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Row.create();
                                Row.width('90%');
                                Row.justifyContent(FlexAlign.SpaceBetween);
                                Row.padding(10);
                                Row.backgroundColor('#FFFFFF');
                                Row.borderRadius(12);
                            }, Row);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('游戏音效');
                                Text.fontSize(16);
                            }, Text);
                            Text.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Toggle.create({ type: ToggleType.Switch, isOn: { value: this.musicOpen, changeEvent: newValue => { this.musicOpen = newValue; } } });
                                Toggle.onChange((isOn: boolean) => {
                                    this.musicOpen = isOn;
                                    this.saveSettings();
                                });
                            }, Toggle);
                            Toggle.pop();
                            Row.pop();
                            // 音效设置
                            Column.pop();
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                // 重置按钮
                                Button.createWithLabel('恢复默认设置');
                                // 重置按钮
                                Button.width('90%');
                                // 重置按钮
                                Button.height(50);
                                // 重置按钮
                                Button.backgroundColor('#EE7CDF');
                                // 重置按钮
                                Button.borderRadius(24);
                                // 重置按钮
                                Button.margin({ top: 20 });
                                // 重置按钮
                                Button.onClick(() => {
                                    this.resetSettings();
                                });
                            }, Button);
                            // 重置按钮
                            Button.pop();
                            Column.pop();
                            Scroll.pop();
                        });
                    }
                }, If);
                If.pop();
                Column.pop();
            }, { moduleName: "entry", pagePath: "entry/src/main/ets/view/Personalization/GameSettings" });
            NavDestination.title('游戏设置');
            NavDestination.onShown(() => {
                console.log("出现了");
                if (this.dataPreferences) {
                    this.difficulty = this.dataPreferences.getSync('difficulty', 'easy') as 'easy' | 'medium' | 'hard';
                    this.musicOpen = this.dataPreferences.getSync('musicOpen', false) as boolean;
                    console.log(this.musicOpen + "-----" + this.difficulty);
                }
            });
            NavDestination.onReady((context: NavDestinationContext) => {
                this.pageInfos = context.pathStack;
                Logger.info("GameSettings page ready");
            });
        }, NavDestination);
        NavDestination.pop();
    }
    aboutToAppear() {
        // 获取偏好设置
        let options: preferences.Options = { name: 'myStore' };
        this.dataPreferences = preferences.getPreferencesSync(getContext(this), options);
        this.isLoading = false;
    }
    saveSettings() {
        if (this.dataPreferences) {
            // 保存难度设置
            this.dataPreferences.putSync('difficulty', this.difficulty);
            // 保存音效设置
            this.dataPreferences.putSync('musicOpen', this.musicOpen);
            // 刷新设置
            this.dataPreferences.flushSync();
        }
    }
    resetSettings() {
        this.difficulty = 'easy';
        this.musicOpen = false;
        this.saveSettings();
        this.diag.openMessageDiag(this.ctx, "已恢复默认设置", () => { });
    }
    rerender() {
        this.updateDirtyElements();
    }
}
(function () {
    if (typeof NavigationBuilderRegister === "function") {
        NavigationBuilderRegister("gameSettings", wrapBuilder(GameSettingsBuilder));
    }
})();
