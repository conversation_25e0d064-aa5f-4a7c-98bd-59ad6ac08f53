{":SheShudu:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"b8c1ba3ecddcd8b1be17325fb873c8bc\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":14,\"_valueType\":\"number\",\"_hash\":\"9818aaec7bfe7504d81668c694051752\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"6b8c09abb74b3d778d7ce1bc1efb26ef\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"c489be8273867a50afbc86c53d938c92\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":true,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"routerMap\\\":\\\"$profile:router_map\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:icon\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"5bb361323d0581701a6606e5ab9535c5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"b174ea6ff5824844dde5ad92f6b3ef2b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"*********\",\"_valueType\":\"string\",\"_hash\":\"96bb1e6c7751a5eed3b10dea091ac599\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"45c309ddef659bd1ca26b5b8988a1a78\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@PreBuild", "_executionId": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@PreBuild:1750751647522", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "c5dcb19255868cf3b91f3717f30afd9c"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "4401b2c8b2f2b4b3c30680d37994f66e"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5", {"fileSnapShotHashValue": "edef0d26517b5ef56657bfbb4c9fcf9d"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5", {"fileSnapShotHashValue": "71b0ce8ff2261eeca66e2a21673cc710"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json", {"fileSnapShotHashValue": "907278b8a785111b40abc99464f92855"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "921f2b190ebf7ae76eeedcbae2fcc680"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "84d0cda9ded7038be8791d2855a53b2c"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "e4bbe9a806dc79d752f19bf7a5528092"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\oh-package.json5", {"fileSnapShotHashValue": "d4e151d0d6367b78bac8c208919cae69"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\oh-package.json5", {"fileSnapShotHashValue": "b893d8fb433f8fc44f390046b755ecd9"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":SheShudu:entry:default@GenerateMetadata": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"artifactName\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7f42b9cbe0181813f8dc60881bf90988\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSigned\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a1368a74a9a6bab3a8b88ab0063c1681\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"relatedEntryModules\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"85b4d1fa598cae3838191eb99a09f4dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"remoteHspMetaData\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"4789086a6779d6842529a03edde7237d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetDeviceType\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"7c784b382cfa2c3a26c881fb43380eab\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@GenerateMetadata", "_key": ":SheShu<PERSON>:entry:default@GenerateMetadata", "_executionId": ":SheShudu:entry:default@GenerateMetadata:1750039466111", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "4401b2c8b2f2b4b3c30680d37994f66e"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\hap_metadata\\default\\output_metadata.json", {"fileSnapShotHashValue": "4e5596b3e2db41b4e06784aeb4e06b0d"}]]}}, ":SheShudu:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.atomicservice.6917576098804821135\\\",\\\"bundleType\\\":\\\"atomicService\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:app_icon\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"c5a4ec5c4b2a440fbc91ed206a4efc49\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"bc2129ba20a21b7e5234139ede1b4d7b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\",\"_hash\":\"40d5093f345351dd6d67ce5d6a209345\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\",\"_hash\":\"4c2dd7bb7507f74001ec27f2936db7f9\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"3c48b1937556f5c7e1ff0b2e0af0184f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d35c8440e915c3a94c482ddd6f7af075\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"edbf05a2d2be2c385e75d9565a48d419\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a15b7a3ed818faa99a4a10d67f52cb72\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":true,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"routerMap\\\":\\\"$profile:router_map\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:icon\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}]}}\",\"_valueType\":\"string\",\"_hash\":\"40ed32832d6fb268ad021252c0a06d6e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"555604752defc243b4e4c55d1549fc06\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":14,\"_valueType\":\"number\",\"_hash\":\"a0871669f157ed4efdda103625c1fcf4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"086cac69f102cdd9ee25e54982ad7b76\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"3b15da42c5f4b695fbd1d0b43191764a\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":She<PERSON>hu<PERSON>:entry:default@MergeProfile", "_executionId": ":SheShudu:entry:default@MergeProfile:1750039466125", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\app.json5", {"fileSnapShotHashValue": "c5dcb19255868cf3b91f3717f30afd9c"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5", {"fileSnapShotHashValue": "edef0d26517b5ef56657bfbb4c9fcf9d"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "4401b2c8b2f2b4b3c30680d37994f66e"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "d193b0d43974b365118c90a1a82bc931"}]]}}, ":SheShudu:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@CreateBuildProfile", "_executionId": ":She<PERSON>hu<PERSON>:entry:default@CreateBuildProfile:1750035916819", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\app.json5", {"fileSnapShotHashValue": "c5dcb19255868cf3b91f3717f30afd9c"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5", {"fileSnapShotHashValue": "edef0d26517b5ef56657bfbb4c9fcf9d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "d42e7fe1789c419ba10e315e70102f28"}]]}}, ":SheShudu:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9141bebef47c71175aada1bd7bf\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":She<PERSON>hu<PERSON>:entry:default@GeneratePkgContextInfo", "_executionId": ":SheShudu:entry:default@GeneratePkgContextInfo:1750757302553", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@ProcessIntegratedHsp": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@ProcessIntegratedHsp", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@ProcessIntegratedHsp", "_executionId": ":She<PERSON><PERSON><PERSON>:entry:default@ProcessIntegratedHsp:1750757302563", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@MakePackInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appResOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.atomicservice.6917576098804821135\\\",\\\"bundleType\\\":\\\"atomicService\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:app_icon\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"1da703925b17afe8d3ad1d7cfae511f8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"3c48b1937556f5c7e1ff0b2e0af0184f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileSdkVersion\",\"_value\":14,\"_valueType\":\"number\",\"_hash\":\"cf4a6e562cdde15320ad72f5f3dd193d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"c166d88837153e59136adb6a4b649962\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"routerMap\\\":\\\"$profile:router_map\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:icon\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}]}}\",\"_valueType\":\"string\",\"_hash\":\"bff0c44d95fb2cadcba2778ad0249506\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@MakePackInfo", "_key": ":She<PERSON>hu<PERSON>:entry:default@MakePackInfo", "_executionId": ":SheShu<PERSON>:entry:default@MakePackInfo:1750039466162", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\app.json5", {"fileSnapShotHashValue": "c5dcb19255868cf3b91f3717f30afd9c"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "4401b2c8b2f2b4b3c30680d37994f66e"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5", {"fileSnapShotHashValue": "edef0d26517b5ef56657bfbb4c9fcf9d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info", {"fileSnapShotHashValue": "df9d21016ad5c09c71e9a38c287f6106"}]]}}, ":SheShudu:entry:default@SyscapTransform": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@SyscapTransform", "_key": ":SheShu<PERSON>:entry:default@SyscapTransform", "_executionId": ":SheShudu:entry:default@SyscapTransform:1750757302594", "_inputFiles": {"dataType": "Map", "value": [["C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe", {"fileSnapShotHashValue": "f51c97724363f4067537d46eea7dbec3"}], ["C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define", {"fileSnapShotHashValue": "50350430e0e86d363f41395f9457979d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc", {"fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"750b4bda198545a67903dfb3f6a00a95\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"beta1\",\"_valueType\":\"string\",\"_hash\":\"e588408901fe47899eadeda64a5c41fb\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\",\"_hash\":\"ac54f3d4ced2d4c1d666d40e4f7c454a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"c166d88837153e59136adb6a4b649962\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":She<PERSON>hu<PERSON>:entry:default@ProcessProfile", "_executionId": ":She<PERSON>hu<PERSON>:entry:default@ProcessProfile:1750039466178", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "d193b0d43974b365118c90a1a82bc931"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "472b02efc6b886674d92e8df8f6aae5d"}]]}}, ":SheShudu:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"45c309ddef659bd1ca26b5b8988a1a78\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@ProcessRouterMap", "_executionId": ":She<PERSON>hu<PERSON>:entry:default@ProcessRouterMap:1750751647966", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\oh-package.json5", {"fileSnapShotHashValue": "d4e151d0d6367b78bac8c208919cae69"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\oh-package.json5", {"fileSnapShotHashValue": "b893d8fb433f8fc44f390046b755ecd9"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "4401b2c8b2f2b4b3c30680d37994f66e"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json", {"fileSnapShotHashValue": "907278b8a785111b40abc99464f92855"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json", {"fileSnapShotHashValue": "cc1091caf270d2ef51ea4c1c211d2d62"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "8ddfeb1f8923d66efd62cb905ce997bb"}]]}}, ":SheShudu:entry:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\opt-compression.json\",\"_value\":\"{\\\"context\\\":{\\\"extensionPath\\\":\\\"C:\\\\\\\\Program Files\\\\\\\\Huawei\\\\\\\\DevEco Studio\\\\\\\\sdk\\\\\\\\default\\\\\\\\hms\\\\\\\\toolchains\\\\\\\\lib\\\\\\\\libimage_transcoder_shared.dll\\\"},\\\"compression\\\":{\\\"media\\\":{\\\"enable\\\":false},\\\"filters\\\":[]}}\",\"_valueType\":\"string\",\"_hash\":\"3e697fb8f30306f565ac65fdd685fc1e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\process_profile\\\\\\\\default\\\\\\\\module.json\\\",\\\"packageName\\\":\\\"com.atomicservice.6917576098804821135\\\",\\\"output\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\",\\\"moduleNames\\\":\\\"entry\\\",\\\"ResourceTable\\\":[\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\generated\\\\\\\\r\\\\\\\\default\\\\\\\\ResourceTable.h\\\"],\\\"applicationResource\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\AppScope\\\\\\\\resources\\\",\\\"moduleResources\\\":[\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\resources\\\"],\\\"dependencies\\\":[],\\\"iconCheck\\\":false,\\\"ids\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\",\\\"definedIds\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ids_map\\\\\\\\id_defined.json\\\",\\\"definedSysIds\\\":\\\"C:\\\\\\\\Program Files\\\\\\\\Huawei\\\\\\\\DevEco Studio\\\\\\\\sdk\\\\\\\\default\\\\\\\\hms\\\\\\\\toolchains\\\\\\\\id_defined.json\\\"}\",\"_valueType\":\"string\",\"_hash\":\"97bf9463d2f2a3d6a93544f6182bafad\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resource_str\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c59ebe125e2aaa014a4454c9564cf3c6\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@ProcessResource", "_key": ":She<PERSON>hu<PERSON>:entry:default@ProcessResource", "_executionId": ":SheShudu:entry:default@ProcessResource:1750035917037", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "7dd0171aa1c952ed2a7495895b00f5ae"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", {"isDirectory": false, "fileSnapShotHashValue": "c6c2f624c1205861f1ffc535967df8dc"}]]}}, ":SheShudu:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\modules.ap\",\"_valueType\":\"string\",\"_hash\":\"d2e82fadd193a2c093d6154ec0e5893c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":12,\"_valueType\":\"number\",\"_hash\":\"b8c1ba3ecddcd8b1be17325fb873c8bc\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":14,\"_valueType\":\"number\",\"_hash\":\"9818aaec7bfe7504d81668c694051752\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\"}\",\"_valueType\":\"string\",\"_hash\":\"97f2bea0600635b68d1e77c413e70a7c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"974757f304b5bfd1c1454ea7a38cd0d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\",\"_hash\":\"7f73d283b9b015b2ba6a7ea100024a2f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_value\":\"{\\\"changedFileList\\\":\\\"D:\\\\\\\\yysrc\\\\\\\\sourcecode\\\\\\\\mrhp-sharedoc\\\\\\\\2025\\\\\\\\SheShudu\\\\\\\\entry\\\\\\\\build\\\\\\\\default\\\\\\\\intermediates\\\\\\\\patch\\\\\\\\default\\\\\\\\changedFileList.json\\\"}\",\"_valueType\":\"string\",\"_hash\":\"82cf76dcd8a9dccf0f02048645565f83\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\",\"_valueType\":\"string\",\"_hash\":\"027c51f086ad19f613ac5ae45d36903b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"45c309ddef659bd1ca26b5b8988a1a78\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":SheShudu:entry:default@GenerateLoaderJson", "_executionId": ":SheShudu:entry:default@GenerateLoaderJson:1750751648005", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "e4bbe9a806dc79d752f19bf7a5528092"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "8ddfeb1f8923d66efd62cb905ce997bb"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json", {"fileSnapShotHashValue": "cc1091caf270d2ef51ea4c1c211d2d62"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "ce145cb0159488bd1feb5a0547820554"}]]}}, ":SheShudu:entry:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"excludeFromHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@ProcessLibs", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@ProcessLibs", "_executionId": ":She<PERSON>hu<PERSON>:entry:default@ProcessLibs:1750757302755", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5", {"fileSnapShotHashValue": "edef0d26517b5ef56657bfbb4c9fcf9d"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5", {"fileSnapShotHashValue": "71b0ce8ff2261eeca66e2a21673cc710"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe,-l,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json\",\"_valueType\":\"string\",\"_hash\":\"bc333f26e20ee9f382481ae7194b0694\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\",\"_hash\":\"a8d4d3e21eee2971605e6391e6f8afa7\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"ebe85c5c20c0e5d9e03bc5dc7135f953\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@CompileResource", "_key": ":SheShudu:entry:default@CompileResource", "_executionId": ":SheShudu:entry:default@CompileResource:1750751648055", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "00ea666441b89c69e64b5e41bb496f57"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources", {"fileSnapShotHashValue": "9063e9ef4a72fddad83236abe78958d3"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "472b02efc6b886674d92e8df8f6aae5d"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json", {"fileSnapShotHashValue": "cc1091caf270d2ef51ea4c1c211d2d62"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "7dd0171aa1c952ed2a7495895b00f5ae"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "0cac314c3a4a5ac9700627a913eace82"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": "0fe60e1219ccb20897b872d617d96598"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "3d688d4cb308d1108f3e905d87967a77"}]]}}, ":SheShudu:entry:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"055eeab6818b3f1a4978705cfba272fa\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@DoNativeStrip", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@DoNativeStrip", "_executionId": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@DoNativeStrip:1750035917418", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@CompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"beta1\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@CompileArkTS", "_key": ":She<PERSON>hu<PERSON>:entry:default@CompileArkTS", "_executionId": ":SheShu<PERSON>:entry:default@CompileArkTS:1750757302810", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "5b0a95e70062d9b3d4f67290fcd09563"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": "9e8f6426e3f5123af718b98b761a1ff1"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "7f2dbf427c5cfb92881e4380ec41742f"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", {"fileSnapShotHashValue": "561a07cfb5f2ba9106871eba9b52fbfb"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "44d7f147ed53d7ced22d7ed8f8d34cb3"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "9f32efbcf30133672af8a80eed1308dc"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "d42e7fe1789c419ba10e315e70102f28"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "e4bbe9a806dc79d752f19bf7a5528092"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": "23f4f9ce28b2a05b0a36ac01f327c0de"}]]}}, ":SheShudu:entry:default@BuildJS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"beta1\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@BuildJS", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@BuildJS", "_executionId": ":She<PERSON>hu<PERSON>:entry:default@BuildJS:1750757302845", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "5b0a95e70062d9b3d4f67290fcd09563"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile", {"isDirectory": true, "fileSnapShotHashValue": "9e8f6426e3f5123af718b98b761a1ff1"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "7f2dbf427c5cfb92881e4380ec41742f"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json", {"fileSnapShotHashValue": "561a07cfb5f2ba9106871eba9b52fbfb"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "44d7f147ed53d7ced22d7ed8f8d34cb3"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@CacheNativeLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"055eeab6818b3f1a4978705cfba272fa\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@CacheNativeLibs", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@CacheNativeLibs", "_executionId": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@CacheNativeLibs:1750035918764", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\patch\\default\\base_native_libs.json", {"isDirectory": false, "fileSnapShotHashValue": "562a5608edcd653238f20490c6010014"}]]}}, ":SheShudu:entry:default@GeneratePkgModuleJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@GeneratePkgModuleJson", "_key": ":SheShu<PERSON>:entry:default@GeneratePkgModuleJson", "_executionId": ":SheShudu:entry:default@GeneratePkgModuleJson:1750751650713", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"fileSnapShotHashValue": "132f918f7a52a201a279da5ad24e0079"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json", {"fileSnapShotHashValue": "132f918f7a52a201a279da5ad24e0079"}]]}}, ":SheShudu:entry:default@PackageHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"hotReload\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceMapDir\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"bundleType\",\"_value\":\"atomicService\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"java,-Dfile.encoding=GBK,-jar,C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar,--mode,hap,--force,true,--lib-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default,--json-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json,--resources-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources,--index-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index,--pack-info-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info,--out-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap,--ets-path,D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@PackageHap", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@PackageHap", "_executionId": ":SheShu<PERSON>:entry:default@PackageHap:1750757304731", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default", {"isDirectory": false, "fileSnapShotHashValue": ""}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "132f918f7a52a201a279da5ad24e0079"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources", {"isDirectory": false, "fileSnapShotHashValue": "80d588aca6efd1b5215c28c70a83ab28"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index", {"isDirectory": false, "fileSnapShotHashValue": "b4d402a93128f13c9948eaee0535e89a"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info", {"isDirectory": false, "fileSnapShotHashValue": "df9d21016ad5c09c71e9a38c287f6106"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": false, "fileSnapShotHashValue": "23f4f9ce28b2a05b0a36ac01f327c0de"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", {"isDirectory": false, "fileSnapShotHashValue": "d688f00e59fd83ac4c6d0a968edc1662"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "d42e7fe1789c419ba10e315e70102f28"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "4a570aabcb7312fee10dbc174b5d899c"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map", {"fileSnapShotHashValue": "d688f00e59fd83ac4c6d0a968edc1662"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map", {"fileSnapShotHashValue": "d688f00e59fd83ac4c6d0a968edc1662"}]]}}, ":SheShudu:entry:default@SignHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"enableSignTask\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"*********\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@SignHap", "_key": ":<PERSON><PERSON><PERSON><PERSON>:entry:default@SignHap", "_executionId": ":She<PERSON><PERSON><PERSON>:entry:default@SignHap:1750757305277", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "4a570aabcb7312fee10dbc174b5d899c"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":SheShudu:entry:default@CollectDebugSymbol": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "<PERSON><PERSON><PERSON><PERSON>", "_moduleName": "entry", "_taskName": "default@CollectDebugSymbol", "_key": ":She<PERSON>hu<PERSON>:entry:default@CollectDebugSymbol", "_executionId": ":SheShu<PERSON>:entry:default@CollectDebugSymbol:1750757305288", "_inputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map", {"fileSnapShotHashValue": "d688f00e59fd83ac4c6d0a968edc1662"}], ["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}