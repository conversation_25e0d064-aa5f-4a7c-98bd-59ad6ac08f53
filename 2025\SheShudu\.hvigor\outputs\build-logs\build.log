[2025-06-24T17:27:27.063] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.064] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.071] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-24T17:27:27.095] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .
[2025-06-24T17:27:27.095] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7670440673828125
[2025-06-24T17:27:27.100] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-24T17:27:27.104] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.104] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.105] [DEBUG] debug-file - Executing task :entry:default@ProcessLibs
[2025-06-24T17:27:27.107] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.107] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.108] [DEBUG] debug-file - entry : default@ProcessLibs cost memory 0.1252288818359375
[2025-06-24T17:27:27.108] [DEBUG] debug-file - runTaskFromQueue task cost before running: 588 ms 
[2025-06-24T17:27:27.109] [INFO] debug-file - Finished :entry:default@ProcessLibs... after 4 ms 
[2025-06-24T17:27:27.111] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.111] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.112] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@CompileResource
[2025-06-24T17:27:27.116] [DEBUG] debug-file - Executing task :entry:default@CompileResource
[2025-06-24T17:27:27.141] [DEBUG] debug-file - Incremental task entry:default@CompileResource pre-execution cost: 24 ms .
[2025-06-24T17:27:27.141] [DEBUG] debug-file - entry : default@CompileResource cost memory 1.3920822143554688
[2025-06-24T17:27:27.143] [INFO] debug-file - UP-TO-DATE :entry:default@CompileResource...  
[2025-06-24T17:27:27.145] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.145] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.149] [DEBUG] debug-file - Executing task :entry:default@DoNativeStrip
[2025-06-24T17:27:27.149] [DEBUG] debug-file - Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:27.150] [DEBUG] debug-file - Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-06-24T17:27:27.150] [DEBUG] debug-file - entry : default@DoNativeStrip cost memory 0.07317352294921875
[2025-06-24T17:27:27.150] [INFO] debug-file - UP-TO-DATE :entry:default@DoNativeStrip...  
[2025-06-24T17:27:27.153] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.154] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.157] [DEBUG] debug-file - Executing task :entry:default@CompileArkTS
[2025-06-24T17:27:27.171] [DEBUG] debug-file - entry:default@CompileArkTS is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets' has been changed.
[2025-06-24T17:27:27.172] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .
[2025-06-24T17:27:27.188] [DEBUG] debug-file - build config:
[2025-06-24T17:27:27.189] [DEBUG] debug-file - {
  moduleType: 'entry',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile',
  etsLoaderPath: 'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader',
  modulePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
  compileSdkVersion: 14,
  compatibleSdkVersion: 12,
  compatibleSdkVersionStage: 'beta1',
  bundleName: 'c***5',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\modules.ap',
  entryModuleName: 'entry',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'entry',
  allModuleNameHash: '5f173d74b06d1b11d4ab34f4a3957465',
  externalApiPaths: [
    'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default',
  bundleType: 'atomicService',
  arkTSVersion: undefined,
  apiVersion: 14,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: { caseSensitiveCheck: true, useNormalizedOHMUrl: false },
  buildDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build',
  deviceTypes: [ 'phone', 'tablet' ],
  useNormalizedOHMUrl: false,
  pkgContextInfo: undefined,
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: undefined, definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  aceModuleJsonPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json',
  appResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt',
  rawFileResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile',
  resourceTableHash: 'b4dba2e09ce066d2be03c76cd5133ec1',
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:14:*********:Release',
  aceModuleRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\supervisual',
  aceBuildJson: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json',
  cachePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug',
  aceModuleBuild: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets',
  supportChunks: true,
  declaredFilesPath: undefined,
  pkgNameToPkgBriefInfo: {
    entry: {
      pkgRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'entry'
    }
  },
  projectModel: {
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\src\\ohosTest\\ets': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\ohosTest': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu': {
      moduleName: 'SheShudu',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    }
  },
  pkgJsonFileHash: 'd01805ed5ebaa3fd7df4966d8c5aaf3c',
  allModulePaths: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5',
    mockConfigKey2ModuleInfo: {}
  },
  otherPaths: {
    'entry/*': [
      '..\\*',
      '..\\..\\..\\build\\default\\generated\\profile\\default\\*'
    ]
  },
  collectImportersConfig: undefined
}
[2025-06-24T17:27:27.189] [DEBUG] debug-file - Compile arkts with external api path: C:\Program Files\Huawei\DevEco Studio\sdk\default\hms\ets
[2025-06-24T17:27:27.190] [DEBUG] debug-file - default@CompileArkTS work[77] is submitted.
[2025-06-24T17:27:27.191] [DEBUG] debug-file - default@CompileArkTS work[77] is pushed to ready queue.
[2025-06-24T17:27:27.191] [DEBUG] debug-file - default@CompileArkTS work[77] is not dispatched.
[2025-06-24T17:27:27.191] [DEBUG] debug-file - CopyResources startTime: 32279087367400
[2025-06-24T17:27:27.191] [DEBUG] debug-file - default@CompileArkTS work[78] is submitted.
[2025-06-24T17:27:27.192] [DEBUG] debug-file - default@CompileArkTS work[78] is pushed to ready queue.
[2025-06-24T17:27:27.192] [DEBUG] debug-file - default@CompileArkTS work[78] is not dispatched.
[2025-06-24T17:27:27.192] [DEBUG] debug-file - entry : default@CompileArkTS cost memory -4.825050354003906
[2025-06-24T17:27:27.195] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.195] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.198] [DEBUG] debug-file - Executing task :entry:default@BuildJS
[2025-06-24T17:27:27.201] [DEBUG] debug-file - entry : default@BuildJS cost memory 0.12694549560546875
[2025-06-24T17:27:27.201] [DEBUG] debug-file - runTaskFromQueue task cost before running: 681 ms 
[2025-06-24T17:27:27.201] [INFO] debug-file - Finished :entry:default@BuildJS... after 3 ms 
[2025-06-24T17:27:27.204] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:27.204] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:27.206] [DEBUG] debug-file - Executing task :entry:default@CacheNativeLibs
[2025-06-24T17:27:27.207] [DEBUG] debug-file - Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:27.208] [DEBUG] debug-file - Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .
[2025-06-24T17:27:27.208] [DEBUG] debug-file - entry : default@CacheNativeLibs cost memory 0.08661651611328125
[2025-06-24T17:27:27.208] [INFO] debug-file - UP-TO-DATE :entry:default@CacheNativeLibs...  
[2025-06-24T17:27:27.301] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:27:27.301] [DEBUG] debug-file - default@CompileArkTS work[77] has been dispatched to worker[4].
[2025-06-24T17:27:27.301] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:27:27.301] [DEBUG] debug-file - A work dispatched to worker[4] failed because of worker busy.
[2025-06-24T17:27:27.301] [DEBUG] debug-file - Create  resident worker with id: 3.
[2025-06-24T17:27:27.305] [DEBUG] debug-file - default@CompileArkTS work[78] has been dispatched to worker[3].
[2025-06-24T17:27:28.773] [DEBUG] debug-file - worker[3] has one work done.
[2025-06-24T17:27:28.774] [DEBUG] debug-file - CopyResources is end, endTime: 32280669961900
[2025-06-24T17:27:28.774] [DEBUG] debug-file - default@CompileArkTS work[78] done.
[2025-06-24T17:27:29.190] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:27:29.196] [DEBUG] debug-file - default@CompileArkTS work[77] done.
[2025-06-24T17:27:29.198] [INFO] debug-file - Finished :entry:default@CompileArkTS... after 1 s 924 ms 
[2025-06-24T17:27:29.200] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.200] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.201] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgModuleJson
[2025-06-24T17:27:29.201] [DEBUG] debug-file - Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:29.202] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .
[2025-06-24T17:27:29.202] [DEBUG] debug-file - entry : default@GeneratePkgModuleJson cost memory 0.07498931884765625
[2025-06-24T17:27:29.202] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgModuleJson...  
[2025-06-24T17:27:29.205] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.205] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.211] [DEBUG] debug-file - Executing task :entry:default@PackageHap
[2025-06-24T17:27:29.223] [DEBUG] debug-file - entry:default@PackageHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets' has been changed.
[2025-06-24T17:27:29.223] [DEBUG] debug-file - Incremental task entry:default@PackageHap pre-execution cost: 12 ms .
[2025-06-24T17:27:29.223] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.224] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.226] [DEBUG] debug-file - Use tool [C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\lib\app_packing_tool.jar]
 [
  'java',
  '-Dfile.encoding=GBK',
  '-jar',
  'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar',
  '--mode',
  'hap',
  '--force',
  'true',
  '--lib-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default',
  '--json-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json',
  '--resources-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources',
  '--index-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index',
  '--pack-info-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info',
  '--out-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap',
  '--ets-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets'
]
[2025-06-24T17:27:29.228] [DEBUG] debug-file - default@PackageHap work[79] is submitted.
[2025-06-24T17:27:29.229] [DEBUG] debug-file - default@PackageHap work[79] is pushed to ready queue.
[2025-06-24T17:27:29.229] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:27:29.229] [DEBUG] debug-file - default@PackageHap work[79] has been dispatched to worker[4].
[2025-06-24T17:27:29.229] [DEBUG] debug-file - default@PackageHap work[79] is dispatched.
[2025-06-24T17:27:29.234] [DEBUG] debug-file - entry : default@PackageHap cost memory 1.2945404052734375
[2025-06-24T17:27:29.231] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 794734592,
  heapTotal: 505585664,
  heapUsed: 440753984,
  external: 4875594,
  arrayBuffers: 674181
} os memoryUsage :25.15814208984375
[2025-06-24T17:27:29.300] [DEBUG] debug-file - A work dispatched to worker[4] failed because of worker busy.
[2025-06-24T17:27:29.347] [DEBUG] debug-file - A work dispatched to worker[3] failed because unable to get work from ready queue.
[2025-06-24T17:27:29.793] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:27:29.793] [DEBUG] debug-file - default@PackageHap work[79] done.
[2025-06-24T17:27:29.793] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-24T17:27:29.795] [INFO] debug-file - Finished :entry:default@PackageHap... after 583 ms 
[2025-06-24T17:27:29.797] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.797] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.800] [DEBUG] debug-file - Executing task :entry:default@SignHap
[2025-06-24T17:27:29.800] [WARN] debug-file - Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5.
[2025-06-24T17:27:29.801] [DEBUG] debug-file - entry:default@SignHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap' has been changed.
[2025-06-24T17:27:29.801] [DEBUG] debug-file - Incremental task entry:default@SignHap pre-execution cost: 1 ms .
[2025-06-24T17:27:29.801] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.801] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.801] [DEBUG] debug-file - entry : default@SignHap cost memory 0.11902618408203125
[2025-06-24T17:27:29.801] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 282 ms 
[2025-06-24T17:27:29.802] [INFO] debug-file - Finished :entry:default@SignHap... after 2 ms 
[2025-06-24T17:27:29.805] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.805] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.805] [DEBUG] debug-file - Executing task :entry:default@CollectDebugSymbol
[2025-06-24T17:27:29.810] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:29.810] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:29.811] [DEBUG] debug-file - entry : default@CollectDebugSymbol cost memory 0.23946380615234375
[2025-06-24T17:27:29.811] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 291 ms 
[2025-06-24T17:27:29.811] [INFO] debug-file - Finished :entry:default@CollectDebugSymbol... after 6 ms 
[2025-06-24T17:27:29.813] [DEBUG] debug-file - Executing task :entry:assembleHap
[2025-06-24T17:27:29.813] [DEBUG] debug-file - entry : assembleHap cost memory 0.0112762451171875
[2025-06-24T17:27:29.813] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 293 ms 
[2025-06-24T17:27:29.813] [INFO] debug-file - Finished :entry:assembleHap... after 1 ms 
[2025-06-24T17:27:29.822] [DEBUG] debug-file - BUILD SUCCESSFUL in 3 s 303 ms 
[2025-06-24T17:27:29.823] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-24T17:27:29.823] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.
[2025-06-24T17:27:29.823] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-24T17:27:29.823] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-24T17:27:29.823] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-24T17:27:29.823] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-24T17:27:29.824] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json cache.
[2025-06-24T17:27:29.824] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-06-24T17:27:29.825] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.
[2025-06-24T17:27:29.825] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\syscap_tool.exe cache by regenerate.
[2025-06-24T17:27:29.825] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\ets\api\device-define cache by regenerate.
[2025-06-24T17:27:29.826] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc cache.
[2025-06-24T17:27:29.826] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:2 ms .
[2025-06-24T17:27:29.826] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-24T17:27:29.826] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-24T17:27:29.827] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2025-06-24T17:27:29.827] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-24T17:27:29.827] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5 cache by regenerate.
[2025-06-24T17:27:29.828] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build-profile.json5 cache by regenerate.
[2025-06-24T17:27:29.828] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache.
[2025-06-24T17:27:29.828] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:2 ms .
[2025-06-24T17:27:29.828] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.
[2025-06-24T17:27:29.828] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2025-06-24T17:27:29.830] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:27:29.831] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:27:29.832] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:27:29.832] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:27:29.832] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:27:29.833] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets cache from map.
[2025-06-24T17:27:29.833] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:27:29.833] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-24T17:27:29.834] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache.
[2025-06-24T17:27:29.834] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:6 ms .
[2025-06-24T17:27:29.836] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:27:29.836] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:27:29.838] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:27:29.838] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:27:29.838] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:27:29.839] [DEBUG] debug-file - Update task entry:default@BuildJS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\js cache.
[2025-06-24T17:27:29.839] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:5 ms .
[2025-06-24T17:27:29.839] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2025-06-24T17:27:29.839] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.
[2025-06-24T17:27:29.840] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\stripped_native_libs\default cache by regenerate.
[2025-06-24T17:27:29.840] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\module.json cache by regenerate.
[2025-06-24T17:27:29.840] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources cache by regenerate.
[2025-06-24T17:27:29.849] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources.index cache by regenerate.
[2025-06-24T17:27:29.849] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\pack.info cache by regenerate.
[2025-06-24T17:27:29.850] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache from map.
[2025-06-24T17:27:29.850] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:27:29.850] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:27:29.851] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache.
[2025-06-24T17:27:29.852] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\source_map\default\sourceMaps.map cache.
[2025-06-24T17:27:29.853] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\mapping\sourceMaps.map cache.
[2025-06-24T17:27:29.853] [DEBUG] debug-file - Incremental task entry:default@PackageHap post-execution cost:15 ms .
[2025-06-24T17:27:29.853] [DEBUG] debug-file - Update task entry:default@SignHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache from map.
[2025-06-24T17:27:29.854] [DEBUG] debug-file - Update task entry:default@SignHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-signed.hap cache.
[2025-06-24T17:27:29.854] [DEBUG] debug-file - Incremental task entry:default@SignHap post-execution cost:1 ms .
[2025-06-24T17:27:29.856] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:27:29.857] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache by regenerate.
[2025-06-24T17:27:29.857] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\symbol cache.
[2025-06-24T17:27:29.857] [DEBUG] debug-file - Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-06-24T17:27:29.877] [DEBUG] debug-file - Cleanup worker 3.
[2025-06-24T17:27:29.878] [DEBUG] debug-file - Worker 3 has been cleaned up.
[2025-06-24T17:27:29.878] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-24T17:27:29.878] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-24T17:27:29.878] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-24T17:27:29.883] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:27:29.883] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:27:29.883] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:27:29.884] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:27:29.889] [DEBUG] debug-file - worker[3] exits with exit code 1.
[2025-06-24T17:27:58.779] [DEBUG] debug-file - session manager: set active socket. socketId=Cu-3_n32Y0vnSpdfAABJ
[2025-06-24T17:27:58.812] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:27:58.830] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-24T17:27:58.837] [DEBUG] debug-file - Cache service initialization finished in 7 ms 
[2025-06-24T17:27:58.860] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:27:58.870] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:27:58.870] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:27:58.880] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-24T17:27:58.880] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-24T17:27:58.880] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-24T17:27:58.880] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-24T17:27:58.882] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  }
} in this build.
[2025-06-24T17:27:58.889] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-24T17:27:58.929] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-24T17:27:58.973] [DEBUG] debug-file - Sdk init in 79 ms 
[2025-06-24T17:27:59.015] [DEBUG] debug-file - Project task initialization takes 41 ms 
[2025-06-24T17:27:59.017] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:27:59.017] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:27:59.017] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:27:59.050] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:27:59.071] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:27:59.072] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:27:59.096] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-24T17:27:59.097] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-24T17:27:59.098] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-24T17:27:59.098] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
}
[2025-06-24T17:27:59.098] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "default"
}
[2025-06-24T17:27:59.099] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-24T17:27:59.099] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
} in this build.
[2025-06-24T17:27:59.109] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-24T17:27:59.109] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:27:59.109] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:27:59.109] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:27:59.297] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 183 ms 
[2025-06-24T17:27:59.299] [DEBUG] debug-file - project has submodules:entry
[2025-06-24T17:27:59.306] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-24T17:27:59.326] [DEBUG] debug-file - load to the disk finished
[2025-06-24T17:27:59.348] [DEBUG] debug-file - Module SheShudu Collected Dependency: 
[2025-06-24T17:27:59.348] [DEBUG] debug-file - Module SheShudu's total dependency: 0
[2025-06-24T17:27:59.356] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-24T17:27:59.356] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-24T17:27:59.360] [DEBUG] debug-file - Configuration phase cost:517 ms 
[2025-06-24T17:27:59.362] [DEBUG] debug-file - Configuration task cost before running: 546 ms 
[2025-06-24T17:27:59.364] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.364] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.367] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-24T17:27:59.376] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 6 ms .
[2025-06-24T17:27:59.376] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.5305404663085938
[2025-06-24T17:27:59.378] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-24T17:27:59.380] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.380] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.380] [DEBUG] debug-file - Executing task :entry:default@GenerateMetadata
[2025-06-24T17:27:59.381] [DEBUG] debug-file - Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:59.383] [DEBUG] debug-file - Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .
[2025-06-24T17:27:59.384] [DEBUG] debug-file - entry : default@GenerateMetadata cost memory -3.8796005249023438
[2025-06-24T17:27:59.384] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateMetadata...  
[2025-06-24T17:27:59.386] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.386] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.387] [DEBUG] debug-file - Executing task :entry:default@ConfigureCmake
[2025-06-24T17:27:59.387] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.387] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.387] [DEBUG] debug-file - entry : default@ConfigureCmake cost memory 0.0360565185546875
[2025-06-24T17:27:59.387] [DEBUG] debug-file - runTaskFromQueue task cost before running: 571 ms 
[2025-06-24T17:27:59.387] [INFO] debug-file - Finished :entry:default@ConfigureCmake... after 1 ms 
[2025-06-24T17:27:59.389] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.389] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.389] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-24T17:27:59.391] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-24T17:27:59.391] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.10509490966796875
[2025-06-24T17:27:59.391] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-24T17:27:59.392] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.392] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.393] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-24T17:27:59.394] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:59.394] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-24T17:27:59.394] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.10121917724609375
[2025-06-24T17:27:59.395] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-24T17:27:59.396] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.396] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.397] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-24T17:27:59.397] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.397] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.397] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03624725341796875
[2025-06-24T17:27:59.397] [DEBUG] debug-file - runTaskFromQueue task cost before running: 581 ms 
[2025-06-24T17:27:59.397] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-24T17:27:59.399] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.399] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.403] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-24T17:27:59.403] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:59.403] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.038543701171875
[2025-06-24T17:27:59.403] [DEBUG] debug-file - runTaskFromQueue task cost before running: 588 ms 
[2025-06-24T17:27:59.404] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 1 ms 
[2025-06-24T17:27:59.407] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.407] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.409] [DEBUG] debug-file - Executing task :entry:default@ProcessIntegratedHsp
[2025-06-24T17:27:59.410] [DEBUG] debug-file - entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json' does not exist.
[2025-06-24T17:27:59.410] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .
[2025-06-24T17:27:59.410] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.410] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.411] [DEBUG] debug-file - entry : default@ProcessIntegratedHsp cost memory 0.1171417236328125
[2025-06-24T17:27:59.411] [DEBUG] debug-file - runTaskFromQueue task cost before running: 595 ms 
[2025-06-24T17:27:59.411] [INFO] debug-file - Finished :entry:default@ProcessIntegratedHsp... after 2 ms 
[2025-06-24T17:27:59.413] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.413] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.414] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithCmake
[2025-06-24T17:27:59.414] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.414] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.414] [DEBUG] debug-file - entry : default@BuildNativeWithCmake cost memory 0.037078857421875
[2025-06-24T17:27:59.414] [DEBUG] debug-file - runTaskFromQueue task cost before running: 598 ms 
[2025-06-24T17:27:59.414] [INFO] debug-file - Finished :entry:default@BuildNativeWithCmake... after 1 ms 
[2025-06-24T17:27:59.416] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.416] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.416] [DEBUG] debug-file - Executing task :entry:default@MakePackInfo
[2025-06-24T17:27:59.420] [DEBUG] debug-file - Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .
[2025-06-24T17:27:59.420] [DEBUG] debug-file - entry : default@MakePackInfo cost memory 0.138458251953125
[2025-06-24T17:27:59.421] [INFO] debug-file - UP-TO-DATE :entry:default@MakePackInfo...  
[2025-06-24T17:27:59.422] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.422] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.423] [DEBUG] debug-file - Executing task :entry:default@SyscapTransform
[2025-06-24T17:27:59.423] [DEBUG] debug-file - File: 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.
[2025-06-24T17:27:59.424] [DEBUG] debug-file - Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:59.426] [DEBUG] debug-file - entry:default@SyscapTransform is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc' does not exist.
[2025-06-24T17:27:59.426] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .
[2025-06-24T17:27:59.426] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.426] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.426] [DEBUG] debug-file - entry : default@SyscapTransform cost memory 0.15123748779296875
[2025-06-24T17:27:59.426] [DEBUG] debug-file - runTaskFromQueue task cost before running: 610 ms 
[2025-06-24T17:27:59.426] [INFO] debug-file - Finished :entry:default@SyscapTransform... after 3 ms 
[2025-06-24T17:27:59.428] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.428] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.429] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-24T17:27:59.429] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-24T17:27:59.430] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05930328369140625
[2025-06-24T17:27:59.430] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-24T17:27:59.431] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.431] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.433] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-24T17:27:59.440] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-24T17:27:59.440] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.20148468017578125
[2025-06-24T17:27:59.441] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-24T17:27:59.443] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.443] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.444] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithNinja
[2025-06-24T17:27:59.444] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.444] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.445] [DEBUG] debug-file - entry : default@BuildNativeWithNinja cost memory 0.05667877197265625
[2025-06-24T17:27:59.445] [DEBUG] debug-file - runTaskFromQueue task cost before running: 629 ms 
[2025-06-24T17:27:59.445] [INFO] debug-file - Finished :entry:default@BuildNativeWithNinja... after 1 ms 
[2025-06-24T17:27:59.447] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.447] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.448] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@ProcessResource
[2025-06-24T17:27:59.451] [DEBUG] debug-file - Executing task :entry:default@ProcessResource
[2025-06-24T17:27:59.454] [DEBUG] debug-file - Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .
[2025-06-24T17:27:59.454] [DEBUG] debug-file - entry : default@ProcessResource cost memory 0.17124176025390625
[2025-06-24T17:27:59.457] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessResource...  
[2025-06-24T17:27:59.459] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.459] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.464] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-24T17:27:59.475] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 7 ms .
[2025-06-24T17:27:59.476] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7693023681640625
[2025-06-24T17:27:59.480] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-24T17:27:59.481] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.482] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.482] [DEBUG] debug-file - Executing task :entry:default@ProcessLibs
[2025-06-24T17:27:59.484] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.484] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.485] [DEBUG] debug-file - entry : default@ProcessLibs cost memory 0.12564849853515625
[2025-06-24T17:27:59.485] [DEBUG] debug-file - runTaskFromQueue task cost before running: 670 ms 
[2025-06-24T17:27:59.486] [INFO] debug-file - Finished :entry:default@ProcessLibs... after 3 ms 
[2025-06-24T17:27:59.488] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.488] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.488] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@CompileResource
[2025-06-24T17:27:59.490] [DEBUG] debug-file - Executing task :entry:default@CompileResource
[2025-06-24T17:27:59.509] [DEBUG] debug-file - Incremental task entry:default@CompileResource pre-execution cost: 19 ms .
[2025-06-24T17:27:59.510] [DEBUG] debug-file - entry : default@CompileResource cost memory 1.4075851440429688
[2025-06-24T17:27:59.511] [INFO] debug-file - UP-TO-DATE :entry:default@CompileResource...  
[2025-06-24T17:27:59.513] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.513] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.515] [DEBUG] debug-file - Executing task :entry:default@DoNativeStrip
[2025-06-24T17:27:59.515] [DEBUG] debug-file - Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:59.516] [DEBUG] debug-file - Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-06-24T17:27:59.516] [DEBUG] debug-file - entry : default@DoNativeStrip cost memory 0.0745697021484375
[2025-06-24T17:27:59.516] [INFO] debug-file - UP-TO-DATE :entry:default@DoNativeStrip...  
[2025-06-24T17:27:59.518] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.518] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.521] [DEBUG] debug-file - Executing task :entry:default@CompileArkTS
[2025-06-24T17:27:59.531] [DEBUG] debug-file - entry:default@CompileArkTS is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets' has been changed.
[2025-06-24T17:27:59.532] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .
[2025-06-24T17:27:59.543] [DEBUG] debug-file - build config:
[2025-06-24T17:27:59.543] [DEBUG] debug-file - {
  moduleType: 'entry',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile',
  etsLoaderPath: 'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader',
  modulePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
  compileSdkVersion: 14,
  compatibleSdkVersion: 12,
  compatibleSdkVersionStage: 'beta1',
  bundleName: 'c***5',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\modules.ap',
  entryModuleName: 'entry',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'entry',
  allModuleNameHash: '5f173d74b06d1b11d4ab34f4a3957465',
  externalApiPaths: [
    'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default',
  bundleType: 'atomicService',
  arkTSVersion: undefined,
  apiVersion: 14,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: { caseSensitiveCheck: true, useNormalizedOHMUrl: false },
  buildDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build',
  deviceTypes: [ 'phone', 'tablet' ],
  useNormalizedOHMUrl: false,
  pkgContextInfo: undefined,
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: undefined, definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  aceModuleJsonPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json',
  appResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt',
  rawFileResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile',
  resourceTableHash: 'b4dba2e09ce066d2be03c76cd5133ec1',
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:14:*********:Release',
  aceModuleRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\supervisual',
  aceBuildJson: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json',
  cachePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug',
  aceModuleBuild: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets',
  supportChunks: true,
  declaredFilesPath: undefined,
  pkgNameToPkgBriefInfo: {
    entry: {
      pkgRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'entry'
    }
  },
  projectModel: {
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\src\\ohosTest\\ets': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\ohosTest': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu': {
      moduleName: 'SheShudu',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    }
  },
  pkgJsonFileHash: 'd01805ed5ebaa3fd7df4966d8c5aaf3c',
  allModulePaths: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5',
    mockConfigKey2ModuleInfo: {}
  },
  otherPaths: {
    'entry/*': [
      '..\\*',
      '..\\..\\..\\build\\default\\generated\\profile\\default\\*'
    ]
  },
  collectImportersConfig: undefined
}
[2025-06-24T17:27:59.544] [DEBUG] debug-file - Compile arkts with external api path: C:\Program Files\Huawei\DevEco Studio\sdk\default\hms\ets
[2025-06-24T17:27:59.545] [DEBUG] debug-file - default@CompileArkTS work[80] is submitted.
[2025-06-24T17:27:59.546] [DEBUG] debug-file - default@CompileArkTS work[80] is pushed to ready queue.
[2025-06-24T17:27:59.546] [DEBUG] debug-file - default@CompileArkTS work[80] is not dispatched.
[2025-06-24T17:27:59.546] [DEBUG] debug-file - CopyResources startTime: 32311441939700
[2025-06-24T17:27:59.546] [DEBUG] debug-file - default@CompileArkTS work[81] is submitted.
[2025-06-24T17:27:59.547] [DEBUG] debug-file - default@CompileArkTS work[81] is pushed to ready queue.
[2025-06-24T17:27:59.547] [DEBUG] debug-file - default@CompileArkTS work[81] is not dispatched.
[2025-06-24T17:27:59.547] [DEBUG] debug-file - entry : default@CompileArkTS cost memory 1.5870361328125
[2025-06-24T17:27:59.548] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.548] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.552] [DEBUG] debug-file - Executing task :entry:default@BuildJS
[2025-06-24T17:27:59.555] [DEBUG] debug-file - entry : default@BuildJS cost memory 0.12688446044921875
[2025-06-24T17:27:59.555] [DEBUG] debug-file - runTaskFromQueue task cost before running: 739 ms 
[2025-06-24T17:27:59.555] [INFO] debug-file - Finished :entry:default@BuildJS... after 3 ms 
[2025-06-24T17:27:59.557] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:27:59.557] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:27:59.559] [DEBUG] debug-file - Executing task :entry:default@CacheNativeLibs
[2025-06-24T17:27:59.559] [DEBUG] debug-file - Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:27:59.560] [DEBUG] debug-file - Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .
[2025-06-24T17:27:59.561] [DEBUG] debug-file - entry : default@CacheNativeLibs cost memory 0.08788299560546875
[2025-06-24T17:27:59.561] [INFO] debug-file - UP-TO-DATE :entry:default@CacheNativeLibs...  
[2025-06-24T17:27:59.650] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:27:59.650] [DEBUG] debug-file - default@CompileArkTS work[80] has been dispatched to worker[4].
[2025-06-24T17:27:59.650] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:27:59.650] [DEBUG] debug-file - A work dispatched to worker[4] failed because of worker busy.
[2025-06-24T17:27:59.650] [DEBUG] debug-file - Create  resident worker with id: 3.
[2025-06-24T17:27:59.652] [DEBUG] debug-file - default@CompileArkTS work[81] has been dispatched to worker[3].
[2025-06-24T17:28:00.701] [DEBUG] debug-file - worker[3] has one work done.
[2025-06-24T17:28:00.701] [DEBUG] debug-file - CopyResources is end, endTime: 32312596959900
[2025-06-24T17:28:00.701] [DEBUG] debug-file - default@CompileArkTS work[81] done.
[2025-06-24T17:28:00.812] [DEBUG] debug-file - A work dispatched to worker[3] failed because unable to get work from ready queue.
[2025-06-24T17:28:01.145] [DEBUG] debug-file - Ark compile task finished.finished time is 32313040669300
[2025-06-24T17:28:01.145] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:28:01.150] [DEBUG] debug-file - default@CompileArkTS work[80] done.
[2025-06-24T17:28:01.151] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-24T17:28:01.153] [INFO] debug-file - Finished :entry:default@CompileArkTS... after 1 s 520 ms 
[2025-06-24T17:28:01.155] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.155] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.155] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgModuleJson
[2025-06-24T17:28:01.156] [DEBUG] debug-file - Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:28:01.156] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .
[2025-06-24T17:28:01.157] [DEBUG] debug-file - entry : default@GeneratePkgModuleJson cost memory 0.07546234130859375
[2025-06-24T17:28:01.157] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgModuleJson...  
[2025-06-24T17:28:01.159] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.159] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.165] [DEBUG] debug-file - Executing task :entry:default@PackageHap
[2025-06-24T17:28:01.175] [DEBUG] debug-file - entry:default@PackageHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets' has been changed.
[2025-06-24T17:28:01.175] [DEBUG] debug-file - Incremental task entry:default@PackageHap pre-execution cost: 10 ms .
[2025-06-24T17:28:01.175] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.175] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.177] [DEBUG] debug-file - Use tool [C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\lib\app_packing_tool.jar]
 [
  'java',
  '-Dfile.encoding=GBK',
  '-jar',
  'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar',
  '--mode',
  'hap',
  '--force',
  'true',
  '--lib-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default',
  '--json-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json',
  '--resources-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources',
  '--index-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index',
  '--pack-info-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info',
  '--out-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap',
  '--ets-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets'
]
[2025-06-24T17:28:01.178] [DEBUG] debug-file - default@PackageHap work[82] is submitted.
[2025-06-24T17:28:01.179] [DEBUG] debug-file - default@PackageHap work[82] is pushed to ready queue.
[2025-06-24T17:28:01.179] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:28:01.179] [DEBUG] debug-file - default@PackageHap work[82] has been dispatched to worker[4].
[2025-06-24T17:28:01.179] [DEBUG] debug-file - default@PackageHap work[82] is dispatched.
[2025-06-24T17:28:01.182] [DEBUG] debug-file - entry : default@PackageHap cost memory 1.3076324462890625
[2025-06-24T17:28:01.181] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 771231744,
  heapTotal: 483971072,
  heapUsed: 452981832,
  external: 4849661,
  arrayBuffers: 882180
} os memoryUsage :25.14781951904297
[2025-06-24T17:28:01.600] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:28:01.601] [DEBUG] debug-file - default@PackageHap work[82] done.
[2025-06-24T17:28:01.601] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-24T17:28:01.603] [INFO] debug-file - Finished :entry:default@PackageHap... after 436 ms 
[2025-06-24T17:28:01.605] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.605] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.607] [DEBUG] debug-file - Executing task :entry:default@SignHap
[2025-06-24T17:28:01.608] [WARN] debug-file - Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5.
[2025-06-24T17:28:01.608] [DEBUG] debug-file - entry:default@SignHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap' has been changed.
[2025-06-24T17:28:01.609] [DEBUG] debug-file - Incremental task entry:default@SignHap pre-execution cost: 1 ms .
[2025-06-24T17:28:01.609] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.609] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.609] [DEBUG] debug-file - entry : default@SignHap cost memory 0.1169281005859375
[2025-06-24T17:28:01.609] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 794 ms 
[2025-06-24T17:28:01.610] [INFO] debug-file - Finished :entry:default@SignHap... after 2 ms 
[2025-06-24T17:28:01.612] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.612] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.612] [DEBUG] debug-file - Executing task :entry:default@CollectDebugSymbol
[2025-06-24T17:28:01.617] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:01.617] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:01.617] [DEBUG] debug-file - entry : default@CollectDebugSymbol cost memory 0.2393646240234375
[2025-06-24T17:28:01.617] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 802 ms 
[2025-06-24T17:28:01.618] [INFO] debug-file - Finished :entry:default@CollectDebugSymbol... after 6 ms 
[2025-06-24T17:28:01.620] [DEBUG] debug-file - Executing task :entry:assembleHap
[2025-06-24T17:28:01.620] [DEBUG] debug-file - entry : assembleHap cost memory 0.0113067626953125
[2025-06-24T17:28:01.620] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 804 ms 
[2025-06-24T17:28:01.620] [INFO] debug-file - Finished :entry:assembleHap... after 1 ms 
[2025-06-24T17:28:01.628] [DEBUG] debug-file - BUILD SUCCESSFUL in 2 s 812 ms 
[2025-06-24T17:28:01.628] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-24T17:28:01.628] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.
[2025-06-24T17:28:01.628] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-24T17:28:01.628] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-24T17:28:01.628] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-24T17:28:01.629] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-24T17:28:01.629] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json cache.
[2025-06-24T17:28:01.630] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-06-24T17:28:01.630] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.
[2025-06-24T17:28:01.630] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\syscap_tool.exe cache by regenerate.
[2025-06-24T17:28:01.630] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\ets\api\device-define cache by regenerate.
[2025-06-24T17:28:01.631] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc cache.
[2025-06-24T17:28:01.631] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:2 ms .
[2025-06-24T17:28:01.631] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-24T17:28:01.631] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-24T17:28:01.632] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2025-06-24T17:28:01.632] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-24T17:28:01.632] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5 cache by regenerate.
[2025-06-24T17:28:01.632] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build-profile.json5 cache by regenerate.
[2025-06-24T17:28:01.632] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache.
[2025-06-24T17:28:01.633] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:1 ms .
[2025-06-24T17:28:01.633] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.
[2025-06-24T17:28:01.633] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2025-06-24T17:28:01.637] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:28:01.637] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:28:01.638] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:28:01.638] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:28:01.639] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:28:01.639] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets cache from map.
[2025-06-24T17:28:01.639] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:28:01.639] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-24T17:28:01.640] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache.
[2025-06-24T17:28:01.640] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:8 ms .
[2025-06-24T17:28:01.641] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:28:01.642] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:28:01.643] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:28:01.643] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:28:01.643] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:28:01.644] [DEBUG] debug-file - Update task entry:default@BuildJS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\js cache.
[2025-06-24T17:28:01.644] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:4 ms .
[2025-06-24T17:28:01.644] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2025-06-24T17:28:01.644] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.
[2025-06-24T17:28:01.645] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\stripped_native_libs\default cache by regenerate.
[2025-06-24T17:28:01.645] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\module.json cache by regenerate.
[2025-06-24T17:28:01.645] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources cache by regenerate.
[2025-06-24T17:28:01.654] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources.index cache by regenerate.
[2025-06-24T17:28:01.654] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\pack.info cache by regenerate.
[2025-06-24T17:28:01.654] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache from map.
[2025-06-24T17:28:01.654] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:28:01.655] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:28:01.655] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache.
[2025-06-24T17:28:01.655] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\source_map\default\sourceMaps.map cache.
[2025-06-24T17:28:01.656] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\mapping\sourceMaps.map cache.
[2025-06-24T17:28:01.656] [DEBUG] debug-file - Incremental task entry:default@PackageHap post-execution cost:12 ms .
[2025-06-24T17:28:01.656] [DEBUG] debug-file - Update task entry:default@SignHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache from map.
[2025-06-24T17:28:01.656] [DEBUG] debug-file - Update task entry:default@SignHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-signed.hap cache.
[2025-06-24T17:28:01.656] [DEBUG] debug-file - Incremental task entry:default@SignHap post-execution cost:1 ms .
[2025-06-24T17:28:01.658] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:28:01.659] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache by regenerate.
[2025-06-24T17:28:01.659] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\symbol cache.
[2025-06-24T17:28:01.659] [DEBUG] debug-file - Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-06-24T17:28:01.677] [DEBUG] debug-file - Cleanup worker 3.
[2025-06-24T17:28:01.677] [DEBUG] debug-file - Worker 3 has been cleaned up.
[2025-06-24T17:28:01.677] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-24T17:28:01.677] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-24T17:28:01.678] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-24T17:28:01.697] [DEBUG] debug-file - worker[3] exits with exit code 1.
[2025-06-24T17:28:01.709] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:28:01.709] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:28:01.710] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:28:01.710] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:28:21.642] [DEBUG] debug-file - session manager: set active socket. socketId=kA8Nt2xpXI0jxTHtAABL
[2025-06-24T17:28:21.767] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:28:21.782] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-24T17:28:21.787] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2025-06-24T17:28:21.801] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:28:21.807] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:28:21.808] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:28:21.816] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-24T17:28:21.816] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-24T17:28:21.816] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-24T17:28:21.816] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-24T17:28:21.819] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  }
} in this build.
[2025-06-24T17:28:21.822] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-24T17:28:21.836] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-24T17:28:21.946] [DEBUG] debug-file - Sdk init in 123 ms 
[2025-06-24T17:28:22.014] [DEBUG] debug-file - Project task initialization takes 55 ms 
[2025-06-24T17:28:22.014] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:28:22.014] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:28:22.015] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:28:22.029] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:28:22.034] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:28:22.035] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:28:22.055] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-24T17:28:22.055] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-24T17:28:22.056] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-24T17:28:22.059] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
}
[2025-06-24T17:28:22.059] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "default"
}
[2025-06-24T17:28:22.059] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-24T17:28:22.074] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
} in this build.
[2025-06-24T17:28:22.089] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2025-06-24T17:28:22.089] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:28:22.089] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:28:22.090] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:28:22.448] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 354 ms 
[2025-06-24T17:28:22.449] [DEBUG] debug-file - project has submodules:entry
[2025-06-24T17:28:22.453] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-24T17:28:22.460] [DEBUG] debug-file - load to the disk finished
[2025-06-24T17:28:22.473] [DEBUG] debug-file - Module SheShudu Collected Dependency: 
[2025-06-24T17:28:22.473] [DEBUG] debug-file - Module SheShudu's total dependency: 0
[2025-06-24T17:28:22.487] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-24T17:28:22.488] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-24T17:28:22.493] [DEBUG] debug-file - Configuration phase cost:700 ms 
[2025-06-24T17:28:22.495] [DEBUG] debug-file - Configuration task cost before running: 726 ms 
[2025-06-24T17:28:22.499] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.499] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.505] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-24T17:28:22.514] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 6 ms .
[2025-06-24T17:28:22.514] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.31058502197265625
[2025-06-24T17:28:22.516] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-24T17:28:22.519] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.521] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.523] [DEBUG] debug-file - Executing task :entry:default@GenerateMetadata
[2025-06-24T17:28:22.524] [DEBUG] debug-file - Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms 
[2025-06-24T17:28:22.526] [DEBUG] debug-file - Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .
[2025-06-24T17:28:22.526] [DEBUG] debug-file - entry : default@GenerateMetadata cost memory 0.09297943115234375
[2025-06-24T17:28:22.527] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateMetadata...  
[2025-06-24T17:28:22.529] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.529] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.529] [DEBUG] debug-file - Executing task :entry:default@ConfigureCmake
[2025-06-24T17:28:22.529] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.529] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.529] [DEBUG] debug-file - entry : default@ConfigureCmake cost memory 0.0360260009765625
[2025-06-24T17:28:22.530] [DEBUG] debug-file - runTaskFromQueue task cost before running: 760 ms 
[2025-06-24T17:28:22.530] [INFO] debug-file - Finished :entry:default@ConfigureCmake... after 1 ms 
[2025-06-24T17:28:22.532] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.533] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.534] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-24T17:28:22.536] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-24T17:28:22.536] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.10483551025390625
[2025-06-24T17:28:22.536] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-24T17:28:22.538] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.539] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.539] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-24T17:28:22.540] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:28:22.541] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-24T17:28:22.541] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.10101318359375
[2025-06-24T17:28:22.541] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-24T17:28:22.544] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.545] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.545] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-24T17:28:22.545] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.545] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.546] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03624725341796875
[2025-06-24T17:28:22.546] [DEBUG] debug-file - runTaskFromQueue task cost before running: 776 ms 
[2025-06-24T17:28:22.546] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-24T17:28:22.548] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.548] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.553] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-24T17:28:22.553] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:28:22.553] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.14545440673828125
[2025-06-24T17:28:22.554] [DEBUG] debug-file - runTaskFromQueue task cost before running: 784 ms 
[2025-06-24T17:28:22.554] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 1 ms 
[2025-06-24T17:28:22.556] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.557] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.562] [DEBUG] debug-file - Executing task :entry:default@ProcessIntegratedHsp
[2025-06-24T17:28:22.564] [DEBUG] debug-file - entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json' does not exist.
[2025-06-24T17:28:22.564] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .
[2025-06-24T17:28:22.569] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.569] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.569] [DEBUG] debug-file - entry : default@ProcessIntegratedHsp cost memory -4.7309112548828125
[2025-06-24T17:28:22.569] [DEBUG] debug-file - runTaskFromQueue task cost before running: 800 ms 
[2025-06-24T17:28:22.571] [INFO] debug-file - Finished :entry:default@ProcessIntegratedHsp... after 8 ms 
[2025-06-24T17:28:22.573] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.574] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.574] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithCmake
[2025-06-24T17:28:22.575] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.575] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.575] [DEBUG] debug-file - entry : default@BuildNativeWithCmake cost memory 0.037078857421875
[2025-06-24T17:28:22.575] [DEBUG] debug-file - runTaskFromQueue task cost before running: 806 ms 
[2025-06-24T17:28:22.575] [INFO] debug-file - Finished :entry:default@BuildNativeWithCmake... after 1 ms 
[2025-06-24T17:28:22.577] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.577] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.578] [DEBUG] debug-file - Executing task :entry:default@MakePackInfo
[2025-06-24T17:28:22.582] [DEBUG] debug-file - Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .
[2025-06-24T17:28:22.583] [DEBUG] debug-file - entry : default@MakePackInfo cost memory 0.1545562744140625
[2025-06-24T17:28:22.585] [INFO] debug-file - UP-TO-DATE :entry:default@MakePackInfo...  
[2025-06-24T17:28:22.590] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.590] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.592] [DEBUG] debug-file - Executing task :entry:default@SyscapTransform
[2025-06-24T17:28:22.592] [DEBUG] debug-file - File: 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.
[2025-06-24T17:28:22.594] [DEBUG] debug-file - Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms 
[2025-06-24T17:28:22.595] [DEBUG] debug-file - entry:default@SyscapTransform is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc' does not exist.
[2025-06-24T17:28:22.596] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .
[2025-06-24T17:28:22.596] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.596] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.596] [DEBUG] debug-file - entry : default@SyscapTransform cost memory 0.15171051025390625
[2025-06-24T17:28:22.596] [DEBUG] debug-file - runTaskFromQueue task cost before running: 827 ms 
[2025-06-24T17:28:22.596] [INFO] debug-file - Finished :entry:default@SyscapTransform... after 4 ms 
[2025-06-24T17:28:22.599] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.599] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.600] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-24T17:28:22.603] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .
[2025-06-24T17:28:22.603] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.06029510498046875
[2025-06-24T17:28:22.603] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-24T17:28:22.607] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.607] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.611] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-24T17:28:22.617] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .
[2025-06-24T17:28:22.618] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.20147705078125
[2025-06-24T17:28:22.622] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-24T17:28:22.659] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.659] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.661] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithNinja
[2025-06-24T17:28:22.661] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.661] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.664] [DEBUG] debug-file - entry : default@BuildNativeWithNinja cost memory 0.05667877197265625
[2025-06-24T17:28:22.664] [DEBUG] debug-file - runTaskFromQueue task cost before running: 895 ms 
[2025-06-24T17:28:22.665] [INFO] debug-file - Finished :entry:default@BuildNativeWithNinja... after 4 ms 
[2025-06-24T17:28:22.672] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.672] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.677] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@ProcessResource
[2025-06-24T17:28:22.681] [DEBUG] debug-file - Executing task :entry:default@ProcessResource
[2025-06-24T17:28:22.685] [DEBUG] debug-file - Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .
[2025-06-24T17:28:22.685] [DEBUG] debug-file - entry : default@ProcessResource cost memory 0.16942596435546875
[2025-06-24T17:28:22.692] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessResource...  
[2025-06-24T17:28:22.699] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.699] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.704] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-24T17:28:22.740] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 26 ms .
[2025-06-24T17:28:22.741] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7676467895507812
[2025-06-24T17:28:22.748] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-24T17:28:22.752] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.752] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.753] [DEBUG] debug-file - Executing task :entry:default@ProcessLibs
[2025-06-24T17:28:22.756] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.756] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.757] [DEBUG] debug-file - entry : default@ProcessLibs cost memory 0.12572479248046875
[2025-06-24T17:28:22.757] [DEBUG] debug-file - runTaskFromQueue task cost before running: 988 ms 
[2025-06-24T17:28:22.758] [INFO] debug-file - Finished :entry:default@ProcessLibs... after 4 ms 
[2025-06-24T17:28:22.763] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.763] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.764] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@CompileResource
[2025-06-24T17:28:22.768] [DEBUG] debug-file - Executing task :entry:default@CompileResource
[2025-06-24T17:28:22.794] [DEBUG] debug-file - Incremental task entry:default@CompileResource pre-execution cost: 26 ms .
[2025-06-24T17:28:22.795] [DEBUG] debug-file - entry : default@CompileResource cost memory -4.463600158691406
[2025-06-24T17:28:22.796] [INFO] debug-file - UP-TO-DATE :entry:default@CompileResource...  
[2025-06-24T17:28:22.797] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.797] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.799] [DEBUG] debug-file - Executing task :entry:default@DoNativeStrip
[2025-06-24T17:28:22.800] [DEBUG] debug-file - Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:28:22.801] [DEBUG] debug-file - Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .
[2025-06-24T17:28:22.801] [DEBUG] debug-file - entry : default@DoNativeStrip cost memory 0.075164794921875
[2025-06-24T17:28:22.801] [INFO] debug-file - UP-TO-DATE :entry:default@DoNativeStrip...  
[2025-06-24T17:28:22.803] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.803] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.807] [DEBUG] debug-file - Executing task :entry:default@CompileArkTS
[2025-06-24T17:28:22.821] [DEBUG] debug-file - entry:default@CompileArkTS is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets' has been changed.
[2025-06-24T17:28:22.821] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .
[2025-06-24T17:28:22.832] [DEBUG] debug-file - build config:
[2025-06-24T17:28:22.833] [DEBUG] debug-file - {
  moduleType: 'entry',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile',
  etsLoaderPath: 'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader',
  modulePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
  compileSdkVersion: 14,
  compatibleSdkVersion: 12,
  compatibleSdkVersionStage: 'beta1',
  bundleName: 'c***5',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\modules.ap',
  entryModuleName: 'entry',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'entry',
  allModuleNameHash: '5f173d74b06d1b11d4ab34f4a3957465',
  externalApiPaths: [
    'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default',
  bundleType: 'atomicService',
  arkTSVersion: undefined,
  apiVersion: 14,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: { caseSensitiveCheck: true, useNormalizedOHMUrl: false },
  buildDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build',
  deviceTypes: [ 'phone', 'tablet' ],
  useNormalizedOHMUrl: false,
  pkgContextInfo: undefined,
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: undefined, definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  aceModuleJsonPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json',
  appResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt',
  rawFileResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile',
  resourceTableHash: 'b4dba2e09ce066d2be03c76cd5133ec1',
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:14:*********:Release',
  aceModuleRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\supervisual',
  aceBuildJson: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json',
  cachePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug',
  aceModuleBuild: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets',
  supportChunks: true,
  declaredFilesPath: undefined,
  pkgNameToPkgBriefInfo: {
    entry: {
      pkgRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'entry'
    }
  },
  projectModel: {
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\src\\ohosTest\\ets': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\ohosTest': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu': {
      moduleName: 'SheShudu',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    }
  },
  pkgJsonFileHash: 'd01805ed5ebaa3fd7df4966d8c5aaf3c',
  allModulePaths: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5',
    mockConfigKey2ModuleInfo: {}
  },
  otherPaths: {
    'entry/*': [
      '..\\*',
      '..\\..\\..\\build\\default\\generated\\profile\\default\\*'
    ]
  },
  collectImportersConfig: undefined
}
[2025-06-24T17:28:22.833] [DEBUG] debug-file - Compile arkts with external api path: C:\Program Files\Huawei\DevEco Studio\sdk\default\hms\ets
[2025-06-24T17:28:22.834] [DEBUG] debug-file - default@CompileArkTS work[83] is submitted.
[2025-06-24T17:28:22.836] [DEBUG] debug-file - default@CompileArkTS work[83] is pushed to ready queue.
[2025-06-24T17:28:22.836] [DEBUG] debug-file - default@CompileArkTS work[83] is not dispatched.
[2025-06-24T17:28:22.836] [DEBUG] debug-file - CopyResources startTime: 32334732448700
[2025-06-24T17:28:22.836] [DEBUG] debug-file - default@CompileArkTS work[84] is submitted.
[2025-06-24T17:28:22.838] [DEBUG] debug-file - default@CompileArkTS work[84] is pushed to ready queue.
[2025-06-24T17:28:22.838] [DEBUG] debug-file - default@CompileArkTS work[84] is not dispatched.
[2025-06-24T17:28:22.838] [DEBUG] debug-file - entry : default@CompileArkTS cost memory 1.5879898071289062
[2025-06-24T17:28:22.839] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.839] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.842] [DEBUG] debug-file - Executing task :entry:default@BuildJS
[2025-06-24T17:28:22.846] [DEBUG] debug-file - entry : default@BuildJS cost memory 0.1268463134765625
[2025-06-24T17:28:22.846] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 77 ms 
[2025-06-24T17:28:22.847] [INFO] debug-file - Finished :entry:default@BuildJS... after 4 ms 
[2025-06-24T17:28:22.849] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:22.849] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:22.854] [DEBUG] debug-file - Executing task :entry:default@CacheNativeLibs
[2025-06-24T17:28:22.855] [DEBUG] debug-file - Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:28:22.856] [DEBUG] debug-file - Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .
[2025-06-24T17:28:22.857] [DEBUG] debug-file - entry : default@CacheNativeLibs cost memory 0.0886077880859375
[2025-06-24T17:28:22.858] [INFO] debug-file - UP-TO-DATE :entry:default@CacheNativeLibs...  
[2025-06-24T17:28:23.039] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:28:23.039] [DEBUG] debug-file - default@CompileArkTS work[83] has been dispatched to worker[4].
[2025-06-24T17:28:23.147] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:28:23.147] [DEBUG] debug-file - A work dispatched to worker[4] failed because of worker busy.
[2025-06-24T17:28:23.147] [DEBUG] debug-file - Create  resident worker with id: 3.
[2025-06-24T17:28:23.148] [DEBUG] debug-file - default@CompileArkTS work[84] has been dispatched to worker[3].
[2025-06-24T17:28:24.432] [DEBUG] debug-file - worker[3] has one work done.
[2025-06-24T17:28:24.433] [DEBUG] debug-file - CopyResources is end, endTime: 32336328886700
[2025-06-24T17:28:24.435] [DEBUG] debug-file - default@CompileArkTS work[84] done.
[2025-06-24T17:28:24.690] [DEBUG] debug-file - Ark compile task finished.finished time is 32336586248400
[2025-06-24T17:28:24.691] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:28:24.699] [DEBUG] debug-file - default@CompileArkTS work[83] done.
[2025-06-24T17:28:24.699] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-24T17:28:24.705] [INFO] debug-file - Finished :entry:default@CompileArkTS... after 1 s 682 ms 
[2025-06-24T17:28:24.708] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:24.708] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:24.709] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgModuleJson
[2025-06-24T17:28:24.710] [DEBUG] debug-file - Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:28:24.711] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .
[2025-06-24T17:28:24.711] [DEBUG] debug-file - entry : default@GeneratePkgModuleJson cost memory 0.**********
[2025-06-24T17:28:24.711] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgModuleJson...  
[2025-06-24T17:28:24.715] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:24.715] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:24.728] [DEBUG] debug-file - Executing task :entry:default@PackageHap
[2025-06-24T17:28:24.748] [DEBUG] debug-file - entry:default@PackageHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets' has been changed.
[2025-06-24T17:28:24.749] [DEBUG] debug-file - Incremental task entry:default@PackageHap pre-execution cost: 18 ms .
[2025-06-24T17:28:24.749] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:24.749] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:24.754] [DEBUG] debug-file - Use tool [C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\lib\app_packing_tool.jar]
 [
  'java',
  '-Dfile.encoding=GBK',
  '-jar',
  'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar',
  '--mode',
  'hap',
  '--force',
  'true',
  '--lib-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default',
  '--json-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json',
  '--resources-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources',
  '--index-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index',
  '--pack-info-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info',
  '--out-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap',
  '--ets-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets'
]
[2025-06-24T17:28:24.758] [DEBUG] debug-file - default@PackageHap work[85] is submitted.
[2025-06-24T17:28:24.760] [DEBUG] debug-file - default@PackageHap work[85] is pushed to ready queue.
[2025-06-24T17:28:24.761] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:28:24.761] [DEBUG] debug-file - default@PackageHap work[85] has been dispatched to worker[4].
[2025-06-24T17:28:24.761] [DEBUG] debug-file - default@PackageHap work[85] is dispatched.
[2025-06-24T17:28:24.766] [DEBUG] debug-file - entry : default@PackageHap cost memory -4.990966796875
[2025-06-24T17:28:24.767] [DEBUG] debug-file - A work dispatched to worker[3] failed because unable to get work from ready queue.
[2025-06-24T17:28:24.764] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 807792640,
  heapTotal: 507162624,
  heapUsed: 448586216,
  external: 4921538,
  arrayBuffers: 954057
} os memoryUsage :25.259326934814453
[2025-06-24T17:28:25.267] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:28:25.267] [DEBUG] debug-file - default@PackageHap work[85] done.
[2025-06-24T17:28:25.270] [INFO] debug-file - Finished :entry:default@PackageHap... after 540 ms 
[2025-06-24T17:28:25.273] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:25.273] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:25.276] [DEBUG] debug-file - Executing task :entry:default@SignHap
[2025-06-24T17:28:25.277] [WARN] debug-file - Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5.
[2025-06-24T17:28:25.278] [DEBUG] debug-file - entry:default@SignHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap' has been changed.
[2025-06-24T17:28:25.278] [DEBUG] debug-file - Incremental task entry:default@SignHap pre-execution cost: 1 ms .
[2025-06-24T17:28:25.278] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:25.278] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:25.279] [DEBUG] debug-file - entry : default@SignHap cost memory 0.1148223876953125
[2025-06-24T17:28:25.279] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 509 ms 
[2025-06-24T17:28:25.279] [INFO] debug-file - Finished :entry:default@SignHap... after 3 ms 
[2025-06-24T17:28:25.281] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:25.281] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:25.282] [DEBUG] debug-file - Executing task :entry:default@CollectDebugSymbol
[2025-06-24T17:28:25.291] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:28:25.291] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:28:25.292] [DEBUG] debug-file - entry : default@CollectDebugSymbol cost memory 0.27518463134765625
[2025-06-24T17:28:25.292] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 523 ms 
[2025-06-24T17:28:25.292] [INFO] debug-file - Finished :entry:default@CollectDebugSymbol... after 10 ms 
[2025-06-24T17:28:25.295] [DEBUG] debug-file - Executing task :entry:assembleHap
[2025-06-24T17:28:25.295] [DEBUG] debug-file - entry : assembleHap cost memory 0.0113067626953125
[2025-06-24T17:28:25.296] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 526 ms 
[2025-06-24T17:28:25.296] [INFO] debug-file - Finished :entry:assembleHap... after 1 ms 
[2025-06-24T17:28:25.305] [DEBUG] debug-file - BUILD SUCCESSFUL in 3 s 535 ms 
[2025-06-24T17:28:25.306] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-24T17:28:25.306] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.
[2025-06-24T17:28:25.306] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-24T17:28:25.306] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-24T17:28:25.306] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-24T17:28:25.306] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-24T17:28:25.307] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json cache.
[2025-06-24T17:28:25.308] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-06-24T17:28:25.308] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.
[2025-06-24T17:28:25.308] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\syscap_tool.exe cache by regenerate.
[2025-06-24T17:28:25.308] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\ets\api\device-define cache by regenerate.
[2025-06-24T17:28:25.309] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc cache.
[2025-06-24T17:28:25.309] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:2 ms .
[2025-06-24T17:28:25.309] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-24T17:28:25.310] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-24T17:28:25.310] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2025-06-24T17:28:25.310] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-24T17:28:25.311] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5 cache by regenerate.
[2025-06-24T17:28:25.311] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build-profile.json5 cache by regenerate.
[2025-06-24T17:28:25.311] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache.
[2025-06-24T17:28:25.311] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:2 ms .
[2025-06-24T17:28:25.311] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.
[2025-06-24T17:28:25.311] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2025-06-24T17:28:25.313] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:28:25.314] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:28:25.315] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:28:25.315] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:28:25.315] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:28:25.316] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets cache from map.
[2025-06-24T17:28:25.316] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:28:25.318] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-24T17:28:25.319] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache.
[2025-06-24T17:28:25.319] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:8 ms .
[2025-06-24T17:28:25.323] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:28:25.324] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:28:25.325] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:28:25.325] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:28:25.325] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:28:25.326] [DEBUG] debug-file - Update task entry:default@BuildJS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\js cache.
[2025-06-24T17:28:25.326] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:8 ms .
[2025-06-24T17:28:25.327] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2025-06-24T17:28:25.327] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.
[2025-06-24T17:28:25.328] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\stripped_native_libs\default cache by regenerate.
[2025-06-24T17:28:25.329] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\module.json cache by regenerate.
[2025-06-24T17:28:25.329] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources cache by regenerate.
[2025-06-24T17:28:25.336] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources.index cache by regenerate.
[2025-06-24T17:28:25.336] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\pack.info cache by regenerate.
[2025-06-24T17:28:25.336] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache from map.
[2025-06-24T17:28:25.336] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:28:25.337] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:28:25.337] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache.
[2025-06-24T17:28:25.338] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\source_map\default\sourceMaps.map cache.
[2025-06-24T17:28:25.338] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\mapping\sourceMaps.map cache.
[2025-06-24T17:28:25.338] [DEBUG] debug-file - Incremental task entry:default@PackageHap post-execution cost:12 ms .
[2025-06-24T17:28:25.338] [DEBUG] debug-file - Update task entry:default@SignHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache from map.
[2025-06-24T17:28:25.338] [DEBUG] debug-file - Update task entry:default@SignHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-signed.hap cache.
[2025-06-24T17:28:25.339] [DEBUG] debug-file - Incremental task entry:default@SignHap post-execution cost:1 ms .
[2025-06-24T17:28:25.341] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:28:25.341] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache by regenerate.
[2025-06-24T17:28:25.341] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\symbol cache.
[2025-06-24T17:28:25.342] [DEBUG] debug-file - Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-06-24T17:28:25.365] [DEBUG] debug-file - Cleanup worker 3.
[2025-06-24T17:28:25.365] [DEBUG] debug-file - Worker 3 has been cleaned up.
[2025-06-24T17:28:25.365] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-24T17:28:25.365] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-24T17:28:25.366] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-24T17:28:25.371] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:28:25.372] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:28:25.372] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:28:25.373] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:28:25.378] [DEBUG] debug-file - worker[3] exits with exit code 1.
[2025-06-24T17:33:59.460] [DEBUG] debug-file - session manager: set active socket. socketId=_OrZthX67QCbkBQGAABN
[2025-06-24T17:33:59.465] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:33:59.481] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-24T17:33:59.486] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2025-06-24T17:33:59.499] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:33:59.504] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:33:59.504] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:33:59.510] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-24T17:33:59.510] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-24T17:33:59.510] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-24T17:33:59.510] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-24T17:33:59.512] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  }
} in this build.
[2025-06-24T17:33:59.516] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-24T17:33:59.525] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-24T17:33:59.578] [DEBUG] debug-file - Sdk init in 62 ms 
[2025-06-24T17:33:59.615] [DEBUG] debug-file - Project task initialization takes 36 ms 
[2025-06-24T17:33:59.615] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:33:59.615] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:33:59.615] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:33:59.621] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:33:59.625] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:33:59.626] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:33:59.632] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-24T17:33:59.632] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-24T17:33:59.632] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-24T17:33:59.633] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
}
[2025-06-24T17:33:59.633] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "default"
}
[2025-06-24T17:33:59.633] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-24T17:33:59.633] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
} in this build.
[2025-06-24T17:33:59.636] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-24T17:33:59.636] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:33:59.636] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:33:59.637] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:33:59.746] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 108 ms 
[2025-06-24T17:33:59.747] [DEBUG] debug-file - project has submodules:entry
[2025-06-24T17:33:59.748] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-24T17:33:59.754] [DEBUG] debug-file - load to the disk finished
[2025-06-24T17:33:59.767] [DEBUG] debug-file - Module SheShudu Collected Dependency: 
[2025-06-24T17:33:59.767] [DEBUG] debug-file - Module SheShudu's total dependency: 0
[2025-06-24T17:33:59.769] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-24T17:33:59.769] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-24T17:33:59.771] [DEBUG] debug-file - Configuration phase cost:279 ms 
[2025-06-24T17:33:59.776] [DEBUG] debug-file - Configuration task cost before running: 309 ms 
[2025-06-24T17:33:59.779] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.779] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.783] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-24T17:33:59.791] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 7 ms .
[2025-06-24T17:33:59.792] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.31183624267578125
[2025-06-24T17:33:59.795] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-24T17:33:59.798] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.798] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.799] [DEBUG] debug-file - Executing task :entry:default@GenerateMetadata
[2025-06-24T17:33:59.800] [DEBUG] debug-file - Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:33:59.801] [DEBUG] debug-file - Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .
[2025-06-24T17:33:59.801] [DEBUG] debug-file - entry : default@GenerateMetadata cost memory 0.092498779296875
[2025-06-24T17:33:59.801] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateMetadata...  
[2025-06-24T17:33:59.803] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.803] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.803] [DEBUG] debug-file - Executing task :entry:default@ConfigureCmake
[2025-06-24T17:33:59.804] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.804] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.804] [DEBUG] debug-file - entry : default@ConfigureCmake cost memory 0.0360260009765625
[2025-06-24T17:33:59.804] [DEBUG] debug-file - runTaskFromQueue task cost before running: 336 ms 
[2025-06-24T17:33:59.804] [INFO] debug-file - Finished :entry:default@ConfigureCmake... after 1 ms 
[2025-06-24T17:33:59.806] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.807] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.807] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-24T17:33:59.809] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .
[2025-06-24T17:33:59.809] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.10509490966796875
[2025-06-24T17:33:59.809] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-24T17:33:59.811] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.811] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.812] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-24T17:33:59.813] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:33:59.814] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2025-06-24T17:33:59.814] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.10121917724609375
[2025-06-24T17:33:59.814] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-24T17:33:59.816] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.816] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.817] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-24T17:33:59.817] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.817] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.817] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03624725341796875
[2025-06-24T17:33:59.818] [DEBUG] debug-file - runTaskFromQueue task cost before running: 350 ms 
[2025-06-24T17:33:59.818] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-24T17:33:59.820] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.820] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.826] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-24T17:33:59.826] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:33:59.827] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.03856658935546875
[2025-06-24T17:33:59.827] [DEBUG] debug-file - runTaskFromQueue task cost before running: 360 ms 
[2025-06-24T17:33:59.827] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 2 ms 
[2025-06-24T17:33:59.831] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.831] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.833] [DEBUG] debug-file - Executing task :entry:default@ProcessIntegratedHsp
[2025-06-24T17:33:59.836] [DEBUG] debug-file - entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json' does not exist.
[2025-06-24T17:33:59.837] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .
[2025-06-24T17:33:59.837] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.837] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.837] [DEBUG] debug-file - entry : default@ProcessIntegratedHsp cost memory 0.11837005615234375
[2025-06-24T17:33:59.837] [DEBUG] debug-file - runTaskFromQueue task cost before running: 370 ms 
[2025-06-24T17:33:59.839] [INFO] debug-file - Finished :entry:default@ProcessIntegratedHsp... after 4 ms 
[2025-06-24T17:33:59.843] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.844] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.845] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithCmake
[2025-06-24T17:33:59.845] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.845] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.845] [DEBUG] debug-file - entry : default@BuildNativeWithCmake cost memory 0.037078857421875
[2025-06-24T17:33:59.845] [DEBUG] debug-file - runTaskFromQueue task cost before running: 378 ms 
[2025-06-24T17:33:59.846] [INFO] debug-file - Finished :entry:default@BuildNativeWithCmake... after 1 ms 
[2025-06-24T17:33:59.848] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.849] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.849] [DEBUG] debug-file - Executing task :entry:default@MakePackInfo
[2025-06-24T17:33:59.853] [DEBUG] debug-file - Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .
[2025-06-24T17:33:59.854] [DEBUG] debug-file - entry : default@MakePackInfo cost memory 0.13848114013671875
[2025-06-24T17:33:59.859] [INFO] debug-file - UP-TO-DATE :entry:default@MakePackInfo...  
[2025-06-24T17:33:59.861] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.862] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.864] [DEBUG] debug-file - Executing task :entry:default@SyscapTransform
[2025-06-24T17:33:59.864] [DEBUG] debug-file - File: 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.
[2025-06-24T17:33:59.865] [DEBUG] debug-file - Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:33:59.867] [DEBUG] debug-file - entry:default@SyscapTransform is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc' does not exist.
[2025-06-24T17:33:59.867] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .
[2025-06-24T17:33:59.868] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.868] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.868] [DEBUG] debug-file - entry : default@SyscapTransform cost memory 0.15129852294921875
[2025-06-24T17:33:59.869] [DEBUG] debug-file - runTaskFromQueue task cost before running: 402 ms 
[2025-06-24T17:33:59.869] [INFO] debug-file - Finished :entry:default@SyscapTransform... after 5 ms 
[2025-06-24T17:33:59.879] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.879] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.881] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-24T17:33:59.882] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .
[2025-06-24T17:33:59.883] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.06026458740234375
[2025-06-24T17:33:59.884] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-24T17:33:59.887] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.889] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.891] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-24T17:33:59.895] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .
[2025-06-24T17:33:59.896] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.2014617919921875
[2025-06-24T17:33:59.897] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-24T17:33:59.899] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.899] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.900] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithNinja
[2025-06-24T17:33:59.900] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.900] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.901] [DEBUG] debug-file - entry : default@BuildNativeWithNinja cost memory 0.05667877197265625
[2025-06-24T17:33:59.901] [DEBUG] debug-file - runTaskFromQueue task cost before running: 434 ms 
[2025-06-24T17:33:59.902] [INFO] debug-file - Finished :entry:default@BuildNativeWithNinja... after 2 ms 
[2025-06-24T17:33:59.904] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.905] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.907] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@ProcessResource
[2025-06-24T17:33:59.910] [DEBUG] debug-file - Executing task :entry:default@ProcessResource
[2025-06-24T17:33:59.913] [DEBUG] debug-file - Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .
[2025-06-24T17:33:59.913] [DEBUG] debug-file - entry : default@ProcessResource cost memory 0.16938018798828125
[2025-06-24T17:33:59.918] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessResource...  
[2025-06-24T17:33:59.920] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.920] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.926] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-24T17:33:59.942] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-06-24T17:33:59.942] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7662124633789062
[2025-06-24T17:33:59.949] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-24T17:33:59.951] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.951] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.952] [DEBUG] debug-file - Executing task :entry:default@ProcessLibs
[2025-06-24T17:33:59.955] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.955] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.957] [DEBUG] debug-file - entry : default@ProcessLibs cost memory 0.12554168701171875
[2025-06-24T17:33:59.957] [DEBUG] debug-file - runTaskFromQueue task cost before running: 490 ms 
[2025-06-24T17:33:59.959] [INFO] debug-file - Finished :entry:default@ProcessLibs... after 6 ms 
[2025-06-24T17:33:59.963] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:33:59.963] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:33:59.965] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@CompileResource
[2025-06-24T17:33:59.969] [DEBUG] debug-file - Executing task :entry:default@CompileResource
[2025-06-24T17:34:00.003] [DEBUG] debug-file - Incremental task entry:default@CompileResource pre-execution cost: 33 ms .
[2025-06-24T17:34:00.003] [DEBUG] debug-file - entry : default@CompileResource cost memory 1.4075851440429688
[2025-06-24T17:34:00.005] [INFO] debug-file - UP-TO-DATE :entry:default@CompileResource...  
[2025-06-24T17:34:00.011] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:00.012] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:00.015] [DEBUG] debug-file - Executing task :entry:default@DoNativeStrip
[2025-06-24T17:34:00.015] [DEBUG] debug-file - Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:00.016] [DEBUG] debug-file - Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .
[2025-06-24T17:34:00.017] [DEBUG] debug-file - entry : default@DoNativeStrip cost memory 0.0774688720703125
[2025-06-24T17:34:00.017] [INFO] debug-file - UP-TO-DATE :entry:default@DoNativeStrip...  
[2025-06-24T17:34:00.019] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:00.019] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:00.027] [DEBUG] debug-file - Executing task :entry:default@CompileArkTS
[2025-06-24T17:34:00.041] [DEBUG] debug-file - entry:default@CompileArkTS is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets' has been changed.
[2025-06-24T17:34:00.041] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .
[2025-06-24T17:34:00.059] [DEBUG] debug-file - build config:
[2025-06-24T17:34:00.059] [DEBUG] debug-file - {
  moduleType: 'entry',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile',
  etsLoaderPath: 'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader',
  modulePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
  compileSdkVersion: 14,
  compatibleSdkVersion: 12,
  compatibleSdkVersionStage: 'beta1',
  bundleName: 'c***5',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\modules.ap',
  entryModuleName: 'entry',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'entry',
  allModuleNameHash: '5f173d74b06d1b11d4ab34f4a3957465',
  externalApiPaths: [
    'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default',
  bundleType: 'atomicService',
  arkTSVersion: undefined,
  apiVersion: 14,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: { caseSensitiveCheck: true, useNormalizedOHMUrl: false },
  buildDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build',
  deviceTypes: [ 'phone', 'tablet' ],
  useNormalizedOHMUrl: false,
  pkgContextInfo: undefined,
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: undefined, definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  aceModuleJsonPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json',
  appResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt',
  rawFileResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile',
  resourceTableHash: 'b4dba2e09ce066d2be03c76cd5133ec1',
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:14:*********:Release',
  aceModuleRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\supervisual',
  aceBuildJson: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json',
  cachePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug',
  aceModuleBuild: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets',
  supportChunks: true,
  declaredFilesPath: undefined,
  pkgNameToPkgBriefInfo: {
    entry: {
      pkgRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'entry'
    }
  },
  projectModel: {
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\src\\ohosTest\\ets': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\ohosTest': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu': {
      moduleName: 'SheShudu',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    }
  },
  pkgJsonFileHash: 'd01805ed5ebaa3fd7df4966d8c5aaf3c',
  allModulePaths: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5',
    mockConfigKey2ModuleInfo: {}
  },
  otherPaths: {
    'entry/*': [
      '..\\*',
      '..\\..\\..\\build\\default\\generated\\profile\\default\\*'
    ]
  },
  collectImportersConfig: undefined
}
[2025-06-24T17:34:00.059] [DEBUG] debug-file - Compile arkts with external api path: C:\Program Files\Huawei\DevEco Studio\sdk\default\hms\ets
[2025-06-24T17:34:00.060] [DEBUG] debug-file - default@CompileArkTS work[86] is submitted.
[2025-06-24T17:34:00.061] [DEBUG] debug-file - default@CompileArkTS work[86] is pushed to ready queue.
[2025-06-24T17:34:00.061] [DEBUG] debug-file - default@CompileArkTS work[86] is not dispatched.
[2025-06-24T17:34:00.061] [DEBUG] debug-file - CopyResources startTime: 32671957430400
[2025-06-24T17:34:00.061] [DEBUG] debug-file - default@CompileArkTS work[87] is submitted.
[2025-06-24T17:34:00.062] [DEBUG] debug-file - default@CompileArkTS work[87] is pushed to ready queue.
[2025-06-24T17:34:00.062] [DEBUG] debug-file - default@CompileArkTS work[87] is not dispatched.
[2025-06-24T17:34:00.062] [DEBUG] debug-file - entry : default@CompileArkTS cost memory -4.837974548339844
[2025-06-24T17:34:00.064] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:00.064] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:00.067] [DEBUG] debug-file - Executing task :entry:default@BuildJS
[2025-06-24T17:34:00.070] [DEBUG] debug-file - entry : default@BuildJS cost memory 0.1268463134765625
[2025-06-24T17:34:00.070] [DEBUG] debug-file - runTaskFromQueue task cost before running: 602 ms 
[2025-06-24T17:34:00.071] [INFO] debug-file - Finished :entry:default@BuildJS... after 3 ms 
[2025-06-24T17:34:00.074] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:00.074] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:00.077] [DEBUG] debug-file - Executing task :entry:default@CacheNativeLibs
[2025-06-24T17:34:00.077] [DEBUG] debug-file - Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:00.078] [DEBUG] debug-file - Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .
[2025-06-24T17:34:00.078] [DEBUG] debug-file - entry : default@CacheNativeLibs cost memory 0.08788299560546875
[2025-06-24T17:34:00.079] [INFO] debug-file - UP-TO-DATE :entry:default@CacheNativeLibs...  
[2025-06-24T17:34:00.272] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:34:00.274] [DEBUG] debug-file - default@CompileArkTS work[86] has been dispatched to worker[4].
[2025-06-24T17:34:00.705] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:34:00.705] [DEBUG] debug-file - A work dispatched to worker[4] failed because of worker busy.
[2025-06-24T17:34:00.705] [DEBUG] debug-file - Create  resident worker with id: 3.
[2025-06-24T17:34:00.707] [DEBUG] debug-file - default@CompileArkTS work[87] has been dispatched to worker[3].
[2025-06-24T17:34:02.726] [DEBUG] debug-file - worker[3] has one work done.
[2025-06-24T17:34:02.727] [DEBUG] debug-file - CopyResources is end, endTime: 32674622354000
[2025-06-24T17:34:02.727] [DEBUG] debug-file - default@CompileArkTS work[87] done.
[2025-06-24T17:34:02.738] [DEBUG] debug-file - Ark compile task finished.finished time is 32674634235600
[2025-06-24T17:34:02.738] [DEBUG] debug-file - worker[4] has one work error.
[2025-06-24T17:34:02.738] [DEBUG] debug-file - default@CompileArkTS work[86] failed.
[2025-06-24T17:34:02.739] [ERROR] debug-file - Failed :entry:default@CompileArkTS... 
[2025-06-24T17:34:02.739] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-24T17:34:02.742] [DEBUG] debug-file - ERROR: stacktrace = Error: ArkTS Compiler Error
[33m1 WARN: [33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs[39m
[31m1 ERROR: [31mArkTS:ERROR File: D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/ets/view/ShuduGame.ets:518:21
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)

[39m
[31m2 ERROR: [31mArkTS:ERROR File: D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/ets/view/ShuduGame.ets:508:12
 Property 'maxWidth' does not exist on type 'ColumnAttribute'.

[39m
[31mCOMPILE RESULT:FAIL {ERROR:3 WARN:1}[39m
    at runArkPack (C:\Program Files\Huawei\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-compose\dist\src\arkts-pack.js:1:5418)
[2025-06-24T17:34:02.743] [ERROR] debug-file - Error: ArkTS Compiler Error
[33m1 WARN: [33mArkTS:WARN: For details about ArkTS syntax errors, see FAQs[39m
[31m1 ERROR: [31mArkTS:ERROR File: D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/ets/view/ShuduGame.ets:518:21
 Use explicit types instead of "any", "unknown" (arkts-no-any-unknown)

[39m
[31m2 ERROR: [31mArkTS:ERROR File: D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/ets/view/ShuduGame.ets:508:12
 Property 'maxWidth' does not exist on type 'ColumnAttribute'.

[39m
[31mCOMPILE RESULT:FAIL {ERROR:3 WARN:1}[39m
    at runArkPack (C:\Program Files\Huawei\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-compose\dist\src\arkts-pack.js:1:5418)
[2025-06-24T17:34:02.753] [WARN] debug-file - BUILD FAILED in 3 s 285 ms 
[2025-06-24T17:34:02.753] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-24T17:34:02.753] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.
[2025-06-24T17:34:02.753] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-24T17:34:02.754] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-24T17:34:02.754] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-24T17:34:02.755] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:2 ms .
[2025-06-24T17:34:02.757] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json cache.
[2025-06-24T17:34:02.757] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .
[2025-06-24T17:34:02.757] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.
[2025-06-24T17:34:02.757] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\syscap_tool.exe cache by regenerate.
[2025-06-24T17:34:02.758] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\ets\api\device-define cache by regenerate.
[2025-06-24T17:34:02.759] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc cache.
[2025-06-24T17:34:02.760] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:3 ms .
[2025-06-24T17:34:02.760] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-24T17:34:02.760] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-24T17:34:02.760] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2025-06-24T17:34:02.760] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-24T17:34:02.761] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5 cache by regenerate.
[2025-06-24T17:34:02.761] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build-profile.json5 cache by regenerate.
[2025-06-24T17:34:02.761] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache.
[2025-06-24T17:34:02.761] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:2 ms .
[2025-06-24T17:34:02.762] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.
[2025-06-24T17:34:02.762] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2025-06-24T17:34:02.764] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:34:02.764] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:34:02.765] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:34:02.765] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:34:02.766] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:34:02.766] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets cache from map.
[2025-06-24T17:34:02.766] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:34:02.767] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-24T17:34:02.767] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache.
[2025-06-24T17:34:02.767] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:6 ms .
[2025-06-24T17:34:02.768] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:34:02.769] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:34:02.770] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:34:02.770] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:34:02.770] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:34:02.771] [DEBUG] debug-file - Update task entry:default@BuildJS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\js cache.
[2025-06-24T17:34:02.771] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:4 ms .
[2025-06-24T17:34:02.771] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2025-06-24T17:34:02.789] [DEBUG] debug-file - Cleanup worker 3.
[2025-06-24T17:34:02.789] [DEBUG] debug-file - Worker 3 has been cleaned up.
[2025-06-24T17:34:02.790] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-24T17:34:02.790] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-24T17:34:02.790] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-24T17:34:02.800] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:34:02.800] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:34:02.801] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:34:02.801] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:34:02.801] [DEBUG] debug-file - worker[3] exits with exit code 1.
[2025-06-24T17:34:43.606] [DEBUG] debug-file - session manager: set active socket. socketId=C9ySEjIhBsxoN69KAABP
[2025-06-24T17:34:43.612] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:34:43.629] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-24T17:34:43.636] [DEBUG] debug-file - Cache service initialization finished in 7 ms 
[2025-06-24T17:34:43.652] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:34:43.661] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:34:43.662] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:34:43.669] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-24T17:34:43.669] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-24T17:34:43.669] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-24T17:34:43.669] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-24T17:34:43.672] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  }
} in this build.
[2025-06-24T17:34:43.680] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-24T17:34:43.695] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-24T17:34:43.726] [DEBUG] debug-file - Sdk init in 45 ms 
[2025-06-24T17:34:43.797] [DEBUG] debug-file - Project task initialization takes 54 ms 
[2025-06-24T17:34:43.797] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:34:43.797] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:34:43.797] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\hvigorfile.ts
[2025-06-24T17:34:43.812] [DEBUG] debug-file - hvigorfile, resolving D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:34:43.815] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-24T17:34:43.816] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-24T17:34:43.827] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-24T17:34:43.828] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-24T17:34:43.828] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-24T17:34:43.828] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
}
[2025-06-24T17:34:43.828] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "default"
}
[2025-06-24T17:34:43.828] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-24T17:34:43.828] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": false
  },
  "name": "debug"
} in this build.
[2025-06-24T17:34:43.833] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2025-06-24T17:34:43.834] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-24T17:34:43.834] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:34:43.834] [DEBUG] debug-file - hvigorfile, resolve finished D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\hvigorfile.ts
[2025-06-24T17:34:43.970] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 135 ms 
[2025-06-24T17:34:43.971] [DEBUG] debug-file - project has submodules:entry
[2025-06-24T17:34:43.973] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-24T17:34:43.998] [DEBUG] debug-file - load to the disk finished
[2025-06-24T17:34:44.004] [DEBUG] debug-file - Module SheShudu Collected Dependency: 
[2025-06-24T17:34:44.004] [DEBUG] debug-file - Module SheShudu's total dependency: 0
[2025-06-24T17:34:44.007] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-24T17:34:44.007] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-24T17:34:44.010] [DEBUG] debug-file - Configuration phase cost:369 ms 
[2025-06-24T17:34:44.012] [DEBUG] debug-file - Configuration task cost before running: 397 ms 
[2025-06-24T17:34:44.014] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.014] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.017] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-24T17:34:44.024] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 6 ms .
[2025-06-24T17:34:44.025] [DEBUG] debug-file - entry : default@PreBuild cost memory 0.312713623046875
[2025-06-24T17:34:44.027] [INFO] debug-file - UP-TO-DATE :entry:default@PreBuild...  
[2025-06-24T17:34:44.029] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.029] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.030] [DEBUG] debug-file - Executing task :entry:default@GenerateMetadata
[2025-06-24T17:34:44.031] [DEBUG] debug-file - Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:44.032] [DEBUG] debug-file - Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .
[2025-06-24T17:34:44.033] [DEBUG] debug-file - entry : default@GenerateMetadata cost memory 0.092498779296875
[2025-06-24T17:34:44.033] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateMetadata...  
[2025-06-24T17:34:44.035] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.035] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.035] [DEBUG] debug-file - Executing task :entry:default@ConfigureCmake
[2025-06-24T17:34:44.035] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.035] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.036] [DEBUG] debug-file - entry : default@ConfigureCmake cost memory 0.0360260009765625
[2025-06-24T17:34:44.036] [DEBUG] debug-file - runTaskFromQueue task cost before running: 421 ms 
[2025-06-24T17:34:44.036] [INFO] debug-file - Finished :entry:default@ConfigureCmake... after 1 ms 
[2025-06-24T17:34:44.038] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.038] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.039] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-24T17:34:44.042] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .
[2025-06-24T17:34:44.042] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.1051025390625
[2025-06-24T17:34:44.042] [INFO] debug-file - UP-TO-DATE :entry:default@MergeProfile...  
[2025-06-24T17:34:44.044] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.044] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.046] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-24T17:34:44.047] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:44.049] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .
[2025-06-24T17:34:44.049] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.1016845703125
[2025-06-24T17:34:44.049] [INFO] debug-file - UP-TO-DATE :entry:default@CreateBuildProfile...  
[2025-06-24T17:34:44.053] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.053] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.055] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-24T17:34:44.056] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.056] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.056] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03624725341796875
[2025-06-24T17:34:44.056] [DEBUG] debug-file - runTaskFromQueue task cost before running: 441 ms 
[2025-06-24T17:34:44.056] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 2 ms 
[2025-06-24T17:34:44.058] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.059] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.062] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-24T17:34:44.062] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:44.063] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.0382843017578125
[2025-06-24T17:34:44.063] [DEBUG] debug-file - runTaskFromQueue task cost before running: 448 ms 
[2025-06-24T17:34:44.063] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 1 ms 
[2025-06-24T17:34:44.065] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.065] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.067] [DEBUG] debug-file - Executing task :entry:default@ProcessIntegratedHsp
[2025-06-24T17:34:44.069] [DEBUG] debug-file - entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json' does not exist.
[2025-06-24T17:34:44.069] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .
[2025-06-24T17:34:44.069] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.069] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.070] [DEBUG] debug-file - entry : default@ProcessIntegratedHsp cost memory 0.11786651611328125
[2025-06-24T17:34:44.070] [DEBUG] debug-file - runTaskFromQueue task cost before running: 455 ms 
[2025-06-24T17:34:44.070] [INFO] debug-file - Finished :entry:default@ProcessIntegratedHsp... after 3 ms 
[2025-06-24T17:34:44.073] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.073] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.074] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithCmake
[2025-06-24T17:34:44.074] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.074] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.074] [DEBUG] debug-file - entry : default@BuildNativeWithCmake cost memory 0.037078857421875
[2025-06-24T17:34:44.075] [DEBUG] debug-file - runTaskFromQueue task cost before running: 460 ms 
[2025-06-24T17:34:44.075] [INFO] debug-file - Finished :entry:default@BuildNativeWithCmake... after 1 ms 
[2025-06-24T17:34:44.077] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.077] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.078] [DEBUG] debug-file - Executing task :entry:default@MakePackInfo
[2025-06-24T17:34:44.081] [DEBUG] debug-file - Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .
[2025-06-24T17:34:44.081] [DEBUG] debug-file - entry : default@MakePackInfo cost memory 0.1386871337890625
[2025-06-24T17:34:44.083] [INFO] debug-file - UP-TO-DATE :entry:default@MakePackInfo...  
[2025-06-24T17:34:44.085] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.085] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.088] [DEBUG] debug-file - Executing task :entry:default@SyscapTransform
[2025-06-24T17:34:44.088] [DEBUG] debug-file - File: 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.
[2025-06-24T17:34:44.089] [DEBUG] debug-file - Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:44.091] [DEBUG] debug-file - entry:default@SyscapTransform is not up-to-date, since the output file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc' does not exist.
[2025-06-24T17:34:44.091] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .
[2025-06-24T17:34:44.091] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.091] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.091] [DEBUG] debug-file - entry : default@SyscapTransform cost memory 0.15102386474609375
[2025-06-24T17:34:44.091] [DEBUG] debug-file - runTaskFromQueue task cost before running: 477 ms 
[2025-06-24T17:34:44.092] [INFO] debug-file - Finished :entry:default@SyscapTransform... after 4 ms 
[2025-06-24T17:34:44.094] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.094] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.096] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-24T17:34:44.097] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2025-06-24T17:34:44.097] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.05956268310546875
[2025-06-24T17:34:44.097] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessProfile...  
[2025-06-24T17:34:44.100] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.100] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.101] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-24T17:34:44.109] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .
[2025-06-24T17:34:44.109] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory -5.297737121582031
[2025-06-24T17:34:44.111] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessRouterMap...  
[2025-06-24T17:34:44.113] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.114] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.114] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithNinja
[2025-06-24T17:34:44.115] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.115] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.115] [DEBUG] debug-file - entry : default@BuildNativeWithNinja cost memory 0.056884765625
[2025-06-24T17:34:44.115] [DEBUG] debug-file - runTaskFromQueue task cost before running: 501 ms 
[2025-06-24T17:34:44.116] [INFO] debug-file - Finished :entry:default@BuildNativeWithNinja... after 2 ms 
[2025-06-24T17:34:44.118] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.118] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.119] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@ProcessResource
[2025-06-24T17:34:44.121] [DEBUG] debug-file - Executing task :entry:default@ProcessResource
[2025-06-24T17:34:44.123] [DEBUG] debug-file - Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .
[2025-06-24T17:34:44.123] [DEBUG] debug-file - entry : default@ProcessResource cost memory 0.1692047119140625
[2025-06-24T17:34:44.127] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessResource...  
[2025-06-24T17:34:44.130] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.130] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.133] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-24T17:34:44.151] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-06-24T17:34:44.151] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7632598876953125
[2025-06-24T17:34:44.156] [INFO] debug-file - UP-TO-DATE :entry:default@GenerateLoaderJson...  
[2025-06-24T17:34:44.157] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.157] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.158] [DEBUG] debug-file - Executing task :entry:default@ProcessLibs
[2025-06-24T17:34:44.160] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.160] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.161] [DEBUG] debug-file - entry : default@ProcessLibs cost memory 0.12558746337890625
[2025-06-24T17:34:44.161] [DEBUG] debug-file - runTaskFromQueue task cost before running: 546 ms 
[2025-06-24T17:34:44.162] [INFO] debug-file - Finished :entry:default@ProcessLibs... after 4 ms 
[2025-06-24T17:34:44.164] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.164] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.164] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@CompileResource
[2025-06-24T17:34:44.167] [DEBUG] debug-file - Executing task :entry:default@CompileResource
[2025-06-24T17:34:44.188] [DEBUG] debug-file - Incremental task entry:default@CompileResource pre-execution cost: 20 ms .
[2025-06-24T17:34:44.188] [DEBUG] debug-file - entry : default@CompileResource cost memory 1.4109878540039062
[2025-06-24T17:34:44.189] [INFO] debug-file - UP-TO-DATE :entry:default@CompileResource...  
[2025-06-24T17:34:44.192] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.192] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.194] [DEBUG] debug-file - Executing task :entry:default@DoNativeStrip
[2025-06-24T17:34:44.195] [DEBUG] debug-file - Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:44.196] [DEBUG] debug-file - Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-06-24T17:34:44.196] [DEBUG] debug-file - entry : default@DoNativeStrip cost memory 0.074432373046875
[2025-06-24T17:34:44.196] [INFO] debug-file - UP-TO-DATE :entry:default@DoNativeStrip...  
[2025-06-24T17:34:44.198] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.198] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.202] [DEBUG] debug-file - Executing task :entry:default@CompileArkTS
[2025-06-24T17:34:44.218] [DEBUG] debug-file - build config:
[2025-06-24T17:34:44.218] [DEBUG] debug-file - {
  moduleType: 'entry',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile',
  etsLoaderPath: 'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader',
  modulePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
  compileSdkVersion: 14,
  compatibleSdkVersion: 12,
  compatibleSdkVersionStage: 'beta1',
  bundleName: 'c***5',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\modules.ap',
  entryModuleName: 'entry',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'entry',
  allModuleNameHash: '5f173d74b06d1b11d4ab34f4a3957465',
  externalApiPaths: [
    'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default',
  bundleType: 'atomicService',
  arkTSVersion: undefined,
  apiVersion: 14,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: { caseSensitiveCheck: true, useNormalizedOHMUrl: false },
  buildDir: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build',
  deviceTypes: [ 'phone', 'tablet' ],
  useNormalizedOHMUrl: false,
  pkgContextInfo: undefined,
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: undefined, definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  aceModuleJsonPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json',
  appResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt',
  rawFileResource: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile',
  resourceTableHash: 'b4dba2e09ce066d2be03c76cd5133ec1',
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:14:*********:Release',
  aceModuleRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\supervisual',
  aceBuildJson: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json',
  cachePath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\cache\\default\\default@CompileArkTS\\esmodule\\debug',
  aceModuleBuild: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets',
  supportChunks: true,
  declaredFilesPath: undefined,
  pkgNameToPkgBriefInfo: {
    entry: {
      pkgRoot: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'entry'
    }
  },
  projectModel: {
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\src\\ohosTest\\ets': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\ohosTest': {
      moduleName: 'entry_test',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry': {
      moduleName: 'entry',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    },
    'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu': {
      moduleName: 'SheShudu',
      modulePkgPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu',
      belongProjectPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu'
    }
  },
  pkgJsonFileHash: 'd01805ed5ebaa3fd7df4966d8c5aaf3c',
  allModulePaths: [ 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5',
    mockConfigKey2ModuleInfo: {}
  },
  otherPaths: {
    'entry/*': [
      '..\\*',
      '..\\..\\..\\build\\default\\generated\\profile\\default\\*'
    ]
  },
  collectImportersConfig: undefined
}
[2025-06-24T17:34:44.218] [DEBUG] debug-file - Compile arkts with external api path: C:\Program Files\Huawei\DevEco Studio\sdk\default\hms\ets
[2025-06-24T17:34:44.219] [DEBUG] debug-file - default@CompileArkTS work[88] is submitted.
[2025-06-24T17:34:44.220] [DEBUG] debug-file - default@CompileArkTS work[88] is pushed to ready queue.
[2025-06-24T17:34:44.220] [DEBUG] debug-file - default@CompileArkTS work[88] is not dispatched.
[2025-06-24T17:34:44.220] [DEBUG] debug-file - CopyResources startTime: 32716115917600
[2025-06-24T17:34:44.220] [DEBUG] debug-file - default@CompileArkTS work[89] is submitted.
[2025-06-24T17:34:44.220] [DEBUG] debug-file - default@CompileArkTS work[89] is pushed to ready queue.
[2025-06-24T17:34:44.220] [DEBUG] debug-file - default@CompileArkTS work[89] is not dispatched.
[2025-06-24T17:34:44.221] [DEBUG] debug-file - entry : default@CompileArkTS cost memory 1.1825637817382812
[2025-06-24T17:34:44.225] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.225] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.228] [DEBUG] debug-file - Executing task :entry:default@BuildJS
[2025-06-24T17:34:44.231] [DEBUG] debug-file - entry : default@BuildJS cost memory 0.1268310546875
[2025-06-24T17:34:44.231] [DEBUG] debug-file - runTaskFromQueue task cost before running: 616 ms 
[2025-06-24T17:34:44.231] [INFO] debug-file - Finished :entry:default@BuildJS... after 3 ms 
[2025-06-24T17:34:44.233] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:44.233] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:44.236] [DEBUG] debug-file - Executing task :entry:default@CacheNativeLibs
[2025-06-24T17:34:44.237] [DEBUG] debug-file - Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:44.239] [DEBUG] debug-file - Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .
[2025-06-24T17:34:44.239] [DEBUG] debug-file - entry : default@CacheNativeLibs cost memory 0.0886077880859375
[2025-06-24T17:34:44.239] [INFO] debug-file - UP-TO-DATE :entry:default@CacheNativeLibs...  
[2025-06-24T17:34:44.438] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:34:44.439] [DEBUG] debug-file - default@CompileArkTS work[88] has been dispatched to worker[4].
[2025-06-24T17:34:44.439] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:34:44.439] [DEBUG] debug-file - A work dispatched to worker[4] failed because of worker busy.
[2025-06-24T17:34:44.439] [DEBUG] debug-file - Create  resident worker with id: 3.
[2025-06-24T17:34:44.441] [DEBUG] debug-file - default@CompileArkTS work[89] has been dispatched to worker[3].
[2025-06-24T17:34:45.918] [DEBUG] debug-file - worker[3] has one work done.
[2025-06-24T17:34:45.919] [DEBUG] debug-file - CopyResources is end, endTime: 32717815024000
[2025-06-24T17:34:45.919] [DEBUG] debug-file - default@CompileArkTS work[89] done.
[2025-06-24T17:34:46.144] [DEBUG] debug-file - A work dispatched to worker[3] failed because unable to get work from ready queue.
[2025-06-24T17:34:46.513] [DEBUG] debug-file - Ark compile task finished.finished time is 32718409261000
[2025-06-24T17:34:46.513] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:34:46.521] [DEBUG] debug-file - default@CompileArkTS work[88] done.
[2025-06-24T17:34:46.521] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-24T17:34:46.524] [INFO] debug-file - Finished :entry:default@CompileArkTS... after 2 s 94 ms 
[2025-06-24T17:34:46.526] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:46.526] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:46.528] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgModuleJson
[2025-06-24T17:34:46.528] [DEBUG] debug-file - Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms 
[2025-06-24T17:34:46.529] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .
[2025-06-24T17:34:46.529] [DEBUG] debug-file - entry : default@GeneratePkgModuleJson cost memory 0.07288360595703125
[2025-06-24T17:34:46.529] [INFO] debug-file - UP-TO-DATE :entry:default@GeneratePkgModuleJson...  
[2025-06-24T17:34:46.532] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:46.533] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:46.541] [DEBUG] debug-file - Executing task :entry:default@PackageHap
[2025-06-24T17:34:46.560] [DEBUG] debug-file - entry:default@PackageHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets' has been changed.
[2025-06-24T17:34:46.560] [DEBUG] debug-file - Incremental task entry:default@PackageHap pre-execution cost: 18 ms .
[2025-06-24T17:34:46.560] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:46.560] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:46.563] [DEBUG] debug-file - Use tool [C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\lib\app_packing_tool.jar]
 [
  'java',
  '-Dfile.encoding=GBK',
  '-jar',
  'C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar',
  '--mode',
  'hap',
  '--force',
  'true',
  '--lib-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default',
  '--json-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json',
  '--resources-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources',
  '--index-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index',
  '--pack-info-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info',
  '--out-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap',
  '--ets-path',
  'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets'
]
[2025-06-24T17:34:46.565] [DEBUG] debug-file - default@PackageHap work[90] is submitted.
[2025-06-24T17:34:46.565] [DEBUG] debug-file - default@PackageHap work[90] is pushed to ready queue.
[2025-06-24T17:34:46.566] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-24T17:34:46.566] [DEBUG] debug-file - default@PackageHap work[90] has been dispatched to worker[4].
[2025-06-24T17:34:46.566] [DEBUG] debug-file - default@PackageHap work[90] is dispatched.
[2025-06-24T17:34:46.576] [DEBUG] debug-file - entry : default@PackageHap cost memory 1.276519775390625
[2025-06-24T17:34:46.569] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 779907072,
  heapTotal: 498348032,
  heapUsed: 451192984,
  external: 5107105,
  arrayBuffers: 1139624
} os memoryUsage :25.214717864990234
[2025-06-24T17:34:47.101] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-24T17:34:47.101] [DEBUG] debug-file - default@PackageHap work[90] done.
[2025-06-24T17:34:47.101] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-24T17:34:47.102] [INFO] debug-file - Finished :entry:default@PackageHap... after 560 ms 
[2025-06-24T17:34:47.105] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:47.105] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:47.107] [DEBUG] debug-file - Executing task :entry:default@SignHap
[2025-06-24T17:34:47.108] [WARN] debug-file - Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5.
[2025-06-24T17:34:47.108] [DEBUG] debug-file - entry:default@SignHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap' has been changed.
[2025-06-24T17:34:47.108] [DEBUG] debug-file - Incremental task entry:default@SignHap pre-execution cost: 1 ms .
[2025-06-24T17:34:47.108] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:47.108] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:47.109] [DEBUG] debug-file - entry : default@SignHap cost memory 0.11479949951171875
[2025-06-24T17:34:47.109] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 494 ms 
[2025-06-24T17:34:47.109] [INFO] debug-file - Finished :entry:default@SignHap... after 2 ms 
[2025-06-24T17:34:47.111] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:47.111] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:47.112] [DEBUG] debug-file - Executing task :entry:default@CollectDebugSymbol
[2025-06-24T17:34:47.116] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-24T17:34:47.116] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-24T17:34:47.116] [DEBUG] debug-file - entry : default@CollectDebugSymbol cost memory 0.2395782470703125
[2025-06-24T17:34:47.116] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 501 ms 
[2025-06-24T17:34:47.116] [INFO] debug-file - Finished :entry:default@CollectDebugSymbol... after 5 ms 
[2025-06-24T17:34:47.118] [DEBUG] debug-file - Executing task :entry:assembleHap
[2025-06-24T17:34:47.118] [DEBUG] debug-file - entry : assembleHap cost memory 0.0113067626953125
[2025-06-24T17:34:47.118] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 503 ms 
[2025-06-24T17:34:47.118] [INFO] debug-file - Finished :entry:assembleHap... after 1 ms 
[2025-06-24T17:34:47.126] [DEBUG] debug-file - BUILD SUCCESSFUL in 3 s 511 ms 
[2025-06-24T17:34:47.126] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-24T17:34:47.126] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.
[2025-06-24T17:34:47.126] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-24T17:34:47.126] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-24T17:34:47.126] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-24T17:34:47.127] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-24T17:34:47.127] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json cache.
[2025-06-24T17:34:47.128] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-06-24T17:34:47.128] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.
[2025-06-24T17:34:47.128] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\syscap_tool.exe cache by regenerate.
[2025-06-24T17:34:47.128] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\ets\api\device-define cache by regenerate.
[2025-06-24T17:34:47.129] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc cache.
[2025-06-24T17:34:47.129] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:2 ms .
[2025-06-24T17:34:47.129] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-24T17:34:47.129] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-24T17:34:47.129] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2025-06-24T17:34:47.129] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-24T17:34:47.130] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5 cache by regenerate.
[2025-06-24T17:34:47.130] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build-profile.json5 cache by regenerate.
[2025-06-24T17:34:47.130] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache.
[2025-06-24T17:34:47.130] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:1 ms .
[2025-06-24T17:34:47.130] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.
[2025-06-24T17:34:47.130] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2025-06-24T17:34:47.132] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:34:47.133] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:34:47.133] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:34:47.134] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:34:47.134] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:34:47.134] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets cache by regenerate.
[2025-06-24T17:34:47.138] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:34:47.138] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-24T17:34:47.141] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache.
[2025-06-24T17:34:47.141] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:11 ms .
[2025-06-24T17:34:47.143] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-24T17:34:47.143] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-24T17:34:47.144] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-24T17:34:47.144] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-24T17:34:47.145] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-24T17:34:47.145] [DEBUG] debug-file - Update task entry:default@BuildJS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\js cache.
[2025-06-24T17:34:47.146] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:5 ms .
[2025-06-24T17:34:47.146] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2025-06-24T17:34:47.146] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.
[2025-06-24T17:34:47.147] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\stripped_native_libs\default cache by regenerate.
[2025-06-24T17:34:47.147] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\module.json cache by regenerate.
[2025-06-24T17:34:47.147] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources cache by regenerate.
[2025-06-24T17:34:47.154] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources.index cache by regenerate.
[2025-06-24T17:34:47.154] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\pack.info cache by regenerate.
[2025-06-24T17:34:47.155] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache from map.
[2025-06-24T17:34:47.155] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:34:47.155] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-24T17:34:47.156] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache.
[2025-06-24T17:34:47.156] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\source_map\default\sourceMaps.map cache.
[2025-06-24T17:34:47.156] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\mapping\sourceMaps.map cache.
[2025-06-24T17:34:47.156] [DEBUG] debug-file - Incremental task entry:default@PackageHap post-execution cost:11 ms .
[2025-06-24T17:34:47.156] [DEBUG] debug-file - Update task entry:default@SignHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache from map.
[2025-06-24T17:34:47.157] [DEBUG] debug-file - Update task entry:default@SignHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-signed.hap cache.
[2025-06-24T17:34:47.157] [DEBUG] debug-file - Incremental task entry:default@SignHap post-execution cost:1 ms .
[2025-06-24T17:34:47.159] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-24T17:34:47.159] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache by regenerate.
[2025-06-24T17:34:47.159] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\symbol cache.
[2025-06-24T17:34:47.159] [DEBUG] debug-file - Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .
[2025-06-24T17:34:47.184] [DEBUG] debug-file - Cleanup worker 3.
[2025-06-24T17:34:47.185] [DEBUG] debug-file - Worker 3 has been cleaned up.
[2025-06-24T17:34:47.185] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-24T17:34:47.186] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-24T17:34:47.187] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-24T17:34:47.194] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:34:47.194] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-24T17:34:47.194] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:34:47.194] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-24T17:34:47.216] [DEBUG] debug-file - worker[3] exits with exit code 1.
