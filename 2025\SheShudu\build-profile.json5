{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_SheShudu_-Bq-OlNn9b0GLsKijpLAIc6z2eNOnwXzO9aPhqrdznQ=.cer",
          "storePassword": "0000001B54A5F2BD0DAC7543C793A09190301F10738043E8D818ACCC227466D36506E0844653B3341A0F67",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B11D174023D44BF34AD53FF0E5D2BA30B9697252E1D42CC8F91979D241CFE6DD6EE83E601DFDB7E",
          "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_SheShudu_-Bq-OlNn9b0GLsKijpLAIc6z2eNOnwXzO9aPhqrdznQ=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_SheShudu_-Bq-OlNn9b0GLsKijpLAIc6z2eNOnwXzO9aPhqrdznQ=.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": false
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}