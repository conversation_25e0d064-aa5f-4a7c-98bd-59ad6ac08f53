{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "storePassword": "0000001EAD77A2A0AAE1F3D3911CEF103F81B838572643B96916EC69B26E1D6524B102743FFFE9B5D26DCD37298C",
          "certpath": "D:/yysrc/数海鹅独发布证书.cer",
          "keyAlias": "shed_release",
          "keyPassword": "0000001EAC256CA70E5A0B5A715D346840DF897ABBD26C6871200A22B122AD54F3FEB3D3D51958653C85BFAA9572",
          "profile": "D:/yysrc/数海鹅独发布profileRelease.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "D:/yysrc/sourcecode/mrhp-doc/harmony/shed_release.p12"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": false
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}