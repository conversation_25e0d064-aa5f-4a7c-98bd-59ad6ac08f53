{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "4817044e-e813-47e2-b6a5-4b70d189c320", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34001001175900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19624705-180c-4f21-8e0d-7c3ef0ac0ad8", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34001024600200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ec8b14-394b-46f6-88f0-185d10543296", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34001030987100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368e8114-1c61-466e-af46-ad8a661206cb", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34001031352800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67fb837f-4eca-44dd-b94d-e3df89ae48d7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040620626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6799b47-d832-4c96-bada-4531f8269b48", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040628908800, "endTime": 34041216131000}, "additional": {"children": ["7b42ef1a-eeec-47bc-b309-eae2f6213dcd", "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "465c25b8-7087-426c-af2e-6e06f7bd47c7", "e5eef688-cdc2-45df-ab39-3e16883f0f45", "5ad9bb23-bbaa-479e-9b46-9bd4178d4bd3", "14b10a5f-e25c-414b-b8ca-96ae87af6452", "ce4ea51f-003c-46a3-ae06-67466ba02e05"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "61d607b4-d81b-4195-a41d-b073e248d066"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b42ef1a-eeec-47bc-b309-eae2f6213dcd", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040628911600, "endTime": 34040644494400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "fa514ad7-dd60-4093-bcf2-9803607b8992"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040644514700, "endTime": 34041211407600}, "additional": {"children": ["495c2d79-2e19-45fe-9044-c7637b7e9cd6", "c138d822-75b0-4ceb-b5c2-0c54547fb63c", "9ec381db-08f2-49ce-8c80-2b63ff129f3a", "7e3c484e-5aa6-403f-9ccf-7f7c024f6414", "e7034f58-16b6-4f40-a7c0-f2b81808a9cd", "100e3920-6828-4087-913f-4b81dfdbc2e4", "3e687256-0986-4588-a266-5b6db816df58", "82d2c99e-a358-41d2-b001-f21b15d5da47", "64a4b8e6-be6f-4cfc-a0a0-2ecf5cdeb9bb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "465c25b8-7087-426c-af2e-6e06f7bd47c7", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041211432500, "endTime": 34041216117100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "8b43c4ae-a26b-4af7-8221-74ab3738db4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5eef688-cdc2-45df-ab39-3e16883f0f45", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041216122700, "endTime": 34041216124700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "d04742c5-347b-48a6-91b2-85a8b8c50355"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ad9bb23-bbaa-479e-9b46-9bd4178d4bd3", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040633031700, "endTime": 34040633083000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "e826b076-44ee-4930-8dcf-4ab05eebf857"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e826b076-44ee-4930-8dcf-4ab05eebf857", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040633031700, "endTime": 34040633083000}, "additional": {"logType": "info", "children": [], "durationId": "5ad9bb23-bbaa-479e-9b46-9bd4178d4bd3", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "14b10a5f-e25c-414b-b8ca-96ae87af6452", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040638229700, "endTime": 34040638255500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "de08a47b-48b6-41a1-9425-cf24b3ce7715"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de08a47b-48b6-41a1-9425-cf24b3ce7715", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040638229700, "endTime": 34040638255500}, "additional": {"logType": "info", "children": [], "durationId": "14b10a5f-e25c-414b-b8ca-96ae87af6452", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "13985179-9c26-4193-9da1-64acd4628f83", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040638418900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d04d3b6-f75c-4394-a2ea-2446c83801f3", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040644354200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa514ad7-dd60-4093-bcf2-9803607b8992", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040628911600, "endTime": 34040644494400}, "additional": {"logType": "info", "children": [], "durationId": "7b42ef1a-eeec-47bc-b309-eae2f6213dcd", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "495c2d79-2e19-45fe-9044-c7637b7e9cd6", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040650932100, "endTime": 34040650946200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "dcdd4a1f-a293-472e-ba7d-2ddbf9ccc1f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c138d822-75b0-4ceb-b5c2-0c54547fb63c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040650967100, "endTime": 34040654818700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "370cf206-801e-4f31-a07b-8447c877a0d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ec381db-08f2-49ce-8c80-2b63ff129f3a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040654834000, "endTime": 34040841484900}, "additional": {"children": ["48aa65d9-5cbe-4920-89b3-bf40acef47fd", "64b8c580-df25-40db-ba09-ababca862ce6", "03b7ce25-9cc7-4a71-b30e-71b8209aec97"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "32627da7-2318-4daa-848e-0a18a59c961d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e3c484e-5aa6-403f-9ccf-7f7c024f6414", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040841743100, "endTime": 34040970015400}, "additional": {"children": ["abe18157-0d6b-47bc-8016-12ba5c3e2a78"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "9bb9de7b-8b8d-4ce8-a8f3-dfb53bb0d386"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7034f58-16b6-4f40-a7c0-f2b81808a9cd", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040970026800, "endTime": 34041168611200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "08cdfedb-4df4-48f1-96ef-f1c92d82be54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "100e3920-6828-4087-913f-4b81dfdbc2e4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041169985100, "endTime": 34041193223900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "0601b0c3-d3e9-4904-ac89-0ed40b9df446"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e687256-0986-4588-a266-5b6db816df58", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041193248200, "endTime": 34041211177200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "33b6f9db-f9ae-4c28-9404-d8ffa15f0150"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82d2c99e-a358-41d2-b001-f21b15d5da47", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041211204600, "endTime": 34041211394900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "2a0b7d23-c8e4-47c6-a3c0-3a7e4c5acd4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcdd4a1f-a293-472e-ba7d-2ddbf9ccc1f1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040650932100, "endTime": 34040650946200}, "additional": {"logType": "info", "children": [], "durationId": "495c2d79-2e19-45fe-9044-c7637b7e9cd6", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "370cf206-801e-4f31-a07b-8447c877a0d0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040650967100, "endTime": 34040654818700}, "additional": {"logType": "info", "children": [], "durationId": "c138d822-75b0-4ceb-b5c2-0c54547fb63c", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "48aa65d9-5cbe-4920-89b3-bf40acef47fd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040655361400, "endTime": 34040655378600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec381db-08f2-49ce-8c80-2b63ff129f3a", "logId": "7cc704d7-009a-4fb0-adc0-2b77b56c8c05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cc704d7-009a-4fb0-adc0-2b77b56c8c05", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040655361400, "endTime": 34040655378600}, "additional": {"logType": "info", "children": [], "durationId": "48aa65d9-5cbe-4920-89b3-bf40acef47fd", "parent": "32627da7-2318-4daa-848e-0a18a59c961d"}}, {"head": {"id": "64b8c580-df25-40db-ba09-ababca862ce6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040657225700, "endTime": 34040836412700}, "additional": {"children": ["22ae7a13-5dc8-4c2e-a8cb-2048ba2afc0a", "be2ecbb4-5992-4d63-8132-56d62a2fff9c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec381db-08f2-49ce-8c80-2b63ff129f3a", "logId": "e48a761a-21d1-422b-a5e2-7bb8bf2aa4ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22ae7a13-5dc8-4c2e-a8cb-2048ba2afc0a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040657227500, "endTime": 34040672995700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64b8c580-df25-40db-ba09-ababca862ce6", "logId": "205f2489-8386-4c8e-83e0-7d077f76f978"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be2ecbb4-5992-4d63-8132-56d62a2fff9c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040673010000, "endTime": 34040836354100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64b8c580-df25-40db-ba09-ababca862ce6", "logId": "7bb1052d-b265-4f35-90dc-1cc92239da64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88e72354-a016-4a7e-9598-71cfbf9d8763", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040657233400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51228c16-b89f-42dc-a0ed-7f0424c3fd17", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040672864900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205f2489-8386-4c8e-83e0-7d077f76f978", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040657227500, "endTime": 34040672995700}, "additional": {"logType": "info", "children": [], "durationId": "22ae7a13-5dc8-4c2e-a8cb-2048ba2afc0a", "parent": "e48a761a-21d1-422b-a5e2-7bb8bf2aa4ca"}}, {"head": {"id": "f197c375-7b64-49a2-8db2-5d3cc37f540c", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040673022900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21216e6a-68f9-4b4c-91b7-5a60be4ef9fb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040696526100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408a2379-fce9-45c4-ae8d-83421c36ec00", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040696932600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048118f2-800b-4b3b-83ef-48a433880e3c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040697089200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809dba21-bbaf-40c6-ad01-a974ef2e4f26", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040697234700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4388dc66-7cff-42ea-bc30-fce8372e9045", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040706180600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd3d0e0-e402-4108-98e5-6b3f0b1a855f", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040721144700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f8031d3-e265-4a59-a072-461d0f7b11c5", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040737271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bc3de0-45ca-4fe8-895e-7fde746b176f", "name": "Sdk init in 60 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040781733600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "593b2e21-b073-443c-a97f-980e108770ca", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040781896500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 56}, "markType": "other"}}, {"head": {"id": "62e1185d-c7e5-4f8a-befd-b5883c8f547d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040781951900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 56}, "markType": "other"}}, {"head": {"id": "03b951c5-6425-4956-afdb-616f670dc3aa", "name": "Project task initialization takes 53 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040836061200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c76d3b-70f3-4134-96e7-3fd596078e2d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040836195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5320b3a7-cd3c-47d3-9579-e3a5f0dde15c", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040836257700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87bb7faf-8875-45a7-9a47-4b0c865ecc74", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040836305500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb1052d-b265-4f35-90dc-1cc92239da64", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040673010000, "endTime": 34040836354100}, "additional": {"logType": "info", "children": [], "durationId": "be2ecbb4-5992-4d63-8132-56d62a2fff9c", "parent": "e48a761a-21d1-422b-a5e2-7bb8bf2aa4ca"}}, {"head": {"id": "e48a761a-21d1-422b-a5e2-7bb8bf2aa4ca", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040657225700, "endTime": 34040836412700}, "additional": {"logType": "info", "children": ["205f2489-8386-4c8e-83e0-7d077f76f978", "7bb1052d-b265-4f35-90dc-1cc92239da64"], "durationId": "64b8c580-df25-40db-ba09-ababca862ce6", "parent": "32627da7-2318-4daa-848e-0a18a59c961d"}}, {"head": {"id": "03b7ce25-9cc7-4a71-b30e-71b8209aec97", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040840568500, "endTime": 34040841095800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ec381db-08f2-49ce-8c80-2b63ff129f3a", "logId": "1b6dc107-1dcb-4ec4-bdf6-f51b17eafdf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b6dc107-1dcb-4ec4-bdf6-f51b17eafdf9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040840568500, "endTime": 34040841095800}, "additional": {"logType": "info", "children": [], "durationId": "03b7ce25-9cc7-4a71-b30e-71b8209aec97", "parent": "32627da7-2318-4daa-848e-0a18a59c961d"}}, {"head": {"id": "32627da7-2318-4daa-848e-0a18a59c961d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040654834000, "endTime": 34040841484900}, "additional": {"logType": "info", "children": ["7cc704d7-009a-4fb0-adc0-2b77b56c8c05", "e48a761a-21d1-422b-a5e2-7bb8bf2aa4ca", "1b6dc107-1dcb-4ec4-bdf6-f51b17eafdf9"], "durationId": "9ec381db-08f2-49ce-8c80-2b63ff129f3a", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "abe18157-0d6b-47bc-8016-12ba5c3e2a78", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040843255700, "endTime": 34040970004900}, "additional": {"children": ["f9d0da98-65cc-4dfe-ab9c-9cc368cc63cf", "5393581a-e57c-4046-bc8c-d31b9ed84f7a", "f32edc02-d6d0-4a4c-88f8-94d763b1def9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7e3c484e-5aa6-403f-9ccf-7f7c024f6414", "logId": "d118f149-2319-48ea-b95b-d9960904e45b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9d0da98-65cc-4dfe-ab9c-9cc368cc63cf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040854439400, "endTime": 34040854462200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abe18157-0d6b-47bc-8016-12ba5c3e2a78", "logId": "da2b012a-e587-4448-8b30-557e47094be3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da2b012a-e587-4448-8b30-557e47094be3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040854439400, "endTime": 34040854462200}, "additional": {"logType": "info", "children": [], "durationId": "f9d0da98-65cc-4dfe-ab9c-9cc368cc63cf", "parent": "d118f149-2319-48ea-b95b-d9960904e45b"}}, {"head": {"id": "5393581a-e57c-4046-bc8c-d31b9ed84f7a", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040868271400, "endTime": 34040965052300}, "additional": {"children": ["d323d9cc-7e07-4dc3-ac40-39d4396a4e7d", "4a0c65df-d154-47f2-b2e4-1db5ec810132"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abe18157-0d6b-47bc-8016-12ba5c3e2a78", "logId": "74c2e55a-b7ae-4c00-85df-c4e981707fa5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d323d9cc-7e07-4dc3-ac40-39d4396a4e7d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040868287700, "endTime": 34040911580300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5393581a-e57c-4046-bc8c-d31b9ed84f7a", "logId": "7cf50da5-b9f3-490e-a0b1-1e5de763ea93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a0c65df-d154-47f2-b2e4-1db5ec810132", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040911698400, "endTime": 34040965033400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5393581a-e57c-4046-bc8c-d31b9ed84f7a", "logId": "f7cd4f8d-e884-4ac0-a978-50762d54b930"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35d36313-4a33-48a0-9462-ede5590e787d", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040868310800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54dc1bfa-50f0-4615-9e41-19ea288465c3", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040903858500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cf50da5-b9f3-490e-a0b1-1e5de763ea93", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040868287700, "endTime": 34040911580300}, "additional": {"logType": "info", "children": [], "durationId": "d323d9cc-7e07-4dc3-ac40-39d4396a4e7d", "parent": "74c2e55a-b7ae-4c00-85df-c4e981707fa5"}}, {"head": {"id": "3df990b9-6844-4f72-8118-dfce370e4300", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040911773600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370664d2-03b6-4171-984e-e37106b7af87", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040958256400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415ca1d8-a970-4671-aa79-93d686604fb1", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040958426000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f83998-a126-4d58-9f3a-d5d5b41195e6", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040958771300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b6e4a4-6ac9-4ed6-a7cd-f7337533b61d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040958973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9993d317-087f-4e98-baf3-6b34226d067e", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040959075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e951c86d-02eb-4dcf-8019-0870c302db07", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040959137000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05b3b821-c5ac-4f7e-ad3c-3d91ebfc504d", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040959211200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a8fbb2-1d6a-443f-aed8-7892045456cf", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040964680200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fd6259b-0fb9-4b40-83a5-da26a46150cb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040964866100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40df99ed-681d-43fc-8665-75b56780e57b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040964931300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7437dd6-0825-46e1-b02f-010cd71d2a0b", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040964984800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7cd4f8d-e884-4ac0-a978-50762d54b930", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040911698400, "endTime": 34040965033400}, "additional": {"logType": "info", "children": [], "durationId": "4a0c65df-d154-47f2-b2e4-1db5ec810132", "parent": "74c2e55a-b7ae-4c00-85df-c4e981707fa5"}}, {"head": {"id": "74c2e55a-b7ae-4c00-85df-c4e981707fa5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040868271400, "endTime": 34040965052300}, "additional": {"logType": "info", "children": ["7cf50da5-b9f3-490e-a0b1-1e5de763ea93", "f7cd4f8d-e884-4ac0-a978-50762d54b930"], "durationId": "5393581a-e57c-4046-bc8c-d31b9ed84f7a", "parent": "d118f149-2319-48ea-b95b-d9960904e45b"}}, {"head": {"id": "f32edc02-d6d0-4a4c-88f8-94d763b1def9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040969968700, "endTime": 34040969989600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "abe18157-0d6b-47bc-8016-12ba5c3e2a78", "logId": "1b0fa565-2d56-41f1-99ae-56d4d49b2dc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b0fa565-2d56-41f1-99ae-56d4d49b2dc7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040969968700, "endTime": 34040969989600}, "additional": {"logType": "info", "children": [], "durationId": "f32edc02-d6d0-4a4c-88f8-94d763b1def9", "parent": "d118f149-2319-48ea-b95b-d9960904e45b"}}, {"head": {"id": "d118f149-2319-48ea-b95b-d9960904e45b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040843255700, "endTime": 34040970004900}, "additional": {"logType": "info", "children": ["da2b012a-e587-4448-8b30-557e47094be3", "74c2e55a-b7ae-4c00-85df-c4e981707fa5", "1b0fa565-2d56-41f1-99ae-56d4d49b2dc7"], "durationId": "abe18157-0d6b-47bc-8016-12ba5c3e2a78", "parent": "9bb9de7b-8b8d-4ce8-a8f3-dfb53bb0d386"}}, {"head": {"id": "9bb9de7b-8b8d-4ce8-a8f3-dfb53bb0d386", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040841743100, "endTime": 34040970015400}, "additional": {"logType": "info", "children": ["d118f149-2319-48ea-b95b-d9960904e45b"], "durationId": "7e3c484e-5aa6-403f-9ccf-7f7c024f6414", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "56a16a5c-f887-4394-857b-ee5c5bd108f7", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041069057700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d1ff7d-dc43-4f0c-b2bc-037fd1b307db", "name": "hvigorfile, resolve hvigorfile dependencies in 199 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041168462700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08cdfedb-4df4-48f1-96ef-f1c92d82be54", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040970026800, "endTime": 34041168611200}, "additional": {"logType": "info", "children": [], "durationId": "e7034f58-16b6-4f40-a7c0-f2b81808a9cd", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "64a4b8e6-be6f-4cfc-a0a0-2ecf5cdeb9bb", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041169643600, "endTime": 34041169969800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "logId": "809271ad-2bed-4e8b-bc61-84d9f083dfe7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96d2079c-2a78-4bc0-8d52-4248c045f39b", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041169672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "809271ad-2bed-4e8b-bc61-84d9f083dfe7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041169643600, "endTime": 34041169969800}, "additional": {"logType": "info", "children": [], "durationId": "64a4b8e6-be6f-4cfc-a0a0-2ecf5cdeb9bb", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "ac8e289a-9616-4297-bf64-e64a5f7c50c5", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041171097200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a5fd458-5ce6-4071-9966-6aaf149f70e8", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041190262000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0601b0c3-d3e9-4904-ac89-0ed40b9df446", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041169985100, "endTime": 34041193223900}, "additional": {"logType": "info", "children": [], "durationId": "100e3920-6828-4087-913f-4b81dfdbc2e4", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "a36bd617-fa7c-4c51-bee6-15462233b0ba", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041198792800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4b8d8a-82be-48da-bf0d-6590d339320f", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041198920200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1afb04f0-bbc5-4967-be3c-da411258cc7e", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041201565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d693509b-4810-4edd-81e6-5623d8573eeb", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041201883300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33b6f9db-f9ae-4c28-9404-d8ffa15f0150", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041193248200, "endTime": 34041211177200}, "additional": {"logType": "info", "children": [], "durationId": "3e687256-0986-4588-a266-5b6db816df58", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "9d82160f-cd77-4157-a376-1727e928e547", "name": "Configuration phase cost:561 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041211231900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0b7d23-c8e4-47c6-a3c0-3a7e4c5acd4e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041211204600, "endTime": 34041211394900}, "additional": {"logType": "info", "children": [], "durationId": "82d2c99e-a358-41d2-b001-f21b15d5da47", "parent": "d87fbdd5-d601-49ed-9a47-fa633a240af4"}}, {"head": {"id": "d87fbdd5-d601-49ed-9a47-fa633a240af4", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040644514700, "endTime": 34041211407600}, "additional": {"logType": "info", "children": ["dcdd4a1f-a293-472e-ba7d-2ddbf9ccc1f1", "370cf206-801e-4f31-a07b-8447c877a0d0", "32627da7-2318-4daa-848e-0a18a59c961d", "9bb9de7b-8b8d-4ce8-a8f3-dfb53bb0d386", "08cdfedb-4df4-48f1-96ef-f1c92d82be54", "0601b0c3-d3e9-4904-ac89-0ed40b9df446", "33b6f9db-f9ae-4c28-9404-d8ffa15f0150", "2a0b7d23-c8e4-47c6-a3c0-3a7e4c5acd4e", "809271ad-2bed-4e8b-bc61-84d9f083dfe7"], "durationId": "d9642126-8d7b-49eb-9fa4-c7a2d0a7ac4b", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "ce4ea51f-003c-46a3-ae06-67466ba02e05", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041216077000, "endTime": 34041216097500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b6799b47-d832-4c96-bada-4531f8269b48", "logId": "d94cdd33-2ad3-42ef-bac6-75aaee024597"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d94cdd33-2ad3-42ef-bac6-75aaee024597", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041216077000, "endTime": 34041216097500}, "additional": {"logType": "info", "children": [], "durationId": "ce4ea51f-003c-46a3-ae06-67466ba02e05", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "8b43c4ae-a26b-4af7-8221-74ab3738db4b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041211432500, "endTime": 34041216117100}, "additional": {"logType": "info", "children": [], "durationId": "465c25b8-7087-426c-af2e-6e06f7bd47c7", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "d04742c5-347b-48a6-91b2-85a8b8c50355", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041216122700, "endTime": 34041216124700}, "additional": {"logType": "info", "children": [], "durationId": "e5eef688-cdc2-45df-ab39-3e16883f0f45", "parent": "61d607b4-d81b-4195-a41d-b073e248d066"}}, {"head": {"id": "61d607b4-d81b-4195-a41d-b073e248d066", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040628908800, "endTime": 34041216131000}, "additional": {"logType": "info", "children": ["fa514ad7-dd60-4093-bcf2-9803607b8992", "d87fbdd5-d601-49ed-9a47-fa633a240af4", "8b43c4ae-a26b-4af7-8221-74ab3738db4b", "d04742c5-347b-48a6-91b2-85a8b8c50355", "e826b076-44ee-4930-8dcf-4ab05eebf857", "de08a47b-48b6-41a1-9425-cf24b3ce7715", "d94cdd33-2ad3-42ef-bac6-75aaee024597"], "durationId": "b6799b47-d832-4c96-bada-4531f8269b48"}}, {"head": {"id": "da8fa962-6e22-43d9-80c8-d8dda67e3eb1", "name": "Configuration task cost before running: 593 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041216756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fabbf4a3-9e24-4b6a-af13-ed581fed2962", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041234983100, "endTime": 34041247177300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9fc119dc-39cb-421e-b987-b0840af0912a", "logId": "ea21808f-9567-492c-adb1-fb9f8da013f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fc119dc-39cb-421e-b987-b0840af0912a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041227522300}, "additional": {"logType": "detail", "children": [], "durationId": "fabbf4a3-9e24-4b6a-af13-ed581fed2962"}}, {"head": {"id": "92a64c79-d2ef-4764-a49c-a263f4263a68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041228110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9237ca86-bc46-4d24-8496-a547e2dda166", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041228956400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598cfddb-07d9-437f-8132-53ac89ed8d83", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041234997300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dfe39c6-1864-4e51-b5dc-1139186fcb91", "name": "Incremental task entry:default@PreBuild pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041246961400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "185fc1e4-d44c-48cb-ab7a-8e11061f3e9d", "name": "entry : default@PreBuild cost memory 0.3112030029296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041247103900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea21808f-9567-492c-adb1-fb9f8da013f7", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041234983100, "endTime": 34041247177300}, "additional": {"logType": "info", "children": [], "durationId": "fabbf4a3-9e24-4b6a-af13-ed581fed2962"}}, {"head": {"id": "36602f02-df66-4484-9c43-3841a88b81cb", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041254148900, "endTime": 34041256962600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1dd0dc61-48e6-4024-87cd-f2cee5e63f54", "logId": "acc6ec44-081a-4f5b-b875-221d5265f556"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dd0dc61-48e6-4024-87cd-f2cee5e63f54", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041252022300}, "additional": {"logType": "detail", "children": [], "durationId": "36602f02-df66-4484-9c43-3841a88b81cb"}}, {"head": {"id": "dd1a7564-cc07-4087-8193-efbf9b17b797", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041252668600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bc1f38-47f3-4371-aa06-03be39459170", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041252786000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2026fdf-688a-47b2-8912-7f096155eac9", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041254169900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afa5004c-d29b-476e-a927-4fb54a6c17a7", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041255238900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66694322-5f0b-4a19-8211-f3f823624b79", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041256752200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "837c7ebb-3cbe-4d91-8419-ecae01532388", "name": "entry : default@GenerateMetadata cost memory 0.09180450439453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041256887600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc6ec44-081a-4f5b-b875-221d5265f556", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041254148900, "endTime": 34041256962600}, "additional": {"logType": "info", "children": [], "durationId": "36602f02-df66-4484-9c43-3841a88b81cb"}}, {"head": {"id": "de3ec70d-e367-4b36-9334-8335ed3947f4", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041263728600, "endTime": 34041264361400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c9bf10a1-66f0-40e2-85f7-5b7e68536f95", "logId": "b7b36d59-349d-4f38-a034-03a757a0b75a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9bf10a1-66f0-40e2-85f7-5b7e68536f95", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041262406400}, "additional": {"logType": "detail", "children": [], "durationId": "de3ec70d-e367-4b36-9334-8335ed3947f4"}}, {"head": {"id": "ea98a555-31f1-4ae2-9d86-e55af5e9c203", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041263267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b4d7e6-2b04-4720-a5bf-484f942f11ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041263391500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01b80943-9fb6-48f0-8522-a0205c17b66b", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041263738600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc547c4e-baef-4143-a484-cc61ff161821", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041263953000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86597ec-d88d-4100-8941-aae888de4bd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041264024300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "143f27c3-2bf6-4827-8835-f6d43d70b561", "name": "entry : default@ConfigureCmake cost memory 0.03619384765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041264190800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e6cc89-58e6-4ede-a635-371c9a591286", "name": "runTaskFromQueue task cost before running: 640 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041264295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7b36d59-349d-4f38-a034-03a757a0b75a", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041263728600, "endTime": 34041264361400, "totalTime": 547000}, "additional": {"logType": "info", "children": [], "durationId": "de3ec70d-e367-4b36-9334-8335ed3947f4"}}, {"head": {"id": "52288a8f-c396-43bb-9d33-3c8910104e2f", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041268086300, "endTime": 34041270763300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "44b14f34-5c22-443e-8ef4-46b049a8ed9f", "logId": "d5be6bff-c3e4-44ea-b52a-bca416a9e597"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44b14f34-5c22-443e-8ef4-46b049a8ed9f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041266602600}, "additional": {"logType": "detail", "children": [], "durationId": "52288a8f-c396-43bb-9d33-3c8910104e2f"}}, {"head": {"id": "5dea03c8-41f4-462f-83e0-09479153ed28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041267015200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea120a22-9a2e-4be3-af30-f51a88df93d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041267211700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a066d28-96e8-4f7b-aa15-0ec8ed0cb549", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041268099100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7192c5a-d0e7-4677-a159-b092542a31d3", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041270225200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440e49a5-caf0-4af6-ae48-f1175cd87b1e", "name": "entry : default@MergeProfile cost memory 0.10509490966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041270363800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5be6bff-c3e4-44ea-b52a-bca416a9e597", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041268086300, "endTime": 34041270763300}, "additional": {"logType": "info", "children": [], "durationId": "52288a8f-c396-43bb-9d33-3c8910104e2f"}}, {"head": {"id": "c8034273-c962-41cc-82f3-50c0f08e8074", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041276696800, "endTime": 34041278778000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "64dca40d-e9c7-464d-9943-9c6045c46fd5", "logId": "6a9c4bf8-38bd-4e33-b5ce-9dbfe7633dab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64dca40d-e9c7-464d-9943-9c6045c46fd5", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041274332500}, "additional": {"logType": "detail", "children": [], "durationId": "c8034273-c962-41cc-82f3-50c0f08e8074"}}, {"head": {"id": "921fbf4c-689b-4535-8f03-3d8d089733a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041275093900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b592d49-4eee-45ba-9396-acb4035f379e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041275238500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4953b8-7efa-4999-a278-7ad1911bf216", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041276710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2709323-e957-4f7b-8c4d-421d123cf40e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041277566600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8faa53b-08d2-4fc4-9854-e963b712ccd0", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041278584400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36950144-3a4d-43e6-90bc-71c488e4a395", "name": "entry : default@CreateBuildProfile cost memory 0.09978485107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041278700800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9c4bf8-38bd-4e33-b5ce-9dbfe7633dab", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041276696800, "endTime": 34041278778000}, "additional": {"logType": "info", "children": [], "durationId": "c8034273-c962-41cc-82f3-50c0f08e8074"}}, {"head": {"id": "10ab66f6-6eac-4fd5-9b85-447c1f3c5567", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041282461100, "endTime": 34041285513500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "14df222d-d4ed-42e9-b4db-8707131b2649", "logId": "1fe1db41-9b26-4874-8363-1352800ac8b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14df222d-d4ed-42e9-b4db-8707131b2649", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041280760300}, "additional": {"logType": "detail", "children": [], "durationId": "10ab66f6-6eac-4fd5-9b85-447c1f3c5567"}}, {"head": {"id": "e5b732de-1132-42de-9138-9770da6d426e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041281118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad049469-a70b-4ef7-aa18-24389571a4c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041281379500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b300959a-6090-4c9d-974a-54c62269ce1e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041282473600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0916d050-9a5d-427c-a92f-f3f54802de4f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041282643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d03a15-cd6f-46b8-8e04-3c8367bfccda", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041282710000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ea9cf7-02cf-48d0-ad21-670c003b4f1d", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041285266200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc38970-105f-4a1e-b791-bf15c4d4be28", "name": "runTaskFromQueue task cost before running: 662 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041285446200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe1db41-9b26-4874-8363-1352800ac8b4", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041282461100, "endTime": 34041285513500, "totalTime": 2955100}, "additional": {"logType": "info", "children": [], "durationId": "10ab66f6-6eac-4fd5-9b85-447c1f3c5567"}}, {"head": {"id": "28cb4434-5253-4705-93bd-3b78f06784f2", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041293817500, "endTime": 34041294667900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e14d6afc-9a56-46b4-b0f7-3d5858d0e69e", "logId": "66d54f2b-91ec-4026-8c21-6bf07ff3581b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e14d6afc-9a56-46b4-b0f7-3d5858d0e69e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041287494000}, "additional": {"logType": "detail", "children": [], "durationId": "28cb4434-5253-4705-93bd-3b78f06784f2"}}, {"head": {"id": "13067100-ee80-4799-8912-e5812aca1c95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041288080100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e84e8a1-162b-41bc-bf15-1b5c4e0270cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041288249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebaf6757-1b26-411f-a641-b39feb4b12c5", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041293836900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2ddc81a-be8b-4f48-a298-54b3f5f68afe", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041294126800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8601de-8b8d-4391-b16d-38c32cacdf06", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03713226318359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041294420600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e782e6-b0a2-401a-bb81-96d66afb05ea", "name": "runTaskFromQueue task cost before running: 671 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041294553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d54f2b-91ec-4026-8c21-6bf07ff3581b", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041293817500, "endTime": 34041294667900, "totalTime": 694200}, "additional": {"logType": "info", "children": [], "durationId": "28cb4434-5253-4705-93bd-3b78f06784f2"}}, {"head": {"id": "42453918-149f-43f4-9c78-8f5a79b30480", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041299059500, "endTime": 34041301333400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "94f737c9-a275-4d3e-8a91-91cf26f830f0", "logId": "165a4279-fad4-44b6-b5df-36ab4dbd953f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94f737c9-a275-4d3e-8a91-91cf26f830f0", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041296737900}, "additional": {"logType": "detail", "children": [], "durationId": "42453918-149f-43f4-9c78-8f5a79b30480"}}, {"head": {"id": "b641198e-7d16-448f-a65e-1a6152e308dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041297183200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3626f68e-1083-4b89-bb05-e215c88aac25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041297301300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35bd3a0-c6cc-494a-adba-cbf2603c7cbd", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041299071600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5765529e-74a1-4025-8094-746a2384f9e4", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041300807900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc27592c-071b-46d2-888b-8b7b2d30cc8d", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041300947500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684078fc-e89a-4941-a466-0a035125c116", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041301032500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69486474-91ef-4a2b-9e96-cf9689dbee6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041301081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac8d9a0-d0ad-4093-842a-d4a013f8d6cc", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1178741455078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041301203200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4cc9426-4f19-4aa8-bcf6-fe0d58ae1419", "name": "runTaskFromQueue task cost before running: 677 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041301280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "165a4279-fad4-44b6-b5df-36ab4dbd953f", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041299059500, "endTime": 34041301333400, "totalTime": 2206600}, "additional": {"logType": "info", "children": [], "durationId": "42453918-149f-43f4-9c78-8f5a79b30480"}}, {"head": {"id": "e2b12c60-7278-46ce-b79b-fa9f0d5ef3c2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305200800, "endTime": 34041306085800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "781fb884-cf1a-48e8-aeb3-86dfed817412", "logId": "90f5ebb7-0820-48fe-bcf0-cfc60c359c42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "781fb884-cf1a-48e8-aeb3-86dfed817412", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041303819500}, "additional": {"logType": "detail", "children": [], "durationId": "e2b12c60-7278-46ce-b79b-fa9f0d5ef3c2"}}, {"head": {"id": "16ba018a-24c5-4020-a6f2-e0b414f803cf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041304295800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ef32a7d-4ba7-412a-a76d-71ad58e04e01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041304418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f1f0f4-c94a-4c9f-9acc-32fbe982ea86", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb61998b-634b-4f61-b91f-0e7e804ed0e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305345100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9094b8-85b8-4fa3-8d65-451a395428ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305400400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9243cf46-1fce-4a24-b3cd-0b17d7072b16", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305469100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbff2446-466b-40dc-83bd-80a8b85f7ed1", "name": "runTaskFromQueue task cost before running: 682 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305655100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f5ebb7-0820-48fe-bcf0-cfc60c359c42", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041305200800, "endTime": 34041306085800, "totalTime": 362700}, "additional": {"logType": "info", "children": [], "durationId": "e2b12c60-7278-46ce-b79b-fa9f0d5ef3c2"}}, {"head": {"id": "a95eaf30-c375-4f65-9c5e-59f054aef5ea", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041310853400, "endTime": 34041314306400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ffc7787a-c240-487a-af63-809462808950", "logId": "1eb04e7b-7dee-4128-b33d-331bce5a0f16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffc7787a-c240-487a-af63-809462808950", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041309524000}, "additional": {"logType": "detail", "children": [], "durationId": "a95eaf30-c375-4f65-9c5e-59f054aef5ea"}}, {"head": {"id": "3a77cf97-bfb5-4a1f-8198-e937701aefe1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041310024900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdb71cf-fa7e-4de2-a3e4-e8590253e37b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041310147900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e947949b-20af-41ad-a39e-bfd9bae85867", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041310863000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76218204-5d2f-41f4-bd44-58d31b16cf24", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041314004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c6e7b5-46ff-4355-895a-e0aa4157d138", "name": "entry : default@MakePackInfo cost memory 0.13858795166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041314175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eb04e7b-7dee-4128-b33d-331bce5a0f16", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041310853400, "endTime": 34041314306400}, "additional": {"logType": "info", "children": [], "durationId": "a95eaf30-c375-4f65-9c5e-59f054aef5ea"}}, {"head": {"id": "7521b76a-6c67-4fb8-bb08-c87641a6eab1", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041319812100, "endTime": 34041327510300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "00d52a6a-cb5d-4538-8168-554c87864355", "logId": "11035159-9db2-4035-8b18-bb3efbfbd427"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00d52a6a-cb5d-4538-8168-554c87864355", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041317550800}, "additional": {"logType": "detail", "children": [], "durationId": "7521b76a-6c67-4fb8-bb08-c87641a6eab1"}}, {"head": {"id": "61842e84-168c-40af-8599-89619aaad969", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041317960500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66c6c05-fa28-4afa-a244-b8cd13c77003", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041318087000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd24aeea-1e90-4324-bceb-b14c3a0b268b", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041319825500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d73e6d79-f009-4972-a5aa-6e1febb1e89e", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041320002600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368b25d4-1bda-4d21-b4e9-b500bba5cfef", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041321884900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f2c3c4-1091-465c-8817-4c03cf967992", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041326651900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64adaa21-a624-41a3-b275-a4f2b0d2083b", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041326815400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00369297-fa6b-4b82-8c63-d9b40e063314", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041326910000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4683253a-57be-48e3-919b-41b6938e9b4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041326965100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4339fad6-6589-4730-8c96-20f846daf721", "name": "entry : default@SyscapTransform cost memory 0.16979217529296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041327041900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f28d742-88e0-4ab6-8ab5-7694d1d1ac1d", "name": "runTaskFromQueue task cost before running: 703 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041327309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11035159-9db2-4035-8b18-bb3efbfbd427", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041319812100, "endTime": 34041327510300, "totalTime": 7288700}, "additional": {"logType": "info", "children": [], "durationId": "7521b76a-6c67-4fb8-bb08-c87641a6eab1"}}, {"head": {"id": "20ba4369-ef56-4f26-ba0c-69a19888824e", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041334037200, "endTime": 34041335979600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a542a690-d766-453e-bf98-5e441a352789", "logId": "deecfb98-5353-40f2-9474-9e80998eea46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a542a690-d766-453e-bf98-5e441a352789", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041331079300}, "additional": {"logType": "detail", "children": [], "durationId": "20ba4369-ef56-4f26-ba0c-69a19888824e"}}, {"head": {"id": "0e516728-1b47-4cfb-8ac9-4b674626de7b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041331649900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07119254-2787-4639-b9d6-13616c3f5d33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041331775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44e8039-7fb0-414e-9f8e-8d0c566a3165", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041334049000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b2d831-1eae-46e7-8bc9-ee9f2016ddd8", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041335581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe575898-3ed0-4a8a-ac98-6648df1d26f1", "name": "entry : default@ProcessProfile cost memory 0.060272216796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041335816400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deecfb98-5353-40f2-9474-9e80998eea46", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041334037200, "endTime": 34041335979600}, "additional": {"logType": "info", "children": [], "durationId": "20ba4369-ef56-4f26-ba0c-69a19888824e"}}, {"head": {"id": "51307908-83d1-4966-9eab-26054d2883be", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041343130100, "endTime": 34041347861800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "52311a22-4d5a-4f4b-9c62-5ab6eaa744cf", "logId": "a53d7328-1088-45eb-8d5b-58e74af719ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52311a22-4d5a-4f4b-9c62-5ab6eaa744cf", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041337871100}, "additional": {"logType": "detail", "children": [], "durationId": "51307908-83d1-4966-9eab-26054d2883be"}}, {"head": {"id": "c148ffdd-7404-48df-b8bd-bb11b0734503", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041338280300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedd3281-5623-4988-b822-0129ebd2960c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041338391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85defe82-922c-411d-b34a-c612c2b4688b", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041343146500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2da1d89c-0869-4394-b54c-6117cf4ecb5e", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041347574600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd69ea5-044f-4650-8046-7f57cfe0d00e", "name": "entry : default@ProcessRouterMap cost memory 0.20145416259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041347778000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53d7328-1088-45eb-8d5b-58e74af719ff", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041343130100, "endTime": 34041347861800}, "additional": {"logType": "info", "children": [], "durationId": "51307908-83d1-4966-9eab-26054d2883be"}}, {"head": {"id": "7728d95a-8739-43dc-93e9-112c349e2598", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041354559700, "endTime": 34041356485600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "24904cc9-c27e-4494-9c86-d51dc12fc713", "logId": "f5101f40-817c-44a7-a14e-62797b717ac9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24904cc9-c27e-4494-9c86-d51dc12fc713", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041352403900}, "additional": {"logType": "detail", "children": [], "durationId": "7728d95a-8739-43dc-93e9-112c349e2598"}}, {"head": {"id": "0f82aeab-c899-4258-89b1-f4588cba1681", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041353010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a95a61-8e54-4dbd-af71-57c800d3856c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041353210100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b057ec1e-0c56-48c4-8967-7f5792eb62e6", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041354572800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2a0a2d2-f3fc-4d1f-a5ca-72cb995459ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041354778300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8686d412-0635-402e-8930-e938bfba7cec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041354866800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961c0f6a-2618-43f1-9aab-c1634db98d6a", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041356252600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb27e049-b683-4ff8-881d-c59424695475", "name": "runTaskFromQueue task cost before running: 732 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041356415500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5101f40-817c-44a7-a14e-62797b717ac9", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041354559700, "endTime": 34041356485600, "totalTime": 1829100}, "additional": {"logType": "info", "children": [], "durationId": "7728d95a-8739-43dc-93e9-112c349e2598"}}, {"head": {"id": "7a0af512-9195-45e4-b657-da61546eb63e", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041362888100, "endTime": 34041368518100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "a98f7c5a-b59c-4551-8eef-6ca19d78b875", "logId": "51870cd0-24ff-4df0-832a-3a75a84a822f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a98f7c5a-b59c-4551-8eef-6ca19d78b875", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041360019300}, "additional": {"logType": "detail", "children": [], "durationId": "7a0af512-9195-45e4-b657-da61546eb63e"}}, {"head": {"id": "f7dac46e-6877-41e1-8660-b48f3c4cf3b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041360420800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33190855-ee88-49a5-947e-12b35f2733de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041360528500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1c8e7df-2a93-4224-8d03-3d5e27806fb2", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041361295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06218228-edec-4bb3-93fc-ca5712fd6804", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041364402300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d192969-7b81-44ca-b52d-3f3bdd931979", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041366941300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d45d9626-cf3b-40ac-930c-1d9805e072d5", "name": "entry : default@ProcessResource cost memory 0.16959381103515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041367082000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51870cd0-24ff-4df0-832a-3a75a84a822f", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041362888100, "endTime": 34041368518100}, "additional": {"logType": "info", "children": [], "durationId": "7a0af512-9195-45e4-b657-da61546eb63e"}}, {"head": {"id": "c3593757-8b3a-4008-ab1e-12488cffacee", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041377425200, "endTime": 34041393071900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "72bd3c77-0929-4886-868e-d5bae674791a", "logId": "0bc40cc1-b7a6-4fba-a97b-d4c0b170165d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72bd3c77-0929-4886-868e-d5bae674791a", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041371814100}, "additional": {"logType": "detail", "children": [], "durationId": "c3593757-8b3a-4008-ab1e-12488cffacee"}}, {"head": {"id": "939c28f2-ab9d-437a-bea5-1deb4f37c4a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041372255100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c7c21e-aee0-481d-ba55-af57167ad71e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041372385900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e44d74-648f-48bb-848c-14ca73d2eab5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041377438700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e441a1c1-c3f9-4883-bf84-7c2ed41c72e4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041392848200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bfc8bc2-5b6c-4f87-a80b-c86fdf8656f9", "name": "entry : default@GenerateLoaderJson cost memory 0.7635955810546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041392999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc40cc1-b7a6-4fba-a97b-d4c0b170165d", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041377425200, "endTime": 34041393071900}, "additional": {"logType": "info", "children": [], "durationId": "c3593757-8b3a-4008-ab1e-12488cffacee"}}, {"head": {"id": "33fbc0d7-ffbe-4f96-b2e4-3de690b997f3", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041402364300, "endTime": 34041405726900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "16ff30d1-f44d-4469-9b7c-2fb58e8ead17", "logId": "0f03aea0-07bc-4e7e-ba2f-5540996f274f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16ff30d1-f44d-4469-9b7c-2fb58e8ead17", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041401193300}, "additional": {"logType": "detail", "children": [], "durationId": "33fbc0d7-ffbe-4f96-b2e4-3de690b997f3"}}, {"head": {"id": "89246632-bdf6-4166-ac2a-c20de7b5f20c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041401523400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c9c3d3b-e2bf-4741-8c9f-35f9f003d30a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041401669500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6c68997-efb8-46eb-9980-399fb67e2afa", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041402382200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe2c72f4-6e63-4281-b80c-f4ad0173d9f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041404503700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85eac7c7-d963-4e6d-8f1e-6a59c32aa1fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041404620200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba263a46-ba44-4abf-8386-0d00a996fa62", "name": "entry : default@ProcessLibs cost memory 0.129058837890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041405339100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "644db305-0d9c-45d5-8553-4ff32981a636", "name": "runTaskFromQueue task cost before running: 782 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041405623100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f03aea0-07bc-4e7e-ba2f-5540996f274f", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041402364300, "endTime": 34041405726900, "totalTime": 3091100}, "additional": {"logType": "info", "children": [], "durationId": "33fbc0d7-ffbe-4f96-b2e4-3de690b997f3"}}, {"head": {"id": "2e8ba4a9-579a-4697-9c17-c5d195483d05", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041415421100, "endTime": 34041437174600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "354c8676-1fe7-45d8-8192-27a8a07651fe", "logId": "a0702757-b4a4-46a7-ba91-dc1faf12f079"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "354c8676-1fe7-45d8-8192-27a8a07651fe", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041409448000}, "additional": {"logType": "detail", "children": [], "durationId": "2e8ba4a9-579a-4697-9c17-c5d195483d05"}}, {"head": {"id": "c274ac48-9b82-4fd9-8134-bb8a28ef8816", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041409852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e05c9e-83d9-4ca5-9ae3-a7a01a28c131", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041409964900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a4ffefd-6434-4874-ad18-08617fe0a54f", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041412794100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80899ac0-8886-4466-82c8-0acd4cb03fdd", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041415446500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7081ebbc-37a2-4c04-8d96-f635c3a47eab", "name": "Incremental task entry:default@CompileResource pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041436956300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc9cb2c-dc6a-4ff5-bb10-32bd37d6cb92", "name": "entry : default@CompileResource cost memory 1.40740966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041437082000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0702757-b4a4-46a7-ba91-dc1faf12f079", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041415421100, "endTime": 34041437174600}, "additional": {"logType": "info", "children": [], "durationId": "2e8ba4a9-579a-4697-9c17-c5d195483d05"}}, {"head": {"id": "f20ee197-76ee-4c72-89fd-cacbfaad7838", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041443929700, "endTime": 34041444963700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "dab1141e-99b6-432a-920f-1db1602063c3", "logId": "922f31ef-9aa6-46ca-8c86-293629f492dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dab1141e-99b6-432a-920f-1db1602063c3", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041440267400}, "additional": {"logType": "detail", "children": [], "durationId": "f20ee197-76ee-4c72-89fd-cacbfaad7838"}}, {"head": {"id": "1c300b17-8ac8-4953-9db9-765fe3d57b56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041441339300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d0b55d-9aad-4ef4-a46f-32387046b008", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041441460600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a50623-a576-4389-b5be-f7c0a2974b24", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041443940100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "522519d4-b435-4119-95b1-02081a67323e", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041444167800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e880b8d-e7be-43a5-b564-27a291a0bbc8", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041444826900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca1b3b39-e2cd-4630-b4fb-43a5ee3101f7", "name": "entry : default@DoNativeStrip cost memory 0.07367706298828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041444903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922f31ef-9aa6-46ca-8c86-293629f492dc", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041443929700, "endTime": 34041444963700}, "additional": {"logType": "info", "children": [], "durationId": "f20ee197-76ee-4c72-89fd-cacbfaad7838"}}, {"head": {"id": "f411b948-a357-4725-b616-d1b53acd2d0e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041450543800, "endTime": 34041465429100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "bc8b282e-7d49-42d9-b80a-78dd37c043af", "logId": "5e800181-51ab-4c0c-801b-77bb6a6fe8ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc8b282e-7d49-42d9-b80a-78dd37c043af", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041446434200}, "additional": {"logType": "detail", "children": [], "durationId": "f411b948-a357-4725-b616-d1b53acd2d0e"}}, {"head": {"id": "1a917d21-e561-4d36-8e9f-68a59e23712b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041446956300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e152c604-00d8-4419-8df5-879f18758f3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041447052700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0cabbf-e95b-44bf-95b3-223ae53a3d4b", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041450555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9209f628-7823-48e5-84c3-2902fa8bc743", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041465222500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bacd2cf-828f-4a11-8681-47bef2b382a2", "name": "entry : default@CompileArkTS cost memory 0.6778793334960938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041465357000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e800181-51ab-4c0c-801b-77bb6a6fe8ba", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041450543800, "endTime": 34041465429100}, "additional": {"logType": "info", "children": [], "durationId": "f411b948-a357-4725-b616-d1b53acd2d0e"}}, {"head": {"id": "9d60ef56-7da9-431e-b6e0-a8b4f65df8ab", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041473332800, "endTime": 34041477629900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "911779ee-f0a5-46a9-9508-8fb1f8d83ca0", "logId": "4ee6a55c-5032-49ec-9b54-e0d643979509"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "911779ee-f0a5-46a9-9508-8fb1f8d83ca0", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041469044600}, "additional": {"logType": "detail", "children": [], "durationId": "9d60ef56-7da9-431e-b6e0-a8b4f65df8ab"}}, {"head": {"id": "0efce3e5-147d-4415-bafb-b70420ce51d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041469429300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8288b2-836a-41d8-8090-7fd0a7768581", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041469523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd10a62-d58a-4906-92be-e7c63ce45146", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041473344600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb65531-530f-44ff-8321-42d0323241dc", "name": "entry : default@BuildJS cost memory 0.12703704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041477433900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e13067-fcd4-44a4-bd00-7299ac0be416", "name": "runTaskFromQueue task cost before running: 854 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041477565400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ee6a55c-5032-49ec-9b54-e0d643979509", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041473332800, "endTime": 34041477629900, "totalTime": 4214000}, "additional": {"logType": "info", "children": [], "durationId": "9d60ef56-7da9-431e-b6e0-a8b4f65df8ab"}}, {"head": {"id": "88bdf83c-c363-499d-bb04-ba8e28968d79", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041482078200, "endTime": 34041483865400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eed7ff0f-eac4-448e-88a2-6481c5d5514e", "logId": "dafa0541-0a34-41f9-a931-880c7f6fd7cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eed7ff0f-eac4-448e-88a2-6481c5d5514e", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041479273000}, "additional": {"logType": "detail", "children": [], "durationId": "88bdf83c-c363-499d-bb04-ba8e28968d79"}}, {"head": {"id": "bef5584f-0d2e-4df0-a78c-8df4311fc0be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041479709400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b844b9fa-e76b-4721-bdda-dcf00ab62069", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041479817800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d600e08b-b5b2-4b8a-882e-1df478384c17", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041482088900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a392dab-6876-43bf-b642-8dad916f90c6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041482529500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1af74e1-c698-4172-af8c-300979fdf560", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041483688400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37fd3136-1767-437e-925c-4293ab82394e", "name": "entry : default@CacheNativeLibs cost memory 0.08740997314453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041483796300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dafa0541-0a34-41f9-a931-880c7f6fd7cc", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041482078200, "endTime": 34041483865400}, "additional": {"logType": "info", "children": [], "durationId": "88bdf83c-c363-499d-bb04-ba8e28968d79"}}, {"head": {"id": "8afec7d2-1348-4ef0-8f0c-adae6c083f84", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041486537500, "endTime": 34041487616200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "0c0db674-5dc2-45fa-9bf2-fc23a064a453", "logId": "bf6c213e-78e6-43e4-af3a-ab0f716216f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c0db674-5dc2-45fa-9bf2-fc23a064a453", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041485314000}, "additional": {"logType": "detail", "children": [], "durationId": "8afec7d2-1348-4ef0-8f0c-adae6c083f84"}}, {"head": {"id": "484675f1-49d5-4ffa-8906-898ed56f219b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041485637600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3559acdf-54b0-405f-9977-c4cad1299fc1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041485724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1107dd04-8878-497c-ac04-6c63f93cf2a0", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041486545500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2598d943-2776-45cc-b677-61952a7071d9", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041486744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c6d4b4a-9f05-4fc7-acc6-c3bc705e476c", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041487463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d22a486b-6885-4161-a292-de0c180b5c3f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0706939697265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041487549700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf6c213e-78e6-43e4-af3a-ab0f716216f3", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041486537500, "endTime": 34041487616200}, "additional": {"logType": "info", "children": [], "durationId": "8afec7d2-1348-4ef0-8f0c-adae6c083f84"}}, {"head": {"id": "ee738843-dd47-4ed3-bdb0-0d9a08b0892b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041498364800, "endTime": 34041512265300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "4388fc37-29b0-4d2a-ae70-591c447b9312", "logId": "d26f9dba-cfb4-45bf-8efe-280d60d877c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4388fc37-29b0-4d2a-ae70-591c447b9312", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041490882200}, "additional": {"logType": "detail", "children": [], "durationId": "ee738843-dd47-4ed3-bdb0-0d9a08b0892b"}}, {"head": {"id": "dac699f7-aa7c-426d-8abd-f1564e72f365", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041491393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59804402-d3ab-4261-95a2-35d78826bbeb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041491508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f5c30cf-9a3a-4547-b5d9-12720c2ca362", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041498375400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6063cdb-d933-4155-b835-d7faf26fceda", "name": "Incremental task entry:default@PackageHap pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041511974200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b4b9486-cb4b-4b70-861a-93b6c26332e2", "name": "entry : default@PackageHap cost memory 0.8387832641601562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041512149500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26f9dba-cfb4-45bf-8efe-280d60d877c8", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041498364800, "endTime": 34041512265300}, "additional": {"logType": "info", "children": [], "durationId": "ee738843-dd47-4ed3-bdb0-0d9a08b0892b"}}, {"head": {"id": "5e3d3bad-a789-4117-809b-18b1921a9bff", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********1600, "endTime": 34041518757400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "129faf85-268e-4da7-a307-375724633a0e", "logId": "aa4eee0f-c510-4f90-ad4b-6c828c8946bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "129faf85-268e-4da7-a307-375724633a0e", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041515057000}, "additional": {"logType": "detail", "children": [], "durationId": "5e3d3bad-a789-4117-809b-18b1921a9bff"}}, {"head": {"id": "f2f6d54b-964e-4a10-9912-189ad93abbf2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041515412300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e22231f-7bc4-48f5-8398-a86dc63fdf42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041515499400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb85b561-e2d2-466e-8d3c-f3615f9e0f1c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041517370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b347f7c3-290c-4e5e-94e6-aee6661685bf", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041517649400}, "additional": {"logType": "warn", "children": [], "durationId": "5e3d3bad-a789-4117-809b-18b1921a9bff"}}, {"head": {"id": "c68d1e91-aa7b-4a60-9e1b-785db255c3fa", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041518219900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90714109-23cf-42b8-9f3d-6ced486830a6", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041518308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01966ed7-bee5-4337-8293-ddce99af7785", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041518380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd57b8a-eba1-4e15-901d-f836db83421c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041518432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aacd3893-16ea-45d1-873f-0391ffc576cc", "name": "entry : default@SignHap cost memory 0.32904815673828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041518589300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7576cad-7200-4d79-b43b-358fdcb8fc60", "name": "runTaskFromQueue task cost before running: 895 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041518695900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4eee0f-c510-4f90-ad4b-6c828c8946bb", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********1600, "endTime": 34041518757400, "totalTime": 1317000}, "additional": {"logType": "info", "children": [], "durationId": "5e3d3bad-a789-4117-809b-18b1921a9bff"}}, {"head": {"id": "918b5063-8328-4685-b6e8-d3f2a4e2dcaa", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041521518000, "endTime": 34041527047300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "df73aeb7-e58d-4b78-882b-47dcf786de79", "logId": "63e8c55d-8487-4549-8983-8bf06ff62bf0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df73aeb7-e58d-4b78-882b-47dcf786de79", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041520238700}, "additional": {"logType": "detail", "children": [], "durationId": "918b5063-8328-4685-b6e8-d3f2a4e2dcaa"}}, {"head": {"id": "e483d93e-cadf-46fd-973f-b81229276109", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041520718800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e498e35d-683f-48f9-9b03-e88cadf9cea8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041520810300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8ff2c6-e461-4ed1-9f6c-7844c94a3945", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041521527200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fa12c75-615c-45da-97cb-2b259ea58074", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041526730500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d007cf1-e84e-4c74-a9cd-0f84d8ebf936", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041526841200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce109b47-cae8-4f79-8ddb-72336d3a091d", "name": "entry : default@CollectDebugSymbol cost memory 0.23957061767578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041526914800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97776285-d124-4bcd-8ecc-b2eb7e8888cf", "name": "runTaskFromQueue task cost before running: 903 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041526990100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e8c55d-8487-4549-8983-8bf06ff62bf0", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041521518000, "endTime": 34041527047300, "totalTime": 5454200}, "additional": {"logType": "info", "children": [], "durationId": "918b5063-8328-4685-b6e8-d3f2a4e2dcaa"}}, {"head": {"id": "5a2fef1a-5ee3-4cc0-bada-daf990bf8fca", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041528662400, "endTime": 34041528945700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "6c5b622e-00ca-4bd9-8000-a461d9c35fe6", "logId": "2c502658-2485-47a4-b059-b405c70dfbf2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c5b622e-00ca-4bd9-8000-a461d9c35fe6", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041528622500}, "additional": {"logType": "detail", "children": [], "durationId": "5a2fef1a-5ee3-4cc0-bada-daf990bf8fca"}}, {"head": {"id": "030dd4e6-5982-47d5-a03c-6bfa9<PERSON>da90", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041528668200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2a696bc-52e3-4f6f-aebf-8d2bba260456", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041528775800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c012ed-f192-41b6-92b2-377523bdb561", "name": "runTaskFromQueue task cost before running: 905 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041528855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c502658-2485-47a4-b059-b405c70dfbf2", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041528662400, "endTime": 34041528945700, "totalTime": 177800}, "additional": {"logType": "info", "children": [], "durationId": "5a2fef1a-5ee3-4cc0-bada-daf990bf8fca"}}, {"head": {"id": "c1b6558a-5bfa-4205-994e-97903bfa9f71", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536093100, "endTime": 34041536115600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc40d19b-f18d-4c78-a7c5-11ecfd85f1e6", "logId": "ec43ed4f-39d2-4bfc-952c-dd51b691a248"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec43ed4f-39d2-4bfc-952c-dd51b691a248", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536093100, "endTime": 34041536115600}, "additional": {"logType": "info", "children": [], "durationId": "c1b6558a-5bfa-4205-994e-97903bfa9f71"}}, {"head": {"id": "fbe4d006-4961-4acf-a434-c44886a20fe9", "name": "BUILD SUCCESSFUL in 912 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536152000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "c2a80faa-a29d-4e94-9019-325255220550", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34040624405700, "endTime": 34041536393400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "0db23e01-55f6-4c42-bd41-50a912037b41", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccecd9bb-e225-48a5-bc2b-daef801df1e9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536481400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5539ed66-3294-403b-a678-0da2eed03721", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536527200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd914104-5925-4539-bd51-c0d5a822c3ec", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536567100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d34b129b-3cc7-4c53-bb82-bf85620e9c14", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536636200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91a06346-cfdd-49dc-a24b-157946efffc9", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041536933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e4176e-36ea-4a22-a0f5-fe02ffdc0a3a", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041537520400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe19469-7e1e-431e-ac26-bf625e8630f7", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041537759100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf32b7ba-8fcd-4bc5-8c39-a7c62f565dba", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041537826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4223340-4fd1-41de-8050-17edc17be4d7", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041537883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "556aa506-dd4c-4d7f-88f2-aa811cef061f", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041538191600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4399261c-ac1a-4e3f-8c41-b8de0f04f136", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041539175100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65870cd5-f51f-4bb7-aad1-8d5f8e15fc67", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041539467900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8e805a-cb3e-447d-b93e-42ee4fbd5a13", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041539535500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ac1781-97c8-4f44-9f5e-12b21182da9b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041540469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6190eee6-258c-4629-a7dd-a4301bf5a4af", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041540538400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c7da3d8-b5c2-4d0d-bba9-cf0b56edc85a", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041540585700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ad9389e-8ef0-49d2-a01b-b9286ca99cbe", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041541105200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fab127f-bac8-4f6b-addf-7eddab4c2110", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041541411400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9436e0b-e314-4392-b0c1-c9c538400007", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041541628100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af450f60-a211-4004-bf21-f8b3c300772d", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041541903600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae0c3f0-1616-4cd7-ae6b-c2640fc3db21", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041541965500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d8fb102-6688-446a-b48e-32e94a726b3e", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041542020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be54202a-df13-4e83-9baa-2b6616e568e9", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041542066000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c0a9aef-7c40-42f7-9d16-d8b990f42393", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041543088100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9617f0c1-be64-4f9f-b8e2-ad9006951bbb", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041543518600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcfcc00f-fd15-4205-b5a4-8a0f1a8cbcd6", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041544176400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851bb96e-9818-4549-97ed-3bd3e88f9a29", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041544355200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eca00ec-da43-45de-9bae-77450043f163", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041544520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f60e825-39cc-453a-aaed-b35f055298dc", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c89561bc-16e3-405f-b890-c10f172dc8e1", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545249900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81443101-0b61-45dd-8651-d200c3654fdc", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545313500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed5d78bb-f4fd-4b30-84d2-e6aedc11a5e1", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6754edc5-2ff5-4834-89ca-172feb3cd063", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545403400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70a3cb35-efbc-4614-a21e-e0b0e38921ef", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545632400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92162597-a87e-47de-b48a-8313466c9257", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041545851500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0279c75a-f310-4845-908e-4a7fe59c5c47", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041546059600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b19aa1-ecb9-457c-999d-42cc9968470d", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041548012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a7158f6-3e5a-411b-91fa-b01ea6c0bdf3", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041548226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece39100-6a8b-414b-98b2-fc6e5c614bb1", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041548444200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9200ff4d-0849-4473-a9cf-e49b489b1406", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34041548635500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}