{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "c3805d0c-ed11-4590-ac7c-eb7a0de6494c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760246420900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60d527da-de88-4476-867c-fcc8dc1112c2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760256598800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf0de8e3-52d4-4f6a-8845-c7c63e18cd62", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760256980700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2cb67b3-01e5-460d-a5d0-53a88cf36068", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31760268487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11b84331-fcfa-48c8-a0ea-9ab889bc6cba", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830534930600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830542385000, "endTime": 31830768140600}, "additional": {"children": ["4d04571c-1556-48d7-bfc5-35a72092d36f", "3488c390-efa5-4985-aecd-7b3475581a07", "bd4c5d43-6856-4249-aae1-6fa63ac33630", "1257be35-0a47-45b1-98f8-4d5299525f63", "bbb14779-b67a-4080-9abb-5ef32f95f55b", "1d1266e8-01a6-4852-ba68-61740d132d3c", "3904661d-2024-44bb-ae18-f93cba1054c3"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d04571c-1556-48d7-bfc5-35a72092d36f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830542387000, "endTime": 31830555922700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "796c3e45-1e7b-46b9-a89d-596e9c8d2ddf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3488c390-efa5-4985-aecd-7b3475581a07", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830555939600, "endTime": 31830767086700}, "additional": {"children": ["3b74fa37-1f97-4b14-8192-08ca5d206180", "91a5cb34-6d49-4c34-a46e-00fdd48d065e", "c2e58034-ddb5-408d-b60a-03a9e4a6fe25", "b3b28630-a3e4-40d7-addf-d94d791ede70", "ac406809-ddb5-40ee-96f2-9ccd4e6f8742", "9469f067-b8a1-4029-9908-26d79f62c699", "ade1c18c-e7ef-4ea3-a4bd-c9eafafe90bb", "0d88c11d-4d1d-4130-9c32-0e8be9106c53", "e6b46510-2cdc-4284-ac1f-8e100ec7d7e7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "ef004b6c-6948-4e19-b3aa-cf849b193615"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd4c5d43-6856-4249-aae1-6fa63ac33630", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830767110200, "endTime": 31830768131500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "a63a1910-381d-4cb7-9c72-b54567c44b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1257be35-0a47-45b1-98f8-4d5299525f63", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830768135300, "endTime": 31830768136000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "77699d78-522b-4dad-a2f1-67bf5cc996b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbb14779-b67a-4080-9abb-5ef32f95f55b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830545904100, "endTime": 31830545991800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "db1a6a3b-c7d2-4a14-89a3-48d314e2f4fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db1a6a3b-c7d2-4a14-89a3-48d314e2f4fb", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830545904100, "endTime": 31830545991800}, "additional": {"logType": "info", "children": [], "durationId": "bbb14779-b67a-4080-9abb-5ef32f95f55b", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "1d1266e8-01a6-4852-ba68-61740d132d3c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830551007100, "endTime": 31830551024000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "06e833de-b2a9-4b29-82b6-165dc7c4baa3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06e833de-b2a9-4b29-82b6-165dc7c4baa3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830551007100, "endTime": 31830551024000}, "additional": {"logType": "info", "children": [], "durationId": "1d1266e8-01a6-4852-ba68-61740d132d3c", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "85a4b151-1690-4634-9cfe-9fea5d346605", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830551076800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70484f3e-57b2-4b40-b14b-496eca8cb336", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830555794100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796c3e45-1e7b-46b9-a89d-596e9c8d2ddf", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830542387000, "endTime": 31830555922700}, "additional": {"logType": "info", "children": [], "durationId": "4d04571c-1556-48d7-bfc5-35a72092d36f", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "3b74fa37-1f97-4b14-8192-08ca5d206180", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830561461100, "endTime": 31830561469000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "80e349f9-35f6-48b0-8e72-4262d3ee22d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91a5cb34-6d49-4c34-a46e-00fdd48d065e", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830561485700, "endTime": 31830566070600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "c4d05629-a7f0-4930-a2f3-4db9dac7d1b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2e58034-ddb5-408d-b60a-03a9e4a6fe25", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830566087800, "endTime": 31830654032300}, "additional": {"children": ["fd6efb02-287a-4edf-93e5-9b9c3fc4f66e", "b83839de-986f-4faa-a10e-6d7d48e8efef", "2438d0c1-8bc4-4f65-8a8f-0635f119b672"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "c675b5ff-e256-4dfc-88cc-974b3cb6e962"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3b28630-a3e4-40d7-addf-d94d791ede70", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830654045700, "endTime": 31830673346200}, "additional": {"children": ["c74890f0-83b6-4d0a-85a7-c8c7c974cf2e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "14efd99a-8dfd-4b50-a19e-19a1c4a2822c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac406809-ddb5-40ee-96f2-9ccd4e6f8742", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830673354600, "endTime": 31830742512500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "d9973c43-8bb2-4fdf-83a0-c0426efe903f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9469f067-b8a1-4029-9908-26d79f62c699", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830743425100, "endTime": 31830758694600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "3a65fe13-b963-460e-92b5-61050640d966"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ade1c18c-e7ef-4ea3-a4bd-c9eafafe90bb", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830758714100, "endTime": 31830766937900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "ade0966d-ed77-4169-ac4a-49741d1e48e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d88c11d-4d1d-4130-9c32-0e8be9106c53", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830766954200, "endTime": 31830767077900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "5a8f040a-01b9-4180-8c17-8fb049d495c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80e349f9-35f6-48b0-8e72-4262d3ee22d9", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830561461100, "endTime": 31830561469000}, "additional": {"logType": "info", "children": [], "durationId": "3b74fa37-1f97-4b14-8192-08ca5d206180", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "c4d05629-a7f0-4930-a2f3-4db9dac7d1b9", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830561485700, "endTime": 31830566070600}, "additional": {"logType": "info", "children": [], "durationId": "91a5cb34-6d49-4c34-a46e-00fdd48d065e", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "fd6efb02-287a-4edf-93e5-9b9c3fc4f66e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830566810200, "endTime": 31830566827600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2e58034-ddb5-408d-b60a-03a9e4a6fe25", "logId": "1320a6c1-49bf-4cf8-87e7-3f4456598345"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1320a6c1-49bf-4cf8-87e7-3f4456598345", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830566810200, "endTime": 31830566827600}, "additional": {"logType": "info", "children": [], "durationId": "fd6efb02-287a-4edf-93e5-9b9c3fc4f66e", "parent": "c675b5ff-e256-4dfc-88cc-974b3cb6e962"}}, {"head": {"id": "b83839de-986f-4faa-a10e-6d7d48e8efef", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830568422300, "endTime": 31830653405300}, "additional": {"children": ["aced196f-aa4e-438d-9de2-dfb5165b1e5c", "1efd006f-2e4b-4eb1-9b03-751f8f1f8735"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2e58034-ddb5-408d-b60a-03a9e4a6fe25", "logId": "a22f7eac-50d0-461f-95c6-3c35722a9bf3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aced196f-aa4e-438d-9de2-dfb5165b1e5c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830568423300, "endTime": 31830574994900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b83839de-986f-4faa-a10e-6d7d48e8efef", "logId": "f42a0c2e-edc1-41f6-a668-137341b00d7b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1efd006f-2e4b-4eb1-9b03-751f8f1f8735", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830575008700, "endTime": 31830653391700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b83839de-986f-4faa-a10e-6d7d48e8efef", "logId": "c3992201-febe-4141-a260-c5ab3c166397"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c26b30fd-93b5-428c-a3b6-28363bb18998", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830568426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97a2a1f3-0f50-4329-9bed-9565e74d8533", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830574876900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f42a0c2e-edc1-41f6-a668-137341b00d7b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830568423300, "endTime": 31830574994900}, "additional": {"logType": "info", "children": [], "durationId": "aced196f-aa4e-438d-9de2-dfb5165b1e5c", "parent": "a22f7eac-50d0-461f-95c6-3c35722a9bf3"}}, {"head": {"id": "fe7859c1-d815-4c73-b0cc-8b00333b6bd3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830575020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bac1edfc-f6b5-4dd1-9b0c-a873f49fbe22", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830581276100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4cf1225-40b3-46c9-92ad-a6c099705ba1", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830581392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432f7e18-f783-4f40-b503-f94ae6e9b07b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830581520500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "894596b4-dd93-4aa8-90cc-3aaf5f89d776", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830581613100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14524b58-8324-4706-b9de-cf311f40a952", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830584014500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918c36e9-63ca-4011-8fda-ec8490174a7a", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830588580400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0bea634-3c9e-4af6-8d6c-ac731ad9e814", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830599519100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acade8d9-df69-4a35-a485-ba803f7daa90", "name": "Sdk init in 45 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830633672400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a2cf3b-ca5c-4ca8-a3ea-5033f22afa1d", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830633804300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 19}, "markType": "other"}}, {"head": {"id": "71eb0062-a4f3-4ae9-bf53-85b0a140edde", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830633857000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 19}, "markType": "other"}}, {"head": {"id": "c0383268-3bc3-4c22-97b0-c37538988bfa", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830652959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d07e075-891f-4376-9229-362977dd48dd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830653062200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5779ce2b-3f06-42c9-8801-962060671a6b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830653118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f2c20f-21df-4535-9001-dcffa6c332ce", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830653309700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3992201-febe-4141-a260-c5ab3c166397", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830575008700, "endTime": 31830653391700}, "additional": {"logType": "info", "children": [], "durationId": "1efd006f-2e4b-4eb1-9b03-751f8f1f8735", "parent": "a22f7eac-50d0-461f-95c6-3c35722a9bf3"}}, {"head": {"id": "a22f7eac-50d0-461f-95c6-3c35722a9bf3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830568422300, "endTime": 31830653405300}, "additional": {"logType": "info", "children": ["f42a0c2e-edc1-41f6-a668-137341b00d7b", "c3992201-febe-4141-a260-c5ab3c166397"], "durationId": "b83839de-986f-4faa-a10e-6d7d48e8efef", "parent": "c675b5ff-e256-4dfc-88cc-974b3cb6e962"}}, {"head": {"id": "2438d0c1-8bc4-4f65-8a8f-0635f119b672", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830654005800, "endTime": 31830654020500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c2e58034-ddb5-408d-b60a-03a9e4a6fe25", "logId": "82cb25a5-9d0e-4188-992a-004868ad3084"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82cb25a5-9d0e-4188-992a-004868ad3084", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830654005800, "endTime": 31830654020500}, "additional": {"logType": "info", "children": [], "durationId": "2438d0c1-8bc4-4f65-8a8f-0635f119b672", "parent": "c675b5ff-e256-4dfc-88cc-974b3cb6e962"}}, {"head": {"id": "c675b5ff-e256-4dfc-88cc-974b3cb6e962", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830566087800, "endTime": 31830654032300}, "additional": {"logType": "info", "children": ["1320a6c1-49bf-4cf8-87e7-3f4456598345", "a22f7eac-50d0-461f-95c6-3c35722a9bf3", "82cb25a5-9d0e-4188-992a-004868ad3084"], "durationId": "c2e58034-ddb5-408d-b60a-03a9e4a6fe25", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "c74890f0-83b6-4d0a-85a7-c8c7c974cf2e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830654572700, "endTime": 31830673336500}, "additional": {"children": ["61aa2b21-24bd-4245-adb3-465685056845", "7d36b795-6616-435f-aabd-45f815d50281", "82f4d0b1-2601-4d43-9644-56089b197ad6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b3b28630-a3e4-40d7-addf-d94d791ede70", "logId": "97710c56-600a-4db6-a391-76a96510ef10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61aa2b21-24bd-4245-adb3-465685056845", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830657085800, "endTime": 31830657100700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c74890f0-83b6-4d0a-85a7-c8c7c974cf2e", "logId": "785e6292-653f-4304-89be-ca0a9da1aaad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "785e6292-653f-4304-89be-ca0a9da1aaad", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830657085800, "endTime": 31830657100700}, "additional": {"logType": "info", "children": [], "durationId": "61aa2b21-24bd-4245-adb3-465685056845", "parent": "97710c56-600a-4db6-a391-76a96510ef10"}}, {"head": {"id": "7d36b795-6616-435f-aabd-45f815d50281", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830658622400, "endTime": 31830672005400}, "additional": {"children": ["58bef399-20d5-4594-ab10-95a947a2413f", "690df6f1-00cf-4ac7-970a-68162503dc6d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c74890f0-83b6-4d0a-85a7-c8c7c974cf2e", "logId": "436d6665-9c8a-4f6d-b083-3e162f67d040"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58bef399-20d5-4594-ab10-95a947a2413f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830658623600, "endTime": 31830662310100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d36b795-6616-435f-aabd-45f815d50281", "logId": "2d5c3a07-bb75-4c26-8944-ac0dddc1d8e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "690df6f1-00cf-4ac7-970a-68162503dc6d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830662329600, "endTime": 31830671993800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d36b795-6616-435f-aabd-45f815d50281", "logId": "3ee3c4e9-0c49-4c3f-a6bb-c568a64a9ab0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "102b05a1-e359-4df3-b6a2-c41f7a840876", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830658626700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e4fd81e-038b-437a-904c-53f6e4f4c5c5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830662138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5c3a07-bb75-4c26-8944-ac0dddc1d8e4", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830658623600, "endTime": 31830662310100}, "additional": {"logType": "info", "children": [], "durationId": "58bef399-20d5-4594-ab10-95a947a2413f", "parent": "436d6665-9c8a-4f6d-b083-3e162f67d040"}}, {"head": {"id": "19d437f2-253f-4fff-afc4-c3edaeac4eff", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830662342700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7ea58c5-a56d-4687-95b5-4abe4eb873ef", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830667726700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ab7950-2192-43cd-9b6d-e603ee8970d2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830667839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad953e1-a466-4ef0-bb37-c0575bec11db", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830668026200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ef56776-a86a-4018-a32c-2896c9731475", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830668214600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd3e55b-bd3e-45af-ad7c-d9335270c322", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830668298700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bcc5792-066d-4ff5-b887-e55d71e11f8d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830668360800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e94405f5-75c4-4881-a9bf-d466c9ed20dd", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830668423900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "495251ed-e3f5-4c51-bc65-f9ea0fd554a3", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830671680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f14a6cca-6a2b-4711-b670-b7ddf41c7f2a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830671838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21bd7955-8bd6-4df7-86b5-459ec1aeb582", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830671899800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db28c3e-39f6-44f2-968b-385e2d3d11ee", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830671948300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee3c4e9-0c49-4c3f-a6bb-c568a64a9ab0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830662329600, "endTime": 31830671993800}, "additional": {"logType": "info", "children": [], "durationId": "690df6f1-00cf-4ac7-970a-68162503dc6d", "parent": "436d6665-9c8a-4f6d-b083-3e162f67d040"}}, {"head": {"id": "436d6665-9c8a-4f6d-b083-3e162f67d040", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830658622400, "endTime": 31830672005400}, "additional": {"logType": "info", "children": ["2d5c3a07-bb75-4c26-8944-ac0dddc1d8e4", "3ee3c4e9-0c49-4c3f-a6bb-c568a64a9ab0"], "durationId": "7d36b795-6616-435f-aabd-45f815d50281", "parent": "97710c56-600a-4db6-a391-76a96510ef10"}}, {"head": {"id": "82f4d0b1-2601-4d43-9644-56089b197ad6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830673308000, "endTime": 31830673321600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c74890f0-83b6-4d0a-85a7-c8c7c974cf2e", "logId": "55f6a880-9f03-48b7-98f8-6fba9e705f50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55f6a880-9f03-48b7-98f8-6fba9e705f50", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830673308000, "endTime": 31830673321600}, "additional": {"logType": "info", "children": [], "durationId": "82f4d0b1-2601-4d43-9644-56089b197ad6", "parent": "97710c56-600a-4db6-a391-76a96510ef10"}}, {"head": {"id": "97710c56-600a-4db6-a391-76a96510ef10", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830654572700, "endTime": 31830673336500}, "additional": {"logType": "info", "children": ["785e6292-653f-4304-89be-ca0a9da1aaad", "436d6665-9c8a-4f6d-b083-3e162f67d040", "55f6a880-9f03-48b7-98f8-6fba9e705f50"], "durationId": "c74890f0-83b6-4d0a-85a7-c8c7c974cf2e", "parent": "14efd99a-8dfd-4b50-a19e-19a1c4a2822c"}}, {"head": {"id": "14efd99a-8dfd-4b50-a19e-19a1c4a2822c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830654045700, "endTime": 31830673346200}, "additional": {"logType": "info", "children": ["97710c56-600a-4db6-a391-76a96510ef10"], "durationId": "b3b28630-a3e4-40d7-addf-d94d791ede70", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "e2a78832-93e8-452c-9b71-000ea98fe40e", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830696251800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65be5150-7586-462a-9449-24282d765aad", "name": "hvigorfile, resolve hvigorfile dependencies in 69 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830742384200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9973c43-8bb2-4fdf-83a0-c0426efe903f", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830673354600, "endTime": 31830742512500}, "additional": {"logType": "info", "children": [], "durationId": "ac406809-ddb5-40ee-96f2-9ccd4e6f8742", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "e6b46510-2cdc-4284-ac1f-8e100ec7d7e7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830743235100, "endTime": 31830743413300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3488c390-efa5-4985-aecd-7b3475581a07", "logId": "2630ea8a-059d-4132-849c-645a9b7eb020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26a8ffb5-9f35-445f-bf3d-fe9ef26778f0", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830743261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2630ea8a-059d-4132-849c-645a9b7eb020", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830743235100, "endTime": 31830743413300}, "additional": {"logType": "info", "children": [], "durationId": "e6b46510-2cdc-4284-ac1f-8e100ec7d7e7", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "51d15adc-53aa-4197-8af1-0b3259c3963f", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830744391600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ed3e5b-749b-4899-befb-04b7e9406f51", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830758130300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a65fe13-b963-460e-92b5-61050640d966", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830743425100, "endTime": 31830758694600}, "additional": {"logType": "info", "children": [], "durationId": "9469f067-b8a1-4029-9908-26d79f62c699", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "e9a2637d-1a81-4733-bcc1-f25ba2f07017", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830762614900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f673b4-6d89-4a7b-8acf-6f643cf49ddd", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830762729000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5b6be1d-b36a-467d-9bb2-7f564edf97e5", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830764429200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6523afc3-2f85-44ee-a748-000f772fc2be", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830764513500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade0966d-ed77-4169-ac4a-49741d1e48e7", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830758714100, "endTime": 31830766937900}, "additional": {"logType": "info", "children": [], "durationId": "ade1c18c-e7ef-4ea3-a4bd-c9eafafe90bb", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "776e23b0-a228-4763-bacb-30b5368e35f8", "name": "Configuration phase cost:206 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830766975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8f040a-01b9-4180-8c17-8fb049d495c3", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830766954200, "endTime": 31830767077900}, "additional": {"logType": "info", "children": [], "durationId": "0d88c11d-4d1d-4130-9c32-0e8be9106c53", "parent": "ef004b6c-6948-4e19-b3aa-cf849b193615"}}, {"head": {"id": "ef004b6c-6948-4e19-b3aa-cf849b193615", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830555939600, "endTime": 31830767086700}, "additional": {"logType": "info", "children": ["80e349f9-35f6-48b0-8e72-4262d3ee22d9", "c4d05629-a7f0-4930-a2f3-4db9dac7d1b9", "c675b5ff-e256-4dfc-88cc-974b3cb6e962", "14efd99a-8dfd-4b50-a19e-19a1c4a2822c", "d9973c43-8bb2-4fdf-83a0-c0426efe903f", "3a65fe13-b963-460e-92b5-61050640d966", "ade0966d-ed77-4169-ac4a-49741d1e48e7", "5a8f040a-01b9-4180-8c17-8fb049d495c3", "2630ea8a-059d-4132-849c-645a9b7eb020"], "durationId": "3488c390-efa5-4985-aecd-7b3475581a07", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "3904661d-2024-44bb-ae18-f93cba1054c3", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830768110800, "endTime": 31830768123900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f198916c-e8c3-4f05-a21c-96cb841ef3b9", "logId": "eded0c68-1524-4309-8a1c-8700de31f28e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eded0c68-1524-4309-8a1c-8700de31f28e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830768110800, "endTime": 31830768123900}, "additional": {"logType": "info", "children": [], "durationId": "3904661d-2024-44bb-ae18-f93cba1054c3", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "a63a1910-381d-4cb7-9c72-b54567c44b91", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830767110200, "endTime": 31830768131500}, "additional": {"logType": "info", "children": [], "durationId": "bd4c5d43-6856-4249-aae1-6fa63ac33630", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "77699d78-522b-4dad-a2f1-67bf5cc996b7", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830768135300, "endTime": 31830768136000}, "additional": {"logType": "info", "children": [], "durationId": "1257be35-0a47-45b1-98f8-4d5299525f63", "parent": "61cbd2b3-983d-4632-9a7a-01abd88c9c59"}}, {"head": {"id": "61cbd2b3-983d-4632-9a7a-01abd88c9c59", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830542385000, "endTime": 31830768140600}, "additional": {"logType": "info", "children": ["796c3e45-1e7b-46b9-a89d-596e9c8d2ddf", "ef004b6c-6948-4e19-b3aa-cf849b193615", "a63a1910-381d-4cb7-9c72-b54567c44b91", "77699d78-522b-4dad-a2f1-67bf5cc996b7", "db1a6a3b-c7d2-4a14-89a3-48d314e2f4fb", "06e833de-b2a9-4b29-82b6-165dc7c4baa3", "eded0c68-1524-4309-8a1c-8700de31f28e"], "durationId": "f198916c-e8c3-4f05-a21c-96cb841ef3b9"}}, {"head": {"id": "d138fdcc-adee-4782-ad96-19114807b09d", "name": "Configuration task cost before running: 231 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830768285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1aeb52-ed81-435c-a820-534921aeb539", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830772554900, "endTime": 31830782013100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "dbc4f50f-18c4-49e6-b8f5-ca057c0e167a", "logId": "9a27f06a-0a91-466d-a4a9-78d05a2c5a12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbc4f50f-18c4-49e6-b8f5-ca057c0e167a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830769466900}, "additional": {"logType": "detail", "children": [], "durationId": "7b1aeb52-ed81-435c-a820-534921aeb539"}}, {"head": {"id": "111c812e-2001-495e-81b7-32248f815522", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830769846600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0becd80-a05b-4a55-aa06-9a51c272c23b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830769930700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e33f51-f5cd-4817-a1df-30c6346e806a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830772569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "889df47f-2ea5-4244-a2fc-3d157661de17", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830781721500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "146f64f0-e5af-48bc-b736-8cf59204a987", "name": "entry : default@PreBuild cost memory 0.31549072265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********2000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a27f06a-0a91-466d-a4a9-78d05a2c5a12", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830772554900, "endTime": 31830782013100}, "additional": {"logType": "info", "children": [], "durationId": "7b1aeb52-ed81-435c-a820-534921aeb539"}}, {"head": {"id": "e2d8296d-5eb7-4f0c-920c-925331fdf286", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830788371000, "endTime": 31830790889400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b7f4975c-3fd8-4f2d-b2e5-a38c5d8c2d01", "logId": "0976d034-6514-41c4-805f-6ef498d938b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7f4975c-3fd8-4f2d-b2e5-a38c5d8c2d01", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830786551500}, "additional": {"logType": "detail", "children": [], "durationId": "e2d8296d-5eb7-4f0c-920c-925331fdf286"}}, {"head": {"id": "a849e802-0d50-4a58-96a5-f64e5725fcf7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830786993600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c999934-aefc-4c06-8874-614d53e0495b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830787269900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "487f18d6-e1cd-4fe4-ab18-6556f13f5a51", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830788386700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2738ebaa-c976-403d-86e9-ffce9346fcfc", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830789648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e08142-0809-4680-9b5d-b4d62dea626d", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830790692700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512774cc-52f5-44e1-8b94-6ea382e9fe34", "name": "entry : default@GenerateMetadata cost memory 0.093780517578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830790815900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0976d034-6514-41c4-805f-6ef498d938b8", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830788371000, "endTime": 31830790889400}, "additional": {"logType": "info", "children": [], "durationId": "e2d8296d-5eb7-4f0c-920c-925331fdf286"}}, {"head": {"id": "66a79ac4-9923-4824-b892-f657ac44781e", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830793983700, "endTime": 31830794843600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "96683cf6-528f-4906-b6ec-dbe1264534be", "logId": "3174d3f0-a3b3-4a8b-a2a6-9eb3ca5bc685"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96683cf6-528f-4906-b6ec-dbe1264534be", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830793239900}, "additional": {"logType": "detail", "children": [], "durationId": "66a79ac4-9923-4824-b892-f657ac44781e"}}, {"head": {"id": "9e90d435-fe9a-49c9-86ea-13dba15bceec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830793720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f48e925-74af-46e9-9d4d-ecc700474bbb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830793833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8221acb4-717f-446a-b7f4-9f35bc39dbfd", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830793990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f6c31cb-d7c0-4a59-8d25-96a4b3a5af99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830794194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7ac2ea-3da9-4716-aeac-145aeb8e2344", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830794408600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f492711-deb8-46d7-8531-a3b4b9cbc430", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830794602700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb03534-6fe5-468a-829d-4bb662f57c83", "name": "runTaskFromQueue task cost before running: 257 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830794758700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3174d3f0-a3b3-4a8b-a2a6-9eb3ca5bc685", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830793983700, "endTime": 31830794843600, "totalTime": 729200}, "additional": {"logType": "info", "children": [], "durationId": "66a79ac4-9923-4824-b892-f657ac44781e"}}, {"head": {"id": "2a5b89fb-78bf-4fef-84a4-3c9e92608f25", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830799746000, "endTime": 31830801858400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "fa2a2692-243c-4d9e-8935-120b96507b13", "logId": "d3329c7d-43df-4fdd-bda7-6104a6fa6445"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa2a2692-243c-4d9e-8935-120b96507b13", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830798252900}, "additional": {"logType": "detail", "children": [], "durationId": "2a5b89fb-78bf-4fef-84a4-3c9e92608f25"}}, {"head": {"id": "e8c8117d-d6b5-4a68-85ce-f14caa7b7b55", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830798735000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c476acf-7c2b-416a-a26b-2053d02937ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830798905600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c57ee9b7-2bb9-4f7e-b791-6b4467dc79e0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830799761000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae4d4df6-a821-4a5e-b67b-f76dc0decf25", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830801659900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bf40f8c-6481-499b-8e1d-7d6fd19278dc", "name": "entry : default@MergeProfile cost memory 0.1061553955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830801792200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3329c7d-43df-4fdd-bda7-6104a6fa6445", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830799746000, "endTime": 31830801858400}, "additional": {"logType": "info", "children": [], "durationId": "2a5b89fb-78bf-4fef-84a4-3c9e92608f25"}}, {"head": {"id": "14a8eb2c-2271-4adb-80b0-ce0a13567a42", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830805213300, "endTime": 31830807542900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a53346a5-3621-4a61-adb8-52ab0261b71c", "logId": "228dc262-bb91-464f-8cc5-395d5a97b3d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a53346a5-3621-4a61-adb8-52ab0261b71c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830803415900}, "additional": {"logType": "detail", "children": [], "durationId": "14a8eb2c-2271-4adb-80b0-ce0a13567a42"}}, {"head": {"id": "f600fafc-e868-4141-9807-133377aa1b0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830803933200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4908ac3-dd06-48d0-82d8-5d76a6e4839c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830804035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c639393-28c7-4689-8614-dbcd79437ba0", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830805229400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7accd0bd-7e19-4a08-8768-11cf2d325e7a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830806371200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6fb57aa-09a3-4a83-be08-58d8823c0e24", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830807338100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789b57b2-aea3-420e-bcd2-dbc10f6f30f8", "name": "entry : default@CreateBuildProfile cost memory 0.10253143310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830807439500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "228dc262-bb91-464f-8cc5-395d5a97b3d1", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830805213300, "endTime": 31830807542900}, "additional": {"logType": "info", "children": [], "durationId": "14a8eb2c-2271-4adb-80b0-ce0a13567a42"}}, {"head": {"id": "ecb59187-48ab-4ff8-8430-4c64a7dbcca5", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814316300, "endTime": 31830814800800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "735a2a29-7aef-4f70-aeca-a7764bf3d2a7", "logId": "6553a849-3196-45be-891d-4f6b2f3b1d87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "735a2a29-7aef-4f70-aeca-a7764bf3d2a7", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830812906700}, "additional": {"logType": "detail", "children": [], "durationId": "ecb59187-48ab-4ff8-8430-4c64a7dbcca5"}}, {"head": {"id": "bfee868c-84db-4ff1-85eb-c86b38f2beaf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830813393100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e29f5bcc-33f3-4931-859c-8b56025aa345", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830813508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2699606b-29cc-4d57-bbd1-3caddb239f50", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814325600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d785493-b654-4991-805a-4ab32f860dbe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814455300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed8b523c-3bec-40c1-a37a-85e66d79346c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b106fc5d-cc4d-4be4-8eee-5a551fdfade9", "name": "entry : default@PreCheckSyscap cost memory 0.06774139404296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814648800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56a086ff-c1d6-482b-91b0-634f8a06b684", "name": "runTaskFromQueue task cost before running: 277 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6553a849-3196-45be-891d-4f6b2f3b1d87", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830814316300, "endTime": 31830814800800, "totalTime": 410800}, "additional": {"logType": "info", "children": [], "durationId": "ecb59187-48ab-4ff8-8430-4c64a7dbcca5"}}, {"head": {"id": "6d6b30fe-2b88-468e-80a9-1b4d1c5e7644", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830820085000, "endTime": 31830820698100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7ae6bfe1-f971-4ab6-8761-c4c13bd19f2e", "logId": "e6d97ed5-c4e9-430a-a540-e48fd76a7190"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ae6bfe1-f971-4ab6-8761-c4c13bd19f2e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830816226500}, "additional": {"logType": "detail", "children": [], "durationId": "6d6b30fe-2b88-468e-80a9-1b4d1c5e7644"}}, {"head": {"id": "89950ced-f397-4d7d-a17c-d1f68b70131b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830816548000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06e4ab31-2a48-4e6a-8d6a-83bcdf35acfe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830816644100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a688a09a-4571-4f14-97fb-68ad25348d27", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830820093600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5eb6234-f035-4441-8c07-e52d7cccb937", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830820287700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df735825-ad71-4652-b669-a9511906ce1e", "name": "entry : default@GeneratePkgContextInfo cost memory 0.038543701171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830820511800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1198d436-c2d1-4220-afdd-d641fb2cfda5", "name": "runTaskFromQueue task cost before running: 283 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830820605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d97ed5-c4e9-430a-a540-e48fd76a7190", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830820085000, "endTime": 31830820698100, "totalTime": 504600}, "additional": {"logType": "info", "children": [], "durationId": "6d6b30fe-2b88-468e-80a9-1b4d1c5e7644"}}, {"head": {"id": "eb302b82-3052-4499-a7b7-61b968961472", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830824643500, "endTime": 31830826458600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "c220aa87-0369-48b6-a1c5-ba5939e1744e", "logId": "5afaf271-b7a6-453e-8a69-c49274cb3032"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c220aa87-0369-48b6-a1c5-ba5939e1744e", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830822342700}, "additional": {"logType": "detail", "children": [], "durationId": "eb302b82-3052-4499-a7b7-61b968961472"}}, {"head": {"id": "f0e982ab-ae5d-4e27-9aed-c4ed8e76a735", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830822704600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e8bb9e-f27a-4742-b745-47367ce61eea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830822818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca3e5fa-dc5f-49ae-8f9e-e7e51f2dc540", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830824653200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81a03b83-14ea-4168-bcb6-45609113156d", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830826033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19d3e45a-a459-4aa9-b40c-3fadd4ce3d59", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830826154000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "299577c9-4b6d-45fd-af7d-95ae11e35af9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830826235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1adf49c1-ef7d-49d8-8dd7-2f3894df4a5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830826282000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56bf8473-444d-4780-859b-070499a7c2fb", "name": "entry : default@ProcessIntegratedHsp cost memory 0.117156982421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830826348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "820118e3-7008-4b46-944e-0c2994cee82d", "name": "runTaskFromQueue task cost before running: 289 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830826410900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afaf271-b7a6-453e-8a69-c49274cb3032", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830824643500, "endTime": 31830826458600, "totalTime": 1755400}, "additional": {"logType": "info", "children": [], "durationId": "eb302b82-3052-4499-a7b7-61b968961472"}}, {"head": {"id": "3e910a7e-f61e-4cfb-a049-07642d8713b3", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830147400, "endTime": 31830830582400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "332cd45f-17a9-4445-8810-15585db34d5a", "logId": "b4cdde96-15f8-4742-b6e8-470250ecde8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "332cd45f-17a9-4445-8810-15585db34d5a", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830828377200}, "additional": {"logType": "detail", "children": [], "durationId": "3e910a7e-f61e-4cfb-a049-07642d8713b3"}}, {"head": {"id": "88549da2-5c2b-45f9-9afb-0fde07b92758", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830828702300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d62f405-8055-486d-a7e5-eb6b968cd074", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830828846200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b40bd58d-1ef4-4039-ad7d-d4df88a47251", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830164300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f4d97f-bc3f-444a-a212-f156b64741d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830323900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adab4577-e421-4f8d-85c5-8d30f299e24d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830399100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abd3e0dd-ef05-4a3b-963b-dc9bcf327803", "name": "entry : default@BuildNativeWithCmake cost memory 0.17803955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "169b6f65-66bf-4fb9-b4fe-d6fc2d2b9379", "name": "runTaskFromQueue task cost before running: 293 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830531800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4cdde96-15f8-4742-b6e8-470250ecde8c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830830147400, "endTime": 31830830582400, "totalTime": 371500}, "additional": {"logType": "info", "children": [], "durationId": "3e910a7e-f61e-4cfb-a049-07642d8713b3"}}, {"head": {"id": "b230b7b6-3845-4600-80ec-c3d166d1f770", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830833540100, "endTime": 31830837033700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a44bb3f1-e1f7-480e-af55-f8e4429223f1", "logId": "fca96d30-5142-4f0d-8fb2-bcdb9f428dbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a44bb3f1-e1f7-480e-af55-f8e4429223f1", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830832285600}, "additional": {"logType": "detail", "children": [], "durationId": "b230b7b6-3845-4600-80ec-c3d166d1f770"}}, {"head": {"id": "20762a47-e1cb-4dad-9847-9ef9e1769896", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830832619800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a3ae21-64c8-4ebf-b377-f42136696184", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830832711800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "578e3168-be28-42b9-818e-b306029d5e05", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830833550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e4d4406-5478-4e5e-ad08-fe859933e28f", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830836837900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2055e4a7-0b89-4725-a5c1-97fede05254b", "name": "entry : default@MakePackInfo cost memory 0.13954925537109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830836965700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca96d30-5142-4f0d-8fb2-bcdb9f428dbd", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830833540100, "endTime": 31830837033700}, "additional": {"logType": "info", "children": [], "durationId": "b230b7b6-3845-4600-80ec-c3d166d1f770"}}, {"head": {"id": "c327c911-23ec-4be6-89ac-524ad3f8a578", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830842490200, "endTime": 31830848851800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "3d3be06c-c333-4d3f-8843-5ae24221cb26", "logId": "6a293f13-37b8-4ac7-b10c-6a38f02edc1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d3be06c-c333-4d3f-8843-5ae24221cb26", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830839896700}, "additional": {"logType": "detail", "children": [], "durationId": "c327c911-23ec-4be6-89ac-524ad3f8a578"}}, {"head": {"id": "dde9d9cd-c4fc-41f7-986b-9c8fe2264152", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830840483500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "322af507-5ea2-431e-80e7-b57f363b6368", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830840593500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1efec49-7ff2-4c24-a637-71363fbecbfb", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830842501800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfd419c7-5bd3-4fb0-b9d8-399a6cd80d78", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830843010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803deecf-796d-4e9c-9ece-d5d26b97570c", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830844865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a510a039-957f-443b-9318-7756df7e9464", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830847787600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48cdbfb1-9afe-4d2c-85cc-6ff3a5f85feb", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830847928200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba9343ed-b8d7-4d08-9e77-2104daa9fad5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830848073800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98ddc3c-053b-43d6-9f42-1e6e7f7efe70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830848362300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f4b4b9-7a9a-4273-9893-ae25efd6877e", "name": "entry : default@SyscapTransform cost memory 0.152008056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830848578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9819c7e3-5b5f-4057-9198-1127b92da9c6", "name": "runTaskFromQueue task cost before running: 311 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830848732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a293f13-37b8-4ac7-b10c-6a38f02edc1b", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830842490200, "endTime": 31830848851800, "totalTime": 6215300}, "additional": {"logType": "info", "children": [], "durationId": "c327c911-23ec-4be6-89ac-524ad3f8a578"}}, {"head": {"id": "52d6baa3-6b20-422c-863f-bcab569542c3", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830852332400, "endTime": 31830853513700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "919e0422-195e-4b92-abbb-e24bfebbd4c1", "logId": "f8416e08-a620-469f-9cf4-0027ee14b25c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "919e0422-195e-4b92-abbb-e24bfebbd4c1", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830850674300}, "additional": {"logType": "detail", "children": [], "durationId": "52d6baa3-6b20-422c-863f-bcab569542c3"}}, {"head": {"id": "2788f141-dc1f-459d-bf2e-0a5a93e99329", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830851014600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a242b9f7-33d5-4960-8fd6-c7d96f743d47", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830851224000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb2fbf74-a73f-4595-b478-1696e46dad0c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830852343900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea20d4c7-3c63-427a-85ac-3abdc0f54cc3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830853284100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7da693f0-f1de-487f-904e-3a12d24ae6cb", "name": "entry : default@ProcessProfile cost memory 0.05995941162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830853412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8416e08-a620-469f-9cf4-0027ee14b25c", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830852332400, "endTime": 31830853513700}, "additional": {"logType": "info", "children": [], "durationId": "52d6baa3-6b20-422c-863f-bcab569542c3"}}, {"head": {"id": "b3b8f47a-e97f-4372-9dbd-34d44f3089da", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830859082300, "endTime": 31830864053500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d70af0e4-efef-4804-8614-dfa667ddc8c1", "logId": "d68e8403-786b-45f8-a25b-ee04fe7630b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d70af0e4-efef-4804-8614-dfa667ddc8c1", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830856306900}, "additional": {"logType": "detail", "children": [], "durationId": "b3b8f47a-e97f-4372-9dbd-34d44f3089da"}}, {"head": {"id": "b1d3fd46-37dd-45ee-a4d8-a35fa8ad24e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830856910200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f66e1969-3f24-48a0-9c9c-1b8353537ec7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830857023800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8320485-bd88-4329-9e19-8c2d1ad9647e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830859094100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "734afac6-42a3-4596-a0f9-a7f35cdb14c9", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830863843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "978ac57a-a326-49bc-9286-7a122c31103b", "name": "entry : default@ProcessRouterMap cost memory 0.20319366455078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830863979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68e8403-786b-45f8-a25b-ee04fe7630b5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830859082300, "endTime": 31830864053500}, "additional": {"logType": "info", "children": [], "durationId": "b3b8f47a-e97f-4372-9dbd-34d44f3089da"}}, {"head": {"id": "2d0229bc-f420-4cfa-8a3c-1a93c039dc8b", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830868479700, "endTime": 31830869543800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3b43e11a-370a-477b-8a1f-a364ba095f5c", "logId": "37323cff-fb24-4f82-9fa4-9f523868966e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b43e11a-370a-477b-8a1f-a364ba095f5c", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830866765500}, "additional": {"logType": "detail", "children": [], "durationId": "2d0229bc-f420-4cfa-8a3c-1a93c039dc8b"}}, {"head": {"id": "ea7002fa-f02e-4816-9d52-50d4f16fc4b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830867088600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d102d37d-3d2f-4355-a08e-8f78da17ed05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830867400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa264ba-9cad-497a-abcf-e3048d04b670", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830868490900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b77f85a3-2665-48c3-8f23-a630cdfdd7ea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830868658100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eae61129-d021-4774-8d73-0eae94125633", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830868728900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d76eefbc-a372-4a91-9f22-cf26eb96fa34", "name": "entry : default@BuildNativeWithNinja cost memory 0.056884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830869367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b6e060-0b2e-4059-a955-aee177e9aa16", "name": "runTaskFromQueue task cost before running: 332 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830869471300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37323cff-fb24-4f82-9fa4-9f523868966e", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830868479700, "endTime": 31830869543800, "totalTime": 971900}, "additional": {"logType": "info", "children": [], "durationId": "2d0229bc-f420-4cfa-8a3c-1a93c039dc8b"}}, {"head": {"id": "c1ef52f9-0c62-4f70-9606-a10ad8eb0091", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830876357500, "endTime": 31830883403200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d00aa6ab-7999-4c66-a0bb-81d3cf7f6eed", "logId": "11ab0c89-827b-4a3e-b02d-1cb5f4ac332a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d00aa6ab-7999-4c66-a0bb-81d3cf7f6eed", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830873424600}, "additional": {"logType": "detail", "children": [], "durationId": "c1ef52f9-0c62-4f70-9606-a10ad8eb0091"}}, {"head": {"id": "439ccea3-6633-498b-9306-086e80128960", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830873902100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e04c155-8c07-4369-9213-bd9aeccb613a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830874010000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce52714-c46a-41a0-b705-781a57a782c8", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830875064900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a5c6b74-f559-43f9-8127-720d16d40610", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830877978400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62133394-2551-467d-b33c-dded240fd3f8", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830880840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7711b443-497d-4b4d-bf41-e1200672113f", "name": "entry : default@ProcessResource cost memory 0.1695709228515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830880984600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ab0c89-827b-4a3e-b02d-1cb5f4ac332a", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830876357500, "endTime": 31830883403200}, "additional": {"logType": "info", "children": [], "durationId": "c1ef52f9-0c62-4f70-9606-a10ad8eb0091"}}, {"head": {"id": "73f1e634-1db8-4836-a006-40d45e0c2eea", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830893029500, "endTime": 31830912819300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5bd2c1be-867b-4679-9b08-3b6f1bb79c78", "logId": "6d4ed1ab-ac6b-4f53-aa55-19ad7a412444"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bd2c1be-867b-4679-9b08-3b6f1bb79c78", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830888371100}, "additional": {"logType": "detail", "children": [], "durationId": "73f1e634-1db8-4836-a006-40d45e0c2eea"}}, {"head": {"id": "dd94ad80-aa31-4f25-ad47-311482ee6f5a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830888824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "597773b1-7f00-4ae5-827a-32f1846297a6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830888928800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a8fc954-9779-43e9-a864-30e70366c84c", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830893044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "329ce614-8001-41e1-9275-82cf2d183370", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830911854700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb74f0b-a683-48fb-81e3-3c2e876b5fc1", "name": "entry : default@GenerateLoaderJson cost memory 0.7669601440429688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830912012300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d4ed1ab-ac6b-4f53-aa55-19ad7a412444", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830893029500, "endTime": 31830912819300}, "additional": {"logType": "info", "children": [], "durationId": "73f1e634-1db8-4836-a006-40d45e0c2eea"}}, {"head": {"id": "daa2a0df-5372-4f26-b24d-14bb17fe6a70", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830923543500, "endTime": 31830928398000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "7176ebed-ae69-4334-850a-ff7f165df39e", "logId": "d48b2fad-16f6-405f-b0af-b1867125c20f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7176ebed-ae69-4334-850a-ff7f165df39e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830921901400}, "additional": {"logType": "detail", "children": [], "durationId": "daa2a0df-5372-4f26-b24d-14bb17fe6a70"}}, {"head": {"id": "003b919e-93c7-42a2-8bd2-e0d06b63d8b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830922373100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29f12535-4ef0-4180-b44a-d8b9627c60c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830922537400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83d34d8c-e2aa-434d-9936-75c4f338f646", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830923566000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df4925d-5ee1-41bc-8943-6bef9272d7d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830925742300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559ee687-05d9-4aa6-8582-2fda94f009dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830925883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a13d1de0-1100-4550-90b3-2efd27647eb9", "name": "entry : default@ProcessLibs cost memory -5.3753509521484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830928153400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed17472d-095d-4dd4-8ecb-731cb4c3b144", "name": "runTaskFromQueue task cost before running: 391 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830928308000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48b2fad-16f6-405f-b0af-b1867125c20f", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830923543500, "endTime": 31830928398000, "totalTime": 4730500}, "additional": {"logType": "info", "children": [], "durationId": "daa2a0df-5372-4f26-b24d-14bb17fe6a70"}}, {"head": {"id": "02652531-b7d0-4eb3-b417-80a072dd40ee", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830935295700, "endTime": 31830960908100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4735efa8-8e14-40fc-85b3-e29e28dfe67f", "logId": "0e7ff1c7-c07f-42d5-94be-82cc767e52a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4735efa8-8e14-40fc-85b3-e29e28dfe67f", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830931359900}, "additional": {"logType": "detail", "children": [], "durationId": "02652531-b7d0-4eb3-b417-80a072dd40ee"}}, {"head": {"id": "89b58ce8-d685-4fa3-a02d-f4bd6a9d3db3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830931781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d79650a4-64a4-4ebf-b1f2-e6ee678cb4d1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830931880500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc88082e-8fcd-423b-9a65-a170f9f3ef43", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830933002400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bda6112-b634-4f4e-b118-982420658a40", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830935319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57e4146-f9f6-4262-8152-8c4a8b891a4e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830960634100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c78791-9d39-4c3a-bff3-f8b1121b8f95", "name": "entry : default@CompileResource cost memory 1.4091873168945312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830960819100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7ff1c7-c07f-42d5-94be-82cc767e52a7", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830935295700, "endTime": 31830960908100}, "additional": {"logType": "info", "children": [], "durationId": "02652531-b7d0-4eb3-b417-80a072dd40ee"}}, {"head": {"id": "9c7e45cd-443e-4fac-ba46-3b851b1a91c5", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830970193400, "endTime": 31830972847300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "31102e51-873d-4a63-9dd1-2d1bd9078a8b", "logId": "d7d66ffb-a344-477d-8e8a-7e7656f959ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31102e51-873d-4a63-9dd1-2d1bd9078a8b", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830966491500}, "additional": {"logType": "detail", "children": [], "durationId": "9c7e45cd-443e-4fac-ba46-3b851b1a91c5"}}, {"head": {"id": "32b2683b-5897-4f26-9945-e113568b459d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830966940500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fad6df6-7d48-497e-bfa0-7359ded7e4c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830967049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de28ff76-98f7-4f14-b760-c0ea64b32799", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830970364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de856e4-be0f-4352-8f9f-44cb456c7081", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830970848900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8b195c-335f-4811-b2d7-82cacfa68cbe", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830972599200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21725dc-c220-49e0-925c-50fe27927a8d", "name": "entry : default@DoNativeStrip cost memory 0.075531005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830972770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7d66ffb-a344-477d-8e8a-7e7656f959ab", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830970193400, "endTime": 31830972847300}, "additional": {"logType": "info", "children": [], "durationId": "9c7e45cd-443e-4fac-ba46-3b851b1a91c5"}}, {"head": {"id": "97cf7763-69bb-47aa-9a94-cf6de0b2f1c4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830982139800, "endTime": 31832646267800}, "additional": {"children": ["483baa3e-1627-472f-898a-1fc51efb4f28", "a94bff88-abd4-45ce-a02c-6250c8ef93be"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2397b533-3dec-4063-897d-5d26b807ae84", "logId": "d4d73f99-3d41-45d4-bd97-05cdbfe07b2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2397b533-3dec-4063-897d-5d26b807ae84", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830974923600}, "additional": {"logType": "detail", "children": [], "durationId": "97cf7763-69bb-47aa-9a94-cf6de0b2f1c4"}}, {"head": {"id": "a99c8382-f528-498b-9c58-89c8abb9d212", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830975392600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e0eadfe-4a3b-48e0-a362-b2862d271519", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830975514300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a16ead82-a44a-4dad-a63b-517bf332b6bb", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830982397300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6e6167-55f2-4d85-a20d-da17b8a45c7c", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831009346700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccddd7f-eb8d-4892-8855-029ef69bba94", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********5300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a0029f-e7f3-454f-aeb6-3c010397292e", "name": "default@CompileArkTS work[68] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831014085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "483baa3e-1627-472f-898a-1fc51efb4f28", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31831016818700, "endTime": 31832639887100}, "additional": {"children": ["a8e2e3c7-8870-4362-8cc0-cdf27c2d0b11", "a71ae491-dba9-4f70-a7f2-d1d0934c3a28", "3c1584ef-919c-4625-b1a2-44902f73b6ea", "6828998e-8309-4488-94a1-12969a591dfd", "492b8026-d64a-41bf-ad75-d34b65bdae33", "2007761f-d3da-44cb-ba1d-808078cab091"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "97cf7763-69bb-47aa-9a94-cf6de0b2f1c4", "logId": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a57cf0e9-1700-48e1-9222-0aa8acd88e2e", "name": "default@CompileArkTS work[68] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831015949300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54170f39-bfab-4ea5-bba7-c18f657469f1", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831016061300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6f9310-8294-4846-a688-bab0f617757c", "name": "default@CompileArkTS work[68] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831016711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e86863-473e-4a0e-af3e-4444f21180dc", "name": "default@CompileArkTS work[68] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831017044000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3f97822-4ddb-43d4-8fc7-dbdf80e1e7aa", "name": "CopyResources startTime: 31831017442400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831017448000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d411061-1a5f-4091-84a6-ea50745634df", "name": "default@CompileArkTS work[69] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831017569500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a94bff88-abd4-45ce-a02c-6250c8ef93be", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31832114426200, "endTime": 31832131280900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "97cf7763-69bb-47aa-9a94-cf6de0b2f1c4", "logId": "315650fb-d7a0-4cec-9a14-0bc0545402dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a148497-c915-4e6a-b8c0-2528b8a8a04e", "name": "default@CompileArkTS work[69] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831018755700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5202ddfc-d3e3-41e8-88e0-74dd48678951", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831018853300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c7c706c-9339-4a77-8607-b1b674377062", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831018998500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa2b37f-249d-4f75-b28b-c502da28f98d", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831019081500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d9a144-0258-4f29-9aa7-e9a906d6a62a", "name": "default@CompileArkTS work[69] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831020907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0aec48-803f-46f8-9a48-83c24bb1fc8a", "name": "default@CompileArkTS work[69] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831022909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65423863-5244-4b8d-a5dc-3a595898d787", "name": "entry : default@CompileArkTS cost memory 1.3115997314453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831023219300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a02ba08f-64cf-45f4-8e7e-b08891b833f0", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831047930900, "endTime": 31831066320400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "fcdd2b45-0b9f-4e03-b162-6e3e00bac174", "logId": "acf12a69-a256-40f4-b7f8-fdbedc209cc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcdd2b45-0b9f-4e03-b162-6e3e00bac174", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831026559100}, "additional": {"logType": "detail", "children": [], "durationId": "a02ba08f-64cf-45f4-8e7e-b08891b833f0"}}, {"head": {"id": "a857e546-960c-40e5-8482-98c260b73792", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831027551600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b321724d-9e54-4736-86c7-6859c8c135bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831027788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96f0aa20-40c9-4ef1-9871-7b40b7125a5a", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831047949900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af046a71-67cc-4f6a-902a-21cf36b40e90", "name": "entry : default@BuildJS cost memory 0.1278839111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831059909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "208d24d0-c78a-4ed1-94f6-1db6cea00177", "name": "runTaskFromQueue task cost before running: 523 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831060328800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf12a69-a256-40f4-b7f8-fdbedc209cc1", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831047930900, "endTime": 31831066320400, "totalTime": 12321700}, "additional": {"logType": "info", "children": [], "durationId": "a02ba08f-64cf-45f4-8e7e-b08891b833f0"}}, {"head": {"id": "57f26887-d82e-458f-b37a-6d530da8f108", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831079015100, "endTime": 31831081644200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a4bcf814-9b7b-455a-8d29-8869317b7514", "logId": "863feeb1-3361-462b-91bb-551cba416c81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4bcf814-9b7b-455a-8d29-8869317b7514", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831070515500}, "additional": {"logType": "detail", "children": [], "durationId": "57f26887-d82e-458f-b37a-6d530da8f108"}}, {"head": {"id": "0aadcc17-8ad9-4cae-bd62-36ac62fbe0b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831073739000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79df79d5-9a32-447e-b31b-3a20dfb84e02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831074146100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e25bf0ef-3db2-4ac7-b471-f2397d57ed05", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831079030900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958bb937-aca2-4f72-b6d1-2ad1666cf1c6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831079539600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b136b5-4bfc-4dcb-86be-6dd272f1a12d", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831080845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35cfa856-dd09-4518-bc19-9881a5edcae0", "name": "entry : default@CacheNativeLibs cost memory 0.08847808837890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831081004100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "863feeb1-3361-462b-91bb-551cba416c81", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831079015100, "endTime": 31831081644200}, "additional": {"logType": "info", "children": [], "durationId": "57f26887-d82e-458f-b37a-6d530da8f108"}}, {"head": {"id": "e3498a82-7390-479a-8a06-7b2f83799d3a", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832131506200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317de4c9-e43e-44d2-9015-a249f5295d34", "name": "CopyResources is end, endTime: 31832131682100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832131704400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "433186f6-2bf9-4fd8-acaa-83860135d26a", "name": "default@CompileArkTS work[69] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832131809900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "315650fb-d7a0-4cec-9a14-0bc0545402dd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31832114426200, "endTime": 31832131280900}, "additional": {"logType": "info", "children": [], "durationId": "a94bff88-abd4-45ce-a02c-6250c8ef93be", "parent": "d4d73f99-3d41-45d4-bd97-05cdbfe07b2f"}}, {"head": {"id": "9eb1a5a8-76af-4640-8625-79b2c98fa3b9", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832132210700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd43ea5-8c3b-4bde-8033-3b9b788fd697", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832640223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e2e3c7-8870-4362-8cc0-cdf27c2d0b11", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31831016903900, "endTime": 31831023577000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "483baa3e-1627-472f-898a-1fc51efb4f28", "logId": "fc1ca620-4526-415f-90a3-339e30913619"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc1ca620-4526-415f-90a3-339e30913619", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831016903900, "endTime": 31831023577000}, "additional": {"logType": "info", "children": [], "durationId": "a8e2e3c7-8870-4362-8cc0-cdf27c2d0b11", "parent": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}}, {"head": {"id": "a71ae491-dba9-4f70-a7f2-d1d0934c3a28", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31831023596600, "endTime": 31831023976200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "483baa3e-1627-472f-898a-1fc51efb4f28", "logId": "4729d57c-ede3-43f3-b786-eac24242b187"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4729d57c-ede3-43f3-b786-eac24242b187", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831023596600, "endTime": 31831023976200}, "additional": {"logType": "info", "children": [], "durationId": "a71ae491-dba9-4f70-a7f2-d1d0934c3a28", "parent": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}}, {"head": {"id": "3c1584ef-919c-4625-b1a2-44902f73b6ea", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31831023993000, "endTime": 31831024027900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "483baa3e-1627-472f-898a-1fc51efb4f28", "logId": "c1122d33-ca8d-48ba-be3d-99de530ee686"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1122d33-ca8d-48ba-be3d-99de530ee686", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831023993000, "endTime": 31831024027900}, "additional": {"logType": "info", "children": [], "durationId": "3c1584ef-919c-4625-b1a2-44902f73b6ea", "parent": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}}, {"head": {"id": "6828998e-8309-4488-94a1-12969a591dfd", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31831024050900, "endTime": 31832546534200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "483baa3e-1627-472f-898a-1fc51efb4f28", "logId": "baa80db7-9f9d-4d32-b760-b16df1426d20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baa80db7-9f9d-4d32-b760-b16df1426d20", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31831024050900, "endTime": 31832546534200}, "additional": {"logType": "info", "children": [], "durationId": "6828998e-8309-4488-94a1-12969a591dfd", "parent": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}}, {"head": {"id": "492b8026-d64a-41bf-ad75-d34b65bdae33", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832546554500, "endTime": 31832554214100}, "additional": {"children": ["55eac671-0142-4353-a6b6-f7d16dae6cf8", "01deb480-5e3f-4673-8f5d-4429202d0307", "c54e118c-e7a9-44a2-b207-72b9b7f00101"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "483baa3e-1627-472f-898a-1fc51efb4f28", "logId": "f67f8b4a-4bb9-43e5-b9bf-dbb10751a802"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f67f8b4a-4bb9-43e5-b9bf-dbb10751a802", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832546554500, "endTime": 31832554214100}, "additional": {"logType": "info", "children": ["5f343381-8042-4af4-b8a3-3c3834dc0790", "b41c5eff-b783-4a09-a2fa-a6c1e429b330", "c6157650-c789-4ecd-8398-dbdbdb5a4f5b"], "durationId": "492b8026-d64a-41bf-ad75-d34b65bdae33", "parent": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}}, {"head": {"id": "55eac671-0142-4353-a6b6-f7d16dae6cf8", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832546573400, "endTime": 31832546579900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "492b8026-d64a-41bf-ad75-d34b65bdae33", "logId": "5f343381-8042-4af4-b8a3-3c3834dc0790"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f343381-8042-4af4-b8a3-3c3834dc0790", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832546573400, "endTime": 31832546579900}, "additional": {"logType": "info", "children": [], "durationId": "55eac671-0142-4353-a6b6-f7d16dae6cf8", "parent": "f67f8b4a-4bb9-43e5-b9bf-dbb10751a802"}}, {"head": {"id": "01deb480-5e3f-4673-8f5d-4429202d0307", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832546583200, "endTime": 31832548522300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "492b8026-d64a-41bf-ad75-d34b65bdae33", "logId": "b41c5eff-b783-4a09-a2fa-a6c1e429b330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b41c5eff-b783-4a09-a2fa-a6c1e429b330", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832546583200, "endTime": 31832548522300}, "additional": {"logType": "info", "children": [], "durationId": "01deb480-5e3f-4673-8f5d-4429202d0307", "parent": "f67f8b4a-4bb9-43e5-b9bf-dbb10751a802"}}, {"head": {"id": "c54e118c-e7a9-44a2-b207-72b9b7f00101", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832548526500, "endTime": 31832554201000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "492b8026-d64a-41bf-ad75-d34b65bdae33", "logId": "c6157650-c789-4ecd-8398-dbdbdb5a4f5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6157650-c789-4ecd-8398-dbdbdb5a4f5b", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832548526500, "endTime": 31832554201000}, "additional": {"logType": "info", "children": [], "durationId": "c54e118c-e7a9-44a2-b207-72b9b7f00101", "parent": "f67f8b4a-4bb9-43e5-b9bf-dbb10751a802"}}, {"head": {"id": "2007761f-d3da-44cb-ba1d-808078cab091", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832554229700, "endTime": 31832639751900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "483baa3e-1627-472f-898a-1fc51efb4f28", "logId": "1b7a9dbf-c403-4612-8c26-a3820eb9bc06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b7a9dbf-c403-4612-8c26-a3820eb9bc06", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832554229700, "endTime": 31832639751900}, "additional": {"logType": "info", "children": [], "durationId": "2007761f-d3da-44cb-ba1d-808078cab091", "parent": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d"}}, {"head": {"id": "100103c0-d4f1-404c-8c41-08318cd3bc8e", "name": "default@CompileArkTS work[68] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832646041900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4508fa5f-e50b-4f74-b42c-84aa2b551a1d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31831016818700, "endTime": 31832639887100}, "additional": {"logType": "info", "children": ["fc1ca620-4526-415f-90a3-339e30913619", "4729d57c-ede3-43f3-b786-eac24242b187", "c1122d33-ca8d-48ba-be3d-99de530ee686", "baa80db7-9f9d-4d32-b760-b16df1426d20", "f67f8b4a-4bb9-43e5-b9bf-dbb10751a802", "1b7a9dbf-c403-4612-8c26-a3820eb9bc06"], "durationId": "483baa3e-1627-472f-898a-1fc51efb4f28", "parent": "d4d73f99-3d41-45d4-bd97-05cdbfe07b2f"}}, {"head": {"id": "d70bc65b-579a-4a6f-9295-88896a965469", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832646193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d73f99-3d41-45d4-bd97-05cdbfe07b2f", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830982139800, "endTime": 31832646267800, "totalTime": 1657763300}, "additional": {"logType": "info", "children": ["4508fa5f-e50b-4f74-b42c-84aa2b551a1d", "315650fb-d7a0-4cec-9a14-0bc0545402dd"], "durationId": "97cf7763-69bb-47aa-9a94-cf6de0b2f1c4"}}, {"head": {"id": "91c92a80-648d-49e0-aaeb-dbc0cb63a07e", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832651060000, "endTime": 31832652446900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "8e1311ff-cbc6-4631-9f98-e928ec069172", "logId": "58177f10-ddf5-4731-9a99-da1dc486d6cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e1311ff-cbc6-4631-9f98-e928ec069172", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832649779900}, "additional": {"logType": "detail", "children": [], "durationId": "91c92a80-648d-49e0-aaeb-dbc0cb63a07e"}}, {"head": {"id": "00f27c64-7a8b-452d-aeb3-77aec222829c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832650152100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85870574-7d6b-4ddf-babb-4419926a51fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832650242100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "238b904c-7fea-4ba5-9a99-67c359b211aa", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832651068700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8698baa-47f7-4bda-9b70-bf7f37636a2d", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832651382900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a9d4945-d5fc-40f1-97e0-c8d057fd125c", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832652182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e308e4ba-2649-4041-9d89-eef4d854985c", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07757568359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832652341100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58177f10-ddf5-4731-9a99-da1dc486d6cb", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832651060000, "endTime": 31832652446900}, "additional": {"logType": "info", "children": [], "durationId": "91c92a80-648d-49e0-aaeb-dbc0cb63a07e"}}, {"head": {"id": "70710a1e-d724-45b0-9bb5-2dd2248d5aba", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832664079800, "endTime": 31833159941500}, "additional": {"children": ["a40830d0-5e7c-4350-9ecf-7433ef114a2f", "df88e9a1-274f-4913-bee2-4f602c10731b", "cfeb7a15-6345-4b62-961d-21eac386a3fc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "b655030c-75af-4263-a928-4ae7a5b35844", "logId": "515c8907-a80b-4748-9b5c-0d0fcf388e09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b655030c-75af-4263-a928-4ae7a5b35844", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832657349500}, "additional": {"logType": "detail", "children": [], "durationId": "70710a1e-d724-45b0-9bb5-2dd2248d5aba"}}, {"head": {"id": "10c4fc2a-8932-4a34-9390-7c1d411971a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832657784900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ceafda-3b6c-480b-88cd-6c07eb0afad5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832657890200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a86be63-6008-49f2-b68f-dda442a4a0b8", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832664090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee4074b7-664c-44e9-92c7-f4605502dd2d", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832673934400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9ead2e-3367-4e26-a120-b54cb06ce001", "name": "Incremental task entry:default@PackageHap pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832674070400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5df7dd-3cda-4cff-9756-d4c20566a788", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832674170400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f89e1ee-69e8-4e2b-b733-52bf203d629d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832674224500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a40830d0-5e7c-4350-9ecf-7433ef114a2f", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832675029200, "endTime": 31832676234300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70710a1e-d724-45b0-9bb5-2dd2248d5aba", "logId": "e7227959-9e27-4f2a-b838-dede0cbab4b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "408388cb-e7ca-4900-a753-5bd551eecaeb", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832676052300}, "additional": {"logType": "debug", "children": [], "durationId": "70710a1e-d724-45b0-9bb5-2dd2248d5aba"}}, {"head": {"id": "e7227959-9e27-4f2a-b838-dede0cbab4b8", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832675029200, "endTime": 31832676234300}, "additional": {"logType": "info", "children": [], "durationId": "a40830d0-5e7c-4350-9ecf-7433ef114a2f", "parent": "515c8907-a80b-4748-9b5c-0d0fcf388e09"}}, {"head": {"id": "df88e9a1-274f-4913-bee2-4f602c10731b", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832676772500, "endTime": 31832678311100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "70710a1e-d724-45b0-9bb5-2dd2248d5aba", "logId": "bf646b75-1e24-4f7f-bd4f-f93f1a143c53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59c8192d-f82d-49b7-aee6-3ccab376dc86", "name": "default@PackageHap work[70] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832677412400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfeb7a15-6345-4b62-961d-21eac386a3fc", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832678267600, "endTime": 31833159205100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "70710a1e-d724-45b0-9bb5-2dd2248d5aba", "logId": "5dfecfaa-d403-47c6-a5e4-812dca4b05c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42cbcf3f-7388-4df1-a44a-d7f332bd9c73", "name": "default@PackageHap work[70] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832678008400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f1abcc7-3b5f-4388-9409-c2efc78665d8", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832678079900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc4dc3d-dae9-4c10-b439-3604c36bb8f9", "name": "default@PackageHap work[70] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832678196800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "888d4b48-f036-4b56-9496-1e59d096bca7", "name": "default@PackageHap work[70] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832678256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf646b75-1e24-4f7f-bd4f-f93f1a143c53", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832676772500, "endTime": 31832678311100}, "additional": {"logType": "info", "children": [], "durationId": "df88e9a1-274f-4913-bee2-4f602c10731b", "parent": "515c8907-a80b-4748-9b5c-0d0fcf388e09"}}, {"head": {"id": "97dacb8a-ca6b-46b1-8685-7404c96b5544", "name": "entry : default@PackageHap cost memory 1.3057861328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832682608500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21470014-ca4c-4250-9e5d-2aaab1b28c66", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833159483900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61a4856-eff4-445d-ab46-3c7a041e9267", "name": "default@PackageHap work[70] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833159676200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfecfaa-d403-47c6-a5e4-812dca4b05c0", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31832678267600, "endTime": 31833159205100}, "additional": {"logType": "info", "children": [], "durationId": "cfeb7a15-6345-4b62-961d-21eac386a3fc", "parent": "515c8907-a80b-4748-9b5c-0d0fcf388e09"}}, {"head": {"id": "515c8907-a80b-4748-9b5c-0d0fcf388e09", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31832664079800, "endTime": 31833159941500, "totalTime": 495128100}, "additional": {"logType": "info", "children": ["e7227959-9e27-4f2a-b838-dede0cbab4b8", "bf646b75-1e24-4f7f-bd4f-f93f1a143c53", "5dfecfaa-d403-47c6-a5e4-812dca4b05c0"], "durationId": "70710a1e-d724-45b0-9bb5-2dd2248d5aba"}}, {"head": {"id": "04a31eb9-12c2-4778-a6b5-bf5ec5562570", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833172263300, "endTime": 31833174661700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "cacb3866-b661-4269-8156-4c02f78c0c22", "logId": "e7d6b173-23f8-4ac8-a493-3f12aa06db0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cacb3866-b661-4269-8156-4c02f78c0c22", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833167495600}, "additional": {"logType": "detail", "children": [], "durationId": "04a31eb9-12c2-4778-a6b5-bf5ec5562570"}}, {"head": {"id": "0ffb6538-d3c3-48ac-b305-f01b466b2466", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833168068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee67b878-9d68-4402-9950-9e33f49e7282", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833168371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d15c4679-1e27-44cd-a128-32f0905d0cef", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833172277000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab4fb9e2-1429-46d0-9841-026d5db8140d", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833173012700}, "additional": {"logType": "warn", "children": [], "durationId": "04a31eb9-12c2-4778-a6b5-bf5ec5562570"}}, {"head": {"id": "54f81474-62b2-4984-ab89-30448c554541", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833173669300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66659888-539a-4b86-8f90-7b81fbdc39f0", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833173783200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18298162-e796-4093-af72-bf99fefbcb63", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833173869100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9ad54b-7534-499d-8841-8d8cb5df7216", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833173922300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "694648b4-c786-4165-bda0-2bfa15dc7840", "name": "entry : default@SignHap cost memory 0.12115478515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833174465400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3c0e499-2b8b-4557-ac00-ac86c11b747e", "name": "runTaskFromQueue task cost before running: 2 s 637 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833174581400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d6b173-23f8-4ac8-a493-3f12aa06db0e", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833172263300, "endTime": 31833174661700, "totalTime": 2297300}, "additional": {"logType": "info", "children": [], "durationId": "04a31eb9-12c2-4778-a6b5-bf5ec5562570"}}, {"head": {"id": "57e95863-973a-4c8d-ac48-fc13570dfb73", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833180266400, "endTime": 31833187064300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a6f410ca-f806-4fc0-bf63-8061eddf53b9", "logId": "bd5caee1-3f6d-4b20-a0fa-7bda31052643"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6f410ca-f806-4fc0-bf63-8061eddf53b9", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833177348000}, "additional": {"logType": "detail", "children": [], "durationId": "57e95863-973a-4c8d-ac48-fc13570dfb73"}}, {"head": {"id": "c7e4200c-bcdf-47d9-a080-da3501aafae2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833178391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b7a610-67f2-4897-aedc-cf5578bcd0cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833178508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af89af8b-1c32-4946-b5bf-708596513a8a", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833180279100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d7170a8-c646-4541-a17f-860acfcd44ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833186703100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf53d17f-34d6-4dbd-bacf-de530605f8ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833186817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "539c1944-8deb-4c8a-8a9d-004080b8efa1", "name": "entry : default@CollectDebugSymbol cost memory 0.239532470703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833186905600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103120bd-0e27-4fe4-bd62-4d55920fb4ea", "name": "runTaskFromQueue task cost before running: 2 s 649 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833187001600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5caee1-3f6d-4b20-a0fa-7bda31052643", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833180266400, "endTime": 31833187064300, "totalTime": 6712700}, "additional": {"logType": "info", "children": [], "durationId": "57e95863-973a-4c8d-ac48-fc13570dfb73"}}, {"head": {"id": "dc343d16-5a3e-4427-bb10-dabd581c221a", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833188994600, "endTime": 31833189441500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "10ac3edd-27f1-4148-a697-2e34f03b0bdc", "logId": "4e9cf029-b793-45c5-9a29-0407b26bb31d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10ac3edd-27f1-4148-a697-2e34f03b0bdc", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833188954500}, "additional": {"logType": "detail", "children": [], "durationId": "dc343d16-5a3e-4427-bb10-dabd581c221a"}}, {"head": {"id": "ff48e818-63ea-4216-9012-9d7ad4635be3", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833189001000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4ce6560-5826-480b-a292-2e0e91f55550", "name": "entry : assembleHap cost memory 0.01337432861328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833189275600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f337986d-1ad1-443c-8a5e-25e3d111212a", "name": "runTaskFromQueue task cost before running: 2 s 652 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833189380600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e9cf029-b793-45c5-9a29-0407b26bb31d", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833188994600, "endTime": 31833189441500, "totalTime": 366000}, "additional": {"logType": "info", "children": [], "durationId": "dc343d16-5a3e-4427-bb10-dabd581c221a"}}, {"head": {"id": "f0021393-2ac5-488a-977c-c4ad1326378a", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833197814200, "endTime": 31833197836100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3e2b5f14-dee0-4b0f-a642-95da5509b875", "logId": "6714f94c-6164-4d29-b461-fff0bc5946ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6714f94c-6164-4d29-b461-fff0bc5946ab", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833197814200, "endTime": 31833197836100}, "additional": {"logType": "info", "children": [], "durationId": "f0021393-2ac5-488a-977c-c4ad1326378a"}}, {"head": {"id": "5ec554ad-0ab0-4244-9ed9-ccc74f2ae6c6", "name": "BUILD SUCCESSFUL in 2 s 660 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833197939000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "64ac9a7d-1c4f-4d37-addc-af3cb1209e78", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31830538084000, "endTime": 31833198263400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 20}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "e0d7cb15-3bac-4073-90e5-67621d3ecc79", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833198298900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e91204cf-3637-4e4c-ba24-fc296d043942", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833198764100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "092bfd27-5917-4732-83ac-9827c9d9734d", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833198848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ed1043-5d53-43ac-bad5-103618256e8f", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833198903500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a545f4b-284e-477e-9bb8-39a5a37ba055", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833198973400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cb134d7-8ac3-40f7-abe3-13f7dd97e4d1", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833199283200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad609c5-aefa-435f-ac68-fdf632dffdf1", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833199902000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f014f1b-4816-4b07-8118-3bc7370e0aee", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833200168700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c33fd65-99b0-4b7b-bef4-61f00c06e7f1", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833200246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d050db2-dc28-4bd8-aa65-19e6a7e09fd7", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833200308700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60a9ad2-d3d3-426f-86e1-aa19ed8fca58", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833200564700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2dd094-fe80-43a2-b67f-e4496d582798", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833202412600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfda9540-d08e-429e-9435-19d95a911a70", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833202764600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "745503d7-d063-4f0f-99eb-8222c41cba20", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833202864400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25010531-8231-4845-a554-450cb2407d8a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833202922400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bacc7ff-41a9-4057-8def-3809ead76098", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833202973800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ac6190-4d21-4738-83bb-6916661c7c7c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833203029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bbc7a4d-da7a-4264-883c-7d6022be3f6f", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833203555300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc288da-550f-4a86-99e3-45c2f5f19217", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833203807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a3d392f-0f8b-43d9-bd09-8533fcdf89ed", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833204009900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35ce4345-943b-4606-abc4-cef9273b279f", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833204301600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "367f36dd-1a2c-40c0-9ac0-a5b6f8e1a5ae", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833204379600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729c4a6c-aaef-4113-ac69-e3954c51812d", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833204435900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a234af-43fb-4405-a0df-b8d120384d3a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833206503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b0a4599-2825-4d76-a196-55cdb896cad8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833206924800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6048c500-e647-43d9-8ed7-6d676039b346", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833207818900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30738598-fa66-4362-a114-d4451e6e9174", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833208047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0ab571-b2c2-4476-a7bd-a8046aef7d32", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833208300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2278d86a-38d1-4271-adb8-e5be179a7f4c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833208804800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d4efc4-7223-4397-93d6-9778efb03ad5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833211740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af4be3f3-ae37-46cc-a2db-0e2ede563b2f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833212550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa96742-d899-47d2-b4dc-0238dee61424", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833212817800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd301ab-6250-4e31-80cd-0f2ba93ede97", "name": "Incremental task entry:default@CompileArkTS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833213350000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dfd5e92-3e58-48d1-9ed0-4a514dd49e31", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833214393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cddf375e-480e-45c2-9df8-407209e3f8bb", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833214805200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd8f22fd-cf71-47cc-8b43-9c915821ca20", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833215523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b5ce7d-ab03-490b-83e1-57176e86adf8", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833215712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "649510f1-e8fb-4cdf-afab-3a71b3ca5414", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833215894200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd89439-199b-40b5-aa7c-45d349dd9ff2", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833216500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb6f5f88-8c66-4755-a43a-2da559ac15ea", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833216759700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41cc92be-0ff7-4b5d-a123-3f8039008101", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833216834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2686e821-c0c4-4cf6-8241-77a6f6876c4b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833216888100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a429c10-f2e7-4970-80fe-792715166850", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833217628000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94c0f13c-7c56-4dc4-a7ff-e8e75a5eef1a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833217849600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafde368-237b-47f7-bee4-fcae73a7e9ae", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833218028200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc21bef1-b798-444d-b6bf-76d02debc6d7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833223825900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1373b7-eecb-4830-a27f-9769012321da", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833224047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e4cec2b-e870-4043-8b69-dc41bfd89812", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833224239900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e9b867b-efcb-4727-8c7d-38ff938edcfb", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833224313600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb91240-7e76-4b3d-a23a-0e8a7786b0be", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833224487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ac1fb14-7f71-45be-a54d-318784ed5c70", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833225077100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2093f853-896b-46ce-922f-f2d6e085ab7d", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833225328800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4df78a6-663f-434d-b19a-2a828586112d", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833225522600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93e980d8-4c20-49cb-88ac-4d9481c6d355", "name": "Incremental task entry:default@PackageHap post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833225789700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc69da7c-f40b-4802-853d-2fb0974a715f", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833225961800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76fc4653-07c5-4a60-860a-c5ac81b0952c", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833226033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b68c86b-f61c-476c-ade2-30e768c8cd61", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833226469800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b6267ac-fb94-4d54-81c4-4bf186efecce", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833228826600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f6003a-823c-46f4-8f70-da07aeaf6599", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833229191700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9d89c5-27ac-4730-a467-ad89232c817e", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833229474600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e69c8f-ca53-4671-a3b1-844a47edec75", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31833229746000}, "additional": {"logType": "debug", "children": []}}], "workLog": []}