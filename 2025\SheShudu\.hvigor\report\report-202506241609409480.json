{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "459c8be3-1d91-452f-9de4-9839da6b630d", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459698363700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b995390-72b2-4857-8bf4-fc26206d477e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459702591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0672074f-5c9f-4442-867f-8b8d636bd0b2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459702927200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2617bae3-3492-4f8b-a0f7-70a8faaf69d7", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459707475100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd50b101-f947-4acd-ac2e-1f4fac715a4a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612839750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612850494800, "endTime": 27613522412700}, "additional": {"children": ["55e8f4c9-b363-4ffa-86e8-c4e91e01b06d", "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "51eb9d41-112f-4198-8ac9-34ee0f68fe9c", "9a207041-4c3e-47d7-bbeb-5bc25aa1be76", "e11c83ef-eb29-40e7-9f06-40d76bb86b4a", "a4085d29-2c7d-40d2-9be4-84c9d47f62b9", "ca842acc-065b-4a96-9401-8424c5473d79"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55e8f4c9-b363-4ffa-86e8-c4e91e01b06d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612850497100, "endTime": 27612870752700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "48a2f4d9-8831-4d2c-a98b-e116d94e2c9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612870773900, "endTime": 27613520653500}, "additional": {"children": ["23e18380-64be-4a85-811e-3264530ddf78", "8a2b8b17-dad7-410c-9f7c-c87e2ed063aa", "21bde69f-5ad3-4c38-abeb-bf2f904f1b33", "b01e2aa5-b56e-477a-acd8-906019814801", "08d540fa-2d25-4377-aa6c-b0122c83995e", "8fc61a4a-906a-4b51-a02f-d05c8f837a72", "22c74427-df37-4f4f-a09f-e1e992ef4737", "a17b3414-f443-49df-b463-efbfb05bec06", "6367a129-b607-413e-b721-b32064918ef0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "de3549ae-1ad7-4622-a847-64874190ef40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51eb9d41-112f-4198-8ac9-34ee0f68fe9c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613520701000, "endTime": 27613522400300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "df8cdb6a-efc2-4c49-aace-ac935a13d0e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a207041-4c3e-47d7-bbeb-5bc25aa1be76", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613522405600, "endTime": 27613522406900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "ab613749-444f-47e7-8291-bcb7643f9949"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e11c83ef-eb29-40e7-9f06-40d76bb86b4a", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612856257400, "endTime": 27612856319900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "e5d90f53-5a2e-468c-965c-84c20fd225b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5d90f53-5a2e-468c-965c-84c20fd225b0", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612856257400, "endTime": 27612856319900}, "additional": {"logType": "info", "children": [], "durationId": "e11c83ef-eb29-40e7-9f06-40d76bb86b4a", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "a4085d29-2c7d-40d2-9be4-84c9d47f62b9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612863433600, "endTime": 27612863494800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "2998d42a-ddda-4d51-8b99-3af8aa70cfe8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2998d42a-ddda-4d51-8b99-3af8aa70cfe8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612863433600, "endTime": 27612863494800}, "additional": {"logType": "info", "children": [], "durationId": "a4085d29-2c7d-40d2-9be4-84c9d47f62b9", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "e08a45ac-210c-4d86-a2fc-c4b1bbb22bab", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612863723800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8361b656-0d8f-4fc4-ad84-c03b9489121a", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612870619700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48a2f4d9-8831-4d2c-a98b-e116d94e2c9f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612850497100, "endTime": 27612870752700}, "additional": {"logType": "info", "children": [], "durationId": "55e8f4c9-b363-4ffa-86e8-c4e91e01b06d", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "23e18380-64be-4a85-811e-3264530ddf78", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612880410200, "endTime": 27612880424900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "39989198-44eb-4d7b-8981-26117e15c632"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a2b8b17-dad7-410c-9f7c-c87e2ed063aa", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612880537300, "endTime": 27612895747200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "a3876d4d-b45f-4093-8eb0-2fde6bc288a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21bde69f-5ad3-4c38-abeb-bf2f904f1b33", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612895785100, "endTime": 27613114510900}, "additional": {"children": ["b44d7ea2-735f-4fab-8d18-6e759064ec07", "44d3a911-731f-41a3-a67c-5dc804e8a5b9", "1091f3f4-5164-43a7-a0df-f7e9e25bc835"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "96659cae-7402-4b9d-ab9f-ce250fd3906d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b01e2aa5-b56e-477a-acd8-906019814801", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613114524700, "endTime": 27613253368500}, "additional": {"children": ["d77a813d-6753-4505-9787-7e8f1acd8185"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "c0b65e68-3db1-400f-90b9-d111b0f09d8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08d540fa-2d25-4377-aa6c-b0122c83995e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613253459500, "endTime": 27613430385100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "f1d30c6e-fc63-4170-b73c-253b0ce75f72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fc61a4a-906a-4b51-a02f-d05c8f837a72", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613433383100, "endTime": 27613502242600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "2bcee3eb-3300-4503-8f74-bc42857d95ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22c74427-df37-4f4f-a09f-e1e992ef4737", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613502300200, "endTime": 27613520442300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "1fb63009-4e1e-4657-90a3-0cc8d75a1209"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a17b3414-f443-49df-b463-efbfb05bec06", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613520467700, "endTime": 27613520642000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "192f2b0d-c6f1-4c95-85c0-c03064c1f805"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39989198-44eb-4d7b-8981-26117e15c632", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612880410200, "endTime": 27612880424900}, "additional": {"logType": "info", "children": [], "durationId": "23e18380-64be-4a85-811e-3264530ddf78", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "a3876d4d-b45f-4093-8eb0-2fde6bc288a8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612880537300, "endTime": 27612895747200}, "additional": {"logType": "info", "children": [], "durationId": "8a2b8b17-dad7-410c-9f7c-c87e2ed063aa", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "b44d7ea2-735f-4fab-8d18-6e759064ec07", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612897043600, "endTime": 27612897066900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21bde69f-5ad3-4c38-abeb-bf2f904f1b33", "logId": "abb716e3-3a4b-44c8-b0b3-2a91deac4552"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abb716e3-3a4b-44c8-b0b3-2a91deac4552", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612897043600, "endTime": 27612897066900}, "additional": {"logType": "info", "children": [], "durationId": "b44d7ea2-735f-4fab-8d18-6e759064ec07", "parent": "96659cae-7402-4b9d-ab9f-ce250fd3906d"}}, {"head": {"id": "44d3a911-731f-41a3-a67c-5dc804e8a5b9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612900223000, "endTime": 27613113461000}, "additional": {"children": ["026ba328-52ba-418b-a3a0-3f8673951265", "5c5fd23a-8830-40d2-b87a-7b745a6833a1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21bde69f-5ad3-4c38-abeb-bf2f904f1b33", "logId": "ce5e5c27-2ed1-410c-954c-f573258c7354"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "026ba328-52ba-418b-a3a0-3f8673951265", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612900225500, "endTime": 27612941980100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44d3a911-731f-41a3-a67c-5dc804e8a5b9", "logId": "04d1e444-db09-4a2f-9e96-0a90a9302767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c5fd23a-8830-40d2-b87a-7b745a6833a1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612941996300, "endTime": 27613113433800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44d3a911-731f-41a3-a67c-5dc804e8a5b9", "logId": "7e72bb4e-3e6d-47d7-b90e-8f31eeb7787c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "167d888a-ab89-4d2c-8c87-fba785f84eef", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612900231700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c13f00-98b5-4e70-951e-384e199e0b40", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612941852700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d1e444-db09-4a2f-9e96-0a90a9302767", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612900225500, "endTime": 27612941980100}, "additional": {"logType": "info", "children": [], "durationId": "026ba328-52ba-418b-a3a0-3f8673951265", "parent": "ce5e5c27-2ed1-410c-954c-f573258c7354"}}, {"head": {"id": "7258d245-5b67-4d02-b284-fe99590365d6", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612942031300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e34da0-5463-44a6-821f-69a70d044550", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612980511100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090c2706-f773-4329-a3b7-01cc6357c1e7", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612980739200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a828ce5-a934-40d6-8995-605d1ee30d61", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612981069000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9c9006e-88c7-43d1-a086-d41ee96f93fe", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612981266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3043eea-a350-4b9d-b0cd-71496d58bb7f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612986610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1abfb97-57d9-44b1-b0d9-676ce6efa931", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612998192200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "963bf293-135c-4e1f-be9a-aaf0e4c7302a", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613024922700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9796e135-259f-447d-90f9-8998dde9745a", "name": "Sdk init in 70 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613069265100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55cdd3dd-09ed-4e67-9841-065edc5aa310", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613069612600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 9}, "markType": "other"}}, {"head": {"id": "5a39d7bf-92b3-4bf6-8879-fcb065a037da", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613069671100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 9}, "markType": "other"}}, {"head": {"id": "6fe2095b-bdc3-4d2d-96f7-3fa7b856058e", "name": "Project task initialization takes 41 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613113010900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "197ab0f3-cb69-4ca6-9bfe-29bb68e9e841", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613113210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3bbb47d-eedd-40ae-a723-0bd166b6d754", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613113283300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9a7d04-7b03-4281-bed3-2c7b116afad7", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613113335500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e72bb4e-3e6d-47d7-b90e-8f31eeb7787c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612941996300, "endTime": 27613113433800}, "additional": {"logType": "info", "children": [], "durationId": "5c5fd23a-8830-40d2-b87a-7b745a6833a1", "parent": "ce5e5c27-2ed1-410c-954c-f573258c7354"}}, {"head": {"id": "ce5e5c27-2ed1-410c-954c-f573258c7354", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612900223000, "endTime": 27613113461000}, "additional": {"logType": "info", "children": ["04d1e444-db09-4a2f-9e96-0a90a9302767", "7e72bb4e-3e6d-47d7-b90e-8f31eeb7787c"], "durationId": "44d3a911-731f-41a3-a67c-5dc804e8a5b9", "parent": "96659cae-7402-4b9d-ab9f-ce250fd3906d"}}, {"head": {"id": "1091f3f4-5164-43a7-a0df-f7e9e25bc835", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613114472600, "endTime": 27613114492800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "21bde69f-5ad3-4c38-abeb-bf2f904f1b33", "logId": "29a79b00-3025-4e74-8ca0-095bbf1d4bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29a79b00-3025-4e74-8ca0-095bbf1d4bef", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613114472600, "endTime": 27613114492800}, "additional": {"logType": "info", "children": [], "durationId": "1091f3f4-5164-43a7-a0df-f7e9e25bc835", "parent": "96659cae-7402-4b9d-ab9f-ce250fd3906d"}}, {"head": {"id": "96659cae-7402-4b9d-ab9f-ce250fd3906d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612895785100, "endTime": 27613114510900}, "additional": {"logType": "info", "children": ["abb716e3-3a4b-44c8-b0b3-2a91deac4552", "ce5e5c27-2ed1-410c-954c-f573258c7354", "29a79b00-3025-4e74-8ca0-095bbf1d4bef"], "durationId": "21bde69f-5ad3-4c38-abeb-bf2f904f1b33", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "d77a813d-6753-4505-9787-7e8f1acd8185", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613115150700, "endTime": 27613253354200}, "additional": {"children": ["3a5ee4c4-86d2-4625-ab7d-006b2c76de10", "3f0e1278-7f10-4c6e-931d-7147ac3d7c6c", "ffa0f0e4-c27d-40e4-bd57-65901ff24257"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b01e2aa5-b56e-477a-acd8-906019814801", "logId": "50e6260c-4ebe-4092-850c-0b8f018dc3ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a5ee4c4-86d2-4625-ab7d-006b2c76de10", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613140057400, "endTime": 27613140081000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d77a813d-6753-4505-9787-7e8f1acd8185", "logId": "3279a6bb-7db8-4d83-af94-2c5a801ef41d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3279a6bb-7db8-4d83-af94-2c5a801ef41d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613140057400, "endTime": 27613140081000}, "additional": {"logType": "info", "children": [], "durationId": "3a5ee4c4-86d2-4625-ab7d-006b2c76de10", "parent": "50e6260c-4ebe-4092-850c-0b8f018dc3ca"}}, {"head": {"id": "3f0e1278-7f10-4c6e-931d-7147ac3d7c6c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613147002100, "endTime": 27613250775500}, "additional": {"children": ["f4ec03a3-f121-497f-bd94-0c6faab82d46", "7b0765b0-6c88-49e3-acb2-cab79e729317"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d77a813d-6753-4505-9787-7e8f1acd8185", "logId": "1fc0a983-8741-4c4e-8554-8bf5f7a8b3e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4ec03a3-f121-497f-bd94-0c6faab82d46", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613147004900, "endTime": 27613161545100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f0e1278-7f10-4c6e-931d-7147ac3d7c6c", "logId": "7a82224b-e5f5-4887-9885-ab793a83d628"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b0765b0-6c88-49e3-acb2-cab79e729317", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613161631300, "endTime": 27613250757700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f0e1278-7f10-4c6e-931d-7147ac3d7c6c", "logId": "95f92ba9-5644-4ab2-9616-319680baf95c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4d42712-b78a-4e81-9fa7-9c067cd9cfd0", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613147010900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f27bce9-5d39-4a9c-bf7f-64311d7378cb", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613161239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a82224b-e5f5-4887-9885-ab793a83d628", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613147004900, "endTime": 27613161545100}, "additional": {"logType": "info", "children": [], "durationId": "f4ec03a3-f121-497f-bd94-0c6faab82d46", "parent": "1fc0a983-8741-4c4e-8554-8bf5f7a8b3e7"}}, {"head": {"id": "6967b437-2a81-488b-a719-6ac521e50838", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613161662500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0adccf-ba05-4f86-a976-c92164132932", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613237760800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e15bc5-8373-4681-883b-a1594dd5a2f9", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613238258400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30730616-915c-49ab-8705-1762508d5ca4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613239283900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6eb0a51-4de3-4aa8-94d0-ad9eda4b8739", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613240508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e78c64-f227-4557-9343-d186a58b0ea2", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613240678100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bca18e58-dbbd-4dd9-90a6-dc3f1a79d237", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613240754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fd5a6b-aa40-43bd-b117-4cdd52b79b23", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613241438500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de4037b6-aedf-401f-aea4-c69db49af6e6", "name": "Module entry task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613250340200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9051c94e-2839-4161-bbf7-4e051b522986", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613250573200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20f50816-d107-4dea-967f-51e4a1ba343f", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613250658200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83241831-1203-45b2-83ce-73dad1501d36", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613250709300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f92ba9-5644-4ab2-9616-319680baf95c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613161631300, "endTime": 27613250757700}, "additional": {"logType": "info", "children": [], "durationId": "7b0765b0-6c88-49e3-acb2-cab79e729317", "parent": "1fc0a983-8741-4c4e-8554-8bf5f7a8b3e7"}}, {"head": {"id": "1fc0a983-8741-4c4e-8554-8bf5f7a8b3e7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613147002100, "endTime": 27613250775500}, "additional": {"logType": "info", "children": ["7a82224b-e5f5-4887-9885-ab793a83d628", "95f92ba9-5644-4ab2-9616-319680baf95c"], "durationId": "3f0e1278-7f10-4c6e-931d-7147ac3d7c6c", "parent": "50e6260c-4ebe-4092-850c-0b8f018dc3ca"}}, {"head": {"id": "ffa0f0e4-c27d-40e4-bd57-65901ff24257", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613253309600, "endTime": 27613253335000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d77a813d-6753-4505-9787-7e8f1acd8185", "logId": "d5564213-2ea9-48d1-b03d-500dffce00e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5564213-2ea9-48d1-b03d-500dffce00e8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613253309600, "endTime": 27613253335000}, "additional": {"logType": "info", "children": [], "durationId": "ffa0f0e4-c27d-40e4-bd57-65901ff24257", "parent": "50e6260c-4ebe-4092-850c-0b8f018dc3ca"}}, {"head": {"id": "50e6260c-4ebe-4092-850c-0b8f018dc3ca", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613115150700, "endTime": 27613253354200}, "additional": {"logType": "info", "children": ["3279a6bb-7db8-4d83-af94-2c5a801ef41d", "1fc0a983-8741-4c4e-8554-8bf5f7a8b3e7", "d5564213-2ea9-48d1-b03d-500dffce00e8"], "durationId": "d77a813d-6753-4505-9787-7e8f1acd8185", "parent": "c0b65e68-3db1-400f-90b9-d111b0f09d8e"}}, {"head": {"id": "c0b65e68-3db1-400f-90b9-d111b0f09d8e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613114524700, "endTime": 27613253368500}, "additional": {"logType": "info", "children": ["50e6260c-4ebe-4092-850c-0b8f018dc3ca"], "durationId": "b01e2aa5-b56e-477a-acd8-906019814801", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "79884854-bc3f-4f17-9151-c7b52e323db8", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613312476900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a77de797-028a-4cd8-b41a-c772481239d9", "name": "hvigorfile, resolve hvigorfile dependencies in 177 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613430053600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d30c6e-fc63-4170-b73c-253b0ce75f72", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613253459500, "endTime": 27613430385100}, "additional": {"logType": "info", "children": [], "durationId": "08d540fa-2d25-4377-aa6c-b0122c83995e", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "6367a129-b607-413e-b721-b32064918ef0", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613431996500, "endTime": 27613433305800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "logId": "aa2978d3-4bd1-46e7-ad6f-00e40812aa81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24346d8e-45dc-4457-a745-40317f6a37e9", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613432126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2978d3-4bd1-46e7-ad6f-00e40812aa81", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613431996500, "endTime": 27613433305800}, "additional": {"logType": "info", "children": [], "durationId": "6367a129-b607-413e-b721-b32064918ef0", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "e0007714-161a-4b6f-97eb-7da331aed4a2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613435917200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a79c7b5-fb74-48b4-b2d8-033aec5df71b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613500956600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bcee3eb-3300-4503-8f74-bc42857d95ad", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613433383100, "endTime": 27613502242600}, "additional": {"logType": "info", "children": [], "durationId": "8fc61a4a-906a-4b51-a02f-d05c8f837a72", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "b89866d8-3be3-4e7f-94a6-40824a51598f", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613508127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a8c086-b762-4b4d-b00a-860e0f4f58c4", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613508334400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05abd08e-2a48-466d-a8a1-53154f461a1a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613512370900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f092e77-cf3b-40f0-851e-179b93b6969a", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613512518100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb63009-4e1e-4657-90a3-0cc8d75a1209", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613502300200, "endTime": 27613520442300}, "additional": {"logType": "info", "children": [], "durationId": "22c74427-df37-4f4f-a09f-e1e992ef4737", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "b45a645a-41f8-4477-a32b-5954e8c46269", "name": "Configuration phase cost:641 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613520495400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "192f2b0d-c6f1-4c95-85c0-c03064c1f805", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613520467700, "endTime": 27613520642000}, "additional": {"logType": "info", "children": [], "durationId": "a17b3414-f443-49df-b463-efbfb05bec06", "parent": "de3549ae-1ad7-4622-a847-64874190ef40"}}, {"head": {"id": "de3549ae-1ad7-4622-a847-64874190ef40", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612870773900, "endTime": 27613520653500}, "additional": {"logType": "info", "children": ["39989198-44eb-4d7b-8981-26117e15c632", "a3876d4d-b45f-4093-8eb0-2fde6bc288a8", "96659cae-7402-4b9d-ab9f-ce250fd3906d", "c0b65e68-3db1-400f-90b9-d111b0f09d8e", "f1d30c6e-fc63-4170-b73c-253b0ce75f72", "2bcee3eb-3300-4503-8f74-bc42857d95ad", "1fb63009-4e1e-4657-90a3-0cc8d75a1209", "192f2b0d-c6f1-4c95-85c0-c03064c1f805", "aa2978d3-4bd1-46e7-ad6f-00e40812aa81"], "durationId": "0f47dee7-b1a8-4ad9-9b26-b84e48dd979c", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "ca842acc-065b-4a96-9401-8424c5473d79", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613522357600, "endTime": 27613522384300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "535b9c7b-54ea-4b5c-82b9-21237a9845b6", "logId": "5244953c-f35a-4889-9516-a195fc161984"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5244953c-f35a-4889-9516-a195fc161984", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613522357600, "endTime": 27613522384300}, "additional": {"logType": "info", "children": [], "durationId": "ca842acc-065b-4a96-9401-8424c5473d79", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "df8cdb6a-efc2-4c49-aace-ac935a13d0e3", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613520701000, "endTime": 27613522400300}, "additional": {"logType": "info", "children": [], "durationId": "51eb9d41-112f-4198-8ac9-34ee0f68fe9c", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "ab613749-444f-47e7-8291-bcb7643f9949", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613522405600, "endTime": 27613522406900}, "additional": {"logType": "info", "children": [], "durationId": "9a207041-4c3e-47d7-bbeb-5bc25aa1be76", "parent": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6"}}, {"head": {"id": "6214067d-4dfd-4973-9cd8-fb7a77f9eec6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612850494800, "endTime": 27613522412700}, "additional": {"logType": "info", "children": ["48a2f4d9-8831-4d2c-a98b-e116d94e2c9f", "de3549ae-1ad7-4622-a847-64874190ef40", "df8cdb6a-efc2-4c49-aace-ac935a13d0e3", "ab613749-444f-47e7-8291-bcb7643f9949", "e5d90f53-5a2e-468c-965c-84c20fd225b0", "2998d42a-ddda-4d51-8b99-3af8aa70cfe8", "5244953c-f35a-4889-9516-a195fc161984"], "durationId": "535b9c7b-54ea-4b5c-82b9-21237a9845b6"}}, {"head": {"id": "4801a470-ce02-455f-bb34-f2be622332a2", "name": "Configuration task cost before running: 679 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613524622900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e624a79-d3d8-404a-83aa-70f166572d3c", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613531484800, "endTime": 27613548624800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "afa4c021-a17b-48bf-9391-b2de8567366a", "logId": "71c47293-f025-40c7-97b8-283d098691fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afa4c021-a17b-48bf-9391-b2de8567366a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613527386500}, "additional": {"logType": "detail", "children": [], "durationId": "1e624a79-d3d8-404a-83aa-70f166572d3c"}}, {"head": {"id": "5a6f7b50-3cca-44f4-89e2-b90364b033b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613528088000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a5791b5-aee6-49c7-b5e3-d9b5902b3f22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613528223800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83dfaeb4-4b6a-483f-be0f-5be1af26861c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613531501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f05499-5f7e-4716-8092-2f0f06e673d8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613548376300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8913698-a10b-4126-9ab0-32d17eee9f64", "name": "entry : default@PreBuild cost memory 0.3152008056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613548551400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c47293-f025-40c7-97b8-283d098691fe", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613531484800, "endTime": 27613548624800}, "additional": {"logType": "info", "children": [], "durationId": "1e624a79-d3d8-404a-83aa-70f166572d3c"}}, {"head": {"id": "131ad56b-e98f-4e25-84f7-271ce8ce03e6", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613570593400, "endTime": 27613576606700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f2f8f6c4-ad44-4f82-8a64-23bd3e225b39", "logId": "4915635f-902d-4296-9658-0750359ded0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2f8f6c4-ad44-4f82-8a64-23bd3e225b39", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613565717000}, "additional": {"logType": "detail", "children": [], "durationId": "131ad56b-e98f-4e25-84f7-271ce8ce03e6"}}, {"head": {"id": "94f49a04-74d2-459c-b706-75bbf817c0b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613567537200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "188f2036-df89-4d2e-82ab-e9096cf2d0d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613568006300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "176f90a5-c3f7-4d31-9cf9-00d0d054f09d", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613570619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e784032-4513-4825-9344-2ac99471f8f1", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613575016000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c25640e2-2110-4c1f-905e-dcfa570fd889", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613576378900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7e85842-c2b3-4413-8988-5985ff7dda43", "name": "entry : default@GenerateMetadata cost memory 0.09618377685546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613576524800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4915635f-902d-4296-9658-0750359ded0d", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613570593400, "endTime": 27613576606700}, "additional": {"logType": "info", "children": [], "durationId": "131ad56b-e98f-4e25-84f7-271ce8ce03e6"}}, {"head": {"id": "15ee66f9-a54d-420d-87a4-c69d167c5000", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581252000, "endTime": 27613581763600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "673a6c38-c5e9-4521-b85d-e17a9bd4b2c2", "logId": "a314ced4-3a1b-4734-8765-35fab4b418c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "673a6c38-c5e9-4521-b85d-e17a9bd4b2c2", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613580206400}, "additional": {"logType": "detail", "children": [], "durationId": "15ee66f9-a54d-420d-87a4-c69d167c5000"}}, {"head": {"id": "03ae731c-a1e0-4d9b-8c0e-c3147f9913e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613580745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a419cfc-5d48-4045-aa49-4860405e7119", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613580912800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e4995ac-53f8-42da-937e-f599757870a4", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581263900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e99fd8-780c-4bf9-84ef-6eaaacc9a5d8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fbf7240-c35c-4396-965e-d0e435008921", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581436400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ef7242-7d9d-496f-846a-762ded5ab380", "name": "entry : default@ConfigureCmake cost memory 0.03692626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b19336be-07ad-4114-b6a0-257976d32f7f", "name": "runTaskFromQueue task cost before running: 736 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581657100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a314ced4-3a1b-4734-8765-35fab4b418c6", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613581252000, "endTime": 27613581763600, "totalTime": 381600}, "additional": {"logType": "info", "children": [], "durationId": "15ee66f9-a54d-420d-87a4-c69d167c5000"}}, {"head": {"id": "02bcbc08-da2f-4c3d-a279-5cc08aa86071", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613591011400, "endTime": 27613597130100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "56344cff-ddcd-4c85-93df-21fff5127314", "logId": "cf4f9be9-2720-4731-be3d-039afb1f3f9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56344cff-ddcd-4c85-93df-21fff5127314", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613585702200}, "additional": {"logType": "detail", "children": [], "durationId": "02bcbc08-da2f-4c3d-a279-5cc08aa86071"}}, {"head": {"id": "7bd15070-c008-4af5-8a18-10e9df3d4e8e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613587612800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9156be0e-b6db-4d32-9d43-ffb096d37499", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613587958100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab48dd7-e59b-41a5-a2a8-2be7f8629aaa", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613591045000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c18bd5-6295-4a5f-b3cc-d98d4c490a7c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613596632700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "916433e9-60b8-47d0-b739-8e9454acf94b", "name": "entry : default@MergeProfile cost memory 0.107208251953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613597003000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf4f9be9-2720-4731-be3d-039afb1f3f9f", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613591011400, "endTime": 27613597130100}, "additional": {"logType": "info", "children": [], "durationId": "02bcbc08-da2f-4c3d-a279-5cc08aa86071"}}, {"head": {"id": "34fdc5fc-3f92-43fe-9dc2-fea044d3a9ad", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613610973000, "endTime": 27613617318100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "28646bae-6cb0-4d6e-8e8a-f2af50e71392", "logId": "f854f125-c575-41ec-8a55-b36e213fee55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28646bae-6cb0-4d6e-8e8a-f2af50e71392", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613603478500}, "additional": {"logType": "detail", "children": [], "durationId": "34fdc5fc-3f92-43fe-9dc2-fea044d3a9ad"}}, {"head": {"id": "6d9d14fd-7f39-4f4e-95ee-6498a66d5fa0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613606431200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec164291-0739-4078-b7dd-1d68ea73af92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613607818000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c33c3da-5a8d-419c-9b84-5408c1737c6b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613610997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23aa8448-e008-4f48-bffd-3abe13cdae8e", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613614322300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9b6044-580d-44d7-8d72-c3ac4e3f6aaf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613616676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5327f36-4092-4ea9-b290-c1c0d5eb2ffc", "name": "entry : default@CreateBuildProfile cost memory 0.35333251953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613617096800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f854f125-c575-41ec-8a55-b36e213fee55", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613610973000, "endTime": 27613617318100}, "additional": {"logType": "info", "children": [], "durationId": "34fdc5fc-3f92-43fe-9dc2-fea044d3a9ad"}}, {"head": {"id": "c484b6c7-0bb4-4911-8110-94c6cd321d05", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613629125800, "endTime": 27613631602800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "32f81297-bc50-4c7f-9085-62c51f90ea79", "logId": "36680b58-1e23-46a0-a8ef-a332e123765e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32f81297-bc50-4c7f-9085-62c51f90ea79", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613620928200}, "additional": {"logType": "detail", "children": [], "durationId": "c484b6c7-0bb4-4911-8110-94c6cd321d05"}}, {"head": {"id": "963eb755-b16b-42aa-aace-bfe30b80b7e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613621893100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "981fb89f-ee1f-4c14-af84-ab57d75d5494", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613622219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b5cc96b-4a21-44f1-be3f-2b8142ad53fd", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613629172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e70cc36d-e7b1-4f5e-bb7e-2efa56802fab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613629564500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad78702-25a6-4a52-8943-95e4c32855dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613630557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a387b06-07c6-4aa9-b7bc-78d5f0559748", "name": "entry : default@PreCheckSyscap cost memory 0.03714752197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613630921200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4dfb23-2f61-4f99-820b-3be20fd6fd5c", "name": "runTaskFromQueue task cost before running: 786 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613631196400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36680b58-1e23-46a0-a8ef-a332e123765e", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613629125800, "endTime": 27613631602800, "totalTime": 2032600}, "additional": {"logType": "info", "children": [], "durationId": "c484b6c7-0bb4-4911-8110-94c6cd321d05"}}, {"head": {"id": "40cd7288-c6b8-4f33-bcd5-b91905e1e01c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613657733400, "endTime": 27613660229800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b2a8d253-bb65-4928-880d-638cf1ab08cb", "logId": "b553250f-b65e-4fcf-b904-cac411455d1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2a8d253-bb65-4928-880d-638cf1ab08cb", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613640467300}, "additional": {"logType": "detail", "children": [], "durationId": "40cd7288-c6b8-4f33-bcd5-b91905e1e01c"}}, {"head": {"id": "721c6fb0-6e98-4e37-bc2f-da08542ea065", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613641105700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f41fe1fd-21e4-4908-87fc-ff90f9768ed5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613641238800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7fe0e1-01a0-49f4-98b6-21c4217d35de", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613657757100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b4b1b1-cc9e-41c6-80b1-3f745118c917", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613658797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "861e4865-c13d-4361-9a43-4ada9f59ee5f", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03942108154296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613659902600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf3eec6-ca02-45a2-bc4d-63c96b160312", "name": "runTaskFromQueue task cost before running: 815 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613660127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b553250f-b65e-4fcf-b904-cac411455d1c", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613657733400, "endTime": 27613660229800, "totalTime": 2354000}, "additional": {"logType": "info", "children": [], "durationId": "40cd7288-c6b8-4f33-bcd5-b91905e1e01c"}}, {"head": {"id": "72824801-d0f4-45ca-8118-1eb8213ec0d2", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613668947000, "endTime": 27613673836200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "44ff77e8-a185-4efc-b554-3f8e772b3f1d", "logId": "bef67303-4ecf-4827-9d88-c156314a63a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44ff77e8-a185-4efc-b554-3f8e772b3f1d", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613664009700}, "additional": {"logType": "detail", "children": [], "durationId": "72824801-d0f4-45ca-8118-1eb8213ec0d2"}}, {"head": {"id": "b200fbe3-7887-48d6-acac-debf1057b0b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613664836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa46162-ec14-440e-a0c9-88eb0b8e1b4c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613664971600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f2863f-2c55-4a59-bd6c-597a66cef99a", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613668966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed53844-75d2-46bf-8dbd-2df9e61d1923", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613671709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c763a05-bba6-45fa-a107-e1148efdf7f6", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613672151500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "323ac05f-b086-4fae-950d-33fb7f247681", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613672357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3156c8d2-9d0a-4577-9916-031e5921fbba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613672427700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5293e0a2-5e26-43ce-a536-969ce30354b6", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11920166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613673484500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4b3bc2-09ca-4109-8ded-149b0d3dbe47", "name": "runTaskFromQueue task cost before running: 828 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613673738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bef67303-4ecf-4827-9d88-c156314a63a1", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613668947000, "endTime": 27613673836200, "totalTime": 4763900}, "additional": {"logType": "info", "children": [], "durationId": "72824801-d0f4-45ca-8118-1eb8213ec0d2"}}, {"head": {"id": "346982fd-76ed-43c6-ac9e-b0c9c4083aa3", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613678528000, "endTime": 27613679339200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4dacf37e-8a80-44e4-8cd8-e47bf0cce118", "logId": "62f94fee-c8ae-482e-9d87-f5ec520<PERSON>bee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dacf37e-8a80-44e4-8cd8-e47bf0cce118", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613676602100}, "additional": {"logType": "detail", "children": [], "durationId": "346982fd-76ed-43c6-ac9e-b0c9c4083aa3"}}, {"head": {"id": "8246b104-ab15-42d3-90db-3f6e2fff0729", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613677184200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38e904d9-2bfd-488e-ad91-6d85bd278d74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613677311100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad076be-4601-4481-8404-22419da0c6eb", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613678542300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d71917fa-face-48e0-bfbd-28cebd206529", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613678702500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75324dcf-beec-4618-b7bb-39f9d5d761b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613678769300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55315354-2ed5-4700-8ee7-502f95137762", "name": "entry : default@BuildNativeWithCmake cost memory 0.038055419921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613679088900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76b9750d-4cb2-4d59-95b7-6d30de21c0ef", "name": "runTaskFromQueue task cost before running: 834 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613679201100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62f94fee-c8ae-482e-9d87-f5ec520<PERSON>bee", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613678528000, "endTime": 27613679339200, "totalTime": 652100}, "additional": {"logType": "info", "children": [], "durationId": "346982fd-76ed-43c6-ac9e-b0c9c4083aa3"}}, {"head": {"id": "b8c099e2-0e2f-41b4-87d6-5d18f5bd5fe8", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613686150500, "endTime": 27613690986700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "76a0556f-7588-4b89-a4af-36b24138550f", "logId": "304ea836-6464-4d83-89a4-b14b67a97837"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76a0556f-7588-4b89-a4af-36b24138550f", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613683180800}, "additional": {"logType": "detail", "children": [], "durationId": "b8c099e2-0e2f-41b4-87d6-5d18f5bd5fe8"}}, {"head": {"id": "d9228f51-0c44-4f34-8303-3555a9575008", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613684317500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7fe79c-3cf3-4515-9f8d-3269cabac617", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613684519200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45e8fa3c-242c-48fa-8636-be1bd2f4f077", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613686166100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaef38a8-8f88-480f-b5a9-299966f182d4", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613690696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0353bc51-2d83-4cce-880d-ef5d10379bad", "name": "entry : default@MakePackInfo cost memory 0.14006805419921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613690912700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304ea836-6464-4d83-89a4-b14b67a97837", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613686150500, "endTime": 27613690986700}, "additional": {"logType": "info", "children": [], "durationId": "b8c099e2-0e2f-41b4-87d6-5d18f5bd5fe8"}}, {"head": {"id": "875d369d-1e1d-4144-a996-2cea3e10caaa", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613696937800, "endTime": 27613704559100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "d3323ea9-fa2e-4cdd-9214-850b9a6138dd", "logId": "cb67803a-ef66-47d6-97ae-1e8553b75a70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3323ea9-fa2e-4cdd-9214-850b9a6138dd", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613694373200}, "additional": {"logType": "detail", "children": [], "durationId": "875d369d-1e1d-4144-a996-2cea3e10caaa"}}, {"head": {"id": "098a5728-757c-4303-847d-f9764c3ea83f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613694991600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c8e1f8-e696-4b30-a74c-f6105b19acaf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613695130700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c37a30de-b04f-4243-9d96-2044109a76e7", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613696954300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d619b2-ecd7-40aa-9d42-488913f2008b", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613697201400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "768f77ae-f3e4-4960-a7d8-8a108c5cd8e7", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613698560600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18610630-6f54-4b03-b38b-2d2caaeeaf39", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613703536600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48748263-9d2e-4855-98fc-ddda75f79c59", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613703753100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1865b3-c543-483d-8f22-cf6884da87bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613704063200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a43213ee-e712-4839-b869-21a0a1d2733d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613704171100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d7a5dc8-dc8d-4110-b63d-ef1bd2e2bcb5", "name": "entry : default@SyscapTransform cost memory 0.15606689453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613704273800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf5c53b-5dc9-4b51-a336-776f22d8b032", "name": "runTaskFromQueue task cost before running: 859 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613704482500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb67803a-ef66-47d6-97ae-1e8553b75a70", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613696937800, "endTime": 27613704559100, "totalTime": 7450400}, "additional": {"logType": "info", "children": [], "durationId": "875d369d-1e1d-4144-a996-2cea3e10caaa"}}, {"head": {"id": "84aab204-7321-4708-98c9-9901e454d606", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613711381400, "endTime": 27613713055900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "223b7c11-4d37-46f1-b36e-54b3fccd94f3", "logId": "e1b77764-dbc9-46d7-af3d-71d3d7f26c93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "223b7c11-4d37-46f1-b36e-54b3fccd94f3", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613707170000}, "additional": {"logType": "detail", "children": [], "durationId": "84aab204-7321-4708-98c9-9901e454d606"}}, {"head": {"id": "3a9c83c0-6742-421e-aa1f-f692b6d4e838", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613708205400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99bd2d0-4d17-4064-995e-033e5ef790d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613708533100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4352af0-e6e7-41b5-8d27-9d713ec9b449", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613711399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bc38245-084f-48d6-8742-7d9715378bc0", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613712624400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50976bac-c938-400d-886c-8df37f8702f8", "name": "entry : default@ProcessProfile cost memory 0.0612335205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613712772400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b77764-dbc9-46d7-af3d-71d3d7f26c93", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613711381400, "endTime": 27613713055900}, "additional": {"logType": "info", "children": [], "durationId": "84aab204-7321-4708-98c9-9901e454d606"}}, {"head": {"id": "ec9a4e1a-2e05-484e-85de-67144fa692bc", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613719310500, "endTime": 27613725510500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4f09a61f-e345-429d-861d-bb1efefb5ed4", "logId": "de6dda1c-ffd3-46fe-b8a3-bcf9325b608f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f09a61f-e345-429d-861d-bb1efefb5ed4", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613715972900}, "additional": {"logType": "detail", "children": [], "durationId": "ec9a4e1a-2e05-484e-85de-67144fa692bc"}}, {"head": {"id": "b128dd98-ae5c-4a53-a310-4918723616d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613716584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea736a7-482e-4118-8cff-98f1407e4854", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613716710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49f98f7c-ce01-4765-9fcb-16b59a21cf53", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613719327400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11fe714-0680-4480-9f98-794cecabec7f", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613725233300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7d4ce3a-c27d-4918-8355-8e80092da523", "name": "entry : default@ProcessRouterMap cost memory 0.20397186279296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613725422700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de6dda1c-ffd3-46fe-b8a3-bcf9325b608f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613719310500, "endTime": 27613725510500}, "additional": {"logType": "info", "children": [], "durationId": "ec9a4e1a-2e05-484e-85de-67144fa692bc"}}, {"head": {"id": "450ab5a6-8b68-43f8-b395-bf19f77d6627", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613735973500, "endTime": 27613737221400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7f5e8c09-cf73-43c2-98b8-eb0c78407d70", "logId": "a5ef5f16-6ea6-487e-8a14-e0010fc86434"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f5e8c09-cf73-43c2-98b8-eb0c78407d70", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613729323600}, "additional": {"logType": "detail", "children": [], "durationId": "450ab5a6-8b68-43f8-b395-bf19f77d6627"}}, {"head": {"id": "57c0ae63-3534-4816-b26a-b6c2c3ac0ce3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613730040300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564f1b96-7b7b-4772-91f0-3b630c672c75", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613734841300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b87e0fd8-1aaa-4819-a011-6b65b8d001fd", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613735987600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6cb31e6-0dd0-412e-898e-ce6395ca60a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613736131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ce30fc-478c-4536-b8f9-c12d4212a8ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613736196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b606301-b169-494d-bbe0-c716dfbdf679", "name": "entry : default@BuildNativeWithNinja cost memory 0.05754852294921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613736990500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f2feaa-43b5-46b1-a509-d928f2a657ba", "name": "runTaskFromQueue task cost before running: 892 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613737151100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5ef5f16-6ea6-487e-8a14-e0010fc86434", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613735973500, "endTime": 27613737221400, "totalTime": 1151500}, "additional": {"logType": "info", "children": [], "durationId": "450ab5a6-8b68-43f8-b395-bf19f77d6627"}}, {"head": {"id": "f34d8749-ac23-4f00-bf62-10692f75d4a4", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613745238800, "endTime": 27613757628100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "187cddb2-9b94-43b9-9be2-6425df1a3951", "logId": "f50bbbdd-029c-4fbb-b1d8-9ee96a2d07c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "187cddb2-9b94-43b9-9be2-6425df1a3951", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613740419700}, "additional": {"logType": "detail", "children": [], "durationId": "f34d8749-ac23-4f00-bf62-10692f75d4a4"}}, {"head": {"id": "d049413d-9c99-499a-b58c-044fabc010d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613740982400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113a9c37-835d-4a1c-83d6-65c1e5d00a9a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613741143100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd77095-1bb3-45ea-9dbb-b0069c3110bf", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613742914500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c219b9f1-e3c0-4a3f-83cb-0ae14c04431a", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613747261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa56fc7-0677-427d-87bc-7435fbbf3afe", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613751782100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51176f92-8cec-4540-8a10-1393940fe35e", "name": "entry : default@ProcessResource cost memory 0.17020416259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613752041600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50bbbdd-029c-4fbb-b1d8-9ee96a2d07c6", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613745238800, "endTime": 27613757628100}, "additional": {"logType": "info", "children": [], "durationId": "f34d8749-ac23-4f00-bf62-10692f75d4a4"}}, {"head": {"id": "d0f617d6-726f-456e-8a5d-c15cafa96ea4", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613778168900, "endTime": 27613806397500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "477a9236-ad8b-4daf-9577-177015c6f88b", "logId": "c1d53f98-e531-48f4-a1ff-499e7bca5b55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "477a9236-ad8b-4daf-9577-177015c6f88b", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613764885700}, "additional": {"logType": "detail", "children": [], "durationId": "d0f617d6-726f-456e-8a5d-c15cafa96ea4"}}, {"head": {"id": "57a08682-66ad-4da7-8dba-39eac58085ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613765510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0919463c-bbc2-4586-a48a-fa53558c317b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613765646600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd872065-6525-4bdb-986a-7042da5ea1e4", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613778194700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba3ce5b-154b-4b16-94ac-dd06d9ea3fbc", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613805981400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f828fea3-c76a-4f9a-a9d5-97686f91b8e0", "name": "entry : default@GenerateLoaderJson cost memory 0.76519775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613806167600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1d53f98-e531-48f4-a1ff-499e7bca5b55", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613778168900, "endTime": 27613806397500}, "additional": {"logType": "info", "children": [], "durationId": "d0f617d6-726f-456e-8a5d-c15cafa96ea4"}}, {"head": {"id": "e703de44-3939-4806-ab35-86e3467da5ef", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613819601900, "endTime": 27613826908700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "add5b6af-77ba-4b48-8661-208a6a4a7ffd", "logId": "7b997ef0-0046-4a4c-ac36-94e2f173b39b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "add5b6af-77ba-4b48-8661-208a6a4a7ffd", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613816306900}, "additional": {"logType": "detail", "children": [], "durationId": "e703de44-3939-4806-ab35-86e3467da5ef"}}, {"head": {"id": "75feaccc-d051-4677-8596-b9a32fa8dd66", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613817602200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2ec1dc-2432-4f0d-9d54-1e91f1620189", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613817911400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5e1660f-528a-496c-8733-dce2545d745d", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613819621700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97160e0c-84fe-47bf-90ad-848dcb8509e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613824961900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a80c67-aff2-45da-a7a8-2ba1809a0988", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613825237000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7768e382-86ce-446d-b7bf-93c8198e5e7e", "name": "entry : default@ProcessLibs cost memory 0.12633514404296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613826657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47a19b4-cdc9-43db-9ab3-7b6989f6596a", "name": "runTaskFromQueue task cost before running: 981 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613826830300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b997ef0-0046-4a4c-ac36-94e2f173b39b", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613819601900, "endTime": 27613826908700, "totalTime": 7194900}, "additional": {"logType": "info", "children": [], "durationId": "e703de44-3939-4806-ab35-86e3467da5ef"}}, {"head": {"id": "f4db1b25-807c-4e60-998e-7d2442562db7", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613841390800, "endTime": 27613890957700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3582f7f6-13d8-4b4e-a298-4003218439d1", "logId": "14206181-2990-4765-b10b-0d240f900eeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3582f7f6-13d8-4b4e-a298-4003218439d1", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613834115400}, "additional": {"logType": "detail", "children": [], "durationId": "f4db1b25-807c-4e60-998e-7d2442562db7"}}, {"head": {"id": "0eb283eb-f622-434d-bdd9-3f775cb56fa6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613835233500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb661ba8-10aa-4bb5-bab6-0d7e16f1c0b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613835431900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2147861-7869-48cd-a0ac-79815a92acb1", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613836978400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd20270-cd8e-41e1-90ef-972615935fb1", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613841422000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde52ea1-bde6-49cb-8b0a-93231e349c3d", "name": "Incremental task entry:default@CompileResource pre-execution cost: 48 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613890524800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f8767f-e728-4ca9-bd0c-d14bfbf80489", "name": "entry : default@CompileResource cost memory -9.287574768066406", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613890833200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14206181-2990-4765-b10b-0d240f900eeb", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613841390800, "endTime": 27613890957700}, "additional": {"logType": "info", "children": [], "durationId": "f4db1b25-807c-4e60-998e-7d2442562db7"}}, {"head": {"id": "bb3b3957-d4b5-4e25-8ba2-ba18b5cee958", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613897438200, "endTime": 27613899000100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ac22d099-d862-440f-b7f0-fa6b4824629b", "logId": "9353b9f9-60fe-4a0a-8390-5c070e2575e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac22d099-d862-440f-b7f0-fa6b4824629b", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613894229300}, "additional": {"logType": "detail", "children": [], "durationId": "bb3b3957-d4b5-4e25-8ba2-ba18b5cee958"}}, {"head": {"id": "45428060-c3fd-4bdf-bc2d-e8d19d784486", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613894691800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f61e8216-7f4c-4fa7-a82b-a485bb094bc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613894832400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da6ef653-8f51-499c-bc40-e526a1758980", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613897451900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74cc3dad-2cf4-40dd-8219-a12ab7c09f22", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613897756500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7f4ba8-d233-4d0f-890d-6f8fee9a865f", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613898746700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97bc320c-f05f-4d2e-8b2c-90a06128c640", "name": "entry : default@DoNativeStrip cost memory 0.07741546630859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613898918100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9353b9f9-60fe-4a0a-8390-5c070e2575e3", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613897438200, "endTime": 27613899000100}, "additional": {"logType": "info", "children": [], "durationId": "bb3b3957-d4b5-4e25-8ba2-ba18b5cee958"}}, {"head": {"id": "bcf91451-1469-4117-b2fc-9810ba4cdba6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613906871100, "endTime": 27615309113700}, "additional": {"children": ["6a865136-19ae-4e80-80a9-92e5d9458654", "d90c7a02-953a-435a-9dfe-c9777e2448b3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "7e54c79e-46b5-4e0a-b781-b03a99d99e73", "logId": "00cbadbd-d331-401f-b78b-9cc82c9c827a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e54c79e-46b5-4e0a-b781-b03a99d99e73", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613901264400}, "additional": {"logType": "detail", "children": [], "durationId": "bcf91451-1469-4117-b2fc-9810ba4cdba6"}}, {"head": {"id": "12400a3f-64bf-40f4-84ff-5639f8c9e928", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613901882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449c708c-82d0-4eef-9bcf-42b9397c0a3c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613902026200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af7fdba-acca-470b-b6ee-f2c9283c165c", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613906885700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02423aa5-e272-465d-8e26-d0b576058c1a", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613928364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2913b92a-b383-41df-8d36-e04daa448ecf", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613928591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1257485b-9cf3-4a11-a5e2-e25d593804dd", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613946493100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c994a4-2280-46fa-bccf-3ac2328c4cb7", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613947047700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6609a068-c130-4d72-939a-ab057caf6db8", "name": "default@CompileArkTS work[33] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613948739800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a865136-19ae-4e80-80a9-92e5d9458654", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27614159651400, "endTime": 27615257765900}, "additional": {"children": ["cfb3b728-b670-44c5-a93b-afb8c528b5f0", "e352e745-9d75-42df-aba9-32790c4bc37d", "bb21251e-92a8-45e5-a93d-b42b32b24d28", "a2f44786-fcb2-40a8-9ce2-638f91dff9f5", "99908b00-061b-41f2-bae0-a1562efacfc9", "324f53c3-640d-46cf-9a0c-f5895cc9061d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bcf91451-1469-4117-b2fc-9810ba4cdba6", "logId": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e783caf8-7826-4566-b7be-111ce092ba88", "name": "default@CompileArkTS work[33] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613950039100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342aa738-f7b9-4a47-a701-fca883b704bd", "name": "default@CompileArkTS work[33] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613950172500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce0625ba-7783-46f3-b673-7c2841703b42", "name": "CopyResources startTime: 27613950253600", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613950256900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a73d92f7-a1a7-4f98-90d3-1899c8a37f60", "name": "default@CompileArkTS work[34] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613950407800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d90c7a02-953a-435a-9dfe-c9777e2448b3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27615296409800, "endTime": 27615308535000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bcf91451-1469-4117-b2fc-9810ba4cdba6", "logId": "79d3df0c-aaed-4069-8e42-1637efcfd7d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a068e176-9725-4210-b7bc-95a0bf8d7438", "name": "default@CompileArkTS work[34] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613951240700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15df36f1-38db-431b-844b-03b2d0e72b3b", "name": "default@CompileArkTS work[34] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613951354000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed60192-02fd-4995-83b7-8b6bb7aad18d", "name": "entry : default@CompileArkTS cost memory 1.6300735473632812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613951522200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "389361ef-97ff-4d72-a97e-8a179673f36e", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613958313700, "endTime": 27613961022300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "3796f154-a12e-49ee-a747-37992a7aa4d7", "logId": "26581c01-4e7a-4928-9207-d11271c4db48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3796f154-a12e-49ee-a747-37992a7aa4d7", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613952967700}, "additional": {"logType": "detail", "children": [], "durationId": "389361ef-97ff-4d72-a97e-8a179673f36e"}}, {"head": {"id": "e571a581-e9bd-4a29-995d-a6050e4631e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613953377100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4721798-d811-4aaf-a44c-35d36aa2db69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613953475100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91aa043c-35eb-45e4-9dcc-c5993d66f54a", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613958327000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd18d57a-c7aa-43ef-a836-3cb0a6c2aab7", "name": "entry : default@BuildJS cost memory 0.16173553466796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613960805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bf56399-67c7-4474-ae84-313e64951c45", "name": "runTaskFromQueue task cost before running: 1 s 115 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613960951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26581c01-4e7a-4928-9207-d11271c4db48", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613958313700, "endTime": 27613961022300, "totalTime": 2612500}, "additional": {"logType": "info", "children": [], "durationId": "389361ef-97ff-4d72-a97e-8a179673f36e"}}, {"head": {"id": "726acb6c-cafd-4be1-bb72-8d1f1e6e4656", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613965703100, "endTime": 27613967754900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ff420c1e-f3ad-4dad-97ed-bd9a0a02dbc2", "logId": "72be4f02-7e98-460e-ab61-4546d53bd6c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff420c1e-f3ad-4dad-97ed-bd9a0a02dbc2", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613962914000}, "additional": {"logType": "detail", "children": [], "durationId": "726acb6c-cafd-4be1-bb72-8d1f1e6e4656"}}, {"head": {"id": "c24ef603-594e-447b-9c4f-21bb8dc7e06c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613963323500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58749e9f-c9fe-4c8a-bea7-9b77460c6bf5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613963438100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "938ce093-f89b-4106-8fa5-5e874efefbe8", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613965718900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6233d325-d201-4ed1-91cb-f91ff0ee7e97", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613966507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df0735f-80db-48d2-b4e7-a255757f9b62", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613967558900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f18d52f3-0be1-4456-987a-68277b8f37b6", "name": "entry : default@CacheNativeLibs cost memory 0.091552734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613967680300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72be4f02-7e98-460e-ab61-4546d53bd6c9", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613965703100, "endTime": 27613967754900}, "additional": {"logType": "info", "children": [], "durationId": "726acb6c-cafd-4be1-bb72-8d1f1e6e4656"}}, {"head": {"id": "ba87eede-722e-46eb-8cd1-24d125fbd70c", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614158981400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27a987f-403d-4be4-8b12-6fdb07c16806", "name": "default@CompileArkTS work[33] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614159222400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e39d23-7d9b-40bb-a183-a125e152ae4f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614174628800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1026abff-fd97-4ed1-81af-9ef7cfb9ebcb", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614174842600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60058ab3-a457-4434-bf95-b01f46d6f14a", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614175336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97f0e7e-5eaa-4dc2-b0a6-55c6cac8b318", "name": "default@CompileArkTS work[34] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614177071200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c71751-48e6-42bf-88a9-341c726fe178", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615258047400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb3b728-b670-44c5-a93b-afb8c528b5f0", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27614159916400, "endTime": 27614165017800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a865136-19ae-4e80-80a9-92e5d9458654", "logId": "c95d5e54-38df-4767-b9b3-18b19d3535a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c95d5e54-38df-4767-b9b3-18b19d3535a4", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614159916400, "endTime": 27614165017800}, "additional": {"logType": "info", "children": [], "durationId": "cfb3b728-b670-44c5-a93b-afb8c528b5f0", "parent": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}}, {"head": {"id": "e352e745-9d75-42df-aba9-32790c4bc37d", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27614165040800, "endTime": 27614165191900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a865136-19ae-4e80-80a9-92e5d9458654", "logId": "9d2f9f20-2843-4b4b-b0b2-d4d9e3330076"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d2f9f20-2843-4b4b-b0b2-d4d9e3330076", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614165040800, "endTime": 27614165191900}, "additional": {"logType": "info", "children": [], "durationId": "e352e745-9d75-42df-aba9-32790c4bc37d", "parent": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}}, {"head": {"id": "bb21251e-92a8-45e5-a93d-b42b32b24d28", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27614165202200, "endTime": 27614165236200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a865136-19ae-4e80-80a9-92e5d9458654", "logId": "8cb9c1df-105a-4eb1-bb14-784fd1ed7900"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8cb9c1df-105a-4eb1-bb14-784fd1ed7900", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614165202200, "endTime": 27614165236200}, "additional": {"logType": "info", "children": [], "durationId": "bb21251e-92a8-45e5-a93d-b42b32b24d28", "parent": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}}, {"head": {"id": "a2f44786-fcb2-40a8-9ce2-638f91dff9f5", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27614165253300, "endTime": 27615187504400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a865136-19ae-4e80-80a9-92e5d9458654", "logId": "c5a1c88f-d7a7-409a-9be5-c77bcfd53ffb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5a1c88f-d7a7-409a-9be5-c77bcfd53ffb", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27614165253300, "endTime": 27615187504400}, "additional": {"logType": "info", "children": [], "durationId": "a2f44786-fcb2-40a8-9ce2-638f91dff9f5", "parent": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}}, {"head": {"id": "99908b00-061b-41f2-bae0-a1562efacfc9", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615187529100, "endTime": 27615193186800}, "additional": {"children": ["c8124117-3ce6-4f79-a48a-33282e32439e", "389732f7-4095-4029-8d54-653f7852976b", "f4bf59bd-a04a-4595-a00b-3f5d88c923a4"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a865136-19ae-4e80-80a9-92e5d9458654", "logId": "672edb0a-058d-41a0-864e-20fa1a10eb88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "672edb0a-058d-41a0-864e-20fa1a10eb88", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615187529100, "endTime": 27615193186800}, "additional": {"logType": "info", "children": ["816e980f-6680-49c2-838c-14b5c8a9ed40", "d42dc5e6-ee51-4d34-851d-b08b31c47338", "75c0d2d2-ba7a-4b48-bbe5-b54ac9335707"], "durationId": "99908b00-061b-41f2-bae0-a1562efacfc9", "parent": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}}, {"head": {"id": "c8124117-3ce6-4f79-a48a-33282e32439e", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615187553100, "endTime": 27615187558500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "99908b00-061b-41f2-bae0-a1562efacfc9", "logId": "816e980f-6680-49c2-838c-14b5c8a9ed40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "816e980f-6680-49c2-838c-14b5c8a9ed40", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615187553100, "endTime": 27615187558500}, "additional": {"logType": "info", "children": [], "durationId": "c8124117-3ce6-4f79-a48a-33282e32439e", "parent": "672edb0a-058d-41a0-864e-20fa1a10eb88"}}, {"head": {"id": "389732f7-4095-4029-8d54-653f7852976b", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615187561800, "endTime": 27615190566000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "99908b00-061b-41f2-bae0-a1562efacfc9", "logId": "d42dc5e6-ee51-4d34-851d-b08b31c47338"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d42dc5e6-ee51-4d34-851d-b08b31c47338", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615187561800, "endTime": 27615190566000}, "additional": {"logType": "info", "children": [], "durationId": "389732f7-4095-4029-8d54-653f7852976b", "parent": "672edb0a-058d-41a0-864e-20fa1a10eb88"}}, {"head": {"id": "f4bf59bd-a04a-4595-a00b-3f5d88c923a4", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615190569600, "endTime": 27615193173500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "99908b00-061b-41f2-bae0-a1562efacfc9", "logId": "75c0d2d2-ba7a-4b48-bbe5-b54ac9335707"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75c0d2d2-ba7a-4b48-bbe5-b54ac9335707", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615190569600, "endTime": 27615193173500}, "additional": {"logType": "info", "children": [], "durationId": "f4bf59bd-a04a-4595-a00b-3f5d88c923a4", "parent": "672edb0a-058d-41a0-864e-20fa1a10eb88"}}, {"head": {"id": "324f53c3-640d-46cf-9a0c-f5895cc9061d", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615193205300, "endTime": 27615257597400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a865136-19ae-4e80-80a9-92e5d9458654", "logId": "4fcc3d9f-27a0-4961-83c0-aa5eafb64a5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fcc3d9f-27a0-4961-83c0-aa5eafb64a5f", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615193205300, "endTime": 27615257597400}, "additional": {"logType": "info", "children": [], "durationId": "324f53c3-640d-46cf-9a0c-f5895cc9061d", "parent": "19657f9b-2db9-47c0-ba46-3863b954c7c1"}}, {"head": {"id": "2ff67105-efd0-42c3-95c9-776246c479a0", "name": "default@CompileArkTS work[33] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615263782600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19657f9b-2db9-47c0-ba46-3863b954c7c1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27614159651400, "endTime": 27615257765900}, "additional": {"logType": "info", "children": ["c95d5e54-38df-4767-b9b3-18b19d3535a4", "9d2f9f20-2843-4b4b-b0b2-d4d9e3330076", "8cb9c1df-105a-4eb1-bb14-784fd1ed7900", "c5a1c88f-d7a7-409a-9be5-c77bcfd53ffb", "672edb0a-058d-41a0-864e-20fa1a10eb88", "4fcc3d9f-27a0-4961-83c0-aa5eafb64a5f"], "durationId": "6a865136-19ae-4e80-80a9-92e5d9458654", "parent": "00cbadbd-d331-401f-b78b-9cc82c9c827a"}}, {"head": {"id": "ef64a7f4-2c07-4c50-89a1-9b955d5bd44e", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615263922400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc36f17a-0379-45a6-8a73-ba56c1d9726c", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615308695500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47e740c-e11f-4b34-9afa-1a573ef21027", "name": "CopyResources is end, endTime: 27615308877800", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615308883600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51effe8c-6f0a-43a9-9e8a-80566775dd79", "name": "default@CompileArkTS work[34] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615308968100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79d3df0c-aaed-4069-8e42-1637efcfd7d4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27615296409800, "endTime": 27615308535000}, "additional": {"logType": "info", "children": [], "durationId": "d90c7a02-953a-435a-9dfe-c9777e2448b3", "parent": "00cbadbd-d331-401f-b78b-9cc82c9c827a"}}, {"head": {"id": "332790f6-eff9-4bb4-98cc-9f36ea73c020", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615309052100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00cbadbd-d331-401f-b78b-9cc82c9c827a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27613906871100, "endTime": 27615309113700, "totalTime": 1154963400}, "additional": {"logType": "info", "children": ["19657f9b-2db9-47c0-ba46-3863b954c7c1", "79d3df0c-aaed-4069-8e42-1637efcfd7d4"], "durationId": "bcf91451-1469-4117-b2fc-9810ba4cdba6"}}, {"head": {"id": "6d6fa50c-062b-4a9a-b468-be64d329d028", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615314541800, "endTime": 27615315487400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "17ee0a37-32a4-4a3e-b9c6-c716bdd1ddfc", "logId": "615b408c-c85a-4282-9c5d-66d2da227c73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17ee0a37-32a4-4a3e-b9c6-c716bdd1ddfc", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615313113600}, "additional": {"logType": "detail", "children": [], "durationId": "6d6fa50c-062b-4a9a-b468-be64d329d028"}}, {"head": {"id": "ede30887-b743-4a7a-a160-8a52b2dcfc1f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615313541800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82962f5-757e-486e-bbd8-4bf2b9b67d70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615313640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df929f7e-bf9b-4e2e-9b3e-b0d9335534ba", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615314550800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c540961-e836-45e0-a032-a56c6975421e", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615314751000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0c6c67-59d9-4610-90df-66f9eacf1eb3", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615315330000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1bfc8e-c565-48bf-ac21-77b2dea8dd0b", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07483673095703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615315418100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "615b408c-c85a-4282-9c5d-66d2da227c73", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615314541800, "endTime": 27615315487400}, "additional": {"logType": "info", "children": [], "durationId": "6d6fa50c-062b-4a9a-b468-be64d329d028"}}, {"head": {"id": "eba4e886-fca2-478e-bb00-e4ecf9ec4475", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615325509400, "endTime": 27615846524600}, "additional": {"children": ["c3ad34af-9a40-4f47-ac1f-77c11efaaf6c", "f5b08039-869e-480c-9ba8-cd2e5eb3c79f", "299e79b8-021a-4113-b56c-a4834f6581e5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "7be0f209-65df-4e70-a778-404d39abdadf", "logId": "f9809f92-9254-4026-854e-ac0ca20a7b0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7be0f209-65df-4e70-a778-404d39abdadf", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615318554800}, "additional": {"logType": "detail", "children": [], "durationId": "eba4e886-fca2-478e-bb00-e4ecf9ec4475"}}, {"head": {"id": "b4318bc5-449b-4507-8991-4ff37e7f93d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615318903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c423cc76-4930-4279-a71a-179bad2550e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615318999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4d0425a-0cdb-410d-b774-089a4c31f345", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615325528500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b87b492-832b-4033-926a-2274fcc81afc", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615338195100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efeab340-1d30-483a-b688-411e04308fee", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615338370500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b890981-0812-478a-a7d3-7a26b23ef6e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615338466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8348d24-714c-46a7-be68-cfa03e23cc82", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615338521300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ad34af-9a40-4f47-ac1f-77c11efaaf6c", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615339523800, "endTime": 27615341236800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eba4e886-fca2-478e-bb00-e4ecf9ec4475", "logId": "8ae4eb8a-ebca-496a-91fe-cac379be8032"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22301c4c-09a9-4243-aba3-bdd885c7b585", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615341060300}, "additional": {"logType": "debug", "children": [], "durationId": "eba4e886-fca2-478e-bb00-e4ecf9ec4475"}}, {"head": {"id": "8ae4eb8a-ebca-496a-91fe-cac379be8032", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615339523800, "endTime": 27615341236800}, "additional": {"logType": "info", "children": [], "durationId": "c3ad34af-9a40-4f47-ac1f-77c11efaaf6c", "parent": "f9809f92-9254-4026-854e-ac0ca20a7b0f"}}, {"head": {"id": "f5b08039-869e-480c-9ba8-cd2e5eb3c79f", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615341895700, "endTime": 27615343715800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eba4e886-fca2-478e-bb00-e4ecf9ec4475", "logId": "b92f839a-5eeb-46a6-9a54-6d916d486f44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83d82890-0549-4697-95da-f0cff99fb710", "name": "default@PackageHap work[35] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615342722800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "299e79b8-021a-4113-b56c-a4834f6581e5", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615343680900, "endTime": 27615845586300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "eba4e886-fca2-478e-bb00-e4ecf9ec4475", "logId": "6f64bc54-73aa-44b2-a0f9-fcce417c5a36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c11e9b5-f5ba-4196-b3dd-3564a3882086", "name": "default@PackageHap work[35] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615343432700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c73430cc-b89b-4643-8258-3e4382e2e04b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615343516000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12abc759-96ef-4c36-91a5-40f3687cbb91", "name": "default@PackageHap work[35] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615343610100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14eb237-1ed2-4603-9915-aa64d62011aa", "name": "default@PackageHap work[35] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615343665300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b92f839a-5eeb-46a6-9a54-6d916d486f44", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615341895700, "endTime": 27615343715800}, "additional": {"logType": "info", "children": [], "durationId": "f5b08039-869e-480c-9ba8-cd2e5eb3c79f", "parent": "f9809f92-9254-4026-854e-ac0ca20a7b0f"}}, {"head": {"id": "a603e329-59db-454e-a633-9bbbd35e6e5e", "name": "entry : default@PackageHap cost memory 1.3136367797851562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615347830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a491dc8a-7e70-4700-b3d6-23230a67320d", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615845867000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0aeb644-4640-4393-895d-3c98cf641051", "name": "default@PackageHap work[35] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615846333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f64bc54-73aa-44b2-a0f9-fcce417c5a36", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27615343680900, "endTime": 27615845586300}, "additional": {"logType": "info", "children": [], "durationId": "299e79b8-021a-4113-b56c-a4834f6581e5", "parent": "f9809f92-9254-4026-854e-ac0ca20a7b0f"}}, {"head": {"id": "aa560de1-e2b9-4757-8c6e-4551d20fdad7", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615846454000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9809f92-9254-4026-854e-ac0ca20a7b0f", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615325509400, "endTime": 27615846524600, "totalTime": 520085700}, "additional": {"logType": "info", "children": ["8ae4eb8a-ebca-496a-91fe-cac379be8032", "b92f839a-5eeb-46a6-9a54-6d916d486f44", "6f64bc54-73aa-44b2-a0f9-fcce417c5a36"], "durationId": "eba4e886-fca2-478e-bb00-e4ecf9ec4475"}}, {"head": {"id": "ac26f412-4984-4a46-8402-d2862e6a15b1", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615857257600, "endTime": 27615860528200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "a5109584-4bdd-487c-b757-9f437ab47284", "logId": "2259afca-d0be-4593-bff1-9b39a5dbdba2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5109584-4bdd-487c-b757-9f437ab47284", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615852311500}, "additional": {"logType": "detail", "children": [], "durationId": "ac26f412-4984-4a46-8402-d2862e6a15b1"}}, {"head": {"id": "aed1ea81-b3fd-42cf-9d7c-8769996a8e74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615852717200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb606d3-98e7-42c6-a82b-573dfa408841", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615852833600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97a2be9-df9c-41c8-ba7a-0aadd62811e9", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615857271000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab7f857-e26e-4906-ba6a-625733d10931", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615858054800}, "additional": {"logType": "warn", "children": [], "durationId": "ac26f412-4984-4a46-8402-d2862e6a15b1"}}, {"head": {"id": "79bfc2ba-245e-43df-a686-48d995d68083", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615859417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bd778f9-6cb0-4543-8539-a2e8e4a39c2d", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615859561600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d65e8a-4296-4ba0-975d-5440d014a8f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615859662600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a98f6c8a-e98a-4de7-81c3-39eba33bfe18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615859752600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdbb9d0-71e9-46f8-98dc-43202105a235", "name": "entry : default@SignHap cost memory 0.12448883056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615860354400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a2b8144-d2de-4ca2-a4e2-e22802105f70", "name": "runTaskFromQueue task cost before running: 3 s 15 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615860463500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2259afca-d0be-4593-bff1-9b39a5dbdba2", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615857257600, "endTime": 27615860528200, "totalTime": 3187800}, "additional": {"logType": "info", "children": [], "durationId": "ac26f412-4984-4a46-8402-d2862e6a15b1"}}, {"head": {"id": "4db859a0-e0f5-48a0-b485-f8d41a4ca725", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615865362400, "endTime": 27615875763800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6593c84f-22e7-407b-9739-ab4a1740bafc", "logId": "09e29e14-5e27-4c7c-ab22-a4ab1e761ae4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6593c84f-22e7-407b-9739-ab4a1740bafc", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615863090300}, "additional": {"logType": "detail", "children": [], "durationId": "4db859a0-e0f5-48a0-b485-f8d41a4ca725"}}, {"head": {"id": "052dcf86-d28e-4179-b36c-aa50e049a37d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615863982100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aad3092-21d6-4308-88a6-4157832f181d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615864104800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e94dda-9b88-4f2d-8eb4-95902159de85", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615865375000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9494ae7c-b54e-44d7-b5e9-ed1a10d6d6ee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615875070500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "982a25c0-263e-4230-804c-d1724dd1721d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615875271300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0961ea0-f7a5-4226-9686-86392d833e93", "name": "entry : default@CollectDebugSymbol cost memory 0.24015045166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615875419300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ed82f9-8c96-4fe6-9323-0d2d78375142", "name": "runTaskFromQueue task cost before running: 3 s 30 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615875533400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e29e14-5e27-4c7c-ab22-a4ab1e761ae4", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615865362400, "endTime": 27615875763800, "totalTime": 10140600}, "additional": {"logType": "info", "children": [], "durationId": "4db859a0-e0f5-48a0-b485-f8d41a4ca725"}}, {"head": {"id": "67212554-1cd1-4dd3-9c0c-ac92fd5c5054", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615878557700, "endTime": 27615879263700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "04b426fc-2200-4d67-9eb3-c0f74d0f8887", "logId": "e4caa75a-d90d-493f-be4b-ffa886a2de9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04b426fc-2200-4d67-9eb3-c0f74d0f8887", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615878495900}, "additional": {"logType": "detail", "children": [], "durationId": "67212554-1cd1-4dd3-9c0c-ac92fd5c5054"}}, {"head": {"id": "48ca623a-63b8-4043-96f4-6083a0518e88", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615878567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a4c2507-410b-419d-adb4-b2ce1a60b967", "name": "entry : assembleHap cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615879003400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0fd32e-59b5-4dff-9890-9baaf8ac71d2", "name": "runTaskFromQueue task cost before running: 3 s 34 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615879192000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4caa75a-d90d-493f-be4b-ffa886a2de9b", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615878557700, "endTime": 27615879263700, "totalTime": 607400}, "additional": {"logType": "info", "children": [], "durationId": "67212554-1cd1-4dd3-9c0c-ac92fd5c5054"}}, {"head": {"id": "dc0177dc-b092-41ca-819c-52cc5e250ae8", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615888449100, "endTime": 27615888476200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "772532e7-8856-4cc1-92ea-cdca2e9d7862", "logId": "da714e0b-b225-4068-a490-973e6a357d88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da714e0b-b225-4068-a490-973e6a357d88", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615888449100, "endTime": 27615888476200}, "additional": {"logType": "info", "children": [], "durationId": "dc0177dc-b092-41ca-819c-52cc5e250ae8"}}, {"head": {"id": "4b68d0ba-d932-4cb0-944c-55d705816129", "name": "BUILD SUCCESSFUL in 3 s 43 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615888564100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "b15337c4-fa1e-4505-8b46-abb60c3155b6", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27612846089400, "endTime": 27615890608300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 9}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ee1b22bf-72a4-484f-b2b4-f2387956de00", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615890815900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e385b8ac-ed1d-49c4-8cf4-0e2b8aee67b1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615890974100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4241ebad-c990-4bfd-8051-785bf30523a1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615891061700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1eb4132-06dc-4455-b53f-d14c5969c134", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615891143000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c420588-bdce-4760-95a4-7eb303e527e1", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615891250700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "027f7087-211a-448e-8c9c-2da09e584fd7", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615891741200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9179cb21-0535-42a1-8036-af7f23b86593", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615892488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1764f8-5a4f-43db-9757-1d09cb325ebc", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615892737400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4412c3ea-e46b-4aee-be30-0763d8b9f7f3", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615892816600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77889df7-0b09-45f4-a88d-5e0af3979123", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615892884500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffbb633a-6d0f-40c4-bec5-2b69fd13a942", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615893136200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bdb353b-463e-4a09-88b3-1d8c3e56b9b4", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615894229900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75f225ce-92bb-4f9a-a8d6-cc871ed883cd", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615894565500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1d6c5b0-1363-4a78-a607-12cb8816c165", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615894706000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c28de66-0a64-46e9-8144-1bd40ef4c1b0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615894779000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ba95767-2fd6-4f12-9e7f-3d4cf49376d5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615894890500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045deb54-37d2-4866-ae49-d4f2a1c42999", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615894964600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e3830d-7c4a-42e8-8ec5-f52378ae0e83", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615895287100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f3dd9d-93ca-4bfd-a732-2c199a5dc6fd", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615895502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13eb0db9-c71e-4e32-8f43-8001b675f914", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615895688900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d65fea-660b-4adb-8cfc-73e64f6b07bd", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615896113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5ce3eb1-be6a-4e93-abf5-33795e070f66", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615896190200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62a5d70b-f581-485c-bf6e-6cfc7c2629f3", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615896242500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0a69b7-4669-4499-b0c5-e20f33aed221", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615898221700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b19722e-bddf-4c55-8880-bed901e12aad", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615898822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b15c398-c384-4f0b-944a-b2946c9c74e1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615901646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed463980-7a11-4cc4-84d9-cdd603de1a37", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615902213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f2cba4c-51ef-424b-be95-391e6374399c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615902508400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f048a552-e56b-462d-b96a-5b59a6389a1e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615903587300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "236245ac-d107-4efd-8f18-98f79b63fd63", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615903712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b9fc4e7-9464-402f-9744-e0f43a6027d2", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615904414100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "563ae070-d85f-4649-bf28-f78bb1218c4c", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615904791400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2acbbf1f-7041-4ae4-a651-2b2698ba96b0", "name": "Incremental task entry:default@CompileArkTS post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615905530800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d2149d5-164c-4e4b-9cbb-edbce39335e9", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615908202300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8807210a-7fdf-4e3b-829e-4e4e90e85199", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615908724600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb8bbdc-7d17-4270-9c0e-c68ab5f4b693", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615911155400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e59e89ff-6e17-46dc-aaf1-c907ce9eb62c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615911735100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e1f9e81-a98e-4b8f-ac81-5322553a5bf4", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615912050600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7221b375-08d7-4fc2-95a3-898f0bc64bab", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615913414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8ffbea7-258b-42a6-a8a5-3db31d0de54b", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615913940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baac0ed7-8b9a-4d86-b8c0-3df96371dfdd", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615914050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d0d1944-9a2d-4ffb-a617-52a1199859bc", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615914160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33e75f10-bb68-44bc-a547-40d762bd890c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615915704200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8fcec7d-5e00-4368-a3db-d7398a8c13df", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615918459300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "260eebd2-a781-4d21-8870-d96f903d00f7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615919941200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad5aedc-b29b-4fa6-b222-fc3a6ae97513", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615931464300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effe5d18-7a46-499d-a12a-b3fd6d507f68", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615931930800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4c1e72-ae13-4e2b-9722-717917fcf713", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615934781500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38520d11-b53c-4515-b4a5-c8a80dfff529", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615934946000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803bfb2c-ae1a-4a21-9d9c-90700abfbe54", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615935388800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0467717-5b3f-496b-8ce7-723ce3e0d374", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615936318300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5a05e2-c71a-4cbd-a3dd-0646581b7944", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615936708900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f7b2ec4-57af-4efa-b2be-3b5bccf1c2bb", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615937098800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24facd31-47d2-4995-b76e-e0d2aa9e1463", "name": "Incremental task entry:default@PackageHap post-execution cost:24 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615937732400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25391924-903a-4bd4-b33c-5978f9f0af47", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615938204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f67d2a5a-3918-40d0-a8b9-7f0d9aed09cf", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615938415800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a371e613-3f78-404d-88ff-aa37ece75358", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615938917900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7cb522-8e1a-4eb6-94bd-81a973a2cf8d", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615945080100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70e5bc0e-7b74-452a-b2a4-62ecc249c3b5", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615945542400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd964198-9a9c-47bb-90f0-5d9cc8fa49e2", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615946194300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db47444b-52e6-49db-96f7-1e070957cc55", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615946722500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}