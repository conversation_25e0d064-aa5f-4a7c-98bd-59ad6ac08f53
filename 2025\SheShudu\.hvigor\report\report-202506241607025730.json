{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "4bcef9a6-0db4-4bfd-9726-4bddcf51e170", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962793926000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bf0a54f-ebed-4a9b-8829-cc32bf4a45fe", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962798995400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18cf7aef-7d41-4941-91b4-a5b4f17227e4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962799348100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d42e324-5452-402b-b572-9e8899c4297b", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26962803041100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c58dc593-ed04-475b-b8c7-2da649c939ff", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454466261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d2aa49-e256-4418-8594-0d2442ceabbb", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454473694400, "endTime": 27454867668900}, "additional": {"children": ["989abdfc-a97a-439a-9de8-d201ea94b218", "d2e15c95-425c-4e45-97d6-42f758765f20", "eca50bad-ea46-48ae-927e-9f24f46dab67", "59fab7bf-5bd5-4fea-b310-a88a6d49d2d9", "091378bf-bb81-444b-99d4-51623212818e", "7c1b9913-2b72-400f-b25e-7483d90b2a4b", "f0c07fad-d649-4b4d-bf6d-02c09f5af156"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "989abdfc-a97a-439a-9de8-d201ea94b218", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454473696900, "endTime": 27454489876700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "073ceb31-8ea4-4dd3-8131-b8568309e4c7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2e15c95-425c-4e45-97d6-42f758765f20", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454489982600, "endTime": 27454866398400}, "additional": {"children": ["8a65df5e-d527-4683-abc4-fba1b1db780c", "14a2713a-f382-4e4d-9069-a35a9a4189c4", "cd73d97a-f7e0-4389-85cf-39f0888164a6", "3f77e4e5-3694-4508-a24d-f5c5bec8e660", "d5ba43cd-7133-4b07-9b21-04e528a727cf", "6f4c741d-89e3-4356-a9b0-d2913a5a4d96", "c0a75902-96ad-4bab-b46c-e4730df7e655", "eef8c803-e2f8-492a-b6ac-9a8ed328ca36", "7d674ef7-2c04-4163-be69-238ab5b02a64"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "5d084f80-b66b-4204-80d6-c8374bf26330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eca50bad-ea46-48ae-927e-9f24f46dab67", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454866422100, "endTime": 27454867658900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "bd216dc8-2295-4a6a-ab42-3d8f971abee7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59fab7bf-5bd5-4fea-b310-a88a6d49d2d9", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454867664600, "endTime": 27454867665800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "847208a4-d5d3-4ea0-890d-536374e3f440"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "091378bf-bb81-444b-99d4-51623212818e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454477675300, "endTime": 27454477919200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "8fdbea43-2d80-4a44-9c1b-dcdf59c1aa8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fdbea43-2d80-4a44-9c1b-dcdf59c1aa8b", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454477675300, "endTime": 27454477919200}, "additional": {"logType": "info", "children": [], "durationId": "091378bf-bb81-444b-99d4-51623212818e", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "7c1b9913-2b72-400f-b25e-7483d90b2a4b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454482681300, "endTime": 27454482710300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "74467f57-eb71-4b85-9d8d-513bc00ee0fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74467f57-eb71-4b85-9d8d-513bc00ee0fa", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454482681300, "endTime": 27454482710300}, "additional": {"logType": "info", "children": [], "durationId": "7c1b9913-2b72-400f-b25e-7483d90b2a4b", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "52078194-67f4-4ad7-9951-94db62f053c7", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454482756800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9a6c53c-799f-4400-9718-14bb40b6a55e", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454489633200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "073ceb31-8ea4-4dd3-8131-b8568309e4c7", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454473696900, "endTime": 27454489876700}, "additional": {"logType": "info", "children": [], "durationId": "989abdfc-a97a-439a-9de8-d201ea94b218", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "8a65df5e-d527-4683-abc4-fba1b1db780c", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454495786700, "endTime": 27454495796200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "8e9e995c-811a-403e-90f3-ce7d69ac7469"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14a2713a-f382-4e4d-9069-a35a9a4189c4", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454495810400, "endTime": 27454502561400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "ffdf48c4-35f6-4cdb-b52c-35c145c3a924"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd73d97a-f7e0-4389-85cf-39f0888164a6", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454502594000, "endTime": 27454658233100}, "additional": {"children": ["33ab5efb-fb7c-490b-987c-bfee828cdaf2", "5bc24d81-8eee-4fd5-adda-ee046d4e65ab", "e19db306-4b71-4820-9e9e-ddee8cbe5004"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "9d15da8d-570b-4859-a93a-23f154e3c659"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f77e4e5-3694-4508-a24d-f5c5bec8e660", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454658264200, "endTime": 27454713178100}, "additional": {"children": ["9ced6287-b4e8-4d39-bb99-589a8fb8f1f2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "8146fec7-62f9-4dea-ade9-84d120051600"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5ba43cd-7133-4b07-9b21-04e528a727cf", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454713189400, "endTime": 27454834961400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "d28969ad-46f1-4ef7-a334-bc3b8e2d9b62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f4c741d-89e3-4356-a9b0-d2913a5a4d96", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454836065400, "endTime": 27454854133000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "e1c5d249-8010-489a-9f84-8734a783a38b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0a75902-96ad-4bab-b46c-e4730df7e655", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454854155600, "endTime": 27454866146100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "b24d5706-55e4-447a-9429-a1fd610fcdd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eef8c803-e2f8-492a-b6ac-9a8ed328ca36", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454866167500, "endTime": 27454866383000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "e8833b46-2f48-45ba-8675-111c2c28afca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e9e995c-811a-403e-90f3-ce7d69ac7469", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454495786700, "endTime": 27454495796200}, "additional": {"logType": "info", "children": [], "durationId": "8a65df5e-d527-4683-abc4-fba1b1db780c", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "ffdf48c4-35f6-4cdb-b52c-35c145c3a924", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454495810400, "endTime": 27454502561400}, "additional": {"logType": "info", "children": [], "durationId": "14a2713a-f382-4e4d-9069-a35a9a4189c4", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "33ab5efb-fb7c-490b-987c-bfee828cdaf2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454503636500, "endTime": 27454503662300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd73d97a-f7e0-4389-85cf-39f0888164a6", "logId": "5445ff2c-2182-4854-8aa7-2ccb504f55c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5445ff2c-2182-4854-8aa7-2ccb504f55c6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454503636500, "endTime": 27454503662300}, "additional": {"logType": "info", "children": [], "durationId": "33ab5efb-fb7c-490b-987c-bfee828cdaf2", "parent": "9d15da8d-570b-4859-a93a-23f154e3c659"}}, {"head": {"id": "5bc24d81-8eee-4fd5-adda-ee046d4e65ab", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454507120900, "endTime": 27454656283400}, "additional": {"children": ["97b2465d-ebe1-45e2-816b-fb1ab5736799", "cd03bf89-e4f6-45e8-9a6b-a0cfaa775a82"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd73d97a-f7e0-4389-85cf-39f0888164a6", "logId": "0ec31e1f-d247-46a9-b085-a28099974ee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97b2465d-ebe1-45e2-816b-fb1ab5736799", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454507126100, "endTime": 27454515309000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5bc24d81-8eee-4fd5-adda-ee046d4e65ab", "logId": "e11d5b45-9506-4667-8c5b-19fdcef3980f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd03bf89-e4f6-45e8-9a6b-a0cfaa775a82", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454515365700, "endTime": 27454656214600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5bc24d81-8eee-4fd5-adda-ee046d4e65ab", "logId": "b770a4cb-33d8-4b3c-8fe3-ce0be6c5fd2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dcfa986-dd59-4f91-a37e-cadfafe85634", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454507138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7ab8a4-aada-4da5-b3a5-f13b68092764", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454515056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11d5b45-9506-4667-8c5b-19fdcef3980f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454507126100, "endTime": 27454515309000}, "additional": {"logType": "info", "children": [], "durationId": "97b2465d-ebe1-45e2-816b-fb1ab5736799", "parent": "0ec31e1f-d247-46a9-b085-a28099974ee9"}}, {"head": {"id": "f9f68904-e32e-46dc-8489-cd09ad6028e2", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454515403200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b62658c-f179-4e14-bc59-3f34cb9e128a", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454526263100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87330969-e5b3-4f97-aafd-bf115a10c034", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454526515800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fa28a1-b89c-4886-b7f0-626dab40c1fa", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454526674800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20e80381-c07a-4dab-84f9-0e8725b48e9b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454526766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20205e8-26c6-4064-9b08-e31970d60d0c", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454529568000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9c7166-0797-4ff0-9157-6eb62a73f621", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454537133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6b0f75-fb14-4f9b-b685-a9d0485dfcaf", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454562989000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "496c3886-deca-408f-bff3-d1f937378156", "name": "Sdk init in 88 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454626222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1e46bc9-693d-4bab-98cc-1573116c99e0", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454626419400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 7}, "markType": "other"}}, {"head": {"id": "5a7c87fc-a3d0-4f0a-8ac4-b85ced5b4d24", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454626436400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 7}, "markType": "other"}}, {"head": {"id": "ca54aa7c-a4a1-4768-a329-63e5d84928e8", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454655841500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f6ba17f-be5a-4562-aca2-a6751ae02367", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454656032100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaa3f306-b884-4b5b-81e1-355ecb9dcdc2", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454656101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "173f6fd5-fc32-4271-8865-fa979e0f67ed", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454656160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b770a4cb-33d8-4b3c-8fe3-ce0be6c5fd2b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454515365700, "endTime": 27454656214600}, "additional": {"logType": "info", "children": [], "durationId": "cd03bf89-e4f6-45e8-9a6b-a0cfaa775a82", "parent": "0ec31e1f-d247-46a9-b085-a28099974ee9"}}, {"head": {"id": "0ec31e1f-d247-46a9-b085-a28099974ee9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454507120900, "endTime": 27454656283400}, "additional": {"logType": "info", "children": ["e11d5b45-9506-4667-8c5b-19fdcef3980f", "b770a4cb-33d8-4b3c-8fe3-ce0be6c5fd2b"], "durationId": "5bc24d81-8eee-4fd5-adda-ee046d4e65ab", "parent": "9d15da8d-570b-4859-a93a-23f154e3c659"}}, {"head": {"id": "e19db306-4b71-4820-9e9e-ddee8cbe5004", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454658046000, "endTime": 27454658073000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd73d97a-f7e0-4389-85cf-39f0888164a6", "logId": "775947b7-3279-424e-b2c1-77559cf58e15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "775947b7-3279-424e-b2c1-77559cf58e15", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454658046000, "endTime": 27454658073000}, "additional": {"logType": "info", "children": [], "durationId": "e19db306-4b71-4820-9e9e-ddee8cbe5004", "parent": "9d15da8d-570b-4859-a93a-23f154e3c659"}}, {"head": {"id": "9d15da8d-570b-4859-a93a-23f154e3c659", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454502594000, "endTime": 27454658233100}, "additional": {"logType": "info", "children": ["5445ff2c-2182-4854-8aa7-2ccb504f55c6", "0ec31e1f-d247-46a9-b085-a28099974ee9", "775947b7-3279-424e-b2c1-77559cf58e15"], "durationId": "cd73d97a-f7e0-4389-85cf-39f0888164a6", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "9ced6287-b4e8-4d39-bb99-589a8fb8f1f2", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454659563400, "endTime": 27454713160200}, "additional": {"children": ["f66549dd-f8fe-4952-8d10-db21ce946d33", "167eee4b-bb8f-4bf1-b6f8-88d6ed32f3cb", "02cbac45-6b8d-4b40-9f1a-8db678c2f25a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3f77e4e5-3694-4508-a24d-f5c5bec8e660", "logId": "9a9ab01c-db4d-4043-9ca5-5bec2bdd8ce9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f66549dd-f8fe-4952-8d10-db21ce946d33", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454663088000, "endTime": 27454663106100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ced6287-b4e8-4d39-bb99-589a8fb8f1f2", "logId": "74474d2d-eb81-42e3-be63-158ad4393b81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74474d2d-eb81-42e3-be63-158ad4393b81", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454663088000, "endTime": 27454663106100}, "additional": {"logType": "info", "children": [], "durationId": "f66549dd-f8fe-4952-8d10-db21ce946d33", "parent": "9a9ab01c-db4d-4043-9ca5-5bec2bdd8ce9"}}, {"head": {"id": "167eee4b-bb8f-4bf1-b6f8-88d6ed32f3cb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454664971400, "endTime": 27454710167300}, "additional": {"children": ["36a2ad22-f81f-4767-88cf-d49250d41329", "158c50fa-e5e0-410b-bd42-e19ecc5cd303"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ced6287-b4e8-4d39-bb99-589a8fb8f1f2", "logId": "9f8dbd7a-beba-424a-bfd1-f002bdc009ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36a2ad22-f81f-4767-88cf-d49250d41329", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454664973600, "endTime": 27454670699700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "167eee4b-bb8f-4bf1-b6f8-88d6ed32f3cb", "logId": "841a0e4c-6ce8-4ae9-9387-3059c99fe916"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "158c50fa-e5e0-410b-bd42-e19ecc5cd303", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454670718100, "endTime": 27454710135400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "167eee4b-bb8f-4bf1-b6f8-88d6ed32f3cb", "logId": "54c23cd4-b4bd-4082-bff5-31a98fac43d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "388d2a80-9bce-4bc2-b1e7-4fc5605845ad", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454664979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04075619-0505-4377-bf5a-57aee1736894", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454670550300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "841a0e4c-6ce8-4ae9-9387-3059c99fe916", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454664973600, "endTime": 27454670699700}, "additional": {"logType": "info", "children": [], "durationId": "36a2ad22-f81f-4767-88cf-d49250d41329", "parent": "9f8dbd7a-beba-424a-bfd1-f002bdc009ad"}}, {"head": {"id": "4b135b2d-6fa8-45c3-872a-7f3c1350f8c9", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454670732700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbfb616a-70ef-4f1f-83ad-8af17cda5587", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454700161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd370651-85a1-4acb-a352-97fea2ee69c2", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454700497800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03b6d932-e8da-4552-bcf8-85025e067ce5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454700987900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f3c49e-7c64-465d-843d-5f44e5d7da0e", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454701697400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152e1de1-ffee-42a5-a860-80d44eadddc1", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454702917100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a76e4b0-0ece-4a39-8fe1-301bb1ee5c15", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454703023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a2a97a-1dbe-4c37-8fc5-ca1db2660463", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454703100000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a566808e-c01e-4700-9d1d-42bbed549f0b", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454709491400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b92ab6-5ac1-4de9-a269-cd448fecb2ef", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454709849500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b4fa2c8-a009-47b3-b62c-73c2abb22703", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454709932000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c45a61dd-8b37-4687-9ad6-dba734465e20", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454710049800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54c23cd4-b4bd-4082-bff5-31a98fac43d5", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454670718100, "endTime": 27454710135400}, "additional": {"logType": "info", "children": [], "durationId": "158c50fa-e5e0-410b-bd42-e19ecc5cd303", "parent": "9f8dbd7a-beba-424a-bfd1-f002bdc009ad"}}, {"head": {"id": "9f8dbd7a-beba-424a-bfd1-f002bdc009ad", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454664971400, "endTime": 27454710167300}, "additional": {"logType": "info", "children": ["841a0e4c-6ce8-4ae9-9387-3059c99fe916", "54c23cd4-b4bd-4082-bff5-31a98fac43d5"], "durationId": "167eee4b-bb8f-4bf1-b6f8-88d6ed32f3cb", "parent": "9a9ab01c-db4d-4043-9ca5-5bec2bdd8ce9"}}, {"head": {"id": "02cbac45-6b8d-4b40-9f1a-8db678c2f25a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454713109700, "endTime": 27454713134600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ced6287-b4e8-4d39-bb99-589a8fb8f1f2", "logId": "2dd632cc-0c9e-4d45-95cb-8850c28f40ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dd632cc-0c9e-4d45-95cb-8850c28f40ff", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454713109700, "endTime": 27454713134600}, "additional": {"logType": "info", "children": [], "durationId": "02cbac45-6b8d-4b40-9f1a-8db678c2f25a", "parent": "9a9ab01c-db4d-4043-9ca5-5bec2bdd8ce9"}}, {"head": {"id": "9a9ab01c-db4d-4043-9ca5-5bec2bdd8ce9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454659563400, "endTime": 27454713160200}, "additional": {"logType": "info", "children": ["74474d2d-eb81-42e3-be63-158ad4393b81", "9f8dbd7a-beba-424a-bfd1-f002bdc009ad", "2dd632cc-0c9e-4d45-95cb-8850c28f40ff"], "durationId": "9ced6287-b4e8-4d39-bb99-589a8fb8f1f2", "parent": "8146fec7-62f9-4dea-ade9-84d120051600"}}, {"head": {"id": "8146fec7-62f9-4dea-ade9-84d120051600", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454658264200, "endTime": 27454713178100}, "additional": {"logType": "info", "children": ["9a9ab01c-db4d-4043-9ca5-5bec2bdd8ce9"], "durationId": "3f77e4e5-3694-4508-a24d-f5c5bec8e660", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "c6e947de-a757-45b9-94b9-32d89482e63e", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454751279700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb36e687-e766-4f3a-b4d6-d1af4aead9ba", "name": "hvigorfile, resolve hvigorfile dependencies in 122 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454834839200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d28969ad-46f1-4ef7-a334-bc3b8e2d9b62", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454713189400, "endTime": 27454834961400}, "additional": {"logType": "info", "children": [], "durationId": "d5ba43cd-7133-4b07-9b21-04e528a727cf", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "7d674ef7-2c04-4163-be69-238ab5b02a64", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454835713600, "endTime": 27454836051600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2e15c95-425c-4e45-97d6-42f758765f20", "logId": "b8a435db-1f33-40b1-a66b-19aeb519eb9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3223f63c-fc85-4a48-9d79-cec450234280", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454835743500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a435db-1f33-40b1-a66b-19aeb519eb9f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454835713600, "endTime": 27454836051600}, "additional": {"logType": "info", "children": [], "durationId": "7d674ef7-2c04-4163-be69-238ab5b02a64", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "8dac9392-6a0d-4843-b7df-7fc7fa327665", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454837104700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d30e21a-e28e-4fec-96a8-064b6963e389", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454853548500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c5d249-8010-489a-9f84-8734a783a38b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454836065400, "endTime": 27454854133000}, "additional": {"logType": "info", "children": [], "durationId": "6f4c741d-89e3-4356-a9b0-d2913a5a4d96", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "52e5022f-b19f-4b16-a9bb-910356feb23e", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454861312500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b8f33a7-fffc-47a1-a1d0-6ffcfd44fcd1", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454861435800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b50b913-638e-4eb7-80b1-39f73fe5d8a5", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454863354800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ac4a1a7-4b4f-44ec-9d2b-14183870503e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454863468500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24d5706-55e4-447a-9429-a1fd610fcdd4", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454854155600, "endTime": 27454866146100}, "additional": {"logType": "info", "children": [], "durationId": "c0a75902-96ad-4bab-b46c-e4730df7e655", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "a19c98b5-607c-400d-8da3-39010d9b984e", "name": "Configuration phase cost:371 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454866190400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8833b46-2f48-45ba-8675-111c2c28afca", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454866167500, "endTime": 27454866383000}, "additional": {"logType": "info", "children": [], "durationId": "eef8c803-e2f8-492a-b6ac-9a8ed328ca36", "parent": "5d084f80-b66b-4204-80d6-c8374bf26330"}}, {"head": {"id": "5d084f80-b66b-4204-80d6-c8374bf26330", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454489982600, "endTime": 27454866398400}, "additional": {"logType": "info", "children": ["8e9e995c-811a-403e-90f3-ce7d69ac7469", "ffdf48c4-35f6-4cdb-b52c-35c145c3a924", "9d15da8d-570b-4859-a93a-23f154e3c659", "8146fec7-62f9-4dea-ade9-84d120051600", "d28969ad-46f1-4ef7-a334-bc3b8e2d9b62", "e1c5d249-8010-489a-9f84-8734a783a38b", "b24d5706-55e4-447a-9429-a1fd610fcdd4", "e8833b46-2f48-45ba-8675-111c2c28afca", "b8a435db-1f33-40b1-a66b-19aeb519eb9f"], "durationId": "d2e15c95-425c-4e45-97d6-42f758765f20", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "f0c07fad-d649-4b4d-bf6d-02c09f5af156", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454867626100, "endTime": 27454867647600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87d2aa49-e256-4418-8594-0d2442ceabbb", "logId": "e8009f30-1a9a-4818-9c1a-d37d43f5ccbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8009f30-1a9a-4818-9c1a-d37d43f5ccbf", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454867626100, "endTime": 27454867647600}, "additional": {"logType": "info", "children": [], "durationId": "f0c07fad-d649-4b4d-bf6d-02c09f5af156", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "bd216dc8-2295-4a6a-ab42-3d8f971abee7", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454866422100, "endTime": 27454867658900}, "additional": {"logType": "info", "children": [], "durationId": "eca50bad-ea46-48ae-927e-9f24f46dab67", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "847208a4-d5d3-4ea0-890d-536374e3f440", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454867664600, "endTime": 27454867665800}, "additional": {"logType": "info", "children": [], "durationId": "59fab7bf-5bd5-4fea-b310-a88a6d49d2d9", "parent": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f"}}, {"head": {"id": "6ab0b5c2-6f4a-4e96-ad9a-554693563a7f", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454473694400, "endTime": 27454867668900}, "additional": {"logType": "info", "children": ["073ceb31-8ea4-4dd3-8131-b8568309e4c7", "5d084f80-b66b-4204-80d6-c8374bf26330", "bd216dc8-2295-4a6a-ab42-3d8f971abee7", "847208a4-d5d3-4ea0-890d-536374e3f440", "8fdbea43-2d80-4a44-9c1b-dcdf59c1aa8b", "74467f57-eb71-4b85-9d8d-513bc00ee0fa", "e8009f30-1a9a-4818-9c1a-d37d43f5ccbf"], "durationId": "87d2aa49-e256-4418-8594-0d2442ceabbb"}}, {"head": {"id": "71ac235f-08cd-4d35-8227-ccb4cbf3bcb7", "name": "Configuration task cost before running: 399 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454867881200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ee90f9d-eab7-48cf-a6f9-8829ac0e57ed", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454874429100, "endTime": 27454881402300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7f502de6-3145-458c-b0de-985d75273063", "logId": "0e1aa5cd-321d-49ca-80b3-bfdfa14ab385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f502de6-3145-458c-b0de-985d75273063", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454869529300}, "additional": {"logType": "detail", "children": [], "durationId": "7ee90f9d-eab7-48cf-a6f9-8829ac0e57ed"}}, {"head": {"id": "037f5a23-b8f4-45f2-843c-5b5cb6e25b06", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454869914500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b7adfd-db1d-4d1c-bff3-f3ccbf846c38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454870022600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c8c44af-3c95-468b-8de7-498a587dee5e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454874451600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b9de61-371c-461b-b986-f7f4ebe387d8", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454881179400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa2592d-30aa-4cb3-9e2a-0286db61c7bd", "name": "entry : default@PreBuild cost memory 0.314117431640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454881326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1aa5cd-321d-49ca-80b3-bfdfa14ab385", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454874429100, "endTime": 27454881402300}, "additional": {"logType": "info", "children": [], "durationId": "7ee90f9d-eab7-48cf-a6f9-8829ac0e57ed"}}, {"head": {"id": "1e2aa2d5-822b-49d1-a6cc-7a319f617527", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454885961700, "endTime": 27454891850000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7121bafa-4525-48e8-99ff-4eb3c7a739fc", "logId": "d823a043-2d40-4939-ab0a-6d2f1f0a58aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7121bafa-4525-48e8-99ff-4eb3c7a739fc", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454884669500}, "additional": {"logType": "detail", "children": [], "durationId": "1e2aa2d5-822b-49d1-a6cc-7a319f617527"}}, {"head": {"id": "c4903af3-0d0f-4ed0-b2e7-1f86b4efd464", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454885033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2a2633-e3fa-4157-b599-1dedbd5b56f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454885133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "606aed95-5b92-416e-a6bb-f025d53b31b6", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454885975700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b56a92-010e-46c5-a440-68da08f109b7", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454887912900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f949c975-1bc9-4a38-b0ab-920fefef4ec5", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454891551500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9171d506-0f63-4c77-89b3-07a8e1214f90", "name": "entry : default@GenerateMetadata cost memory 0.096710205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454891718400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d823a043-2d40-4939-ab0a-6d2f1f0a58aa", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454885961700, "endTime": 27454891850000}, "additional": {"logType": "info", "children": [], "durationId": "1e2aa2d5-822b-49d1-a6cc-7a319f617527"}}, {"head": {"id": "ac1e5ff5-4352-4730-bb1d-d9663f745703", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894252000, "endTime": 27454894605800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "52e04ee4-30c9-4aab-b6a4-b023b275ceb4", "logId": "35279429-854a-48ef-8444-e07c1bd36001"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52e04ee4-30c9-4aab-b6a4-b023b275ceb4", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454893680500}, "additional": {"logType": "detail", "children": [], "durationId": "ac1e5ff5-4352-4730-bb1d-d9663f745703"}}, {"head": {"id": "24bd0acd-613b-4e59-9ba2-a0f0ca904d74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894016500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "682c17be-5770-46a0-8479-7aef2186f77e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894108200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc4ccca-b2f8-4259-979b-ec7d57f0fc5a", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31a831d-5b9f-4122-91ab-e6e2848fb76d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894348300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb93519-b6ad-45d0-a17f-00cb7f43e0c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894397300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b08b6526-6f8b-495c-927f-4ccb4027a223", "name": "entry : default@ConfigureCmake cost memory 0.0367889404296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894465800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc2f388a-34f3-4eaa-b0a7-ec791b2b4e11", "name": "runTaskFromQueue task cost before running: 426 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35279429-854a-48ef-8444-e07c1bd36001", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454894252000, "endTime": 27454894605800, "totalTime": 273000}, "additional": {"logType": "info", "children": [], "durationId": "ac1e5ff5-4352-4730-bb1d-d9663f745703"}}, {"head": {"id": "6341d453-3fa8-44fd-beb6-163b2ee0c46a", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454897541200, "endTime": 27454900917700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7b15a4d9-5a4e-4b7c-b4b8-0e32af298aeb", "logId": "28271706-b468-403d-856a-8111098fb7ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b15a4d9-5a4e-4b7c-b4b8-0e32af298aeb", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454896153200}, "additional": {"logType": "detail", "children": [], "durationId": "6341d453-3fa8-44fd-beb6-163b2ee0c46a"}}, {"head": {"id": "f60dc91a-67ee-4659-9c3a-948e3edeede8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454896536500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b24d8c5e-d76a-45b5-83d6-3024d1d1a34d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454896635500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3670a651-1667-47c3-8ca9-a6dafddd5731", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454897556300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc70d6dd-fe25-41a5-bc46-ca54a145e77c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454900641600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447a043e-f6af-4bf7-ae1c-ef814fdef490", "name": "entry : default@MergeProfile cost memory 0.10699462890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454900780800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28271706-b468-403d-856a-8111098fb7ee", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454897541200, "endTime": 27454900917700}, "additional": {"logType": "info", "children": [], "durationId": "6341d453-3fa8-44fd-beb6-163b2ee0c46a"}}, {"head": {"id": "35d3ba1e-83c8-4bfb-9165-6346144180c6", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454905293000, "endTime": 27454909223200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "93b19fe5-a028-4521-8367-1f364e2fd496", "logId": "ebd5c9a0-11c7-482e-8e31-47884fd6000e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93b19fe5-a028-4521-8367-1f364e2fd496", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454903295300}, "additional": {"logType": "detail", "children": [], "durationId": "35d3ba1e-83c8-4bfb-9165-6346144180c6"}}, {"head": {"id": "508cebb1-195f-4cef-833e-889938f5628d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454903684900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c82297f-87e0-4525-b568-7a5097fb5f17", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454903847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9ec86c-f3ac-44ee-98f3-675c314cc803", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454905310200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86f9b81-086d-4e0f-9812-a6de160cf623", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454907743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a16d99-6eda-4df2-9a54-c2294fc56fbc", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454908947100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a0a18f8-8f88-43cb-bdaf-94e5618ba9a7", "name": "entry : default@CreateBuildProfile cost memory 0.106170654296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454909083500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd5c9a0-11c7-482e-8e31-47884fd6000e", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454905293000, "endTime": 27454909223200}, "additional": {"logType": "info", "children": [], "durationId": "35d3ba1e-83c8-4bfb-9165-6346144180c6"}}, {"head": {"id": "c5d71a41-6335-4f7b-bc7a-1db6a3675cd9", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911824100, "endTime": 27454912253200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "292ff82a-9952-4f39-a087-765c493051d9", "logId": "a3925031-5284-4697-b56e-94fdbea9b598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "292ff82a-9952-4f39-a087-765c493051d9", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454910790500}, "additional": {"logType": "detail", "children": [], "durationId": "c5d71a41-6335-4f7b-bc7a-1db6a3675cd9"}}, {"head": {"id": "2ff84356-ecac-4b3c-a8f9-34794d1d7803", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911110600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee9440f-f549-4327-b6d5-f05c012bbfc7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911201800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4c1289-b3e5-4054-9158-c9c05577113b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911835900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2517ff2-e8f3-4280-af7b-d2e577e6b82b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911939300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8726a31-02c7-43fe-ba5b-3e9418cc10c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911995800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0cf4ee5-d751-4fe8-a688-af201d1523e6", "name": "entry : default@PreCheckSyscap cost memory 0.03701019287109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454912069300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "535d482b-103d-47d0-9db1-c6b6c941d716", "name": "runTaskFromQueue task cost before running: 443 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454912191200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3925031-5284-4697-b56e-94fdbea9b598", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454911824100, "endTime": 27454912253200, "totalTime": 347700}, "additional": {"logType": "info", "children": [], "durationId": "c5d71a41-6335-4f7b-bc7a-1db6a3675cd9"}}, {"head": {"id": "21f781be-5c0d-43e7-98f3-8c83c83c4b00", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454917914500, "endTime": 27454918890100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6bb1cee4-d81f-4ce5-bb19-f1ebdb5a0310", "logId": "c222287b-64ad-49aa-84c6-c4ebb48c1888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bb1cee4-d81f-4ce5-bb19-f1ebdb5a0310", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454913591200}, "additional": {"logType": "detail", "children": [], "durationId": "21f781be-5c0d-43e7-98f3-8c83c83c4b00"}}, {"head": {"id": "a48ec12c-a9c6-455a-b1f6-e328ec91daef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454913900100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ef584f0-d19d-417f-b5c7-51282519ced6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454913980800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ab2310-1857-4356-9f03-2c014156128d", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454917930000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595e245e-bf2a-41e9-91a3-f35d61c6cd9d", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454918148100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bfd2c85-9adf-46d6-9f0c-25ef6e882266", "name": "entry : default@GeneratePkgContextInfo cost memory 0.039276123046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454918701200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a677d63-086d-4d3a-b7a4-17a683b0af62", "name": "runTaskFromQueue task cost before running: 450 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454918828900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c222287b-64ad-49aa-84c6-c4ebb48c1888", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454917914500, "endTime": 27454918890100, "totalTime": 892900}, "additional": {"logType": "info", "children": [], "durationId": "21f781be-5c0d-43e7-98f3-8c83c83c4b00"}}, {"head": {"id": "ab0054c2-c678-4a42-87e1-2e972708c48f", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454924050300, "endTime": 27454927499000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "9cb9d5a9-5f88-419c-9ebd-c3b01d6b1d0a", "logId": "762d5bff-0c75-4252-bf14-5b279a81c302"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cb9d5a9-5f88-419c-9ebd-c3b01d6b1d0a", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454920417200}, "additional": {"logType": "detail", "children": [], "durationId": "ab0054c2-c678-4a42-87e1-2e972708c48f"}}, {"head": {"id": "8bf38801-0c9e-433b-9da6-5c9d7f6287d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454920746000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58364012-3d1b-4d3d-8922-f332f561af0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454920841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d84133c-fa5e-422f-8755-c9c130f5f4d3", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454924063400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f09f4c-71b4-48ae-b1d3-63a006dcf919", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454927063200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff5bdb8-1d00-4313-a2d1-c00688c7e1d5", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454927170000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b458aef-2b7f-4e47-a987-bfccbc3e7b07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454927249200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a53b01bb-ca49-4f79-9828-4a9f6244dfb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454927300700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4f019d-7e40-4d6f-ad76-1d68eea94a05", "name": "entry : default@ProcessIntegratedHsp cost memory -4.684906005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454927377400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f152f63d-be19-473e-b6e1-6321e77e7dc8", "name": "runTaskFromQueue task cost before running: 459 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454927448900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "762d5bff-0c75-4252-bf14-5b279a81c302", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454924050300, "endTime": 27454927499000, "totalTime": 3387200}, "additional": {"logType": "info", "children": [], "durationId": "ab0054c2-c678-4a42-87e1-2e972708c48f"}}, {"head": {"id": "2ad106be-4f55-4932-a92b-4c7fb1aaf545", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930259800, "endTime": 27454930623500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ae579f23-5687-4ded-88b8-df43adfaff19", "logId": "b45a4fe7-b9c2-4a30-bc97-ec38a8b80f1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae579f23-5687-4ded-88b8-df43adfaff19", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454929310000}, "additional": {"logType": "detail", "children": [], "durationId": "2ad106be-4f55-4932-a92b-4c7fb1aaf545"}}, {"head": {"id": "4f330f95-c2ac-499e-987a-b9ce1546f2b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454929619900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "629c8303-1e8f-4d68-b80b-46905a22980e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454929702000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21f82e8-0f3b-4afb-8dc3-e6e3faa1bd1d", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930269600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d5647f-a2c6-45ee-9708-5ff752cce6e2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68c3ad22-3f30-4c4c-b238-6535e6b6750b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411b67cc-abbd-439c-9c42-8748698d8848", "name": "entry : default@BuildNativeWithCmake cost memory 0.037841796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930508700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "966bcc48-3a5b-404f-9e01-5e581ee6f17a", "name": "runTaskFromQueue task cost before running: 462 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b45a4fe7-b9c2-4a30-bc97-ec38a8b80f1e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454930259800, "endTime": 27454930623500, "totalTime": 303900}, "additional": {"logType": "info", "children": [], "durationId": "2ad106be-4f55-4932-a92b-4c7fb1aaf545"}}, {"head": {"id": "2277cbf8-6a4a-400e-b666-ad76992c3a84", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454933478900, "endTime": 27454936170800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f4e1f6fc-22dc-49e3-ae82-7d7bc43a850c", "logId": "11c39be0-243a-40d1-ad98-487b62b714ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4e1f6fc-22dc-49e3-ae82-7d7bc43a850c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454932140000}, "additional": {"logType": "detail", "children": [], "durationId": "2277cbf8-6a4a-400e-b666-ad76992c3a84"}}, {"head": {"id": "c251d667-e9c2-4ac9-b7a4-9542c8eaa0e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454932546700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa5e00d-77b9-4614-9633-97022f120b27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454932645700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cdb3e0e-cdac-4e1e-b6b0-ae6ba32f1a62", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454933489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de477e49-2d93-44bd-94f0-de5312a993c0", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454935942500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349c74d2-9350-4df3-bc38-632169e2d3f0", "name": "entry : default@MakePackInfo cost memory 0.13982391357421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454936097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c39be0-243a-40d1-ad98-487b62b714ae", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454933478900, "endTime": 27454936170800}, "additional": {"logType": "info", "children": [], "durationId": "2277cbf8-6a4a-400e-b666-ad76992c3a84"}}, {"head": {"id": "8ad4b172-2cb8-487a-b9e0-c23a3720578a", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454941611700, "endTime": 27454944225400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "2fc9ae84-0100-4aaa-9d0c-2798ab1b100c", "logId": "13aa21fe-9cf5-4068-90c6-b175862de517"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fc9ae84-0100-4aaa-9d0c-2798ab1b100c", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454938684100}, "additional": {"logType": "detail", "children": [], "durationId": "8ad4b172-2cb8-487a-b9e0-c23a3720578a"}}, {"head": {"id": "2a2ab554-d5c9-41c6-a4c6-c6f2bfb768c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454939562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06cb65fb-12fb-47a3-9d25-089eff65194b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454940160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed00089d-f5dd-46d2-bd76-daafa42e5a02", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454941624200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eef1c715-4622-4d52-a16a-2ada02f1fcaf", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454941759400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e2273a-c444-4267-b9a1-1bd23fc1fdbf", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454942407100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eff81bf-8b48-485c-9477-0c23d49f6777", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454943755100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a6e2839-b464-4e5c-a0c4-d6d5165cd2d9", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454943883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84e1760e-755f-4f1e-92d9-fc17321b7f40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454943967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "341e4372-f01e-4db4-83ca-e45ccdbea8cb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454944031800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20015350-a2dd-46cd-8d70-ecabab242325", "name": "entry : default@SyscapTransform cost memory 0.1545562744140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454944105300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb96b95-7932-4da3-8a41-52cd07d67fd8", "name": "runTaskFromQueue task cost before running: 475 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454944175700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13aa21fe-9cf5-4068-90c6-b175862de517", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454941611700, "endTime": 27454944225400, "totalTime": 2551500}, "additional": {"logType": "info", "children": [], "durationId": "8ad4b172-2cb8-487a-b9e0-c23a3720578a"}}, {"head": {"id": "1b252dd1-cec5-44e7-8666-2ee5fbc33f20", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454946918900, "endTime": 27454947894400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "070d9ecc-5569-4f39-b6c2-b83a875491ba", "logId": "234c1a9a-7c6a-49dd-b887-fbde1150d885"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "070d9ecc-5569-4f39-b6c2-b83a875491ba", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454945563600}, "additional": {"logType": "detail", "children": [], "durationId": "1b252dd1-cec5-44e7-8666-2ee5fbc33f20"}}, {"head": {"id": "4098c0ce-66c6-4492-9598-104b12eaf72c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454945886900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "726eea6c-3aa1-43f4-a28c-80fc660316af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454945968100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b17cb5-5769-4cfa-814c-588b98fc96bc", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454946929800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30e5713-f317-4a97-a581-b93bde6a023b", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454947674300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e79ab2f-f594-469f-85c1-be7c090b08ea", "name": "entry : default@ProcessProfile cost memory 0.06058502197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454947800400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234c1a9a-7c6a-49dd-b887-fbde1150d885", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454946918900, "endTime": 27454947894400}, "additional": {"logType": "info", "children": [], "durationId": "1b252dd1-cec5-44e7-8666-2ee5fbc33f20"}}, {"head": {"id": "e6f009f4-0d75-4f40-a42b-ef5fcd0d0a58", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454952186500, "endTime": 27454957524700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "845699ee-1019-4c1c-8fa6-9960c229ec26", "logId": "0b749429-160d-41b5-95c2-c4c1c4fe9fb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "845699ee-1019-4c1c-8fa6-9960c229ec26", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454950056400}, "additional": {"logType": "detail", "children": [], "durationId": "e6f009f4-0d75-4f40-a42b-ef5fcd0d0a58"}}, {"head": {"id": "c0811515-932a-4fe0-936d-469e594f1da8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454950389700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4edde27-75ef-4d72-a333-6ee4fac3d270", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454950498600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc2c8ee-c054-4b79-89bc-f85fcfa849ce", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454952199400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e3cd7f2-e68d-4f4d-843a-b9646341ec55", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454957326300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56eabf0c-e5e9-4fcf-b8f5-6d51026b68ff", "name": "entry : default@ProcessRouterMap cost memory 0.2142791748046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454957457800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b749429-160d-41b5-95c2-c4c1c4fe9fb5", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454952186500, "endTime": 27454957524700}, "additional": {"logType": "info", "children": [], "durationId": "e6f009f4-0d75-4f40-a42b-ef5fcd0d0a58"}}, {"head": {"id": "6ff6a642-51cd-4fda-b29a-60665dc7cb7c", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961066900, "endTime": 27454962009900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2fa0e00b-e8fb-4132-8adc-e4592a084995", "logId": "07c2bd7b-3609-4442-bc24-61fd47f39b41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fa0e00b-e8fb-4132-8adc-e4592a084995", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454960053600}, "additional": {"logType": "detail", "children": [], "durationId": "6ff6a642-51cd-4fda-b29a-60665dc7cb7c"}}, {"head": {"id": "909c846c-9a8d-451e-aafc-17479266c23b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454960379300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b41a5ff-7bf5-4b61-ab64-b500b5a181cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454960475100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6292a58-677a-4305-8be7-cf814e120a92", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961075200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e8334e-9be6-4711-8ef8-6181014a961d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961193100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eddc0b01-6207-4423-a959-426b4f89753e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961247400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af40b97b-dc01-4870-8d5f-283e9cb399ba", "name": "entry : default@BuildNativeWithNinja cost memory 0.05745697021484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961854900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab2d4881-fe4c-421b-9fe7-74e3c9d42058", "name": "runTaskFromQueue task cost before running: 493 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961951900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c2bd7b-3609-4442-bc24-61fd47f39b41", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454961066900, "endTime": 27454962009900, "totalTime": 868300}, "additional": {"logType": "info", "children": [], "durationId": "6ff6a642-51cd-4fda-b29a-60665dc7cb7c"}}, {"head": {"id": "06ee0e65-b4e6-477b-be5b-a4df3549723c", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454966825800, "endTime": 27454974084000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ea4a1ee4-c232-47a4-96b8-87f245a054c5", "logId": "584eadf0-43b5-46c3-8840-6d92c7022672"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea4a1ee4-c232-47a4-96b8-87f245a054c5", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454964048100}, "additional": {"logType": "detail", "children": [], "durationId": "06ee0e65-b4e6-477b-be5b-a4df3549723c"}}, {"head": {"id": "54fdbc40-c876-45f5-bea0-77d89f4d717e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454964480200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2930fd17-14ee-484d-9373-d03b9439164e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454964573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "810704ee-afc9-45bd-9144-8dfdc51e6281", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454965340800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56760690-3127-4343-bc7b-0a63536ae46b", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454968383400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decca5fc-def6-4f60-b42d-f26f55285e49", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454970472500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a127fa77-26ce-40fb-9336-021777286333", "name": "entry : default@ProcessResource cost memory 0.17047882080078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454970598800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "584eadf0-43b5-46c3-8840-6d92c7022672", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454966825800, "endTime": 27454974084000}, "additional": {"logType": "info", "children": [], "durationId": "06ee0e65-b4e6-477b-be5b-a4df3549723c"}}, {"head": {"id": "78901ccf-5609-47da-bad9-37529621bc27", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454983976200, "endTime": 27455003261000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "36bc1c7e-460b-408c-ad33-8f4614a22958", "logId": "0145ee35-3921-4219-b1c3-c5ad713287d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36bc1c7e-460b-408c-ad33-8f4614a22958", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454979176500}, "additional": {"logType": "detail", "children": [], "durationId": "78901ccf-5609-47da-bad9-37529621bc27"}}, {"head": {"id": "aebcbdcc-9f7c-4ee4-9f71-ce67cf273a98", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454979971800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28af7320-b717-405c-b56e-0a3e98a8e2e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454980091400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c90ddd38-807e-41d5-af26-fe0ba7b4970a", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454984004600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f687db07-136f-4559-b001-f551d7d25824", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455003048300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc0b18c5-89f7-4288-b28b-08ef29967201", "name": "entry : default@GenerateLoaderJson cost memory 0.7715606689453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455003188400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0145ee35-3921-4219-b1c3-c5ad713287d8", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454983976200, "endTime": 27455003261000}, "additional": {"logType": "info", "children": [], "durationId": "78901ccf-5609-47da-bad9-37529621bc27"}}, {"head": {"id": "7ee4bffe-0c27-4eb7-b8fa-ae507ae31121", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455013295900, "endTime": 27455019599700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "15325ff4-9dbd-4c41-925f-3cb3ab984546", "logId": "81148a1c-0a49-4aeb-a593-4afa9882aa28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15325ff4-9dbd-4c41-925f-3cb3ab984546", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455011433600}, "additional": {"logType": "detail", "children": [], "durationId": "7ee4bffe-0c27-4eb7-b8fa-ae507ae31121"}}, {"head": {"id": "51576fbc-7c10-4cda-980d-688c4bfe051a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455012105800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f484d8-4d44-47cf-93a2-2219eadee182", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455012238900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7623d68-6d2a-49da-b28c-4eb3f220e3fc", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455013312000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef13349-d623-4c21-aabb-79748b253799", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455017706200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1aaebc3-0976-4ea2-b8ef-4c017bad5cdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455017962200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "864ba655-3aec-41b2-a3d7-08f97e458d1b", "name": "entry : default@ProcessLibs cost memory 0.12656402587890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455019220000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd62ee2-8d8c-4a64-af56-0c719cec1f2d", "name": "runTaskFromQueue task cost before running: 551 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455019461800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81148a1c-0a49-4aeb-a593-4afa9882aa28", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455013295900, "endTime": 27455019599700, "totalTime": 6123000}, "additional": {"logType": "info", "children": [], "durationId": "7ee4bffe-0c27-4eb7-b8fa-ae507ae31121"}}, {"head": {"id": "4d729318-5449-414c-9627-5e9a28a005d8", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455031620500, "endTime": 27455052453500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ca33188d-115f-4061-8238-b8068e6bc86e", "logId": "7d314564-76e7-4836-91ed-6d581d58fa96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca33188d-115f-4061-8238-b8068e6bc86e", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455026967700}, "additional": {"logType": "detail", "children": [], "durationId": "4d729318-5449-414c-9627-5e9a28a005d8"}}, {"head": {"id": "11226ae3-1fc6-4d68-a9eb-f5befbdca0c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455027457300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d7d6f4-f1dd-4852-8787-d1c5b13a730f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455027567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc43acf-002d-4512-a0d2-61f7eee0f365", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455028517600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f27034-194f-41a5-9481-cb4326a908fd", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455031649400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a862b7-bcdc-4d01-ba7d-f6a67896fa6b", "name": "Incremental task entry:default@CompileResource pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455052226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204bb863-47d0-4c2c-ab8d-771140459f88", "name": "entry : default@CompileResource cost memory -4.413299560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455052370100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d314564-76e7-4836-91ed-6d581d58fa96", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455031620500, "endTime": 27455052453500}, "additional": {"logType": "info", "children": [], "durationId": "4d729318-5449-414c-9627-5e9a28a005d8"}}, {"head": {"id": "60c4cf78-750a-4baa-91e3-0cee6193dd46", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455059221400, "endTime": 27455060299100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "603a7479-4f68-4c9e-9b79-ce3cf81b566d", "logId": "24c7e661-a2f6-41ff-bef1-f3aac11798f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "603a7479-4f68-4c9e-9b79-ce3cf81b566d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455055890400}, "additional": {"logType": "detail", "children": [], "durationId": "60c4cf78-750a-4baa-91e3-0cee6193dd46"}}, {"head": {"id": "1496aae7-b2ae-48e0-918b-bddfeb8f8603", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455056850700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d696afd0-5136-4da3-a4b1-1309d1ba6304", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455056984400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1485507-e612-459b-a9ee-4805f4efd3c6", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455059233000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f9db9eb-4b53-4a7d-b74d-fd678dd6f8b6", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455059454100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4efff3be-dd1f-4281-97a9-73b403f674af", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455060151000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fda7b3c4-41d0-43b7-8dbb-dad630281123", "name": "entry : default@DoNativeStrip cost memory 0.07720184326171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455060231300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c7e661-a2f6-41ff-bef1-f3aac11798f7", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455059221400, "endTime": 27455060299100}, "additional": {"logType": "info", "children": [], "durationId": "60c4cf78-750a-4baa-91e3-0cee6193dd46"}}, {"head": {"id": "1acf70b9-4f91-4d1a-9cb3-62bec9941b4e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455065316700, "endTime": 27458995899600}, "additional": {"children": ["1493a2b1-462c-42d9-b6e3-4c3b7446891b", "3736031f-5a44-4bbf-a6c4-dafb04d9ec04"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "cbb76569-8eab-479c-97b4-be0798bc4acc", "logId": "0a0c373d-70c2-48b6-940e-7be1d7c21e2f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbb76569-8eab-479c-97b4-be0798bc4acc", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455061532000}, "additional": {"logType": "detail", "children": [], "durationId": "1acf70b9-4f91-4d1a-9cb3-62bec9941b4e"}}, {"head": {"id": "c0bfdac7-fc46-498c-a3be-7919354d33bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455061896000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba179499-2584-4828-b2f2-420145035cbb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455061990500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9003afc-8fe0-4d44-b128-dc691051ccb9", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455065327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b35ab6-4b32-4faa-b82b-028b83183cfd", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455077670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde15892-84cf-4034-8b93-13749fadb7f7", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455077839700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142892a0-db7e-4cd3-9e6f-5dfdca8ac885", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455089048300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "134a82d6-9e88-4d57-a0a4-ef8caff4257a", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455089575900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff9fc0a-fc6b-4de8-8bb7-b4d1ffa7d12e", "name": "default@CompileArkTS work[30] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455090552000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27455197316100, "endTime": 27458989029400}, "additional": {"children": ["733de25e-8a0e-4750-baca-4f015a950288", "d9833bc1-e16e-4087-8007-0ffbc1461d8b", "4cef37e7-0d1e-4b53-a618-8cbdb94b0256", "eb0d7551-188c-40db-b816-62da2376759a", "dd350580-0ff7-4a19-aea0-98baae6e0302", "da196767-8b5c-4fd5-b057-1dd82b7e2879"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "1acf70b9-4f91-4d1a-9cb3-62bec9941b4e", "logId": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a0660dc-f388-4127-9d90-c15a1c1b557d", "name": "default@CompileArkTS work[30] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455091410800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7da5efa-2667-4291-aa9d-61952e82ffa2", "name": "default@CompileArkTS work[30] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455091516800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d71f11-3b1c-44f9-82d7-eafa8f40072e", "name": "CopyResources startTime: 27455091576300", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455091579000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9aab62b-9062-4da3-8c70-1db51f58a12e", "name": "default@CompileArkTS work[31] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455091664600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3736031f-5a44-4bbf-a6c4-dafb04d9ec04", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27456643143200, "endTime": 27456666390900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "1acf70b9-4f91-4d1a-9cb3-62bec9941b4e", "logId": "5a54e7e7-8f54-407f-b4d8-2aad8f87a542"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d899daac-ea48-4e8d-9e44-7579b7893dcb", "name": "default@CompileArkTS work[31] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455092409600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d68aa7-3582-48d6-999f-018e14cf122c", "name": "default@CompileArkTS work[31] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455092508700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ebaf3d-2054-4dbc-b068-6bd60085b7ad", "name": "entry : default@CompileArkTS cost memory 1.5940475463867188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455092826600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c817355d-c15d-4015-9fcd-c5cea9b82f45", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455098812700, "endTime": 27455101201900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "68bb5d47-6f9d-4c96-824a-2edd423e0dd1", "logId": "75af9ed0-fa4b-45c5-ba03-2d173bbc7cc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68bb5d47-6f9d-4c96-824a-2edd423e0dd1", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455094227300}, "additional": {"logType": "detail", "children": [], "durationId": "c817355d-c15d-4015-9fcd-c5cea9b82f45"}}, {"head": {"id": "a5176b43-260e-40d5-bedb-5b573f9ad685", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455094635900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11061940-96ee-4e19-b56e-16d7435c4ead", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455094774300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b68f015f-14d2-494a-ae18-08357ebda9a4", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455098825100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3ee7dd-c92b-4f0c-bff6-8c57d1bb7d65", "name": "entry : default@BuildJS cost memory 0.1282958984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455101013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3621f1ac-e84f-4a3b-a661-4c098d83718c", "name": "runTaskFromQueue task cost before running: 632 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455101143400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75af9ed0-fa4b-45c5-ba03-2d173bbc7cc7", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455098812700, "endTime": 27455101201900, "totalTime": 2311500}, "additional": {"logType": "info", "children": [], "durationId": "c817355d-c15d-4015-9fcd-c5cea9b82f45"}}, {"head": {"id": "2f6b61fb-27f4-4da0-b09c-afea43fb34a4", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455107782400, "endTime": 27455109349600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "56b3afe5-2447-4234-8619-5c0d8770b67a", "logId": "6fa98c00-ce66-4142-bdb3-690ddeb212ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56b3afe5-2447-4234-8619-5c0d8770b67a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455102983100}, "additional": {"logType": "detail", "children": [], "durationId": "2f6b61fb-27f4-4da0-b09c-afea43fb34a4"}}, {"head": {"id": "faf96550-1cad-4542-b7a1-b06d947ee1c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455103406200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e60a91-c50d-452d-b853-37a297bbc061", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455103730700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed17590d-55c9-4a04-bae8-864f7184c839", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455107811300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00413fcb-c032-4eb9-8d84-c331ea0d8762", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455108212600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff9f5ef-c6b7-4a8c-a779-aadc21faebf1", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455109146800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b49924ad-a4bc-4eea-ae8d-caa26024d274", "name": "entry : default@CacheNativeLibs cost memory 0.09798431396484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455109243700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa98c00-ce66-4142-bdb3-690ddeb212ad", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455107782400, "endTime": 27455109349600}, "additional": {"logType": "info", "children": [], "durationId": "2f6b61fb-27f4-4da0-b09c-afea43fb34a4"}}, {"head": {"id": "ba5364ac-653b-4872-8ba1-6a37e36e18f9", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455196508500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03377393-287c-412a-b90f-b15b4c877551", "name": "default@CompileArkTS work[30] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455196875900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f6055e-55f1-45f4-aed6-eac093218f42", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455196999200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99354aa6-2e05-44b7-94a6-6b652e9227a4", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455197057700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7ffc73-6a2e-4c84-bbe8-8f94707e4264", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455197116300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f47870f-2aa2-4380-8ecf-a45752733910", "name": "default@CompileArkTS work[31] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455198020700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b692ba-78af-4a18-8df0-610b12c22526", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27456666574900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2792dc3-3885-4e18-a9b4-03e9924e8cae", "name": "CopyResources is end, endTime: 27456666699900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27456666703500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9551ae-88c2-446a-a18a-9f1911094a0c", "name": "default@CompileArkTS work[31] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27456666784100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a54e7e7-8f54-407f-b4d8-2aad8f87a542", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27456643143200, "endTime": 27456666390900}, "additional": {"logType": "info", "children": [], "durationId": "3736031f-5a44-4bbf-a6c4-dafb04d9ec04", "parent": "0a0c373d-70c2-48b6-940e-7be1d7c21e2f"}}, {"head": {"id": "8db960ea-df56-44d3-b91f-b2339cc6b3b3", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27456885607900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d70e6bc-287b-4cdc-997a-1c9ab357ccd4", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458989783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733de25e-8a0e-4750-baca-4f015a950288", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27455197449600, "endTime": 27455201698000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "logId": "1d90c21c-1cc0-4c07-9485-e669f5bd8f8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d90c21c-1cc0-4c07-9485-e669f5bd8f8a", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455197449600, "endTime": 27455201698000}, "additional": {"logType": "info", "children": [], "durationId": "733de25e-8a0e-4750-baca-4f015a950288", "parent": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}}, {"head": {"id": "d9833bc1-e16e-4087-8007-0ffbc1461d8b", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27455201717900, "endTime": 27455201819700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "logId": "6a3923aa-2385-487e-bb5a-539515a6a34d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a3923aa-2385-487e-bb5a-539515a6a34d", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455201717900, "endTime": 27455201819700}, "additional": {"logType": "info", "children": [], "durationId": "d9833bc1-e16e-4087-8007-0ffbc1461d8b", "parent": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}}, {"head": {"id": "4cef37e7-0d1e-4b53-a618-8cbdb94b0256", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27455201828800, "endTime": 27455201860600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "logId": "99615095-6955-43c2-b1f8-8bfe970bc5d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99615095-6955-43c2-b1f8-8bfe970bc5d4", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455201828800, "endTime": 27455201860600}, "additional": {"logType": "info", "children": [], "durationId": "4cef37e7-0d1e-4b53-a618-8cbdb94b0256", "parent": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}}, {"head": {"id": "eb0d7551-188c-40db-b816-62da2376759a", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27455201874700, "endTime": 27458903406800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "logId": "6960226c-fbbd-414d-9365-f098b8c57c62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6960226c-fbbd-414d-9365-f098b8c57c62", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455201874700, "endTime": 27458903406800}, "additional": {"logType": "info", "children": [], "durationId": "eb0d7551-188c-40db-b816-62da2376759a", "parent": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}}, {"head": {"id": "dd350580-0ff7-4a19-aea0-98baae6e0302", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27458903441500, "endTime": 27458920243000}, "additional": {"children": ["e486549d-13f8-4917-bb0b-786728c9aaff", "2e028167-bdb2-4610-9940-e4f7dbbb4bf7", "29f6d845-abad-4107-8b62-b914f9c8a49a"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "logId": "8e5017fc-4dff-4756-9a44-55474a866cb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e5017fc-4dff-4756-9a44-55474a866cb8", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458903441500, "endTime": 27458920243000}, "additional": {"logType": "info", "children": ["08e4b07a-dc2e-4a3c-b79f-74b32fdd66f9", "2bb458a6-a963-4d69-9b7f-7c9041fe8687", "8f43e316-3eaa-4d54-a38b-f4b139c1e942"], "durationId": "dd350580-0ff7-4a19-aea0-98baae6e0302", "parent": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}}, {"head": {"id": "e486549d-13f8-4917-bb0b-786728c9aaff", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27458903479400, "endTime": 27458903486500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "dd350580-0ff7-4a19-aea0-98baae6e0302", "logId": "08e4b07a-dc2e-4a3c-b79f-74b32fdd66f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08e4b07a-dc2e-4a3c-b79f-74b32fdd66f9", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458903479400, "endTime": 27458903486500}, "additional": {"logType": "info", "children": [], "durationId": "e486549d-13f8-4917-bb0b-786728c9aaff", "parent": "8e5017fc-4dff-4756-9a44-55474a866cb8"}}, {"head": {"id": "2e028167-bdb2-4610-9940-e4f7dbbb4bf7", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27458903490500, "endTime": 27458914510200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "dd350580-0ff7-4a19-aea0-98baae6e0302", "logId": "2bb458a6-a963-4d69-9b7f-7c9041fe8687"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bb458a6-a963-4d69-9b7f-7c9041fe8687", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458903490500, "endTime": 27458914510200}, "additional": {"logType": "info", "children": [], "durationId": "2e028167-bdb2-4610-9940-e4f7dbbb4bf7", "parent": "8e5017fc-4dff-4756-9a44-55474a866cb8"}}, {"head": {"id": "29f6d845-abad-4107-8b62-b914f9c8a49a", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27458914516900, "endTime": 27458920228800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "dd350580-0ff7-4a19-aea0-98baae6e0302", "logId": "8f43e316-3eaa-4d54-a38b-f4b139c1e942"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f43e316-3eaa-4d54-a38b-f4b139c1e942", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458914516900, "endTime": 27458920228800}, "additional": {"logType": "info", "children": [], "durationId": "29f6d845-abad-4107-8b62-b914f9c8a49a", "parent": "8e5017fc-4dff-4756-9a44-55474a866cb8"}}, {"head": {"id": "da196767-8b5c-4fd5-b057-1dd82b7e2879", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27458920257000, "endTime": 27458987961300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "logId": "2d4a84be-00e6-413b-9b10-510f523389cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d4a84be-00e6-413b-9b10-510f523389cc", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458920257000, "endTime": 27458987961300}, "additional": {"logType": "info", "children": [], "durationId": "da196767-8b5c-4fd5-b057-1dd82b7e2879", "parent": "3671988a-622b-4fa4-ad40-6df45ae7e10a"}}, {"head": {"id": "e43a34e8-b920-4aa1-a124-26aedfe1b947", "name": "default@CompileArkTS work[30] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458995731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3671988a-622b-4fa4-ad40-6df45ae7e10a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27455197316100, "endTime": 27458989029400}, "additional": {"logType": "info", "children": ["1d90c21c-1cc0-4c07-9485-e669f5bd8f8a", "6a3923aa-2385-487e-bb5a-539515a6a34d", "99615095-6955-43c2-b1f8-8bfe970bc5d4", "6960226c-fbbd-414d-9365-f098b8c57c62", "8e5017fc-4dff-4756-9a44-55474a866cb8", "2d4a84be-00e6-413b-9b10-510f523389cc"], "durationId": "1493a2b1-462c-42d9-b6e3-4c3b7446891b", "parent": "0a0c373d-70c2-48b6-940e-7be1d7c21e2f"}}, {"head": {"id": "0a0c373d-70c2-48b6-940e-7be1d7c21e2f", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27455065316700, "endTime": 27458995899600, "totalTime": 3819364800}, "additional": {"logType": "info", "children": ["3671988a-622b-4fa4-ad40-6df45ae7e10a", "5a54e7e7-8f54-407f-b4d8-2aad8f87a542"], "durationId": "1acf70b9-4f91-4d1a-9cb3-62bec9941b4e"}}, {"head": {"id": "a431d0d8-3cd8-4365-b6b8-920d068c31f5", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459001046600, "endTime": 27459002321900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "85f8e963-012d-4df5-bf1c-aedf47b94c30", "logId": "202c5e26-61b9-41d0-b52b-8bcb29372391"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85f8e963-012d-4df5-bf1c-aedf47b94c30", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458999506400}, "additional": {"logType": "detail", "children": [], "durationId": "a431d0d8-3cd8-4365-b6b8-920d068c31f5"}}, {"head": {"id": "6bc474bb-defd-4d98-ba5d-3763d02270da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27458999996800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f0effdd-9fbc-45be-a20c-e4199e365842", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459000105800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60aa5243-138b-4b96-84e0-22a8a43bc7ac", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459001058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc34de5-e91b-4095-81c4-a33953eadb1e", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459001269400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f9935e-1694-4e0d-a1e0-9e4d3f55d40a", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459002139200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce158e40-6273-4ea1-a498-eff54825d759", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07546234130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459002235000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202c5e26-61b9-41d0-b52b-8bcb29372391", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459001046600, "endTime": 27459002321900}, "additional": {"logType": "info", "children": [], "durationId": "a431d0d8-3cd8-4365-b6b8-920d068c31f5"}}, {"head": {"id": "fd911fed-7025-42a8-98ee-9df5548719d9", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459017233300, "endTime": 27459624388900}, "additional": {"children": ["e02d674a-3524-4dcf-aae2-e34d2e5c3ae6", "534eb9b3-9c87-4caa-b22d-67bec2b92f9f", "6f7a901a-54d6-4e1d-a7a8-fa65f3f594fe"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "f070e71f-f8f3-4b33-bc98-6f65d5cfc00c", "logId": "80d1a843-0491-490f-bdca-cea402d6aa17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f070e71f-f8f3-4b33-bc98-6f65d5cfc00c", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459004690400}, "additional": {"logType": "detail", "children": [], "durationId": "fd911fed-7025-42a8-98ee-9df5548719d9"}}, {"head": {"id": "bfe33a75-0533-4de0-bdd1-c1aa2e0be0eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459005372300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45ede9fd-f109-4991-8594-5175bec55682", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459005496300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a22eefb-5d02-48f5-988e-165470834c4c", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459017246700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3f19861-658e-478e-bd8a-cc93d75a90b0", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459031097100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de210f0-bab5-4cc3-8fcf-f3e702dcd391", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459031231900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f687fb8e-ded0-4073-9d22-f5d4bca1bd82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459031524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6210d367-a939-4b58-a3be-a430323be4b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459031597900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02d674a-3524-4dcf-aae2-e34d2e5c3ae6", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459032507300, "endTime": 27459033801900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd911fed-7025-42a8-98ee-9df5548719d9", "logId": "55c6e5ad-5304-424d-bbec-caffd96a141e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d8bf189-d2f5-4ae0-a056-ecea4f0ceb7c", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459033651500}, "additional": {"logType": "debug", "children": [], "durationId": "fd911fed-7025-42a8-98ee-9df5548719d9"}}, {"head": {"id": "55c6e5ad-5304-424d-bbec-caffd96a141e", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459032507300, "endTime": 27459033801900}, "additional": {"logType": "info", "children": [], "durationId": "e02d674a-3524-4dcf-aae2-e34d2e5c3ae6", "parent": "80d1a843-0491-490f-bdca-cea402d6aa17"}}, {"head": {"id": "534eb9b3-9c87-4caa-b22d-67bec2b92f9f", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459034424900, "endTime": 27459035840900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd911fed-7025-42a8-98ee-9df5548719d9", "logId": "6bd7d800-cbeb-4776-ab0f-1a3dca949d24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6400b06-8e1d-4f68-b4c9-677609d58c89", "name": "default@PackageHap work[32] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459035069700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7a901a-54d6-4e1d-a7a8-fa65f3f594fe", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27459209949500, "endTime": 27459624048100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "fd911fed-7025-42a8-98ee-9df5548719d9", "logId": "39bb9f42-46a2-4f56-b59b-9335538663ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e1c6ee0-cfc0-4c22-a3e8-cba79f254f00", "name": "default@PackageHap work[32] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459035699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e495c8e8-65bf-42ad-9740-229fead05cc9", "name": "default@PackageHap work[32] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459035782100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd7d800-cbeb-4776-ab0f-1a3dca949d24", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459034424900, "endTime": 27459035840900}, "additional": {"logType": "info", "children": [], "durationId": "534eb9b3-9c87-4caa-b22d-67bec2b92f9f", "parent": "80d1a843-0491-490f-bdca-cea402d6aa17"}}, {"head": {"id": "ecd9af60-77cd-4031-afb0-08e8dd45481e", "name": "entry : default@PackageHap cost memory 1.2705841064453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459040643000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "272b6369-45dc-4401-84cb-1fc653955cf0", "name": "default@PackageHap work[32] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459209862900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2663e331-93a5-4401-8f0a-7ac0bbed06d6", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459240569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4458e00-88eb-44c9-b0b6-513261d5cd1b", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459240731300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514a1cbd-d45a-4d7e-8a11-1032b1eabce2", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459240800700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f50e55b-161b-45fa-9db4-5372d3019853", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459240875800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32051288-d74d-4202-803b-317f591490d3", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459240950500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96ac602d-fa23-40c8-9f63-45ac91f8c8fd", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459241050800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5913b33e-1459-4ff7-8371-71f624ac2950", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459624155900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a17178ca-dc73-4f0b-a640-382bdd99ed30", "name": "default@PackageHap work[32] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459624300600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39bb9f42-46a2-4f56-b59b-9335538663ce", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27459209949500, "endTime": 27459624048100}, "additional": {"logType": "info", "children": [], "durationId": "6f7a901a-54d6-4e1d-a7a8-fa65f3f594fe", "parent": "80d1a843-0491-490f-bdca-cea402d6aa17"}}, {"head": {"id": "80d1a843-0491-490f-bdca-cea402d6aa17", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459017233300, "endTime": 27459624388900, "totalTime": 437691600}, "additional": {"logType": "info", "children": ["55c6e5ad-5304-424d-bbec-caffd96a141e", "6bd7d800-cbeb-4776-ab0f-1a3dca949d24", "39bb9f42-46a2-4f56-b59b-9335538663ce"], "durationId": "fd911fed-7025-42a8-98ee-9df5548719d9"}}, {"head": {"id": "5f5d3d49-0a2c-463b-b416-1d9c2ca9dde2", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459629787800, "endTime": 27459631299600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "af474821-c10b-4cc0-ba75-e6ad9f72ea18", "logId": "cf5950dc-f8b5-46ca-96c8-998ac9db6ac5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af474821-c10b-4cc0-ba75-e6ad9f72ea18", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459627098100}, "additional": {"logType": "detail", "children": [], "durationId": "5f5d3d49-0a2c-463b-b416-1d9c2ca9dde2"}}, {"head": {"id": "53a6c49c-99fd-4d53-9a0c-ff18082bc4f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459627557700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914490b8-cfed-4b8f-89fc-c87bd66861cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459627657500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd3d92f2-c6c8-477a-b182-38d2101b6a03", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459629799200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ccf47f-1afc-44fe-b18b-9c672702a5d0", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459630068200}, "additional": {"logType": "warn", "children": [], "durationId": "5f5d3d49-0a2c-463b-b416-1d9c2ca9dde2"}}, {"head": {"id": "bfc13950-d2b1-48d1-bddd-6c0a3da9291e", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459630604100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017c8164-ee8a-4215-b795-7deec0091869", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459630695600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bd00de-275c-47d0-a302-c5c1160c5f78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459630773400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0610dbea-9a00-4ea8-bd78-cac6ed47fde1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459630828400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3560a193-78aa-400a-b248-2943a0ed65cf", "name": "entry : default@SignHap cost memory 0.1310577392578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459631150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb2c8896-5890-49ea-b7d8-23436acc7e16", "name": "runTaskFromQueue task cost before running: 5 s 162 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459631241200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf5950dc-f8b5-46ca-96c8-998ac9db6ac5", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459629787800, "endTime": 27459631299600, "totalTime": 1449100}, "additional": {"logType": "info", "children": [], "durationId": "5f5d3d49-0a2c-463b-b416-1d9c2ca9dde2"}}, {"head": {"id": "df908a97-d82c-49f1-be84-af7e9e76acb0", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459634199100, "endTime": 27459640355400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "90b34bcd-c7c7-49af-b361-f84ebb4b795d", "logId": "deed7013-6374-43e0-8e9b-c1007a81026c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90b34bcd-c7c7-49af-b361-f84ebb4b795d", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459632975600}, "additional": {"logType": "detail", "children": [], "durationId": "df908a97-d82c-49f1-be84-af7e9e76acb0"}}, {"head": {"id": "b29d0cab-b71a-4980-b157-f72a5c9d02c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459633381500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b23425a-cde5-492d-b4a7-9313ef8b10de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459633476800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc10785-d270-4a48-9cf7-9bee0a1870f1", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459634208600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0150dde-1b1d-45be-bbba-af785250ec68", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459638673300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a67e254-baee-496d-bf74-058a93ac3698", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459638943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b2de7f2-c897-49c9-93b8-87f102ff5f48", "name": "entry : default@CollectDebugSymbol cost memory 0.2408447265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459640075600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e133d6a9-4334-4b91-85c3-9d2ad66f3b71", "name": "runTaskFromQueue task cost before running: 5 s 171 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459640229900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deed7013-6374-43e0-8e9b-c1007a81026c", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459634199100, "endTime": 27459640355400, "totalTime": 5997800}, "additional": {"logType": "info", "children": [], "durationId": "df908a97-d82c-49f1-be84-af7e9e76acb0"}}, {"head": {"id": "1463cdfc-650b-4bf5-9e4f-f6a255bd4321", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459642117400, "endTime": 27459642421900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "fdd5be2f-b411-4ee6-bcd3-43d5ad446928", "logId": "8b490c77-2f97-4bc4-bc4e-0057e59def28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdd5be2f-b411-4ee6-bcd3-43d5ad446928", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459642064900}, "additional": {"logType": "detail", "children": [], "durationId": "1463cdfc-650b-4bf5-9e4f-f6a255bd4321"}}, {"head": {"id": "77ec68d1-8e0d-4d54-b049-5847c4508c8d", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459642124900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d8c001-df78-4591-9816-6fda32756f08", "name": "entry : assembleHap cost memory 0.0116424560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459642253500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d73f94-8086-431e-b35f-2d4d452c9ad1", "name": "runTaskFromQueue task cost before running: 5 s 174 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459642361800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b490c77-2f97-4bc4-bc4e-0057e59def28", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459642117400, "endTime": 27459642421900, "totalTime": 223200}, "additional": {"logType": "info", "children": [], "durationId": "1463cdfc-650b-4bf5-9e4f-f6a255bd4321"}}, {"head": {"id": "73314d23-a842-42a5-806f-ce82184c25b3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459649543600, "endTime": 27459649569700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "655b595a-0ea0-4eb7-b01c-22741c2048ea", "logId": "f5519444-6312-497e-bbbe-154c6ad50301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5519444-6312-497e-bbbe-154c6ad50301", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459649543600, "endTime": 27459649569700}, "additional": {"logType": "info", "children": [], "durationId": "73314d23-a842-42a5-806f-ce82184c25b3"}}, {"head": {"id": "4d1b429d-62d1-47ae-b487-f7802259905b", "name": "BUILD SUCCESSFUL in 5 s 181 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459649608000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "5f4ef3c6-874c-49d7-bf2e-62a392223cb2", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27454469328400, "endTime": 27459649857300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 7}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "4e5ffb33-4e33-4194-ab0f-6d887988ee1e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459649884500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f66b52-d6c5-4a24-8a7a-3c83c231bbd9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459649947600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "883ecb55-b805-4086-93d9-3cad0b71d5c2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459649998800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfc9883c-3b17-44f4-847c-fe88c151f5b4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459650046300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "150d39df-8032-4f2e-8305-09f52dc6f5fc", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459650105100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0ef0f1c-e8d3-4782-8ed6-d3a237c4f027", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459650406600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789edd2c-4ca8-44c5-abc4-f7636f8485ff", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459651030700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feeaffd3-5170-4bf2-aa00-f8aad81fb894", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459651291200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4f0171c-5a52-4eb1-820b-6057a764e169", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459651361800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d2e453-fc28-49c3-acab-85a01f0beb28", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459651421200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db8c554c-1e42-47cc-a9f0-785b26219b9d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459651659300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1e7067-6f3a-49ce-8856-9697c9c96f2f", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459652384300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dc645b3-18c0-4b5e-a7c4-a074c89e5530", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459652618100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ccd53bb-d79d-427f-ae8b-fefee522ba5d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459652685100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6afde5a-4614-4720-90f5-e78d1b430495", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459652734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7aabc3-9a40-4765-9d04-c6662c89aed6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459652783100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e53d52c9-6daf-48a9-aefd-c9c477fdc51e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459652829800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d11287-2913-4fcf-9d7c-abe0189560fc", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459653077100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa6d1f5-5bfb-4b51-8235-5fb315551e6d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459653260100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcd815fe-a18c-4974-8e84-75385c11a30e", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459653457200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a50d4029-4cc8-4f8f-99cf-0033bf009fda", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459653697800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "836d3607-a203-4b0f-92bc-8130a153a54d", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459653761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd7651d7-cf5a-4a50-982b-64dae66e7f9b", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459653815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99b3978-e0a0-4777-95f7-082fe05a9df2", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459657388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42bc7c91-c221-45d6-b727-62387d94e024", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459657815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "383d5a99-09f5-446a-a360-1a978db7e3b7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459658894400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa9e9958-2d09-41b4-8a89-c6b7a35c608c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459659132100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4365ff30-cd4c-43fa-971a-81dd7724b9ce", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459659316300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b5213f-b17d-4c65-a924-302e416ca422", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459659762700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa87f0ca-d6e5-48a8-8cee-065b856230e5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459659856800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502bf314-d530-4e29-812f-2e23ee4d152c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459660062000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f3ec7e4-5dd0-4211-9d73-313d27ba5101", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459660273500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21dfa3f0-cd87-48c5-9f74-551cbe6f138b", "name": "Incremental task entry:default@CompileArkTS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459660789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34e984d1-430e-414c-b276-4de7b9127482", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459661857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d6f12b1-828d-4f2f-aa18-4f8256483b7d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459662255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf9663b-d2e7-4698-8a61-df7904507c1c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459663031900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9545412a-c814-440c-9194-76120ecc900e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459663221100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc83c331-b371-4f80-a5e4-ca6dc8ec4e0a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459663388500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14eb27cf-3468-4ac5-a985-6420b1ce64e9", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459663826400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12f72c0-b9f4-4f86-a837-1126f6119e5b", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459664049600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aec6146a-dd43-4d0a-b79b-f89ba4d16a11", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459664118900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daa90e11-0531-486a-80ad-060f1d8302c4", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459664169500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2337827b-cdfc-4cee-8821-86f45f2e8bd3", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459665050800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67f2d84d-4de7-4c47-914e-0785a77c8a06", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459665302300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae585a0-42b0-4258-9f66-220c07484de0", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459665495700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f4c0534-1998-49a4-b5f4-bcd628484146", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459670253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8606551-bf7c-4db9-9d2f-effcf735b048", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459670479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6309904-b8d0-4ca3-bf86-d8fea3fe7cd5", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459670640800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4dd0b26-8e22-47fc-9243-53f0d4e9f6ca", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459670702500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ff7f13-9479-4a78-a4a1-e16d0ebb7ab4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459671097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278bf70a-57d2-4325-90dc-27ceed22abf5", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459672339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3532f602-5d1f-4fcb-a334-4520ebbe50f9", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459672684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c6e005-9adf-4e7f-b2dc-663c47661d01", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459673119900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "562eae69-b908-4aba-bb80-f762ad445341", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459673707700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d002e2-2493-4f4b-a03c-48583a7656ec", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459674428400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5be5fb0-9c94-4eee-a0b9-ff18408b6e8a", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459674569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba3ce150-0a8a-41ab-be35-70217e176455", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459674959600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953578fe-83fb-41ce-92b8-b88e6203777c", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459677609900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f536d44-6037-4784-b899-56ab20960fc9", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459678008400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b3c71da-1ac0-491a-b5c9-011dc686d999", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459678436500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b304ebe0-d6d7-4761-8780-c8a67e25b890", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27459678679800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}