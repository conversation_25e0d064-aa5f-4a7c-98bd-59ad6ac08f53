{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "d573b943-bc06-4b4a-b138-ccab7324bc0c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804061938000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da2115f4-f9bc-4c81-abdd-afb254308082", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804068331300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91545277-dc8c-4942-8c52-71422921384f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804068628500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df65bedd-ee9a-4208-a878-ed46ad87f388", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33804079586100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e3dd24-387c-4a9a-b296-6a2bda1be83f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823156739900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823162938900, "endTime": 33823528987300}, "additional": {"children": ["2b9e216b-8dd8-4fee-a8f8-a49e2394e4ba", "d72516ff-256e-419b-a845-40dd7dea75a0", "11cd2373-aec2-417b-8caa-e4e74d838b8d", "e280f46d-72c2-4147-ae69-61384096680e", "96c4ed11-5e69-42f5-a055-dab3fe476713", "c2886b27-1f72-4c9b-a3e2-4b8553bdd5e5", "071a56bf-9b37-4a07-b718-b21cb949c6d8"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b9e216b-8dd8-4fee-a8f8-a49e2394e4ba", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823162973100, "endTime": 33823178818600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "4a0dd9cd-daf0-4b0d-8c4d-378eafd9c757"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d72516ff-256e-419b-a845-40dd7dea75a0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823178869700, "endTime": 33823527581100}, "additional": {"children": ["d227fa3f-797a-4d9e-9b66-610d186136a9", "94d046bf-4045-47e5-9461-7bc289b9afe0", "087d2a9d-8163-4a53-9513-5751819edda8", "92de2a99-6f6b-42fd-a890-34b440c9a3eb", "263eccd9-32c1-4cbf-83e9-f6d981d6f68e", "56d7e9b6-9024-4f8f-b8a6-a2038278c754", "a30ef4f8-0f72-42f8-9ba5-025104a45525", "a897f2f4-f37e-44f0-aed6-add7097ab6bd", "7230bae2-c58b-4953-9c6b-1d99725c6a87"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11cd2373-aec2-417b-8caa-e4e74d838b8d", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823527605100, "endTime": 33823528978300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "c3498c46-522e-40f0-a617-b65128ec4b63"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e280f46d-72c2-4147-ae69-61384096680e", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823528983800, "endTime": 33823528985100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "fa257cee-79c1-4f20-8bd2-6bdeb7f81d61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96c4ed11-5e69-42f5-a055-dab3fe476713", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823166959200, "endTime": 33823166998000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "66ef9920-ae8a-4132-bd99-d9e494932e54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66ef9920-ae8a-4132-bd99-d9e494932e54", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823166959200, "endTime": 33823166998000}, "additional": {"logType": "info", "children": [], "durationId": "96c4ed11-5e69-42f5-a055-dab3fe476713", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "c2886b27-1f72-4c9b-a3e2-4b8553bdd5e5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823174475000, "endTime": 33823174503700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "bc06bf0a-3f15-48af-aa75-bfbc555981b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc06bf0a-3f15-48af-aa75-bfbc555981b5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823174475000, "endTime": 33823174503700}, "additional": {"logType": "info", "children": [], "durationId": "c2886b27-1f72-4c9b-a3e2-4b8553bdd5e5", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "078737a0-8f64-416f-b1df-952610b6b4a3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823174600200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea78c0b4-fb8d-4269-abed-16af092c2d89", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823178656400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a0dd9cd-daf0-4b0d-8c4d-378eafd9c757", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823162973100, "endTime": 33823178818600}, "additional": {"logType": "info", "children": [], "durationId": "2b9e216b-8dd8-4fee-a8f8-a49e2394e4ba", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "d227fa3f-797a-4d9e-9b66-610d186136a9", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823186157800, "endTime": 33823186165300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "53cad99d-5a97-4cfe-9c08-f9af2b837e68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94d046bf-4045-47e5-9461-7bc289b9afe0", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823186179100, "endTime": 33823190328100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "989e8228-b118-48ea-a022-52de3be425e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "087d2a9d-8163-4a53-9513-5751819edda8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823190351600, "endTime": 33823280843900}, "additional": {"children": ["e05ac974-e540-4faf-a091-f98111c2eb29", "3d9a2617-6840-4f82-8721-bcb11cbaa360", "9159fd11-2a3d-492d-8784-42fc80fc4191"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "4a8e5444-2919-4260-8df3-58e6ac033f34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92de2a99-6f6b-42fd-a890-34b440c9a3eb", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823280857000, "endTime": 33823306964900}, "additional": {"children": ["f2f39a8a-0c66-4c0c-a4ed-f6f299fdf02a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "2f7b1c57-61d1-49d3-9dea-8ab6e8ac3724"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "263eccd9-32c1-4cbf-83e9-f6d981d6f68e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823306976700, "endTime": 33823487210100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "473a5983-48d4-4374-ac92-82f0ec1c6e15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56d7e9b6-9024-4f8f-b8a6-a2038278c754", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823488227900, "endTime": 33823518638400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "361521c3-59d8-481d-84a6-bae0f4ccb9d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a30ef4f8-0f72-42f8-9ba5-025104a45525", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823518662700, "endTime": 33823527358200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "c745ea55-2d43-457d-8539-305935363ca3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a897f2f4-f37e-44f0-aed6-add7097ab6bd", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823527379100, "endTime": 33823527568800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "8e61f91c-d385-4ca6-82c0-000642d225cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53cad99d-5a97-4cfe-9c08-f9af2b837e68", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823186157800, "endTime": 33823186165300}, "additional": {"logType": "info", "children": [], "durationId": "d227fa3f-797a-4d9e-9b66-610d186136a9", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "989e8228-b118-48ea-a022-52de3be425e9", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823186179100, "endTime": 33823190328100}, "additional": {"logType": "info", "children": [], "durationId": "94d046bf-4045-47e5-9461-7bc289b9afe0", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "e05ac974-e540-4faf-a091-f98111c2eb29", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823191007900, "endTime": 33823191028800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "087d2a9d-8163-4a53-9513-5751819edda8", "logId": "fcf58f63-6c58-46eb-b369-ae88ddbe5829"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcf58f63-6c58-46eb-b369-ae88ddbe5829", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823191007900, "endTime": 33823191028800}, "additional": {"logType": "info", "children": [], "durationId": "e05ac974-e540-4faf-a091-f98111c2eb29", "parent": "4a8e5444-2919-4260-8df3-58e6ac033f34"}}, {"head": {"id": "3d9a2617-6840-4f82-8721-bcb11cbaa360", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823193483000, "endTime": 33823279472600}, "additional": {"children": ["34713288-e6e4-467e-9fbb-66113e3b62c9", "d7347d26-99c0-4168-a62a-d6c55013d121"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "087d2a9d-8163-4a53-9513-5751819edda8", "logId": "7b67bb7e-3423-49cd-9f4d-dc65b667b5d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34713288-e6e4-467e-9fbb-66113e3b62c9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823193486200, "endTime": 33823196259000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d9a2617-6840-4f82-8721-bcb11cbaa360", "logId": "5712c46d-6b18-4aaa-9511-bf6fb3781b28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7347d26-99c0-4168-a62a-d6c55013d121", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823196273200, "endTime": 33823279448600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d9a2617-6840-4f82-8721-bcb11cbaa360", "logId": "3a9036d2-42ad-4cde-8973-3f0da0bbc99b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1770e694-5bdc-4304-96a3-7809c3a62f81", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823193518200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83192f7e-54fb-47d3-92af-738661425544", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823196149000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5712c46d-6b18-4aaa-9511-bf6fb3781b28", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823193486200, "endTime": 33823196259000}, "additional": {"logType": "info", "children": [], "durationId": "34713288-e6e4-467e-9fbb-66113e3b62c9", "parent": "7b67bb7e-3423-49cd-9f4d-dc65b667b5d0"}}, {"head": {"id": "1e598224-29aa-4986-aa6a-83b4812c368b", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823196281100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6081db72-4a17-4746-9a9c-03143b0fe542", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823202376800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17e275a4-30a5-4bae-84c4-c617d8979466", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823202479900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a3e94c-b4f2-47e4-b91a-b57e2f445e16", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823202604100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec18b62-8020-4454-9f70-5885e5d99278", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823202719300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4124d6fd-32ae-4fde-bfce-2e2d396ef237", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823204120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c78ecc-a925-4222-be5e-696c63734443", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823207896700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "156096c6-d830-44e6-ab1d-04fa1a0d9499", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823216880300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53deb9c9-416f-4b2c-904c-6ef6097bce8d", "name": "Sdk init in 37 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823245427800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57ab0bed-1b87-446e-8b89-619f04e1f0d9", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823245565100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 53}, "markType": "other"}}, {"head": {"id": "2d03024d-07dd-476d-bd45-7322e5188516", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823245579200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 53}, "markType": "other"}}, {"head": {"id": "c502694c-1da7-488e-ac06-b1f5092a97d0", "name": "Project task initialization takes 32 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823278258300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881fa438-f192-48ad-803e-744b60beb7fd", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823278391500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ccb6a4-af2a-4fb7-8828-a6156f8edbdf", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823279096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f35b40-d92a-4a16-8cf2-b37d86ae85ea", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823279368600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9036d2-42ad-4cde-8973-3f0da0bbc99b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823196273200, "endTime": 33823279448600}, "additional": {"logType": "info", "children": [], "durationId": "d7347d26-99c0-4168-a62a-d6c55013d121", "parent": "7b67bb7e-3423-49cd-9f4d-dc65b667b5d0"}}, {"head": {"id": "7b67bb7e-3423-49cd-9f4d-dc65b667b5d0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823193483000, "endTime": 33823279472600}, "additional": {"logType": "info", "children": ["5712c46d-6b18-4aaa-9511-bf6fb3781b28", "3a9036d2-42ad-4cde-8973-3f0da0bbc99b"], "durationId": "3d9a2617-6840-4f82-8721-bcb11cbaa360", "parent": "4a8e5444-2919-4260-8df3-58e6ac033f34"}}, {"head": {"id": "9159fd11-2a3d-492d-8784-42fc80fc4191", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823280808400, "endTime": 33823280828400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "087d2a9d-8163-4a53-9513-5751819edda8", "logId": "206bd4d0-b6e9-42a4-8d6d-37d89ee6c4c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "206bd4d0-b6e9-42a4-8d6d-37d89ee6c4c6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823280808400, "endTime": 33823280828400}, "additional": {"logType": "info", "children": [], "durationId": "9159fd11-2a3d-492d-8784-42fc80fc4191", "parent": "4a8e5444-2919-4260-8df3-58e6ac033f34"}}, {"head": {"id": "4a8e5444-2919-4260-8df3-58e6ac033f34", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823190351600, "endTime": 33823280843900}, "additional": {"logType": "info", "children": ["fcf58f63-6c58-46eb-b369-ae88ddbe5829", "7b67bb7e-3423-49cd-9f4d-dc65b667b5d0", "206bd4d0-b6e9-42a4-8d6d-37d89ee6c4c6"], "durationId": "087d2a9d-8163-4a53-9513-5751819edda8", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "f2f39a8a-0c66-4c0c-a4ed-f6f299fdf02a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823282432200, "endTime": 33823306913900}, "additional": {"children": ["aa1acc6b-9eca-4f0a-9a3f-96d63c0639e4", "58285625-da9c-4910-b2d1-f96cee59c8e2", "1d3e3c8d-348d-425c-bd96-5e9c2c6fbeda"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92de2a99-6f6b-42fd-a890-34b440c9a3eb", "logId": "29af29dd-013c-4d59-932a-c8e674dc3b8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa1acc6b-9eca-4f0a-9a3f-96d63c0639e4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823285958700, "endTime": 33823285975000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2f39a8a-0c66-4c0c-a4ed-f6f299fdf02a", "logId": "956a21e2-16a5-4890-8340-1d6e0f7c90d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "956a21e2-16a5-4890-8340-1d6e0f7c90d5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823285958700, "endTime": 33823285975000}, "additional": {"logType": "info", "children": [], "durationId": "aa1acc6b-9eca-4f0a-9a3f-96d63c0639e4", "parent": "29af29dd-013c-4d59-932a-c8e674dc3b8f"}}, {"head": {"id": "58285625-da9c-4910-b2d1-f96cee59c8e2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823288193700, "endTime": 33823304181500}, "additional": {"children": ["841e7eaf-eb1e-45b4-848a-b5d12376e6d9", "b640f19a-1029-486c-b32e-8b0f33e67e40"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2f39a8a-0c66-4c0c-a4ed-f6f299fdf02a", "logId": "f47ef63d-d571-47d4-95ea-c0f067de6132"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "841e7eaf-eb1e-45b4-848a-b5d12376e6d9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823288195600, "endTime": 33823292226900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58285625-da9c-4910-b2d1-f96cee59c8e2", "logId": "272dbca4-9677-4d7a-adbf-973f9d9de90a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b640f19a-1029-486c-b32e-8b0f33e67e40", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823292246200, "endTime": 33823304165600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "58285625-da9c-4910-b2d1-f96cee59c8e2", "logId": "87f6812f-659d-442c-bdf2-634842cec62b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c12a3e4-0c8f-4426-8f6f-0bd6d34acdfc", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823288201000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faad00fd-e69c-499a-8ee6-60f07199275e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823292073900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "272dbca4-9677-4d7a-adbf-973f9d9de90a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823288195600, "endTime": 33823292226900}, "additional": {"logType": "info", "children": [], "durationId": "841e7eaf-eb1e-45b4-848a-b5d12376e6d9", "parent": "f47ef63d-d571-47d4-95ea-c0f067de6132"}}, {"head": {"id": "45b84397-afec-419b-abc6-34792ce772d3", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823292257700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee0d0964-a1d8-4e95-a8e7-5b037ac8020d", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823298174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827c1b4d-afb1-40f6-aa10-8fb398e6b611", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823298332400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a6d29b2-447c-4508-b206-43bd5d00e87c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823298596300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "221e4d12-1b6f-4a1a-9494-3e7b18e0403b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823298740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3651fe57-0c9c-4e50-a42c-897e6b62ffd7", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823298803500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9abf4f27-2361-4733-8ae6-e2dac073cffb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823298849400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d84b0779-3b11-4798-ae7c-427860e1afc9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823299836800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f55d77-aac9-403c-95ef-2d84604105ac", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823302734400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e2a865b-7ca6-43a7-b2aa-c184c61e666e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823302887100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c312449d-e19e-425b-bee3-40532f897668", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823304018400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c2c923a-3c2e-426a-916c-14eca1dfef20", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823304105700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87f6812f-659d-442c-bdf2-634842cec62b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823292246200, "endTime": 33823304165600}, "additional": {"logType": "info", "children": [], "durationId": "b640f19a-1029-486c-b32e-8b0f33e67e40", "parent": "f47ef63d-d571-47d4-95ea-c0f067de6132"}}, {"head": {"id": "f47ef63d-d571-47d4-95ea-c0f067de6132", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823288193700, "endTime": 33823304181500}, "additional": {"logType": "info", "children": ["272dbca4-9677-4d7a-adbf-973f9d9de90a", "87f6812f-659d-442c-bdf2-634842cec62b"], "durationId": "58285625-da9c-4910-b2d1-f96cee59c8e2", "parent": "29af29dd-013c-4d59-932a-c8e674dc3b8f"}}, {"head": {"id": "1d3e3c8d-348d-425c-bd96-5e9c2c6fbeda", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823306879100, "endTime": 33823306899000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2f39a8a-0c66-4c0c-a4ed-f6f299fdf02a", "logId": "4d3e3630-2e53-4ba4-90b3-e07d8a5e2509"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d3e3630-2e53-4ba4-90b3-e07d8a5e2509", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823306879100, "endTime": 33823306899000}, "additional": {"logType": "info", "children": [], "durationId": "1d3e3c8d-348d-425c-bd96-5e9c2c6fbeda", "parent": "29af29dd-013c-4d59-932a-c8e674dc3b8f"}}, {"head": {"id": "29af29dd-013c-4d59-932a-c8e674dc3b8f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823282432200, "endTime": 33823306913900}, "additional": {"logType": "info", "children": ["956a21e2-16a5-4890-8340-1d6e0f7c90d5", "f47ef63d-d571-47d4-95ea-c0f067de6132", "4d3e3630-2e53-4ba4-90b3-e07d8a5e2509"], "durationId": "f2f39a8a-0c66-4c0c-a4ed-f6f299fdf02a", "parent": "2f7b1c57-61d1-49d3-9dea-8ab6e8ac3724"}}, {"head": {"id": "2f7b1c57-61d1-49d3-9dea-8ab6e8ac3724", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823280857000, "endTime": 33823306964900}, "additional": {"logType": "info", "children": ["29af29dd-013c-4d59-932a-c8e674dc3b8f"], "durationId": "92de2a99-6f6b-42fd-a890-34b440c9a3eb", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "dc193823-9b4a-4d24-b260-f6cb0f066c4d", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823374936800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b954b2-bf61-4e2b-bb82-8520d111918c", "name": "hvigorfile, resolve hvigorfile dependencies in 181 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823487089800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473a5983-48d4-4374-ac92-82f0ec1c6e15", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823306976700, "endTime": 33823487210100}, "additional": {"logType": "info", "children": [], "durationId": "263eccd9-32c1-4cbf-83e9-f6d981d6f68e", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "7230bae2-c58b-4953-9c6b-1d99725c6a87", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823487975600, "endTime": 33823488208900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d72516ff-256e-419b-a845-40dd7dea75a0", "logId": "f4fe81fe-a6e6-4ffd-a5e1-f45e3bd45cd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70c586eb-4bdc-4f22-9ea5-b2a59d02c47e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823488000400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fe81fe-a6e6-4ffd-a5e1-f45e3bd45cd6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823487975600, "endTime": 33823488208900}, "additional": {"logType": "info", "children": [], "durationId": "7230bae2-c58b-4953-9c6b-1d99725c6a87", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "1c1b30a1-437c-4847-a1dd-82a3101f6be5", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823489246000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af37a854-314f-493b-b8a8-d6ff998b122a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823517793100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "361521c3-59d8-481d-84a6-bae0f4ccb9d7", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823488227900, "endTime": 33823518638400}, "additional": {"logType": "info", "children": [], "durationId": "56d7e9b6-9024-4f8f-b8a6-a2038278c754", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "74f89d2b-0ef8-4048-9ec3-80665c65620b", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823522453500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c284bc4-4953-4cac-8536-08c11a34070a", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823522580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad91a13-2d6b-4971-aedd-15e4926d021a", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823524581200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c92627f-0479-4af7-a6da-75fb1e8de0d1", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823524710300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c745ea55-2d43-457d-8539-305935363ca3", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823518662700, "endTime": 33823527358200}, "additional": {"logType": "info", "children": [], "durationId": "a30ef4f8-0f72-42f8-9ba5-025104a45525", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "39507e40-b827-40a8-9e8a-8e97b05a8006", "name": "Configuration phase cost:342 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823527400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e61f91c-d385-4ca6-82c0-000642d225cc", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823527379100, "endTime": 33823527568800}, "additional": {"logType": "info", "children": [], "durationId": "a897f2f4-f37e-44f0-aed6-add7097ab6bd", "parent": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d"}}, {"head": {"id": "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823178869700, "endTime": 33823527581100}, "additional": {"logType": "info", "children": ["53cad99d-5a97-4cfe-9c08-f9af2b837e68", "989e8228-b118-48ea-a022-52de3be425e9", "4a8e5444-2919-4260-8df3-58e6ac033f34", "2f7b1c57-61d1-49d3-9dea-8ab6e8ac3724", "473a5983-48d4-4374-ac92-82f0ec1c6e15", "361521c3-59d8-481d-84a6-bae0f4ccb9d7", "c745ea55-2d43-457d-8539-305935363ca3", "8e61f91c-d385-4ca6-82c0-000642d225cc", "f4fe81fe-a6e6-4ffd-a5e1-f45e3bd45cd6"], "durationId": "d72516ff-256e-419b-a845-40dd7dea75a0", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "071a56bf-9b37-4a07-b718-b21cb949c6d8", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823528946700, "endTime": 33823528965800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9", "logId": "8db61f9e-8f92-4620-a7f6-9c62b902c265"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8db61f9e-8f92-4620-a7f6-9c62b902c265", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823528946700, "endTime": 33823528965800}, "additional": {"logType": "info", "children": [], "durationId": "071a56bf-9b37-4a07-b718-b21cb949c6d8", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "c3498c46-522e-40f0-a617-b65128ec4b63", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823527605100, "endTime": 33823528978300}, "additional": {"logType": "info", "children": [], "durationId": "11cd2373-aec2-417b-8caa-e4e74d838b8d", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "fa257cee-79c1-4f20-8bd2-6bdeb7f81d61", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823528983800, "endTime": 33823528985100}, "additional": {"logType": "info", "children": [], "durationId": "e280f46d-72c2-4147-ae69-61384096680e", "parent": "936d5bf9-be86-432e-b9ca-d5df30a2e4df"}}, {"head": {"id": "936d5bf9-be86-432e-b9ca-d5df30a2e4df", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823162938900, "endTime": 33823528987300}, "additional": {"logType": "info", "children": ["4a0dd9cd-daf0-4b0d-8c4d-378eafd9c757", "9d63d5ba-1272-48fc-a1e6-af431d5a0c8d", "c3498c46-522e-40f0-a617-b65128ec4b63", "fa257cee-79c1-4f20-8bd2-6bdeb7f81d61", "66ef9920-ae8a-4132-bd99-d9e494932e54", "bc06bf0a-3f15-48af-aa75-bfbc555981b5", "8db61f9e-8f92-4620-a7f6-9c62b902c265"], "durationId": "6ca77a03-09e4-43ea-bf97-5587ae0ac1c9"}}, {"head": {"id": "6096d0e8-42bd-4537-a85a-1f1df21387f0", "name": "Configuration task cost before running: 370 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823529209600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93347296-d46f-453d-8222-a7e3bd271e58", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823543789700, "endTime": 33823564562000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a741e68e-1c8c-41b8-84a9-f80248bb0a5a", "logId": "8b039c25-9a87-469c-89bb-c97cd4064a8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a741e68e-1c8c-41b8-84a9-f80248bb0a5a", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823530880600}, "additional": {"logType": "detail", "children": [], "durationId": "93347296-d46f-453d-8222-a7e3bd271e58"}}, {"head": {"id": "f6aea74f-15db-4802-9b0c-84de30401fd1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823535011600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "149b3f19-3315-4d24-be68-98e772432975", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823535477000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39250aae-24ba-448f-8ba3-e1f3ea7c1a7b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823543803900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7af2c9b-04b0-452e-a923-489c0dd162a3", "name": "Incremental task entry:default@PreBuild pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823564205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a90df3e3-ced3-4f34-888c-cf909e9be5ec", "name": "entry : default@PreBuild cost memory 0.30156707763671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823564468200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b039c25-9a87-469c-89bb-c97cd4064a8a", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823543789700, "endTime": 33823564562000}, "additional": {"logType": "info", "children": [], "durationId": "93347296-d46f-453d-8222-a7e3bd271e58"}}, {"head": {"id": "92bbee95-1002-47f9-a3fb-eecda1d730a2", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823578743400, "endTime": 33823582322900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "170fefc6-0a91-4aaa-a6c6-7fbaa5147ab3", "logId": "8cb3a47d-20a5-4e9a-ba97-4b0a121cd14c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "170fefc6-0a91-4aaa-a6c6-7fbaa5147ab3", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823575811200}, "additional": {"logType": "detail", "children": [], "durationId": "92bbee95-1002-47f9-a3fb-eecda1d730a2"}}, {"head": {"id": "7212e1fd-2dbd-4348-81cb-4276c1d83790", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823577075100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0621a6c-73f2-44b5-a45a-87053e1a22f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823577341500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa14f921-198f-4d18-b8a6-d335448b61cf", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823578770300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3622aaae-136c-4d15-8497-c0dc9bcfb7de", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823580334300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de744476-a51b-4135-ae49-f0c0994fe3f0", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823582093700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba7004a1-3d4a-4262-a4a8-0465abf5ab1c", "name": "entry : default@GenerateMetadata cost memory 0.0937347412109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823582241800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb3a47d-20a5-4e9a-ba97-4b0a121cd14c", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823578743400, "endTime": 33823582322900}, "additional": {"logType": "info", "children": [], "durationId": "92bbee95-1002-47f9-a3fb-eecda1d730a2"}}, {"head": {"id": "1222f7c6-5310-4c63-9c61-044d53b8b63c", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587385500, "endTime": 33823587904700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "43ad5f8a-227c-4ffc-aa95-755518bb77bf", "logId": "93ea06b3-0b88-41f6-8e62-58bdf4dc3556"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43ad5f8a-227c-4ffc-aa95-755518bb77bf", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823584433700}, "additional": {"logType": "detail", "children": [], "durationId": "1222f7c6-5310-4c63-9c61-044d53b8b63c"}}, {"head": {"id": "4e171813-2542-4e15-a5ae-62066c3c47e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587014900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08a6a341-2ee5-44ed-94cc-8c99f60e3c70", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587194000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a4053eb-b54a-4b51-b342-b8b1ec2e2384", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587393300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb46ee5-a46c-4c2b-966f-e78ab989cb67", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587490800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da9bf92-45d9-4b13-b4f4-cffa345a152c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587564100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1955f7d2-4ae1-4395-aabd-89ee3fbbf960", "name": "entry : default@ConfigureCmake cost memory 0.0359954833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587720200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a421bd0-6e77-48e5-bcba-7e555bdcca48", "name": "runTaskFromQueue task cost before running: 428 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ea06b3-0b88-41f6-8e62-58bdf4dc3556", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823587385500, "endTime": 33823587904700, "totalTime": 425500}, "additional": {"logType": "info", "children": [], "durationId": "1222f7c6-5310-4c63-9c61-044d53b8b63c"}}, {"head": {"id": "1e7c8235-bed3-4c66-9685-0117cf334520", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823593049300, "endTime": 33823596008200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "29f1d843-d677-4201-80c8-ab624a1520db", "logId": "61f88bae-c78d-4da4-bdba-493fa1f92286"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29f1d843-d677-4201-80c8-ab624a1520db", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823591033400}, "additional": {"logType": "detail", "children": [], "durationId": "1e7c8235-bed3-4c66-9685-0117cf334520"}}, {"head": {"id": "9c0fa5aa-cf61-4ab8-b7d7-923d38cbf4c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823591753900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61152841-efe9-45e0-9ccd-186125869345", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823591943800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d324a6c-fc1a-4fa6-bf72-7daca38f0be0", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823593061100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4027ff6-0400-4af0-b778-4ec98adbedb6", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823595683900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "293b2241-9451-4fdf-a47c-9de6b2a6c693", "name": "entry : default@MergeProfile cost memory 0.10491180419921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823595918300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f88bae-c78d-4da4-bdba-493fa1f92286", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823593049300, "endTime": 33823596008200}, "additional": {"logType": "info", "children": [], "durationId": "1e7c8235-bed3-4c66-9685-0117cf334520"}}, {"head": {"id": "9fa7245e-6f98-48ee-a24c-488dcf2fcc48", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823600465500, "endTime": 33823603445200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5e9ee54d-7648-48d2-b5e9-943fded4e74c", "logId": "137c7d7d-a74e-4061-b641-29518b52ea45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e9ee54d-7648-48d2-b5e9-943fded4e74c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823598304600}, "additional": {"logType": "detail", "children": [], "durationId": "9fa7245e-6f98-48ee-a24c-488dcf2fcc48"}}, {"head": {"id": "1d2fcbbb-6a61-4261-a436-8f0b58439a32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823599048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99445750-ad60-4e0e-bf4b-cf0bb37a0ba7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823599338900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ce66ab8-30ef-41ac-bef8-7ea55f695940", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823600479900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00d279b9-dc0c-42fc-bf53-341f5cbe7796", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823601696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcc33455-5c49-4b96-83b1-1cdf97795d5c", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823603179300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c6931ba-17e6-43e1-a06e-96529f287844", "name": "entry : default@CreateBuildProfile cost memory 0.10245513916015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823603368900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "137c7d7d-a74e-4061-b641-29518b52ea45", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823600465500, "endTime": 33823603445200}, "additional": {"logType": "info", "children": [], "durationId": "9fa7245e-6f98-48ee-a24c-488dcf2fcc48"}}, {"head": {"id": "659356bb-ebe5-4e53-a898-99e4d5b11623", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607051400, "endTime": 33823607966500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "fcee3176-6cd8-443e-8782-19af4e300041", "logId": "a42980f0-4e2f-4dc0-a9b4-e3af0f5cb85e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcee3176-6cd8-443e-8782-19af4e300041", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823605368600}, "additional": {"logType": "detail", "children": [], "durationId": "659356bb-ebe5-4e53-a898-99e4d5b11623"}}, {"head": {"id": "c9f10049-9c55-441c-8c0d-823fd9ed344e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823605741900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20d8ce3-d870-481d-ac80-88ac3d6fb681", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823605913100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4924988-e05d-4c92-80a6-6968ee7e413c", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c84adc-7048-483a-b719-ea665fe9bc96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607444700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c40cbd69-0d73-4425-aad6-4db73765134d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607571500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17616ce1-f7e9-44eb-b485-88a2810f5ca8", "name": "entry : default@PreCheckSyscap cost memory 0.0362396240234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607740100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cdc7a0f-d68e-40c6-be71-f6fad37ffcb8", "name": "runTaskFromQueue task cost before running: 448 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607857100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42980f0-4e2f-4dc0-a9b4-e3af0f5cb85e", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823607051400, "endTime": 33823607966500, "totalTime": 782500}, "additional": {"logType": "info", "children": [], "durationId": "659356bb-ebe5-4e53-a898-99e4d5b11623"}}, {"head": {"id": "3a608ed9-c2bd-489e-9584-190d804435b8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823628500900, "endTime": 33823629401300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a114182-b70d-4c73-84e1-9a50c0f01847", "logId": "5a57c171-81d6-4376-bc83-3dd52932dd1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a114182-b70d-4c73-84e1-9a50c0f01847", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823611187200}, "additional": {"logType": "detail", "children": [], "durationId": "3a608ed9-c2bd-489e-9584-190d804435b8"}}, {"head": {"id": "cd927530-ac8b-4ecd-869c-b25a06dd3fba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823611769900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34a42b39-0fb8-4322-9de7-32c016a827df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823611915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031057ff-5d0d-42af-b2b7-e1c1ea3de10b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823628510100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf2848ee-412b-472c-8b0b-a8118803c0d4", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823628801600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9613f0b4-c930-42b0-a747-f752adbc1c63", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03850555419921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823629043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a20bbd-e6f2-46fa-848c-7f24d4d120b3", "name": "runTaskFromQueue task cost before running: 470 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823629204600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a57c171-81d6-4376-bc83-3dd52932dd1e", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823628500900, "endTime": 33823629401300, "totalTime": 667000}, "additional": {"logType": "info", "children": [], "durationId": "3a608ed9-c2bd-489e-9584-190d804435b8"}}, {"head": {"id": "9c4dd9c5-db55-48aa-9aee-9d86f5b27e44", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823635583000, "endTime": 33823638843300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "e5da943b-bc93-44f2-91c5-dab49caa6f1e", "logId": "5c5fed86-80c4-488c-a798-16fe1e0f02b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5da943b-bc93-44f2-91c5-dab49caa6f1e", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823632985500}, "additional": {"logType": "detail", "children": [], "durationId": "9c4dd9c5-db55-48aa-9aee-9d86f5b27e44"}}, {"head": {"id": "8fde5cef-995f-480d-9223-7c6c92f6e7b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823633565600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32291020-2c9d-4987-842b-2b8653905a1a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823633704400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b3f8747-db93-4d36-bd29-7d57184acad0", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823635593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663ec388-3e35-472c-9ded-0e3371b4de8c", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823637393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04f79931-943a-4a1b-b348-a284636d5e5f", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823638127500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a17f28-6384-45d1-a767-496a8eded69c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823638477400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44491ced-48cd-40f9-9631-df817d3b5b9b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823638548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "491d2da0-f3ca-4387-823e-037c36e7c0ea", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1175537109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823638679100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c625d6ba-0fd1-4a26-ab59-bf7e65edba42", "name": "runTaskFromQueue task cost before running: 479 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823638780600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c5fed86-80c4-488c-a798-16fe1e0f02b5", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823635583000, "endTime": 33823638843300, "totalTime": 3176500}, "additional": {"logType": "info", "children": [], "durationId": "9c4dd9c5-db55-48aa-9aee-9d86f5b27e44"}}, {"head": {"id": "49fdb490-d0d0-4522-8e4e-705f111e2bd2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643006200, "endTime": 33823643782700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "153bbd9d-7e8c-44c0-a188-acae67dc2f65", "logId": "823df8ce-dd49-4bd0-afeb-cb8b7566c12a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "153bbd9d-7e8c-44c0-a188-acae67dc2f65", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823641744700}, "additional": {"logType": "detail", "children": [], "durationId": "49fdb490-d0d0-4522-8e4e-705f111e2bd2"}}, {"head": {"id": "77da3b79-30aa-4671-923a-df8c0e5087d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823642120400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a2d3b3-0dbb-4675-81cd-10562cf021c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823642307900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58aac6f5-2568-48c1-8b59-a4e6c90581ec", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44d050e-0b83-4db0-8562-9cc5225349a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643440700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69a3db37-0757-4683-b038-296f10797145", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab10a2fa-0483-4aca-b4b5-6006701a8514", "name": "entry : default@BuildNativeWithCmake cost memory 0.03704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643636200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee53b80e-63ec-488c-9f79-f5c763ce3b33", "name": "runTaskFromQueue task cost before running: 484 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "823df8ce-dd49-4bd0-afeb-cb8b7566c12a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823643006200, "endTime": 33823643782700, "totalTime": 692800}, "additional": {"logType": "info", "children": [], "durationId": "49fdb490-d0d0-4522-8e4e-705f111e2bd2"}}, {"head": {"id": "47a65d94-bca3-4792-9c93-ada6dcf4e537", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823650421900, "endTime": 33823664791200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "00e5eda9-b1d2-483f-8953-5316c2be3ec5", "logId": "7756f43e-a6f0-4c6f-abdc-954f53a5df47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00e5eda9-b1d2-483f-8953-5316c2be3ec5", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823645710200}, "additional": {"logType": "detail", "children": [], "durationId": "47a65d94-bca3-4792-9c93-ada6dcf4e537"}}, {"head": {"id": "3b7b9af9-1a13-4cbe-a120-6b8be2a82df7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823646135400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3c31eeb-0a0f-4815-b1ac-95becd9894a0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823646311200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92df7dc8-cdab-43fe-a772-9cb971e9f648", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823650439800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b43357-5053-4466-9147-1071078ba8fe", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823664426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12baa52b-4392-4fc3-9841-bac290266c58", "name": "entry : default@MakePackInfo cost memory 0.13826751708984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823664714800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7756f43e-a6f0-4c6f-abdc-954f53a5df47", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823650421900, "endTime": 33823664791200}, "additional": {"logType": "info", "children": [], "durationId": "47a65d94-bca3-4792-9c93-ada6dcf4e537"}}, {"head": {"id": "3eee6b06-bc4a-48b0-b5f5-0f241ab33540", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823670544400, "endTime": 33823673915000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "f7d1c9c4-06f0-478e-8b88-86ac6d232bae", "logId": "33a4fa85-6850-4850-a919-e9ab0ebb77df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7d1c9c4-06f0-478e-8b88-86ac6d232bae", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823668466800}, "additional": {"logType": "detail", "children": [], "durationId": "3eee6b06-bc4a-48b0-b5f5-0f241ab33540"}}, {"head": {"id": "289e6166-a0e1-43db-9c6e-735d10e6f0fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823668993700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a122bc57-ac5c-48ac-8195-2ae1a3ea4079", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823669118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f163bb5-8d29-45ea-af3a-932c187d3f04", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823670556400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acd1e8cb-0373-4dd6-8c9a-d3903d1c4507", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823670723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d4a889-66a9-409c-8f94-ba3dd57ca31e", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823671464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cfa85a4-38f5-4339-a842-661166a5a392", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823673144700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94cf89ad-fc99-42cd-b3a4-d64175e4ef98", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823673318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62bbaf20-95f7-49c9-b42f-0232872651a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823673407200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eff09d6-dbdf-4e75-9f7b-678eec20ef5a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823673486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93e089b1-64e4-4691-b1ef-fe73164d5ae3", "name": "entry : default@SyscapTransform cost memory 0.15082550048828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823673560500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6758109f-d6e8-4041-9473-e8aa6173f2de", "name": "runTaskFromQueue task cost before running: 514 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823673711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a4fa85-6850-4850-a919-e9ab0ebb77df", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823670544400, "endTime": 33823673915000, "totalTime": 3138200}, "additional": {"logType": "info", "children": [], "durationId": "3eee6b06-bc4a-48b0-b5f5-0f241ab33540"}}, {"head": {"id": "de55b41c-70bd-48cd-b7f2-7520b1fda834", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823677053500, "endTime": 33823679043500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3661ebca-50b4-447d-828c-a3417b74a68a", "logId": "fd20a458-71ed-464d-90dc-cadf9d6453f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3661ebca-50b4-447d-828c-a3417b74a68a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823675703200}, "additional": {"logType": "detail", "children": [], "durationId": "de55b41c-70bd-48cd-b7f2-7520b1fda834"}}, {"head": {"id": "19a2d5e3-eed8-4857-849d-a7d08f2b64e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823676077100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b9e3f2-5604-49f5-a496-e754611e5af4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823676189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "160e7d16-ed2a-454c-a9cd-9623099357e4", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823677060300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb438de1-9a34-400a-91fc-6089003926ff", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823678699500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411cfaff-f3a2-4466-9972-7d7f06f340ca", "name": "entry : default@ProcessProfile cost memory 0.0601654052734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823678958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd20a458-71ed-464d-90dc-cadf9d6453f2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823677053500, "endTime": 33823679043500}, "additional": {"logType": "info", "children": [], "durationId": "de55b41c-70bd-48cd-b7f2-7520b1fda834"}}, {"head": {"id": "18d77615-2617-4e53-bba6-b026feaf247e", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823684991500, "endTime": 33823696990900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c9af35c3-a90c-4afb-b31e-06ff51ff46e6", "logId": "fc1f0fdb-2c58-446e-b523-ae4e1195085e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9af35c3-a90c-4afb-b31e-06ff51ff46e6", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823681912500}, "additional": {"logType": "detail", "children": [], "durationId": "18d77615-2617-4e53-bba6-b026feaf247e"}}, {"head": {"id": "7a41edce-5120-42c7-99b5-284690a3ae2d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823682440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3cc85b9-2855-4d87-bf1b-8171082deff1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823682880200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af420ce3-7588-41b5-937d-0a125caccc60", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823685007100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87ecfa8-feeb-48fe-a5ea-2ed3ee23ff75", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823695497700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d76b04d-4cff-4e35-91dd-2bd2259eca6a", "name": "entry : default@ProcessRouterMap cost memory -5.37017822265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823696603900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc1f0fdb-2c58-446e-b523-ae4e1195085e", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823684991500, "endTime": 33823696990900}, "additional": {"logType": "info", "children": [], "durationId": "18d77615-2617-4e53-bba6-b026feaf247e"}}, {"head": {"id": "4ae89936-c6a5-415c-87d9-852bd1e48a6f", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823702971900, "endTime": 33823704384400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3fbd1e22-8892-4614-ba18-84116fdd1769", "logId": "1916f847-d578-4110-a813-544ca3efee3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fbd1e22-8892-4614-ba18-84116fdd1769", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823701433100}, "additional": {"logType": "detail", "children": [], "durationId": "4ae89936-c6a5-415c-87d9-852bd1e48a6f"}}, {"head": {"id": "934c8498-883e-480d-a353-e9adcd91f948", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823701909300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6afb2b40-e077-4e0d-9569-3c2ccc724108", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823702155600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d1ee18-e433-44cb-a698-44a92b150919", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823702982300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87688e26-88e1-41c7-be3f-a0bcff228d54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823703107800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9200877c-3b24-4b08-b19d-36369e60580d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823703166200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e3645c-a08a-4ba1-adc6-194c0be6ff99", "name": "entry : default@BuildNativeWithNinja cost memory 0.05654144287109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823704195300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b3179e-44d4-46d9-9eda-43a3fbe754b7", "name": "runTaskFromQueue task cost before running: 545 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823704322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1916f847-d578-4110-a813-544ca3efee3b", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823702971900, "endTime": 33823704384400, "totalTime": 1327400}, "additional": {"logType": "info", "children": [], "durationId": "4ae89936-c6a5-415c-87d9-852bd1e48a6f"}}, {"head": {"id": "d589295a-4070-4147-bb1a-fb716f458a1b", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823710512600, "endTime": 33823725584600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "80e0552f-e4cd-4191-b14e-e2e7eee3905f", "logId": "4a4404ab-b1b5-44c6-a06c-eadf4a4b797a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80e0552f-e4cd-4191-b14e-e2e7eee3905f", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823707294400}, "additional": {"logType": "detail", "children": [], "durationId": "d589295a-4070-4147-bb1a-fb716f458a1b"}}, {"head": {"id": "8a63f4d2-4468-4d28-a2d4-dc0389d14461", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823707741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7139d68-eef2-4965-a225-663538c7884b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823707849100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6330ed04-5429-4db4-b8ee-656d1812163b", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823708907300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b889389-0a48-48dd-8825-283b95230df0", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823713778000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6057d013-8888-4ee0-9cc4-953e93ec5e0a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823719602000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10dfd656-5571-4db0-8614-5397e03267ae", "name": "entry : default@ProcessResource cost memory 0.16861724853515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823719778700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a4404ab-b1b5-44c6-a06c-eadf4a4b797a", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823710512600, "endTime": 33823725584600}, "additional": {"logType": "info", "children": [], "durationId": "d589295a-4070-4147-bb1a-fb716f458a1b"}}, {"head": {"id": "68ce2216-f786-4354-a7e9-71fa15a71ef8", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823740712400, "endTime": 33823754930300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9cb62501-3221-41ef-9f7a-c98c47af4702", "logId": "252e2480-7576-4384-a948-54db5aad5dbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cb62501-3221-41ef-9f7a-c98c47af4702", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823733999600}, "additional": {"logType": "detail", "children": [], "durationId": "68ce2216-f786-4354-a7e9-71fa15a71ef8"}}, {"head": {"id": "6fe90d90-643f-43e9-9353-7204b3f8c97e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823734653800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb59f44-a797-427f-9265-68974ab7b6f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823734799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21fbbd8-ca14-4c1e-af6a-a3be61ae5b3b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823740724600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e2b05f-5944-4fce-bcfe-632b6fb99d67", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823754716500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0324d710-2a0d-43ef-be53-cbb26d1ffc5d", "name": "entry : default@GenerateLoaderJson cost memory 0.7564620971679688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823754856100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252e2480-7576-4384-a948-54db5aad5dbf", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823740712400, "endTime": 33823754930300}, "additional": {"logType": "info", "children": [], "durationId": "68ce2216-f786-4354-a7e9-71fa15a71ef8"}}, {"head": {"id": "2aff845a-834c-4db3-ac73-bf2498dc6a1f", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823781500100, "endTime": 33823790867400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "aa042933-6aaa-497d-ae81-3a1c7d065bc0", "logId": "decdd873-b215-4d8d-9651-c9080eb33d5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa042933-6aaa-497d-ae81-3a1c7d065bc0", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823779735600}, "additional": {"logType": "detail", "children": [], "durationId": "2aff845a-834c-4db3-ac73-bf2498dc6a1f"}}, {"head": {"id": "839981c0-b926-4556-9271-15857339a394", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823780250900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68f7f4bb-d25a-4f91-82e0-9323ffbcbd92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823780369600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9312b5bf-591b-45f0-a644-45355d31<PERSON>ea", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823781707200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55405a5-1352-4f99-8d3c-affa3434fe92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823788397800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337e60a6-7005-4cf8-8392-c39ed528463f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823788787100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8c3598-7d1a-43c7-8484-9a1e3f6bb178", "name": "entry : default@ProcessLibs cost memory 0.12496185302734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823790665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a7d6b3-6f57-4d0b-bd31-721ad39a6ac1", "name": "runTaskFromQueue task cost before running: 631 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823790809200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decdd873-b215-4d8d-9651-c9080eb33d5d", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823781500100, "endTime": 33823790867400, "totalTime": 9280100}, "additional": {"logType": "info", "children": [], "durationId": "2aff845a-834c-4db3-ac73-bf2498dc6a1f"}}, {"head": {"id": "55a33e26-14fc-4f2c-b76e-8bbf0866a990", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823796928300, "endTime": 33823839887100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "dd9f9744-f8f5-4022-8d64-3974776de235", "logId": "325a8b1b-76fc-478c-b1ea-839bfe78aa30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd9f9744-f8f5-4022-8d64-3974776de235", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823793468900}, "additional": {"logType": "detail", "children": [], "durationId": "55a33e26-14fc-4f2c-b76e-8bbf0866a990"}}, {"head": {"id": "8bdf1307-375d-4bf9-b31a-d5a98deb2703", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823793847200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d599810-3f0d-4a69-b984-4e2fba1ca4ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823793972400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b9d6ec6-2cec-46e0-94af-3a6f63ad62a4", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823794858800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b125529-997a-4815-b701-4267bf3b9b76", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823796951900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a77e0f64-f11d-42ce-a3aa-a63ecc000651", "name": "Incremental task entry:default@CompileResource pre-execution cost: 40 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823839462900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed244988-241b-4c3d-b579-d538e7213968", "name": "entry : default@CompileResource cost memory 1.4012680053710938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823839723400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "325a8b1b-76fc-478c-b1ea-839bfe78aa30", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823796928300, "endTime": 33823839887100}, "additional": {"logType": "info", "children": [], "durationId": "55a33e26-14fc-4f2c-b76e-8bbf0866a990"}}, {"head": {"id": "3ae6494c-3cdf-4c4b-81ed-8982f4bf197f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823847379000, "endTime": 33823850260200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5ab179b7-2bfb-4cf0-bfe1-c0134d99e2ae", "logId": "076c850b-e3f8-40b3-bbb6-4514aa6eabfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ab179b7-2bfb-4cf0-bfe1-c0134d99e2ae", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823844105300}, "additional": {"logType": "detail", "children": [], "durationId": "3ae6494c-3cdf-4c4b-81ed-8982f4bf197f"}}, {"head": {"id": "98489605-2b4c-4664-babf-3c514ef9e049", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823844567100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5465344-b99a-4700-a1af-300189b69b37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823844671300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed89dced-d479-4167-8321-f685e188af98", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823847389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e95e9bb-1909-464b-b8ec-07edf0ca72fa", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823847627900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "845625d8-8885-4bb2-aa4b-0fa155fc3e58", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823850061900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650e5afd-1ecf-4cd9-a59d-f447e85d78bd", "name": "entry : default@DoNativeStrip cost memory 0.0749359130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823850193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "076c850b-e3f8-40b3-bbb6-4514aa6eabfb", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823847379000, "endTime": 33823850260200}, "additional": {"logType": "info", "children": [], "durationId": "3ae6494c-3cdf-4c4b-81ed-8982f4bf197f"}}, {"head": {"id": "59d6109b-9423-470c-ad41-090907818204", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823857596300, "endTime": 33823878133800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "04ffa1d9-3396-49f0-a38e-35c20d46167a", "logId": "843b76a9-e9fe-4e0c-bd53-3cc3159df124"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04ffa1d9-3396-49f0-a38e-35c20d46167a", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823852412100}, "additional": {"logType": "detail", "children": [], "durationId": "59d6109b-9423-470c-ad41-090907818204"}}, {"head": {"id": "c2ca73bc-b550-4d22-a589-e324549b1e2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823852927400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe7fab9-9510-4998-8c1e-25792ba7a1bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823853037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44b024e3-667c-4f5b-b0e9-8a5aa2407f19", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823857606400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d93ccd-5eb1-4da6-8080-409476290d6c", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823877804300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3894634-3f18-4501-96ad-64d8167313ec", "name": "entry : default@CompileArkTS cost memory 0.6739349365234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823877984000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843b76a9-e9fe-4e0c-bd53-3cc3159df124", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823857596300, "endTime": 33823878133800}, "additional": {"logType": "info", "children": [], "durationId": "59d6109b-9423-470c-ad41-090907818204"}}, {"head": {"id": "d5eabc7a-6420-4b60-8dd2-4650af8fe196", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823888425500, "endTime": 33823891152500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "bb14a188-dd73-491b-b4ff-d22137e61336", "logId": "22050873-ec62-4ed8-9ecd-d31572d9d1f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb14a188-dd73-491b-b4ff-d22137e61336", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823884651200}, "additional": {"logType": "detail", "children": [], "durationId": "d5eabc7a-6420-4b60-8dd2-4650af8fe196"}}, {"head": {"id": "908b6e83-342d-4093-89aa-29d7149419a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823885051500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2544a39-2bde-4d31-94e7-7ff5c19835ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823885276500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1da71a3-9cae-428b-b2c8-f27c42631cbd", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823888436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f22d513-40e4-4b85-944f-827428d9e146", "name": "entry : default@BuildJS cost memory 0.1263580322265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823890943900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884ffe19-0255-4a49-a09e-1dfac6c5c6a5", "name": "runTaskFromQueue task cost before running: 732 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823891068300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22050873-ec62-4ed8-9ecd-d31572d9d1f0", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823888425500, "endTime": 33823891152500, "totalTime": 2620800}, "additional": {"logType": "info", "children": [], "durationId": "d5eabc7a-6420-4b60-8dd2-4650af8fe196"}}, {"head": {"id": "4c76c3a5-f263-45da-96b3-cc07765eeadb", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823897295900, "endTime": 33823899454200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8b090dd9-cc7b-46df-ba12-d60706454f98", "logId": "80848758-d9a0-41f6-b385-01650f41fd21"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b090dd9-cc7b-46df-ba12-d60706454f98", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823892801200}, "additional": {"logType": "detail", "children": [], "durationId": "4c76c3a5-f263-45da-96b3-cc07765eeadb"}}, {"head": {"id": "6f650a9a-8ea4-4a2b-9a31-4927f39936e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823893192700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c835ce-1614-40df-b705-7b937b4ab73a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823893296200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f22690d6-d9eb-48ef-a91f-ba194bfcfedf", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823897306500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a2566d-00aa-44c0-80e9-8a86cd70bb5f", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823897648200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55b9caf6-3948-4b97-9c45-cf15e87ebcd2", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823899269600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f94e47-f233-44ea-840d-09938dc20b3e", "name": "entry : default@CacheNativeLibs cost memory 0.08850860595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823899389700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80848758-d9a0-41f6-b385-01650f41fd21", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823897295900, "endTime": 33823899454200}, "additional": {"logType": "info", "children": [], "durationId": "4c76c3a5-f263-45da-96b3-cc07765eeadb"}}, {"head": {"id": "7f26e140-54c6-46a1-a65e-46013b4f6279", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823902128400, "endTime": 33823903793000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "fd3f9640-28f9-4947-a82d-0a418f87444b", "logId": "0c606212-9f4a-4ce8-b9f9-99e2d50c06fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd3f9640-28f9-4947-a82d-0a418f87444b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823900853900}, "additional": {"logType": "detail", "children": [], "durationId": "7f26e140-54c6-46a1-a65e-46013b4f6279"}}, {"head": {"id": "06279903-d3b7-4bd8-a115-47f2a4239d3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823901172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fafb9a2-b3c7-4b4b-b199-79443d66fa9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823901264800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7dd9d7-5058-4351-a38d-e729c303e09b", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823902141900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b44afe9-f4f5-43e5-aad3-ca2239d4879f", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823902406800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d99d193-619d-40dc-b158-97b8447e4076", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823903502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bebb2be8-ea72-4193-a758-2b4da74e245e", "name": "entry : default@GeneratePkgModuleJson cost memory 0.070465087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823903650300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c606212-9f4a-4ce8-b9f9-99e2d50c06fb", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823902128400, "endTime": 33823903793000}, "additional": {"logType": "info", "children": [], "durationId": "7f26e140-54c6-46a1-a65e-46013b4f6279"}}, {"head": {"id": "5ebed134-c164-416a-a218-c554e1621d9a", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823913109400, "endTime": 33823926042800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "b5253f37-8d0b-44c6-8e91-ae07f9d2ba0c", "logId": "bb29cb1a-a73a-4f7d-b89f-50e2147add5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5253f37-8d0b-44c6-8e91-ae07f9d2ba0c", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823906220700}, "additional": {"logType": "detail", "children": [], "durationId": "5ebed134-c164-416a-a218-c554e1621d9a"}}, {"head": {"id": "0a5a5376-1740-4d9d-a294-87be0dffc2a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823906581600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dd701ae-61b7-45f3-b95d-67e4de7d2b83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823906689800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40209b5e-c0f7-41f6-a8b4-4995a31d6eb4", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823913121100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec07423-0304-483e-a178-33eec7dce504", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823925838500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69708903-16a0-4b6c-9232-04818be77843", "name": "entry : default@PackageHap cost memory 0.834197998046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823925975500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb29cb1a-a73a-4f7d-b89f-50e2147add5a", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823913109400, "endTime": 33823926042800}, "additional": {"logType": "info", "children": [], "durationId": "5ebed134-c164-416a-a218-c554e1621d9a"}}, {"head": {"id": "f71c9af6-0c01-48a4-b24d-022949e24216", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********8400, "endTime": 33823935692100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "8f31d069-1825-4738-a512-ae264c1b572d", "logId": "7d53d73f-e9d2-4df2-b232-57f67ef68e3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f31d069-1825-4738-a512-ae264c1b572d", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823929371300}, "additional": {"logType": "detail", "children": [], "durationId": "f71c9af6-0c01-48a4-b24d-022949e24216"}}, {"head": {"id": "6a4089d3-de4f-44df-9b2b-5cf9eeda6525", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823929739800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6507fa4-847b-4072-a81f-0859cf973511", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823929842200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6352e9f6-03a2-4df6-b728-771af5c03382", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823933582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0da4d4d-f0be-4261-9477-3eea8656a9ab", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823934076700}, "additional": {"logType": "warn", "children": [], "durationId": "f71c9af6-0c01-48a4-b24d-022949e24216"}}, {"head": {"id": "25f46b48-47cc-4794-a63b-42d0b044d4a6", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823935049100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8309c154-d210-4199-80a1-199b04e08750", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823935175400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72c563f-16aa-4e78-a6ff-d47aef052200", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823935256100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f25288-926d-40d3-ba3f-e183f0a6c552", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823935317600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806adc4e-75c5-4ebb-82e1-a806879d3b09", "name": "entry : default@SignHap cost memory 0.11714935302734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823935511300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2813185-284a-45ee-9b6e-d69364bf783c", "name": "runTaskFromQueue task cost before running: 776 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823935584600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d53d73f-e9d2-4df2-b232-57f67ef68e3e", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********8400, "endTime": 33823935692100, "totalTime": 2002900}, "additional": {"logType": "info", "children": [], "durationId": "f71c9af6-0c01-48a4-b24d-022949e24216"}}, {"head": {"id": "8db4d572-9ec4-4a45-a261-7ff3b290ef1d", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823939054000, "endTime": 33823943636300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2f0ed526-284e-4f68-a8f7-9902ff93c74b", "logId": "3b71ccff-53f2-48c1-acca-d2955f7bc0c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f0ed526-284e-4f68-a8f7-9902ff93c74b", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823937618600}, "additional": {"logType": "detail", "children": [], "durationId": "8db4d572-9ec4-4a45-a261-7ff3b290ef1d"}}, {"head": {"id": "da29f1e1-fcd6-408e-aa8b-8e76d8d4b9b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823937938000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6658e58a-03a6-4928-b2cb-dff379b839b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823938029600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09ae118b-bda4-417d-ac41-29d58f2df3fc", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823939064300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d70643-3247-4baf-a60f-7f846d7fddd9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823943346900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7077e6cf-71bb-49ee-91b5-66a565f1f286", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823943442800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5149f14f-144c-46a3-b3dc-7be3fe56ef50", "name": "entry : default@CollectDebugSymbol cost memory 0.238555908203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823943511700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e21bf8-b15c-44d6-b714-410b9c11b2f0", "name": "runTaskFromQueue task cost before running: 784 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823943582300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b71ccff-53f2-48c1-acca-d2955f7bc0c3", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823939054000, "endTime": 33823943636300, "totalTime": 4511600}, "additional": {"logType": "info", "children": [], "durationId": "8db4d572-9ec4-4a45-a261-7ff3b290ef1d"}}, {"head": {"id": "0b625265-9e2b-4337-bad6-8e208811918b", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823945179100, "endTime": 33823945414300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "679823ec-0521-4b6d-b60f-63079f577af2", "logId": "97e57f86-2a86-42d8-8b53-e87d753f6a86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "679823ec-0521-4b6d-b60f-63079f577af2", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823945139800}, "additional": {"logType": "detail", "children": [], "durationId": "0b625265-9e2b-4337-bad6-8e208811918b"}}, {"head": {"id": "ef5d9ce8-2707-467d-9036-fd23b27641a9", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823945185500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b73b0896-da63-4266-a09b-99767f171dc0", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823945295700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d5bd4d-4df0-46bc-a99d-fe4657fb93d4", "name": "runTaskFromQueue task cost before running: 786 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823945363400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e57f86-2a86-42d8-8b53-e87d753f6a86", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823945179100, "endTime": 33823945414300, "totalTime": 169000}, "additional": {"logType": "info", "children": [], "durationId": "0b625265-9e2b-4337-bad6-8e208811918b"}}, {"head": {"id": "c21e7921-a103-4cb0-bfb8-edee87b566dc", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952469200, "endTime": 33823952487600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c8aaf718-a544-41c7-82ec-7287ab783532", "logId": "65a916ff-378c-430a-9e20-c8d22e5d4c4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65a916ff-378c-430a-9e20-c8d22e5d4c4f", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952469200, "endTime": 33823952487600}, "additional": {"logType": "info", "children": [], "durationId": "c21e7921-a103-4cb0-bfb8-edee87b566dc"}}, {"head": {"id": "6e7e3895-882b-4e3e-bc26-f6209facf984", "name": "BUILD SUCCESSFUL in 793 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952520500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "f4ec6b25-39a4-4c8b-951a-0eecbaed3fb7", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823159940500, "endTime": 33823952715300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 53}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "36fc2c19-0665-4e2e-8de1-a37035325201", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2771d22f-2c3a-4181-affe-03b03af5e7ce", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f9ca42-6387-43bf-b92d-2fba081f95ce", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952846000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8dc06cd-18b8-432d-8a26-1e7738d0ab70", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952882400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb18c0d-697c-401e-8663-77c9951f4384", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823952931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86a49980-9bd5-4794-94c2-bcbb82534aa9", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823953233100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df83f44d-c5a0-4990-98e3-6bb9ef41a8b9", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823953859300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaffc035-ece0-494b-b3ea-e2e00b72ad11", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823954136600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65739b43-9e33-4b9a-bb2b-3bf17cc63d34", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823954206000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7b7757-f468-42ea-8abd-3d6f7934cba1", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823954261600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9107ec30-834c-432a-8530-c10d580cd9a9", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823954557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0e02e8-fa68-4756-aaec-2b3e23d84af9", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823955492100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90057b58-9c7d-4707-b046-2af1aa04e5ab", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823955951600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "260101ce-dcfb-4df3-84be-3635a4<PERSON><PERSON>ff", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823956033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f472a9-54b7-4a3c-8aa5-eee932d62dae", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823956086400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "939a485d-6d92-46ee-bcaf-ebc0cb1f9dca", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823956136200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0da32d81-22db-4643-b23d-90312e2d799f", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823956178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21b79ae8-2299-4605-8050-3cc65c4adada", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823956447700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee3cb5a-66e4-4f03-a541-c73ff41dc9db", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823956713900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a089cca-3e84-4fa9-964e-54143a7b42b2", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823957201800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74c9bfac-f5c7-45a3-ad41-d75cf79abe58", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823957764800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef5c4a28-2eb3-4970-acff-81833a2a0833", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823957850200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e0c16c-e7d6-4896-aca5-c5ffecd6221d", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823957909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7898a2-6337-4a79-ad5d-cb56a1d6cd5a", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823957949400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f01835e0-4751-4d76-be44-e20345fc487a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823959345400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "accc69fb-7052-4748-809b-b14d2107c2fa", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823959859600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0797c46a-dc96-461d-8a8d-8f0f6c4d6ba9", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823960706200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2868725-1d22-4018-b7e5-3236b1d89e08", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823960902900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "912e6c05-fb34-4169-983f-289a3e0b200a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823961304300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da361a5f-eb99-4d31-8c70-07ed177f3147", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823962399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55779ec3-ca8a-4bbd-aa01-acc259d135ff", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823962871600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a887005-806a-488e-bd25-2011a763cdc6", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823962950700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9228ef-613f-4c3f-b3c3-e20cd2e00b1b", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823962999600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad661fe-99f4-4a68-8bb5-736e60338398", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823963042500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f1b0129-afb2-403e-a4d0-d87b96e5ad75", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823963313200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d015bda-a161-4385-a543-06841ca1bafa", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823963572200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13602b9a-cb0e-43cb-979b-3593602bb383", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823963791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "032047fd-a3ff-4606-b41e-e26840c5b932", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823967599600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c23f443-8041-4d23-8f47-3d27ec4bb519", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823967892400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8dce83c-930d-4681-bd78-89d2a189a88d", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823968175800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dcb3e96-81e8-4254-9220-0a429b5cab75", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33823968504200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}