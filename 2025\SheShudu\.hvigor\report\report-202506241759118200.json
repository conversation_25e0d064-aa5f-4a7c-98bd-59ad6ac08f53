{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "55d72223-3ccc-49a5-8f9b-3de93d2984dc", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180789538500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24190765-25da-4291-9132-5ca2a2d7591e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180794141400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a6ce72-815b-4aa8-a783-434808d496ef", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180794674500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7116da-0c4e-439a-a85e-db0655846dcf", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34180799106200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08fe16b1-4a4e-4175-842a-84632e85476c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183713866700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183719689100, "endTime": 34183959765400}, "additional": {"children": ["05255ded-12c2-40b5-ab0d-61ef42d6ee4f", "41d205d5-0035-40ac-a37c-2944e144c61a", "c8502a53-07e0-4c2a-bdab-7b67ce1e1dbe", "ad06faa1-99d2-4a88-a9c0-c03c466c222a", "3050c5dd-98b3-440e-8b71-4e730d13879e", "63ff1696-8010-400b-9994-d777ebe69c04", "c894bd99-d64e-4023-8ec6-11e393a9acd9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05255ded-12c2-40b5-ab0d-61ef42d6ee4f", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183719692300, "endTime": 34183731486500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "b155c33d-45fa-421c-aa9a-94f85cc51a7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41d205d5-0035-40ac-a37c-2944e144c61a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183731508900, "endTime": 34183958772000}, "additional": {"children": ["b9121482-cc30-43e1-ba83-f0abeb3d4465", "d74a2b29-8398-469d-bc0d-7114c4eb5b2b", "2c708a4c-02e8-4afa-a7a4-657e2386fe50", "52839100-3159-4641-8a7c-88b996e63d49", "b1c35fa3-bbce-4966-9b38-587bd26acac8", "6c02fe71-0558-4516-b8f4-37d25f2156c8", "dcc22f11-5273-49a8-9662-8f3ef9b682a7", "a49c8cfe-d172-4ad8-ae0e-a36eb8a04d8f", "cc4e217f-c2a3-4178-9c00-a5e4e1e18317"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "009980b2-5c02-40a8-861f-d1f9c4792523"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8502a53-07e0-4c2a-bdab-7b67ce1e1dbe", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183958789000, "endTime": 34183959759000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "fc9f109a-8819-46a8-95e1-27cd90a152bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad06faa1-99d2-4a88-a9c0-c03c466c222a", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183959762800, "endTime": 34183959763600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "d5b3ecc0-4287-485b-8507-bfb9d6ba6d85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3050c5dd-98b3-440e-8b71-4e730d13879e", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183722449600, "endTime": 34183722499200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "9fc318db-08fd-4edb-9bcd-ba443a96b53d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fc318db-08fd-4edb-9bcd-ba443a96b53d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183722449600, "endTime": 34183722499200}, "additional": {"logType": "info", "children": [], "durationId": "3050c5dd-98b3-440e-8b71-4e730d13879e", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "63ff1696-8010-400b-9994-d777ebe69c04", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183727174700, "endTime": 34183727197100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "9349b9ee-309b-458b-abec-0dbb0e1ae22e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9349b9ee-309b-458b-abec-0dbb0e1ae22e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183727174700, "endTime": 34183727197100}, "additional": {"logType": "info", "children": [], "durationId": "63ff1696-8010-400b-9994-d777ebe69c04", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "6fe63140-5685-4a6f-9759-7d1551853635", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183727248800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bb18b03-88a5-42f1-8ea7-b180aa4cb915", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183731340100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b155c33d-45fa-421c-aa9a-94f85cc51a7a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183719692300, "endTime": 34183731486500}, "additional": {"logType": "info", "children": [], "durationId": "05255ded-12c2-40b5-ab0d-61ef42d6ee4f", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "b9121482-cc30-43e1-ba83-f0abeb3d4465", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183736049000, "endTime": 34183736057900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "f210da11-221e-43d8-ae1e-add80b22d1fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d74a2b29-8398-469d-bc0d-7114c4eb5b2b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183736072500, "endTime": 34183740542000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "fb0e4df5-591f-4c49-9a24-0169b3effc99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c708a4c-02e8-4afa-a7a4-657e2386fe50", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183740562700, "endTime": 34183838041000}, "additional": {"children": ["8b15ef17-125f-40f7-a280-dd28cacd42c8", "8b513835-ef84-4060-9f95-8f9f2ceedfff", "3989ca03-9b4e-4849-baf1-acb8bd282a25"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "4c9cc8f9-4fd7-42fb-bb46-ba5e0ff9cff2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52839100-3159-4641-8a7c-88b996e63d49", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183838054900, "endTime": 34183867505600}, "additional": {"children": ["3516de3c-1628-4292-85e9-4cbaf2e0dc9a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "9fc9079c-ebfc-4851-9ff4-fb4b89eec302"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1c35fa3-bbce-4966-9b38-587bd26acac8", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183867511600, "endTime": 34183941021500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "a54befee-77ac-4aad-bdc4-80d385002a6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c02fe71-0558-4516-b8f4-37d25f2156c8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183942002300, "endTime": 34183949911700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "879701d8-5411-4d5d-b634-46bce64bfc62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcc22f11-5273-49a8-9662-8f3ef9b682a7", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183949933500, "endTime": 34183958610600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "911f21f7-6c50-46c0-9b81-fe8768c27a11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a49c8cfe-d172-4ad8-ae0e-a36eb8a04d8f", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183958640600, "endTime": 34183958762300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "6863ddb8-d504-47ea-9dba-1b62f0f67dab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f210da11-221e-43d8-ae1e-add80b22d1fd", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183736049000, "endTime": 34183736057900}, "additional": {"logType": "info", "children": [], "durationId": "b9121482-cc30-43e1-ba83-f0abeb3d4465", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "fb0e4df5-591f-4c49-9a24-0169b3effc99", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183736072500, "endTime": 34183740542000}, "additional": {"logType": "info", "children": [], "durationId": "d74a2b29-8398-469d-bc0d-7114c4eb5b2b", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "8b15ef17-125f-40f7-a280-dd28cacd42c8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183741160400, "endTime": 34183741187200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2c708a4c-02e8-4afa-a7a4-657e2386fe50", "logId": "46a13a3f-9c42-40d0-9b13-baca42c5f011"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46a13a3f-9c42-40d0-9b13-baca42c5f011", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183741160400, "endTime": 34183741187200}, "additional": {"logType": "info", "children": [], "durationId": "8b15ef17-125f-40f7-a280-dd28cacd42c8", "parent": "4c9cc8f9-4fd7-42fb-bb46-ba5e0ff9cff2"}}, {"head": {"id": "8b513835-ef84-4060-9f95-8f9f2ceedfff", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183743072100, "endTime": 34183836415000}, "additional": {"children": ["4645b29e-640a-4db2-87ac-834509d96740", "05830462-9f7b-4fde-941d-34e33e29543a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2c708a4c-02e8-4afa-a7a4-657e2386fe50", "logId": "27e0364c-4d69-4bef-b24b-75c5b520823b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4645b29e-640a-4db2-87ac-834509d96740", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183743074400, "endTime": 34183748013300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b513835-ef84-4060-9f95-8f9f2ceedfff", "logId": "7f3e6fbe-d56f-4f4b-b633-34a19915c609"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05830462-9f7b-4fde-941d-34e33e29543a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183748031200, "endTime": 34183836400300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8b513835-ef84-4060-9f95-8f9f2ceedfff", "logId": "04b7adb2-2dbc-429b-a056-ac1d35b5b1b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27c946c9-195a-40d4-b343-5756bfa70e25", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183743081900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7911502f-4b89-4d1b-a49b-8f2c3e13332e", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183747875600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f3e6fbe-d56f-4f4b-b633-34a19915c609", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183743074400, "endTime": 34183748013300}, "additional": {"logType": "info", "children": [], "durationId": "4645b29e-640a-4db2-87ac-834509d96740", "parent": "27e0364c-4d69-4bef-b24b-75c5b520823b"}}, {"head": {"id": "8a69ec32-7bab-4334-9560-2f3b00cc7ec0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183748039800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d39e1cf-12b4-48da-91d5-5991aa7f0e5d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183754653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2647cb9-9ea2-44d7-a025-033e46164d82", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183754778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de589fd1-eb4b-4db4-971a-5e7ed8d9275f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183754959200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a7f9895-7658-4605-964e-e0c05f694c88", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183755047800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaab5ee7-e209-42d0-a39d-0ad45641a322", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183756671000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56cb531f-eaa9-4718-84ae-22281863230c", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183760795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73c30c41-2309-4168-9918-ecbeed302e28", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183774177200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35845df4-f55c-4df1-b853-7b51466f60b7", "name": "Sdk init in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183807811900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc3028c-9334-48bc-aa30-8b17ab913e06", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183807973700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 59}, "markType": "other"}}, {"head": {"id": "dd796e38-187a-4df8-b46c-2cb5614ee3fe", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183807990000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 59}, "markType": "other"}}, {"head": {"id": "1b7fdee9-3768-4bfb-984c-d05145ffc5f7", "name": "Project task initialization takes 27 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183836096900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4108f9-9f76-42ab-becf-83ba38d20b73", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183836252000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7facd0f7-0209-4f48-a8a1-3ba9eb74badc", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183836308200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7321f219-f1ab-417f-8c54-5c4a18025c0b", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183836353000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b7adb2-2dbc-429b-a056-ac1d35b5b1b9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183748031200, "endTime": 34183836400300}, "additional": {"logType": "info", "children": [], "durationId": "05830462-9f7b-4fde-941d-34e33e29543a", "parent": "27e0364c-4d69-4bef-b24b-75c5b520823b"}}, {"head": {"id": "27e0364c-4d69-4bef-b24b-75c5b520823b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183743072100, "endTime": 34183836415000}, "additional": {"logType": "info", "children": ["7f3e6fbe-d56f-4f4b-b633-34a19915c609", "04b7adb2-2dbc-429b-a056-ac1d35b5b1b9"], "durationId": "8b513835-ef84-4060-9f95-8f9f2ceedfff", "parent": "4c9cc8f9-4fd7-42fb-bb46-ba5e0ff9cff2"}}, {"head": {"id": "3989ca03-9b4e-4849-baf1-acb8bd282a25", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183838002600, "endTime": 34183838023700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2c708a4c-02e8-4afa-a7a4-657e2386fe50", "logId": "2713efee-3d57-4f36-8e70-6b433e672252"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2713efee-3d57-4f36-8e70-6b433e672252", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183838002600, "endTime": 34183838023700}, "additional": {"logType": "info", "children": [], "durationId": "3989ca03-9b4e-4849-baf1-acb8bd282a25", "parent": "4c9cc8f9-4fd7-42fb-bb46-ba5e0ff9cff2"}}, {"head": {"id": "4c9cc8f9-4fd7-42fb-bb46-ba5e0ff9cff2", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183740562700, "endTime": 34183838041000}, "additional": {"logType": "info", "children": ["46a13a3f-9c42-40d0-9b13-baca42c5f011", "27e0364c-4d69-4bef-b24b-75c5b520823b", "2713efee-3d57-4f36-8e70-6b433e672252"], "durationId": "2c708a4c-02e8-4afa-a7a4-657e2386fe50", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "3516de3c-1628-4292-85e9-4cbaf2e0dc9a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183838966700, "endTime": 34183867493800}, "additional": {"children": ["a5908c6d-caf5-449d-a0ac-229049a76861", "49ba672f-a11a-4d0c-abc0-4f73a6223624", "f458616c-46b4-4db5-beaf-97d0304997b4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52839100-3159-4641-8a7c-88b996e63d49", "logId": "834574a0-28d2-4e91-914f-bcce06ba0380"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5908c6d-caf5-449d-a0ac-229049a76861", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183844021100, "endTime": 34183844040700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3516de3c-1628-4292-85e9-4cbaf2e0dc9a", "logId": "f0c308fd-d434-4930-af0f-622775e9c602"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0c308fd-d434-4930-af0f-622775e9c602", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183844021100, "endTime": 34183844040700}, "additional": {"logType": "info", "children": [], "durationId": "a5908c6d-caf5-449d-a0ac-229049a76861", "parent": "834574a0-28d2-4e91-914f-bcce06ba0380"}}, {"head": {"id": "49ba672f-a11a-4d0c-abc0-4f73a6223624", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183846989100, "endTime": 34183866058700}, "additional": {"children": ["5cb40970-4d92-4512-908a-f30dfe314a25", "fed9fdc3-ccba-4b32-921c-05ab26a63616"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3516de3c-1628-4292-85e9-4cbaf2e0dc9a", "logId": "03b69fc9-83e9-47a1-9f12-2c590789fab5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cb40970-4d92-4512-908a-f30dfe314a25", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183846993800, "endTime": 34183851521200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49ba672f-a11a-4d0c-abc0-4f73a6223624", "logId": "2385ed50-9fa7-4911-9baf-d2268ab14d43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fed9fdc3-ccba-4b32-921c-05ab26a63616", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183851543500, "endTime": 34183866045600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49ba672f-a11a-4d0c-abc0-4f73a6223624", "logId": "a9e7e2dc-16dd-42e7-9ccb-d055bf05bef3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f62cf181-e0a9-4590-9efb-10349fd7f6d6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183847000100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2810c31b-e879-4542-bcbc-a6b08596c52c", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183851370400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2385ed50-9fa7-4911-9baf-d2268ab14d43", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183846993800, "endTime": 34183851521200}, "additional": {"logType": "info", "children": [], "durationId": "5cb40970-4d92-4512-908a-f30dfe314a25", "parent": "03b69fc9-83e9-47a1-9f12-2c590789fab5"}}, {"head": {"id": "20de98d5-4762-4df9-a6e8-11791c52b178", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183851557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "329229ef-c1e7-4b29-8a45-33e7d195d2f9", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183860159100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f4c0bd-c29e-424a-92f6-e7fc834de68c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183860321300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934fb5f8-7577-40eb-97b4-8f7056beefbc", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183860543400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ccba5e-14b1-47a7-b360-71fdf6c8283b", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183860756200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b536e90-da10-4ab9-a81b-ce5af7d06986", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183860855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4dba690-d866-4ece-8549-1feaf47b1cbb", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183861567900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0afc4b43-da81-4781-8041-71fbba9e1fd9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183861681900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eefab532-b796-4aed-9c32-2e64bd010240", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183865750800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93379f95-f177-4f6b-9e52-ffa809384454", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183865895100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493642f0-0c86-43ba-8e35-aa6c91dacb7d", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183865952300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7c8363d-1b4d-48db-a834-ebc4015035e7", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183865999700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e7e2dc-16dd-42e7-9ccb-d055bf05bef3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183851543500, "endTime": 34183866045600}, "additional": {"logType": "info", "children": [], "durationId": "fed9fdc3-ccba-4b32-921c-05ab26a63616", "parent": "03b69fc9-83e9-47a1-9f12-2c590789fab5"}}, {"head": {"id": "03b69fc9-83e9-47a1-9f12-2c590789fab5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183846989100, "endTime": 34183866058700}, "additional": {"logType": "info", "children": ["2385ed50-9fa7-4911-9baf-d2268ab14d43", "a9e7e2dc-16dd-42e7-9ccb-d055bf05bef3"], "durationId": "49ba672f-a11a-4d0c-abc0-4f73a6223624", "parent": "834574a0-28d2-4e91-914f-bcce06ba0380"}}, {"head": {"id": "f458616c-46b4-4db5-beaf-97d0304997b4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183867465600, "endTime": 34183867480200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3516de3c-1628-4292-85e9-4cbaf2e0dc9a", "logId": "65099568-8969-4149-bcf7-0575de9586e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65099568-8969-4149-bcf7-0575de9586e3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183867465600, "endTime": 34183867480200}, "additional": {"logType": "info", "children": [], "durationId": "f458616c-46b4-4db5-beaf-97d0304997b4", "parent": "834574a0-28d2-4e91-914f-bcce06ba0380"}}, {"head": {"id": "834574a0-28d2-4e91-914f-bcce06ba0380", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183838966700, "endTime": 34183867493800}, "additional": {"logType": "info", "children": ["f0c308fd-d434-4930-af0f-622775e9c602", "03b69fc9-83e9-47a1-9f12-2c590789fab5", "65099568-8969-4149-bcf7-0575de9586e3"], "durationId": "3516de3c-1628-4292-85e9-4cbaf2e0dc9a", "parent": "9fc9079c-ebfc-4851-9ff4-fb4b89eec302"}}, {"head": {"id": "9fc9079c-ebfc-4851-9ff4-fb4b89eec302", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183838054900, "endTime": 34183867505600}, "additional": {"logType": "info", "children": ["834574a0-28d2-4e91-914f-bcce06ba0380"], "durationId": "52839100-3159-4641-8a7c-88b996e63d49", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "48ffc0ea-023b-45d3-a425-38239896cd9e", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183891223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95e2cc92-afc3-45f6-bc76-dc05c0b087df", "name": "hvigorfile, resolve hvigorfile dependencies in 74 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183940865400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a54befee-77ac-4aad-bdc4-80d385002a6c", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183867511600, "endTime": 34183941021500}, "additional": {"logType": "info", "children": [], "durationId": "b1c35fa3-bbce-4966-9b38-587bd26acac8", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "cc4e217f-c2a3-4178-9c00-a5e4e1e18317", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183941720300, "endTime": 34183941985300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "41d205d5-0035-40ac-a37c-2944e144c61a", "logId": "f1d3e942-a7af-4a0e-ab3e-9ecea0d27d90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a84654ef-aa6c-46e7-aff1-328bbc2c2231", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183941740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d3e942-a7af-4a0e-ab3e-9ecea0d27d90", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183941720300, "endTime": 34183941985300}, "additional": {"logType": "info", "children": [], "durationId": "cc4e217f-c2a3-4178-9c00-a5e4e1e18317", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "38dd2287-2035-4ac4-885d-24a9cb10373b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183942916200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5857a68a-34b4-4db7-88a7-6b46683292d0", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183949093900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879701d8-5411-4d5d-b634-46bce64bfc62", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183942002300, "endTime": 34183949911700}, "additional": {"logType": "info", "children": [], "durationId": "6c02fe71-0558-4516-b8f4-37d25f2156c8", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "b8e2e79c-c163-4bf0-b714-bc6a7502cd50", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183954258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd27d22-d6be-461d-8919-1668ac76656f", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183954377000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee65c262-59b3-4648-b21c-7e186df5ba2e", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183956182300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30db83c6-30e7-4f35-9d3e-660c9eca3a11", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183956341300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "911f21f7-6c50-46c0-9b81-fe8768c27a11", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183949933500, "endTime": 34183958610600}, "additional": {"logType": "info", "children": [], "durationId": "dcc22f11-5273-49a8-9662-8f3ef9b682a7", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "54c45ae7-7030-4ee0-a503-4f89927532bc", "name": "Configuration phase cost:223 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183958659800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6863ddb8-d504-47ea-9dba-1b62f0f67dab", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183958640600, "endTime": 34183958762300}, "additional": {"logType": "info", "children": [], "durationId": "a49c8cfe-d172-4ad8-ae0e-a36eb8a04d8f", "parent": "009980b2-5c02-40a8-861f-d1f9c4792523"}}, {"head": {"id": "009980b2-5c02-40a8-861f-d1f9c4792523", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183731508900, "endTime": 34183958772000}, "additional": {"logType": "info", "children": ["f210da11-221e-43d8-ae1e-add80b22d1fd", "fb0e4df5-591f-4c49-9a24-0169b3effc99", "4c9cc8f9-4fd7-42fb-bb46-ba5e0ff9cff2", "9fc9079c-ebfc-4851-9ff4-fb4b89eec302", "a54befee-77ac-4aad-bdc4-80d385002a6c", "879701d8-5411-4d5d-b634-46bce64bfc62", "911f21f7-6c50-46c0-9b81-fe8768c27a11", "6863ddb8-d504-47ea-9dba-1b62f0f67dab", "f1d3e942-a7af-4a0e-ab3e-9ecea0d27d90"], "durationId": "41d205d5-0035-40ac-a37c-2944e144c61a", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "c894bd99-d64e-4023-8ec6-11e393a9acd9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183959738000, "endTime": 34183959751000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ccc9d5a1-6657-4820-bd91-23cd22ca6329", "logId": "22628bbc-2709-4034-a878-2044b8516a1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22628bbc-2709-4034-a878-2044b8516a1f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183959738000, "endTime": 34183959751000}, "additional": {"logType": "info", "children": [], "durationId": "c894bd99-d64e-4023-8ec6-11e393a9acd9", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "fc9f109a-8819-46a8-95e1-27cd90a152bb", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183958789000, "endTime": 34183959759000}, "additional": {"logType": "info", "children": [], "durationId": "c8502a53-07e0-4c2a-bdab-7b67ce1e1dbe", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "d5b3ecc0-4287-485b-8507-bfb9d6ba6d85", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183959762800, "endTime": 34183959763600}, "additional": {"logType": "info", "children": [], "durationId": "ad06faa1-99d2-4a88-a9c0-c03c466c222a", "parent": "bea56aec-f2c5-424e-abea-74dc4c5f3772"}}, {"head": {"id": "bea56aec-f2c5-424e-abea-74dc4c5f3772", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183719689100, "endTime": 34183959765400}, "additional": {"logType": "info", "children": ["b155c33d-45fa-421c-aa9a-94f85cc51a7a", "009980b2-5c02-40a8-861f-d1f9c4792523", "fc9f109a-8819-46a8-95e1-27cd90a152bb", "d5b3ecc0-4287-485b-8507-bfb9d6ba6d85", "9fc318db-08fd-4edb-9bcd-ba443a96b53d", "9349b9ee-309b-458b-abec-0dbb0e1ae22e", "22628bbc-2709-4034-a878-2044b8516a1f"], "durationId": "ccc9d5a1-6657-4820-bd91-23cd22ca6329"}}, {"head": {"id": "acd9cbfe-1bed-4b4e-98e5-7308a83fab5a", "name": "Configuration task cost before running: 243 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183959899800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3966597-3677-419c-9a02-e8b2ecce0370", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183963784900, "endTime": 34183969575600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "6512519c-8d4d-4918-b344-2e4e09384b4f", "logId": "5154043e-ea10-4533-b5bb-267c91c6d50c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6512519c-8d4d-4918-b344-2e4e09384b4f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183961036900}, "additional": {"logType": "detail", "children": [], "durationId": "c3966597-3677-419c-9a02-e8b2ecce0370"}}, {"head": {"id": "37861f06-fd49-4686-a4d5-2f2fc689739f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183961571300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6904954f-5d57-4677-93f5-0bf16e30fb1d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183961693900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca532453-7a87-4e04-89ca-032ab61ca900", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183963794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fda6f688-83d7-45e4-a179-3c73f28e2b00", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183969257700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c56f4dda-635f-4b9c-9992-8ae4f47aab1e", "name": "entry : default@PreBuild cost memory 0.38330841064453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183969479100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5154043e-ea10-4533-b5bb-267c91c6d50c", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183963784900, "endTime": 34183969575600}, "additional": {"logType": "info", "children": [], "durationId": "c3966597-3677-419c-9a02-e8b2ecce0370"}}, {"head": {"id": "8ac1a805-9a36-44ab-8a8e-8e8938d4c9e6", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183974466700, "endTime": 34183976216100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b016b5e0-53df-4ae0-ae2b-85762eb14316", "logId": "33c678c2-454e-4124-b22b-73ebae588149"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b016b5e0-53df-4ae0-ae2b-85762eb14316", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183973209500}, "additional": {"logType": "detail", "children": [], "durationId": "8ac1a805-9a36-44ab-8a8e-8e8938d4c9e6"}}, {"head": {"id": "ee64ab43-976a-439f-85cd-e9268b3af04a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183973609400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ecc560-d868-4f00-95b1-771a3cc39971", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183973706900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff9fe6d-0cca-4fec-9206-6c7ff402239c", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183974476200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f06f51-df67-452e-ad7b-0d7439321f79", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183975238700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e05881bc-9515-4ccb-bfad-ab188cedd3c1", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183975987300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fc180bb-8131-4427-9ab8-4a62f6ac7628", "name": "entry : default@GenerateMetadata cost memory 0.090667724609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183976080700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c678c2-454e-4124-b22b-73ebae588149", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183974466700, "endTime": 34183976216100}, "additional": {"logType": "info", "children": [], "durationId": "8ac1a805-9a36-44ab-8a8e-8e8938d4c9e6"}}, {"head": {"id": "b083a403-06aa-4a92-9445-d2a4b14d3bd6", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978820300, "endTime": 34183979294900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2723dd75-fde2-44fb-8ceb-ac6a32c46d7c", "logId": "55a70f05-ce19-4d43-8d84-941475a42e76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2723dd75-fde2-44fb-8ceb-ac6a32c46d7c", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978257400}, "additional": {"logType": "detail", "children": [], "durationId": "b083a403-06aa-4a92-9445-d2a4b14d3bd6"}}, {"head": {"id": "4a9cac96-ed94-43a9-be14-b7e5fd655cf5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978592200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3b9bca4-e3a5-4b2f-8236-35b5ce76d1f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978689700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c6ff608-12d2-47d1-91f9-4f04c88824c1", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978826700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54766df2-dd89-459a-9293-0283bee3298c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978935100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f541c7f-e4ac-4248-aba0-dfa11ab6b472", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978987600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49654db9-a270-45ee-ae8d-0477db6f680a", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183979047100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba7ce75-b277-4434-957e-5c35c9dcf05c", "name": "runTaskFromQueue task cost before running: 263 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183979188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55a70f05-ce19-4d43-8d84-941475a42e76", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183978820300, "endTime": 34183979294900, "totalTime": 292100}, "additional": {"logType": "info", "children": [], "durationId": "b083a403-06aa-4a92-9445-d2a4b14d3bd6"}}, {"head": {"id": "7233b434-5745-4af7-bd8e-d6508bf028a5", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183982087000, "endTime": 34183983889800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7cf24e51-5dc0-4bc6-9a5d-a3908814b39e", "logId": "5e00ab19-f26f-4f68-a4a4-f5212ecc1ce6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cf24e51-5dc0-4bc6-9a5d-a3908814b39e", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183980966100}, "additional": {"logType": "detail", "children": [], "durationId": "7233b434-5745-4af7-bd8e-d6508bf028a5"}}, {"head": {"id": "1fe7c150-5795-4841-acb4-c00d233fe94c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183981308400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6747cd50-4e96-489f-a6bc-1bc3b74feaea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183981402900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd01f0fc-597d-42eb-81df-fdcbfeb8ef4c", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183982097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c38c4b-d68d-42ec-abb0-99869d922996", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183983628500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d66e3542-c5c6-4d48-b132-eacb956cb628", "name": "entry : default@MergeProfile cost memory 0.10509490966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183983818500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e00ab19-f26f-4f68-a4a4-f5212ecc1ce6", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183982087000, "endTime": 34183983889800}, "additional": {"logType": "info", "children": [], "durationId": "7233b434-5745-4af7-bd8e-d6508bf028a5"}}, {"head": {"id": "3e02b59d-7c3d-4b87-833b-0a9ffcff5525", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183988469600, "endTime": 34183991122500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d71c18a1-1ddd-4a50-b454-722e1abbf042", "logId": "c62c80d8-fbfc-4124-89b3-c4dfd14b7a5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d71c18a1-1ddd-4a50-b454-722e1abbf042", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183985578600}, "additional": {"logType": "detail", "children": [], "durationId": "3e02b59d-7c3d-4b87-833b-0a9ffcff5525"}}, {"head": {"id": "8fcd7b41-3868-4753-ab84-df6af3d04c79", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183987404200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566c5026-e383-4e07-80f5-c63a9750df0e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183987538700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a205bb1-b3a3-446a-9bd6-c9c3c552aeff", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183988482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33788db7-d23c-4a93-9ea7-270fe29da097", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183989445900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34bd3331-d74e-4459-b355-03024a1d457a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183990508800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f4e078d-5b3d-4d2d-ad45-9f2264f97952", "name": "entry : default@CreateBuildProfile cost memory 0.09976959228515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183990635100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62c80d8-fbfc-4124-89b3-c4dfd14b7a5d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183988469600, "endTime": 34183991122500}, "additional": {"logType": "info", "children": [], "durationId": "3e02b59d-7c3d-4b87-833b-0a9ffcff5525"}}, {"head": {"id": "92c9dd0d-1bff-4366-afa0-9ee3c2016aa6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994388800, "endTime": 34183994813200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b8ec1f96-d63c-44cc-88df-aafdcb1be1a6", "logId": "016c9f1a-84e5-40f8-a49e-48e27c1833b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8ec1f96-d63c-44cc-88df-aafdcb1be1a6", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183993018100}, "additional": {"logType": "detail", "children": [], "durationId": "92c9dd0d-1bff-4366-afa0-9ee3c2016aa6"}}, {"head": {"id": "bc247c29-6d37-4000-9de7-8e3088d9757c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183993461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f9590ca-9db2-451d-93a1-8d5fe75c63be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183993577400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a857de48-6483-408b-82a8-aa59499ecde5", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4127b2d-dc37-45c7-8320-e8511100e4fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994516700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d45345-bcbf-4b24-b4e9-966f2e60939e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994567900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c854c596-293f-4675-9412-efc7fac3ad94", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c443262-cb0b-4953-92a7-c4d04a505cee", "name": "runTaskFromQueue task cost before running: 278 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "016c9f1a-84e5-40f8-a49e-48e27c1833b1", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183994388800, "endTime": 34183994813200, "totalTime": 347200}, "additional": {"logType": "info", "children": [], "durationId": "92c9dd0d-1bff-4366-afa0-9ee3c2016aa6"}}, {"head": {"id": "3584b1d9-f915-4d81-bbea-77a954876804", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184000950800, "endTime": 34184001593900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2e9721eb-20fd-48c7-b170-461377345232", "logId": "65ba7d24-64fa-4b3a-aafe-d4ebd51dfefc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e9721eb-20fd-48c7-b170-461377345232", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183996859000}, "additional": {"logType": "detail", "children": [], "durationId": "3584b1d9-f915-4d81-bbea-77a954876804"}}, {"head": {"id": "66f546ce-5e69-4497-8361-d87a77e93fb0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183997234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1775d5bb-6289-4066-b7ef-bb0abba64fd3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183997326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38a873d8-9227-4dcf-8266-d84a462fa991", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184000961000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffbff988-6b0f-4e1c-8e4b-f3ab464adbd5", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184001244600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d6888d7-d11d-449e-a45b-975eed190d01", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03713226318359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184001458200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ee3914-2cf6-46c9-ae2f-d50f332bdf1f", "name": "runTaskFromQueue task cost before running: 285 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184001541100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ba7d24-64fa-4b3a-aafe-d4ebd51dfefc", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184000950800, "endTime": 34184001593900, "totalTime": 575400}, "additional": {"logType": "info", "children": [], "durationId": "3584b1d9-f915-4d81-bbea-77a954876804"}}, {"head": {"id": "1e1b5bda-1f58-445c-ab18-3c2142a58004", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184008196300, "endTime": 34184011277700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "1c10ea26-5a8f-4cf5-afd6-29d793c1afc5", "logId": "27a257f2-f788-428f-8592-7d77f3434a0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c10ea26-5a8f-4cf5-afd6-29d793c1afc5", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184005712300}, "additional": {"logType": "detail", "children": [], "durationId": "1e1b5bda-1f58-445c-ab18-3c2142a58004"}}, {"head": {"id": "72261294-2f43-4b17-a0a7-325c688d8010", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184006173200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f31680-bf03-4cc8-98cc-dd2b2cdec166", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184006305600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22255156-4fd9-4bab-baf4-d3f4c56ff2ed", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184008209300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd0485c9-2c33-4a79-8ae3-b9f50a45c9a7", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184010717000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95bfdd6e-ee3f-4440-ae8a-d8b8b19df0dc", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184010854400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99040c6d-978e-4789-856d-cb2ca6f8ecfb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184010939500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30b1592c-1463-4750-8319-ab46b5b938ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184010990800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7d4761-872e-4654-98cd-8639d06c6139", "name": "entry : default@ProcessIntegratedHsp cost memory 0.117645263671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184011064500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3e40566-5d96-4412-93d5-909074d773cc", "name": "runTaskFromQueue task cost before running: 295 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184011176800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a257f2-f788-428f-8592-7d77f3434a0a", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184008196300, "endTime": 34184011277700, "totalTime": 2965000}, "additional": {"logType": "info", "children": [], "durationId": "1e1b5bda-1f58-445c-ab18-3c2142a58004"}}, {"head": {"id": "9399d6a0-5f2c-47c4-97ca-2d314ccc9644", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014684600, "endTime": 34184015042500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0db2c969-a11e-47f0-b81d-cac5c4dbe4b2", "logId": "f5b3d25e-03bc-4119-8f46-f87b3340e869"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0db2c969-a11e-47f0-b81d-cac5c4dbe4b2", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184013465300}, "additional": {"logType": "detail", "children": [], "durationId": "9399d6a0-5f2c-47c4-97ca-2d314ccc9644"}}, {"head": {"id": "d0074674-7b8c-44a6-ba47-36086162f5b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184013809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2427d5f3-85f7-4485-9ddf-e308871235ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184013899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef831580-9b97-40fa-881c-1e24ec9cd01a", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f6d879-fa6d-49e7-bb97-e72e1f9b3255", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014811500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae18097c-0361-41dc-af41-bc5cb581cfcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014865300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c33d449-e3b4-4e1c-8acd-475daa20b611", "name": "entry : default@BuildNativeWithCmake cost memory 0.03716278076171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014930900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39b4471f-e659-4eda-962d-431f3020133e", "name": "runTaskFromQueue task cost before running: 298 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014995600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b3d25e-03bc-4119-8f46-f87b3340e869", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184014684600, "endTime": 34184015042500, "totalTime": 296200}, "additional": {"logType": "info", "children": [], "durationId": "9399d6a0-5f2c-47c4-97ca-2d314ccc9644"}}, {"head": {"id": "e5d643b1-23b0-44c6-b6d1-df82ada49cff", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184017666400, "endTime": 34184020847100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cba34c0e-f57a-418e-92e2-db6dcaa65935", "logId": "7d9202fa-2a71-484e-b7ad-da3b72c885ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cba34c0e-f57a-418e-92e2-db6dcaa65935", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184016562500}, "additional": {"logType": "detail", "children": [], "durationId": "e5d643b1-23b0-44c6-b6d1-df82ada49cff"}}, {"head": {"id": "f91972db-198d-4ee0-908d-f03972283d7b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184016878600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82fd55d-b853-4523-92f8-97d03eb43621", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184016962700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f1bc1b-1545-46cc-9fa1-a6c4c3371886", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184017673900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4143c59-8730-447d-8995-abd3eb9424e8", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184020668200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40778321-c3a7-4519-9a86-cf3688cfd1a5", "name": "entry : default@MakePackInfo cost memory 0.13796234130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184020785100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d9202fa-2a71-484e-b7ad-da3b72c885ac", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184017666400, "endTime": 34184020847100}, "additional": {"logType": "info", "children": [], "durationId": "e5d643b1-23b0-44c6-b6d1-df82ada49cff"}}, {"head": {"id": "e6b42381-d555-4c65-b518-20aaa74a28c6", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184024898200, "endTime": 34184027697500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "0800148f-3692-4d02-b8ca-e90a03f65859", "logId": "9d6d2d0a-206b-4802-b1d3-3d67e52abdb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0800148f-3692-4d02-b8ca-e90a03f65859", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184022974500}, "additional": {"logType": "detail", "children": [], "durationId": "e6b42381-d555-4c65-b518-20aaa74a28c6"}}, {"head": {"id": "bbe636a5-efdd-4668-a67f-cf4778e7853d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184023503900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86e601ae-eb0e-4995-9c99-9644b0a76fa8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184023618400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff918513-29f2-4710-86f6-49416477eb68", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184024909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd38dcbf-0754-4532-a878-9378ddc2c237", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184025061400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27fd24df-c69d-4883-a934-cf95ab406deb", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184025877000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "228c71a2-e0d6-4333-98b6-5d69d7bed8cf", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184027262700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5939ecfb-3b90-4227-92fe-75992a658d83", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184027376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef9e1fb-84d5-4f3a-b5bd-58adb2774a64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184027457800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819e4a52-9471-4c62-bdb7-08a4f4f0e642", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184027508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f392f586-a8a2-4f0f-a704-fa3320b91d47", "name": "entry : default@SyscapTransform cost memory 0.15026092529296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184027580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbcb5e4a-6adc-406f-817d-0dc0053db9c8", "name": "runTaskFromQueue task cost before running: 311 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184027649400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d6d2d0a-206b-4802-b1d3-3d67e52abdb8", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184024898200, "endTime": 34184027697500, "totalTime": 2738100}, "additional": {"logType": "info", "children": [], "durationId": "e6b42381-d555-4c65-b518-20aaa74a28c6"}}, {"head": {"id": "dcd6d7b5-a5d8-4862-9d2a-1cbfbf970281", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184030931600, "endTime": 34184032132500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "0dde1e21-6806-4544-ae21-f98b1e25f522", "logId": "a5f1d2b9-d763-4b60-8588-e68542c15330"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dde1e21-6806-4544-ae21-f98b1e25f522", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184029403100}, "additional": {"logType": "detail", "children": [], "durationId": "dcd6d7b5-a5d8-4862-9d2a-1cbfbf970281"}}, {"head": {"id": "fa5af240-e54a-46dc-a4a2-7cad3324e4bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184029783500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f99258-fa21-4781-aab4-e8ac404b90bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184029882500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "601825af-436f-4ba6-9ff7-36038af98fe9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184030940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "002126cd-3956-4c99-b5ab-547f1ac0b597", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184031954400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "447e917e-71f8-4246-96df-49c5d66565ab", "name": "entry : default@ProcessProfile cost memory 0.05956268310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184032065000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5f1d2b9-d763-4b60-8588-e68542c15330", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184030931600, "endTime": 34184032132500}, "additional": {"logType": "info", "children": [], "durationId": "dcd6d7b5-a5d8-4862-9d2a-1cbfbf970281"}}, {"head": {"id": "ed05de03-ca13-41fb-983c-008b243e60b1", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184035715900, "endTime": 34184041556500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f4c10aca-2d33-4a50-b380-7a4ec594f62b", "logId": "5b9ccd47-d1f6-456b-a7d6-09e793c5fb45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4c10aca-2d33-4a50-b380-7a4ec594f62b", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184033703300}, "additional": {"logType": "detail", "children": [], "durationId": "ed05de03-ca13-41fb-983c-008b243e60b1"}}, {"head": {"id": "60f52c9a-3329-40bf-9722-979e03a29a7b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184034018300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5561c778-1b81-4d7e-af75-b1edcbd17c40", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184034108700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb6df30a-fc02-4eb6-9554-7ecf3680177e", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184035726600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5758006c-a102-4acb-bcaf-af1521b5bb25", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184041370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf547282-a5f3-4bf5-b288-5d20a9ef81ca", "name": "entry : default@ProcessRouterMap cost memory 0.2012481689453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184041495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9ccd47-d1f6-456b-a7d6-09e793c5fb45", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184035715900, "endTime": 34184041556500}, "additional": {"logType": "info", "children": [], "durationId": "ed05de03-ca13-41fb-983c-008b243e60b1"}}, {"head": {"id": "6e551b98-8e19-4713-b1ad-713b0f24e97b", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184045075300, "endTime": 34184046115500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "66fa9478-64a2-45d3-82eb-18e7acdba23b", "logId": "d6497f45-48bf-4ca2-9686-c5f4725c13b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66fa9478-64a2-45d3-82eb-18e7acdba23b", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184043981300}, "additional": {"logType": "detail", "children": [], "durationId": "6e551b98-8e19-4713-b1ad-713b0f24e97b"}}, {"head": {"id": "f32b0c09-32b9-4c44-85e6-0ce1652bf914", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184044372600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c11db97-c242-4d6b-9a9a-05d7b1185fbf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184044467200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51e34e7-3b6a-4719-938e-61d199d00dd6", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184045082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b3c9846-6100-44af-a1a4-918a9519a2c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184045269100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cdbe994-a413-4a00-8a01-eb2cc510fd31", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184045341800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb54ab8-e463-4e7f-8225-e18899932057", "name": "entry : default@BuildNativeWithNinja cost memory 0.05660247802734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184045957900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef68348-2d8a-45b8-9c26-eb9314bfd305", "name": "runTaskFromQueue task cost before running: 329 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184046052900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6497f45-48bf-4ca2-9686-c5f4725c13b5", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184045075300, "endTime": 34184046115500, "totalTime": 959500}, "additional": {"logType": "info", "children": [], "durationId": "6e551b98-8e19-4713-b1ad-713b0f24e97b"}}, {"head": {"id": "ea268114-c3ad-4900-b8d5-28fff5d703ad", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184054854000, "endTime": 34184059576400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b6aa6c00-8483-4a79-8f6c-667b784f5099", "logId": "c5f6c1f0-4ff6-4f4f-97f2-20667eed7bb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6aa6c00-8483-4a79-8f6c-667b784f5099", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184048334800}, "additional": {"logType": "detail", "children": [], "durationId": "ea268114-c3ad-4900-b8d5-28fff5d703ad"}}, {"head": {"id": "d223dae2-0390-41ee-8292-cbfc1d5348dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184048692500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d736c31-b379-4835-bffb-6d2f57ff8374", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184048798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5eb0b021-2236-4e16-98f7-7aeab8d7cbf0", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184049626800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6afd6ee-dbcf-4232-bdd1-0519d870eb7f", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184056563200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2099afd9-77e9-4dac-b810-0e51c5dac277", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184058218700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "984a1f6f-c59b-438f-8f64-8297c1f4c6ae", "name": "entry : default@ProcessResource cost memory 0.168304443359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184058317000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f6c1f0-4ff6-4f4f-97f2-20667eed7bb2", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184054854000, "endTime": 34184059576400}, "additional": {"logType": "info", "children": [], "durationId": "ea268114-c3ad-4900-b8d5-28fff5d703ad"}}, {"head": {"id": "663677e5-6415-4380-bbc0-0aa2964664b0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184065403100, "endTime": 34184077399100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c6aeaa64-01a6-454f-999c-459762833208", "logId": "0d0ce5e1-833d-48b2-877f-715b34dd6018"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6aeaa64-01a6-454f-999c-459762833208", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184062560500}, "additional": {"logType": "detail", "children": [], "durationId": "663677e5-6415-4380-bbc0-0aa2964664b0"}}, {"head": {"id": "2c636548-d826-4877-922e-6356ce803f9b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184062879100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ceaa6b9-ef4f-4935-a2ed-c47993e2c1c2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184062962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a075601e-bb79-4315-9e15-b9a965155677", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184065410100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "223e9098-63ec-4e37-8a9e-11e4d6b751ad", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184077212900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e8d2ec-19b7-46f7-90d9-daa85fa21534", "name": "entry : default@GenerateLoaderJson cost memory 0.7611618041992188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184077334000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0ce5e1-833d-48b2-877f-715b34dd6018", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184065403100, "endTime": 34184077399100}, "additional": {"logType": "info", "children": [], "durationId": "663677e5-6415-4380-bbc0-0aa2964664b0"}}, {"head": {"id": "149934d3-ccce-48fd-a655-ee103a447564", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184083872000, "endTime": 34184089452200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e4227014-dc25-4dfe-ade1-5aad0ddf622e", "logId": "965bcd53-009f-49e0-a73b-2d972e7b9a68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4227014-dc25-4dfe-ade1-5aad0ddf622e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184082856400}, "additional": {"logType": "detail", "children": [], "durationId": "149934d3-ccce-48fd-a655-ee103a447564"}}, {"head": {"id": "3081e86f-ebad-458c-9232-0e8cdad8103c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184083198900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a67fef90-2d3b-4568-a827-bca633e80926", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184083296200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dfaae89-9663-48df-a7fe-52d9c316b125", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184083881700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f29657-20e0-4bc2-affc-7652a422babc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184086569500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06cc459e-ea1a-4074-8e7a-f72a0e4060f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184086828900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a11d1dc-7d43-445a-b6a4-d1bf9565c467", "name": "entry : default@ProcessLibs cost memory 0.129425048828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184089041700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32a4074-d9b3-4669-877e-d937079ffe79", "name": "runTaskFromQueue task cost before running: 373 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184089365600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "965bcd53-009f-49e0-a73b-2d972e7b9a68", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184083872000, "endTime": 34184089452200, "totalTime": 5455600}, "additional": {"logType": "info", "children": [], "durationId": "149934d3-ccce-48fd-a655-ee103a447564"}}, {"head": {"id": "11b3080c-2756-4c52-a2ca-cb43949bf659", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184095772200, "endTime": 34184117462400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2a55f5d1-e61d-4160-bf31-47b3691af0eb", "logId": "6e7ec412-ca42-46df-9989-86ee09281f66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a55f5d1-e61d-4160-bf31-47b3691af0eb", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184092139500}, "additional": {"logType": "detail", "children": [], "durationId": "11b3080c-2756-4c52-a2ca-cb43949bf659"}}, {"head": {"id": "7c36dccc-7ba9-43b5-821a-da576b05d563", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184092475200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f391fa89-a042-42e4-ae74-8f086802882b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184092570200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ffe7e7-3413-4c12-b29d-26992b4d6379", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184093429400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b79029-634f-49fb-98cc-82a5518337b5", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184095794600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64da447d-3c23-437f-b935-23f06a7ce055", "name": "Incremental task entry:default@CompileResource pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184117245000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6827c709-142e-45c7-a91c-20195fe165a0", "name": "entry : default@CompileResource cost memory 1.406219482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184117379800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e7ec412-ca42-46df-9989-86ee09281f66", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184095772200, "endTime": 34184117462400}, "additional": {"logType": "info", "children": [], "durationId": "11b3080c-2756-4c52-a2ca-cb43949bf659"}}, {"head": {"id": "77c8e0f4-1c45-4658-93a3-ebbfb40a156c", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184123197200, "endTime": 34184124332000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6adb6ec4-2996-4849-9737-be735af98690", "logId": "a263bc6e-acc3-4daf-adc4-ac100f3ed611"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6adb6ec4-2996-4849-9737-be735af98690", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184120308500}, "additional": {"logType": "detail", "children": [], "durationId": "77c8e0f4-1c45-4658-93a3-ebbfb40a156c"}}, {"head": {"id": "3715900b-4309-416c-a8f6-e4d3934714ec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184120673200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e44e2a2-809f-49ab-9e92-8d51de010e3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184120771400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534601d6-6d72-44e5-a85f-4234fd89cd47", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184123207600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "443b501d-8af4-4d03-8d6f-458e6ec7ff6b", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184123429800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72eb7c98-2c5c-40f8-941c-d62d67d3f64d", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184124089100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db4716f-d158-4591-ba67-f434c89a34e9", "name": "entry : default@DoNativeStrip cost memory 0.07403564453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184124217200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a263bc6e-acc3-4daf-adc4-ac100f3ed611", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184123197200, "endTime": 34184124332000}, "additional": {"logType": "info", "children": [], "durationId": "77c8e0f4-1c45-4658-93a3-ebbfb40a156c"}}, {"head": {"id": "bb79795a-cd0d-49a9-be6a-7ba7ab4f7101", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184129302800, "endTime": 34185928130500}, "additional": {"children": ["981f839e-37aa-483d-93f6-3d51ff5ea237", "bc9b07a6-5df2-4155-8701-ac709e6a5e13"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "eaa200ee-0036-4f07-b0da-85fa30f8f62b", "logId": "4102f6fb-2a8e-4174-9d7a-fe9130d8cc78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaa200ee-0036-4f07-b0da-85fa30f8f62b", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184125639800}, "additional": {"logType": "detail", "children": [], "durationId": "bb79795a-cd0d-49a9-be6a-7ba7ab4f7101"}}, {"head": {"id": "2f76350b-331e-4dae-a4d8-c0c1d91b0970", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184125962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56b4a31d-4266-412f-be6c-c34c3f0a71d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184126052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c997647-ebf8-41b3-8460-938c4b644320", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184129313900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea87d395-5552-4737-9c43-b62b4e5d8eb9", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184140749500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c02fef29-fce4-4b75-94a0-12a7a16ad7c4", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184140880100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad72363-0b79-49db-848c-1a16898145de", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184151910400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f9ef46c-daea-4c97-afc3-0becb2480b4c", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184152310600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0ab00d-e4f7-48e5-8ade-96967ac37c39", "name": "default@CompileArkTS work[121] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184153637700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "981f839e-37aa-483d-93f6-3d51ff5ea237", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34184257232000, "endTime": 34185922241900}, "additional": {"children": ["3faddf92-89eb-4a8f-be12-a7f0179c9ede", "c31dd771-dc09-499e-9d58-3dadf832a669", "cf0a7d27-619d-4bbe-ab75-baad3eb90ed4", "3deae5bf-0913-49ab-b90e-89b38784b5c4", "191f0783-19c7-48c7-a7f0-93431710e9ee", "5f5706eb-7ea2-4e92-83f5-24fbf838fa11"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bb79795a-cd0d-49a9-be6a-7ba7ab4f7101", "logId": "5090567f-bfe2-4eda-9022-52b439db931b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "481b8fd3-e826-4f23-a1a5-419a07f20a15", "name": "default@CompileArkTS work[121] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184154464500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa2a8f23-fad4-4242-a076-21791c9ce2cf", "name": "default@CompileArkTS work[121] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184154569700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09696051-befd-44e6-b953-7c3d29c865d5", "name": "CopyResources startTime: 34184154658300", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184154661900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96889b59-6324-4fae-be55-b325a18a0cfb", "name": "default@CompileArkTS work[122] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184154738600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9b07a6-5df2-4155-8701-ac709e6a5e13", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34185541409700, "endTime": 34185554900900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "bb79795a-cd0d-49a9-be6a-7ba7ab4f7101", "logId": "793f1fd0-73a4-4af4-919c-e1c29685c74d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "485d27f8-2303-465b-a799-4b2a4f1d7aa6", "name": "default@CompileArkTS work[122] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184155333600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8be308-5b33-46f3-8984-a54a70359699", "name": "default@CompileArkTS work[122] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184155424400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6709f1c1-f442-416b-8b5d-7bcf8cb72c01", "name": "entry : default@CompileArkTS cost memory 1.5852279663085938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184155508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e96ec9f4-93a9-4553-ad52-078b66f37a2d", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184161109300, "endTime": 34184164483800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "9378e799-0b5a-4846-8bf3-9470798542cd", "logId": "4ff28c49-aa5b-400e-9f56-1ba9a8af2961"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9378e799-0b5a-4846-8bf3-9470798542cd", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184157029200}, "additional": {"logType": "detail", "children": [], "durationId": "e96ec9f4-93a9-4553-ad52-078b66f37a2d"}}, {"head": {"id": "318ae006-55b9-4c82-aa0f-a390153b07f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184157363700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "969b1bb5-c108-4c4a-8d28-a61423df9ac5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184157460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de01347-3bd1-4a0f-98b3-d616115ec611", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184161121500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ef631e-0dde-4a20-820f-c1f2adc7e007", "name": "entry : default@BuildJS cost memory 0.1265411376953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184164188700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5a78afe-df55-4e9d-acad-94725e8c7d39", "name": "runTaskFromQueue task cost before running: 448 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184164403800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff28c49-aa5b-400e-9f56-1ba9a8af2961", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184161109300, "endTime": 34184164483800, "totalTime": 3272400}, "additional": {"logType": "info", "children": [], "durationId": "e96ec9f4-93a9-4553-ad52-078b66f37a2d"}}, {"head": {"id": "8a6b5e56-51a3-4942-a79c-fc4bd56121f6", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184169286500, "endTime": 34184171059800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5d27367e-2b1f-41b7-9f37-03169439e873", "logId": "05982a6f-7860-4f17-b5c7-c65dec4a6cf3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d27367e-2b1f-41b7-9f37-03169439e873", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184166207700}, "additional": {"logType": "detail", "children": [], "durationId": "8a6b5e56-51a3-4942-a79c-fc4bd56121f6"}}, {"head": {"id": "f4835e13-f944-4fcc-a3b9-cddc992fccc4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184166533000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1356c41-c202-4b14-95aa-7004709f2ccd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184166686100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b3e4f8-ebb3-45a5-a79c-969b55cc8fda", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184169299200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d61ecff-87a9-4cef-b28f-259ad0bc3c01", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184169750300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff67e1d7-2bec-4c3e-8ff0-943d8da7bbb6", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184170888000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52a1bcdc-a3a9-4c05-98d2-ac873fbdc65e", "name": "entry : default@CacheNativeLibs cost memory 0.08740997314453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184170994600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05982a6f-7860-4f17-b5c7-c65dec4a6cf3", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184169286500, "endTime": 34184171059800}, "additional": {"logType": "info", "children": [], "durationId": "8a6b5e56-51a3-4942-a79c-fc4bd56121f6"}}, {"head": {"id": "ef6851b1-7de0-46fa-927f-4cf64d668c49", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184256649100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9600ec02-9a3d-48dd-8064-20a052b950f6", "name": "default@CompileArkTS work[121] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184257068900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf316d00-8cd2-49d0-9cb4-8a1ec600c32e", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184257276300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8414ab7-d420-4a3c-a2a5-b042bf1d996b", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184257467800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b7b52bc-b8aa-42ad-85ac-dd9619cc37e4", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184257554100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1777770c-6a97-42e7-ad4e-5870e7d815bd", "name": "default@CompileArkTS work[122] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184259250000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83bcd6c8-3dff-4e5e-911c-14405a7d1a8b", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185555066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2695059f-3f3b-4184-8cee-d240e1af165f", "name": "CopyResources is end, endTime: 34185555410700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185555417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee415df-83a5-46b5-9ae7-a84ac842ad68", "name": "default@CompileArkTS work[122] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185555516800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "793f1fd0-73a4-4af4-919c-e1c29685c74d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 34185541409700, "endTime": 34185554900900}, "additional": {"logType": "info", "children": [], "durationId": "bc9b07a6-5df2-4155-8701-ac709e6a5e13", "parent": "4102f6fb-2a8e-4174-9d7a-fe9130d8cc78"}}, {"head": {"id": "efe135b3-b1e1-4128-991e-aeedd075cab3", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185555654300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620f54a6-f8bb-4ed9-9e2b-fa753f4d05a5", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185922830100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3faddf92-89eb-4a8f-be12-a7f0179c9ede", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34184257449500, "endTime": 34184262617900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "981f839e-37aa-483d-93f6-3d51ff5ea237", "logId": "0caf7eb9-5df3-40ec-9118-529ca6f00a00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0caf7eb9-5df3-40ec-9118-529ca6f00a00", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184257449500, "endTime": 34184262617900}, "additional": {"logType": "info", "children": [], "durationId": "3faddf92-89eb-4a8f-be12-a7f0179c9ede", "parent": "5090567f-bfe2-4eda-9022-52b439db931b"}}, {"head": {"id": "c31dd771-dc09-499e-9d58-3dadf832a669", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34184262635200, "endTime": 34184262738700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "981f839e-37aa-483d-93f6-3d51ff5ea237", "logId": "6cd3ab10-ddda-4a19-ae7b-aa6078865949"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6cd3ab10-ddda-4a19-ae7b-aa6078865949", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184262635200, "endTime": 34184262738700}, "additional": {"logType": "info", "children": [], "durationId": "c31dd771-dc09-499e-9d58-3dadf832a669", "parent": "5090567f-bfe2-4eda-9022-52b439db931b"}}, {"head": {"id": "cf0a7d27-619d-4bbe-ab75-baad3eb90ed4", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34184262751600, "endTime": 34184262788900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "981f839e-37aa-483d-93f6-3d51ff5ea237", "logId": "ec1aca29-6821-4128-bb21-bb29a6717bb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec1aca29-6821-4128-bb21-bb29a6717bb2", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184262751600, "endTime": 34184262788900}, "additional": {"logType": "info", "children": [], "durationId": "cf0a7d27-619d-4bbe-ab75-baad3eb90ed4", "parent": "5090567f-bfe2-4eda-9022-52b439db931b"}}, {"head": {"id": "3deae5bf-0913-49ab-b90e-89b38784b5c4", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34184262806600, "endTime": 34185855524100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "981f839e-37aa-483d-93f6-3d51ff5ea237", "logId": "25670a8c-efe5-4dee-af8b-cf9cd2d8823f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25670a8c-efe5-4dee-af8b-cf9cd2d8823f", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184262806600, "endTime": 34185855524100}, "additional": {"logType": "info", "children": [], "durationId": "3deae5bf-0913-49ab-b90e-89b38784b5c4", "parent": "5090567f-bfe2-4eda-9022-52b439db931b"}}, {"head": {"id": "191f0783-19c7-48c7-a7f0-93431710e9ee", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34185855544000, "endTime": 34185859404300}, "additional": {"children": ["13fc3df7-7184-42fe-9073-6700cf6bb686", "67f6b8bd-02aa-4e40-b3ca-73f8801bc4a5", "1d9c63c3-1c54-47b6-9418-4ef88d5f1aad"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "981f839e-37aa-483d-93f6-3d51ff5ea237", "logId": "f91f54c2-cad5-4778-a160-30c1df111a5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f91f54c2-cad5-4778-a160-30c1df111a5b", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185855544000, "endTime": 34185859404300}, "additional": {"logType": "info", "children": ["a8d4d70f-e4a2-40aa-b0fa-d3863a893608", "9a4c085b-90c7-4c25-9ac7-6ca0dae3dca2", "ae1270a4-cf4d-4599-b45a-9d11edeaa844"], "durationId": "191f0783-19c7-48c7-a7f0-93431710e9ee", "parent": "5090567f-bfe2-4eda-9022-52b439db931b"}}, {"head": {"id": "13fc3df7-7184-42fe-9073-6700cf6bb686", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34185855556900, "endTime": 34185855562100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "191f0783-19c7-48c7-a7f0-93431710e9ee", "logId": "a8d4d70f-e4a2-40aa-b0fa-d3863a893608"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8d4d70f-e4a2-40aa-b0fa-d3863a893608", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185855556900, "endTime": 34185855562100}, "additional": {"logType": "info", "children": [], "durationId": "13fc3df7-7184-42fe-9073-6700cf6bb686", "parent": "f91f54c2-cad5-4778-a160-30c1df111a5b"}}, {"head": {"id": "67f6b8bd-02aa-4e40-b3ca-73f8801bc4a5", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34185855565100, "endTime": 34185856929200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "191f0783-19c7-48c7-a7f0-93431710e9ee", "logId": "9a4c085b-90c7-4c25-9ac7-6ca0dae3dca2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a4c085b-90c7-4c25-9ac7-6ca0dae3dca2", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185855565100, "endTime": 34185856929200}, "additional": {"logType": "info", "children": [], "durationId": "67f6b8bd-02aa-4e40-b3ca-73f8801bc4a5", "parent": "f91f54c2-cad5-4778-a160-30c1df111a5b"}}, {"head": {"id": "1d9c63c3-1c54-47b6-9418-4ef88d5f1aad", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34185856933100, "endTime": 34185859392300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "191f0783-19c7-48c7-a7f0-93431710e9ee", "logId": "ae1270a4-cf4d-4599-b45a-9d11edeaa844"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae1270a4-cf4d-4599-b45a-9d11edeaa844", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185856933100, "endTime": 34185859392300}, "additional": {"logType": "info", "children": [], "durationId": "1d9c63c3-1c54-47b6-9418-4ef88d5f1aad", "parent": "f91f54c2-cad5-4778-a160-30c1df111a5b"}}, {"head": {"id": "5f5706eb-7ea2-4e92-83f5-24fbf838fa11", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34185859418700, "endTime": 34185922088200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "981f839e-37aa-483d-93f6-3d51ff5ea237", "logId": "abee6b52-a7b1-494c-9bb0-c97648680a4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abee6b52-a7b1-494c-9bb0-c97648680a4e", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185859418700, "endTime": 34185922088200}, "additional": {"logType": "info", "children": [], "durationId": "5f5706eb-7ea2-4e92-83f5-24fbf838fa11", "parent": "5090567f-bfe2-4eda-9022-52b439db931b"}}, {"head": {"id": "917cf3bb-f4d4-4bf6-b196-4308b52d80ee", "name": "default@CompileArkTS work[121] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185927971800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5090567f-bfe2-4eda-9022-52b439db931b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34184257232000, "endTime": 34185922241900}, "additional": {"logType": "info", "children": ["0caf7eb9-5df3-40ec-9118-529ca6f00a00", "6cd3ab10-ddda-4a19-ae7b-aa6078865949", "ec1aca29-6821-4128-bb21-bb29a6717bb2", "25670a8c-efe5-4dee-af8b-cf9cd2d8823f", "f91f54c2-cad5-4778-a160-30c1df111a5b", "abee6b52-a7b1-494c-9bb0-c97648680a4e"], "durationId": "981f839e-37aa-483d-93f6-3d51ff5ea237", "parent": "4102f6fb-2a8e-4174-9d7a-fe9130d8cc78"}}, {"head": {"id": "4102f6fb-2a8e-4174-9d7a-fe9130d8cc78", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34184129302800, "endTime": 34185928130500, "totalTime": 1691270800}, "additional": {"logType": "info", "children": ["5090567f-bfe2-4eda-9022-52b439db931b", "793f1fd0-73a4-4af4-919c-e1c29685c74d"], "durationId": "bb79795a-cd0d-49a9-be6a-7ba7ab4f7101"}}, {"head": {"id": "de2a6936-2745-4206-9b7f-162c9e41ebcc", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185932536100, "endTime": 34185933573700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "5bdf4a90-1f4d-4954-ad2d-bbccdcff7de6", "logId": "22bd3b0e-0bc8-40c6-9a69-dbcaf2c4fc26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bdf4a90-1f4d-4954-ad2d-bbccdcff7de6", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185931305700}, "additional": {"logType": "detail", "children": [], "durationId": "de2a6936-2745-4206-9b7f-162c9e41ebcc"}}, {"head": {"id": "e4bc307b-b21a-4d6e-9fa1-d90e5b99c2d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185931597900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc33dcc-bb19-4dbe-9359-18c363fde20e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185931702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cb2cb9a-b997-443b-95dc-03a834394f40", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185932543600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f302b27e-6ac9-4cb8-8941-60883fb0adf0", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185932782400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e45e10d-5780-451c-855b-2c8b6f767f92", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185933441600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc9e66af-320f-4186-8a54-47752feb2ae0", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0706329345703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185933516400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22bd3b0e-0bc8-40c6-9a69-dbcaf2c4fc26", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185932536100, "endTime": 34185933573700}, "additional": {"logType": "info", "children": [], "durationId": "de2a6936-2745-4206-9b7f-162c9e41ebcc"}}, {"head": {"id": "72532bf8-1174-4b97-bfe4-75c4142611cd", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185942378200, "endTime": 34186468760500}, "additional": {"children": ["81042590-0f99-4288-9377-a36e272180f5", "c13d269b-89dd-42a9-a6b0-f15089645a01", "c56ee52c-d661-4e1f-95d9-60e5ffe34f95"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "aaca15e2-cbbe-4aa2-930c-8470561fd6ba", "logId": "14181f66-8166-40bf-bb4f-a4ec39c49d8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaca15e2-cbbe-4aa2-930c-8470561fd6ba", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185935215600}, "additional": {"logType": "detail", "children": [], "durationId": "72532bf8-1174-4b97-bfe4-75c4142611cd"}}, {"head": {"id": "35da087d-6077-48f1-b4e2-dafad6f38a81", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185935588700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1549b3ee-4bc8-44f3-a490-3f04806d2464", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185935745200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8313a2fd-37eb-40d9-a5c0-385d7292826d", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185942395500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "815c61a4-e66c-4528-b7a4-75cc1b01f9a2", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185955203400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5efe88cf-f4ba-4e18-a57e-e002176caf95", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185955333500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "455e0e4f-b7ac-433e-995a-8bf8bf449bfc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185955417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41451aea-fa41-4035-9e47-6881158fb1bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185955466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81042590-0f99-4288-9377-a36e272180f5", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185956199200, "endTime": 34185957462200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72532bf8-1174-4b97-bfe4-75c4142611cd", "logId": "8363174a-a172-41e4-8e09-5521177b705a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfdfbd1b-68b1-4bc3-8cf0-483de3423b72", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185957334800}, "additional": {"logType": "debug", "children": [], "durationId": "72532bf8-1174-4b97-bfe4-75c4142611cd"}}, {"head": {"id": "8363174a-a172-41e4-8e09-5521177b705a", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185956199200, "endTime": 34185957462200}, "additional": {"logType": "info", "children": [], "durationId": "81042590-0f99-4288-9377-a36e272180f5", "parent": "14181f66-8166-40bf-bb4f-a4ec39c49d8b"}}, {"head": {"id": "c13d269b-89dd-42a9-a6b0-f15089645a01", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185958085100, "endTime": 34185959388700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72532bf8-1174-4b97-bfe4-75c4142611cd", "logId": "ca0e8c28-45af-4ce6-8bf9-fb904b82b801"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efef9fb8-0b6a-4052-8928-cbfc6d9693bd", "name": "default@PackageHap work[123] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185958612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c56ee52c-d661-4e1f-95d9-60e5ffe34f95", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34186038680800, "endTime": 34186468445400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "72532bf8-1174-4b97-bfe4-75c4142611cd", "logId": "2863ae6d-8ea5-4fd9-95eb-3a2b476800de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39d2a233-fe3d-4854-b60a-f27c840911e5", "name": "default@PackageHap work[123] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185959250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c55945c-a4c1-442f-ad7b-cad949fcda33", "name": "default@PackageHap work[123] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185959334100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0e8c28-45af-4ce6-8bf9-fb904b82b801", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185958085100, "endTime": 34185959388700}, "additional": {"logType": "info", "children": [], "durationId": "c13d269b-89dd-42a9-a6b0-f15089645a01", "parent": "14181f66-8166-40bf-bb4f-a4ec39c49d8b"}}, {"head": {"id": "30ded73d-2395-47a6-a17e-beedacd9160a", "name": "entry : default@PackageHap cost memory 1.3019790649414062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185964086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d180e3-a1bc-4f3e-ab72-c23c5e145bfc", "name": "default@PackageHap work[123] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186038561700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3af2c04-e246-4217-97a9-8ae09764680b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186069719100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042737f8-20b6-4551-b82f-3c31a0f042ec", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186069905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e106f9e0-0ba9-4618-9876-0912b76e529e", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186070006200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1890c8c-7a64-4010-96ac-02a091167a35", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186070060100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66fc5d1-0a5f-4c77-bc93-b437e4ffcbe0", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186070215500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc9ddf5-e633-4cd1-b6bb-77b6c29c2b9a", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186070281100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d4d5ef-f4c9-4b2c-9d7d-b475264c6589", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186468526300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fb1a26-c319-478c-8775-24069d4172d6", "name": "default@PackageHap work[123] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186468677000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2863ae6d-8ea5-4fd9-95eb-3a2b476800de", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34186038680800, "endTime": 34186468445400}, "additional": {"logType": "info", "children": [], "durationId": "c56ee52c-d661-4e1f-95d9-60e5ffe34f95", "parent": "14181f66-8166-40bf-bb4f-a4ec39c49d8b"}}, {"head": {"id": "14181f66-8166-40bf-bb4f-a4ec39c49d8b", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34185942378200, "endTime": 34186468760500, "totalTime": 451835600}, "additional": {"logType": "info", "children": ["8363174a-a172-41e4-8e09-5521177b705a", "ca0e8c28-45af-4ce6-8bf9-fb904b82b801", "2863ae6d-8ea5-4fd9-95eb-3a2b476800de"], "durationId": "72532bf8-1174-4b97-bfe4-75c4142611cd"}}, {"head": {"id": "e7223ad0-f311-4f38-bff0-48c000433a8e", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186475722900, "endTime": 34186477224300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "35f896e1-98bf-4611-b293-c329a58ea6db", "logId": "9bcb5eb1-fd98-4d7e-858c-bf53f277eb6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35f896e1-98bf-4611-b293-c329a58ea6db", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186473137400}, "additional": {"logType": "detail", "children": [], "durationId": "e7223ad0-f311-4f38-bff0-48c000433a8e"}}, {"head": {"id": "43641cc6-96a5-46d2-b21a-634e294a2226", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186473552600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c1a4b4-b311-4c87-9dab-dfcfdddcbde5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186473669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e232e5fd-2532-4cdd-87ef-fb39d84f5c74", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186475733100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d0e7cb7-80c4-4af7-a324-334b503cf2a1", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186476041400}, "additional": {"logType": "warn", "children": [], "durationId": "e7223ad0-f311-4f38-bff0-48c000433a8e"}}, {"head": {"id": "b35746d8-91d5-4656-a651-1b13d2191ef2", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186476550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f61fe9c0-1219-4405-b51a-3b07bfdbfaa0", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186476695900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7df3ec4-665e-4ce0-bd26-9b953022f47f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186476828100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eebbed47-46ab-4c9b-81ad-ae49bac3a879", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186476895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2698228-4380-4d44-b473-4a3c810f8a7e", "name": "entry : default@SignHap cost memory 0.114410400390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186477082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e3fc2b-b4be-46bf-9780-21df94b38c39", "name": "runTaskFromQueue task cost before running: 2 s 761 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186477167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bcb5eb1-fd98-4d7e-858c-bf53f277eb6b", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186475722900, "endTime": 34186477224300, "totalTime": 1427600}, "additional": {"logType": "info", "children": [], "durationId": "e7223ad0-f311-4f38-bff0-48c000433a8e"}}, {"head": {"id": "9224c077-9c70-4521-8d29-f4ed03cec3f3", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186479674300, "endTime": 34186484189000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "39721841-71a9-4cf0-8cef-2078807241df", "logId": "387de507-0745-4ee0-aded-7578aa1fb072"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39721841-71a9-4cf0-8cef-2078807241df", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186478698800}, "additional": {"logType": "detail", "children": [], "durationId": "9224c077-9c70-4521-8d29-f4ed03cec3f3"}}, {"head": {"id": "e044ad14-dc16-426b-9143-64bbbd5c0039", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186478990700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feaf6cc9-eb27-4320-9caf-7d226835c048", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186479068300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb572d26-3189-48a7-bad7-8267bdd8e7e0", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186479681300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ed9b362-2c31-4d32-b410-a9bc54bd742c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186483874200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d4367db-267c-48b6-9c59-f3ff87cc6bbc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186483953200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bbab02e-873a-495f-ac61-a4f9f72a8053", "name": "entry : default@CollectDebugSymbol cost memory 0.23819732666015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186484017000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d6293d3-0ba1-43ff-80e2-70328089c9c0", "name": "runTaskFromQueue task cost before running: 2 s 768 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186484090400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "387de507-0745-4ee0-aded-7578aa1fb072", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186479674300, "endTime": 34186484189000, "totalTime": 4395600}, "additional": {"logType": "info", "children": [], "durationId": "9224c077-9c70-4521-8d29-f4ed03cec3f3"}}, {"head": {"id": "01988687-d1b3-4b0f-bc3f-d3bc01af9388", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186485533400, "endTime": 34186486038900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d2a42903-6b00-4f5e-aa12-f261b5b321e8", "logId": "ef1ef4cb-e8a7-49cd-af55-c836d659b8f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2a42903-6b00-4f5e-aa12-f261b5b321e8", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186485506200}, "additional": {"logType": "detail", "children": [], "durationId": "01988687-d1b3-4b0f-bc3f-d3bc01af9388"}}, {"head": {"id": "fc468f80-5394-415d-a374-c3d48c6932eb", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186485539000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c6dcfaf-5c8e-4a38-b879-6ed8f2fd170a", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186485741700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6740c937-2493-4873-a599-a44bbb8af24b", "name": "runTaskFromQueue task cost before running: 2 s 769 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186485907800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef1ef4cb-e8a7-49cd-af55-c836d659b8f6", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186485533400, "endTime": 34186486038900, "totalTime": 330200}, "additional": {"logType": "info", "children": [], "durationId": "01988687-d1b3-4b0f-bc3f-d3bc01af9388"}}, {"head": {"id": "0fc18322-d5e6-4d35-adc3-6bc1e04c07f7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186493922600, "endTime": 34186493940900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "eb0eb567-7545-4517-bbfe-a80929d7fe70", "logId": "aea69ea4-3465-4f9d-a6f5-02ddc3635433"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aea69ea4-3465-4f9d-a6f5-02ddc3635433", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186493922600, "endTime": 34186493940900}, "additional": {"logType": "info", "children": [], "durationId": "0fc18322-d5e6-4d35-adc3-6bc1e04c07f7"}}, {"head": {"id": "8a9629b6-b480-4028-848d-0acda59616c0", "name": "BUILD SUCCESSFUL in 2 s 777 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186493977200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "bd6a4eea-883c-4bdb-bc3d-4bcfb4bf9cab", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34183717047300, "endTime": 34186494216800}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 59}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "eb7b5986-0434-4870-a4e9-6f07c2f32aa5", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186494236900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b34fa5d3-3011-490b-a9cd-927df6770cf0", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186494312300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c81e9db-8ead-4bd6-9033-9ce2db482e7b", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186494361800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5785f1ea-2272-4f83-8ec7-6b3a4dc7123b", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186494409500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e923c2-208b-41cc-b420-44eaaed05521", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186494465400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d18fa94-fde9-4c9f-b1e8-9c2c174e9e71", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186494727400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50310b2a-0fe0-414a-b995-4d3c29e273a9", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186495273800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996f50b3-c7e6-44db-a157-f620fcf3c4fc", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186495516300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac8f479c-0ba7-4752-bedc-a75ce5c800be", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186495585900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4ea5172-ccd4-4d9d-b4cc-3f583503377d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186495640000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf542200-3145-4263-8130-a3888a295524", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186495873700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b72a89c8-d1a7-4d27-aaed-1d7a60b21ebe", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186496932300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c277033-f356-40f0-865a-4f7ef1f33df7", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497222300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5627cc9-c610-46de-9bcd-9a1bf8759c25", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f4b7918-65c8-426e-9a21-b886d003e425", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497354100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e83a608f-8292-46a3-a54c-0c143c0e329c", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497402200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9eb2ce4-6cdf-47d0-a095-c39bb8d18f8e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b07dcc-6a79-4478-9ca9-3bc2aa2b7694", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497764000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce783b91-0389-4634-a331-c4bbf06839be", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186497985600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d85d534-533b-41e7-b6d9-2736e22e72c5", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186498208900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7eb9fd-675d-41d0-bff9-ae090307ebdf", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186498448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ee434c-5295-433e-a176-5983ba1c008d", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186498511700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c5083c-6744-4ca8-8a7c-4636a02a08d6", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186498562600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37cb2827-8b7b-4d41-a0c9-bb8523e04459", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186500301600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e265b3-2df4-4b80-a1d1-38740fe40837", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186500743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21eb16a7-67d3-4b8f-9953-d0b418a9af93", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186501428800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "859b9099-f224-4de8-8aa2-38617d12a75d", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186501601300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435cd82a-9d1f-4252-a5dc-8d6fcf80f917", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186501851300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5c08d7-90c0-4057-a4b6-f3a1a7e9003b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186502670100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f47bbabb-d1ed-4840-a821-fd0e38500096", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186502781200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51cd6c1-9d40-4567-8944-d3e510e76473", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186503009700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b341335-032a-4893-acd8-9f637fe4304e", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186503857800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090f2dab-cac8-47e1-ae84-9bc04ef11659", "name": "Incremental task entry:default@CompileArkTS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186504702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c53acd5-4bf4-44d2-84bb-205888efc1de", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186505921200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29997d6a-6935-4840-af8c-e9b329fa46aa", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186506367800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec0450f0-03f5-4ab0-a004-ebc5c7a76815", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186507143400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcfe4301-ed57-489a-b78b-9e846c590460", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186507342000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c60574e9-95ec-4063-b482-d9772e6b3d11", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186507510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32dfd038-616f-43c9-b51f-86734700cf78", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186508051300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303f7fc4-eeac-401d-847d-6da8ba8f2236", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186508263400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca847881-4a4c-4180-bd03-797a8fcac7e7", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186508323900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bf9cf28-bba8-4dcd-8530-5bbc96c59542", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186508381800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f581611-ee12-4beb-8d4d-3ed0a75ab69c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186509060100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3490f24c-ed99-4c2d-a758-5ba0dc21386c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186509325200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c499c9-4dfc-4b99-8c3e-2fa643b4acad", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186509519300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1fde796-a70f-45fa-a3c1-4562de8fb9f8", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186515103400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "810384e5-c96a-405e-bfe4-7537ced0fc92", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186515348100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8344ae76-ef80-425a-9006-bc8f60620efb", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186515522500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d25671a-0c7f-4a81-82f7-567a9e785e36", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186515580800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b073f369-faa1-412a-9d12-db3b4e60af20", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186515798300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b4ea75b-45b2-4f40-8313-9ccdfffe8b4e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186516467600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17631a9-ac1b-4f75-873a-c8d0a5fe6d9e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186516698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50afe702-12e7-45f6-b588-f64d39bd741e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186516906800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fffeb52-011a-4654-a87d-8e21c65fd3b0", "name": "Incremental task entry:default@PackageHap post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186517170500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed96098-c68f-45fc-8ef6-316381df0d11", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186517347900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b686d2ca-c5fc-4558-a701-2d803b28a712", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186517426900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb7c7ae3-950c-42fb-a385-870d1fdae37c", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186517668000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8a09b57-6fe9-40eb-b019-4e19a3ad30f8", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186520562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7e080d-27a6-4f0b-8a09-26b5e9d7137c", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186520862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99817826-45bc-45c6-9bd9-5c4d2aa14ea4", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186521090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6291bfcf-092e-43c3-82d7-27308b482baf", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34186521302500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}