{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "98789deb-3a57-4ac2-b288-0cf9a261437c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615224312600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4f3fd7-5cd7-4b52-b226-469c9f90c358", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615228925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8330fc1d-d51c-43fc-ae2d-c87189b04618", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615229295000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88539c93-47e1-4b97-b3e5-86e5ae06e3da", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615235748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ff4f91-5b9d-47a3-aa77-e69f09c53849", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678911318100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678918808600, "endTime": 26679401647400}, "additional": {"children": ["84ff644b-a708-410f-8c2f-8bde86b3d81e", "27e7041f-58f7-403e-94bd-aa513a6bfb45", "4f7d8327-f431-43a2-b25f-5b6c8481abd7", "9743feed-0faf-4251-a3d0-4259d06cbf34", "2c6c7655-5c34-4ef4-8fa7-f8ea865c8df7", "2f0661dc-e81f-4a97-9f97-6c029c1c26f9", "0f82a727-13d0-4480-aff2-03826defa26f"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84ff644b-a708-410f-8c2f-8bde86b3d81e", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678918809700, "endTime": 26678936394900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "93621ad5-b746-419c-bba7-58a27f31cb3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678936415700, "endTime": 26679400220100}, "additional": {"children": ["386e620d-d5ef-4703-96aa-25b82e0b8c72", "e86926aa-d52b-42b4-850e-4420f6da42ef", "9bd9ba7e-8b43-442e-a57c-3894b09df482", "85aec40f-51f4-491b-888d-b21a92f0d06f", "8b2a66bb-f3d0-4d83-beb3-f491e8628a15", "71ed7f1e-5374-4e54-bcf7-3b2293af29ad", "3f681558-43c3-42ef-bf1f-a408143eaf77", "3e04e076-e4f3-46c6-840a-37d172c6a6c0", "e6f533b3-a0f5-4fa9-bc8d-80f43cdce064"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f7d8327-f431-43a2-b25f-5b6c8481abd7", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679400252700, "endTime": 26679401602000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "36968588-4b1e-407e-9a23-0094f5141e62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9743feed-0faf-4251-a3d0-4259d06cbf34", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679401609900, "endTime": 26679401639800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "816c4509-2c26-43bf-94de-2f30ca481a2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c6c7655-5c34-4ef4-8fa7-f8ea865c8df7", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678924584900, "endTime": 26678924648200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "d7eb3196-593e-429d-9a95-745c0c89cd17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7eb3196-593e-429d-9a95-745c0c89cd17", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678924584900, "endTime": 26678924648200}, "additional": {"logType": "info", "children": [], "durationId": "2c6c7655-5c34-4ef4-8fa7-f8ea865c8df7", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "2f0661dc-e81f-4a97-9f97-6c029c1c26f9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678930629000, "endTime": 26678930651900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "b54de823-c430-448f-953b-719bfab37faa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b54de823-c430-448f-953b-719bfab37faa", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678930629000, "endTime": 26678930651900}, "additional": {"logType": "info", "children": [], "durationId": "2f0661dc-e81f-4a97-9f97-6c029c1c26f9", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "b9c9df30-97ba-42f8-9411-f3ab621c2035", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678930708900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7075544-2080-4b74-8070-78d4c1ebec10", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678936254600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93621ad5-b746-419c-bba7-58a27f31cb3e", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678918809700, "endTime": 26678936394900}, "additional": {"logType": "info", "children": [], "durationId": "84ff644b-a708-410f-8c2f-8bde86b3d81e", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "386e620d-d5ef-4703-96aa-25b82e0b8c72", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678942093300, "endTime": 26678942104000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "3b10ad62-8392-47a8-a1f7-780addddd12b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e86926aa-d52b-42b4-850e-4420f6da42ef", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678942125400, "endTime": 26678949015300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "0790a026-1449-4f87-8fb1-de13244f3c9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bd9ba7e-8b43-442e-a57c-3894b09df482", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678949034600, "endTime": 26679091429600}, "additional": {"children": ["6cd7f503-e426-445a-a89a-d14abcf396e3", "3707759f-03b0-4d4f-a6df-cf12078e55e4", "ed737b7e-233a-44a5-8070-d51b7baff642"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "dde0d8d3-e045-423b-9f0c-96dd5414d2e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85aec40f-51f4-491b-888d-b21a92f0d06f", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679091451300, "endTime": 26679130898400}, "additional": {"children": ["72e8e599-4df2-4b12-bcce-bc8c0833f028"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "cf3e2cfb-7667-4e99-ac0b-ad80048e0332"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b2a66bb-f3d0-4d83-beb3-f491e8628a15", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679130910500, "endTime": 26679313025700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "8c93aa97-909c-4887-9d07-bddb529c6115"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71ed7f1e-5374-4e54-bcf7-3b2293af29ad", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679314906100, "endTime": 26679388545000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "ec9c1caf-8a14-4297-ba9c-01e36ca1a77f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f681558-43c3-42ef-bf1f-a408143eaf77", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679388576000, "endTime": 26679399987500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "7c1f1cc6-0307-4876-9125-c3eadd46d9f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e04e076-e4f3-46c6-840a-37d172c6a6c0", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679400017900, "endTime": 26679400203800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "827f0745-2f1c-43da-8415-709af62f4ac0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b10ad62-8392-47a8-a1f7-780addddd12b", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678942093300, "endTime": 26678942104000}, "additional": {"logType": "info", "children": [], "durationId": "386e620d-d5ef-4703-96aa-25b82e0b8c72", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "0790a026-1449-4f87-8fb1-de13244f3c9f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678942125400, "endTime": 26678949015300}, "additional": {"logType": "info", "children": [], "durationId": "e86926aa-d52b-42b4-850e-4420f6da42ef", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "6cd7f503-e426-445a-a89a-d14abcf396e3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678949893400, "endTime": 26678949916000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9bd9ba7e-8b43-442e-a57c-3894b09df482", "logId": "3aedae87-7b77-44bc-8052-2c82a4bfe6a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3aedae87-7b77-44bc-8052-2c82a4bfe6a9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678949893400, "endTime": 26678949916000}, "additional": {"logType": "info", "children": [], "durationId": "6cd7f503-e426-445a-a89a-d14abcf396e3", "parent": "dde0d8d3-e045-423b-9f0c-96dd5414d2e8"}}, {"head": {"id": "3707759f-03b0-4d4f-a6df-cf12078e55e4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678953231200, "endTime": 26679089608600}, "additional": {"children": ["4813197b-6c45-4bcf-9b24-06e6563bfdcf", "beca4175-07b0-489d-8584-f268eef58e59"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9bd9ba7e-8b43-442e-a57c-3894b09df482", "logId": "de62cd26-45d0-475a-a1b9-5a61564d772b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4813197b-6c45-4bcf-9b24-06e6563bfdcf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678953234000, "endTime": 26678960300500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3707759f-03b0-4d4f-a6df-cf12078e55e4", "logId": "3ae0c3b3-3d46-488a-a294-accc39c5a0ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "beca4175-07b0-489d-8584-f268eef58e59", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678960319000, "endTime": 26679089591100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3707759f-03b0-4d4f-a6df-cf12078e55e4", "logId": "da793c84-60eb-4d13-95c0-d0f8aa84cd65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23591060-12be-495b-a01d-478242807e35", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678953242600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "464f5ae6-f276-4375-99e9-f2133f15ace6", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678960141300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ae0c3b3-3d46-488a-a294-accc39c5a0ab", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678953234000, "endTime": 26678960300500}, "additional": {"logType": "info", "children": [], "durationId": "4813197b-6c45-4bcf-9b24-06e6563bfdcf", "parent": "de62cd26-45d0-475a-a1b9-5a61564d772b"}}, {"head": {"id": "504db3f3-0e83-4c5e-9950-4f6286d4e09a", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678960338300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59875de1-fbb5-41bc-8c5e-fe9a5cfddfbe", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678967071900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ba6ee95-f7a3-4b58-b687-5e8bf8767b60", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678967198500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe776e03-4245-47ef-82b3-dbe2269fbc20", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678967347100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e879bfd-c8d6-4bd6-a083-9831d3799e8f", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678967446900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dbba903-f248-473a-971e-3273954dba1a", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678969796200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2236c25-0bae-470c-ade9-99023efd4347", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678975041400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7162044-ed89-400c-995d-deaa1f912fc3", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678990172400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ad831e-bf44-4106-b034-b589ce0ff319", "name": "Sdk init in 59 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679034659100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ff6676c-b09e-4bf2-be88-6929dbaa5940", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679034916000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 54}, "markType": "other"}}, {"head": {"id": "4980cfda-cb51-4939-81ad-9c0b07ed0e82", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679034969900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 54}, "markType": "other"}}, {"head": {"id": "2c8fe8c7-deb7-4942-a029-d99ff0fdc7b5", "name": "Project task initialization takes 53 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679088996900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "069ff805-4fa9-484c-90c5-3951434c59a4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679089386700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d25ec3-42c2-4ca7-953e-da9a04b3a0c4", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679089485000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d4d4340-ec17-4c47-b818-e8dfdb4c2499", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679089540000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da793c84-60eb-4d13-95c0-d0f8aa84cd65", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678960319000, "endTime": 26679089591100}, "additional": {"logType": "info", "children": [], "durationId": "beca4175-07b0-489d-8584-f268eef58e59", "parent": "de62cd26-45d0-475a-a1b9-5a61564d772b"}}, {"head": {"id": "de62cd26-45d0-475a-a1b9-5a61564d772b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678953231200, "endTime": 26679089608600}, "additional": {"logType": "info", "children": ["3ae0c3b3-3d46-488a-a294-accc39c5a0ab", "da793c84-60eb-4d13-95c0-d0f8aa84cd65"], "durationId": "3707759f-03b0-4d4f-a6df-cf12078e55e4", "parent": "dde0d8d3-e045-423b-9f0c-96dd5414d2e8"}}, {"head": {"id": "ed737b7e-233a-44a5-8070-d51b7baff642", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679091376300, "endTime": 26679091399500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9bd9ba7e-8b43-442e-a57c-3894b09df482", "logId": "f453911d-20c0-4c69-afac-ad16e5749d0e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f453911d-20c0-4c69-afac-ad16e5749d0e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679091376300, "endTime": 26679091399500}, "additional": {"logType": "info", "children": [], "durationId": "ed737b7e-233a-44a5-8070-d51b7baff642", "parent": "dde0d8d3-e045-423b-9f0c-96dd5414d2e8"}}, {"head": {"id": "dde0d8d3-e045-423b-9f0c-96dd5414d2e8", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678949034600, "endTime": 26679091429600}, "additional": {"logType": "info", "children": ["3aedae87-7b77-44bc-8052-2c82a4bfe6a9", "de62cd26-45d0-475a-a1b9-5a61564d772b", "f453911d-20c0-4c69-afac-ad16e5749d0e"], "durationId": "9bd9ba7e-8b43-442e-a57c-3894b09df482", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "72e8e599-4df2-4b12-bcce-bc8c0833f028", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679092173700, "endTime": 26679130875900}, "additional": {"children": ["5cfaca8f-5db0-4006-9991-fc0eb6e4719d", "5a06a0bd-ecbe-4e66-83be-d827b0f1afaf", "0ca90af3-4a83-44d3-b2f4-61072a5ad9d6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85aec40f-51f4-491b-888d-b21a92f0d06f", "logId": "e33df7e8-884f-401e-a0b1-142c4df6b34c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cfaca8f-5db0-4006-9991-fc0eb6e4719d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679097679600, "endTime": 26679097741000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e8e599-4df2-4b12-bcce-bc8c0833f028", "logId": "c463ff4e-5b0e-4c26-9d35-30c412aad8f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c463ff4e-5b0e-4c26-9d35-30c412aad8f0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679097679600, "endTime": 26679097741000}, "additional": {"logType": "info", "children": [], "durationId": "5cfaca8f-5db0-4006-9991-fc0eb6e4719d", "parent": "e33df7e8-884f-401e-a0b1-142c4df6b34c"}}, {"head": {"id": "5a06a0bd-ecbe-4e66-83be-d827b0f1afaf", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679102223900, "endTime": 26679128669200}, "additional": {"children": ["b5167ceb-a36e-480c-a07b-bd75356e5685", "ed3787b7-1e25-479c-9a57-b854a5b7a23e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e8e599-4df2-4b12-bcce-bc8c0833f028", "logId": "ed5957c9-d010-456d-8cd1-9c5cad872631"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5167ceb-a36e-480c-a07b-bd75356e5685", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679102258000, "endTime": 26679110189600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a06a0bd-ecbe-4e66-83be-d827b0f1afaf", "logId": "a92388bb-40b2-404d-bc15-bc1efa4937ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed3787b7-1e25-479c-9a57-b854a5b7a23e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679110216900, "endTime": 26679128653100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a06a0bd-ecbe-4e66-83be-d827b0f1afaf", "logId": "ea1ef642-7048-4b19-9544-61bbd0ebca7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7caeabdc-d900-4be2-828b-cb4a025cca49", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679102268900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552288cc-aad3-4a7e-ab70-7fb912bf5bdb", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679109873400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a92388bb-40b2-404d-bc15-bc1efa4937ae", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679102258000, "endTime": 26679110189600}, "additional": {"logType": "info", "children": [], "durationId": "b5167ceb-a36e-480c-a07b-bd75356e5685", "parent": "ed5957c9-d010-456d-8cd1-9c5cad872631"}}, {"head": {"id": "7ae01f77-b8f4-4a9a-8d90-1bda591ed9bb", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679110271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46a004c7-7c10-4775-a833-9a85cc77c7df", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679122581500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2a7783-c495-4fa5-a4bd-ebd9a4bf0816", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679122903300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ddecc0-b3dc-412d-b559-8ac6c55bb678", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679123612100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad9eb5b5-12cb-4273-ae94-7071a95dd710", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679124237600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9aafec-a224-4a61-b591-0af8069430b5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679124396500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b05325b-595c-416c-941a-9746a348341b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679124468100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d416696-485f-4589-b141-25f1555c0320", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679124553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a356b650-2e91-4d93-9ad7-8e6247e6fdc8", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679128284300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47bedddd-3ac2-4a11-8f9e-08ccf1bd2b3a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679128480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01417611-a073-42d5-8993-fb72cc8812c5", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679128552100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b0b33a-9b32-4975-b477-63cce9407e20", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679128603500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1ef642-7048-4b19-9544-61bbd0ebca7e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679110216900, "endTime": 26679128653100}, "additional": {"logType": "info", "children": [], "durationId": "ed3787b7-1e25-479c-9a57-b854a5b7a23e", "parent": "ed5957c9-d010-456d-8cd1-9c5cad872631"}}, {"head": {"id": "ed5957c9-d010-456d-8cd1-9c5cad872631", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679102223900, "endTime": 26679128669200}, "additional": {"logType": "info", "children": ["a92388bb-40b2-404d-bc15-bc1efa4937ae", "ea1ef642-7048-4b19-9544-61bbd0ebca7e"], "durationId": "5a06a0bd-ecbe-4e66-83be-d827b0f1afaf", "parent": "e33df7e8-884f-401e-a0b1-142c4df6b34c"}}, {"head": {"id": "0ca90af3-4a83-44d3-b2f4-61072a5ad9d6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679130801200, "endTime": 26679130829900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "72e8e599-4df2-4b12-bcce-bc8c0833f028", "logId": "df495106-fca6-4bc9-9ecc-bc74863b30ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df495106-fca6-4bc9-9ecc-bc74863b30ac", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679130801200, "endTime": 26679130829900}, "additional": {"logType": "info", "children": [], "durationId": "0ca90af3-4a83-44d3-b2f4-61072a5ad9d6", "parent": "e33df7e8-884f-401e-a0b1-142c4df6b34c"}}, {"head": {"id": "e33df7e8-884f-401e-a0b1-142c4df6b34c", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679092173700, "endTime": 26679130875900}, "additional": {"logType": "info", "children": ["c463ff4e-5b0e-4c26-9d35-30c412aad8f0", "ed5957c9-d010-456d-8cd1-9c5cad872631", "df495106-fca6-4bc9-9ecc-bc74863b30ac"], "durationId": "72e8e599-4df2-4b12-bcce-bc8c0833f028", "parent": "cf3e2cfb-7667-4e99-ac0b-ad80048e0332"}}, {"head": {"id": "cf3e2cfb-7667-4e99-ac0b-ad80048e0332", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679091451300, "endTime": 26679130898400}, "additional": {"logType": "info", "children": ["e33df7e8-884f-401e-a0b1-142c4df6b34c"], "durationId": "85aec40f-51f4-491b-888d-b21a92f0d06f", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "43da0506-1108-46ed-a6c2-fe6c2777e02c", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679169675500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "375bb77a-a971-4faf-a6b1-73a156fde30b", "name": "hvigorfile, resolve hvigorfile dependencies in 182 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679312882000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c93aa97-909c-4887-9d07-bddb529c6115", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679130910500, "endTime": 26679313025700}, "additional": {"logType": "info", "children": [], "durationId": "8b2a66bb-f3d0-4d83-beb3-f491e8628a15", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "e6f533b3-a0f5-4fa9-bc8d-80f43cdce064", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679314491000, "endTime": 26679314874000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "logId": "ab033d5e-afd1-41d2-a0c4-706f3b5d0adf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b768600d-6bfc-49ff-9bd8-083a3457c744", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679314545100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab033d5e-afd1-41d2-a0c4-706f3b5d0adf", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679314491000, "endTime": 26679314874000}, "additional": {"logType": "info", "children": [], "durationId": "e6f533b3-a0f5-4fa9-bc8d-80f43cdce064", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "9831d1e2-cc7c-42cf-8452-eca112c4d96f", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679316194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e165200d-c4d9-4ae5-b6e7-469539e8b04b", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679387593000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9c1caf-8a14-4297-ba9c-01e36ca1a77f", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679314906100, "endTime": 26679388545000}, "additional": {"logType": "info", "children": [], "durationId": "71ed7f1e-5374-4e54-bcf7-3b2293af29ad", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "180c0700-08d8-46ca-ac5e-30b87375ca19", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679393598100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b02b6e-2213-425c-bdbb-ea2a37c50fd0", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679393740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a03a4b0-3283-4373-9ff1-b4c870772650", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679395548200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a202cd5-f3e7-4c18-a614-a188f6c37bae", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679395707800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c1f1cc6-0307-4876-9125-c3eadd46d9f8", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679388576000, "endTime": 26679399987500}, "additional": {"logType": "info", "children": [], "durationId": "3f681558-43c3-42ef-bf1f-a408143eaf77", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "04219837-459a-4242-ba68-b490c5ba4a2c", "name": "Configuration phase cost:458 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679400046900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "827f0745-2f1c-43da-8415-709af62f4ac0", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679400017900, "endTime": 26679400203800}, "additional": {"logType": "info", "children": [], "durationId": "3e04e076-e4f3-46c6-840a-37d172c6a6c0", "parent": "27327d4f-d71a-4d7e-b095-4c30fc97edf6"}}, {"head": {"id": "27327d4f-d71a-4d7e-b095-4c30fc97edf6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678936415700, "endTime": 26679400220100}, "additional": {"logType": "info", "children": ["3b10ad62-8392-47a8-a1f7-780addddd12b", "0790a026-1449-4f87-8fb1-de13244f3c9f", "dde0d8d3-e045-423b-9f0c-96dd5414d2e8", "cf3e2cfb-7667-4e99-ac0b-ad80048e0332", "8c93aa97-909c-4887-9d07-bddb529c6115", "ec9c1caf-8a14-4297-ba9c-01e36ca1a77f", "7c1f1cc6-0307-4876-9125-c3eadd46d9f8", "827f0745-2f1c-43da-8415-709af62f4ac0", "ab033d5e-afd1-41d2-a0c4-706f3b5d0adf"], "durationId": "27e7041f-58f7-403e-94bd-aa513a6bfb45", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "0f82a727-13d0-4480-aff2-03826defa26f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679401557400, "endTime": 26679401582100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "be5b8dd3-a794-4544-9850-f47fd0aea1c0", "logId": "c40d0591-348a-4266-ab7e-d6a68f6b5532"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c40d0591-348a-4266-ab7e-d6a68f6b5532", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679401557400, "endTime": 26679401582100}, "additional": {"logType": "info", "children": [], "durationId": "0f82a727-13d0-4480-aff2-03826defa26f", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "36968588-4b1e-407e-9a23-0094f5141e62", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679400252700, "endTime": 26679401602000}, "additional": {"logType": "info", "children": [], "durationId": "4f7d8327-f431-43a2-b25f-5b6c8481abd7", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "816c4509-2c26-43bf-94de-2f30ca481a2d", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679401609900, "endTime": 26679401639800}, "additional": {"logType": "info", "children": [], "durationId": "9743feed-0faf-4251-a3d0-4259d06cbf34", "parent": "ba3517e4-8439-4e1e-a16f-17a825a25ea6"}}, {"head": {"id": "ba3517e4-8439-4e1e-a16f-17a825a25ea6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678918808600, "endTime": 26679401647400}, "additional": {"logType": "info", "children": ["93621ad5-b746-419c-bba7-58a27f31cb3e", "27327d4f-d71a-4d7e-b095-4c30fc97edf6", "36968588-4b1e-407e-9a23-0094f5141e62", "816c4509-2c26-43bf-94de-2f30ca481a2d", "d7eb3196-593e-429d-9a95-745c0c89cd17", "b54de823-c430-448f-953b-719bfab37faa", "c40d0591-348a-4266-ab7e-d6a68f6b5532"], "durationId": "be5b8dd3-a794-4544-9850-f47fd0aea1c0"}}, {"head": {"id": "588a360e-a0c8-4cb8-9b11-08b4f3386646", "name": "Configuration task cost before running: 487 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679401836100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33176048-ddd8-460e-8e6d-8e63513ff851", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679411370800, "endTime": 26679766112700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json' has been changed."], "detailId": "d325df15-6ff5-4879-9752-858a1a8380e6", "logId": "0178f5fd-e272-418b-afc3-e12091d33a5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d325df15-6ff5-4879-9752-858a1a8380e6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679403800300}, "additional": {"logType": "detail", "children": [], "durationId": "33176048-ddd8-460e-8e6d-8e63513ff851"}}, {"head": {"id": "9c27a699-53cc-4314-8d04-2fe023b5f41b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679405063200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd920e44-aaaa-4c3e-a5ef-d738883d6a53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679405332200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "468a1048-1efc-45e3-8ebe-140b5a84eb50", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679411398800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72357932-e333-421a-b2d8-13bd9424c836", "name": "entry:default@PreBuild is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679428822900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4121e9ee-83b4-491e-8cfb-d1a1cfb4640f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679429920900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ffd44c9-ddc9-40d2-8446-45a4f2a7ac2c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679434365100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd43d97-9198-4bac-be24-e30dd9f4b431", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679434481900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32864f31-e6c6-49ff-b0bf-373d676d5c95", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679764925700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6097264-01ca-4491-a751-c8a01587751b", "name": "Use tool [win32: NODE_HOME]\n [\n  {\n    NODE_HOME: 'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\node'\n  }\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679765128000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077aa6a7-19aa-48e2-b8e7-773683e1ecf1", "name": "entry : default@PreBuild cost memory 14.217048645019531", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679765903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e22235ba-a65c-43eb-a039-76579d350d38", "name": "runTaskFromQueue task cost before running: 852 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679766034400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0178f5fd-e272-418b-afc3-e12091d33a5f", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679411370800, "endTime": 26679766112700, "totalTime": 354645000}, "additional": {"logType": "info", "children": [], "durationId": "33176048-ddd8-460e-8e6d-8e63513ff851"}}, {"head": {"id": "bfb319db-9ee7-4215-b282-02a3333696af", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679770932200, "endTime": 26679773169300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fe82bf44-a8ff-4f5c-a9cd-0be7b12f5030", "logId": "1957cea6-588d-462a-aefb-9e8540251bc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe82bf44-a8ff-4f5c-a9cd-0be7b12f5030", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679769698000}, "additional": {"logType": "detail", "children": [], "durationId": "bfb319db-9ee7-4215-b282-02a3333696af"}}, {"head": {"id": "b22bff7f-b811-458b-a393-8c3d371d9d13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679770062100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "637e8913-d539-4433-bfbf-3d21998e3d34", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679770165300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710d9ca2-0366-4d15-9600-42ad18230b58", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679770942200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1ffc66a-e222-4fef-aec9-3e8f335b7a35", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679772056100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24412997-ece1-4b98-b785-e8f569cd03d8", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679772970900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8702ee0e-9af3-44c4-9da8-911a3963e177", "name": "entry : default@GenerateMetadata cost memory 0.09862518310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679773092400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1957cea6-588d-462a-aefb-9e8540251bc3", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679770932200, "endTime": 26679773169300}, "additional": {"logType": "info", "children": [], "durationId": "bfb319db-9ee7-4215-b282-02a3333696af"}}, {"head": {"id": "f934a496-1a14-4240-9c7d-4dc7c6d3b23b", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776155600, "endTime": 26679776793200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f84e817b-9f20-482c-9ea7-8febe736dcd7", "logId": "46db3369-a75f-4916-a83d-71825d53bdc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f84e817b-9f20-482c-9ea7-8febe736dcd7", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679775339900}, "additional": {"logType": "detail", "children": [], "durationId": "f934a496-1a14-4240-9c7d-4dc7c6d3b23b"}}, {"head": {"id": "b90e9a39-bbf0-4a98-8740-e2a00554315b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679775696800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b06e05-198d-4900-9e7d-88052c38f98f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679775836900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0e42530-d7b8-420f-bbcb-bcaf1391ecff", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776165800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e469c7f8-3621-4855-8f82-281132b527fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776280400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70b144f6-e6fe-4b63-a64c-24c96cb14365", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776344100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79297717-3fb0-4b73-b969-50888e855a29", "name": "entry : default@ConfigureCmake cost memory 0.0370635986328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa1845d-fb8a-4877-8533-3dd125affb7e", "name": "runTaskFromQueue task cost before running: 862 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776647300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46db3369-a75f-4916-a83d-71825d53bdc5", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679776155600, "endTime": 26679776793200, "totalTime": 451400}, "additional": {"logType": "info", "children": [], "durationId": "f934a496-1a14-4240-9c7d-4dc7c6d3b23b"}}, {"head": {"id": "04728496-f0fc-4395-8cf2-957d9183c837", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679781522100, "endTime": 26679784242500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "de3502a0-76f5-40ed-9534-b64c62a2425d", "logId": "8d680e16-93e5-4046-ae8b-dbfcd43a508b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de3502a0-76f5-40ed-9534-b64c62a2425d", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679779036000}, "additional": {"logType": "detail", "children": [], "durationId": "04728496-f0fc-4395-8cf2-957d9183c837"}}, {"head": {"id": "f98f9aa1-2093-46f6-9a08-b9e0ee70f5b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679779570100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dda1aaf-74f9-4009-b1bc-6d18372e350c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679779678900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21563ae-1679-4f01-8d8c-8e628e738513", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679781546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "766b7167-7bdf-46f4-a8d9-8a7ddee97f7c", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679783684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ac7f81-2c17-4ec6-89dc-35043f30b5b2", "name": "entry : default@MergeProfile cost memory 0.10809326171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679784050200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d680e16-93e5-4046-ae8b-dbfcd43a508b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679781522100, "endTime": 26679784242500}, "additional": {"logType": "info", "children": [], "durationId": "04728496-f0fc-4395-8cf2-957d9183c837"}}, {"head": {"id": "ab96e092-0f3c-4467-a5fa-05d3909487d4", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679790145100, "endTime": 26679793557100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9d498b86-1597-475e-8669-442d4f0b51c6", "logId": "f1256910-bde2-471e-9aa2-0a47416847da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d498b86-1597-475e-8669-442d4f0b51c6", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679787091500}, "additional": {"logType": "detail", "children": [], "durationId": "ab96e092-0f3c-4467-a5fa-05d3909487d4"}}, {"head": {"id": "6c650282-92d4-4aa4-aa31-2eed88fb0b92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679787681900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afe0155-21be-451f-ae1f-f7dc25d84008", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679787849400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb0771ff-ef13-4608-b31c-2e5864b74de4", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679790253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f321689-c64b-403e-8686-99f972558fc3", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679791993500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73c3bdf6-c398-4fd9-bc7c-00ea8ee99da9", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679793263300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54364f52-10e3-46ec-88c3-25a73f848028", "name": "entry : default@CreateBuildProfile cost memory 0.10746002197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679793445000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1256910-bde2-471e-9aa2-0a47416847da", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679790145100, "endTime": 26679793557100}, "additional": {"logType": "info", "children": [], "durationId": "ab96e092-0f3c-4467-a5fa-05d3909487d4"}}, {"head": {"id": "90365846-0417-4198-a06f-b51f944249a4", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679798393400, "endTime": 26679799260500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c4c2281a-f5ab-4f1a-b784-a50d4afb3ad1", "logId": "2e13d7ba-5def-4075-b6fd-02ae75f1a712"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4c2281a-f5ab-4f1a-b784-a50d4afb3ad1", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679796118500}, "additional": {"logType": "detail", "children": [], "durationId": "90365846-0417-4198-a06f-b51f944249a4"}}, {"head": {"id": "15af981b-689a-483c-b07b-74d724b7069c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679796999000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96f0a9a-8772-45e4-8afd-b615452c466b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679797221400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d44f964-720c-4635-855e-12d11cf8ea1d", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679798412000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f678b2df-5ee8-4994-8848-6f25f326d939", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679798590800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1814646-fb93-4cfd-8f69-eb93ec3be5f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679798686500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b903be2-c82f-4dde-a726-eb1a1e8542bf", "name": "entry : default@PreCheckSyscap cost memory 0.03728485107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679799071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "410d5ccf-844a-461d-98d6-efc9bfcbf170", "name": "runTaskFromQueue task cost before running: 885 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679799192600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e13d7ba-5def-4075-b6fd-02ae75f1a712", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679798393400, "endTime": 26679799260500, "totalTime": 778000}, "additional": {"logType": "info", "children": [], "durationId": "90365846-0417-4198-a06f-b51f944249a4"}}, {"head": {"id": "cbdabb9d-bb31-4abb-b9e7-0cc8d240abe6", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679810366900, "endTime": 26679811186400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "90adc220-dfc7-4612-a5cf-734267de78e5", "logId": "9ac235d8-c329-460a-8685-ad168b2e9cc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90adc220-dfc7-4612-a5cf-734267de78e5", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679801758900}, "additional": {"logType": "detail", "children": [], "durationId": "cbdabb9d-bb31-4abb-b9e7-0cc8d240abe6"}}, {"head": {"id": "9e4fa57c-bfa1-41a9-a074-2de96d3304ab", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679802272100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "270d6420-8b6b-4658-b872-92656d844d01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679802506100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a30060-dc7d-4ba7-8ca2-e701bce20feb", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679810382900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc109827-7cf0-4bc0-88ae-bd9143c47008", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679810690800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a7e71b-1aaf-4015-a77e-15a3f3fa8658", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03952789306640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679810965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e565dff3-dfe9-4cbe-b720-7aa34a539595", "name": "runTaskFromQueue task cost before running: 897 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679811112700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ac235d8-c329-460a-8685-ad168b2e9cc6", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679810366900, "endTime": 26679811186400, "totalTime": 722300}, "additional": {"logType": "info", "children": [], "durationId": "cbdabb9d-bb31-4abb-b9e7-0cc8d240abe6"}}, {"head": {"id": "6432a231-c78e-48fd-b152-ef518a9b56b2", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679818591400, "endTime": 26679820652100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "7598b3c3-40d4-4f3c-a8d8-7785b7ff0f1f", "logId": "39c3bcb8-14b8-4e8e-a5e8-069c24e9defa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7598b3c3-40d4-4f3c-a8d8-7785b7ff0f1f", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679816091100}, "additional": {"logType": "detail", "children": [], "durationId": "6432a231-c78e-48fd-b152-ef518a9b56b2"}}, {"head": {"id": "8390653d-0d7e-4643-9e26-c5bb4af55d3d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679816592200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7de2deb7-a179-4d51-ac62-93d608b028e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679816712800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d05a587a-f7f6-46b7-8d1f-d89f58368cc7", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679818606800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "816b2196-31f8-4e55-8d41-e331865f0ed5", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679819959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "875d65d6-c33d-40a6-8f87-8dbe7b5a5965", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679820124600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96dd69fb-043a-409f-a935-d3867417318a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679820214900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c06fcd4-efa9-449d-90af-d484ec6e8abc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679820329200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1b08df5-441d-4990-b955-678d82eae65a", "name": "entry : default@ProcessIntegratedHsp cost memory 0.12078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679820461400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1380c73a-2889-42ae-a974-167ad9468fb3", "name": "runTaskFromQueue task cost before running: 906 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679820581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c3bcb8-14b8-4e8e-a5e8-069c24e9defa", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679818591400, "endTime": 26679820652100, "totalTime": 1972800}, "additional": {"logType": "info", "children": [], "durationId": "6432a231-c78e-48fd-b152-ef518a9b56b2"}}, {"head": {"id": "789a2153-84d4-45d3-9af7-bb65faebb8ae", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679827489000, "endTime": 26679828114500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "238523e1-86b2-4c41-be41-a381d3fec39c", "logId": "90fe398c-24d9-4b05-9d2b-ef9e8f8f589c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "238523e1-86b2-4c41-be41-a381d3fec39c", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679826133200}, "additional": {"logType": "detail", "children": [], "durationId": "789a2153-84d4-45d3-9af7-bb65faebb8ae"}}, {"head": {"id": "c6827f69-781a-463d-8767-96fbbf59a73f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679826607000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647dc223-1424-4d67-80a1-e4009585cb12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679826721600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449cb8c2-2033-4092-aeb8-edf7985e1a69", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679827503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ab0aa0-1a78-4f72-9bfa-39353fdc9951", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679827713200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2233c3fa-f01b-4d1f-80c8-04ce1f4bdc60", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679827829200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5a4c24-c0a4-4670-b911-77cf2fd8ab8a", "name": "entry : default@BuildNativeWithCmake cost memory 0.03826141357421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679827958000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2b475c-d85c-49cf-901b-a16371cc49e0", "name": "runTaskFromQueue task cost before running: 914 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679828052900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90fe398c-24d9-4b05-9d2b-ef9e8f8f589c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679827489000, "endTime": 26679828114500, "totalTime": 541500}, "additional": {"logType": "info", "children": [], "durationId": "789a2153-84d4-45d3-9af7-bb65faebb8ae"}}, {"head": {"id": "ca7770d2-c37a-4a69-83b6-5cb473a47f00", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679833749800, "endTime": 26679836967100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bf99ea32-79cb-41bb-9f19-e21e453ddf80", "logId": "91aa0316-7dae-4f5d-b164-8beed5d68710"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf99ea32-79cb-41bb-9f19-e21e453ddf80", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679829929400}, "additional": {"logType": "detail", "children": [], "durationId": "ca7770d2-c37a-4a69-83b6-5cb473a47f00"}}, {"head": {"id": "007edceb-2c1f-4742-80a0-1f1d3c26adda", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679830679500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0868a3c7-1d3b-4466-8890-bd2482ce8454", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679832743800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07fb642e-de5e-4bf7-af11-39a32a522e8c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679833764700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f753c83-8826-4c7d-8f42-f9daa1c3f11f", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679836729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aae68d3-1729-45a1-96da-182a71b3b5bf", "name": "entry : default@MakePackInfo cost memory 0.14232635498046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679836895900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91aa0316-7dae-4f5d-b164-8beed5d68710", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679833749800, "endTime": 26679836967100}, "additional": {"logType": "info", "children": [], "durationId": "ca7770d2-c37a-4a69-83b6-5cb473a47f00"}}, {"head": {"id": "ec13bffc-ac74-4e01-a801-c569f2e54840", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679841105000, "endTime": 26679843827100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "8749d9bd-0c44-4768-b86a-f6d50d0ddcc3", "logId": "043d73f4-2175-4b44-af7f-98f3b3ff21b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8749d9bd-0c44-4768-b86a-f6d50d0ddcc3", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679839245600}, "additional": {"logType": "detail", "children": [], "durationId": "ec13bffc-ac74-4e01-a801-c569f2e54840"}}, {"head": {"id": "2e83424f-e07e-47d9-9b89-78050eaa3c77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679839691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ece086a-84e9-46e7-8216-4f253e7cb28b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679839877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09fe78f7-d4fd-4e4b-9ac4-fd199d42c723", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679841118300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dacd8dc-3488-467a-ad25-5177e22349dd", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679841298800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a86d7936-3574-40c5-b17a-f093292a366d", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679842144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77e44174-f77a-41b0-8893-5e8725933b9e", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679843366500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2044d2fa-9879-4f94-a621-12f6334c31dc", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679843478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "402ac13d-c6fb-4b92-b7e5-4764499e4ca4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679843560800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232f013c-b295-4813-b2cd-3291a545f1bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679843615200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "037a75f4-af54-4076-8492-d186a7882be5", "name": "entry : default@SyscapTransform cost memory 0.15578460693359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679843694400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3955427d-b305-465b-8719-8d76362bf294", "name": "runTaskFromQueue task cost before running: 929 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679843769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "043d73f4-2175-4b44-af7f-98f3b3ff21b9", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679841105000, "endTime": 26679843827100, "totalTime": 2650600}, "additional": {"logType": "info", "children": [], "durationId": "ec13bffc-ac74-4e01-a801-c569f2e54840"}}, {"head": {"id": "96ede8eb-b05e-4fef-927d-5e4aafa2dc0c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679855722000, "endTime": 26679857135600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1b90eb6b-3cd2-4422-8054-541a5dbb3f25", "logId": "c6baf1c1-5e8e-4ea9-8954-422e0a555c8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b90eb6b-3cd2-4422-8054-541a5dbb3f25", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679845293100}, "additional": {"logType": "detail", "children": [], "durationId": "96ede8eb-b05e-4fef-927d-5e4aafa2dc0c"}}, {"head": {"id": "b0b46860-19c7-4f1e-92e9-45129d9a4c19", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679845679000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "937784a6-2c5b-4b00-9af2-fdc562b6bfc2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679845781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50e0093b-686d-42e4-9c94-c40a69e75cc7", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679855740300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf708f3-912d-428b-b8f8-1490891805fb", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679856898300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb5a8a6-0424-4708-807c-7e9525480daa", "name": "entry : default@ProcessProfile cost memory 0.061126708984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679857033100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6baf1c1-5e8e-4ea9-8954-422e0a555c8e", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679855722000, "endTime": 26679857135600}, "additional": {"logType": "info", "children": [], "durationId": "96ede8eb-b05e-4fef-927d-5e4aafa2dc0c"}}, {"head": {"id": "a984e7c5-fee6-4f1b-ae39-3d6405e4c782", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679860904000, "endTime": 26679869244000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json' has been changed."], "detailId": "6822f6d4-fb7d-4505-b035-f457be87440e", "logId": "4810942e-cb5a-4df5-95a2-81b0a6acbdd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6822f6d4-fb7d-4505-b035-f457be87440e", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679858909200}, "additional": {"logType": "detail", "children": [], "durationId": "a984e7c5-fee6-4f1b-ae39-3d6405e4c782"}}, {"head": {"id": "ee9bee8f-a897-4edd-8716-dabb4c11a1aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679859279100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe48a18c-9bca-4a4c-bee4-291d96be3c02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679859374700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb22aad0-5d81-4e6a-8ad2-1a70e13103d5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679860916600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2edb9d9f-18a6-4920-955b-7f8018d9f2ee", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679865382100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d9adb9-8b74-4812-ba9d-92acdce39ceb", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679865524400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97020fa-2553-4f98-b042-764215bbf7a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679865621700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7688f8de-b355-4b4b-a6ff-04ca5c698edd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679865676900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352c984f-a0aa-4e6b-be75-361516b6482f", "name": "entry : default@ProcessRouterMap cost memory 0.33562469482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679869019300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7807102-9680-4992-9af4-f7fd9ee6e47a", "name": "runTaskFromQueue task cost before running: 955 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679869169900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4810942e-cb5a-4df5-95a2-81b0a6acbdd4", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679860904000, "endTime": 26679869244000, "totalTime": 8234500}, "additional": {"logType": "info", "children": [], "durationId": "a984e7c5-fee6-4f1b-ae39-3d6405e4c782"}}, {"head": {"id": "a7c04fa0-4468-4a45-bc6a-f332385b6a42", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872750000, "endTime": 26679874161000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a7a5b637-37f5-4a52-9982-a0efa6a7386a", "logId": "4d44228e-b14c-43f3-a147-6f5b90bb7451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7a5b637-37f5-4a52-9982-a0efa6a7386a", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679871642700}, "additional": {"logType": "detail", "children": [], "durationId": "a7c04fa0-4468-4a45-bc6a-f332385b6a42"}}, {"head": {"id": "262a26b6-c4e7-4c67-91fb-a5d0d01ac0bf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872010200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa8260c-f810-44ac-b1dc-88c4aea072a1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8b8a0e-bbc5-4f5e-905c-cbb2147d0754", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872761100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89519fba-68f7-4012-8143-34cce0ba6b45", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872904500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7990b1b4-4acd-49b3-9ca3-9b53b2372a80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872968700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47adf5f-774f-49a0-b368-1ee8b5558d73", "name": "entry : default@BuildNativeWithNinja cost memory 0.058258056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679873959700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c333e2-c117-42ee-9e38-5966092f1e67", "name": "runTaskFromQueue task cost before running: 960 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679874093200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d44228e-b14c-43f3-a147-6f5b90bb7451", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679872750000, "endTime": 26679874161000, "totalTime": 1320700}, "additional": {"logType": "info", "children": [], "durationId": "a7c04fa0-4468-4a45-bc6a-f332385b6a42"}}, {"head": {"id": "e28cad9b-e57a-4be1-8c6d-cc3962481677", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679878932100, "endTime": 26679884277900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bda704b1-bfcc-42a0-a781-2d593f5abe7e", "logId": "be71d07d-da71-418c-a036-8ed1f95187bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bda704b1-bfcc-42a0-a781-2d593f5abe7e", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679876289300}, "additional": {"logType": "detail", "children": [], "durationId": "e28cad9b-e57a-4be1-8c6d-cc3962481677"}}, {"head": {"id": "cb0f0a74-bd6a-4d43-a5a4-6ccc1526d7be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679876627200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4945bfe0-d2b8-4eeb-9010-512cfbfa446d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679876714900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc53e62a-dc9a-4a1d-934d-f109fa8d3918", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679877678600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81a33672-1e66-49c3-9600-929d8dada49f", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679880828500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bb301b8-3d77-4479-bf60-249abe2d6b4d", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679882674700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d2e6421-111f-4673-b055-540ddd0422cb", "name": "entry : default@ProcessResource cost memory 0.17333984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679882807800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be71d07d-da71-418c-a036-8ed1f95187bc", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679878932100, "endTime": 26679884277900}, "additional": {"logType": "info", "children": [], "durationId": "e28cad9b-e57a-4be1-8c6d-cc3962481677"}}, {"head": {"id": "cf53fbae-3059-4e18-9e09-879bca143711", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679892763100, "endTime": 26679924492800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json' has been changed."], "detailId": "5da0fec1-56d6-4f68-a8ff-77ef6fe2b652", "logId": "6bff2878-3e51-47c2-ae4c-b4105bf7b50a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5da0fec1-56d6-4f68-a8ff-77ef6fe2b652", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679887533400}, "additional": {"logType": "detail", "children": [], "durationId": "cf53fbae-3059-4e18-9e09-879bca143711"}}, {"head": {"id": "e7f363b0-01ae-4966-a2b4-c11121beaf56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679887904600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a29a248-da1a-461f-a170-bda84da2320b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679887994300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "584142bc-6e6b-4ac4-8f46-f08ff18e9f27", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679892831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ca96e53-9369-4ed9-ab68-6e0f2d95c4cc", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679912638700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7df67d86-7c9f-4b8e-8e38-54d49cc6e4c4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679912792500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e964be2-dcb4-4f95-88f7-5264b156df65", "name": "entry : default@GenerateLoaderJson cost memory 1.0265960693359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679923045600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a2ad5e-6284-43a0-9eb4-bb39028546cc", "name": "runTaskFromQueue task cost before running: 1 s 10 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679924106200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bff2878-3e51-47c2-ae4c-b4105bf7b50a", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679892763100, "endTime": 26679924492800, "totalTime": 30995600}, "additional": {"logType": "info", "children": [], "durationId": "cf53fbae-3059-4e18-9e09-879bca143711"}}, {"head": {"id": "43f80643-c66f-4e60-8e45-2c69f0f162cd", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679934487200, "endTime": 26679938578800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3da25110-7e5c-4236-b321-49151525d0cc", "logId": "51c24b51-a555-4a2a-8e03-7d779fe4317f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3da25110-7e5c-4236-b321-49151525d0cc", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679932567600}, "additional": {"logType": "detail", "children": [], "durationId": "43f80643-c66f-4e60-8e45-2c69f0f162cd"}}, {"head": {"id": "2a4bc05d-19e0-4941-9811-75e0c92efd35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679933253500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b83aad8-168c-41aa-a120-92133076d411", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679933440400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cd6fbc2-58b9-47a1-b187-3443114cab1f", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679934509900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e713e9-4085-4b40-b20a-6cc3d9fd7557", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679937259200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b76a548-5b72-4a47-a15e-1b4996748080", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679937404900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eacd590-9383-455c-88d8-67a7c366dab4", "name": "entry : default@ProcessLibs cost memory 0.129608154296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679938339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42af058-795c-438b-8075-7b29ad908e9d", "name": "runTaskFromQueue task cost before running: 1 s 24 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679938473900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c24b51-a555-4a2a-8e03-7d779fe4317f", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679934487200, "endTime": 26679938578800, "totalTime": 3959300}, "additional": {"logType": "info", "children": [], "durationId": "43f80643-c66f-4e60-8e45-2c69f0f162cd"}}, {"head": {"id": "298d2c7e-d369-4c1e-8bc3-5df395222c18", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679949964700, "endTime": 26680255300800}, "additional": {"children": ["926e4417-35bc-45e0-a8ea-fc8fc5f5fd79", "c6bdeca5-4160-407b-8289-73dee9a38529"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources' has been changed."], "detailId": "abf8e9c1-7599-4f9d-aaab-a226e9ce1b5a", "logId": "55e591c2-7340-496d-8261-90edda50be62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abf8e9c1-7599-4f9d-aaab-a226e9ce1b5a", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679943741100}, "additional": {"logType": "detail", "children": [], "durationId": "298d2c7e-d369-4c1e-8bc3-5df395222c18"}}, {"head": {"id": "3b5dbe7d-0d2f-4af7-a7e4-1d426cfa148b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679944251900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5da79054-0d18-410b-bb31-812fc7232ed3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679944512800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad31f700-c1dc-4e51-8669-fdae11167ed8", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679945803900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70f120da-eb19-4a07-ae36-c7143f50048e", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679950005400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cbe3f30-88e1-4d06-81b7-55d7a0544bec", "name": "entry:default@CompileResource is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679962567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0437eeef-40da-45ed-8ccd-24da0338dfab", "name": "Incremental task entry:default@CompileResource pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679962714200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "926e4417-35bc-45e0-a8ea-fc8fc5f5fd79", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679964179700, "endTime": 26680032974400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "298d2c7e-d369-4c1e-8bc3-5df395222c18", "logId": "f0f6058c-517d-4ec1-954a-2600257e63bb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0f6058c-517d-4ec1-954a-2600257e63bb", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679964179700, "endTime": 26680032974400}, "additional": {"logType": "info", "children": [], "durationId": "926e4417-35bc-45e0-a8ea-fc8fc5f5fd79", "parent": "55e591c2-7340-496d-8261-90edda50be62"}}, {"head": {"id": "bcd7c01e-93b1-4fab-b153-dd6d7dd9a49c", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-l',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680033579200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6bdeca5-4160-407b-8289-73dee9a38529", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680034770600, "endTime": 26680252109500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "298d2c7e-d369-4c1e-8bc3-5df395222c18", "logId": "cd5e727b-7ce1-4d54-a252-3428b2121db0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11c6140d-81c8-43f3-960c-39653be3a24f", "name": "current process  memoryUsage: {\n  rss: 586743808,\n  heapTotal: 162332672,\n  heapUsed: 140205256,\n  external: 3247154,\n  arrayBuffers: 240995\n} os memoryUsage :21.797019958496094", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680036502400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "084f6e48-43c9-4d8b-a906-67f4bc612399", "name": "06-24 15:54:08.291 26276 26792 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680187794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "109f5a16-51a8-488c-9b22-7bb0e9dad74a", "name": "06-24 15:54:08.291 26276 26792 E C01400/ImageTranscoderUtils: ImageFilter IN D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources\\base\\media\\app_icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680188766200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "232aca86-853c-4d7a-83ed-0328b8b06571", "name": "06-24 15:54:08.293 26276 26792 E C01400/ImageTranscoder: TranscodeSLR Only zoom-out is supported.\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680189929500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a8ab4c-eea0-44b5-87b2-6005734dca7b", "name": "Warning: ScaleImage failed, error message: INVALID_PARAMETERS, file path = D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources\\base\\media\\app_icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680190209900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a68b76a-b8f4-4c94-b529-575140dd8823", "name": "06-24 15:54:08.296 26276 26792 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680191945800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8d1e5c3-7765-4dd0-83eb-89bdad4fa04f", "name": "06-24 15:54:08.296 26276 26792 E C01400/ImageTranscoderUtils: ImageFilter IN D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\media\\icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680192181700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e3762fc-9082-432d-a677-12d5dc23f243", "name": "06-24 15:54:08.296 26276 26792 E C01400/ImageTranscoder: TranscodeSLR Only zoom-out is supported.\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680192620000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ff630f-0a7e-4196-99fb-e3373ddd235c", "name": "Warning: ScaleImage failed, error message: INVALID_PARAMETERS, file path = D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\media\\icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680192811700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5ccf71-e665-4a31-acae-e6ed421a43f7", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680194657400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06860935-c226-4eeb-9fe6-4513d5deade1", "name": "astcenc customized so is not be opened when dlclose!\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680249461000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd5e727b-7ce1-4d54-a252-3428b2121db0", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680034770600, "endTime": 26680252109500}, "additional": {"logType": "info", "children": [], "durationId": "c6bdeca5-4160-407b-8289-73dee9a38529", "parent": "55e591c2-7340-496d-8261-90edda50be62"}}, {"head": {"id": "0dd4e153-14a9-4f56-9033-8096a24cae9f", "name": "entry : default@CompileResource cost memory -2.9505081176757812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680255040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40023427-872e-41ac-8725-212c6dd7d3a0", "name": "runTaskFromQueue task cost before running: 1 s 341 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680255216700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e591c2-7340-496d-8261-90edda50be62", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26679949964700, "endTime": 26680255300800, "totalTime": 305199900}, "additional": {"logType": "info", "children": ["f0f6058c-517d-4ec1-954a-2600257e63bb", "cd5e727b-7ce1-4d54-a252-3428b2121db0"], "durationId": "298d2c7e-d369-4c1e-8bc3-5df395222c18"}}, {"head": {"id": "091d5c04-d464-40db-9c6b-ea356abfd884", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680260903300, "endTime": 26680261988200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3b3d5667-e89c-4e96-b695-30e9b3a22d81", "logId": "c210a5e5-4cdd-4fe9-8d5f-ee3f3523ca20"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b3d5667-e89c-4e96-b695-30e9b3a22d81", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680258105600}, "additional": {"logType": "detail", "children": [], "durationId": "091d5c04-d464-40db-9c6b-ea356abfd884"}}, {"head": {"id": "3d161a8b-da85-41ab-a859-a9aabe80e9e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680258575600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bdb8f89-0bf6-47a0-aa7d-055e53aa77c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680258694000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "288ee6ec-26ab-4733-afe6-466deb24e5c5", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680260915100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7580e36c-deaa-4fa8-bb44-447f57cc668d", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680261122700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2299525a-9b8b-4aad-a49b-c63df57e6c7e", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680261829200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "564f265b-ffb7-4291-bd8c-75c8eeb95a47", "name": "entry : default@DoNativeStrip cost memory 0.07762908935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680261921300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c210a5e5-4cdd-4fe9-8d5f-ee3f3523ca20", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680260903300, "endTime": 26680261988200}, "additional": {"logType": "info", "children": [], "durationId": "091d5c04-d464-40db-9c6b-ea356abfd884"}}, {"head": {"id": "187b6eb8-0814-4c48-847c-f3ec1f3caa37", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680268630200, "endTime": 26682603391300}, "additional": {"children": ["278d6564-9b7d-4c8e-a468-594b5529e7be", "32203e15-c952-40e9-b028-d2c489c0f676"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default' has been changed."], "detailId": "0845dc05-e1ec-4b02-a7e6-c08e5f6b0990", "logId": "6d7b77ab-fc8e-4dec-af9d-114bfa527970"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0845dc05-e1ec-4b02-a7e6-c08e5f6b0990", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680263368300}, "additional": {"logType": "detail", "children": [], "durationId": "187b6eb8-0814-4c48-847c-f3ec1f3caa37"}}, {"head": {"id": "ad818423-b65b-4580-a300-2b38e15a7db0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680263823200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b0e147-044a-4b69-ba3c-fc9ea1e8f8ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680263960000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b282b99-e360-42d3-b525-88e476e99062", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680268647400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ee0e9ea-048b-42de-b0b6-f8c9f4a968d1", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680275144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be47ebd-4ccd-42b2-9077-a6cf512f06bc", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680275321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abaa5cfb-04a8-4393-abfb-77dbf7d58b5f", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680287368500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752ce2e9-ab1d-4a4d-ad88-e767c7f156cb", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680287861100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce875ca-f5cc-4739-8539-a0297518eea1", "name": "default@CompileArkTS work[21] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680289309200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278d6564-9b7d-4c8e-a468-594b5529e7be", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26680404860500, "endTime": 26682595308600}, "additional": {"children": ["f9360306-49b9-45d7-821f-2ffbe50615aa", "3de61ee4-d5b4-4807-94e1-49bacadd4eb5", "b3d79103-f798-4fe0-b389-fb54812de1c4", "4c938782-0148-4b1e-bfad-6444b162a40a", "69c5cad8-fde3-4676-bad7-d87b994d57de", "82b98a85-128a-4f3b-87d7-a05bf2a91699"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "187b6eb8-0814-4c48-847c-f3ec1f3caa37", "logId": "4993169c-e587-42f8-8525-a7a084126263"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "032d89f5-351b-46c9-8741-c7cdc921a8a1", "name": "default@CompileArkTS work[21] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680290936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c83e177-724f-479b-8413-48f468d0ccb9", "name": "default@CompileArkTS work[21] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680291074500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07a0de5f-dc19-4425-9a9f-983906d52ea6", "name": "CopyResources startTime: 26680291137800", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680291141700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91fede6-2321-422d-806b-a9114ae1b227", "name": "default@CompileArkTS work[22] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680291359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32203e15-c952-40e9-b028-d2c489c0f676", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26681759561000, "endTime": 26681770735000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "187b6eb8-0814-4c48-847c-f3ec1f3caa37", "logId": "01416d24-5126-498d-98fa-532715ab2f05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fea489cc-9a28-434a-9994-618ccb61dac9", "name": "default@CompileArkTS work[22] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680292253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b993774-f433-468d-a5f1-cea889ff39f7", "name": "default@CompileArkTS work[22] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680292388000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af13ba38-f705-4ecb-ae07-8fd9e0287653", "name": "entry : default@CompileArkTS cost memory 1.286712646484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680292610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4252db3a-ed58-4168-9eab-90cc53259240", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680299578100, "endTime": 26680302568200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "867af80b-044e-4905-ad36-8b0c819e489a", "logId": "e8642a81-a989-43e4-ab39-799c77af3818"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "867af80b-044e-4905-ad36-8b0c819e489a", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680294165200}, "additional": {"logType": "detail", "children": [], "durationId": "4252db3a-ed58-4168-9eab-90cc53259240"}}, {"head": {"id": "7033df69-8edc-45fc-9ab1-2bf4a4c6e453", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680294562400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "795e3cc3-d0ca-41b2-aee0-8b716cc909b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680294652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d35c2bf-af64-41d2-b7e3-f30d7b833e89", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680299594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489bde0a-7d64-4d87-93d6-5f708ef61e39", "name": "entry : default@BuildJS cost memory 0.13287353515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680302368900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618646a1-89fc-4d3a-8bc3-2ca391d8abd6", "name": "runTaskFromQueue task cost before running: 1 s 388 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680302503500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8642a81-a989-43e4-ab39-799c77af3818", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680299578100, "endTime": 26680302568200, "totalTime": 2902000}, "additional": {"logType": "info", "children": [], "durationId": "4252db3a-ed58-4168-9eab-90cc53259240"}}, {"head": {"id": "636fdc03-073f-45ee-ac53-55ea7c08ba38", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680308330500, "endTime": 26680309988000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1001fbb3-4bb4-48eb-a893-27615cda5988", "logId": "a7d6f456-a7ed-4a85-90c7-31527657584b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1001fbb3-4bb4-48eb-a893-27615cda5988", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680304077400}, "additional": {"logType": "detail", "children": [], "durationId": "636fdc03-073f-45ee-ac53-55ea7c08ba38"}}, {"head": {"id": "dd92e756-f86c-4fc9-8ab3-080301be4264", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680304534600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4b197f-eaa4-4130-b666-672c98ab2af1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680304645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3e606a-f0cf-4003-8da9-210a2400b39c", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680308350200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcbac6a0-6ee3-45c4-8f81-0eb5136de5a6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680308762600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc37f96-aa5c-4796-b211-58d97c4e23d9", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680309802300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056c446c-b521-4077-ac81-f5bb21ca045c", "name": "entry : default@CacheNativeLibs cost memory 0.09134674072265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680309912200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d6f456-a7ed-4a85-90c7-31527657584b", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680308330500, "endTime": 26680309988000}, "additional": {"logType": "info", "children": [], "durationId": "636fdc03-073f-45ee-ac53-55ea7c08ba38"}}, {"head": {"id": "8f19e34d-5b6a-472f-abcd-ba174724dc1a", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680403981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c204500-cee9-487d-a11b-468f47d1e1e4", "name": "default@CompileArkTS work[21] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680404529100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34311d73-5f14-44b0-ad04-9b5c06858c20", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680405084300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6bfff3c-847c-45f6-bbaf-4d10299e08a7", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680405537500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8590627-bf6d-4151-b39f-2931eae33e36", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680405764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cdee4ca-219c-48d6-b498-6cee2d046957", "name": "default@CompileArkTS work[22] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680407496200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ad79f9-7ef7-46a6-98fd-0e64b2e288af", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26681770921000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e54aac6e-ad11-4ed0-9769-d31ce91fcd45", "name": "CopyResources is end, endTime: 26681771049200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26681771055100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e699c827-61df-4106-9df1-28f5b093965b", "name": "default@CompileArkTS work[22] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26681771133700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01416d24-5126-498d-98fa-532715ab2f05", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26681759561000, "endTime": 26681770735000}, "additional": {"logType": "info", "children": [], "durationId": "32203e15-c952-40e9-b028-d2c489c0f676", "parent": "6d7b77ab-fc8e-4dec-af9d-114bfa527970"}}, {"head": {"id": "a5395e24-171a-4cfc-a711-89dd4a10d614", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26681771289300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1916e679-bc03-4c9b-8ca0-d154fa261087", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682595644900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9360306-49b9-45d7-821f-2ffbe50615aa", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26680404968600, "endTime": 26680411020800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "278d6564-9b7d-4c8e-a468-594b5529e7be", "logId": "45ddaa10-feb3-448d-a2ea-2942e349c1db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45ddaa10-feb3-448d-a2ea-2942e349c1db", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680404968600, "endTime": 26680411020800}, "additional": {"logType": "info", "children": [], "durationId": "f9360306-49b9-45d7-821f-2ffbe50615aa", "parent": "4993169c-e587-42f8-8525-a7a084126263"}}, {"head": {"id": "3de61ee4-d5b4-4807-94e1-49bacadd4eb5", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26680411049300, "endTime": 26680411197000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "278d6564-9b7d-4c8e-a468-594b5529e7be", "logId": "6739d67b-d842-4ac8-94df-1cd5ef5b28a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6739d67b-d842-4ac8-94df-1cd5ef5b28a0", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680411049300, "endTime": 26680411197000}, "additional": {"logType": "info", "children": [], "durationId": "3de61ee4-d5b4-4807-94e1-49bacadd4eb5", "parent": "4993169c-e587-42f8-8525-a7a084126263"}}, {"head": {"id": "b3d79103-f798-4fe0-b389-fb54812de1c4", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26680411205800, "endTime": 26680411331700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "278d6564-9b7d-4c8e-a468-594b5529e7be", "logId": "2ad57ec8-cc9d-4c37-9889-498b2b770c8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ad57ec8-cc9d-4c37-9889-498b2b770c8c", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680411205800, "endTime": 26680411331700}, "additional": {"logType": "info", "children": [], "durationId": "b3d79103-f798-4fe0-b389-fb54812de1c4", "parent": "4993169c-e587-42f8-8525-a7a084126263"}}, {"head": {"id": "4c938782-0148-4b1e-bfad-6444b162a40a", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26680411373400, "endTime": 26682528567900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "278d6564-9b7d-4c8e-a468-594b5529e7be", "logId": "185d8e43-3848-4752-9616-210af64499a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "185d8e43-3848-4752-9616-210af64499a1", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680411373400, "endTime": 26682528567900}, "additional": {"logType": "info", "children": [], "durationId": "4c938782-0148-4b1e-bfad-6444b162a40a", "parent": "4993169c-e587-42f8-8525-a7a084126263"}}, {"head": {"id": "69c5cad8-fde3-4676-bad7-d87b994d57de", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682528597000, "endTime": 26682541581800}, "additional": {"children": ["21cf0dea-5d99-4b32-b44b-5ad86c3082d5", "6e7d22b3-4958-4491-971a-da6cf1af813b", "5d3e1f3b-9aae-4e2f-8644-054526b1b3bb"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "278d6564-9b7d-4c8e-a468-594b5529e7be", "logId": "c8c317b9-7b9a-4699-9d50-b4af2c2a2128"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8c317b9-7b9a-4699-9d50-b4af2c2a2128", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682528597000, "endTime": 26682541581800}, "additional": {"logType": "info", "children": ["ead1023d-a0fb-4801-9fa1-4fb77a0d2c61", "c9869f6a-7d81-4266-8f88-89bce8a849b6", "20801766-a7a8-4a34-bbbc-7b488f07a693"], "durationId": "69c5cad8-fde3-4676-bad7-d87b994d57de", "parent": "4993169c-e587-42f8-8525-a7a084126263"}}, {"head": {"id": "21cf0dea-5d99-4b32-b44b-5ad86c3082d5", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682528628800, "endTime": 26682528637100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "69c5cad8-fde3-4676-bad7-d87b994d57de", "logId": "ead1023d-a0fb-4801-9fa1-4fb77a0d2c61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ead1023d-a0fb-4801-9fa1-4fb77a0d2c61", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682528628800, "endTime": 26682528637100}, "additional": {"logType": "info", "children": [], "durationId": "21cf0dea-5d99-4b32-b44b-5ad86c3082d5", "parent": "c8c317b9-7b9a-4699-9d50-b4af2c2a2128"}}, {"head": {"id": "6e7d22b3-4958-4491-971a-da6cf1af813b", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682528640800, "endTime": 26682537164300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "69c5cad8-fde3-4676-bad7-d87b994d57de", "logId": "c9869f6a-7d81-4266-8f88-89bce8a849b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9869f6a-7d81-4266-8f88-89bce8a849b6", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682528640800, "endTime": 26682537164300}, "additional": {"logType": "info", "children": [], "durationId": "6e7d22b3-4958-4491-971a-da6cf1af813b", "parent": "c8c317b9-7b9a-4699-9d50-b4af2c2a2128"}}, {"head": {"id": "5d3e1f3b-9aae-4e2f-8644-054526b1b3bb", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682537168400, "endTime": 26682541568000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "69c5cad8-fde3-4676-bad7-d87b994d57de", "logId": "20801766-a7a8-4a34-bbbc-7b488f07a693"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20801766-a7a8-4a34-bbbc-7b488f07a693", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682537168400, "endTime": 26682541568000}, "additional": {"logType": "info", "children": [], "durationId": "5d3e1f3b-9aae-4e2f-8644-054526b1b3bb", "parent": "c8c317b9-7b9a-4699-9d50-b4af2c2a2128"}}, {"head": {"id": "82b98a85-128a-4f3b-87d7-a05bf2a91699", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682541596100, "endTime": 26682595123100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "278d6564-9b7d-4c8e-a468-594b5529e7be", "logId": "d031ce07-c743-4000-81f5-42e47b5b3197"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d031ce07-c743-4000-81f5-42e47b5b3197", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682541596100, "endTime": 26682595123100}, "additional": {"logType": "info", "children": [], "durationId": "82b98a85-128a-4f3b-87d7-a05bf2a91699", "parent": "4993169c-e587-42f8-8525-a7a084126263"}}, {"head": {"id": "c6ddcc2f-b2c1-4319-9917-4a6c043f8020", "name": "default@CompileArkTS work[21] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682603102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4993169c-e587-42f8-8525-a7a084126263", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26680404860500, "endTime": 26682595308600}, "additional": {"logType": "info", "children": ["45ddaa10-feb3-448d-a2ea-2942e349c1db", "6739d67b-d842-4ac8-94df-1cd5ef5b28a0", "2ad57ec8-cc9d-4c37-9889-498b2b770c8c", "185d8e43-3848-4752-9616-210af64499a1", "c8c317b9-7b9a-4699-9d50-b4af2c2a2128", "d031ce07-c743-4000-81f5-42e47b5b3197"], "durationId": "278d6564-9b7d-4c8e-a468-594b5529e7be", "parent": "6d7b77ab-fc8e-4dec-af9d-114bfa527970"}}, {"head": {"id": "8cba9159-3a6c-4a32-b139-d97337cdad37", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682603291000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7b77ab-fc8e-4dec-af9d-114bfa527970", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26680268630200, "endTime": 26682603391300, "totalTime": 2214507500}, "additional": {"logType": "info", "children": ["4993169c-e587-42f8-8525-a7a084126263", "01416d24-5126-498d-98fa-532715ab2f05"], "durationId": "187b6eb8-0814-4c48-847c-f3ec1f3caa37"}}, {"head": {"id": "eecc54f4-85c3-433c-864a-7821fa477470", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682608925900, "endTime": 26682611171200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "78e9c3d5-26bf-4d14-ba9d-f193c292946b", "logId": "a659ff19-0674-4cfd-8280-f8453921b8fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78e9c3d5-26bf-4d14-ba9d-f193c292946b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682607518400}, "additional": {"logType": "detail", "children": [], "durationId": "eecc54f4-85c3-433c-864a-7821fa477470"}}, {"head": {"id": "5f4328eb-f177-4379-ab15-a8e790c97761", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682607902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dfecf64-589b-4f3b-9bde-1bb2f5930c90", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682608007500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f6fc78-811c-4a2f-8b4c-fd2ac5b9d4f0", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682608936300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab71db4f-55a2-40bd-ba32-c0c95db0c931", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682609137900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c579cef-ff99-408f-8101-b2b4462ab6ac", "name": "entry:default@GeneratePkgModuleJson is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682609513200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae5d28f-9ce9-42d4-b8a5-776eda99ef03", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682609609700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7e4bea4-ec2f-4c13-a8c1-fd151db19328", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682609687600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9fcdd8-821f-492a-aabe-72bc3b1c533d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682609739300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0abea19d-c312-414e-8b8e-9f1af0e55671", "name": "entry : default@GeneratePkgModuleJson cost memory 0.1217193603515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682610973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0240b67a-c74b-4a00-a01c-1584171b4020", "name": "runTaskFromQueue task cost before running: 3 s 697 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682611106200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a659ff19-0674-4cfd-8280-f8453921b8fa", "name": "Finished :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682608925900, "endTime": 26682611171200, "totalTime": 2152300}, "additional": {"logType": "info", "children": [], "durationId": "eecc54f4-85c3-433c-864a-7821fa477470"}}, {"head": {"id": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682620190200, "endTime": 26683207420700}, "additional": {"children": ["3b6c97af-bbdc-40fe-967a-e1cba353a98f", "d1ddfbb2-c9d7-448a-b8ed-002ada931845", "4c21bca8-48b5-40d9-8de4-2824bfe3cc8a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "faff977e-78e9-4236-a76c-7d7238987e30", "logId": "192a656a-7856-4799-8b71-7b2f16403356"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "faff977e-78e9-4236-a76c-7d7238987e30", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682613347600}, "additional": {"logType": "detail", "children": [], "durationId": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083"}}, {"head": {"id": "2520f2d6-3a84-4ca0-a0ee-e33899c6214d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682613846800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a496d462-8e4e-420f-999f-718eeda743fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682614006500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f82646-e2a6-4967-9204-da705f9d1bf5", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682620205400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16300f4b-05fa-49fc-92fe-455856111456", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682624151000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc3be8d-2d29-43f8-915f-9548585342c3", "name": "Incremental task entry:default@PackageHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682624288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db97668-2c1f-4d51-8d23-aabcb480a290", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682624378100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b566cb31-b595-4ed5-be39-484d2bdd760c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682624433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6c97af-bbdc-40fe-967a-e1cba353a98f", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682625085500, "endTime": 26682626279200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083", "logId": "d4703d52-2f1e-4232-bd49-78831b64f60a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bdea1ad-054b-435e-b80c-a6925812c9ac", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682626128100}, "additional": {"logType": "debug", "children": [], "durationId": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083"}}, {"head": {"id": "d4703d52-2f1e-4232-bd49-78831b64f60a", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682625085500, "endTime": 26682626279200}, "additional": {"logType": "info", "children": [], "durationId": "3b6c97af-bbdc-40fe-967a-e1cba353a98f", "parent": "192a656a-7856-4799-8b71-7b2f16403356"}}, {"head": {"id": "d1ddfbb2-c9d7-448a-b8ed-002ada931845", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682626819400, "endTime": 26682628221800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083", "logId": "7dbf721c-fe49-4e8f-b131-ec7792e853a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "316e1c04-de12-41b4-b814-86f1d0882c83", "name": "default@PackageHap work[23] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682627359200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c21bca8-48b5-40d9-8de4-2824bfe3cc8a", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682628185300, "endTime": 26683206870900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083", "logId": "91fa65fa-f62b-4240-98ae-87253b57aa24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7f852903-8ea5-4a3e-9f30-e6704641209d", "name": "default@PackageHap work[23] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682627948500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c902b472-0990-449d-8855-9e257e5c1e71", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682628022000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9e5480-ca4b-40aa-90be-8a4c5e3c660c", "name": "default@PackageHap work[23] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682628113200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5913d319-d40d-4315-828f-15298f8399c0", "name": "default@PackageHap work[23] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682628173400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dbf721c-fe49-4e8f-b131-ec7792e853a3", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682626819400, "endTime": 26682628221800}, "additional": {"logType": "info", "children": [], "durationId": "d1ddfbb2-c9d7-448a-b8ed-002ada931845", "parent": "192a656a-7856-4799-8b71-7b2f16403356"}}, {"head": {"id": "17917e51-6553-4ceb-88ee-dd1659e67fa7", "name": "entry : default@PackageHap cost memory 0.8212356567382812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682632002400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e0a0d20-c81c-48c5-b0bb-db4fb7bf5271", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683207071300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84c7b70a-a6fe-4597-a0d5-76af26e81047", "name": "default@PackageHap work[23] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683207256100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91fa65fa-f62b-4240-98ae-87253b57aa24", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26682628185300, "endTime": 26683206870900}, "additional": {"logType": "info", "children": [], "durationId": "4c21bca8-48b5-40d9-8de4-2824bfe3cc8a", "parent": "192a656a-7856-4799-8b71-7b2f16403356"}}, {"head": {"id": "192a656a-7856-4799-8b71-7b2f16403356", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26682620190200, "endTime": 26683207420700, "totalTime": 586686000}, "additional": {"logType": "info", "children": ["d4703d52-2f1e-4232-bd49-78831b64f60a", "7dbf721c-fe49-4e8f-b131-ec7792e853a3", "91fa65fa-f62b-4240-98ae-87253b57aa24"], "durationId": "2ad2f2ec-82ff-4a05-b7af-bd78adedc083"}}, {"head": {"id": "a1c90c35-e30b-4a5e-bfbf-755f369dfc09", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683215552600, "endTime": 26683217817200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "e7790a89-7882-44a4-ac5c-6ac75af35476", "logId": "eb21c907-fcf6-4e9b-a279-5968b54f2bab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7790a89-7882-44a4-ac5c-6ac75af35476", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683211309600}, "additional": {"logType": "detail", "children": [], "durationId": "a1c90c35-e30b-4a5e-bfbf-755f369dfc09"}}, {"head": {"id": "1f0936c9-f450-47a3-991f-30cf79dc8b28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683211947700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f78b291-9937-411f-85f5-89fe93bbf032", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683212104600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6f0c3b-f3e7-4a5c-b8b3-e4b712c1783f", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683215570000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9bd6ab6-d09d-4cf3-abcd-d2b6eed429ef", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683215991700}, "additional": {"logType": "warn", "children": [], "durationId": "a1c90c35-e30b-4a5e-bfbf-755f369dfc09"}}, {"head": {"id": "ca87d92b-d40f-41c6-84f3-c0450391ce42", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683216799600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c112b8bf-0f91-400d-af5a-b1e590241898", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683217090500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac153943-495e-49a5-9aec-e7c4dc193772", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683217225700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f6eab2-3f74-466b-a276-3ac82c34a9bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683217357000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cbd0045-3af7-47cb-baf5-aee9edbde2f5", "name": "entry : default@SignHap cost memory 0.1187591552734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683217632800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9784c4bd-9d64-4e09-a302-d9c868e13cb9", "name": "runTaskFromQueue task cost before running: 4 s 303 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683217743100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb21c907-fcf6-4e9b-a279-5968b54f2bab", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683215552600, "endTime": 26683217817200, "totalTime": 2165900}, "additional": {"logType": "info", "children": [], "durationId": "a1c90c35-e30b-4a5e-bfbf-755f369dfc09"}}, {"head": {"id": "996432ed-a5ba-4ff4-94dd-afe3c5e8f1cc", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683221343500, "endTime": 26683228535400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1b6b4a43-d655-4275-97ca-ee65e33d9acc", "logId": "95311d8b-171d-4885-8c1a-a613db94aa5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b6b4a43-d655-4275-97ca-ee65e33d9acc", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683219771000}, "additional": {"logType": "detail", "children": [], "durationId": "996432ed-a5ba-4ff4-94dd-afe3c5e8f1cc"}}, {"head": {"id": "ddc85e8a-be0d-4462-96bc-d2dbdd0998b1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683220294100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b576c5cc-28af-460a-909b-162b3fa62562", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683220444500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b30fe7-dbbd-4fc3-b6ca-17bc7ef1bc49", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683221357500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db8e4a71-5f18-4128-b1dc-abbf8f8f5aa8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683228070700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c44ed9a-c5c7-4b43-ad95-1d880ce5f4b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683228208900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "311efe51-1ef9-430d-95c0-d80929d32de7", "name": "entry : default@CollectDebugSymbol cost memory 0.24193572998046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683228339200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ea54e0-8afe-43b4-be88-6ebba9f4de2f", "name": "runTaskFromQueue task cost before running: 4 s 314 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683228463100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95311d8b-171d-4885-8c1a-a613db94aa5e", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683221343500, "endTime": 26683228535400, "totalTime": 7090800}, "additional": {"logType": "info", "children": [], "durationId": "996432ed-a5ba-4ff4-94dd-afe3c5e8f1cc"}}, {"head": {"id": "a70609e0-0b24-4e55-84e0-51bcac86f66c", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683230541600, "endTime": 26683230983700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "48e359b9-0bca-494f-a159-4e9627f51276", "logId": "74a43f77-ce9b-4e77-a315-baad0ae54a07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48e359b9-0bca-494f-a159-4e9627f51276", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683230468400}, "additional": {"logType": "detail", "children": [], "durationId": "a70609e0-0b24-4e55-84e0-51bcac86f66c"}}, {"head": {"id": "c3323433-5273-43ea-8ab0-c65f449a8ffa", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683230553700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1480d2-a20c-4a35-8cf3-6e2ed4d2bd7f", "name": "entry : assembleHap cost memory 0.0117340087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683230734700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a32405-1206-4553-9b6e-41aa9ca8022e", "name": "runTaskFromQueue task cost before running: 4 s 316 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683230893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a43f77-ce9b-4e77-a315-baad0ae54a07", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683230541600, "endTime": 26683230983700, "totalTime": 314600}, "additional": {"logType": "info", "children": [], "durationId": "a70609e0-0b24-4e55-84e0-51bcac86f66c"}}, {"head": {"id": "9a4627f2-f405-47b9-84ec-722c68e1a1b9", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683240931200, "endTime": 26683240965400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5214ea38-4728-4c9e-abc8-287a407ea5ee", "logId": "e0bb06cc-be12-46c9-94a3-fb66c646988c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e0bb06cc-be12-46c9-94a3-fb66c646988c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683240931200, "endTime": 26683240965400}, "additional": {"logType": "info", "children": [], "durationId": "9a4627f2-f405-47b9-84ec-722c68e1a1b9"}}, {"head": {"id": "ceb6c30b-e0b9-4068-899c-1b4d0e4eea58", "name": "BUILD SUCCESSFUL in 4 s 327 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683241018000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "ad543a7d-8503-4025-9209-5f9617b7d692", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26678914903500, "endTime": 26683241320500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 54}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ef314375-ab2a-4642-8ca0-06b39292628a", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683242183300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f96a08-4db1-403f-a93c-ccd269fd76cf", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683242532600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fb4bf73-28be-4c7a-8494-17489da9e4b9", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683242747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b52e3ed-32a0-4d4a-a96c-bb8423964470", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683242933300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c28ad7c9-7b92-4ea8-8957-8c0d5f099097", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683243116200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cdd62c-42dd-473f-b70d-55f2465c4f99", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683243217700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44906200-be91-478e-a37b-31cc64589b41", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683243484200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c15a5c6-70fb-4c4b-9b85-148f0eea9e90", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683243712300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62bca5c4-d781-43c4-96b1-74d7c35614fe", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683243927500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe69f8f-fa50-473b-9f03-2702025e82a5", "name": "Update task entry:default@PreBuild input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683244133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c636b5d5-159f-49e6-b167-036818294852", "name": "Incremental task entry:default@PreBuild post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683244512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2efd657-2fe1-4a02-868e-5e69e4004be1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683244629100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55096dc6-ec99-4ecb-9b34-87768a3474b0", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683244692400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d2c31d-cabe-462e-a5f9-d83c11c40b73", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683244749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0587a6ca-a34c-4350-83d5-aa3c6b8b4e28", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683244823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "636fae3e-3f15-4056-a25e-347f2bd82cad", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683245085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cde4fa31-8f64-48cc-bc61-156e01c5bf6b", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683245627400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95f47b5b-1dd7-44f9-9630-9b4d2b4f3e38", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683245891600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40283069-14e8-4cf9-b43b-89a0d5fc6b51", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683245967500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a10cb2-76c7-4955-ad38-14787b53b395", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683246026500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fc93d33-9ddd-4d3e-9bcf-42a50ae5977d", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683246261100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f765cdc-3dea-4fa1-af8f-5aeed03f9385", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683247357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6a71a1-84ed-4d89-8e6f-3f03c1255080", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683247788400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02176676-8ce9-43fe-8e1a-590760d3c875", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683248565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317f0718-bc5c-473b-bb96-ae358f1bbc4a", "name": "Update task entry:default@ProcessRouterMap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683249704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ef80ee-1e6a-4c66-bfda-6949e9e923c5", "name": "Update task entry:default@ProcessRouterMap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683250013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf671d80-98be-4dbb-97fc-898d41a7ec68", "name": "Update task entry:default@ProcessRouterMap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683250323200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d80edc-a3e2-41d4-a6d2-c3eca6ba3923", "name": "Update task entry:default@ProcessRouterMap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\profile\\router_map.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683250529900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88bda3e-7548-40f3-bb88-cb7a25ed6364", "name": "Update task entry:default@ProcessRouterMap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683250611800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3810b561-ce5d-4300-b0bf-13ab8548526a", "name": "Update task entry:default@ProcessRouterMap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683250803400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8be42c7-adee-40f4-86bd-fde05c2c4e94", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683251142200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10ba7b34-9435-4e0b-bc8f-be322fe6c054", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683251243000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92c90653-760b-49e6-838c-a191cccf7636", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683254990200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0158ac-9919-47d6-9e67-c1631d1a107b", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\loader-router-map.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683255492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea3a8a55-bce5-4a76-8d8a-593b61ee28cc", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683256964200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe5ce1e-752c-4939-9391-74312e88666c", "name": "Update task entry:default@GenerateLoaderJson output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683257497300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03986ad4-2d2e-4bec-9b0e-25fccb2b39fb", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683258128400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11bb2c0-1b4c-4fa2-bb99-99479c73c007", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683258775800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a37ebbb-d336-46b0-aa8a-1d9fc32c3498", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683259106400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3301f8-bad7-4e27-a818-f14bee58d642", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683259693300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "387e820d-b799-43e4-a613-82c340229f86", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683260231500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d00c3a-61d0-4aec-9abe-45823c9faced", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683261807200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e15205-6f01-47d3-a5ea-fd7a0b982287", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683262005700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ed8339-9a73-4065-bb0a-18730f49b980", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683263924600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf2aef4-cfb0-4b50-be98-c95cc6aa0cbe", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683264493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "945c7f14-560b-4058-88de-fb70bba04df4", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683265513100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f558a3d9-bb35-4380-ac68-439d5f76419b", "name": "Update task entry:default@CompileResource output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683266373200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c144f23-0be2-491a-84d5-cd3ba8e82d3b", "name": "Update task entry:default@CompileResource output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683276527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d306b43a-7320-431c-8319-b8f20108e144", "name": "Update task entry:default@CompileResource output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683276829400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243c4432-d532-472b-98d9-667b55b3bac3", "name": "Incremental task entry:default@CompileResource post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683277583100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "570713ac-ccc3-4efe-a792-db93e5ecf213", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683277923500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3436202-5dd9-4be0-90ae-e9fa698f9f8b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683282895700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "563db8ef-d79e-490f-9b6a-a837c7381955", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683283421100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a10c341-1800-495f-bd82-b013eef4ad86", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683284581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2899e575-6792-4710-8ca5-bc9c825ad5d1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683285123300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27b49ff9-fec4-4849-9aa6-e13b32125467", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683285621400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a9da608-c8dc-4c63-8af0-b559c70dd127", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683286323300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e656ab8e-8db5-40ee-b83a-090e215a59b7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683292035700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "538d05c5-7d0e-45ee-98f7-ab921a1744b7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683292359100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e76b1208-cd38-4849-9c5c-8b30f2787a4e", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683292669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3d609e-bdcb-40c9-96d0-2905630701d6", "name": "Incremental task entry:default@CompileArkTS post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683293321400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e66e6519-7793-4a51-b769-5d74d5ea9d5d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683294680200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a61f02d-c4c9-42bd-bd16-d68e92a8ea37", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683295497200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b3bf28-c660-49a2-a753-b9752e9d8493", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683298195300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e29592-431c-4efb-9a5b-d61b25dac34e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683301775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2fa410-7ed5-49c4-be72-0d4a3fbb7a14", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683302646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d148da-1aac-4c21-849f-ae1a9f794e9b", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683303604300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "222f3a7b-ce0b-4503-90ac-0fce209301d2", "name": "Incremental task entry:default@BuildJS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683304180500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1540038b-3854-4a5c-a8cc-b642aa5e2eea", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683309751500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94270ae7-6984-4ab9-af05-aaadd02e0c14", "name": "Update task entry:default@GeneratePkgModuleJson input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683310028200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcbc44b-ea28-4e32-8d1b-47b741353ccf", "name": "Update task entry:default@GeneratePkgModuleJson output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683310511500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397f0c5b-a724-4ef9-b60f-25725259f965", "name": "Incremental task entry:default@GeneratePkgModuleJson post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683311369100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1afd8f52-68cf-4ceb-a196-3715a2fb8271", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683312736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daadbdd5-8353-4255-96cb-8dc78e7a20a5", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683314592500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef5007d2-3711-474c-890a-74508aba200c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683315075600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af0b09e6-b4c6-485f-afe4-9a48c671ede1", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683325426500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a60c870-f2be-4a21-af3b-0cd0d14c15e4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683325743400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3d2cae3-7e10-409d-85b6-a27ee2bf4aee", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683325984700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20881014-2cb5-409a-8f24-ea229c26b6f4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683326566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e17c47b-39de-43e0-9bfd-ca4e520de99f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683326877200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce9a2f95-3274-490d-b967-96cf729f4148", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683327667600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6245133-cc7c-4ec1-9e6b-2f0f1dbf8251", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683328112100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e03d82-9fad-4c0f-9d92-2a17a1604b76", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683328439600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f19b11ab-fb7d-4f33-855d-1d07e5470c92", "name": "Incremental task entry:default@PackageHap post-execution cost:18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683328739900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bb81d07-4cbf-484d-9312-a6c15a0cd3d2", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683328986900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0301b5ba-0cf4-4c80-8e5e-5bd887d39cd9", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683329131300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004b5ce8-83b6-4224-98dd-5d3af3a5c2dd", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683329466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0797f63-13e3-4136-89a6-75d2e3c75d74", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683335631700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e487554-be9a-4c58-b8d2-915ccd16fd35", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683336069100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1627a9-fefb-4b95-9b17-382f05218487", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683336452000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf55fc4-dbe8-4506-83ac-b7bf1f257c85", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683336712900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}