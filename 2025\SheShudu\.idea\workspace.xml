<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="6c4fdfce-dedc-4b0d-87ca-3a9e7dd6c74a" name="Changes" comment="增加首选项配置和bug修复，onshown中不能清空矩阵数组">
      <change afterPath="$PROJECT_DIR$/../alphas/user/MutiOpera.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../alphas/user/MutiOpera2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../alphas/user/MutiOpera3.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/entry/src/main/ets/view/ShuduGame.ets" beforeDir="false" afterPath="$PROJECT_DIR$/entry/src/main/ets/view/ShuduGame.ets" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/User/Alpha Machine.ipynb" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/user/Alpha Machine.ipynb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/User/machine_lib.py" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/user/machine_lib.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/autosumit.py" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/autosumit.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/blacklist.txt" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/blacklist.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/brain_credentials.txt" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/brain_credentials.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/sim_queue.csv" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/sim_queue.csv" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/simulation.log" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/simulation.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../alphas/world5.0.py" beforeDir="false" afterPath="$PROJECT_DIR$/../alphas/world5.0.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="ArkTS File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="HiLogStateProjectLevelPreference">
    <panelStates>
      <hilogPanelState>
        <option name="logPanelType" value="ONLINE" />
        <option name="processName" value="com.atomicservice.6917576098804821135" />
      </hilogPanelState>
    </panelStates>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2yZInVcGs7yK1NfZxA55gBp8TMk" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "127.0.0.1:5555": "5378869661750759170126",
    "127.0.0.1:5557": "5370972391750759420239",
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "Application.entry.executor": "Run",
    "MODULE_HAP_TIME": "{\"127.0.0.1:5557entry\":\"2025-06-24T10:03:18.712497300Z\",\"127.0.0.1:5555entry\":\"2025-06-24T09:59:08.756497100Z\"}",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ace.nodejs.version": "18.20.1",
    "last_opened_file_path": "D:/yysrc/sourcecode/mrhp-sharedoc/2025/SheShudu/entry/src/main/resources/base/media"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\resources\base\media" />
      <recent name="D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\resources\rawfile" />
      <recent name="D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets\util" />
      <recent name="D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets\common" />
      <recent name="D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets\view\Personalization" />
    </key>
  </component>
  <component name="RunManager" selected="Application.entry">
    <configuration name="entry" type="HotReLoadTask" factoryName="Hot Reload">
      <MODULE_NAME />
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <HOT_RELOAD_MODULE_NAME>entry</HOT_RELOAD_MODULE_NAME>
      <method v="2">
        <option name="Build.Hvigor.HotReloadBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="entry" type="OhosDebugTask" factoryName="OpenHarmony App">
      <MODULE_NAME>entry</MODULE_NAME>
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <method v="2">
        <option name="Build.Hvigor.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="增加粒子动画效果" />
    <MESSAGE value="增加首选项配置和bug修复，onshown中不能清空矩阵数组" />
    <option name="LAST_COMMIT_MESSAGE" value="增加首选项配置和bug修复，onshown中不能清空矩阵数组" />
  </component>
</project>