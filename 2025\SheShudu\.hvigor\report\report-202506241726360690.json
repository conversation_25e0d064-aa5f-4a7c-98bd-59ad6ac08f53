{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "b400f3b4-deb9-4304-84a4-efeb51277927", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152054096400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "299c0972-d11e-42ff-b081-95e032990f2d", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152132571600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a48149-26fe-49c8-954c-38286cf70de4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152132974500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1381e99e-c76f-41ce-8e61-f5ab07effc66", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227962342200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227970886200, "endTime": 32228459556900}, "additional": {"children": ["c2d76986-ae15-4841-bba7-eb1f26e38e5d", "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "60549adc-31e2-4b2a-875b-923f31dec5cd", "31da0384-2d55-4f04-a384-8a937a2805ac", "cf5d68c7-7951-478f-bc79-02d439fd190f", "bcdd1396-37e7-4505-b5a3-9e417a4e5ac0", "ee546603-55a7-4702-9259-42635ef77d5c"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "ac61a27c-21c0-49fc-87ae-494a2438042b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2d76986-ae15-4841-bba7-eb1f26e38e5d", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227970888900, "endTime": 32227985701100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "9fd4561b-8c48-49ff-8220-9dafcd74fb92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227985787600, "endTime": 32228457781400}, "additional": {"children": ["9e49c076-38ba-4810-8e6a-c2e0e470b322", "a5bf6588-4098-4d2d-8046-b780de57f0c1", "d67fe1b8-aac7-4666-acb8-bea09f70debc", "f585d53d-cdb6-4a5e-807d-058bc3b27bf2", "000ccaed-6b98-419b-b360-a21b0d62d647", "aaac1422-ffb6-497d-96e0-de7913ae69e6", "c3b110aa-41db-453b-bdb0-2cea2f380c90", "cddf2f1d-6fd3-471b-9c09-6fcf7c3dd2c4", "280099f6-e6f9-4257-8fe4-a1d6b8d3bc82"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60549adc-31e2-4b2a-875b-923f31dec5cd", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228457811000, "endTime": 32228459543500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "6cb21eb9-9ef2-43a3-93c1-743cb2553032"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31da0384-2d55-4f04-a384-8a937a2805ac", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228459549900, "endTime": 32228459551300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "f3f8fbae-b672-4643-8c94-9268089660f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf5d68c7-7951-478f-bc79-02d439fd190f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227974218700, "endTime": 32227974264900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "aa607d54-e9a3-47f2-885c-b8a68094cb94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa607d54-e9a3-47f2-885c-b8a68094cb94", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227974218700, "endTime": 32227974264900}, "additional": {"logType": "info", "children": [], "durationId": "cf5d68c7-7951-478f-bc79-02d439fd190f", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "bcdd1396-37e7-4505-b5a3-9e417a4e5ac0", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227980173200, "endTime": 32227980208400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "cb684c3d-b27e-4bac-aeec-81c61b918fc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb684c3d-b27e-4bac-aeec-81c61b918fc2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227980173200, "endTime": 32227980208400}, "additional": {"logType": "info", "children": [], "durationId": "bcdd1396-37e7-4505-b5a3-9e417a4e5ac0", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "e8ae82dc-1240-4579-8a67-822d1f446ac7", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227980437100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3342e416-ce8e-468a-8e0c-b0ba57b0638b", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227985463800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fd4561b-8c48-49ff-8220-9dafcd74fb92", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227970888900, "endTime": 32227985701100}, "additional": {"logType": "info", "children": [], "durationId": "c2d76986-ae15-4841-bba7-eb1f26e38e5d", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "9e49c076-38ba-4810-8e6a-c2e0e470b322", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227992354300, "endTime": 32227992360500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "d172b070-fde9-4990-aacb-06aa0bd6be5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5bf6588-4098-4d2d-8046-b780de57f0c1", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227992375200, "endTime": 32227999166900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "a1c026db-f87b-4256-aa67-f768aa399650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d67fe1b8-aac7-4666-acb8-bea09f70debc", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227999205000, "endTime": 32228252524600}, "additional": {"children": ["283d0f68-29c5-4f94-b77b-faabd9899d8d", "44618e4c-86fe-4c9f-858e-aa5a8bf6d4a1", "abeab167-f056-4d95-b0c0-bf4b072a6c3e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "f6205962-715f-4923-9b95-225a097fcc28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f585d53d-cdb6-4a5e-807d-058bc3b27bf2", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228252598500, "endTime": 32228322235700}, "additional": {"children": ["c7254088-4b46-445f-82d9-6f855ec5acc3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "632081be-0309-4dc8-ba58-75f417d06678"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "000ccaed-6b98-419b-b360-a21b0d62d647", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228322242400, "endTime": 32228410445700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "5b9a1d4b-7cf1-4b26-9747-60bd9b96bd9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aaac1422-ffb6-497d-96e0-de7913ae69e6", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228411806300, "endTime": 32228439450100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "8a7cc1e1-d426-4e66-aca9-30a0eaebe46a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3b110aa-41db-453b-bdb0-2cea2f380c90", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228439479600, "endTime": 32228457430800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "f2eea9eb-1b02-4c3f-a70f-8b8d5f54c289"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cddf2f1d-6fd3-471b-9c09-6fcf7c3dd2c4", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228457453200, "endTime": 32228457624500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "87de670c-8656-4738-8d85-de050c8323de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d172b070-fde9-4990-aacb-06aa0bd6be5a", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227992354300, "endTime": 32227992360500}, "additional": {"logType": "info", "children": [], "durationId": "9e49c076-38ba-4810-8e6a-c2e0e470b322", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "a1c026db-f87b-4256-aa67-f768aa399650", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227992375200, "endTime": 32227999166900}, "additional": {"logType": "info", "children": [], "durationId": "a5bf6588-4098-4d2d-8046-b780de57f0c1", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "283d0f68-29c5-4f94-b77b-faabd9899d8d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228000992800, "endTime": 32228001011200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d67fe1b8-aac7-4666-acb8-bea09f70debc", "logId": "ff1f5c00-0da4-45b2-9e0b-1d1ad0cb0da9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff1f5c00-0da4-45b2-9e0b-1d1ad0cb0da9", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228000992800, "endTime": 32228001011200}, "additional": {"logType": "info", "children": [], "durationId": "283d0f68-29c5-4f94-b77b-faabd9899d8d", "parent": "f6205962-715f-4923-9b95-225a097fcc28"}}, {"head": {"id": "44618e4c-86fe-4c9f-858e-aa5a8bf6d4a1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228006353600, "endTime": 32228250412600}, "additional": {"children": ["2d6a3ebd-07d0-4b74-8420-a9e01e9f070a", "2f71f487-ea9a-4a9a-bb1f-60980c99678a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d67fe1b8-aac7-4666-acb8-bea09f70debc", "logId": "78d22b54-4f2b-4328-97bd-6febb9f7f6dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d6a3ebd-07d0-4b74-8420-a9e01e9f070a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228006356100, "endTime": 32228040763900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44618e4c-86fe-4c9f-858e-aa5a8bf6d4a1", "logId": "72d42b20-3d35-4cea-a05c-727bcbb330ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f71f487-ea9a-4a9a-bb1f-60980c99678a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228040789700, "endTime": 32228250379700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44618e4c-86fe-4c9f-858e-aa5a8bf6d4a1", "logId": "afa243b1-3288-4baa-98eb-e45f7a253456"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27e321b7-aa3a-4408-a889-a9de1fb7361c", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228006360900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87ea19c-38fb-4fdd-9b01-48437c5eb1a7", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228040362400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d42b20-3d35-4cea-a05c-727bcbb330ff", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228006356100, "endTime": 32228040763900}, "additional": {"logType": "info", "children": [], "durationId": "2d6a3ebd-07d0-4b74-8420-a9e01e9f070a", "parent": "78d22b54-4f2b-4328-97bd-6febb9f7f6dc"}}, {"head": {"id": "44f280ea-b0dc-4e7b-87ba-27d4451c744e", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228040815200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6911c4-994e-4404-9f70-a79b2bef90ef", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228134437700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa0f1ea-173e-417d-8c9f-1c926c7be5bb", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228134571600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048d0b60-8080-4366-805a-1272637749fc", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228134757200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ee4537-809c-4ba7-984f-4bceddc281a6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228134898500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc0bfb49-3c05-4c26-b983-529ea199aaf1", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228138430100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcca3611-d22c-45e3-81dd-a8ac8f067363", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228144641100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01616055-07bd-4eb0-93fe-57ae63ca1304", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228177360400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffc986e1-8bbe-4ad3-883a-7e1a554f8edb", "name": "Sdk init in 74 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228219056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "156f9e2c-65ba-4fc3-b48c-4710931d65a1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228219284600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 26}, "markType": "other"}}, {"head": {"id": "ce86e697-29b0-48f2-8599-a0e880b42ab1", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228219337700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 26}, "markType": "other"}}, {"head": {"id": "9aa79b9c-4a2b-4ac1-b0ae-c84e8a48e3f5", "name": "Project task initialization takes 29 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228249647800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4699e0fe-3c1f-49a2-8940-0c34e0e7a8fb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228250010300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd36efc-1e9f-4261-9eb1-8cb7f40a0745", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228250176800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e5cc53-3612-44f1-acf2-8d8d53166812", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228250283400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afa243b1-3288-4baa-98eb-e45f7a253456", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228040789700, "endTime": 32228250379700}, "additional": {"logType": "info", "children": [], "durationId": "2f71f487-ea9a-4a9a-bb1f-60980c99678a", "parent": "78d22b54-4f2b-4328-97bd-6febb9f7f6dc"}}, {"head": {"id": "78d22b54-4f2b-4328-97bd-6febb9f7f6dc", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228006353600, "endTime": 32228250412600}, "additional": {"logType": "info", "children": ["72d42b20-3d35-4cea-a05c-727bcbb330ff", "afa243b1-3288-4baa-98eb-e45f7a253456"], "durationId": "44618e4c-86fe-4c9f-858e-aa5a8bf6d4a1", "parent": "f6205962-715f-4923-9b95-225a097fcc28"}}, {"head": {"id": "abeab167-f056-4d95-b0c0-bf4b072a6c3e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228252391300, "endTime": 32228252413500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d67fe1b8-aac7-4666-acb8-bea09f70debc", "logId": "11604cec-f7ee-4ea6-bfda-b1fcc7fb3485"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11604cec-f7ee-4ea6-bfda-b1fcc7fb3485", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228252391300, "endTime": 32228252413500}, "additional": {"logType": "info", "children": [], "durationId": "abeab167-f056-4d95-b0c0-bf4b072a6c3e", "parent": "f6205962-715f-4923-9b95-225a097fcc28"}}, {"head": {"id": "f6205962-715f-4923-9b95-225a097fcc28", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227999205000, "endTime": 32228252524600}, "additional": {"logType": "info", "children": ["ff1f5c00-0da4-45b2-9e0b-1d1ad0cb0da9", "78d22b54-4f2b-4328-97bd-6febb9f7f6dc", "11604cec-f7ee-4ea6-bfda-b1fcc7fb3485"], "durationId": "d67fe1b8-aac7-4666-acb8-bea09f70debc", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "c7254088-4b46-445f-82d9-6f855ec5acc3", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228257262100, "endTime": 32228322224900}, "additional": {"children": ["0a1026d5-0e84-404a-9d8a-02b73535a1d8", "4c7febb1-a728-4782-b34a-4def5a118df3", "fe266e5b-8395-4be7-9004-2cb5becedd47"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f585d53d-cdb6-4a5e-807d-058bc3b27bf2", "logId": "62937f0e-7e7d-4698-af5b-d6ffbcc7a1f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a1026d5-0e84-404a-9d8a-02b73535a1d8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228272243400, "endTime": 32228272269500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7254088-4b46-445f-82d9-6f855ec5acc3", "logId": "c07853e7-946d-419c-b537-63a909f6602d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c07853e7-946d-419c-b537-63a909f6602d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228272243400, "endTime": 32228272269500}, "additional": {"logType": "info", "children": [], "durationId": "0a1026d5-0e84-404a-9d8a-02b73535a1d8", "parent": "62937f0e-7e7d-4698-af5b-d6ffbcc7a1f3"}}, {"head": {"id": "4c7febb1-a728-4782-b34a-4def5a118df3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228280648700, "endTime": 32228320410300}, "additional": {"children": ["386f6e27-5479-41c3-b75c-c64f08c48004", "2fce7466-d55a-4e1c-b2d3-50320fdd92b8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7254088-4b46-445f-82d9-6f855ec5acc3", "logId": "4a43d879-8e5d-440d-8f2b-d0f107303a47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "386f6e27-5479-41c3-b75c-c64f08c48004", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228280651100, "endTime": 32228294542300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c7febb1-a728-4782-b34a-4def5a118df3", "logId": "751e7ff3-50de-4c83-a43c-460c5dd9904e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fce7466-d55a-4e1c-b2d3-50320fdd92b8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228294909100, "endTime": 32228320398300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c7febb1-a728-4782-b34a-4def5a118df3", "logId": "72d43233-1bb6-48bd-81a7-8840c602b8f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bdf1abd-5676-48ac-8ea9-9e1584ea0229", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228280656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe163e83-b33d-4b92-887f-5ab58bb6cb6d", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228294351200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751e7ff3-50de-4c83-a43c-460c5dd9904e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228280651100, "endTime": 32228294542300}, "additional": {"logType": "info", "children": [], "durationId": "386f6e27-5479-41c3-b75c-c64f08c48004", "parent": "4a43d879-8e5d-440d-8f2b-d0f107303a47"}}, {"head": {"id": "03c74d56-9eed-41b1-9546-63e29f27bd49", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228294926900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ecef9d-72bc-4950-a8dd-7bda14b13c21", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228314437200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ef3517-39b9-43f5-8ddd-72643f595355", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228314567200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7b07cf0-5861-42a7-a46e-5e1d41e1d704", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228314868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "424c6ba8-642c-4542-b740-319c0e79de1d", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228315025900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b3a05e8-a3ed-43b5-8255-887e38dc5420", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228315090700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6636786-e8c5-43b8-9ff1-d78e2f104536", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228315216200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05456221-8c8b-423b-ade8-d5a219da0362", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228315280000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e76778f-bcc3-4362-9e6b-cd4e7b2ff51b", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228319884100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26343337-d047-4d58-b587-7edfda0f5772", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228320106700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add715bc-8639-4225-81ab-ca0fac7aecec", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228320271900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d7d612-e702-4642-b2bd-5b3fdb063f1a", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228320348000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d43233-1bb6-48bd-81a7-8840c602b8f2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228294909100, "endTime": 32228320398300}, "additional": {"logType": "info", "children": [], "durationId": "2fce7466-d55a-4e1c-b2d3-50320fdd92b8", "parent": "4a43d879-8e5d-440d-8f2b-d0f107303a47"}}, {"head": {"id": "4a43d879-8e5d-440d-8f2b-d0f107303a47", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228280648700, "endTime": 32228320410300}, "additional": {"logType": "info", "children": ["751e7ff3-50de-4c83-a43c-460c5dd9904e", "72d43233-1bb6-48bd-81a7-8840c602b8f2"], "durationId": "4c7febb1-a728-4782-b34a-4def5a118df3", "parent": "62937f0e-7e7d-4698-af5b-d6ffbcc7a1f3"}}, {"head": {"id": "fe266e5b-8395-4be7-9004-2cb5becedd47", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228322194400, "endTime": 32228322210600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7254088-4b46-445f-82d9-6f855ec5acc3", "logId": "b25f04ab-a13d-4951-9f8c-89cb32326b64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b25f04ab-a13d-4951-9f8c-89cb32326b64", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228322194400, "endTime": 32228322210600}, "additional": {"logType": "info", "children": [], "durationId": "fe266e5b-8395-4be7-9004-2cb5becedd47", "parent": "62937f0e-7e7d-4698-af5b-d6ffbcc7a1f3"}}, {"head": {"id": "62937f0e-7e7d-4698-af5b-d6ffbcc7a1f3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228257262100, "endTime": 32228322224900}, "additional": {"logType": "info", "children": ["c07853e7-946d-419c-b537-63a909f6602d", "4a43d879-8e5d-440d-8f2b-d0f107303a47", "b25f04ab-a13d-4951-9f8c-89cb32326b64"], "durationId": "c7254088-4b46-445f-82d9-6f855ec5acc3", "parent": "632081be-0309-4dc8-ba58-75f417d06678"}}, {"head": {"id": "632081be-0309-4dc8-ba58-75f417d06678", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228252598500, "endTime": 32228322235700}, "additional": {"logType": "info", "children": ["62937f0e-7e7d-4698-af5b-d6ffbcc7a1f3"], "durationId": "f585d53d-cdb6-4a5e-807d-058bc3b27bf2", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "aabd0365-fb47-4636-b88c-70727288b899", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228349158600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fa5b7e2-85b1-4e43-afd6-1ef97d04c697", "name": "hvigorfile, resolve hvigorfile dependencies in 89 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228410318400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9a1d4b-7cf1-4b26-9747-60bd9b96bd9d", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228322242400, "endTime": 32228410445700}, "additional": {"logType": "info", "children": [], "durationId": "000ccaed-6b98-419b-b360-a21b0d62d647", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "280099f6-e6f9-4257-8fe4-a1d6b8d3bc82", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228411362100, "endTime": 32228411788400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "logId": "142bae75-c125-4ae2-be14-64b0d4163457"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "380c5f48-4515-429e-8d98-2276def3f290", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228411408000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142bae75-c125-4ae2-be14-64b0d4163457", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228411362100, "endTime": 32228411788400}, "additional": {"logType": "info", "children": [], "durationId": "280099f6-e6f9-4257-8fe4-a1d6b8d3bc82", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "3bbe3944-6ed1-4c06-8ebe-88a2badb8fbf", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228412904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08474ffb-7b14-44f1-aefa-1ca6f90ef7fe", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228438621200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a7cc1e1-d426-4e66-aca9-30a0eaebe46a", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228411806300, "endTime": 32228439450100}, "additional": {"logType": "info", "children": [], "durationId": "aaac1422-ffb6-497d-96e0-de7913ae69e6", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "22d2774c-fa41-4dab-a773-77515336c818", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228446175900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d00a3a8-5a0b-4f70-987d-3f455b48b116", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228446417400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5536d97-21a2-4996-9649-578f938e71fa", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228452037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6d1ef1e-1860-46db-8afd-8b42f140013e", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228452226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2eea9eb-1b02-4c3f-a70f-8b8d5f54c289", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228439479600, "endTime": 32228457430800}, "additional": {"logType": "info", "children": [], "durationId": "c3b110aa-41db-453b-bdb0-2cea2f380c90", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "c9969fb0-4b19-417b-8fce-7292dac28f75", "name": "Configuration phase cost:466 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228457479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87de670c-8656-4738-8d85-de050c8323de", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228457453200, "endTime": 32228457624500}, "additional": {"logType": "info", "children": [], "durationId": "cddf2f1d-6fd3-471b-9c09-6fcf7c3dd2c4", "parent": "9ff29a22-10df-4917-a6c0-eaaf30132f58"}}, {"head": {"id": "9ff29a22-10df-4917-a6c0-eaaf30132f58", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227985787600, "endTime": 32228457781400}, "additional": {"logType": "info", "children": ["d172b070-fde9-4990-aacb-06aa0bd6be5a", "a1c026db-f87b-4256-aa67-f768aa399650", "f6205962-715f-4923-9b95-225a097fcc28", "632081be-0309-4dc8-ba58-75f417d06678", "5b9a1d4b-7cf1-4b26-9747-60bd9b96bd9d", "8a7cc1e1-d426-4e66-aca9-30a0eaebe46a", "f2eea9eb-1b02-4c3f-a70f-8b8d5f54c289", "87de670c-8656-4738-8d85-de050c8323de", "142bae75-c125-4ae2-be14-64b0d4163457"], "durationId": "f6fd9174-55cc-48d8-9f5a-d911f235f8fb", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "ee546603-55a7-4702-9259-42635ef77d5c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228459461900, "endTime": 32228459517000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1", "logId": "c9bd0fc2-bafe-4687-b21e-61ffcd9ffeac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9bd0fc2-bafe-4687-b21e-61ffcd9ffeac", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228459461900, "endTime": 32228459517000}, "additional": {"logType": "info", "children": [], "durationId": "ee546603-55a7-4702-9259-42635ef77d5c", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "6cb21eb9-9ef2-43a3-93c1-743cb2553032", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228457811000, "endTime": 32228459543500}, "additional": {"logType": "info", "children": [], "durationId": "60549adc-31e2-4b2a-875b-923f31dec5cd", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "f3f8fbae-b672-4643-8c94-9268089660f7", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228459549900, "endTime": 32228459551300}, "additional": {"logType": "info", "children": [], "durationId": "31da0384-2d55-4f04-a384-8a937a2805ac", "parent": "ac61a27c-21c0-49fc-87ae-494a2438042b"}}, {"head": {"id": "ac61a27c-21c0-49fc-87ae-494a2438042b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227970886200, "endTime": 32228459556900}, "additional": {"logType": "info", "children": ["9fd4561b-8c48-49ff-8220-9dafcd74fb92", "9ff29a22-10df-4917-a6c0-eaaf30132f58", "6cb21eb9-9ef2-43a3-93c1-743cb2553032", "f3f8fbae-b672-4643-8c94-9268089660f7", "aa607d54-e9a3-47f2-885c-b8a68094cb94", "cb684c3d-b27e-4bac-aeec-81c61b918fc2", "c9bd0fc2-bafe-4687-b21e-61ffcd9ffeac"], "durationId": "4e8f7ec7-ae44-419d-8e30-1b2b8f9b88e1"}}, {"head": {"id": "53bb4a8a-b175-454a-94f6-91ea238414ea", "name": "Configuration task cost before running: 494 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228460186800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a9b452-fe7c-47bf-b0dd-64b2e1a791ba", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228466864800, "endTime": 32228480393200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "91949021-9444-4d47-98c9-f4156f6395bf", "logId": "0b0b902d-5642-402a-9665-9f0465667bb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91949021-9444-4d47-98c9-f4156f6395bf", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228461895800}, "additional": {"logType": "detail", "children": [], "durationId": "72a9b452-fe7c-47bf-b0dd-64b2e1a791ba"}}, {"head": {"id": "aeb6779c-0b08-4aef-96fb-f0158d8c8cf0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228462389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75dcebeb-058b-4d56-a9f3-52279dc5f6bd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228462544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "319ade76-64ca-4428-81ad-8419128bb965", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228466882600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c188854-a36f-474a-879a-dc2e5a447b66", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228480164200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a44651b-2391-4910-8aa7-3535c53d2d3f", "name": "entry : default@PreBuild cost memory 0.4855499267578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228480319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b0b902d-5642-402a-9665-9f0465667bb6", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228466864800, "endTime": 32228480393200}, "additional": {"logType": "info", "children": [], "durationId": "72a9b452-fe7c-47bf-b0dd-64b2e1a791ba"}}, {"head": {"id": "1d0ecd06-eba7-4648-bf18-bdb649f4da9c", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228488285200, "endTime": 32228491132400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a804302c-da59-4ffb-90e0-bacb2f5ac41c", "logId": "892d1e92-911a-48e8-af9b-1beb0082faad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a804302c-da59-4ffb-90e0-bacb2f5ac41c", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228485893500}, "additional": {"logType": "detail", "children": [], "durationId": "1d0ecd06-eba7-4648-bf18-bdb649f4da9c"}}, {"head": {"id": "5ef6756e-1e56-4758-ab65-97ef80130109", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228486578300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3604d994-a2a5-4b7a-b5b7-6695b5885660", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228486756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9830670f-90fc-4be9-8dcb-c98f9a5fff11", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228488304200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cd4c249-6cdd-42d8-89b6-e9b3823d9d3a", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228489553700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "679b3a86-8de3-45b5-8a84-eaa2a0a292a3", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228490550500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ab7cd8-c660-42a4-bf5d-56478bccd566", "name": "entry : default@GenerateMetadata cost memory 0.09342193603515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228490845400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "892d1e92-911a-48e8-af9b-1beb0082faad", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228488285200, "endTime": 32228491132400}, "additional": {"logType": "info", "children": [], "durationId": "1d0ecd06-eba7-4648-bf18-bdb649f4da9c"}}, {"head": {"id": "0869a0c2-dd50-47c9-9952-c12d47e1e41a", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496268300, "endTime": 32228497036300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "501adac0-d6b2-4dd5-9063-45051a37f678", "logId": "a93b4d43-f137-4882-9fc9-ef1d345ca6a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "501adac0-d6b2-4dd5-9063-45051a37f678", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228495069800}, "additional": {"logType": "detail", "children": [], "durationId": "0869a0c2-dd50-47c9-9952-c12d47e1e41a"}}, {"head": {"id": "0af0629d-c4f3-4505-b7a3-bab0a727b58a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228495777800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e2eab3-1a0a-41b2-ad3c-30042d93e18d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228495908400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4026748f-54ae-4c08-b9c5-173a124696ff", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496292800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b195426d-37c3-4d40-8e52-0340b3b81057", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496436900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff120914-6fde-4be3-bd9b-676088814f4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496703800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fe2acb8-cfbc-4559-abb6-4f97d338ab9b", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496825500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d31ae83e-9399-40e9-a37b-20c8fb1bb39e", "name": "runTaskFromQueue task cost before running: 531 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496947300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93b4d43-f137-4882-9fc9-ef1d345ca6a2", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228496268300, "endTime": 32228497036300, "totalTime": 656100}, "additional": {"logType": "info", "children": [], "durationId": "0869a0c2-dd50-47c9-9952-c12d47e1e41a"}}, {"head": {"id": "437869c3-a3a2-4ecd-96a9-5d73088d849c", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228503841700, "endTime": 32228508041000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7825a116-f042-4531-8257-b9269003b33f", "logId": "7b6b804a-624d-43bd-8a9b-164fadbe7e75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7825a116-f042-4531-8257-b9269003b33f", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228501517700}, "additional": {"logType": "detail", "children": [], "durationId": "437869c3-a3a2-4ecd-96a9-5d73088d849c"}}, {"head": {"id": "66c0df67-ba81-43fd-bbf3-9283a736cbd9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228502223200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8909e29d-d9f9-464d-980c-f846d6e34af3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228502414400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b39576-9d4a-472b-9675-fca460151d4a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228503856000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3148aa1-b4c0-4316-ba0c-90442fbc5931", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228507792800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0a1f0a-cd25-42cf-af07-8f3ce8b4ee6c", "name": "entry : default@MergeProfile cost memory 0.105133056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228507961000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b6b804a-624d-43bd-8a9b-164fadbe7e75", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228503841700, "endTime": 32228508041000}, "additional": {"logType": "info", "children": [], "durationId": "437869c3-a3a2-4ecd-96a9-5d73088d849c"}}, {"head": {"id": "62da0c97-fa22-4498-b7c8-7453ff00ee85", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228511653800, "endTime": 32228514798500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dc1e72e4-1ddb-43d8-bdc2-51a59ca1ab5e", "logId": "53170b4d-68fc-421f-9f31-3e0b3d69c06c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc1e72e4-1ddb-43d8-bdc2-51a59ca1ab5e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228510216000}, "additional": {"logType": "detail", "children": [], "durationId": "62da0c97-fa22-4498-b7c8-7453ff00ee85"}}, {"head": {"id": "37e65c2c-4c28-4348-a51a-fdd43836d9fe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228510768400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b2664fb-77dd-4783-bb2b-ea92680b075c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228510901700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7044f2-7ac4-495d-a1cb-5888758df77b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228511666500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ab3a79-ef54-4037-949a-b710ba3eef03", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228512590900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab7b8be-4f03-4487-92bc-ab9f19839cb8", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228514426700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e2197ce-e9ff-4630-abd8-05c450129522", "name": "entry : default@CreateBuildProfile cost memory 0.1016845703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228514583400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53170b4d-68fc-421f-9f31-3e0b3d69c06c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228511653800, "endTime": 32228514798500}, "additional": {"logType": "info", "children": [], "durationId": "62da0c97-fa22-4498-b7c8-7453ff00ee85"}}, {"head": {"id": "a2770b49-6190-4c20-9f71-6b77cd02f1f6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228521982400, "endTime": 32228523039500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b386cdff-e8d6-48ec-b599-498f60500ab2", "logId": "369a7f25-de21-42ea-86f4-303834869c39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b386cdff-e8d6-48ec-b599-498f60500ab2", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228519415700}, "additional": {"logType": "detail", "children": [], "durationId": "a2770b49-6190-4c20-9f71-6b77cd02f1f6"}}, {"head": {"id": "05dd1ca6-d3ad-4936-b1c3-afa7de5ab9ad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228520381700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf59db1f-8b2b-4eb6-94b9-8195e0c67593", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228520526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7b2758-df5e-4d05-bc13-07786525f451", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228521995800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb1170c-09fb-4cf1-b54c-ba4c74d5d911", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228522187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eeaaa12-290f-4132-b6ff-d7358d1476ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228522256700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab930ac6-083d-46c7-bd3e-f783f3450c9a", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228522530100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68e325cc-ced9-4019-9774-5fbc4526da63", "name": "runTaskFromQueue task cost before running: 556 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228522833700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "369a7f25-de21-42ea-86f4-303834869c39", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228521982400, "endTime": 32228523039500, "totalTime": 823900}, "additional": {"logType": "info", "children": [], "durationId": "a2770b49-6190-4c20-9f71-6b77cd02f1f6"}}, {"head": {"id": "79b4687b-8641-40b0-9f9d-9570a6d27877", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228533047200, "endTime": 32228534098400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c1adc7e2-b525-4850-8771-11217f40197a", "logId": "a740f348-4432-494b-a4f5-19ac217b2553"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1adc7e2-b525-4850-8771-11217f40197a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228527089400}, "additional": {"logType": "detail", "children": [], "durationId": "79b4687b-8641-40b0-9f9d-9570a6d27877"}}, {"head": {"id": "f198ceda-eb1f-43f0-9e1b-73f3503ecb22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228527518000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8320d8-3ed4-41aa-b7cd-c99a51f17fa9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228527642300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915ddd92-7d02-4978-8ac0-4653438beabb", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228533061800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994155af-502d-47a3-99bf-a7a20b392531", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228533382900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4dd0446-e657-4ee5-a93a-ecca4ffe1d47", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0387725830078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228533869700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e22326-7064-42cb-81a3-8aab9f2c040e", "name": "runTaskFromQueue task cost before running: 568 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228534026600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a740f348-4432-494b-a4f5-19ac217b2553", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228533047200, "endTime": 32228534098400, "totalTime": 948200}, "additional": {"logType": "info", "children": [], "durationId": "79b4687b-8641-40b0-9f9d-9570a6d27877"}}, {"head": {"id": "cbbaec63-a1f4-4ed7-8794-069a8ef82387", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228538593900, "endTime": 32228540800000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "a68f274d-8165-41e2-bdd0-4c9dd0028964", "logId": "b00dd3c3-21f8-4ed6-9325-98702be89723"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a68f274d-8165-41e2-bdd0-4c9dd0028964", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228536239600}, "additional": {"logType": "detail", "children": [], "durationId": "cbbaec63-a1f4-4ed7-8794-069a8ef82387"}}, {"head": {"id": "9e8fb6b3-e6b3-4ef9-b758-bcd7afc41dcd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228536599100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0e24fc-c06c-45a0-9c41-8a21b5fcbd16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228536751200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cd434c2-aa3b-43cd-80ab-87a980491609", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228538610400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "009e0220-be39-4c01-8eae-256f22d2544c", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228540167400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89091c71-683b-4659-a4a3-ac77f28c5ced", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228540327100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd458ce8-1b17-4da2-95a6-ad90c8e58631", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228540433400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a08827-e4d1-4ccc-811d-c13101a5a832", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228540486300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de3eec66-6708-4066-8be5-78b1ccd6f6ea", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1180572509765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228540557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f361d82-8bd2-42a6-8579-015effdff5bb", "name": "runTaskFromQueue task cost before running: 574 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228540733300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00dd3c3-21f8-4ed6-9325-98702be89723", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228538593900, "endTime": 32228540800000, "totalTime": 2118000}, "additional": {"logType": "info", "children": [], "durationId": "cbbaec63-a1f4-4ed7-8794-069a8ef82387"}}, {"head": {"id": "205f7e19-2cd5-4da6-bcc2-be61289cd807", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228544709700, "endTime": 32228545277600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "124ff9d2-5c64-4a25-b82b-2f0a310094fe", "logId": "77ea1819-5b6b-4cc0-ac35-e2cb528361bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "124ff9d2-5c64-4a25-b82b-2f0a310094fe", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228543234700}, "additional": {"logType": "detail", "children": [], "durationId": "205f7e19-2cd5-4da6-bcc2-be61289cd807"}}, {"head": {"id": "5b95930e-c30b-4c2a-af4f-5f800bea063b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228543700600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0429e65-e17d-4494-b4b1-f8173d145d38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228543886700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9b596d1-1eea-4b77-8546-05e1e3ab64c7", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228544726800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f773f3-d577-4bad-9458-46cb9297feb4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228544907500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bd1ee0-e3a5-4a0f-a3e0-d72ff54aa76b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228545012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a339d1-5765-40ee-b919-2f4d1adfcf16", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228545103100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31600859-4754-41fd-8a4d-240b789e9257", "name": "runTaskFromQueue task cost before running: 579 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228545221200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77ea1819-5b6b-4cc0-ac35-e2cb528361bc", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228544709700, "endTime": 32228545277600, "totalTime": 490600}, "additional": {"logType": "info", "children": [], "durationId": "205f7e19-2cd5-4da6-bcc2-be61289cd807"}}, {"head": {"id": "4e1b223f-ac63-4954-9919-8c07df3752ef", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228548205800, "endTime": 32228552899100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6508be38-9a7b-413d-85d6-573531c36519", "logId": "0ebaa1be-7f68-4673-a5d8-c9c3a6a86011"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6508be38-9a7b-413d-85d6-573531c36519", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228546989600}, "additional": {"logType": "detail", "children": [], "durationId": "4e1b223f-ac63-4954-9919-8c07df3752ef"}}, {"head": {"id": "5fa2b985-83e9-4c45-8394-7fa9803c3978", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228547381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5118241-c8a5-4d42-ae8b-3f5b202b73ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228547479800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b5710d-2ca9-409f-9015-6d9f8c82aae7", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228548218500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147a4607-f28a-4f14-8352-af4220b82b93", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228552659100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7b116b7-87a8-48ef-bc27-ec6651b40677", "name": "entry : default@MakePackInfo cost memory 0.1386871337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228552807200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ebaa1be-7f68-4673-a5d8-c9c3a6a86011", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228548205800, "endTime": 32228552899100}, "additional": {"logType": "info", "children": [], "durationId": "4e1b223f-ac63-4954-9919-8c07df3752ef"}}, {"head": {"id": "e6feedf5-fec9-432e-9b57-fb49b1a32902", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228559330900, "endTime": 32228563410100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "7270fd18-a2be-4361-8f39-c2282fe810dd", "logId": "c33038e4-1008-4d67-ad89-44691e66dde6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7270fd18-a2be-4361-8f39-c2282fe810dd", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228556778500}, "additional": {"logType": "detail", "children": [], "durationId": "e6feedf5-fec9-432e-9b57-fb49b1a32902"}}, {"head": {"id": "df88c80c-3b23-4308-b6cb-4b9ea078002a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228557130400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a165f5a-6ce3-45c4-929d-36dade0a9054", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228557230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3ec618e-a17a-423c-b231-79c43de926c7", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228559348300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2daad39-7c9a-4c5e-a3c6-034fab214ebc", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228559555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63fee094-147a-488d-a679-0cd704f43472", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228561045400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cffe5d35-40f2-40d1-b65c-3a45dc2173df", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228562902700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4306929-859d-4076-a531-d0076f3fcaa6", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228563030700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af14369-4867-4909-8dcb-bbf38504b2ee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228563132200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69fc841e-d424-4046-8e79-91721a8bb24a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228563188200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c3161c-ff4c-4dc3-ab36-07ed3b2e053b", "name": "entry : default@SyscapTransform cost memory 0.15142822265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228563263500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2dd3187-8520-4185-8256-cfb9c6da7d50", "name": "runTaskFromQueue task cost before running: 597 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228563333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c33038e4-1008-4d67-ad89-44691e66dde6", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228559330900, "endTime": 32228563410100, "totalTime": 3989600}, "additional": {"logType": "info", "children": [], "durationId": "e6feedf5-fec9-432e-9b57-fb49b1a32902"}}, {"head": {"id": "aea0551f-0137-47ab-bc28-4d89247a7562", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228568960000, "endTime": 32228571506600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1c9d0856-23b8-4154-861b-b686ba4f8d11", "logId": "925a4bd8-4bb0-4560-b0f8-5d2ba7b5c943"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c9d0856-23b8-4154-861b-b686ba4f8d11", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228566558900}, "additional": {"logType": "detail", "children": [], "durationId": "aea0551f-0137-47ab-bc28-4d89247a7562"}}, {"head": {"id": "f2985c7f-02ea-46a2-af64-7e4a8720e062", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228567259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c386b4-03e2-47f2-9d1b-7a3cb0e0ec6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228567370600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8444a56-f7cf-4ef3-a854-1d58df5722c3", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228568972100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c91e629a-ace4-49f7-8fb2-997ec23bf3bb", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228571011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a73c4ab0-39c8-4d62-81d2-3634ce5e79c8", "name": "entry : default@ProcessProfile cost memory 0.0602874755859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228571403800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "925a4bd8-4bb0-4560-b0f8-5d2ba7b5c943", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228568960000, "endTime": 32228571506600}, "additional": {"logType": "info", "children": [], "durationId": "aea0551f-0137-47ab-bc28-4d89247a7562"}}, {"head": {"id": "da557ea9-bfae-471b-8d52-830a72fdd330", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228575926700, "endTime": 32228580348700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3edfa6c8-baf3-4653-9f7a-b2df1f384bfc", "logId": "ed8c36ef-d67e-49fe-8041-3b3311226b61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3edfa6c8-baf3-4653-9f7a-b2df1f384bfc", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228573671800}, "additional": {"logType": "detail", "children": [], "durationId": "da557ea9-bfae-471b-8d52-830a72fdd330"}}, {"head": {"id": "8952a63d-a4db-4c1c-b39e-940ddf3b0280", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228574072500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20959f1-5676-4c57-a1a2-12bb03f84050", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228574179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c8988ed-efd0-44d6-97f4-be05168b93cc", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228575938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6044ced-39d4-48f9-9fe0-21c2f04efe7c", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228580066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56cbabf6-895b-4b1b-9063-498f193d4a1c", "name": "entry : default@ProcessRouterMap cost memory 0.20146942138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228580257000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed8c36ef-d67e-49fe-8041-3b3311226b61", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228575926700, "endTime": 32228580348700}, "additional": {"logType": "info", "children": [], "durationId": "da557ea9-bfae-471b-8d52-830a72fdd330"}}, {"head": {"id": "7f61d390-81d8-46a5-95b5-dcee112b519d", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228586199000, "endTime": 32228587326200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3a5f9dea-6239-4ca9-8dab-59075f1bb89f", "logId": "ffc8758d-8d1e-4e7f-8e1f-49cb8c536fb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a5f9dea-6239-4ca9-8dab-59075f1bb89f", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228584363100}, "additional": {"logType": "detail", "children": [], "durationId": "7f61d390-81d8-46a5-95b5-dcee112b519d"}}, {"head": {"id": "8cb4fae6-aa11-47dd-ad8b-4a1dd1021f6e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228585213500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09f6142-11b7-4753-adfb-1bbd0f6865bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228585364100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07580aed-4e18-49a5-921a-6d1b2dba1697", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228586209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e28f67-8135-4ebf-93dd-43712b55438e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228586395200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be1c3891-c93e-488b-bd40-6c6ebbce212c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228586471900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd60749-6a86-4288-a188-e4a03ef5f08c", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228587159100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2875412e-a86e-4d96-97f3-c019cff59fbe", "name": "runTaskFromQueue task cost before running: 621 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228587264900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffc8758d-8d1e-4e7f-8e1f-49cb8c536fb3", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228586199000, "endTime": 32228587326200, "totalTime": 1046200}, "additional": {"logType": "info", "children": [], "durationId": "7f61d390-81d8-46a5-95b5-dcee112b519d"}}, {"head": {"id": "19654aaf-2ea0-419e-abad-33516c752dc6", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228595575700, "endTime": 32228603347000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "39372d25-6c10-4fc5-a815-db5f2b748763", "logId": "007aeca9-e6d6-469a-b36a-42713cfd53a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39372d25-6c10-4fc5-a815-db5f2b748763", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228590907600}, "additional": {"logType": "detail", "children": [], "durationId": "19654aaf-2ea0-419e-abad-33516c752dc6"}}, {"head": {"id": "f17a92c3-d855-4ca1-a9c1-ea1764ebe327", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228591475800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bd274a3-f25a-4eb7-bd77-b36abd1c3311", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228591748600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4fac0e-bdf9-4f54-9595-9bc20beaf6eb", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228593728700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc11382-ce72-428a-894c-c00d1c8814e9", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228597406900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2567cb41-0a89-4e81-8011-0445da51305a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228600436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a0e54bc-aaa5-41ea-ad25-eccaa91fd1dc", "name": "entry : default@ProcessResource cost memory 0.20795440673828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228600591700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007aeca9-e6d6-469a-b36a-42713cfd53a6", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228595575700, "endTime": 32228603347000}, "additional": {"logType": "info", "children": [], "durationId": "19654aaf-2ea0-419e-abad-33516c752dc6"}}, {"head": {"id": "a52e4f1c-190e-408b-962e-9f3f869a756b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228613078000, "endTime": 32228629178000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "766675b9-4e9a-43d0-9335-00bb1fcbedf6", "logId": "a17d26ef-e1a0-4365-9fde-e8b186e90952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "766675b9-4e9a-43d0-9335-00bb1fcbedf6", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228608881300}, "additional": {"logType": "detail", "children": [], "durationId": "a52e4f1c-190e-408b-962e-9f3f869a756b"}}, {"head": {"id": "c097bbd3-4a6b-4193-9e23-66f9b0d0c714", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228609460700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fa29c4a-186d-41e8-b7e5-cf22975965db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228609604000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd1c97f-95e3-4913-b7ff-29a67be164fa", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228613091200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a607e603-25db-4eb7-9a53-1341fbf599a4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228628702800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1780fe22-19cf-4b10-ae9e-c8b2b06805a8", "name": "entry : default@GenerateLoaderJson cost memory 0.7683258056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228628957400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a17d26ef-e1a0-4365-9fde-e8b186e90952", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228613078000, "endTime": 32228629178000}, "additional": {"logType": "info", "children": [], "durationId": "a52e4f1c-190e-408b-962e-9f3f869a756b"}}, {"head": {"id": "ae06c003-20f8-42f5-8350-7adb5bf2f33d", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228642522700, "endTime": 32228646441000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0f54bb1a-a524-4947-8d94-c5973a10b2f6", "logId": "7b6a8bba-6eb2-4a51-bd15-b55e18fde41b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f54bb1a-a524-4947-8d94-c5973a10b2f6", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228641291900}, "additional": {"logType": "detail", "children": [], "durationId": "ae06c003-20f8-42f5-8350-7adb5bf2f33d"}}, {"head": {"id": "9cd461f9-ae10-4676-83d6-37b52869c5ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228641656100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5421600a-ee69-4182-94ee-0013892b1055", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228641760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40157695-82b1-4ec5-8436-d303a97d74a4", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228642536800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2cb4334-6fe7-4610-a9c6-94c3e633d5c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228645312300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1296deac-fc2b-4f87-bbfe-c87dc870c29b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228645418100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b63ad39-dae1-4b30-bdde-7d2eef92cf46", "name": "entry : default@ProcessLibs cost memory 0.12532806396484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228646259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd930e8-8ce4-4858-9d40-20e39c06452d", "name": "runTaskFromQueue task cost before running: 680 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228646377600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b6a8bba-6eb2-4a51-bd15-b55e18fde41b", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228642522700, "endTime": 32228646441000, "totalTime": 3831500}, "additional": {"logType": "info", "children": [], "durationId": "ae06c003-20f8-42f5-8350-7adb5bf2f33d"}}, {"head": {"id": "e434e3db-5f51-4a43-baa2-f1caf0c351ec", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228653261800, "endTime": 32228677161200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "54390579-61a5-481f-a231-8c35e727e8d1", "logId": "154cc83e-5f21-48f4-bd2f-f83263aae47d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54390579-61a5-481f-a231-8c35e727e8d1", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228648677700}, "additional": {"logType": "detail", "children": [], "durationId": "e434e3db-5f51-4a43-baa2-f1caf0c351ec"}}, {"head": {"id": "315b10d1-9afc-486e-9e89-858145334d57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228648994900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "497c6ad9-0f71-4b0e-99c0-0206181951ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228649087500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d49f63f6-310a-42b7-a736-d640d7cbe073", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228650371300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e57514-d3d4-47e2-94e9-c39baba460fa", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228653292900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d906ac-c218-4210-9ed5-bf74ca593390", "name": "Incremental task entry:default@CompileResource pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228676908500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c85675-ea99-4ce9-a7dc-6e593996a2c2", "name": "entry : default@CompileResource cost memory -4.1702423095703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228677038200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "154cc83e-5f21-48f4-bd2f-f83263aae47d", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228653261800, "endTime": 32228677161200}, "additional": {"logType": "info", "children": [], "durationId": "e434e3db-5f51-4a43-baa2-f1caf0c351ec"}}, {"head": {"id": "da4e7a2c-930c-4cb8-8312-42d60d161427", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228684532000, "endTime": 32228688041600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6ddc17cd-21fb-4d15-bbde-d8c0494f927b", "logId": "da3662de-dcdf-445a-bc48-61ebe794ba53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ddc17cd-21fb-4d15-bbde-d8c0494f927b", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228679956400}, "additional": {"logType": "detail", "children": [], "durationId": "da4e7a2c-930c-4cb8-8312-42d60d161427"}}, {"head": {"id": "d56907ca-2b08-4032-a693-715588bca200", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228680405500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48579df9-6842-42f3-ad9d-1e07559ea6b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228680514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b7ad6f0-6e05-424a-9299-62ad28c622d2", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228684548400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03881499-958e-4b1d-97f8-4796d88462db", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228685310800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d732744-097e-446d-a82f-bd1ca84b5f8a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228687497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8759030d-d1a7-47a2-b51f-c1cbe79f801c", "name": "entry : default@DoNativeStrip cost memory 0.0748748779296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228687661900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da3662de-dcdf-445a-bc48-61ebe794ba53", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228684532000, "endTime": 32228688041600}, "additional": {"logType": "info", "children": [], "durationId": "da4e7a2c-930c-4cb8-8312-42d60d161427"}}, {"head": {"id": "5b3d3817-4be3-4f36-add5-76ccfa3d6957", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228696465900, "endTime": 32228727034200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "9df9a09a-8b9f-4a32-a7b6-32514051a936", "logId": "665d4f6b-77c5-4c73-bdaa-35dcad72e8b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9df9a09a-8b9f-4a32-a7b6-32514051a936", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228691320700}, "additional": {"logType": "detail", "children": [], "durationId": "5b3d3817-4be3-4f36-add5-76ccfa3d6957"}}, {"head": {"id": "29f3da5d-552b-4537-8fad-db212a69dd4a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228691820900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1deea07f-b71a-4841-ae47-639e49306a2b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228691939300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd560be-5d5b-4f20-8053-6d783e0bfe6b", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228696477500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca31c7bd-fdd4-4884-b226-a8b4917518f5", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228726827300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ede58b9-bca0-46f9-ac74-57a1d934c487", "name": "entry : default@CompileArkTS cost memory 0.8929901123046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228726968200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "665d4f6b-77c5-4c73-bdaa-35dcad72e8b3", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228696465900, "endTime": 32228727034200}, "additional": {"logType": "info", "children": [], "durationId": "5b3d3817-4be3-4f36-add5-76ccfa3d6957"}}, {"head": {"id": "1a5bdbe6-2652-4562-a70e-4de9b3e6f1fd", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228749173800, "endTime": 32228760043000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "935c9acf-3c2d-4613-84f6-161769f5295e", "logId": "74e1e4cf-2e02-4a08-b16d-2ce88356c40b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "935c9acf-3c2d-4613-84f6-161769f5295e", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228733878200}, "additional": {"logType": "detail", "children": [], "durationId": "1a5bdbe6-2652-4562-a70e-4de9b3e6f1fd"}}, {"head": {"id": "40aeb6f7-411e-4ac5-bc9e-5eb3ad31ede9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228734561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0548afe3-a0c6-42e2-b175-0666f7eeb2cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228734847900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bde245b0-db7a-4cc9-a641-a565dd36702f", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228749203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e64ad7-fb88-4ed8-b45f-804e41f025b2", "name": "entry : default@BuildJS cost memory 0.12697601318359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228759829900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d72d1e02-0d0e-4b2a-abc1-255cb205b1bc", "name": "runTaskFromQueue task cost before running: 794 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228759976500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e1e4cf-2e02-4a08-b16d-2ce88356c40b", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228749173800, "endTime": 32228760043000, "totalTime": 10782500}, "additional": {"logType": "info", "children": [], "durationId": "1a5bdbe6-2652-4562-a70e-4de9b3e6f1fd"}}, {"head": {"id": "2f017abc-5fd6-48ce-a8fe-d382b5a94bef", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228768756700, "endTime": 32228770291800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d861af80-432b-4863-aa02-aef6b0a5372e", "logId": "bdb51ecf-c452-49f0-bd7b-8ea1dff87477"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d861af80-432b-4863-aa02-aef6b0a5372e", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228762738400}, "additional": {"logType": "detail", "children": [], "durationId": "2f017abc-5fd6-48ce-a8fe-d382b5a94bef"}}, {"head": {"id": "dcac704e-1ee7-4414-a266-93ae220a46eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228763643700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee2a6836-040f-42aa-b59d-03511028aaf1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228763781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a0057e-7971-454a-8478-4c942a25a078", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228768768900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98ffca9-97d8-4f95-8828-6982a8dd1fb4", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228769158000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dabcbbb8-ff34-408a-9240-60bc7b78e670", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228770101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76ce8ab1-093e-454b-ab65-f12954184c62", "name": "entry : default@CacheNativeLibs cost memory 0.08670806884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228770223000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb51ecf-c452-49f0-bd7b-8ea1dff87477", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228768756700, "endTime": 32228770291800}, "additional": {"logType": "info", "children": [], "durationId": "2f017abc-5fd6-48ce-a8fe-d382b5a94bef"}}, {"head": {"id": "5229153f-a1f0-465a-8f78-6d571d346053", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228775873000, "endTime": 32228777610000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "3dc6a79c-c640-4acf-96e3-64bd3179219f", "logId": "14286dc0-48bc-4c3c-ac4d-d142c3d96533"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3dc6a79c-c640-4acf-96e3-64bd3179219f", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228773904100}, "additional": {"logType": "detail", "children": [], "durationId": "5229153f-a1f0-465a-8f78-6d571d346053"}}, {"head": {"id": "6b1d1c38-dc70-4278-aacd-1eaa59a1940d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228774526400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c44c5e-ebec-42d9-9d22-a76606c37d05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228774695100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c762089-9f71-4b83-98b7-ab6100e09010", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228775885000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a303cf09-bb95-4c6f-827b-69cd1bc2fbf1", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228776160000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473adfc5-8d4f-4479-8610-bd516f0ac03b", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228777415100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "223bc125-8ae3-4a7d-a4e0-1fec66a63fb0", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0706329345703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228777534700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14286dc0-48bc-4c3c-ac4d-d142c3d96533", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228775873000, "endTime": 32228777610000}, "additional": {"logType": "info", "children": [], "durationId": "5229153f-a1f0-465a-8f78-6d571d346053"}}, {"head": {"id": "e548cb05-788e-4d02-b526-f3bcfe7bbe1d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228788148800, "endTime": 32228803538700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "fcad12dd-e55e-4b30-a051-f806dd736795", "logId": "e495f2a5-d04b-4d0e-98b6-58177fa5315f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcad12dd-e55e-4b30-a051-f806dd736795", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228780323900}, "additional": {"logType": "detail", "children": [], "durationId": "e548cb05-788e-4d02-b526-f3bcfe7bbe1d"}}, {"head": {"id": "cb232969-15a1-42f9-b0c2-111d6e14984a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228780659000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2ba0a4-d8fc-4c07-8b12-7bdc9edb8cdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228780762000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c31e3693-ff33-4625-9645-d5c82307fbb2", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228788159800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9655154-b859-4118-b6e7-0639e3910926", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228803335600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b99f1f9-abd6-4f58-bb69-6c7cb0ed21dc", "name": "entry : default@PackageHap cost memory 0.8326950073242188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228803470000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e495f2a5-d04b-4d0e-98b6-58177fa5315f", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228788148800, "endTime": 32228803538700}, "additional": {"logType": "info", "children": [], "durationId": "e548cb05-788e-4d02-b526-f3bcfe7bbe1d"}}, {"head": {"id": "db266eae-6791-43af-a1bb-0cbcd6228b09", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228810874100, "endTime": 32228813534000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "3166ab7f-06f8-4176-b10a-4f9d571181c4", "logId": "3f07784c-a33f-42e8-ad2e-bac1315d6ff9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3166ab7f-06f8-4176-b10a-4f9d571181c4", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228807462000}, "additional": {"logType": "detail", "children": [], "durationId": "db266eae-6791-43af-a1bb-0cbcd6228b09"}}, {"head": {"id": "c92a7daf-9ad4-42cd-a7bc-7b738b034f3f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228808065700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8129ea-5457-4842-8847-7066e147e1f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228808194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e480f0c-5e43-49cd-a432-8d533bdc1687", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228810886400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed6f3da-fc01-411d-9afe-424ea41c6a24", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228811504400}, "additional": {"logType": "warn", "children": [], "durationId": "db266eae-6791-43af-a1bb-0cbcd6228b09"}}, {"head": {"id": "0fdb8a67-87ab-46ad-91a9-88fedbe27789", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228812310400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "549ca1ee-0107-4d52-9b61-7efe527f62a0", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228812413100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27feff9e-3c90-439d-998b-77a28509c74e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228812498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d2808e-0bbf-4328-adc9-7a886b838fc6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228812552700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ee0d23-0f57-4639-a5cb-eb5cbdd35b9d", "name": "entry : default@SignHap cost memory 0.1171112060546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228813345600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df6ba20-7e12-4144-8512-38e4113d3441", "name": "runTaskFromQueue task cost before running: 847 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228813468600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f07784c-a33f-42e8-ad2e-bac1315d6ff9", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228810874100, "endTime": 32228813534000, "totalTime": 2573900}, "additional": {"logType": "info", "children": [], "durationId": "db266eae-6791-43af-a1bb-0cbcd6228b09"}}, {"head": {"id": "43dd5d27-f89f-4efc-b072-d4d162ca63d7", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228817510800, "endTime": 32228823339200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "748308af-ecb1-4f45-bd30-d6c0ba9dc0e8", "logId": "b9890e3e-6b12-4b1e-a607-9155dbd910a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "748308af-ecb1-4f45-bd30-d6c0ba9dc0e8", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228815384200}, "additional": {"logType": "detail", "children": [], "durationId": "43dd5d27-f89f-4efc-b072-d4d162ca63d7"}}, {"head": {"id": "2e8877e3-fbdf-4311-81a8-454a059a7bed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228815810800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c708e280-faf1-4006-ac6b-a398056cac20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228816029500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22d1d551-6eb0-4b57-bb32-308ad52433d7", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228817547200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c656ec2d-34fa-4a28-a506-a3f3e0333074", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228822462300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719a04f3-2c4c-474f-88e8-29f7fd00c2f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228822573000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1930944-d882-4e87-b288-15cce279f1d6", "name": "entry : default@CollectDebugSymbol cost memory 0.24048614501953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228822917500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25977d18-926d-47a5-befe-350b806c211e", "name": "runTaskFromQueue task cost before running: 857 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228823086100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9890e3e-6b12-4b1e-a607-9155dbd910a5", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228817510800, "endTime": 32228823339200, "totalTime": 5543800}, "additional": {"logType": "info", "children": [], "durationId": "43dd5d27-f89f-4efc-b072-d4d162ca63d7"}}, {"head": {"id": "672f133c-beee-41c0-a77b-0776a49eb158", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228825309900, "endTime": 32228825672900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1fb28757-150d-4c74-b53a-66a7d8cc6bd2", "logId": "54feb9ff-77bc-4b5b-a040-c080182d6b06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1fb28757-150d-4c74-b53a-66a7d8cc6bd2", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228825268400}, "additional": {"logType": "detail", "children": [], "durationId": "672f133c-beee-41c0-a77b-0776a49eb158"}}, {"head": {"id": "4749af0c-6373-4161-a91e-a7729c8fa8f3", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228825316500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d5e3b8-3322-4ea5-b092-8d8be165ab2a", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228825479000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9580e35-bb00-402b-a92f-bab7157178b7", "name": "runTaskFromQueue task cost before running: 859 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228825554500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54feb9ff-77bc-4b5b-a040-c080182d6b06", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228825309900, "endTime": 32228825672900, "totalTime": 227700}, "additional": {"logType": "info", "children": [], "durationId": "672f133c-beee-41c0-a77b-0776a49eb158"}}, {"head": {"id": "075e2dce-9cb5-47b5-9e4b-b5a0379d8295", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228833918300, "endTime": 32228833939900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "211707aa-d0af-41d8-b4ae-29b4a875fe0c", "logId": "d004dd05-c93e-49ea-a499-b7a132a0a556"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d004dd05-c93e-49ea-a499-b7a132a0a556", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228833918300, "endTime": 32228833939900}, "additional": {"logType": "info", "children": [], "durationId": "075e2dce-9cb5-47b5-9e4b-b5a0379d8295"}}, {"head": {"id": "99196a40-f276-4ff2-9ac4-9aefee66eb30", "name": "BUILD SUCCESSFUL in 868 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228834039600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "ae9f2c14-a589-45ef-b059-af4264f19cc4", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32227966838000, "endTime": 32228834512100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 26}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "211eac09-3d6f-4619-a09c-52a5bf3a15d1", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228834712800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15176042-7ed5-4065-bf90-4680d99e3db6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228834838800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df87fec7-caf4-4237-b5b0-8f67fc975f49", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228834931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40c75d60-f5f9-4c8c-a8d7-aa688db48ef4", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228834984700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d74c619-4c44-4068-b4ec-5c24afdeab36", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228835073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819c1ff7-1d46-42e8-8e84-e8ed76aff23f", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228835410700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "013201f3-e45f-4dbd-9c42-6b840f857588", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228836136400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf15572-11d3-487f-b9af-34671d4fb383", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228836388400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78cf6fc-81e9-4fca-b0d8-b16d2ed89e37", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228836452900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e252461e-1d80-43fe-86bb-6cda255dfa22", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228836509100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e47f4e0a-fdc8-45b9-a570-dba7e446681f", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228836738300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d3768c1-3413-46ba-a6dd-6a825a957563", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228837631200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18efb7f6-ed57-4cbb-9bec-8b4697e4b492", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228837879700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ae0d50-6982-4e1c-bfbd-81ecf75ab684", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228837943100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e64b2f-1643-409f-a3ee-f880e9cf8230", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228838000300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b557b8d8-3bca-4b5f-ab10-8b3c7fba196d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228838044900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b620c88-a858-4888-b05f-97c4b5f57791", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228838088400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b96fb65-1cf9-482f-accd-83fa4b722e57", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228838826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5df1681-daef-4e1d-8770-c574f3cbd7d2", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228839266000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11e63fd3-0251-4eef-8b50-726fb76c960c", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228839499200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6589cfd-5754-44db-a598-7c37b3896df9", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228839771700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd1e4cb8-d1a6-48bf-ae5e-9f03c0f31855", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228839838100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6615f6e5-c93d-4ef4-9ab5-fb815818e942", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228839891400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c53de37-db7e-48b8-b8d1-4b90bd2aee37", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228839944600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e313c9f-59e9-450a-9f24-435106287d32", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228841211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e55d267-fa86-47ac-9d01-057175b5cd34", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228841787200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c2a689-c2e5-4623-ac45-744b5532c540", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228843552500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d409c8-c735-44e6-b882-3b15ddbf4e46", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228843834300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "264e2669-3ef3-48ef-9a71-e06c793cfac5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228844036900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e8463d-3c1d-4b30-b784-c588eb36f7ff", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228844581400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59a9772b-7a1e-4d6f-beed-5d30b9b9a859", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228844816500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16ae9304-6b47-48e7-bfa9-bfc04d4a7669", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228844886500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4acdde9-64bb-4891-b052-eea5d8e018d6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228844935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a893ed6-5ded-4517-acec-ad7c4e894bcc", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228844982400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042c6db8-0021-4193-b03c-d50c7959975b", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228845149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ae96cff-129d-4811-9444-2072a3814ebf", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228845371800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c73b47d6-9002-4cc5-bafa-197dea5ed8e5", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228845577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce02489a-1cbe-4608-8a1d-0cf0645535f0", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228847601500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30641793-5c3d-4e9e-8232-93d1b8013784", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228847928900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33e918b-973c-4f02-ad8f-b8e8822c78c9", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228848217800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21d3b09-012f-4856-b9c4-4f078bc04412", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228848443900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}