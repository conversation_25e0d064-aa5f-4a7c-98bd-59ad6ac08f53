{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "7937a088-fc3c-4a72-a9cb-395ebbbcf5b8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029152735300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f60f4f8-406a-4c55-b589-9ec0ae98b512", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029218569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d003e0-0d03-41c2-bd37-e7204cff7d16", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 29029224812900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "164e2b17-dfd0-4946-ab36-52ee9d0ad381", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321096344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a359ab38-4403-43aa-beac-789232b5d0b5", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321102234400, "endTime": 30321538185600}, "additional": {"children": ["bcb9d678-8881-4819-879b-b21d3d1e9829", "bd905f72-cacd-4bf2-8a5b-4f958433c499", "866e3bc9-d7f5-489f-82bc-fb6f640238be", "e5b3fc00-f5e5-4d51-b840-2feb299c4e3a", "51b52a16-0fed-4af8-8ddd-76d32b470636", "761ef670-3b22-4918-82c1-f51e2f5de5d9", "802cf518-4f80-4de0-9f1a-48e04cc224bb"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bcb9d678-8881-4819-879b-b21d3d1e9829", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321102236300, "endTime": 30321117676000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "350db6d3-c802-490d-856f-c512887be0b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321118434800, "endTime": 30321536706600}, "additional": {"children": ["9edb2c21-c4f3-4128-8247-a423812829b4", "99b28b9a-37ba-4477-acd4-0ac747de2f9c", "30ee511e-1d5d-4a9c-b773-0eb9251101c6", "49c79cb2-dc38-4c6a-a1c1-07051be297f4", "8aea2a4c-efa8-4097-81d2-7eafbb5859df", "f799be6a-8a19-4c27-9ca8-7ef3c3283191", "e7159517-f7da-4c5f-b631-f70f9b898345", "db73f33f-de22-4c3e-97f8-82b61dcb79e1", "eac8069e-c82c-410b-80e3-a69117c235e2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "c3ab0387-3f02-4a44-bbda-5377952e2329"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "866e3bc9-d7f5-489f-82bc-fb6f640238be", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321536731100, "endTime": 30321538175200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "ae52c7db-a677-47ea-aeb8-06f651c5444b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5b3fc00-f5e5-4d51-b840-2feb299c4e3a", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321538179400, "endTime": 30321538180300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "b2cefa8f-d3f2-4567-a408-8a004b6bfdef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51b52a16-0fed-4af8-8ddd-76d32b470636", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321105735000, "endTime": 30321105777900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "f157e9d2-1cea-4fca-a523-6365725684bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f157e9d2-1cea-4fca-a523-6365725684bc", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321105735000, "endTime": 30321105777900}, "additional": {"logType": "info", "children": [], "durationId": "51b52a16-0fed-4af8-8ddd-76d32b470636", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "761ef670-3b22-4918-82c1-f51e2f5de5d9", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321112464600, "endTime": 30321112485900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "8fb4f790-2266-46be-98ed-4b04b1fedca4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fb4f790-2266-46be-98ed-4b04b1fedca4", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321112464600, "endTime": 30321112485900}, "additional": {"logType": "info", "children": [], "durationId": "761ef670-3b22-4918-82c1-f51e2f5de5d9", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "5fe78847-2d2c-4e98-8e32-299ba8ede7a1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321112540700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48a6f080-1e6d-4569-a3ad-cae0bfbe1b19", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321117499400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "350db6d3-c802-490d-856f-c512887be0b4", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321102236300, "endTime": 30321117676000}, "additional": {"logType": "info", "children": [], "durationId": "bcb9d678-8881-4819-879b-b21d3d1e9829", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "9edb2c21-c4f3-4128-8247-a423812829b4", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321125160100, "endTime": 30321125170400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "8abb02b6-a732-4825-a5cf-19035fae12e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99b28b9a-37ba-4477-acd4-0ac747de2f9c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321125194300, "endTime": 30321130628200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "bb3e96e0-b4c2-4c15-b2db-16d28c34516d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30ee511e-1d5d-4a9c-b773-0eb9251101c6", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321130642900, "endTime": 30321239596700}, "additional": {"children": ["c4f97b41-eb14-4e0a-8329-a73d6469ad6b", "c9510ed8-3278-4cba-840a-7522b0bc2777", "380754b1-bbd6-44bb-8e09-32c4aee1869a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "abadd817-9736-46cc-8e84-059aa795960f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49c79cb2-dc38-4c6a-a1c1-07051be297f4", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321239639000, "endTime": 30321304842000}, "additional": {"children": ["3d0e0a03-ee11-49b2-aee8-2b4bcfffb3c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "f672ee92-ee49-4329-b124-b7dab0cd0e17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8aea2a4c-efa8-4097-81d2-7eafbb5859df", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321304851500, "endTime": 30321504833100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "f83cdd0c-6fd0-45fb-9f59-433ae41efd61"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f799be6a-8a19-4c27-9ca8-7ef3c3283191", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321505766200, "endTime": 30321526110500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "fb4dcd21-e4ea-42c2-b5b0-b6bff5ea88fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7159517-f7da-4c5f-b631-f70f9b898345", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321526158900, "endTime": 30321536515500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "e2985027-30c5-473d-9742-75beaa6edb35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db73f33f-de22-4c3e-97f8-82b61dcb79e1", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321536537800, "endTime": 30321536676500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "56673390-0223-4b34-8a20-4ef0c9f1436c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8abb02b6-a732-4825-a5cf-19035fae12e8", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321125160100, "endTime": 30321125170400}, "additional": {"logType": "info", "children": [], "durationId": "9edb2c21-c4f3-4128-8247-a423812829b4", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "bb3e96e0-b4c2-4c15-b2db-16d28c34516d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321125194300, "endTime": 30321130628200}, "additional": {"logType": "info", "children": [], "durationId": "99b28b9a-37ba-4477-acd4-0ac747de2f9c", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "c4f97b41-eb14-4e0a-8329-a73d6469ad6b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321131335400, "endTime": 30321131351800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "30ee511e-1d5d-4a9c-b773-0eb9251101c6", "logId": "a412cfb8-7f58-4f53-a0ff-dc8715667d00"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a412cfb8-7f58-4f53-a0ff-dc8715667d00", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321131335400, "endTime": 30321131351800}, "additional": {"logType": "info", "children": [], "durationId": "c4f97b41-eb14-4e0a-8329-a73d6469ad6b", "parent": "abadd817-9736-46cc-8e84-059aa795960f"}}, {"head": {"id": "c9510ed8-3278-4cba-840a-7522b0bc2777", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321133560100, "endTime": 30321236560900}, "additional": {"children": ["10f423ba-15aa-4546-a553-71b7d47fe596", "5c25d124-2ef3-4bfc-8cf6-14c7a4b0a40a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "30ee511e-1d5d-4a9c-b773-0eb9251101c6", "logId": "2ab77bb9-2710-4195-9163-22c47a48a48b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10f423ba-15aa-4546-a553-71b7d47fe596", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321133562700, "endTime": 30321138183600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c9510ed8-3278-4cba-840a-7522b0bc2777", "logId": "570fbaa0-bf7f-4817-9d89-5909d85cc254"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c25d124-2ef3-4bfc-8cf6-14c7a4b0a40a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321138203000, "endTime": 30321236542400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c9510ed8-3278-4cba-840a-7522b0bc2777", "logId": "07c3fe60-5e1e-4deb-a1df-788efd10785d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a77852b-4438-44e7-a5ae-3c876a993f20", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321133569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0cf166-8e62-4b8f-8c26-6afb798e7b96", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321138041600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "570fbaa0-bf7f-4817-9d89-5909d85cc254", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321133562700, "endTime": 30321138183600}, "additional": {"logType": "info", "children": [], "durationId": "10f423ba-15aa-4546-a553-71b7d47fe596", "parent": "2ab77bb9-2710-4195-9163-22c47a48a48b"}}, {"head": {"id": "0c477601-32c0-41d6-8c0d-748fb7110fe7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321138217400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2164026f-42ad-4330-a4ad-09beadf732bd", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321147492100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e9ff578-e217-4237-b993-fe1be0552141", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321147629900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "063afd01-3379-4c8c-8317-53552fb48cac", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321147831800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee3301a-c41c-4406-b322-1ddb5c7f491c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321147972600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f2e9d69-36f9-494d-bb7b-de6043fe2c76", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321150114100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97653616-03ef-494f-83dd-c9139cb0168b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321156309400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02012dff-ae69-4af8-b543-4f6bb94b2360", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321167636500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2456f28f-a7a4-43b5-b995-21a66152eaa1", "name": "Sdk init in 39 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321196078300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2536b1d0-30b0-4219-b589-84903e24dc64", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321196226900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 54}, "markType": "other"}}, {"head": {"id": "7fb048b0-59fe-4cc5-9c8b-18a8c8763562", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321196279800}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 54}, "markType": "other"}}, {"head": {"id": "5d93e861-60cf-42d4-973a-06a77f6fb57e", "name": "Project task initialization takes 38 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321235183500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8277daa0-2e13-4c27-be1f-fcd9e9a31096", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321235984100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "619827b1-bab1-4fce-8789-6068fd81e506", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321236384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d29aa3-bb9e-40f3-ab70-7e3c3b9289ed", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321236480900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c3fe60-5e1e-4deb-a1df-788efd10785d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321138203000, "endTime": 30321236542400}, "additional": {"logType": "info", "children": [], "durationId": "5c25d124-2ef3-4bfc-8cf6-14c7a4b0a40a", "parent": "2ab77bb9-2710-4195-9163-22c47a48a48b"}}, {"head": {"id": "2ab77bb9-2710-4195-9163-22c47a48a48b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321133560100, "endTime": 30321236560900}, "additional": {"logType": "info", "children": ["570fbaa0-bf7f-4817-9d89-5909d85cc254", "07c3fe60-5e1e-4deb-a1df-788efd10785d"], "durationId": "c9510ed8-3278-4cba-840a-7522b0bc2777", "parent": "abadd817-9736-46cc-8e84-059aa795960f"}}, {"head": {"id": "380754b1-bbd6-44bb-8e09-32c4aee1869a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321239463900, "endTime": 30321239563900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "30ee511e-1d5d-4a9c-b773-0eb9251101c6", "logId": "bc6212c1-de91-410f-a009-cf6c0da660d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc6212c1-de91-410f-a009-cf6c0da660d2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321239463900, "endTime": 30321239563900}, "additional": {"logType": "info", "children": [], "durationId": "380754b1-bbd6-44bb-8e09-32c4aee1869a", "parent": "abadd817-9736-46cc-8e84-059aa795960f"}}, {"head": {"id": "abadd817-9736-46cc-8e84-059aa795960f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321130642900, "endTime": 30321239596700}, "additional": {"logType": "info", "children": ["a412cfb8-7f58-4f53-a0ff-dc8715667d00", "2ab77bb9-2710-4195-9163-22c47a48a48b", "bc6212c1-de91-410f-a009-cf6c0da660d2"], "durationId": "30ee511e-1d5d-4a9c-b773-0eb9251101c6", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "3d0e0a03-ee11-49b2-aee8-2b4bcfffb3c5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321241669600, "endTime": 30321304829400}, "additional": {"children": ["986e910d-5315-4b71-98fa-088de59dbf86", "e0f9f340-4598-4896-b4cc-c75044018b03", "3db8933c-9bc3-47b2-a2f3-55752c9e67dc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "49c79cb2-dc38-4c6a-a1c1-07051be297f4", "logId": "e5753f3e-df0e-4bfd-b13b-745f0c723ce3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "986e910d-5315-4b71-98fa-088de59dbf86", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321250463200, "endTime": 30321250486000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d0e0a03-ee11-49b2-aee8-2b4bcfffb3c5", "logId": "29d26bed-2120-4e19-93bb-12ba731b3d85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29d26bed-2120-4e19-93bb-12ba731b3d85", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321250463200, "endTime": 30321250486000}, "additional": {"logType": "info", "children": [], "durationId": "986e910d-5315-4b71-98fa-088de59dbf86", "parent": "e5753f3e-df0e-4bfd-b13b-745f0c723ce3"}}, {"head": {"id": "e0f9f340-4598-4896-b4cc-c75044018b03", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321253945900, "endTime": 30321302181500}, "additional": {"children": ["4378c7b2-f8d0-44f1-9773-c9a1af7cf032", "08dc3c6f-bbfe-4306-9255-3246c059c6cc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d0e0a03-ee11-49b2-aee8-2b4bcfffb3c5", "logId": "ae324ff3-72b2-4130-ba03-42c702ad99f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4378c7b2-f8d0-44f1-9773-c9a1af7cf032", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321253958100, "endTime": 30321268341000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0f9f340-4598-4896-b4cc-c75044018b03", "logId": "da91c13e-2deb-4cf8-9f69-63ec427ae269"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08dc3c6f-bbfe-4306-9255-3246c059c6cc", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321268412800, "endTime": 30321302154000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e0f9f340-4598-4896-b4cc-c75044018b03", "logId": "f7bb7c0d-d89d-4229-b4f4-78fbeee2667c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7564232b-cdf4-4825-a3f9-57efe469e07a", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321253965200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9cf525b-a9c6-419a-b96b-0c85515998d0", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321266887000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da91c13e-2deb-4cf8-9f69-63ec427ae269", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321253958100, "endTime": 30321268341000}, "additional": {"logType": "info", "children": [], "durationId": "4378c7b2-f8d0-44f1-9773-c9a1af7cf032", "parent": "ae324ff3-72b2-4130-ba03-42c702ad99f9"}}, {"head": {"id": "c0066662-bf8c-47b3-97e7-395b730fc4cc", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321268673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad15386-1694-4678-9155-2af836e9ff8f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321283423700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1658d9e-4cb5-4bf1-8d49-bdb5fbb9e623", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321285104200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15cc43c3-429e-4d36-abe8-f615d956b648", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321285370400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "410aa449-c29d-462a-bdcc-97f9d98c6711", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321285526900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f207de-5751-4c2e-89f7-a49b61a43c27", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321285605300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f4de48-6bf5-4aa5-a685-877b16c21291", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321285659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7301fd22-d589-4e1b-b788-6cbcb4398de9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321287500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "491bc74c-63d3-4521-b1e3-48c1c2dd311c", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321297782800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a64f09-4954-4618-9471-511b06a01adb", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321298205400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29e29c23-b93c-4a1f-a106-e28008c53e7b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321299046900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2deaa69-b32d-4d72-b506-77fd16a86d76", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321301629000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7bb7c0d-d89d-4229-b4f4-78fbeee2667c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321268412800, "endTime": 30321302154000}, "additional": {"logType": "info", "children": [], "durationId": "08dc3c6f-bbfe-4306-9255-3246c059c6cc", "parent": "ae324ff3-72b2-4130-ba03-42c702ad99f9"}}, {"head": {"id": "ae324ff3-72b2-4130-ba03-42c702ad99f9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321253945900, "endTime": 30321302181500}, "additional": {"logType": "info", "children": ["da91c13e-2deb-4cf8-9f69-63ec427ae269", "f7bb7c0d-d89d-4229-b4f4-78fbeee2667c"], "durationId": "e0f9f340-4598-4896-b4cc-c75044018b03", "parent": "e5753f3e-df0e-4bfd-b13b-745f0c723ce3"}}, {"head": {"id": "3db8933c-9bc3-47b2-a2f3-55752c9e67dc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321304788300, "endTime": 30321304812500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d0e0a03-ee11-49b2-aee8-2b4bcfffb3c5", "logId": "f9626e59-dbef-4e08-8f2d-fceb676f0ee1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9626e59-dbef-4e08-8f2d-fceb676f0ee1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321304788300, "endTime": 30321304812500}, "additional": {"logType": "info", "children": [], "durationId": "3db8933c-9bc3-47b2-a2f3-55752c9e67dc", "parent": "e5753f3e-df0e-4bfd-b13b-745f0c723ce3"}}, {"head": {"id": "e5753f3e-df0e-4bfd-b13b-745f0c723ce3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321241669600, "endTime": 30321304829400}, "additional": {"logType": "info", "children": ["29d26bed-2120-4e19-93bb-12ba731b3d85", "ae324ff3-72b2-4130-ba03-42c702ad99f9", "f9626e59-dbef-4e08-8f2d-fceb676f0ee1"], "durationId": "3d0e0a03-ee11-49b2-aee8-2b4bcfffb3c5", "parent": "f672ee92-ee49-4329-b124-b7dab0cd0e17"}}, {"head": {"id": "f672ee92-ee49-4329-b124-b7dab0cd0e17", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321239639000, "endTime": 30321304842000}, "additional": {"logType": "info", "children": ["e5753f3e-df0e-4bfd-b13b-745f0c723ce3"], "durationId": "49c79cb2-dc38-4c6a-a1c1-07051be297f4", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "be6030fb-5270-4bd6-8c86-f2958397691f", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321375716000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aefa161d-edf2-4b92-a564-b0bb3a151c03", "name": "hvigorfile, resolve hvigorfile dependencies in 200 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321504706000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f83cdd0c-6fd0-45fb-9f59-433ae41efd61", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321304851500, "endTime": 30321504833100}, "additional": {"logType": "info", "children": [], "durationId": "8aea2a4c-efa8-4097-81d2-7eafbb5859df", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "eac8069e-c82c-410b-80e3-a69117c235e2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321505570700, "endTime": 30321505754200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "logId": "184cf164-1227-48d8-b6a2-db888cda1887"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71fdf888-d0c2-4a11-b7c5-d6a9a599d498", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321505598600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "184cf164-1227-48d8-b6a2-db888cda1887", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321505570700, "endTime": 30321505754200}, "additional": {"logType": "info", "children": [], "durationId": "eac8069e-c82c-410b-80e3-a69117c235e2", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "aad39129-9ee1-40ec-b76a-fb839eeefcb6", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321506890000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5f99b65-6664-429a-811e-9abfb2d90c78", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321524579100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4dcd21-e4ea-42c2-b5b0-b6bff5ea88fa", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321505766200, "endTime": 30321526110500}, "additional": {"logType": "info", "children": [], "durationId": "f799be6a-8a19-4c27-9ca8-7ef3c3283191", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "8a189ec5-03b5-4352-a559-102b3f66554d", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321531731200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53ab09a6-5397-472a-bdda-c18090b5ae24", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321531863900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b6397e8-5cce-4d4e-9bce-6eab9b38a38d", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321533589900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a75ae54a-c0bd-4944-89d6-b7777a740df9", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321533705100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2985027-30c5-473d-9742-75beaa6edb35", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321526158900, "endTime": 30321536515500}, "additional": {"logType": "info", "children": [], "durationId": "e7159517-f7da-4c5f-b631-f70f9b898345", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "58dcf6dd-aa77-43b2-8977-5f3b3c9421ca", "name": "Configuration phase cost:412 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321536561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56673390-0223-4b34-8a20-4ef0c9f1436c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321536537800, "endTime": 30321536676500}, "additional": {"logType": "info", "children": [], "durationId": "db73f33f-de22-4c3e-97f8-82b61dcb79e1", "parent": "c3ab0387-3f02-4a44-bbda-5377952e2329"}}, {"head": {"id": "c3ab0387-3f02-4a44-bbda-5377952e2329", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321118434800, "endTime": 30321536706600}, "additional": {"logType": "info", "children": ["8abb02b6-a732-4825-a5cf-19035fae12e8", "bb3e96e0-b4c2-4c15-b2db-16d28c34516d", "abadd817-9736-46cc-8e84-059aa795960f", "f672ee92-ee49-4329-b124-b7dab0cd0e17", "f83cdd0c-6fd0-45fb-9f59-433ae41efd61", "fb4dcd21-e4ea-42c2-b5b0-b6bff5ea88fa", "e2985027-30c5-473d-9742-75beaa6edb35", "56673390-0223-4b34-8a20-4ef0c9f1436c", "184cf164-1227-48d8-b6a2-db888cda1887"], "durationId": "bd905f72-cacd-4bf2-8a5b-4f958433c499", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "802cf518-4f80-4de0-9f1a-48e04cc224bb", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321538146400, "endTime": 30321538163100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a359ab38-4403-43aa-beac-789232b5d0b5", "logId": "e4d59edf-1e7c-4802-860c-a248acdbd2db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4d59edf-1e7c-4802-860c-a248acdbd2db", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321538146400, "endTime": 30321538163100}, "additional": {"logType": "info", "children": [], "durationId": "802cf518-4f80-4de0-9f1a-48e04cc224bb", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "ae52c7db-a677-47ea-aeb8-06f651c5444b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321536731100, "endTime": 30321538175200}, "additional": {"logType": "info", "children": [], "durationId": "866e3bc9-d7f5-489f-82bc-fb6f640238be", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "b2cefa8f-d3f2-4567-a408-8a004b6bfdef", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321538179400, "endTime": 30321538180300}, "additional": {"logType": "info", "children": [], "durationId": "e5b3fc00-f5e5-4d51-b840-2feb299c4e3a", "parent": "d1afcc26-8467-472b-a2f5-496b3556c2ec"}}, {"head": {"id": "d1afcc26-8467-472b-a2f5-496b3556c2ec", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321102234400, "endTime": 30321538185600}, "additional": {"logType": "info", "children": ["350db6d3-c802-490d-856f-c512887be0b4", "c3ab0387-3f02-4a44-bbda-5377952e2329", "ae52c7db-a677-47ea-aeb8-06f651c5444b", "b2cefa8f-d3f2-4567-a408-8a004b6bfdef", "f157e9d2-1cea-4fca-a523-6365725684bc", "8fb4f790-2266-46be-98ed-4b04b1fedca4", "e4d59edf-1e7c-4802-860c-a248acdbd2db"], "durationId": "a359ab38-4403-43aa-beac-789232b5d0b5"}}, {"head": {"id": "56a991a0-04dc-4585-821f-acc4a71f0d1e", "name": "Configuration task cost before running: 440 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321538373800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2250cdc5-e86f-419c-9896-9a8303b09799", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321548075800, "endTime": 30321576165000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e020a050-ba70-479b-a040-442fc1d58def", "logId": "28cd5103-96fe-4f44-a739-65d44a380de4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e020a050-ba70-479b-a040-442fc1d58def", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321540694500}, "additional": {"logType": "detail", "children": [], "durationId": "2250cdc5-e86f-419c-9896-9a8303b09799"}}, {"head": {"id": "720c729d-2f0e-4313-8fd4-cfb6091d7a7c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321541309500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ee900a7-3a93-4382-bdf3-2b422619d7ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321541722100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce1ae2e-7db2-43e2-b671-8eca587f8714", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321548095600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd47fedb-8d2a-45b4-a042-77cfa4dfce55", "name": "Incremental task entry:default@PreBuild pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321575265700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "517213c3-695c-4f97-a743-e8a8789a82ad", "name": "entry : default@PreBuild cost memory 0.31484222412109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321576062900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28cd5103-96fe-4f44-a739-65d44a380de4", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321548075800, "endTime": 30321576165000}, "additional": {"logType": "info", "children": [], "durationId": "2250cdc5-e86f-419c-9896-9a8303b09799"}}, {"head": {"id": "1c39fc55-b87e-4dc9-b63c-d0b1cfae6aa5", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321588304100, "endTime": 30321591635600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9b8d442f-8f07-44e3-a98b-36eae65e7363", "logId": "1c68f82c-8e8e-47aa-a145-440a1b2b5c34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b8d442f-8f07-44e3-a98b-36eae65e7363", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321585383900}, "additional": {"logType": "detail", "children": [], "durationId": "1c39fc55-b87e-4dc9-b63c-d0b1cfae6aa5"}}, {"head": {"id": "bcca81df-e472-4f6f-9e1d-a13c7749344d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321585766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d242b40-ade3-4cb0-ad17-7e57f6be133b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321586112400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b7970f-0480-4150-af5f-2e5cbef2dec2", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321588322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1c570a3-2b68-4c53-a082-3dfa045a5b89", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321589574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dcaff46-38cd-4d14-86af-72e02b32b1ed", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321591209200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789b1f5b-43bb-4c34-b85c-83098478f60b", "name": "entry : default@GenerateMetadata cost memory 0.0962677001953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321591394000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c68f82c-8e8e-47aa-a145-440a1b2b5c34", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321588304100, "endTime": 30321591635600}, "additional": {"logType": "info", "children": [], "durationId": "1c39fc55-b87e-4dc9-b63c-d0b1cfae6aa5"}}, {"head": {"id": "349fdcf4-c5a4-45f3-9cd0-9fa3616f6115", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597569000, "endTime": 30321598010800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ff5f1fb1-4117-4174-81ad-e78d207e1bbf", "logId": "6502725c-7c5f-4906-9fa5-c4732be0f716"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff5f1fb1-4117-4174-81ad-e78d207e1bbf", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321596895400}, "additional": {"logType": "detail", "children": [], "durationId": "349fdcf4-c5a4-45f3-9cd0-9fa3616f6115"}}, {"head": {"id": "478d4d9b-f5e3-4fd2-b35d-c9902183c85b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597312500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc77d964-59b0-4b47-aea0-4d19373d0898", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597420400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cc34abc-dedd-444c-8861-d354d26081c5", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597576400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da78ae02-cce1-40f7-8ac0-a0f11b1cacd5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597700700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c526f8-98ab-4e36-b0d2-70cb5ff08991", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597753300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5ceaaf7-bd81-48e3-828c-55c169aba97d", "name": "entry : default@ConfigureCmake cost memory 0.0366058349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597823800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce7b743-daa9-42fe-b5b9-2e63ca06f519", "name": "runTaskFromQueue task cost before running: 499 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597901100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6502725c-7c5f-4906-9fa5-c4732be0f716", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321597569000, "endTime": 30321598010800, "totalTime": 317200}, "additional": {"logType": "info", "children": [], "durationId": "349fdcf4-c5a4-45f3-9cd0-9fa3616f6115"}}, {"head": {"id": "7d791109-865b-4ba7-9a44-995bb85a2159", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321603486900, "endTime": 30321605097000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "34419d92-ef87-4e60-96b5-6a015a79707a", "logId": "381478ce-ca74-44fd-838c-1068e17faa5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34419d92-ef87-4e60-96b5-6a015a79707a", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321599515000}, "additional": {"logType": "detail", "children": [], "durationId": "7d791109-865b-4ba7-9a44-995bb85a2159"}}, {"head": {"id": "0c9f6d4d-d13b-4a4e-90d4-3ddf21b2c921", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321599857600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ddab1fd-4116-4e91-890b-eee8d6d380c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321600091200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf5b95f-9126-433a-9240-3646fffaa39e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321603503100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a46187a-52f6-4db1-a5dd-be928f21b432", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321604919700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8833fac7-1248-40f0-9779-c53147d13c89", "name": "entry : default@MergeProfile cost memory 0.1069793701171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321605027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381478ce-ca74-44fd-838c-1068e17faa5e", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321603486900, "endTime": 30321605097000}, "additional": {"logType": "info", "children": [], "durationId": "7d791109-865b-4ba7-9a44-995bb85a2159"}}, {"head": {"id": "ca709a45-d748-4cd0-9055-1a4dbb5dfc64", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321609999800, "endTime": 30321622284300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a9339695-d495-44df-b5a7-861cf01ff991", "logId": "5b99d9e2-87ea-431d-a9a8-251dd81932fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9339695-d495-44df-b5a7-861cf01ff991", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321608583000}, "additional": {"logType": "detail", "children": [], "durationId": "ca709a45-d748-4cd0-9055-1a4dbb5dfc64"}}, {"head": {"id": "3c9af439-722d-4ae6-a0ee-76d6b63fe4b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321609026400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0480c8e-fc5e-4d31-aff3-dfe36c551d5b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321609126600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ef7f28-93da-4c16-b7b4-de914f0c741d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321610011100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe7e27cd-f3b3-4100-b65b-53ff61bd7468", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321616162100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64eb1aa3-0983-4275-882b-6b9cd308be79", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321618319500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21dc7c5c-d706-4014-99d2-1f9f8c22fb96", "name": "entry : default@CreateBuildProfile cost memory 0.11109161376953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321622186700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b99d9e2-87ea-431d-a9a8-251dd81932fd", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321609999800, "endTime": 30321622284300}, "additional": {"logType": "info", "children": [], "durationId": "ca709a45-d748-4cd0-9055-1a4dbb5dfc64"}}, {"head": {"id": "e076df8e-34b7-4ab2-bd8b-663b1102383f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321630471700, "endTime": 30321633344700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a4becd2e-1c65-468b-a7ba-ff85bb2016c8", "logId": "bfca0d2c-feb8-4bc8-841c-7c15daf069f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4becd2e-1c65-468b-a7ba-ff85bb2016c8", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321626387500}, "additional": {"logType": "detail", "children": [], "durationId": "e076df8e-34b7-4ab2-bd8b-663b1102383f"}}, {"head": {"id": "cc2a3535-f9f6-4e6e-96a0-3b391900425f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321626890600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "954e8501-54b8-4edc-9bbe-cb09a9d432f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321627140100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e620f7-f9a4-4392-b145-b27e4629b643", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321630487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67486379-a16f-491d-8eec-87bf26faeb4f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321632358400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "589f66c1-9b67-401e-a545-3ede164d9afe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321632581700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "410005da-272f-46ba-8957-e94829c4e718", "name": "entry : default@PreCheckSyscap cost memory 0.03682708740234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321632732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b1c9add-2710-40ad-a075-e84de5bc2365", "name": "runTaskFromQueue task cost before running: 534 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321633118300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfca0d2c-feb8-4bc8-841c-7c15daf069f3", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321630471700, "endTime": 30321633344700, "totalTime": 2584800}, "additional": {"logType": "info", "children": [], "durationId": "e076df8e-34b7-4ab2-bd8b-663b1102383f"}}, {"head": {"id": "8b663a2e-2838-4ef7-98c9-7715bddfdfe3", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321645774200, "endTime": 30321647358500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6496e604-75b3-4522-9246-a7587b019d74", "logId": "e4c2cc61-c07f-4254-af4b-ebd96b9cd9c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6496e604-75b3-4522-9246-a7587b019d74", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321636261100}, "additional": {"logType": "detail", "children": [], "durationId": "8b663a2e-2838-4ef7-98c9-7715bddfdfe3"}}, {"head": {"id": "ff4b7953-0911-41d8-bca0-8302ff8f638f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321638819800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd4c96b-6e57-4262-9b58-22fc88efd717", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321639010400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11cd5e9d-55d4-4f58-b467-aa58a303f8c7", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321645793300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b4d1274-6e35-461f-89a2-867251ecd29b", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321646147600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c278ba59-be8d-4513-abb1-5b17aeb8ae49", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03917694091796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321646390600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df52f6d-e3d9-4375-bbc9-88772b7b094e", "name": "runTaskFromQueue task cost before running: 548 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321646611900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c2cc61-c07f-4254-af4b-ebd96b9cd9c1", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321645774200, "endTime": 30321647358500, "totalTime": 779300}, "additional": {"logType": "info", "children": [], "durationId": "8b663a2e-2838-4ef7-98c9-7715bddfdfe3"}}, {"head": {"id": "65c248cc-5147-45d6-b42b-643b39d95267", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321660303000, "endTime": 30321691910100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "7155c28d-2935-4d17-9711-fa63e83a09d2", "logId": "fc7fd2a2-669d-41fc-83a6-71c1fc1c30c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7155c28d-2935-4d17-9711-fa63e83a09d2", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321654198200}, "additional": {"logType": "detail", "children": [], "durationId": "65c248cc-5147-45d6-b42b-643b39d95267"}}, {"head": {"id": "426ee917-c973-4635-b6dc-03691df5654c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321654902000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e573338-7e3e-45c3-98e7-65365e84e5e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321656387400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed6a110-01e5-44c6-aeba-2acb39c3840b", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321660351900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7541ab1b-4562-4de4-b6fa-e41537ce7eb1", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321689406300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b935dbc-7158-42c6-84fa-93a90f94a3cb", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321689693500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e70acd9-0d14-43e9-9784-28117a76e876", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321689801900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cdaab6b-0c42-4590-8724-38be2eb9a956", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321689882600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4c640ab-7e11-4f9d-9f4b-a001776d2868", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11920928955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321690050900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d0d819a-9a0c-40e2-a18f-55bf67850720", "name": "runTaskFromQueue task cost before running: 592 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321691070500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7fd2a2-669d-41fc-83a6-71c1fc1c30c5", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321660303000, "endTime": 30321691910100, "totalTime": 30613100}, "additional": {"logType": "info", "children": [], "durationId": "65c248cc-5147-45d6-b42b-643b39d95267"}}, {"head": {"id": "4251083c-7dad-437c-88c0-97fde360e133", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707176700, "endTime": 30321708046900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ddc82774-9a09-43dd-b0fc-6132c94a82ee", "logId": "6f3fdf44-b535-4a55-94b7-45c3cdf4ad1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddc82774-9a09-43dd-b0fc-6132c94a82ee", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321704657000}, "additional": {"logType": "detail", "children": [], "durationId": "4251083c-7dad-437c-88c0-97fde360e133"}}, {"head": {"id": "78d9ad59-d5e8-4e44-b8d7-b411e2ef3a87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321705508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0c7d89-b9ac-4b33-82f5-b2f25da640ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321705839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d8539fa-639b-469a-bcf8-dc0efe87cf57", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707210400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88692556-c8a1-4ada-9bb2-76c132c76ead", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707568400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2096f3b-f2f2-44a0-bd82-54c539272481", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707677100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abc3141e-2339-4dee-b1b3-02caa14a5985", "name": "entry : default@BuildNativeWithCmake cost memory 0.03765869140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707825600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ba291a3-3780-4e28-898b-b88fb67bc62e", "name": "runTaskFromQueue task cost before running: 609 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707946400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f3fdf44-b535-4a55-94b7-45c3cdf4ad1d", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321707176700, "endTime": 30321708046900, "totalTime": 752100}, "additional": {"logType": "info", "children": [], "durationId": "4251083c-7dad-437c-88c0-97fde360e133"}}, {"head": {"id": "be43f3c6-441b-45e1-8b98-66916fecb344", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321714633700, "endTime": 30321722210900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "171f3f7d-5f92-4511-85ea-354f0eee1822", "logId": "a592904f-24bf-4386-b474-1241902ccb36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "171f3f7d-5f92-4511-85ea-354f0eee1822", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321710746000}, "additional": {"logType": "detail", "children": [], "durationId": "be43f3c6-441b-45e1-8b98-66916fecb344"}}, {"head": {"id": "00eaf08c-721c-412e-bd8d-13ddb68c7607", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321712748900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25424cf6-33ea-459c-824d-7480872b79a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321712881000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff660312-c0db-4f18-98e7-7017886e090e", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321714654800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239f12de-e442-4c1a-9a51-f8ef4d0493b1", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321721260600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56af1d41-491b-4e9d-9a8c-243c61bcdde0", "name": "entry : default@MakePackInfo cost memory 0.1400146484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321722066700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a592904f-24bf-4386-b474-1241902ccb36", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321714633700, "endTime": 30321722210900}, "additional": {"logType": "info", "children": [], "durationId": "be43f3c6-441b-45e1-8b98-66916fecb344"}}, {"head": {"id": "d7b70ad8-4628-4142-8f07-e70eb7b39bf9", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321727268800, "endTime": 30321732737800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "0d6861b9-2199-4634-ade6-123563789378", "logId": "c5e5fb1b-41a7-462c-92be-2036d7789b90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d6861b9-2199-4634-ade6-123563789378", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321725218800}, "additional": {"logType": "detail", "children": [], "durationId": "d7b70ad8-4628-4142-8f07-e70eb7b39bf9"}}, {"head": {"id": "ca369790-9ef2-48f5-8d67-a0922ef382d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321725664900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47cf744-96b0-4dea-847d-fccaac06dec0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321725773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9a43428-f57c-4ed4-a9f3-65a41ca36edb", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321727285300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab4f1b9d-5e7b-4b84-8f53-d87f8a6205cf", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321727636000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "242d59b2-0cf8-45c3-8ee3-21e4c5218a11", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321728924000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e2219c2-ce03-40c3-b581-3abe1da3eb8d", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321731483300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f90c26dd-084d-4c4b-9932-ff5878713e41", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321731671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "746662e4-a0f8-4479-aa1f-ae182e3eee58", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321731771800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b154577-5c6e-462c-8e45-88d9a5114d61", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321731828500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b0c3e7b-9ce5-4259-9ae5-7c9a6d59e074", "name": "entry : default@SyscapTransform cost memory 0.15584564208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321731955200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3e0d48-d547-4898-be3f-13c833ff8cae", "name": "runTaskFromQueue task cost before running: 634 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321732612300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5e5fb1b-41a7-462c-92be-2036d7789b90", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321727268800, "endTime": 30321732737800, "totalTime": 5113900}, "additional": {"logType": "info", "children": [], "durationId": "d7b70ad8-4628-4142-8f07-e70eb7b39bf9"}}, {"head": {"id": "e0731a62-bc98-440b-8768-30b337ee98a5", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321740441600, "endTime": 30321742275700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "78025249-738f-45da-abf6-46fa9d2d244d", "logId": "6dd424a3-8a49-4d66-a973-d9ff4e408fe0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78025249-738f-45da-abf6-46fa9d2d244d", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321735382900}, "additional": {"logType": "detail", "children": [], "durationId": "e0731a62-bc98-440b-8768-30b337ee98a5"}}, {"head": {"id": "e0b699e0-61d6-456c-aa88-a1839ac089aa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321736059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cadbe1e-c73d-43e1-8d03-b55a24fd33b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321736201900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83a484e6-9c04-4042-89e0-18ab963c8a5b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321740470000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4177504a-eb92-4e84-9094-13ea8d6bca17", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321741783400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebaf0847-0788-4427-8d27-adb680f67ed5", "name": "entry : default@ProcessProfile cost memory 0.060577392578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321742116900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dd424a3-8a49-4d66-a973-d9ff4e408fe0", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321740441600, "endTime": 30321742275700}, "additional": {"logType": "info", "children": [], "durationId": "e0731a62-bc98-440b-8768-30b337ee98a5"}}, {"head": {"id": "564c03ce-d28f-4347-8bc6-d2c97690adad", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321748244900, "endTime": 30321752975300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d047d718-845a-43f2-b678-183641b5bfbb", "logId": "d18edd3b-65cf-4070-8284-4e320df9d022"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d047d718-845a-43f2-b678-183641b5bfbb", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321744994900}, "additional": {"logType": "detail", "children": [], "durationId": "564c03ce-d28f-4347-8bc6-d2c97690adad"}}, {"head": {"id": "2a160679-7ae1-4788-b0a8-608b38eded10", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321745837800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d06e8bc-7dbb-4144-b48e-ed9e18fe3c32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321746018900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf63d17-a28b-4ebd-a0b3-0d2910442081", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321748258100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ba50713-0cea-4b95-a397-c7720edd18a1", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321752659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "449e00e2-90e1-4970-8663-84d9ea7e31e2", "name": "entry : default@ProcessRouterMap cost memory 0.22100830078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321752832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18edd3b-65cf-4070-8284-4e320df9d022", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321748244900, "endTime": 30321752975300}, "additional": {"logType": "info", "children": [], "durationId": "564c03ce-d28f-4347-8bc6-d2c97690adad"}}, {"head": {"id": "9a48867e-527a-4cc7-9519-a38fe72225f5", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321757895000, "endTime": 30321759314500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "be7bc484-d0d3-466d-805e-b2b3c0bd764e", "logId": "c62bcc61-af44-4ed5-9fc1-54ba27d7071f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be7bc484-d0d3-466d-805e-b2b3c0bd764e", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321756312200}, "additional": {"logType": "detail", "children": [], "durationId": "9a48867e-527a-4cc7-9519-a38fe72225f5"}}, {"head": {"id": "33fe2fea-8531-483e-b7f3-98145dce324b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321756766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fdbf497-f11e-485f-8f79-7d0c009ec70a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321756868200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf6d28d-1ae0-497e-a833-3b271e581917", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321757908400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb14e7b-b300-4b6e-831c-08583420fa50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321758222800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18924911-45e8-4bd3-8259-c84758512622", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321758345500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1f3831-7c6c-4cbf-b93e-0338330d091c", "name": "entry : default@BuildNativeWithNinja cost memory 0.05725860595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321759109700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db32f090-b86b-4049-8882-ce19ed497978", "name": "runTaskFromQueue task cost before running: 660 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321759238300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62bcc61-af44-4ed5-9fc1-54ba27d7071f", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321757895000, "endTime": 30321759314500, "totalTime": 1319300}, "additional": {"logType": "info", "children": [], "durationId": "9a48867e-527a-4cc7-9519-a38fe72225f5"}}, {"head": {"id": "fdfe879f-dbf4-463d-8765-993ced030494", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321768160900, "endTime": 30321775252800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4d2420b3-4f98-4a05-a1b2-ef26d23a9a63", "logId": "3cbd3def-c979-425c-8a31-bfe1ebcd054f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d2420b3-4f98-4a05-a1b2-ef26d23a9a63", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321763258000}, "additional": {"logType": "detail", "children": [], "durationId": "fdfe879f-dbf4-463d-8765-993ced030494"}}, {"head": {"id": "158b9b2e-a52d-4a57-8da7-7e60f5a596dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321765182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee13ecd-47a8-42dd-adeb-e0df27587409", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321765320800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af4c8fb-061c-42e3-97c0-685b5d629afd", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321766578700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c25d7f-476f-4a0b-b124-6caddd57d2cd", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321769866500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4383d8be-779e-4a85-9e83-3f19e27473f4", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321773035800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678ba503-7fd1-440c-b2bd-33db511c542f", "name": "entry : default@ProcessResource cost memory 0.17037200927734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321773369300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cbd3def-c979-425c-8a31-bfe1ebcd054f", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321768160900, "endTime": 30321775252800}, "additional": {"logType": "info", "children": [], "durationId": "fdfe879f-dbf4-463d-8765-993ced030494"}}, {"head": {"id": "df26d009-feb9-4064-a643-69cce64b4583", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321784144300, "endTime": 30321808548900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fd609cf2-019b-484d-9884-0bbf4aeb117f", "logId": "0e00ab53-0bad-4551-beef-3534ff76c763"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd609cf2-019b-484d-9884-0bbf4aeb117f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321780198100}, "additional": {"logType": "detail", "children": [], "durationId": "df26d009-feb9-4064-a643-69cce64b4583"}}, {"head": {"id": "ebd787ae-21fe-45c1-ad54-a0adf55db3dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321780570900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f74533ec-f4ad-432b-941d-ceb46b0d86c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321780670500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d756e15a-ccdd-40bc-aeda-b3dd27ee260f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321784178600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb0d372-9d6a-42ec-9520-5c393267efec", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321806837500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a24c80-e1b7-4aa8-8ed3-a20bbd575ad3", "name": "entry : default@GenerateLoaderJson cost memory 0.7650680541992188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321808360600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e00ab53-0bad-4551-beef-3534ff76c763", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321784144300, "endTime": 30321808548900}, "additional": {"logType": "info", "children": [], "durationId": "df26d009-feb9-4064-a643-69cce64b4583"}}, {"head": {"id": "2f9a2b15-68f7-4e32-978e-1baf5243ced8", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321818633100, "endTime": 30321821585000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "02afb689-4ab7-4fab-a2cc-43f75418782e", "logId": "10979d64-b642-421a-8bf0-50a32171d2b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02afb689-4ab7-4fab-a2cc-43f75418782e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321817225100}, "additional": {"logType": "detail", "children": [], "durationId": "2f9a2b15-68f7-4e32-978e-1baf5243ced8"}}, {"head": {"id": "4d33b481-ead2-438d-b0a6-e4387f51be82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321817694400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f790ca1-e370-4ff8-8095-3f702debb725", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321817801000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30228b74-62d4-44e4-b01b-e7478e7ae2f5", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321818664400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f45e36fd-b516-428e-b458-555d539a84d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321820543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af2622b-df7e-404b-8943-ebf2ea073cd3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321820643400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e67407d3-5616-4e40-88ec-0a7eede989c4", "name": "entry : default@ProcessLibs cost memory 0.12621307373046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321821396900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a703b12e-4731-41cc-83bf-a4a3b0f06147", "name": "runTaskFromQueue task cost before running: 723 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321821518300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10979d64-b642-421a-8bf0-50a32171d2b8", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321818633100, "endTime": 30321821585000, "totalTime": 2862100}, "additional": {"logType": "info", "children": [], "durationId": "2f9a2b15-68f7-4e32-978e-1baf5243ced8"}}, {"head": {"id": "91392171-0ca9-4a5a-831b-b16dafdf659e", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321828538000, "endTime": 30321848590600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5b8c4196-79b8-4c17-9f08-fb0b65c4f7e9", "logId": "18ec434e-ff92-4ac8-86c6-f2fc1aff8715"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b8c4196-79b8-4c17-9f08-fb0b65c4f7e9", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321824073700}, "additional": {"logType": "detail", "children": [], "durationId": "91392171-0ca9-4a5a-831b-b16dafdf659e"}}, {"head": {"id": "13702e4d-531f-49f4-b757-9366ab2b3661", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321824436100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d687d7-f350-4ba2-af41-ddf5dac0335d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321824541700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6492a0dd-e4be-4ad2-831c-af61d83d6acd", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321825261600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c46fa7f-eb5d-4f10-bccd-701bedf78335", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321828565700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf634657-35aa-4fa6-834a-97631407b93a", "name": "Incremental task entry:default@CompileResource pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321848319900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7007abe8-8be7-4ba0-bda6-1cc4b904cb24", "name": "entry : default@CompileResource cost memory 1.4123764038085938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321848478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18ec434e-ff92-4ac8-86c6-f2fc1aff8715", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321828538000, "endTime": 30321848590600}, "additional": {"logType": "info", "children": [], "durationId": "91392171-0ca9-4a5a-831b-b16dafdf659e"}}, {"head": {"id": "63659e50-872f-4ae9-9469-11f09a1f902b", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321853489100, "endTime": 30321854975000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3f8f09fa-22d4-4e95-b529-0ecc4336f2b4", "logId": "584ce287-9075-4495-b72c-41f5f1867ffc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f8f09fa-22d4-4e95-b529-0ecc4336f2b4", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321850999700}, "additional": {"logType": "detail", "children": [], "durationId": "63659e50-872f-4ae9-9469-11f09a1f902b"}}, {"head": {"id": "e600736d-1f8e-4b8f-b14c-13960d2b2641", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321851327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "989874a0-ceff-40e5-9ec3-0aa1f4a28331", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321851448100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36581675-7b48-4d7b-90a6-b31bb4f79eee", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321853508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb3e72a-1f65-4122-a0b4-f68cb4dbde30", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321853763400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "890a685a-9958-473a-866e-505fc004ab7a", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321854684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e068ad-32db-4df0-88f1-afa58b99f2ed", "name": "entry : default@DoNativeStrip cost memory 0.07708740234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321854850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "584ce287-9075-4495-b72c-41f5f1867ffc", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321853489100, "endTime": 30321854975000}, "additional": {"logType": "info", "children": [], "durationId": "63659e50-872f-4ae9-9469-11f09a1f902b"}}, {"head": {"id": "caf28876-a3d3-4a6b-86fc-e355a3a78c89", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321860163200, "endTime": 30323211813100}, "additional": {"children": ["bf1daa5d-516f-47a0-9375-70709363c9f7", "1dfd9536-21c8-4a96-8ed9-28c18447ef19"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "e1d605bd-dbc1-4394-8825-bdce882ade55", "logId": "dcf59426-0d4c-48fe-9b57-302ca944622a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1d605bd-dbc1-4394-8825-bdce882ade55", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321856623600}, "additional": {"logType": "detail", "children": [], "durationId": "caf28876-a3d3-4a6b-86fc-e355a3a78c89"}}, {"head": {"id": "bc3535a5-39a4-4387-bcde-a6922f507650", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321856957800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897f66f3-df11-4a8d-8da5-dc20d796aba9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321857058400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bba5ac3-4bd1-42b4-b284-1a71bdf27f9a", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321860175300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eeaa5df-7a87-402a-9ccd-123f70aaa297", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321874178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87e51cfa-3d60-4b72-821d-b260d5af4462", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321874333600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deac12d4-e027-4199-8c63-8725a577f810", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321886478700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3f7375-2b4a-40ec-8fe0-3e6f9b7810ff", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321886879800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be116a5-483d-4d73-84db-301b236fe676", "name": "default@CompileArkTS work[45] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321887760300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1daa5d-516f-47a0-9375-70709363c9f7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30321993649100, "endTime": 30323049838200}, "additional": {"children": ["6a3f9c4f-ee5c-4433-b4e5-ecf70ae2d833", "73971b69-bcf1-4343-a21e-4e216ada2fe2", "13bb25bb-68c4-45a4-b1df-43bcb629e69b", "54dca95c-4ee4-4101-9a44-250ce5e982a6", "e8bc6207-0148-40a6-9936-37ff049d9616", "b74712b0-b062-4a1a-a850-7173c09fe58f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "caf28876-a3d3-4a6b-86fc-e355a3a78c89", "logId": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56a5d43e-21f0-4196-97a7-c5ed5f54e525", "name": "default@CompileArkTS work[45] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321888494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23a8260-4dc6-46bf-96db-b6996f454372", "name": "default@CompileArkTS work[45] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321888584600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4642634a-0f76-41cc-ae35-f9dc9d82c368", "name": "CopyResources startTime: 30321888644200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321888646200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3491e18c-6899-4b5e-9090-82f2e26ccce2", "name": "default@CompileArkTS work[46] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321888723300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dfd9536-21c8-4a96-8ed9-28c18447ef19", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30323197388700, "endTime": 30323210056300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "caf28876-a3d3-4a6b-86fc-e355a3a78c89", "logId": "73f5a3b3-5cc3-4f9a-8111-187b7916bccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b69b5cb7-7e8e-4c1f-b09d-2a8e066127cf", "name": "default@CompileArkTS work[46] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321889300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bdc7d8e-25d5-4487-8b30-187b9c1488d7", "name": "default@CompileArkTS work[46] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321889370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46656a03-c3f6-4984-b83c-a1efb5b23b58", "name": "entry : default@CompileArkTS cost memory -4.747802734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321889479500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8137a7ca-9fb9-4f29-8dd1-daa609f7eba9", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321894054600, "endTime": 30321896895200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "4cbb9867-193c-4ef5-9fca-486ccce31a71", "logId": "f9752545-0ca4-4944-93f9-275250b6d12d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cbb9867-193c-4ef5-9fca-486ccce31a71", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321890562200}, "additional": {"logType": "detail", "children": [], "durationId": "8137a7ca-9fb9-4f29-8dd1-daa609f7eba9"}}, {"head": {"id": "95ac8839-e8b6-4d5c-9d5e-9f85916dc070", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321890864000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c4a99b8-50e9-4214-af73-a3ec5411b67f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321891020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c0a5a6-bce4-40eb-a4ac-379991648709", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321894117200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1d9a279-330c-4cbe-8ae1-4eef13cd4c2e", "name": "entry : default@BuildJS cost memory 0.12810516357421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321896711000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16c3ba43-8aee-4610-b10d-54328a3ffcd4", "name": "runTaskFromQueue task cost before running: 798 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321896835000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9752545-0ca4-4944-93f9-275250b6d12d", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321894054600, "endTime": 30321896895200, "totalTime": 2763400}, "additional": {"logType": "info", "children": [], "durationId": "8137a7ca-9fb9-4f29-8dd1-daa609f7eba9"}}, {"head": {"id": "d5e28257-84fd-4902-8253-3e4d5430f2d9", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321900805100, "endTime": 30321902658400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ad1a420e-b561-4043-b0f2-0f0af303d6d8", "logId": "0f7da332-7473-485a-b106-2fd5fe773ca2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad1a420e-b561-4043-b0f2-0f0af303d6d8", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321898189000}, "additional": {"logType": "detail", "children": [], "durationId": "d5e28257-84fd-4902-8253-3e4d5430f2d9"}}, {"head": {"id": "02a1d1fe-e863-4bf3-9971-2d6d2c6336d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321898499500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b17c1d-6553-49b6-a3ca-d1f8ec4d1997", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321898578500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb07a22-ff3a-4a35-98d2-dd4902978570", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321900817700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fe3467-4140-4b31-b7ea-8e95f3ca8947", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321901245400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aba5f66-f5f9-4a63-882b-4cf68ea9d4e7", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321902491600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "376a9a90-e71a-49b1-ba7a-87c45b7d189c", "name": "entry : default@CacheNativeLibs cost memory 0.0919342041015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321902592000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f7da332-7473-485a-b106-2fd5fe773ca2", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321900805100, "endTime": 30321902658400}, "additional": {"logType": "info", "children": [], "durationId": "d5e28257-84fd-4902-8253-3e4d5430f2d9"}}, {"head": {"id": "e5f3ffba-60f4-42ef-bdc3-1cba867eff00", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321993183800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d8424d-9ebc-4411-a9c5-3ec6273b9b3c", "name": "default@CompileArkTS work[45] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321993528900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bca35112-cd2c-4032-a245-936749cf642a", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321993628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917b1942-af6c-4398-9d1a-09715176831a", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321993681500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcbda454-94c9-4da3-b848-5ac8ff8132d8", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321993918100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad395d5-b8ff-4ef7-960a-9e4bc477651c", "name": "default@CompileArkTS work[46] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321996312300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80a2b0a8-b52e-4f63-8b51-2e031c5db819", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323050794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a3f9c4f-ee5c-4433-b4e5-ecf70ae2d833", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30321993735200, "endTime": 30321996761400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bf1daa5d-516f-47a0-9375-70709363c9f7", "logId": "1d9234a3-69bc-48b1-bb86-c9a51d4eceb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d9234a3-69bc-48b1-bb86-c9a51d4eceb4", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321993735200, "endTime": 30321996761400}, "additional": {"logType": "info", "children": [], "durationId": "6a3f9c4f-ee5c-4433-b4e5-ecf70ae2d833", "parent": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}}, {"head": {"id": "73971b69-bcf1-4343-a21e-4e216ada2fe2", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30321996776700, "endTime": 30321996871300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bf1daa5d-516f-47a0-9375-70709363c9f7", "logId": "7c588a89-7784-4e18-8d38-35adec6b7624"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c588a89-7784-4e18-8d38-35adec6b7624", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321996776700, "endTime": 30321996871300}, "additional": {"logType": "info", "children": [], "durationId": "73971b69-bcf1-4343-a21e-4e216ada2fe2", "parent": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}}, {"head": {"id": "13bb25bb-68c4-45a4-b1df-43bcb629e69b", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30321996882000, "endTime": 30321996947700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bf1daa5d-516f-47a0-9375-70709363c9f7", "logId": "ba76a152-bf58-4d91-baa7-35ce2d839e89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba76a152-bf58-4d91-baa7-35ce2d839e89", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321996882000, "endTime": 30321996947700}, "additional": {"logType": "info", "children": [], "durationId": "13bb25bb-68c4-45a4-b1df-43bcb629e69b", "parent": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}}, {"head": {"id": "54dca95c-4ee4-4101-9a44-250ce5e982a6", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30321996984900, "endTime": 30322969184000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bf1daa5d-516f-47a0-9375-70709363c9f7", "logId": "cebf2162-06fa-43b8-b7ca-52a9ef8f7886"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cebf2162-06fa-43b8-b7ca-52a9ef8f7886", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321996984900, "endTime": 30322969184000}, "additional": {"logType": "info", "children": [], "durationId": "54dca95c-4ee4-4101-9a44-250ce5e982a6", "parent": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}}, {"head": {"id": "e8bc6207-0148-40a6-9936-37ff049d9616", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30322969204300, "endTime": 30322976709200}, "additional": {"children": ["5372fc9d-d931-48d6-ac70-a55975b24dff", "60e2c966-e3fb-4966-a237-10a1a41dbaf7", "3679b8cf-4ce6-4fa0-8b83-bc6b9023c60a"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bf1daa5d-516f-47a0-9375-70709363c9f7", "logId": "3c81d79b-6b1e-472c-b276-bbddeeb66a74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c81d79b-6b1e-472c-b276-bbddeeb66a74", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30322969204300, "endTime": 30322976709200}, "additional": {"logType": "info", "children": ["98b8912b-6f57-495f-bb4c-866e48873b03", "516267b5-af3f-408b-845f-2fdd9775d922", "df884c47-96fc-46e8-bd5b-a869da48ec01"], "durationId": "e8bc6207-0148-40a6-9936-37ff049d9616", "parent": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}}, {"head": {"id": "5372fc9d-d931-48d6-ac70-a55975b24dff", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30322969225900, "endTime": 30322969232000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e8bc6207-0148-40a6-9936-37ff049d9616", "logId": "98b8912b-6f57-495f-bb4c-866e48873b03"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98b8912b-6f57-495f-bb4c-866e48873b03", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30322969225900, "endTime": 30322969232000}, "additional": {"logType": "info", "children": [], "durationId": "5372fc9d-d931-48d6-ac70-a55975b24dff", "parent": "3c81d79b-6b1e-472c-b276-bbddeeb66a74"}}, {"head": {"id": "60e2c966-e3fb-4966-a237-10a1a41dbaf7", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30322969235600, "endTime": 30322970107000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e8bc6207-0148-40a6-9936-37ff049d9616", "logId": "516267b5-af3f-408b-845f-2fdd9775d922"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "516267b5-af3f-408b-845f-2fdd9775d922", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30322969235600, "endTime": 30322970107000}, "additional": {"logType": "info", "children": [], "durationId": "60e2c966-e3fb-4966-a237-10a1a41dbaf7", "parent": "3c81d79b-6b1e-472c-b276-bbddeeb66a74"}}, {"head": {"id": "3679b8cf-4ce6-4fa0-8b83-bc6b9023c60a", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30322970115900, "endTime": 30322976695500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "e8bc6207-0148-40a6-9936-37ff049d9616", "logId": "df884c47-96fc-46e8-bd5b-a869da48ec01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df884c47-96fc-46e8-bd5b-a869da48ec01", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30322970115900, "endTime": 30322976695500}, "additional": {"logType": "info", "children": [], "durationId": "3679b8cf-4ce6-4fa0-8b83-bc6b9023c60a", "parent": "3c81d79b-6b1e-472c-b276-bbddeeb66a74"}}, {"head": {"id": "b74712b0-b062-4a1a-a850-7173c09fe58f", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30322976724600, "endTime": 30323049550500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bf1daa5d-516f-47a0-9375-70709363c9f7", "logId": "fe04b30c-c6ad-4395-8e60-5ea919d2eec3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe04b30c-c6ad-4395-8e60-5ea919d2eec3", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30322976724600, "endTime": 30323049550500}, "additional": {"logType": "info", "children": [], "durationId": "b74712b0-b062-4a1a-a850-7173c09fe58f", "parent": "bb2dd87c-da8e-43ca-a041-942d4dd48eda"}}, {"head": {"id": "1f0aa339-f449-467e-aa05-b7dbce7752d0", "name": "default@CompileArkTS work[45] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323059387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb2dd87c-da8e-43ca-a041-942d4dd48eda", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30321993649100, "endTime": 30323049838200}, "additional": {"logType": "info", "children": ["1d9234a3-69bc-48b1-bb86-c9a51d4eceb4", "7c588a89-7784-4e18-8d38-35adec6b7624", "ba76a152-bf58-4d91-baa7-35ce2d839e89", "cebf2162-06fa-43b8-b7ca-52a9ef8f7886", "3c81d79b-6b1e-472c-b276-bbddeeb66a74", "fe04b30c-c6ad-4395-8e60-5ea919d2eec3"], "durationId": "bf1daa5d-516f-47a0-9375-70709363c9f7", "parent": "dcf59426-0d4c-48fe-9b57-302ca944622a"}}, {"head": {"id": "b11ce774-48f6-4fd4-8ed2-c6f63c563447", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323210229800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6fccb3-fa3b-486f-a232-41ce65129c74", "name": "CopyResources is end, endTime: 30323210407700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323210446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de56f84-52b8-4de5-a1d4-90c5dc68221d", "name": "default@CompileArkTS work[46] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323210550400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f5a3b3-5cc3-4f9a-8111-187b7916bccf", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30323197388700, "endTime": 30323210056300}, "additional": {"logType": "info", "children": [], "durationId": "1dfd9536-21c8-4a96-8ed9-28c18447ef19", "parent": "dcf59426-0d4c-48fe-9b57-302ca944622a"}}, {"head": {"id": "62ecee66-5674-4d38-a258-9098f382f550", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323211504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf59426-0d4c-48fe-9b57-302ca944622a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321860163200, "endTime": 30323211813100, "totalTime": 1098237400}, "additional": {"logType": "info", "children": ["bb2dd87c-da8e-43ca-a041-942d4dd48eda", "73f5a3b3-5cc3-4f9a-8111-187b7916bccf"], "durationId": "caf28876-a3d3-4a6b-86fc-e355a3a78c89"}}, {"head": {"id": "084181bd-081c-420a-bc00-96dcfb4843cd", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323219128600, "endTime": 30323220436500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "f4419002-a3e1-4b86-bbf6-93016ff224f8", "logId": "a9e049fa-fa5d-43e3-b4b3-3eb69eb27afb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4419002-a3e1-4b86-bbf6-93016ff224f8", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323216976100}, "additional": {"logType": "detail", "children": [], "durationId": "084181bd-081c-420a-bc00-96dcfb4843cd"}}, {"head": {"id": "f7403876-825b-41fe-bf82-ad0c7f57396e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323217717900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed305c0a-9c24-4d07-85f0-6d25728b7d81", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323217836000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bcab6e5-bbee-47ea-b49d-bc1caab6d04c", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323219140900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a057cdad-9b1b-4846-8658-0aa155778b96", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323219396100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23dd40de-fbc0-473c-982c-777a6e8dc5c2", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323220239100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8906f3-0d4e-4754-92ee-3ce1a8142c49", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07508087158203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323220343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e049fa-fa5d-43e3-b4b3-3eb69eb27afb", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323219128600, "endTime": 30323220436500}, "additional": {"logType": "info", "children": [], "durationId": "084181bd-081c-420a-bc00-96dcfb4843cd"}}, {"head": {"id": "b869d8d5-2590-440d-ad36-6dff1762f069", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323233875200, "endTime": 30323803272300}, "additional": {"children": ["b99edc8a-56e0-4af7-bda4-ca28c8d0c4fd", "c979a9f6-8f67-44f0-bc1d-52290ae7bf2e", "855b8f2f-3d70-4647-9d64-ce53f12b00cd"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "68a7fa14-040f-497d-8f85-0f9f94353ed0", "logId": "19030cf3-9972-4c0a-b808-f235a174377b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68a7fa14-040f-497d-8f85-0f9f94353ed0", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323223768700}, "additional": {"logType": "detail", "children": [], "durationId": "b869d8d5-2590-440d-ad36-6dff1762f069"}}, {"head": {"id": "5ba01727-d5aa-444b-a12e-f5edaefec9ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323224174200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8370c7e-ae4b-4ae9-8483-c3b3ee6a1dc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323224298100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83bf9e24-d67f-430e-8d61-70b652ea0269", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323233889700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7d525ab-49a8-4645-bc1f-ad9339b9c101", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323247446800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f40c3277-8fd1-4d6b-ab3d-9c251362c997", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323247622600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38abbae7-1e09-49d7-a29f-f14a86989bdc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323247760100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f283be8c-df7a-4c91-a7de-6db78f8d5c7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323247864400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99edc8a-56e0-4af7-bda4-ca28c8d0c4fd", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323249033700, "endTime": 30323250574800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b869d8d5-2590-440d-ad36-6dff1762f069", "logId": "3ab370b5-6e38-4c87-a7e1-9dd67d41c70d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "281b2844-f3ef-4a7b-825f-b03d1b83bd82", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323250391800}, "additional": {"logType": "debug", "children": [], "durationId": "b869d8d5-2590-440d-ad36-6dff1762f069"}}, {"head": {"id": "3ab370b5-6e38-4c87-a7e1-9dd67d41c70d", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323249033700, "endTime": 30323250574800}, "additional": {"logType": "info", "children": [], "durationId": "b99edc8a-56e0-4af7-bda4-ca28c8d0c4fd", "parent": "19030cf3-9972-4c0a-b808-f235a174377b"}}, {"head": {"id": "c979a9f6-8f67-44f0-bc1d-52290ae7bf2e", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323251204400, "endTime": 30323253117900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b869d8d5-2590-440d-ad36-6dff1762f069", "logId": "7f503745-b887-42d1-95b9-008211d73fde"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d132acb2-0af0-47d7-bb67-1a0bff99d100", "name": "default@PackageHap work[47] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323251958400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "855b8f2f-3d70-4647-9d64-ce53f12b00cd", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30323252922000, "endTime": 30323802881800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b869d8d5-2590-440d-ad36-6dff1762f069", "logId": "93ebf351-92df-4a0f-86b9-11baca257374"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f87a5b53-7cd9-4456-a977-9186a3da287a", "name": "default@PackageHap work[47] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323252648100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51ef0b9f-cca8-46ac-96a3-18a6264ecf49", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323252736400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0790435-2069-44ea-8d6a-510f93a8f79e", "name": "default@PackageHap work[47] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323252834700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87008428-4ac1-42f4-8096-220782dc40ab", "name": "default@PackageHap work[47] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323252892800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f503745-b887-42d1-95b9-008211d73fde", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323251204400, "endTime": 30323253117900}, "additional": {"logType": "info", "children": [], "durationId": "c979a9f6-8f67-44f0-bc1d-52290ae7bf2e", "parent": "19030cf3-9972-4c0a-b808-f235a174377b"}}, {"head": {"id": "6cf4ff95-028e-49dd-8193-5e6c31b17687", "name": "entry : default@PackageHap cost memory 1.3122482299804688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323258388800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b987124-e760-49b1-b41f-9de346e2f4a5", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323279569200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29b4dc0a-fc2b-4772-8742-5975e319f590", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323802970300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5732ece9-0a96-42b2-b1b8-a744d5e6682d", "name": "default@PackageHap work[47] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323803154300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ebf351-92df-4a0f-86b9-11baca257374", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30323252922000, "endTime": 30323802881800}, "additional": {"logType": "info", "children": [], "durationId": "855b8f2f-3d70-4647-9d64-ce53f12b00cd", "parent": "19030cf3-9972-4c0a-b808-f235a174377b"}}, {"head": {"id": "19030cf3-9972-4c0a-b808-f235a174377b", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323233875200, "endTime": 30323803272300, "totalTime": 569013400}, "additional": {"logType": "info", "children": ["3ab370b5-6e38-4c87-a7e1-9dd67d41c70d", "7f503745-b887-42d1-95b9-008211d73fde", "93ebf351-92df-4a0f-86b9-11baca257374"], "durationId": "b869d8d5-2590-440d-ad36-6dff1762f069"}}, {"head": {"id": "09d81134-c904-4218-ba97-a4740a9d23d6", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323809062300, "endTime": 30323810976200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "30a3adb3-f78c-4903-b42d-ae526f6e9174", "logId": "e77d2ecd-87d5-4ebe-9560-e7cfeed20b31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30a3adb3-f78c-4903-b42d-ae526f6e9174", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323806082300}, "additional": {"logType": "detail", "children": [], "durationId": "09d81134-c904-4218-ba97-a4740a9d23d6"}}, {"head": {"id": "f04e7d07-bd83-4bcd-ad01-5411099f96a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323806566900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38d1875-84b8-4daf-b770-0b2776f7e601", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323806680700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b0426a-a067-4b48-a007-b55ac7926c9c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323809073000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87207729-8361-419c-8302-965ecc610473", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323809555600}, "additional": {"logType": "warn", "children": [], "durationId": "09d81134-c904-4218-ba97-a4740a9d23d6"}}, {"head": {"id": "f8f94705-bb6a-4bb8-983a-27c738f35e99", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323810091500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216202df-bd9e-480b-a623-fd8587d8768e", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323810177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93291910-41c2-49d9-ba09-836bca5193e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323810254200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "268a54a7-b50c-4657-bd82-10b44181a479", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323810323800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b9d956e-ea76-4169-a7a5-3a3f7dadbae6", "name": "entry : default@SignHap cost memory 0.13001251220703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323810758500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "582d35e9-cc68-4ed5-8f61-44037147a461", "name": "runTaskFromQueue task cost before running: 2 s 712 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323810878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e77d2ecd-87d5-4ebe-9560-e7cfeed20b31", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323809062300, "endTime": 30323810976200, "totalTime": 1792800}, "additional": {"logType": "info", "children": [], "durationId": "09d81134-c904-4218-ba97-a4740a9d23d6"}}, {"head": {"id": "00882b74-de55-4f78-9096-c3dde0e89d0e", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323814106300, "endTime": 30323818597000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "aa852980-c0df-4130-86e1-7266e4a5ba2c", "logId": "e1360bba-e6ab-4dc2-aeec-022452b60f9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa852980-c0df-4130-86e1-7266e4a5ba2c", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323812893200}, "additional": {"logType": "detail", "children": [], "durationId": "00882b74-de55-4f78-9096-c3dde0e89d0e"}}, {"head": {"id": "a934b896-7b12-4fb9-b6d1-542c3fc61fc0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323813250100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ae8dfa-5a26-47c1-ad51-897b32a4112e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323813344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98a0a6d-1436-4cb0-a87b-07b46d497752", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323814115900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9848dc38-29c2-4fc8-acc4-246bcb0f3542", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323818297800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db77619-2b95-4ed3-a50a-92202719c8aa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323818382800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16809ab-b68b-4542-9aec-7d880379dc67", "name": "entry : default@CollectDebugSymbol cost memory 0.239959716796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323818462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26845b63-488e-45df-9bc0-91510d905157", "name": "runTaskFromQueue task cost before running: 2 s 720 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323818543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1360bba-e6ab-4dc2-aeec-022452b60f9c", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323814106300, "endTime": 30323818597000, "totalTime": 4417800}, "additional": {"logType": "info", "children": [], "durationId": "00882b74-de55-4f78-9096-c3dde0e89d0e"}}, {"head": {"id": "7d8527ae-d4bf-4a1c-ae98-62f2cbbe871c", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323820039500, "endTime": 30323820327100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "625e9556-9229-4411-8f60-30473cbec410", "logId": "ee5d97ab-9155-43c1-aa11-44036c22aa4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "625e9556-9229-4411-8f60-30473cbec410", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323819995800}, "additional": {"logType": "detail", "children": [], "durationId": "7d8527ae-d4bf-4a1c-ae98-62f2cbbe871c"}}, {"head": {"id": "85433a6a-345e-4bf7-b32b-282b74249f03", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323820046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb47031d-1b61-42ae-88e6-094066347c8b", "name": "entry : assembleHap cost memory 0.0115814208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323820188900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6283ef7f-d715-40a9-837a-edd909fcba04", "name": "runTaskFromQueue task cost before running: 2 s 721 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323820273600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee5d97ab-9155-43c1-aa11-44036c22aa4e", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323820039500, "endTime": 30323820327100, "totalTime": 216700}, "additional": {"logType": "info", "children": [], "durationId": "7d8527ae-d4bf-4a1c-ae98-62f2cbbe871c"}}, {"head": {"id": "722aa296-669f-472a-9d73-72c5d6e27d91", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323828385800, "endTime": 30323828413000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "15350f89-a532-43a6-8c18-4842489aed33", "logId": "d54eb6f8-b851-40b6-92b4-3449aa3815b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d54eb6f8-b851-40b6-92b4-3449aa3815b4", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323828385800, "endTime": 30323828413000}, "additional": {"logType": "info", "children": [], "durationId": "722aa296-669f-472a-9d73-72c5d6e27d91"}}, {"head": {"id": "a161371c-121f-474b-9cbd-a9d8e903bac0", "name": "BUILD SUCCESSFUL in 2 s 730 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323828534100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "3b3a26ba-6d00-4233-a82b-83efa06d6a2c", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30321099332500, "endTime": 30323829189600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 54}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "95c69606-7f2b-47ea-8a48-e607552d27f8", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323829316700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11c715e5-3d2a-4b8a-9455-9644afd20b6d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323829430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "398db512-8cb8-432b-8be7-b8495f9a9c57", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323829489600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7603a8a9-fb77-43bd-96cf-e221fa40eae1", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323829577800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f82df6c-9b99-40f0-bd88-471a317735be", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323829693300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5407462-2706-4adc-8f94-011b2be3fc03", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323830046900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b7d353-fe23-4342-956d-92dd75d6ea53", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323830592400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e91b47b-b17f-4d7d-a0e0-f50d257800f4", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323830819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ffc9898-5298-44a1-891c-555ada36aad0", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323830889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "366d5fbc-e2dd-4795-a973-75fe2bb6b1e1", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323830987500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e43c7f3-ab01-4358-a6d5-8ed89997effe", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323831231700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b6b027-d9d2-4b67-91a1-42416a58ccfb", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323831986100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8018965-ec07-4ffe-adfe-96f2aa14c760", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323832193300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff46082-fa1e-476e-95e8-ec9f48cb4b32", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323832943400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5bad946-26bc-4b6b-a0e5-cd6f2b9c622b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323833036500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147de662-0fe2-4dda-9b6b-5e41c4155973", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323833090800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "768bfd35-5f8d-469b-bf69-6a28ad3359a1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323833139900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8382d33-c4db-4672-a9d0-be372c076649", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323833427800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57c544dc-71c9-462d-bc2c-d04153bdeb04", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323833632000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0da81132-e4da-4f76-91a1-a5a3d3afbc66", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323833809500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52ba2c1-172a-40b8-9625-c314ffb255af", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323834086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e713159-d4d1-4e37-97eb-7149f01fda13", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323834160200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99105ac2-231e-48a5-809a-64872b719528", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323834213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbbc6a20-c692-4bbe-8b1d-9e66f30e60bf", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323835882200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2236f88d-1797-4d7a-8985-b1624f916cb5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323836339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8a8ae6f-d797-4cb0-be0d-fd860cf59d06", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323837185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76de11de-e4af-487f-ba6f-7beece1184ac", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323837386100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b28381-86ba-40e3-8943-8cc0469fadb5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323837693200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6188b8ed-b0c3-4edc-8fec-110e7014d597", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323838216100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcccee80-3cc9-4d18-a73a-f975948875d6", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323838290500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03363486-c88f-44fe-bcbc-c7784926655b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323838498700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04db76a1-0869-4de0-b314-3f103dca3ce7", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323838752100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c4db6d-6829-4a6d-9de1-9f0c8e7f0dc5", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323839337800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed12407-6356-4b7b-ac0a-d434edc06aaa", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323840375800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63c2abbf-c292-465e-96b0-52fbc618c9c9", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323841000300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653f9613-5547-4c85-92fe-e4b8220abf68", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323841785300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8625b408-74a9-4e15-8df1-cd2d24ccf9e3", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323841983600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd34782a-6dc3-4979-be5c-fb3458043846", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323842168400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74fbd52c-f8e1-40e1-bf0f-c4c9d670524e", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323842730600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bb41cf-a2c9-4549-a785-e601f3971812", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323842966400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cac42ea-0567-4dd8-9dd5-166d07ffeca2", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323843037100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8309271a-88e7-438b-b455-487f4297c993", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323843097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d63da1c-a7f8-446e-8398-3c0a87a3d426", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323843822100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab15763-166d-4718-9965-a55dc88cf73f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323844137300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8743b26b-eb22-4aec-970e-f08a5eb9f441", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323844450100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94caf7bf-ae91-4bed-a982-be47cdee692f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323849814800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a7aca0-d162-4851-a8c7-1024d477c120", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323850084500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cdf7dd5-70d8-49e5-a308-507af28db292", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323850286600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93bfdf11-655f-47e5-9f63-11cf96ac8c78", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323850353800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a07b871-cd02-402b-a8a5-192decb8e218", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323850530300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22e2211f-ada0-4431-be74-20e59d5d9e8f", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323851122400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777a9c25-1b38-4667-8359-1973d0dad017", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323851321000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de56df2-95d0-4b02-920c-8442509b932f", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323851506400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89db0c11-2ca8-456d-8d8e-49bd69cf3eea", "name": "Incremental task entry:default@PackageHap post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323851725700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48b0b1fc-376a-4658-94a4-7dc3dd5ad664", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323851873800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170bcec3-97de-44c6-ad66-936c9e1b932a", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323851954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d97c37-3ded-444e-a502-2a1cfce99d03", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323852125900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4527e2ac-8373-450c-80bb-2b80e98befbd", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323854131800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80dd6762-981a-481d-a624-0cbcf7d14b48", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323854348200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b3bf8e4-7b83-4784-a1d7-22d9282c2887", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323854568200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa67ef51-eb0e-45ad-9f15-7aec64333de0", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323854753800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}