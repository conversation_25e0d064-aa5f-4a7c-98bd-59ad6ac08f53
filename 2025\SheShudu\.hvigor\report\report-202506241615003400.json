{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "06f5bc7e-c085-4d53-b773-0d12e8d36f4d", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27858011254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a35d03-7f91-4587-b027-0aa821e9a39c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27858015712200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a0a0fc8-1506-422a-b449-3ac1afa2900a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27858016763800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af4c923-a4e4-4078-9fec-3e2290daf58f", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27858024057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8555d19-da37-4c82-8eea-c6f3d544e4a6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932233682100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932241734600, "endTime": 27932699452800}, "additional": {"children": ["3701fc7d-2772-4c12-8684-033267310b0e", "488087e8-5507-457c-8e20-06a2de8f1681", "6a674f54-4aac-4c1d-9fac-db428f0333b4", "503eedbd-04c3-4be7-a1f7-e9704f701ff3", "4c0f7f82-2017-4bad-ab8a-d471e00a8cca", "afd621d6-932e-4781-8556-ac3c35c90e0b", "6b825ee1-3370-477b-90f5-f03c9895c738"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "019c6be7-adc1-4299-9b3e-39adff088ccb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3701fc7d-2772-4c12-8684-033267310b0e", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932241736800, "endTime": 27932256169900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "5d7bd54e-9075-4284-8fa6-96131b9c50d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "488087e8-5507-457c-8e20-06a2de8f1681", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932256200700, "endTime": 27932698252700}, "additional": {"children": ["e8ca1730-1469-4b7a-bcfc-e9380c631d2a", "4fb63e8e-5a8f-420b-9682-b8749fab9f20", "3af194f6-7c2c-43f4-86ad-eee1a276190a", "55ec151a-0743-462e-89f7-3042004714f1", "5aaa7fe2-d88d-4599-8b16-edf756fbdf25", "9607ef64-f6ad-491f-a4c0-444bb0dfa052", "55888858-5453-4f14-ba43-a1309ff4baac", "b85ee4aa-43d9-4db6-91a5-9fbc945d5a45", "4f6877d0-6dd9-4adb-9de7-5d4a1de5a4b3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "b3f3fa82-87e8-4729-8b24-3f179607e609"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a674f54-4aac-4c1d-9fac-db428f0333b4", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932698273900, "endTime": 27932699443900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "9e2c5156-ff7a-41d6-a1af-9342a92ac50d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "503eedbd-04c3-4be7-a1f7-e9704f701ff3", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932699447900, "endTime": 27932699449000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "1fdbeeda-54ed-49f1-b19a-c51c565d2cb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c0f7f82-2017-4bad-ab8a-d471e00a8cca", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932244802900, "endTime": 27932244845100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "1e079bcd-f38f-4fab-8b83-cb1fc24da451"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e079bcd-f38f-4fab-8b83-cb1fc24da451", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932244802900, "endTime": 27932244845100}, "additional": {"logType": "info", "children": [], "durationId": "4c0f7f82-2017-4bad-ab8a-d471e00a8cca", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "afd621d6-932e-4781-8556-ac3c35c90e0b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932250454600, "endTime": 27932250482500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "17683cfc-f1ba-41ae-946b-8cba36c5a25c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17683cfc-f1ba-41ae-946b-8cba36c5a25c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932250454600, "endTime": 27932250482500}, "additional": {"logType": "info", "children": [], "durationId": "afd621d6-932e-4781-8556-ac3c35c90e0b", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "a2975e3b-4f21-42fb-9c70-165ea495491d", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932250541200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "531f0aae-bfae-4f07-bf99-d7f93161a682", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932255285100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d7bd54e-9075-4284-8fa6-96131b9c50d7", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932241736800, "endTime": 27932256169900}, "additional": {"logType": "info", "children": [], "durationId": "3701fc7d-2772-4c12-8684-033267310b0e", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "e8ca1730-1469-4b7a-bcfc-e9380c631d2a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932263620900, "endTime": 27932263628700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "34f880e7-92c0-42b0-8fbf-4dfd411eeea1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fb63e8e-5a8f-420b-9682-b8749fab9f20", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932263646100, "endTime": 27932267017500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "fd18f41e-e884-4b7e-939c-0b761aea9ba4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3af194f6-7c2c-43f4-86ad-eee1a276190a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932267034700, "endTime": 27932531150300}, "additional": {"children": ["9e8efaba-9f38-4bcc-9a22-aba217e1d3d2", "0c247257-cc3b-4398-8c8b-16cb17bb88b8", "ea441941-8d0e-471b-9ba0-525ca012d0ed"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "f6ed8f36-0bde-4d1e-80cc-593c17ce5dd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55ec151a-0743-462e-89f7-3042004714f1", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932531166500, "endTime": 27932576613700}, "additional": {"children": ["e67f3732-c2ce-4976-a3f7-848e5810eaf7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "b74846d3-c71f-4ced-9484-c97b99971e1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5aaa7fe2-d88d-4599-8b16-edf756fbdf25", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932576622400, "endTime": 27932673196000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "79f0aae9-8b36-4f9c-8f83-931f34529159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9607ef64-f6ad-491f-a4c0-444bb0dfa052", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932674100200, "endTime": 27932686669700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "84be647f-d89f-403d-bc5e-05ea1fdabee9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55888858-5453-4f14-ba43-a1309ff4baac", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932686689600, "endTime": 27932698076200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "55d77f31-d01f-429e-b1c2-107e766cbf7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b85ee4aa-43d9-4db6-91a5-9fbc945d5a45", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932698094500, "endTime": 27932698242700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "c1f34922-596a-428b-b838-50fdac48d32f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34f880e7-92c0-42b0-8fbf-4dfd411eeea1", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932263620900, "endTime": 27932263628700}, "additional": {"logType": "info", "children": [], "durationId": "e8ca1730-1469-4b7a-bcfc-e9380c631d2a", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "fd18f41e-e884-4b7e-939c-0b761aea9ba4", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932263646100, "endTime": 27932267017500}, "additional": {"logType": "info", "children": [], "durationId": "4fb63e8e-5a8f-420b-9682-b8749fab9f20", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "9e8efaba-9f38-4bcc-9a22-aba217e1d3d2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932267548500, "endTime": 27932267565800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3af194f6-7c2c-43f4-86ad-eee1a276190a", "logId": "efda40d9-0497-4b07-a54f-e75e6d129a9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "efda40d9-0497-4b07-a54f-e75e6d129a9b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932267548500, "endTime": 27932267565800}, "additional": {"logType": "info", "children": [], "durationId": "9e8efaba-9f38-4bcc-9a22-aba217e1d3d2", "parent": "f6ed8f36-0bde-4d1e-80cc-593c17ce5dd4"}}, {"head": {"id": "0c247257-cc3b-4398-8c8b-16cb17bb88b8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932270009600, "endTime": 27932529935200}, "additional": {"children": ["8100807c-2956-456a-8fb9-7681f878827b", "32e7fd4c-65d4-4d8d-a18b-61fef8b7f5dd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3af194f6-7c2c-43f4-86ad-eee1a276190a", "logId": "9f00dbc8-ee6a-4d5b-b0ac-1bf00b76e5d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8100807c-2956-456a-8fb9-7681f878827b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932270012200, "endTime": 27932277151500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c247257-cc3b-4398-8c8b-16cb17bb88b8", "logId": "43652892-197e-477f-bab5-5d7857a20785"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32e7fd4c-65d4-4d8d-a18b-61fef8b7f5dd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932277168500, "endTime": 27932529914000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0c247257-cc3b-4398-8c8b-16cb17bb88b8", "logId": "559c650b-148c-4bee-8f88-35442c0cdec2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60dd506-29f1-40c3-acd4-5844bfd70215", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932270020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd93e0a-e71f-48b6-9e49-c6846af0e6aa", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932277008000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43652892-197e-477f-bab5-5d7857a20785", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932270012200, "endTime": 27932277151500}, "additional": {"logType": "info", "children": [], "durationId": "8100807c-2956-456a-8fb9-7681f878827b", "parent": "9f00dbc8-ee6a-4d5b-b0ac-1bf00b76e5d5"}}, {"head": {"id": "5a8d1a41-e4dd-430f-8bbb-02351794e0de", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932277184800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f04c851b-0982-4098-9541-12e9acabeb20", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932288409800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b25ef91-b699-480c-ab97-39b5d17420a4", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932288538600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8457dce9-de19-41bd-87bc-c72402677fbd", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932288675300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d3d3a1-93af-4483-8cca-9c1e8ea40506", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932291000000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e395bc79-e236-4d92-a3c3-ccf185fc6fec", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932294099000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32283783-496f-4b51-83ef-677afa875422", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932300164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7be636f-b072-4618-a53b-27a2939aa5ce", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932325894500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9624567b-36b3-4edc-a886-a7d02bec87ce", "name": "Sdk init in 152 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932457984400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26dfd374-f001-4fb1-93a4-764f5fceb876", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932459742100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 15}, "markType": "other"}}, {"head": {"id": "308ac4fa-7eab-4e3f-a2ae-611805b8e9d1", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932460536600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 15}, "markType": "other"}}, {"head": {"id": "ccc7595e-b84e-44fd-b0b8-26c04179f601", "name": "Project task initialization takes 66 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932529449100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1edbe88-c2a4-433f-a8b0-6e0541ae8279", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932529601400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef057bb-962c-4cf0-839c-9f76c985e008", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932529668100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "324c8337-8e5c-468c-9c18-d08ccfdc6b20", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932529720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559c650b-148c-4bee-8f88-35442c0cdec2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932277168500, "endTime": 27932529914000}, "additional": {"logType": "info", "children": [], "durationId": "32e7fd4c-65d4-4d8d-a18b-61fef8b7f5dd", "parent": "9f00dbc8-ee6a-4d5b-b0ac-1bf00b76e5d5"}}, {"head": {"id": "9f00dbc8-ee6a-4d5b-b0ac-1bf00b76e5d5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932270009600, "endTime": 27932529935200}, "additional": {"logType": "info", "children": ["43652892-197e-477f-bab5-5d7857a20785", "559c650b-148c-4bee-8f88-35442c0cdec2"], "durationId": "0c247257-cc3b-4398-8c8b-16cb17bb88b8", "parent": "f6ed8f36-0bde-4d1e-80cc-593c17ce5dd4"}}, {"head": {"id": "ea441941-8d0e-471b-9ba0-525ca012d0ed", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932531113900, "endTime": 27932531133100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3af194f6-7c2c-43f4-86ad-eee1a276190a", "logId": "ce73a2e1-80d1-4a04-999d-05ceb7a292c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce73a2e1-80d1-4a04-999d-05ceb7a292c5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932531113900, "endTime": 27932531133100}, "additional": {"logType": "info", "children": [], "durationId": "ea441941-8d0e-471b-9ba0-525ca012d0ed", "parent": "f6ed8f36-0bde-4d1e-80cc-593c17ce5dd4"}}, {"head": {"id": "f6ed8f36-0bde-4d1e-80cc-593c17ce5dd4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932267034700, "endTime": 27932531150300}, "additional": {"logType": "info", "children": ["efda40d9-0497-4b07-a54f-e75e6d129a9b", "9f00dbc8-ee6a-4d5b-b0ac-1bf00b76e5d5", "ce73a2e1-80d1-4a04-999d-05ceb7a292c5"], "durationId": "3af194f6-7c2c-43f4-86ad-eee1a276190a", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "e67f3732-c2ce-4976-a3f7-848e5810eaf7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932533151700, "endTime": 27932576599200}, "additional": {"children": ["70242060-1e61-40f6-925e-a046214b8219", "c51f7f30-1970-4176-ac97-3dcd33233159", "3ebe6f51-8f58-4b32-9124-1a0284b7874b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "55ec151a-0743-462e-89f7-3042004714f1", "logId": "f6ec473c-46ec-4848-b245-4741cbff6841"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70242060-1e61-40f6-925e-a046214b8219", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932544747500, "endTime": 27932544770400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e67f3732-c2ce-4976-a3f7-848e5810eaf7", "logId": "5c24c5a5-ea72-440b-ba44-92fcd04a73f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c24c5a5-ea72-440b-ba44-92fcd04a73f4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932544747500, "endTime": 27932544770400}, "additional": {"logType": "info", "children": [], "durationId": "70242060-1e61-40f6-925e-a046214b8219", "parent": "f6ec473c-46ec-4848-b245-4741cbff6841"}}, {"head": {"id": "c51f7f30-1970-4176-ac97-3dcd33233159", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932549874500, "endTime": 27932572064200}, "additional": {"children": ["37873563-0744-4583-bfa7-83ed878813c8", "14688d54-c1bc-4ba5-af49-d9a13e9015ca"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e67f3732-c2ce-4976-a3f7-848e5810eaf7", "logId": "44e5c103-3368-4de4-9527-3d1eee84f323"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37873563-0744-4583-bfa7-83ed878813c8", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932549876900, "endTime": 27932553525300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c51f7f30-1970-4176-ac97-3dcd33233159", "logId": "7dea6dd7-7dbb-4e9f-a125-5d430feda2a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14688d54-c1bc-4ba5-af49-d9a13e9015ca", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932553548100, "endTime": 27932572048600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c51f7f30-1970-4176-ac97-3dcd33233159", "logId": "845f95c4-ab6a-4863-abd8-ced86bc5b921"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3aeeb548-886b-496c-8f50-53da11c705c6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932549884200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e400ea18-c110-4703-8e0c-83d4420191d8", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932553333800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dea6dd7-7dbb-4e9f-a125-5d430feda2a9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932549876900, "endTime": 27932553525300}, "additional": {"logType": "info", "children": [], "durationId": "37873563-0744-4583-bfa7-83ed878813c8", "parent": "44e5c103-3368-4de4-9527-3d1eee84f323"}}, {"head": {"id": "921f8a13-bfa1-404c-a840-01967cdb220b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932553563600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12b76bf-4834-4b2c-add3-9598e4f142ae", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc525a9-56c0-473b-859d-1118494753a6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563312900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2d86557-ee6d-45cb-bb64-4ca38bc89f55", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563537600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a515d94f-4a0e-415d-bf7e-9015bdc9d234", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82b7ad6b-a9bf-4c2d-9dfa-3ac4ee36cb28", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563740100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f17e0810-4725-4b48-9089-12c56799d540", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563806200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be24463c-04a9-4b00-802f-ed0ee5633737", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932563888900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dd7774e-a88b-41ba-9a7f-0442568109b9", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932571701100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa593ed-fb4a-4132-983c-ceddd0b01bcc", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932571890000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d37adc-17c0-478a-969a-aa03008c4426", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932571954000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b09ae6-65ef-4cea-adb5-65a9e4e3a289", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932572002900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "845f95c4-ab6a-4863-abd8-ced86bc5b921", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932553548100, "endTime": 27932572048600}, "additional": {"logType": "info", "children": [], "durationId": "14688d54-c1bc-4ba5-af49-d9a13e9015ca", "parent": "44e5c103-3368-4de4-9527-3d1eee84f323"}}, {"head": {"id": "44e5c103-3368-4de4-9527-3d1eee84f323", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932549874500, "endTime": 27932572064200}, "additional": {"logType": "info", "children": ["7dea6dd7-7dbb-4e9f-a125-5d430feda2a9", "845f95c4-ab6a-4863-abd8-ced86bc5b921"], "durationId": "c51f7f30-1970-4176-ac97-3dcd33233159", "parent": "f6ec473c-46ec-4848-b245-4741cbff6841"}}, {"head": {"id": "3ebe6f51-8f58-4b32-9124-1a0284b7874b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932576554400, "endTime": 27932576580500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e67f3732-c2ce-4976-a3f7-848e5810eaf7", "logId": "922d5f4c-1529-4014-b5d8-e5c7004646fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "922d5f4c-1529-4014-b5d8-e5c7004646fa", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932576554400, "endTime": 27932576580500}, "additional": {"logType": "info", "children": [], "durationId": "3ebe6f51-8f58-4b32-9124-1a0284b7874b", "parent": "f6ec473c-46ec-4848-b245-4741cbff6841"}}, {"head": {"id": "f6ec473c-46ec-4848-b245-4741cbff6841", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932533151700, "endTime": 27932576599200}, "additional": {"logType": "info", "children": ["5c24c5a5-ea72-440b-ba44-92fcd04a73f4", "44e5c103-3368-4de4-9527-3d1eee84f323", "922d5f4c-1529-4014-b5d8-e5c7004646fa"], "durationId": "e67f3732-c2ce-4976-a3f7-848e5810eaf7", "parent": "b74846d3-c71f-4ced-9484-c97b99971e1c"}}, {"head": {"id": "b74846d3-c71f-4ced-9484-c97b99971e1c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932531166500, "endTime": 27932576613700}, "additional": {"logType": "info", "children": ["f6ec473c-46ec-4848-b245-4741cbff6841"], "durationId": "55ec151a-0743-462e-89f7-3042004714f1", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "e96d405e-c2cb-4985-9bf8-8d3cd75e1e11", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932602133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cec19a0a-61d1-4128-865b-aae185f35d72", "name": "hvigorfile, resolve hvigorfile dependencies in 97 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932672979900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79f0aae9-8b36-4f9c-8f83-931f34529159", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932576622400, "endTime": 27932673196000}, "additional": {"logType": "info", "children": [], "durationId": "5aaa7fe2-d88d-4599-8b16-edf756fbdf25", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "4f6877d0-6dd9-4adb-9de7-5d4a1de5a4b3", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932673890200, "endTime": 27932674086700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "488087e8-5507-457c-8e20-06a2de8f1681", "logId": "0173f218-5abd-4a35-8625-889a6a10ca0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c51199fc-25ce-4d76-889e-184829c22281", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932673919200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0173f218-5abd-4a35-8625-889a6a10ca0b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932673890200, "endTime": 27932674086700}, "additional": {"logType": "info", "children": [], "durationId": "4f6877d0-6dd9-4adb-9de7-5d4a1de5a4b3", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "3e28a1ce-4760-4658-90db-6a3fcadadd53", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932675823900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b96b470-2022-4759-86a5-90858c8bb807", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932685813400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84be647f-d89f-403d-bc5e-05ea1fdabee9", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932674100200, "endTime": 27932686669700}, "additional": {"logType": "info", "children": [], "durationId": "9607ef64-f6ad-491f-a4c0-444bb0dfa052", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "12029e22-408f-4ef7-a9a5-aa3dc7146dd0", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932692759800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4e03121-0566-4bb9-b75c-9e5ed19d3037", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932692900700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb800099-a8f4-4c98-802b-5c67fbaafe93", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932695083700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833c5393-bc3b-48b1-a5b1-b2a1b8f6cd50", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932695211400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55d77f31-d01f-429e-b1c2-107e766cbf7a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932686689600, "endTime": 27932698076200}, "additional": {"logType": "info", "children": [], "durationId": "55888858-5453-4f14-ba43-a1309ff4baac", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "8b632186-393e-4c4c-b41d-b31f638a23a7", "name": "Configuration phase cost:435 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932698117900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f34922-596a-428b-b838-50fdac48d32f", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932698094500, "endTime": 27932698242700}, "additional": {"logType": "info", "children": [], "durationId": "b85ee4aa-43d9-4db6-91a5-9fbc945d5a45", "parent": "b3f3fa82-87e8-4729-8b24-3f179607e609"}}, {"head": {"id": "b3f3fa82-87e8-4729-8b24-3f179607e609", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932256200700, "endTime": 27932698252700}, "additional": {"logType": "info", "children": ["34f880e7-92c0-42b0-8fbf-4dfd411eeea1", "fd18f41e-e884-4b7e-939c-0b761aea9ba4", "f6ed8f36-0bde-4d1e-80cc-593c17ce5dd4", "b74846d3-c71f-4ced-9484-c97b99971e1c", "79f0aae9-8b36-4f9c-8f83-931f34529159", "84be647f-d89f-403d-bc5e-05ea1fdabee9", "55d77f31-d01f-429e-b1c2-107e766cbf7a", "c1f34922-596a-428b-b838-50fdac48d32f", "0173f218-5abd-4a35-8625-889a6a10ca0b"], "durationId": "488087e8-5507-457c-8e20-06a2de8f1681", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "6b825ee1-3370-477b-90f5-f03c9895c738", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932699420300, "endTime": 27932699435100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d6869a9d-c4bb-4e1b-abad-31f52d074f57", "logId": "2fd8e4ba-cad8-4541-82ad-01bb52395b43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2fd8e4ba-cad8-4541-82ad-01bb52395b43", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932699420300, "endTime": 27932699435100}, "additional": {"logType": "info", "children": [], "durationId": "6b825ee1-3370-477b-90f5-f03c9895c738", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "9e2c5156-ff7a-41d6-a1af-9342a92ac50d", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932698273900, "endTime": 27932699443900}, "additional": {"logType": "info", "children": [], "durationId": "6a674f54-4aac-4c1d-9fac-db428f0333b4", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "1fdbeeda-54ed-49f1-b19a-c51c565d2cb2", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932699447900, "endTime": 27932699449000}, "additional": {"logType": "info", "children": [], "durationId": "503eedbd-04c3-4be7-a1f7-e9704f701ff3", "parent": "019c6be7-adc1-4299-9b3e-39adff088ccb"}}, {"head": {"id": "019c6be7-adc1-4299-9b3e-39adff088ccb", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932241734600, "endTime": 27932699452800}, "additional": {"logType": "info", "children": ["5d7bd54e-9075-4284-8fa6-96131b9c50d7", "b3f3fa82-87e8-4729-8b24-3f179607e609", "9e2c5156-ff7a-41d6-a1af-9342a92ac50d", "1fdbeeda-54ed-49f1-b19a-c51c565d2cb2", "1e079bcd-f38f-4fab-8b83-cb1fc24da451", "17683cfc-f1ba-41ae-946b-8cba36c5a25c", "2fd8e4ba-cad8-4541-82ad-01bb52395b43"], "durationId": "d6869a9d-c4bb-4e1b-abad-31f52d074f57"}}, {"head": {"id": "eb7b65ce-f9be-4de9-9174-48a1575dc394", "name": "Configuration task cost before running: 463 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932699615600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87987f21-8a8e-4ef7-ac01-c4d7d494624d", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932703888900, "endTime": 27932713379100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "acb1cb40-c045-493c-a989-8542bca29365", "logId": "16000aea-efd8-4784-891c-df3413047078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acb1cb40-c045-493c-a989-8542bca29365", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932701079100}, "additional": {"logType": "detail", "children": [], "durationId": "87987f21-8a8e-4ef7-ac01-c4d7d494624d"}}, {"head": {"id": "b86a182a-cf46-495a-8bdf-f1aff21f5cbe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932701488100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bf55c9c-5266-4622-9b55-fc484c9ea04d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932701593900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a85f7f9a-5b8b-4cfe-b78e-51d2fbced6f8", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932703903900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413f4c31-8b10-4bff-8d86-4a34acf5a25f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932713136000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413292a2-c24e-42de-9f2c-b30b00b05dab", "name": "entry : default@PreBuild cost memory 0.31591033935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932713295900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16000aea-efd8-4784-891c-df3413047078", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932703888900, "endTime": 27932713379100}, "additional": {"logType": "info", "children": [], "durationId": "87987f21-8a8e-4ef7-ac01-c4d7d494624d"}}, {"head": {"id": "b91aeb84-02ad-40ae-a36c-e1b260edcb0a", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932719670800, "endTime": 27932721288900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d652ca3e-5f91-4540-92d0-ba40c1cc67db", "logId": "4de86603-907f-44be-92a6-e99822e684df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d652ca3e-5f91-4540-92d0-ba40c1cc67db", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932718480400}, "additional": {"logType": "detail", "children": [], "durationId": "b91aeb84-02ad-40ae-a36c-e1b260edcb0a"}}, {"head": {"id": "6ce81270-f68a-4f34-bbc3-6ec926f61bdf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932718871200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2679ad8-d9bd-4dee-ac76-a8459a0a1c92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932718974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dbfe7c6-7fb1-4760-9ab3-e1b809ff023f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932719681600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58da82f1-c95c-49d4-8618-203066187bd5", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932720331300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "badb5c71-7fcd-4c80-82ec-d6feff156c05", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932721118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42d654ac-2dd0-4405-98e0-0417fe5efac8", "name": "entry : default@GenerateMetadata cost memory 0.0954742431640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932721213300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de86603-907f-44be-92a6-e99822e684df", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932719670800, "endTime": 27932721288900}, "additional": {"logType": "info", "children": [], "durationId": "b91aeb84-02ad-40ae-a36c-e1b260edcb0a"}}, {"head": {"id": "e83f9553-e709-473f-874a-ef1e94881719", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932724931800, "endTime": 27932725309500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0288e850-cff4-46b6-8c2d-197f936bcace", "logId": "0c6408cf-480a-46b1-85ab-4549e179130c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0288e850-cff4-46b6-8c2d-197f936bcace", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932723815700}, "additional": {"logType": "detail", "children": [], "durationId": "e83f9553-e709-473f-874a-ef1e94881719"}}, {"head": {"id": "e6e0cd1e-7167-4962-9729-ce019f38b2b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932724641500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc8d9082-35cc-408a-ad52-7e7fe001b907", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932724762400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b490d2c5-6874-4bc2-9128-d3eb97b6337b", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932724939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba740443-122d-4c4b-a477-496b57756e61", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932725035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15105d11-15ea-46d6-be29-43bc461185a3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932725087900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ea56b57-5d6d-4fb9-ba48-ebc5cf596887", "name": "entry : default@ConfigureCmake cost memory 0.0367431640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932725158400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00037034-c138-44c9-8302-8bcdad5f7174", "name": "runTaskFromQueue task cost before running: 489 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932725236600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c6408cf-480a-46b1-85ab-4549e179130c", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932724931800, "endTime": 27932725309500, "totalTime": 288800}, "additional": {"logType": "info", "children": [], "durationId": "e83f9553-e709-473f-874a-ef1e94881719"}}, {"head": {"id": "36e37fa9-2b9e-4b86-9330-33bf940ecdef", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932728088100, "endTime": 27932730006600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "5fb334b2-2003-4f6f-b40b-7c5630386ea2", "logId": "254adba2-4873-4c47-832d-e7a63720c6b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fb334b2-2003-4f6f-b40b-7c5630386ea2", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932727014600}, "additional": {"logType": "detail", "children": [], "durationId": "36e37fa9-2b9e-4b86-9330-33bf940ecdef"}}, {"head": {"id": "0ac14d7b-3c16-4c94-9720-64a29d60157b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932727408900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce92e6c2-bc9b-47e9-ba5e-075b0478ec67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932727517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b193bec-59d6-4ef0-afb4-70ae814ed0e4", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932728097400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20108cb-64f4-401f-8340-6008d91d2bda", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932729607300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24c8a3e-baf4-419b-99d6-0d3296004a41", "name": "entry : default@MergeProfile cost memory 0.1070556640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932729873100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254adba2-4873-4c47-832d-e7a63720c6b5", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932728088100, "endTime": 27932730006600}, "additional": {"logType": "info", "children": [], "durationId": "36e37fa9-2b9e-4b86-9330-33bf940ecdef"}}, {"head": {"id": "faefdcae-375b-4629-95b7-13114ac47300", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932735036100, "endTime": 27932737763700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0ba313fa-05d0-4249-a0d6-3d8e7c851dbf", "logId": "f6ef05bd-9fce-4611-87e0-cc58bb19d16d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ba313fa-05d0-4249-a0d6-3d8e7c851dbf", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932733222800}, "additional": {"logType": "detail", "children": [], "durationId": "faefdcae-375b-4629-95b7-13114ac47300"}}, {"head": {"id": "3d32bcea-9192-45a7-953a-6dc5b7975cb0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932734154000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "556ae55d-3030-4d04-97c8-c48c0d71a606", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932734345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0c6556-5762-4d46-a6bd-3bf2022fc653", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932735047300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce48c21f-1f56-4f0a-8671-f676a52f63ca", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932735936300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750f79e9-d650-47d9-a59c-b3171bbc9636", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932737549500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a104ddf-6cde-44b0-882e-79312af4f151", "name": "entry : default@CreateBuildProfile cost memory 0.10555267333984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932737686100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6ef05bd-9fce-4611-87e0-cc58bb19d16d", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932735036100, "endTime": 27932737763700}, "additional": {"logType": "info", "children": [], "durationId": "faefdcae-375b-4629-95b7-13114ac47300"}}, {"head": {"id": "dfa5e96a-79eb-4554-8ab1-9ab31544cc68", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932743920900, "endTime": 27932744394300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4b04b943-242c-4eab-9feb-93f8debdf126", "logId": "89accf64-2393-4bd3-ae9f-cd458eb24991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b04b943-242c-4eab-9feb-93f8debdf126", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932742631900}, "additional": {"logType": "detail", "children": [], "durationId": "dfa5e96a-79eb-4554-8ab1-9ab31544cc68"}}, {"head": {"id": "4f239555-a0bc-46f0-97ae-e53bb0645754", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932743033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7b2e5e-e238-4326-8ead-a67d7b17b221", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932743140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "385a1e83-f02d-4258-ae12-26c7c7947d9e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932743932500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ca91b42-217e-4f69-abf1-a267485239fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932744048800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca7d7840-381e-44cc-8fc1-d1c74100e2ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932744106700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec467235-1b38-4891-bab8-bd2e75e32a21", "name": "entry : default@PreCheckSyscap cost memory 0.03696441650390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932744181400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fc92f9-52aa-4280-82fb-25444ec2dff4", "name": "runTaskFromQueue task cost before running: 508 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932744262100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89accf64-2393-4bd3-ae9f-cd458eb24991", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932743920900, "endTime": 27932744394300, "totalTime": 324000}, "additional": {"logType": "info", "children": [], "durationId": "dfa5e96a-79eb-4554-8ab1-9ab31544cc68"}}, {"head": {"id": "dd6c0083-ec54-42c3-8a54-98d53f2d3008", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932750654800, "endTime": 27932751229900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8dd8744b-69fb-438f-85cf-0160345ed05a", "logId": "68035f75-9632-451a-917f-ae1d22872cd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dd8744b-69fb-438f-85cf-0160345ed05a", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932746515400}, "additional": {"logType": "detail", "children": [], "durationId": "dd6c0083-ec54-42c3-8a54-98d53f2d3008"}}, {"head": {"id": "1befd9ec-444f-4bcc-a67b-9e1fe497fc50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932746879200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "205a41b4-197d-4209-afd4-4c9074e908d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932746981300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c00a6cd-affd-4321-b2dc-3b819ca9aa52", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932750668200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00083076-f0b8-4427-963f-c30774fdb7da", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932750881700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f251c20f-8044-49e1-a5ce-6d2c4f2adad3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0389862060546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932751083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f337058-73c1-4b30-8dd7-23e9ae1cd13a", "name": "runTaskFromQueue task cost before running: 514 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932751170600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68035f75-9632-451a-917f-ae1d22872cd0", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932750654800, "endTime": 27932751229900, "totalTime": 503700}, "additional": {"logType": "info", "children": [], "durationId": "dd6c0083-ec54-42c3-8a54-98d53f2d3008"}}, {"head": {"id": "5394d3d2-5240-4b26-8062-6ed88dd3b60d", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932754966900, "endTime": 27932757578900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "9714c7d2-bf96-4276-ba51-1e41ec121ffc", "logId": "cb7a87e2-2b70-4142-a5d0-030b40eb864b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9714c7d2-bf96-4276-ba51-1e41ec121ffc", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932752911800}, "additional": {"logType": "detail", "children": [], "durationId": "5394d3d2-5240-4b26-8062-6ed88dd3b60d"}}, {"head": {"id": "5b7ad1ba-62ba-426a-91d9-f872e70751b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932753264300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226b030b-f103-40a6-96a3-c0e06af764de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932753378200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922aecfd-26b6-4c3d-b8cd-950459008d5f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932754978700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12230bd5-44f2-4b07-82ff-079830f0dd6e", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932756981100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be9d6b60-b382-4201-bd8c-6e62c06e472d", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932757134700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e47e069f-d8cc-4500-9237-52d3c1ef3f92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932757230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40a08f32-92d7-4fe6-bbe0-e08c8010f5b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932757285500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf0c9cb8-7842-4c4a-9549-e0513b201f9a", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11922454833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932757370800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e11086-fbf2-4c51-a73d-b08a331c63aa", "name": "runTaskFromQueue task cost before running: 521 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932757451700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb7a87e2-2b70-4142-a5d0-030b40eb864b", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932754966900, "endTime": 27932757578900, "totalTime": 2472400}, "additional": {"logType": "info", "children": [], "durationId": "5394d3d2-5240-4b26-8062-6ed88dd3b60d"}}, {"head": {"id": "52543d51-6139-4a01-916d-e3f9274142a8", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761187100, "endTime": 27932761628600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "62f942f5-fbbc-48f1-b312-8e9809ae2397", "logId": "035ef625-ebf9-4aad-90df-41f2b8a55cfc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62f942f5-fbbc-48f1-b312-8e9809ae2397", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932759827600}, "additional": {"logType": "detail", "children": [], "durationId": "52543d51-6139-4a01-916d-e3f9274142a8"}}, {"head": {"id": "5715b7f7-289d-44fd-bec3-f5dde6f63246", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932760183400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a0df1e-1cb6-4cbf-b682-84740ac12923", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932760287900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f14cf42-70c8-4a96-9834-146848f078d6", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761203700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57159e74-6987-4e3c-a355-b215cbade1fd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761349400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7adb972a-fb96-4d5c-8d10-ec09a4afe839", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761410800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c680c590-0715-4d2f-af3a-cddb45bccf9e", "name": "entry : default@BuildNativeWithCmake cost memory 0.0377960205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761491500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d54c706-b7e2-4c0b-b0ec-1b6f0da0927f", "name": "runTaskFromQueue task cost before running: 525 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035ef625-ebf9-4aad-90df-41f2b8a55cfc", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932761187100, "endTime": 27932761628600, "totalTime": 369900}, "additional": {"logType": "info", "children": [], "durationId": "52543d51-6139-4a01-916d-e3f9274142a8"}}, {"head": {"id": "4fcf5862-2d57-47f3-a3dc-b751fa0ce975", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932764495500, "endTime": 27932768136300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "76eeedee-a817-4891-9969-8df69b9477c6", "logId": "94361085-85f9-49d1-8a87-a12ca9e6fdb4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76eeedee-a817-4891-9969-8df69b9477c6", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932763223700}, "additional": {"logType": "detail", "children": [], "durationId": "4fcf5862-2d57-47f3-a3dc-b751fa0ce975"}}, {"head": {"id": "f89b9762-1000-4661-a8ab-933eaafa3389", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932763668000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b0de7c9-5e6a-441d-9887-22ea020064d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932763779400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab961cee-b566-496c-8080-9df4bfc61903", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932764508700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39530a05-89e2-4bb1-9481-1180b2fca705", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932767929300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3a0a3e-d644-4e7d-95ac-87416c7aebf2", "name": "entry : default@MakePackInfo cost memory 0.13990020751953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932768064400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94361085-85f9-49d1-8a87-a12ca9e6fdb4", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932764495500, "endTime": 27932768136300}, "additional": {"logType": "info", "children": [], "durationId": "4fcf5862-2d57-47f3-a3dc-b751fa0ce975"}}, {"head": {"id": "50b172ec-f2c1-4da2-8990-a3479e725f2f", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932774650100, "endTime": 27932778608300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "2858853f-4f00-4b21-bf97-b9657aeeee54", "logId": "0ea94cff-50bb-4712-8f8f-93a2b13fa735"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2858853f-4f00-4b21-bf97-b9657aeeee54", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932770668300}, "additional": {"logType": "detail", "children": [], "durationId": "50b172ec-f2c1-4da2-8990-a3479e725f2f"}}, {"head": {"id": "427716d2-c3e0-44cb-8e8d-f873eeb876e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932771489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "610ff094-fede-4196-aca2-66cc5af52d16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932771606900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ee225e4-95a7-4da8-8bff-804369139362", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932774666300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e51dd71f-dbb7-4560-9d38-a6efed976c90", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932774901400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7badb08c-4c63-45e8-a369-43557b269f19", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932775769800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54450474-e6c8-4211-957d-6ad9661eee3d", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932777660000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0e39cd8-4919-47d1-81bc-c1e8e4c115fc", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932777830900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534deef9-78bd-4845-8209-64342acd4aec", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932778165000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "604e5ec0-dd2e-44a8-a8cc-3fd7c29f482c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932778257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd3dc52-bfc2-4812-a295-992546ad4e3b", "name": "entry : default@SyscapTransform cost memory 0.15460205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932778371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "effd396c-8a45-4bf9-b432-e0ecc086578e", "name": "runTaskFromQueue task cost before running: 542 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932778497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ea94cff-50bb-4712-8f8f-93a2b13fa735", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932774650100, "endTime": 27932778608300, "totalTime": 3822200}, "additional": {"logType": "info", "children": [], "durationId": "50b172ec-f2c1-4da2-8990-a3479e725f2f"}}, {"head": {"id": "fa6a0104-065c-4739-b02d-c75caf0525ec", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932781983700, "endTime": 27932784666800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dbfc8910-8447-43a2-8427-66cc0da50867", "logId": "7b3e3a6c-05cc-4c98-b11e-0a39c415f16e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbfc8910-8447-43a2-8427-66cc0da50867", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932780414300}, "additional": {"logType": "detail", "children": [], "durationId": "fa6a0104-065c-4739-b02d-c75caf0525ec"}}, {"head": {"id": "6dc6b986-09b6-4033-bf89-d4e55cabdeca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932780735100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c05c26d-c4dc-4a1c-b92a-babb886e2501", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932780859700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae5e3a6-a854-4925-8fb8-7dfa4adc5786", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932781996700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ef4f436-56b2-46eb-af56-fa944b9ad900", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932784457300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c55b82c-71f8-442d-b60f-fdc6829a2a7f", "name": "entry : default@ProcessProfile cost memory -5.393486022949219", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932784586000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b3e3a6c-05cc-4c98-b11e-0a39c415f16e", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932781983700, "endTime": 27932784666800}, "additional": {"logType": "info", "children": [], "durationId": "fa6a0104-065c-4739-b02d-c75caf0525ec"}}, {"head": {"id": "49900cae-3f47-44c3-ae7a-418b9a5f6ed1", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932788290900, "endTime": 27932794194000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8d7c7a4a-01b2-4455-a4f7-25fd7f0798bf", "logId": "d91e9eb9-78fc-4e88-8d8f-ce5311ab6fae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d7c7a4a-01b2-4455-a4f7-25fd7f0798bf", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932786292400}, "additional": {"logType": "detail", "children": [], "durationId": "49900cae-3f47-44c3-ae7a-418b9a5f6ed1"}}, {"head": {"id": "381d6154-7881-47b3-a81f-94e54f05e95f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932786647900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "172fe85c-0966-48a8-8076-cf1634b324c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932786774100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c7e49a7-85b4-4ee7-b4ad-c897cef108d9", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932788307300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab5dbc1-5e11-4e32-b4e9-7c34529ca781", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932793873500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fabd659-a42f-4dd8-8e5e-cc7fbc624bc9", "name": "entry : default@ProcessRouterMap cost memory 0.2032928466796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932794048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91e9eb9-78fc-4e88-8d8f-ce5311ab6fae", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932788290900, "endTime": 27932794194000}, "additional": {"logType": "info", "children": [], "durationId": "49900cae-3f47-44c3-ae7a-418b9a5f6ed1"}}, {"head": {"id": "0a7f34b8-c44d-4c83-b28b-a83b9ef47b17", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932798355900, "endTime": 27932799450600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "af9bdd70-4045-49a8-a083-bf5a6e2595ca", "logId": "127d8382-c462-4b57-a099-a87c9424cfe6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af9bdd70-4045-49a8-a083-bf5a6e2595ca", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932796944100}, "additional": {"logType": "detail", "children": [], "durationId": "0a7f34b8-c44d-4c83-b28b-a83b9ef47b17"}}, {"head": {"id": "3c8c16b7-092b-4a1d-9d09-933bfad8d88c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932797364600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c9bf5e-ad97-4f75-88fe-0db0a2cc81af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932797584100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a67ab3a-5943-4902-8f50-d5bd99c4012e", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932798367900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa71612b-003f-420d-9210-d1a3afc08c6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932798488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01cb00ea-1f03-4b65-9aa3-2d0d1e05716f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932798549100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66535333-ddbd-4767-ac74-cfea73d74a78", "name": "entry : default@BuildNativeWithNinja cost memory 0.058074951171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932799251000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b26313d-8e47-49c3-834c-d2da25671c79", "name": "runTaskFromQueue task cost before running: 563 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932799387100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "127d8382-c462-4b57-a099-a87c9424cfe6", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932798355900, "endTime": 27932799450600, "totalTime": 1009900}, "additional": {"logType": "info", "children": [], "durationId": "0a7f34b8-c44d-4c83-b28b-a83b9ef47b17"}}, {"head": {"id": "0702128b-2621-4e4b-a1ad-39465c110991", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932803759300, "endTime": 27932811390800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "027c2390-6f4e-4a97-ba2c-e8e9802f3466", "logId": "c86d5202-148b-4f05-898d-25f0bc294fa5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "027c2390-6f4e-4a97-ba2c-e8e9802f3466", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932801444300}, "additional": {"logType": "detail", "children": [], "durationId": "0702128b-2621-4e4b-a1ad-39465c110991"}}, {"head": {"id": "331c66cd-f90a-4979-b103-535a9544634a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932801788700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b9c33b-ca4c-4f36-aad4-7d016a155749", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932801883800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb1a37ff-4c49-4deb-938b-7f43b6e61422", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932802723000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c0d1751-dc68-4d07-9588-067b79fd09b7", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932806561500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8632e4b2-bf08-48e1-999a-dfae190010f0", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932809656900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9feb03ea-28ef-4efa-b1c3-aeef46f15a8e", "name": "entry : default@ProcessResource cost memory 0.17046356201171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932809834800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86d5202-148b-4f05-898d-25f0bc294fa5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932803759300, "endTime": 27932811390800}, "additional": {"logType": "info", "children": [], "durationId": "0702128b-2621-4e4b-a1ad-39465c110991"}}, {"head": {"id": "5d15676c-762e-4870-923b-4b8dd118cdf2", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932818457700, "endTime": 27932833608200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "689a90eb-e106-4135-94a3-01245ff07f87", "logId": "28f16de2-f865-4426-b4f7-86f46bceec86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "689a90eb-e106-4135-94a3-01245ff07f87", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932814962800}, "additional": {"logType": "detail", "children": [], "durationId": "5d15676c-762e-4870-923b-4b8dd118cdf2"}}, {"head": {"id": "19d5d7f7-6389-41b8-a5d6-ae11bf4b8cd0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932815525600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d788cdf8-b7f1-4535-a49b-73c46bc08ce2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932815636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e98b1384-4364-4cf3-b2fc-f7c863587b57", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932818470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac311a6-a0c1-44d7-8f59-48b9924424d7", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932833403300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e339ea9-a93c-4ac3-afb8-fbe67bfc2a18", "name": "entry : default@GenerateLoaderJson cost memory 0.7676010131835938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932833536000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f16de2-f865-4426-b4f7-86f46bceec86", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932818457700, "endTime": 27932833608200}, "additional": {"logType": "info", "children": [], "durationId": "5d15676c-762e-4870-923b-4b8dd118cdf2"}}, {"head": {"id": "a0a8eb26-ed40-4700-bf24-54c8a0c25dfa", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932844118700, "endTime": 27932847602800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "37e97ad3-02ce-4de0-9490-0de6fa6691be", "logId": "3a820013-a972-478e-8071-2496fc57c734"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37e97ad3-02ce-4de0-9490-0de6fa6691be", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932842697200}, "additional": {"logType": "detail", "children": [], "durationId": "a0a8eb26-ed40-4700-bf24-54c8a0c25dfa"}}, {"head": {"id": "f9722869-f547-4b0d-aeb7-da47cfdd0707", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932843256300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "149f2fcd-8680-4f74-8426-faa83f77db16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932843410500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6413df7-fd18-42c8-ac98-bdd1e80c11fc", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932844131200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e01a436f-d3b1-4aeb-bfb9-aa860fe0f980", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932846031900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "505659a9-bd73-4c92-8ad0-e61903c55214", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932846150600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f7dcfff-704a-40e0-90d0-91b61e4c8149", "name": "entry : default@ProcessLibs cost memory 0.12645721435546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932847215000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3357784e-b467-448e-8882-2a0a21951b57", "name": "runTaskFromQueue task cost before running: 611 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932847410300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a820013-a972-478e-8071-2496fc57c734", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932844118700, "endTime": 27932847602800, "totalTime": 3257100}, "additional": {"logType": "info", "children": [], "durationId": "a0a8eb26-ed40-4700-bf24-54c8a0c25dfa"}}, {"head": {"id": "69411fa0-c5f7-4f5d-8800-12821d69a951", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932853681900, "endTime": 27932878115100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f92e9db2-5472-4afb-ba97-8a318dd8a0fc", "logId": "d5a4c8e8-a46c-43c9-a178-01b32543eb6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f92e9db2-5472-4afb-ba97-8a318dd8a0fc", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932850116200}, "additional": {"logType": "detail", "children": [], "durationId": "69411fa0-c5f7-4f5d-8800-12821d69a951"}}, {"head": {"id": "cbdd33db-ab5f-45f5-832c-e61389b4a35f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932850498500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d65a0e1f-b251-4a38-827e-614f0dd97e08", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932850610000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09c3255-c171-49a4-bf5b-52df273bbdec", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932851465600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3f02ca7-f091-4b86-b3fc-2df8d777b0ae", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932853706800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0613003-6df3-443a-9387-29d32cc11727", "name": "Incremental task entry:default@CompileResource pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932877872600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e8ca073-9bbe-4691-9560-798acc06e680", "name": "entry : default@CompileResource cost memory 1.4096298217773438", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932878020700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a4c8e8-a46c-43c9-a178-01b32543eb6e", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932853681900, "endTime": 27932878115100}, "additional": {"logType": "info", "children": [], "durationId": "69411fa0-c5f7-4f5d-8800-12821d69a951"}}, {"head": {"id": "78cbe39b-981d-4bf4-82c2-9fcf2761102f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932884584900, "endTime": 27932886169600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f2f461e5-489e-4548-85e5-70bb179bbf2f", "logId": "b4300e45-1ce9-4ddf-b548-bfc9efaff4fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2f461e5-489e-4548-85e5-70bb179bbf2f", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932881494800}, "additional": {"logType": "detail", "children": [], "durationId": "78cbe39b-981d-4bf4-82c2-9fcf2761102f"}}, {"head": {"id": "09303fcb-df55-4606-b58c-197d615a9b18", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932881911700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d74e6575-4244-4ea8-9362-facf75bd8869", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932882022400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b86e7a-f725-44f7-91fa-6f747de93868", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932884598100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78cb2384-379c-4580-b868-9269942e4f86", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932885000800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af57175d-7ad8-44da-b757-315e103f1dcd", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932885969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c16d248-7778-4378-9bc7-5d327970c1f1", "name": "entry : default@DoNativeStrip cost memory 0.07721710205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932886092900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4300e45-1ce9-4ddf-b548-bfc9efaff4fc", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932884584900, "endTime": 27932886169600}, "additional": {"logType": "info", "children": [], "durationId": "78cbe39b-981d-4bf4-82c2-9fcf2761102f"}}, {"head": {"id": "c27f8291-67bb-429b-9173-6cebb602d484", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932893525300, "endTime": 27932911031000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "d77029e7-0864-45ab-b935-315a0ea37aa5", "logId": "d779aed0-869d-4f7a-a1fc-dc89b2af7a22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d77029e7-0864-45ab-b935-315a0ea37aa5", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932888044500}, "additional": {"logType": "detail", "children": [], "durationId": "c27f8291-67bb-429b-9173-6cebb602d484"}}, {"head": {"id": "63a25c61-1cbe-48ea-bbb8-259959e7ec7c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932888556000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e168728a-67fc-4340-8459-a2c426752d13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932888668000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa2a6f8-ea42-475d-93bb-ae2ce4ec6fd4", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932893539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626bc5fc-a1cb-484a-8432-a414e7e1e717", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932910163000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dd4af98-215f-45b7-815b-00cd88b6a0bc", "name": "entry : default@CompileArkTS cost memory 0.681915283203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932910387300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d779aed0-869d-4f7a-a1fc-dc89b2af7a22", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932893525300, "endTime": 27932911031000}, "additional": {"logType": "info", "children": [], "durationId": "c27f8291-67bb-429b-9173-6cebb602d484"}}, {"head": {"id": "7555c8f6-ce9e-47b3-a97c-43a6926f0f44", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932922849100, "endTime": 27932930233800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "c18edf85-8953-43c7-b7a5-c957db1d9d07", "logId": "496a51da-c8d4-4701-ab61-f4172ea65522"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c18edf85-8953-43c7-b7a5-c957db1d9d07", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932917055100}, "additional": {"logType": "detail", "children": [], "durationId": "7555c8f6-ce9e-47b3-a97c-43a6926f0f44"}}, {"head": {"id": "eb6cee64-c74b-4ee6-9453-9467745fb2f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932917522500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c19fd0bf-2c3a-4f85-97b7-78b7d7f0aea6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932917661500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d9a149-8cca-4785-b881-7f3e3cf64155", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932922868700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d47359-2132-4329-ad36-8625af241c84", "name": "entry : default@BuildJS cost memory 0.12847137451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932929738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c6d292-087b-4860-8c19-376038a4cf52", "name": "runTaskFromQueue task cost before running: 693 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932930114800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "496a51da-c8d4-4701-ab61-f4172ea65522", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932922849100, "endTime": 27932930233800, "totalTime": 7227300}, "additional": {"logType": "info", "children": [], "durationId": "7555c8f6-ce9e-47b3-a97c-43a6926f0f44"}}, {"head": {"id": "9764fd09-688c-4469-b7da-5cb5b3ac4d7f", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932940064400, "endTime": 27932944024800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ec2a6eb5-49fa-4cb5-befc-f1d94932b217", "logId": "9bfdf267-80b5-46af-bac9-d55563991230"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec2a6eb5-49fa-4cb5-befc-f1d94932b217", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932934808300}, "additional": {"logType": "detail", "children": [], "durationId": "9764fd09-688c-4469-b7da-5cb5b3ac4d7f"}}, {"head": {"id": "c8604991-4e90-4253-a540-e3b3c7dba255", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932935363100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de4998f-c1f3-4a33-9e56-3b8bc4a166cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932935567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2b7156-bc36-42ba-ba07-45db8c6c8e0f", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932940109500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5499f7-ca71-49e6-8410-5d599968bbca", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932942169000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d26f8e-f44f-4534-8f43-d9d5bb441134", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932943653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a3947ac-b024-4e24-9ab0-cd5178a2e470", "name": "entry : default@CacheNativeLibs cost memory 0.09283447265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932943828100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bfdf267-80b5-46af-bac9-d55563991230", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932940064400, "endTime": 27932944024800}, "additional": {"logType": "info", "children": [], "durationId": "9764fd09-688c-4469-b7da-5cb5b3ac4d7f"}}, {"head": {"id": "eee55711-b28e-4f33-a036-2e9b7cf9fa9c", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932948476300, "endTime": 27932950174800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "b12b8cac-d830-41b0-b418-52c1b442d99b", "logId": "f9981c68-a5b5-4f91-8db2-9219f1a0270c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b12b8cac-d830-41b0-b418-52c1b442d99b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932946152000}, "additional": {"logType": "detail", "children": [], "durationId": "eee55711-b28e-4f33-a036-2e9b7cf9fa9c"}}, {"head": {"id": "0c3346d3-f6de-4b80-84e0-cba92e37aa28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932946623800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e906ad68-9731-4237-a797-956490eb5e7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932946959500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f91a70-c792-4c57-8071-0566fb29de83", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932948501200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68665290-57c8-4df4-ac2b-db380b436992", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932948870900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55631234-aa5c-4052-b8a6-6e9c1359c99b", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932949957000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35dba1e-ae7a-44b1-ac42-36b2af14aade", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0735321044921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932950098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9981c68-a5b5-4f91-8db2-9219f1a0270c", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932948476300, "endTime": 27932950174800}, "additional": {"logType": "info", "children": [], "durationId": "eee55711-b28e-4f33-a036-2e9b7cf9fa9c"}}, {"head": {"id": "a8f0598c-eebd-49ed-b9b6-ddb8f87c5444", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932965999700, "endTime": 27932993121800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e6a6eac2-7812-4b86-8c1c-2f681b65a950", "logId": "c1b2f879-ee6d-40e9-aaf0-e7bdc29dc26d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6a6eac2-7812-4b86-8c1c-2f681b65a950", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932952874600}, "additional": {"logType": "detail", "children": [], "durationId": "a8f0598c-eebd-49ed-b9b6-ddb8f87c5444"}}, {"head": {"id": "d2ddccd0-67ee-43e0-abca-28aa07a08e6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932953235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69750e38-3939-448d-a26d-1ee4107d6962", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932953437500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "201e921a-3235-47a6-9538-ca929d57765d", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932966013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5217c229-a5ac-4cb2-a078-0bdaaa94fdc3", "name": "Incremental task entry:default@PackageHap pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932992622300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17f819d1-70fd-4f36-a576-476df82d2118", "name": "entry : default@PackageHap cost memory 0.8438720703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932992981200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1b2f879-ee6d-40e9-aaf0-e7bdc29dc26d", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932965999700, "endTime": 27932993121800}, "additional": {"logType": "info", "children": [], "durationId": "a8f0598c-eebd-49ed-b9b6-ddb8f87c5444"}}, {"head": {"id": "db9d5a56-0697-4c62-adb9-cfcc37f252d0", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********0000, "endTime": 27933005260700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "deb70de7-6983-41d3-9609-b81aad36b654", "logId": "296d8e46-64b7-4369-93e5-7f78eff2a224"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "deb70de7-6983-41d3-9609-b81aad36b654", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932998960800}, "additional": {"logType": "detail", "children": [], "durationId": "db9d5a56-0697-4c62-adb9-cfcc37f252d0"}}, {"head": {"id": "0617972a-b07d-497f-9cac-a1d7a133fdc3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932999462400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15687abc-65e2-4a98-b374-1f29d390e30a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932999703900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058e3a15-32b0-4712-a897-921a8ca88ca0", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933002465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70896e7f-a381-4b17-a3fa-416a80058542", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933002922800}, "additional": {"logType": "warn", "children": [], "durationId": "db9d5a56-0697-4c62-adb9-cfcc37f252d0"}}, {"head": {"id": "369b38d2-392f-4f6b-a8dc-2cfa9f02a243", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933003835400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1395248-04c9-4a2d-b68a-5009626dfd37", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933004215100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32154970-b4a8-45a2-bc8f-30356952630f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933004531600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6ccba58-f089-483a-afe5-45c86974e64d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933004733900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5a1140f-e557-420f-85b4-408031e733cc", "name": "entry : default@SignHap cost memory 0.12172698974609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933005097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b973f1-8eca-4853-9ff3-32517413d0b5", "name": "runTaskFromQueue task cost before running: 768 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933005199700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296d8e46-64b7-4369-93e5-7f78eff2a224", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********0000, "endTime": 27933005260700, "totalTime": 2733900}, "additional": {"logType": "info", "children": [], "durationId": "db9d5a56-0697-4c62-adb9-cfcc37f252d0"}}, {"head": {"id": "b87c94d7-b233-454f-a3b2-f6eaff060692", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933011919300, "endTime": 27933017445500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a9ae85d9-ffe3-49b1-b16a-6a2b709d9e45", "logId": "9f61c5e7-4b15-4db6-af5c-fa5fde8b8c28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9ae85d9-ffe3-49b1-b16a-6a2b709d9e45", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933010263200}, "additional": {"logType": "detail", "children": [], "durationId": "b87c94d7-b233-454f-a3b2-f6eaff060692"}}, {"head": {"id": "4e802201-f40f-45d7-8144-9ed110e9f41e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933010893700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5772360-8260-46e8-8fd3-a6a40ff8a69a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933011032000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb68cbc1-0885-4165-88fe-1d01e5868159", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933011932600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad402ad4-30ee-4b39-9abf-ae06de693679", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933017022900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc780147-1f93-4da5-b088-398fdb11a3a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933017138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93f24beb-656e-48d5-b82a-52f632e959ca", "name": "entry : default@CollectDebugSymbol cost memory 0.2408599853515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933017254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d62cd5de-dd76-49ed-a598-75d72b3e1f8a", "name": "runTaskFromQueue task cost before running: 781 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933017352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f61c5e7-4b15-4db6-af5c-fa5fde8b8c28", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933011919300, "endTime": 27933017445500, "totalTime": 5413500}, "additional": {"logType": "info", "children": [], "durationId": "b87c94d7-b233-454f-a3b2-f6eaff060692"}}, {"head": {"id": "208e4372-a0e7-498a-9f7d-18e2f4d03094", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933019356100, "endTime": 27933019752800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a8cc0666-7444-49d7-8a5a-ca2ea4e11e57", "logId": "fd2f1480-83e5-4148-b373-1f659db6925c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8cc0666-7444-49d7-8a5a-ca2ea4e11e57", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933019258600}, "additional": {"logType": "detail", "children": [], "durationId": "208e4372-a0e7-498a-9f7d-18e2f4d03094"}}, {"head": {"id": "e5bccb20-74bf-4a8d-b06e-3f8fb684cc42", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933019372400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "886f95fe-d003-4907-8042-042c29a060a7", "name": "entry : assembleHap cost memory 0.011627197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933019584000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f0967f-f66b-4d59-bcc1-2d3ef6782e44", "name": "runTaskFromQueue task cost before running: 783 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933019690100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2f1480-83e5-4148-b373-1f659db6925c", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933019356100, "endTime": 27933019752800, "totalTime": 316000}, "additional": {"logType": "info", "children": [], "durationId": "208e4372-a0e7-498a-9f7d-18e2f4d03094"}}, {"head": {"id": "63d852d2-bb82-4060-a8bb-865886bbc9c3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933032553000, "endTime": 27933032573700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "93d505d8-a07c-4f33-be9b-2a580a4e98e4", "logId": "cbd57d54-2cba-4f78-8741-dd22b31e1e7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbd57d54-2cba-4f78-8741-dd22b31e1e7e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933032553000, "endTime": 27933032573700}, "additional": {"logType": "info", "children": [], "durationId": "63d852d2-bb82-4060-a8bb-865886bbc9c3"}}, {"head": {"id": "da2b8902-e1c7-45e0-bb99-c17d0dd3c596", "name": "BUILD SUCCESSFUL in 796 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933032614500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "e3fdeed3-687e-4df6-b6a4-cc0f8837cfbd", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27932237205400, "endTime": 27933033271200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 15}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d8a8e6e6-cbf4-47b6-8d95-50d9f04f601f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933033354000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ea68aa6-2e75-49c5-979c-e19a8b13ea15", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933033469200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "575ca802-cd67-4c90-bbf5-f2542203208b", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933033530300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d508f2-37e8-458a-bf84-8ce63a124fd9", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933033581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a50ef6-95ca-4149-a149-0876bc194eec", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933033647500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd37fc9e-0777-466e-a519-4452cb2e8d3f", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933034112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea8b9904-bbd7-4277-b830-5ccf4de5bf0d", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933034935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdae1d03-05ab-4af3-b79d-9fda8a57a90e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933035218500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d60761-fdcb-4877-b88b-22dc13661bbd", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933035375800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6767808a-5d35-4725-8158-d1b4bd1d1978", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933035741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7978d61c-60fc-494e-accf-020603679412", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933036102500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51400ee3-08fb-4b78-826f-36bee4c553f9", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933037556500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ec3dbeb-fe26-4b12-82b9-04a0959cf760", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933038292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da2d4d0c-04ef-4991-b069-33152103e944", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933038406600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf4f522-16a6-477b-b7e6-9bd4ac8c90f8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933038492100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b437b7f8-52c3-4919-b9a6-0c8af499f1d3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933038588400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c6145f-efa3-439f-bb91-f751664e5578", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933038667100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d829534-7693-4556-86fb-9fbe0e22eb06", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933040966100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec52544-7910-4f74-be36-854f01126a0b", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933041479300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeddcc6e-ed60-4af5-ac7f-ec522366def3", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933041827800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee424e96-09e1-455b-bf49-1bfb51a114dd", "name": "Incremental task entry:default@ProcessLibs post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933042133300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ea40efb-cf21-4ada-b3fb-c711e57f266f", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933042205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64f2f43-bc73-4139-b50d-72792c3b53b5", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933042253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f16bab63-82f3-42c0-b05d-fda41fd0d66e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933042575700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00bfdc87-9822-466b-b7f7-1c03caeb7e98", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933043759300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c4a2a36-fb2a-4145-8bb8-df4f37957d96", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933044486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeec9dec-991e-4a0a-ac63-ac4df4de9f2d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933045449700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438f6289-7ce1-48bd-a1f2-d8714cf8299e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933045758100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a6509c-40bb-4410-9134-1224fd4ab978", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933046240900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cd9e4ba-4fcd-41c5-a015-89262f8c93e1", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933046944700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "903173ab-d0b1-4199-b153-7a5fc81995f5", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933047201700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dade6f9d-fee8-458c-b891-819c63c20202", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933047273900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3b785a2-7f7d-4ac8-9581-c68ae8c1d462", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933047486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aa36d44-178c-4ae0-9f2c-8a52c6f5637e", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933047712100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a791d79-16a2-4cdc-bc54-344a37fd79d1", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933047939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7be7d6-682b-4936-8beb-5517846bb142", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933048167100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a83893-fbd1-4dc4-a8b3-f05c29b49a8a", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933048455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f507a20a-66cb-436e-87d0-f6db5c58e909", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933050752900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6950123e-a9d4-45ff-b2b5-5b47dac84b51", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933051135400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "407ab2c7-e125-4c98-8882-3e412a70cfec", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933051406700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5e6faf-1a35-452d-b18c-51dc90aaac9b", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933051645800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}