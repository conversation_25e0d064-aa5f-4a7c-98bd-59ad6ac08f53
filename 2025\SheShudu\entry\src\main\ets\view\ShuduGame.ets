import { hilog } from "@kit.PerformanceAnalysisKit";
import { window,ComponentUtils, componentUtils, curves } from "@kit.ArkUI";
import { common } from "@kit.AbilityKit";
import { preferences, unifiedDataChannel, uniformTypeDescriptor } from "@kit.ArkData";
import { image } from "@kit.ImageKit";
import { BusinessError } from "@kit.BasicServicesKit";
import fs from '@ohos.file.fs';
import { ChessPieces } from "../model/GameModels";
import { ArrayList, HashMap, HashSet } from "@kit.ArkTS";
import { CommonDialog } from "../util/CommonDialog";
import MyAvPlayer from "../util/MyAvPlayer";


@Builder
export function ShuduGameBuilder(name: string, param: Object) {
  ShuduGame()
}

class Tmp {
  iconStr2: ResourceStr = $r("app.media.qie");

  set(val: Resource) {
    this.iconStr2 = val;
  }
}



@Component
export default struct ShuduGame{
  pageInfos: NavPathStack = new NavPathStack();

  UIContext = this.getUIContext();
  uiAbilityContext = this.UIContext.getHostContext() as common.UIAbilityContext;
  private windowClass: window.Window = AppStorage.get<window.Window>('windowClass')!;
  private naviIndicatorHeight:number = AppStorage.get<number>('naviIndicatorHeight')!;
  private statusBarHeight:number = AppStorage.get<number>('statusBarHeight')!;

  private settings: RenderingContextSettings = new RenderingContextSettings(true);
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.settings);
  private datas0:Array<ChessPieces>=[];
  private datas1:Array<ChessPieces>=[];

  private shud:number[][]=Array(9).fill(0).map(() => Array(9).fill(0));
  private  cellsize=0;
  //private mntStack:Stack<ChessPieces> = new Stack<ChessPieces>();

  private wf:componentUtils.Offset={x:0,y:0};
  private  chooseOne:ChessPieces|undefined=undefined;
  @State usedDatas:Array<ChessPieces>=[];
  @State modeFlag:number=1;//游戏模式 1-经典数字，0-卡通动物
  @State initFlag:number=1;//是否初始化棋盘标志 1-初始话，0-不初始化
  @State lastX:number=0;
  @State lastY:number=0;
  @State short:number=0;
  @State showInstructionDialog:boolean=false;
  //菜单相关属性
  private iconStr: ResourceStr = $r("app.media.ss");
  @State select: boolean = true;

  //音乐播放器控制类
  private  mavplr:MyAvPlayer=new MyAvPlayer()
  private  musicOpen:boolean=false;

  //动画控制
  @State gameSatus:number=0;//0-游戏初始化，1-游戏成功，2-游戏失败
  @State animate: boolean = false;
  @State picColor:string[]=Array(9).fill('#ffffff');
  @State picBwith:number[]=Array(9).fill(0);;
  @State picRotae:number[]=Array(9).fill(0);;
  //难度控制
  private  difficulty:'easy' | 'medium' | 'hard'='easy'

  //操作控制
  @State operStatus:number=0;//0-点击模式，1-拖动模式

  //配置读取
  private  dataPreferences: preferences.Preferences | null = null;

  @Builder
  SubMenu() {
    Menu() {
      MenuItem({ startIcon: $r("app.media.number6"),content: "数独经典模式" })
        .onChange((selected) => {
          if(selected){
            this.initFlag=1;
            this.modeFlag=1;
            this.usedDatas=this.datas1;
            this.initCnavas()
          }
        })
      MenuItem({ startIcon: $r("app.media.number7"),content: "自在经典模式" })
        .onChange((selected) => {
          if(selected){
            this.initFlag=0;
            this.modeFlag=1;
            this.usedDatas=this.datas1;
            this.initCnavas()
          }
        })
      MenuItem({ startIcon: $r("app.media.animal8"),content: "趣味卡通模式" })
        .onChange((selected) => {
          if(selected){
            this.initFlag=1;
            this.modeFlag=0;
            this.usedDatas=this.datas0;
            this.initCnavas()
          }
        })
      MenuItem({ startIcon: $r("app.media.animal7"),content: "自在卡通模式" })
        .onChange((selected) => {
          if(selected){
            this.initFlag=0;
            this.modeFlag=0;
            this.usedDatas=this.datas0;
            this.initCnavas()
          }
        })
    }
  }

  @Builder
  MyMenu() {
    Menu() {
      MenuItem({ startIcon: $r("app.media.qie"), content: "游戏模式选择" ,builder: this.SubMenu})
        .onChange((selected) => {
          console.info("菜单选项1 select" + selected);
          let Str: Tmp = new Tmp();
          Str.set($r("app.media.niu"));
        })
      MenuItem({
        startIcon: $r('app.media.personlize'),
        content: "个人中心",
        endIcon: $r("app.media.nav_arrow")
      }).onChange((selected) => {
        if(selected){
            this.pageInfos.pushPathByName("personalization",null)
        }
      })
    }
  }

  build() {
    NavDestination() {
      Stack(){
        Scroll(){
          Column({space:30}) {
            Row({space:20}){
              Column() {
                Image($r('app.media.menue'))
                  .width(30)
                  .height(30)
                  .draggable(false)
                Text('菜单')
              }.bindMenu(this.MyMenu)

              Column(){
                Image($r('app.media.restart'))
                  .width(30)
                  .height(30)
                  .draggable(false)
                if(this.initFlag===0){
                  Text('重新开始')
                }else{
                  Text('换一题')
                }

              }.onClick(
                ()=>{
                  this.initCnavas()
                }
              )

            }.width('90%')
            .justifyContent(FlexAlign.Start)
            Column({space:5}){
              // 棋盘容器
              Column() {
                Canvas(this.context)
                  .id("canvas")
                  .width(this.short)
                  .height(this.short)
                  .backgroundColor('#FAFAFA')
                  .allowDrop([uniformTypeDescriptor.UniformDataType.IMAGE])
                  .onReady(() => {
                    this.initCnavas()
                  })
                  .onDrop((dragEvent: DragEvent) => {
                    //this.context.saveLayer();
                    if(this.operStatus===1){
                      let windowx=dragEvent.getWindowX();
                      let windowy=dragEvent.getWindowY();

                      this.drawChossedImage(windowx, windowy);
                    }



                  })
                  .onClick(
                    (event:ClickEvent)=>{
                      if(this.operStatus===0){
                        let windowx=event.windowX;
                        let windowy=event.windowY;
                        this.drawChossedImage(windowx, windowy);
                      }

                    }
                  )
              }
              .padding(16)
              .backgroundColor('#FFFFFF')
              .borderRadius(16)
              .shadow({
                radius: 12,
                color: '#20000000',
                offsetX: 0,
                offsetY: 4
              })
              .border({ width: 2, color: '#E0E0E0' })

              Row(){
                ForEach(this.usedDatas, (value: ChessPieces,index:number) => {
                  Image($r(value.photo))
                    .width(Math.floor(this.short/9)).height(Math.floor(this.short/9))
                    .backgroundColor(this.picColor[index])
                    .rotate({ angle: this.picRotae[index] })
                    .borderRadius(this.picBwith[index])
                    .draggable(this.operStatus==1?true:false)
                    .objectFit(ImageFit.Contain)
                    .shadow({
                      radius: this.picBwith[index],
                      color: '#20000000',
                      offsetX: 0,
                      offsetY: 0
                    })
                    .onDragStart(
                      ()=>{
                        this.chooseOne=value;

                      }
                    )
                    .onPreDrag(
                      (value:PreDragStatus)=>{
                        if(value===PreDragStatus.READY_TO_TRIGGER_DRAG_ACTION){
                          //音效播放
                          this.mavplr.setUrl(this.uiAbilityContext,"select01.mp3")
                        }

                      }
                    )
                    .gesture(
                      LongPressGesture({ repeat: false })
                        // 由于repeat设置为true，长按动作存在时会连续触发，触发间隔为duration（默认值500ms）
                        .onAction((event: GestureEvent) => {

                        })
                          // 长按动作一结束触发
                        .onActionEnd((event: GestureEvent) => {

                        })
                    )
                    .onClick(
                      ()=>{
                        if(this.operStatus==0){
                          this.chooseOne=value;
                          this.getUIContext()?.animateTo({ duration:2000,curve: Curve.Rhythm }, () => {
                            this.picColor=Array(9).fill('#ffffff');
                            this.picBwith=Array(9).fill(0);;
                            this.picRotae=Array(9).fill(0);;
                            // 第三步：闭包内通过状态变量改变UI界面
                            // 这里可以写任何能改变UI的逻辑比如数组添加，显隐控制，系统会检测改变后的UI界面与之前的UI界面的差异，对有差异的部分添加动画
                            // 组件一的rotate属性发生变化，所以会给组件一添加rotate旋转动画
                            this.picColor[index] = '#fff1f1';
                            this.picRotae[index]=360;
                            this.picBwith[index]=10;
                          })
                          //音效播放
                          this.mavplr.setUrl(this.uiAbilityContext,"select01.mp3")
                        }
                      }
                    )
                }, (value:ChessPieces,index: number) => {
                  console.log("什么鬼"+value.photo+value.photoid)
                  return index.toString()+JSON.stringify(value)} )
              }.width('100%')
              .justifyContent(FlexAlign.SpaceEvenly)
            }




            // 操作按钮区域
            Row({ space: 16 }) {
              // 撤销按钮
              Button() {
                Row({ space: 8 }) {
                  Image($r('app.media.restart'))
                    .width(20)
                    .height(20)
                    .fillColor('#FFFFFF')

                  Text('撤销上一步')
                    .fontSize(16)
                    .fontColor('#FFFFFF')
                    .fontWeight(FontWeight.Medium)
                }
                .justifyContent(FlexAlign.Center)
                .alignItems(VerticalAlign.Center)
              }
              .height(48)
              .layoutWeight(1)
              .backgroundColor('#E74C3C')
              .borderRadius(24)
              .shadow({
                radius: 8,
                color: '#40E74C3C',
                offsetX: 0,
                offsetY: 2
              })
              .onClick(() => {
                this.context.clearRect(this.lastX,this.lastY,this.cellsize-8,this.cellsize-8)
                this.shud[Math.floor(this.lastX/this.cellsize)][Math.floor(this.lastY/this.cellsize)]=0;
              })

              // 操作说明按钮
              Button() {
                Row({ space: 8 }) {
                  Image($r('app.media.doc'))
                    .width(20)
                    .height(20)
                    .fillColor('#FFFFFF')

                  Text('操作说明')
                    .fontSize(16)
                    .fontColor('#FFFFFF')
                    .fontWeight(FontWeight.Medium)
                }
                .justifyContent(FlexAlign.Center)
                .alignItems(VerticalAlign.Center)
              }
              .height(48)
              .layoutWeight(1)
              .backgroundColor('#3498DB')
              .borderRadius(24)
              .shadow({
                radius: 8,
                color: '#403498DB',
                offsetX: 0,
                offsetY: 2
              })
              .onClick(() => {
                this.showInstructionDialog = true;
              })
            }
            .width('90%')
            .margin({ top: 16 })


          }
          .justifyContent(FlexAlign.Start)
          .alignItems(HorizontalAlign.Center)
          .width('100%')
          .backgroundColor(Color.White)

        }
        if(this.gameSatus===2 || this.gameSatus===1){
          Particle({
            particles: [
              {
                emitter: {
                  particle: {
                    type: ParticleType.IMAGE,
                    config: {
                      src: $r(this.gameSatus===2?"app.media.gamedown":"app.media.goal2"),
                      size: [20, 20]
                    },
                    count: 500, //粒子总数
                    lifetime: 10000, //粒子生命周期，单位ms
                    lifetimeRange: 100//粒子生命周期取值范围，单位ms
                  },
                  emitRate: this.gameSatus===2?10:20, //每秒发射粒子数
                  position: [0, 0],
                  shape: ParticleEmitterShape.ELLIPSE//发射器形状
                },
                opacity: {
                  range: [0.8, 1.0], //粒子透明度的初始值从【0.0到1.0】随机产生
                  updater: {
                    type: ParticleUpdater.CURVE,
                    config: [
                      {
                        from: 0.0,
                        to: 1.0,
                        startMillis: 0,
                        endMillis: 3000,
                        curve: Curve.EaseIn
                      },
                      {
                        from: 1.0,
                        to: 0.0,
                        startMillis: 5000,
                        endMillis: 10000,
                        curve: Curve.EaseIn
                      }
                    ]
                  }
                },
                scale: {
                  range: [1.0, 1.0],
                  updater: {
                    type: ParticleUpdater.CURVE,
                    config: [
                      {
                        from: 1.0,
                        to: 1.5,
                        startMillis: 0,
                        endMillis: 3000,
                        curve: Curve.EaseIn
                      }
                    ]
                  }
                },
                acceleration: {
                  //加速度的配置，从大小和方向两个维度变化，speed表示加速度大小，angle表示加速度方向
                  speed: {
                    range: [3, 9],
                    updater: {
                      type: ParticleUpdater.RANDOM, //Speed的变化方式是随机变化
                      config: [1, 20]
                    }
                  },
                  angle: {
                    range: [225, 315]
                  }
                }

              }
            ]
          }).width(300).height(300)
        }

        // 操作说明对话框
        if (this.showInstructionDialog) {
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('#80000000')
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
            .onClick(() => {
              this.showInstructionDialog = false;
            })

          Column({ space: 20 }) {
            // 标题
            Row({ space: 12 }) {
              Image($r('app.media.doc'))
                .width(28)
                .height(28)
                .fillColor('#3498DB')

              Text('游戏操作说明')
                .fontSize(22)
                .fontWeight(FontWeight.Bold)
                .fontColor('#2C3E50')
            }
            .justifyContent(FlexAlign.Center)

            // 分隔线
            Divider()
              .color('#E1E8ED')
              .strokeWidth(2)
              .margin({ top: 8, bottom: 8 })

            // 操作方式说明
            Column({ space: 20 }) {
              // 方式一：拖拽操作
              Column({ space: 12 }) {
                Row({ space: 8 }) {
                  Text('🎯')
                    .fontSize(18)

                  Text('方式一：拖拽操作')
                    .fontSize(18)
                    .fontWeight(FontWeight.Bold)
                    .fontColor('#E67E22')
                }
                .justifyContent(FlexAlign.Start)

                Column({ space: 12 }) {
                  // 步骤1
                  Row({ space: 10 }) {
                    Text('1️⃣')
                      .fontSize(16)

                    Column({ space: 2 }) {
                      Text('长按棋盘下方的图片')
                        .fontSize(15)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#2C3E50')

                      Text('选择您要放置的数字或图标')
                        .fontSize(13)
                        .fontColor('#7F8C8D')
                    }
                    .alignItems(HorizontalAlign.Start)
                    .layoutWeight(1)
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)

                  // 步骤2
                  Row({ space: 10 }) {
                    Text('2️⃣')
                      .fontSize(16)

                    Column({ space: 2 }) {
                      Text('拖动到棋盘中')
                        .fontSize(15)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#2C3E50')

                      Text('将选中的图片拖拽到棋盘的目标位置')
                        .fontSize(13)
                        .fontColor('#7F8C8D')
                    }
                    .alignItems(HorizontalAlign.Start)
                    .layoutWeight(1)
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)

                  // 步骤3
                  Row({ space: 10 }) {
                    Text('3️⃣')
                      .fontSize(16)

                    Column({ space: 2 }) {
                      Text('松开手指完成放置')
                        .fontSize(15)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#2C3E50')

                      Text('在正确的位置松开手指即可完成数字填入')
                        .fontSize(13)
                        .fontColor('#7F8C8D')
                    }
                    .alignItems(HorizontalAlign.Start)
                    .layoutWeight(1)
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)
                }
                .padding({ left: 8 })
              }
              .width('100%')
              .alignItems(HorizontalAlign.Start)
              .padding(12)
              .backgroundColor('#F8F9FA')
              .borderRadius(8)
              .border({ width: 1, color: '#E9ECEF' })

              // 方式二：点击操作
              Column({ space: 12 }) {
                Row({ space: 8 }) {
                  Text('👆')
                    .fontSize(18)

                  Text('方式二：点击操作')
                    .fontSize(18)
                    .fontWeight(FontWeight.Bold)
                    .fontColor('#27AE60')
                }
                .justifyContent(FlexAlign.Start)

                Column({ space: 12 }) {
                  // 步骤1
                  Row({ space: 10 }) {
                    Text('1️⃣')
                      .fontSize(16)

                    Column({ space: 2 }) {
                      Text('点击棋盘下方的图片')
                        .fontSize(15)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#2C3E50')

                      Text('选择您要放置的数字或图标')
                        .fontSize(13)
                        .fontColor('#7F8C8D')
                    }
                    .alignItems(HorizontalAlign.Start)
                    .layoutWeight(1)
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)

                  // 步骤2
                  Row({ space: 10 }) {
                    Text('2️⃣')
                      .fontSize(16)

                    Column({ space: 2 }) {
                      Text('点击棋盘位置')
                        .fontSize(15)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#2C3E50')

                      Text('直接点击棋盘上要放置数字的位置')
                        .fontSize(13)
                        .fontColor('#7F8C8D')
                    }
                    .alignItems(HorizontalAlign.Start)
                    .layoutWeight(1)
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)

                  // 步骤3
                  Row({ space: 10 }) {
                    Text('✅')
                      .fontSize(16)

                    Column({ space: 2 }) {
                      Text('自动完成放置')
                        .fontSize(15)
                        .fontWeight(FontWeight.Medium)
                        .fontColor('#2C3E50')

                      Text('系统自动将选中的数字放置到点击位置')
                        .fontSize(13)
                        .fontColor('#7F8C8D')
                    }
                    .alignItems(HorizontalAlign.Start)
                    .layoutWeight(1)
                  }
                  .width('100%')
                  .alignItems(VerticalAlign.Top)
                }
                .padding({ left: 8 })
              }
              .width('100%')
              .alignItems(HorizontalAlign.Start)
              .padding(12)
              .backgroundColor('#E8F5E8')
              .borderRadius(8)
              .border({ width: 1, color: '#C8E6C9' })
            }
            .width('100%')
            .alignItems(HorizontalAlign.Start)

            // 游戏规则提示
            Column({ space: 8 }) {
              Row({ space: 8 }) {
                Text('📋')
                  .fontSize(18)

                Text('游戏规则')
                  .fontSize(16)
                  .fontWeight(FontWeight.Medium)
                  .fontColor('#E67E22')
              }
              .justifyContent(FlexAlign.Start)

              Text('• 每行、每列、每个3×3区域内数字不能重复\n• 遵循标准数独规则完成挑战\n• 自在模式可在空棋盘上自由游戏')
                .fontSize(14)
                .fontColor('#34495E')
                .lineHeight(20)
            }
            .width('100%')
            .alignItems(HorizontalAlign.Start)
            .padding(12)
            .backgroundColor('#FFF3E0')
            .borderRadius(8)
            .border({ width: 1, color: '#FFE0B2' })

            // 关闭按钮
            Button('我知道了')
              .width(120)
              .height(40)
              .backgroundColor('#3498DB')
              .borderRadius(20)
              .fontColor('#FFFFFF')
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .onClick(() => {
                this.showInstructionDialog = false;
              })
          }
          .width('85%')
          .width(400)
          .padding(24)
          .backgroundColor('#FFFFFF')
          .borderRadius(16)
          .shadow({
            radius: 20,
            color: '#40000000',
            offsetX: 0,
            offsetY: 8
          })
          .onClick((event) => {
            // 阻止事件冒泡，避免点击对话框内容时关闭对话框
            //event.stopPropagation();
          })
        }

      }.alignContent(Alignment.Center)










    }.hideTitleBar(true)
    .padding({top:this.statusBarHeight,bottom:this.naviIndicatorHeight})

    .onReady((context: NavDestinationContext) => {
      this.pageInfos = context.pathStack;
      hilog.info(0,"数海鹅独","current page config info is " + JSON.stringify(context.getConfigInRouteMap()));
    })
    .onShown(
      ()=>{
        hilog.info(0,"数海鹅独","头部%{public}d,状态栏%{public}d",this.naviIndicatorHeight,this.statusBarHeight);
        this.windowClass.setWindowLayoutFullScreen(true)
        this.windowClass.setWindowSystemBarEnable([]);
        //获取窗口宽高
        try {
          //Get window properties
          let properties:window.WindowProperties = this.windowClass.getWindowProperties();
          let rect = properties.windowRect;
          hilog.info(0,"数海鹅独","长度%{public}d,宽度%{public}d",px2vp(rect.height),px2vp(rect.width));
          let min=px2vp(Math.min(...[rect.width,rect.height]));
          this.short=min>600?min*0.7:min;
          this.short=min>600?min*0.7:min;
          this.cellsize=Math.floor(this.short/9);
          //rect.width: Window Width；rect.height: Window height
          if(this.dataPreferences){
            this.difficulty=this.dataPreferences.getSync('difficulty','easy') as 'easy' | 'medium' | 'hard'
            this.musicOpen=this.dataPreferences.getSync('musicOpen',true) as boolean;
            this.operStatus=this.dataPreferences.getSync('operStatus',0) as number;
          }
          if(this.musicOpen){
            //初始化音乐播放器
            this.mavplr.init();
          }
          if(this.operStatus===1){
            this.picColor=Array(9).fill('#ffffff');
            this.picBwith=Array(9).fill(0);;
            this.picRotae=Array(9).fill(0);;
          }

        } catch (exception) {
          console.error('有些页面初始化异常: ' + JSON.stringify(exception));
        }



      }
    )
    .onHidden(
      ()=>{
        this.windowClass.setWindowLayoutFullScreen(false)
        this.windowClass.setWindowSystemBarEnable(['status', 'navigation']);
        this.mavplr.release()
      }
    )

  }

  private drawChossedImage(windowx: number, windowy: number) {


    let cellsize = Math.floor(this.short / 9);
    this.resourceUriToPixelMap(cellsize).then((pixelMap: image.PixelMap | undefined) => {
      if (pixelMap) {
        //获取画布组件相对应用窗口的坐标
        let componentUtils: ComponentUtils = this.UIContext.getComponentUtils();
        let modePosition = componentUtils.getRectangleById("canvas");
        this.wf = modePosition.windowOffset;
        let tochX = windowx - px2vp(this.wf.x);
        let tochY = windowy - px2vp(this.wf.y);

        //计算画布坐标系坐标
        tochX = tochX - tochX % (cellsize); //对x减调多余的
        tochY = tochY - tochY % (cellsize);

        //判断坐标内是否已有数字
        let sdux = tochX / cellsize;
        let sduy = tochY / cellsize;
        console.log('下棋位置' + sdux + "---" + sduy);
        if (this.shud[sdux][sduy] == 0) {
          //根据图片大小和网格线大小，计算坐标使图片渲染在网格中心，图片大小默认为网格大小-8
          tochX = tochX + (8 / 2);
          tochY = tochY + (8 / 2);
          console.log("坐标信息:", JSON.stringify(modePosition));
          this.lastX = tochX;
          this.lastY = tochY;
          this.shud[sdux][sduy] = this.chooseOne ? this.chooseOne.index : 0;
          console.log("下棋后的矩阵变化" + JSON.stringify(this.shud));
          this.context.drawImage(pixelMap, tochX, tochY);

          //检查是否破坏游戏规则
          this.checkGameResu();
          console.info('成功画画了');

        } else {
          //提示错误
          console.log('下棋位置重复');
        }

      } else {
        console.log("没有获取到图片资源");
      }
    });
  }

  aboutToAppear(): void {
    //初始化棋子图片
    this.initDatas()

    // 获取偏好设置
    let options: preferences.Options = { name: 'myStore' };
    this.dataPreferences  =preferences.getPreferencesSync(getContext(this), options);


  }

  checkGameResu() {
    let len1= this.shud.length;
    let len2=this.shud[0].length;
    let remain=false;
    let jiuge: HashMap<string, HashMap<number,string>> = new HashMap();
    let lie: HashMap<number, HashMap<number,string>> = new HashMap();
    let repeat:HashSet<string>=new HashSet();
    for(let i=0;i<len1;i++){
      let hang:HashMap<number,string>=new HashMap();
      for(let j=0;j<len2;j++){
        if(this.shud[i][j]===0){
          remain=true;
        }else{
          if(hang.hasKey(this.shud[i][j])){
            repeat.add(i+"-"+j);
            repeat.add(hang.get(this.shud[i][j]))
          }
          if(lie.get(j)===undefined){
            lie.set(j,new HashMap());
          }
          if(lie.get(j).hasKey(this.shud[i][j])){
            repeat.add(i+"-"+j);
            repeat.add(lie.get(j).get(this.shud[i][j]))
          }
          let keyJ:string=Math.floor(i/3)+"-"+Math.floor(j/3);
          if(jiuge.get(keyJ)===undefined){
            jiuge.set(keyJ,new HashMap());
          }
          if(jiuge.get(keyJ).hasKey(this.shud[i][j])){
            repeat.add(i+"-"+j);
            repeat.add(jiuge.get(keyJ).get(this.shud[i][j]))
          }
          hang.set(this.shud[i][j],i+"-"+j);
          lie.get(j).set(this.shud[i][j],i+"-"+j);
          jiuge.get(keyJ).set(this.shud[i][j],i+"-"+j)
        }
      }
    }
    if(repeat.isEmpty() && remain){
        //游戏继续
      console.log("游戏继续")
      //音效播放
      this.mavplr.setUrl(this.uiAbilityContext,"jump01.mp3")
    }else if(repeat.isEmpty() && !remain){
        //游戏胜利
      this.gameSatus=1;
      this.mavplr.setUrl(this.uiAbilityContext,"success.mp3")
      let confirm=()=>{
        this.initCnavas()
        this.mavplr.stop();
      }
      new CommonDialog().openMessageDiag(this.UIContext,"太棒了,恭喜你完成挑战！",confirm);
      console.log("游戏胜利")
    }else if(!repeat.isEmpty()){
        //游戏失败
      console.log("你输了哦")
      this.gameSatus=2;
      this.mavplr.setUrl(this.uiAbilityContext,"fail.mp3")
      let confirm=()=>{
        this.initCnavas()
        this.mavplr.stop();
      }
      new CommonDialog().openErrorDialog(this.UIContext,"游戏结束，继续加油！",confirm,px2vp(this.wf.x),px2vp(this.wf.y))
      repeat.forEach((value: string, key?: string): void => {
          if(value){
            let splits=value.split("-");
            //获取冲突元素坐标
            let dupx=Number(splits[0]);
            let dupy=Number(splits[1]);
            //获取冲突元素根对象
            let duplicate:ChessPieces= this.usedDatas[this.shud[dupx][dupy]-1];//矩阵中存的下标是1开始的
            let cellsize=Math.floor(this.short/9);
            this.context.beginPath()
            this.context.strokeStyle = '#ff0000'
            this.context.lineWidth = 4
            this.context.moveTo(cellsize*(dupx)+4+2, cellsize*(dupy)+4+2)
            this.context.lineTo(cellsize*(dupx+1)-4-2, cellsize*(dupy+1)-4-2)
            this.context.stroke()
          }

      });

    }

  }

  async  resourceUriToPixelMap(cellsize:number): Promise<image.PixelMap|undefined> {
    try {
      const context = getContext(this);
      const resourceMgr = context.resourceManager;
      // 获取资源Buffer并生成PixelMap
      let filename="animal1";
      if(this.chooseOne){
        filename=this.chooseOne.photo.replaceAll("app.media.","")
      }else{
        console.log("当前选中图片资源不存在")
        return undefined;
      }
      let val= resourceMgr.getMediaByNameSync(filename)
      const imageSource = image.createImageSource(val.buffer);
      return await imageSource.createPixelMap({desiredSize:{width: vp2px(cellsize-8), height: vp2px(cellsize-8)}});


    } catch (err) {
      console.error('Resource conversion failed:', err);
    }
    return undefined;
  }
  async  resourceToPixelMap(media: ChessPieces,cellsize:number): Promise<image.PixelMap|undefined> {
    try {
      const context = getContext(this);
      const resourceMgr = context.resourceManager;
      // 获取资源Buffer并生成PixelMap
      let  filename=media.photo.replaceAll("app.media.","")
      let val= resourceMgr.getMediaByNameSync(filename)
      const imageSource = image.createImageSource(val.buffer);
      return await imageSource.createPixelMap({desiredSize:{width: vp2px(cellsize-8), height: vp2px(cellsize-8)}});
    } catch (err) {
      console.error('Resource conversion failed:', err);
    }
    return undefined;
  }


  initDatas(){
    for(let i=1;i<=9;i++){
      let animal= new ChessPieces(i,"app.media."+"animal"+i,$r(`app.media.animal+${i}`).id);
      let numer =new ChessPieces(i,"app.media."+"number"+i,$r(`app.media.number+${i}`).id);
      this.datas0[i-1]=animal;
      this.datas1[i-1]=numer;
    }
    if(this.modeFlag==0){
      this.usedDatas=this.datas0;
    }else{
      this.usedDatas=this.datas1;
    }
    console.log("数据源"+JSON.stringify(this.usedDatas))

  }
  initCnavas(){
    //清空画布+初始化矩阵+初始化游戏状态
    this.shud=Array(9).fill(0).map(() => Array(9).fill(0));
    this.gameSatus=0;
    this.context.clearRect(0,0,this.short,this.short)
    this.mavplr.stop()
    //可以在这里绘制内容。
    let kuan=this.cellsize;


    // 绘制水平网格线
    for (let x = 0; x <8; x++) {
      if((x+1)%3===0){
        // 3x3区域分隔线 - 深色粗线
        this.context.beginPath()
        this.context.strokeStyle = '#2C3E50'
        this.context.lineWidth = 3
        this.context.moveTo(0, kuan*(1+x))
        this.context.lineTo(kuan*9, kuan*(1+x))
        this.context.stroke()
      }else{
        // 普通网格线 - 浅色细线
        this.context.beginPath()
        this.context.strokeStyle = '#BDC3C7'
        this.context.lineWidth = 1
        this.context.moveTo(0, kuan*(1+x))
        this.context.lineTo(kuan*9, kuan*(1+x))
        this.context.stroke()
      }
    }
    // 绘制垂直网格线
    for (let x = 0; x <8; x++) {
      if((x+1)%3===0){
        // 3x3区域分隔线 - 深色粗线
        this.context.beginPath()
        this.context.strokeStyle = '#2C3E50'
        this.context.lineWidth = 3
        this.context.moveTo(kuan*(1+x),0 )
        this.context.lineTo(kuan*(1+x),kuan*9 )
        this.context.stroke()
      }else{
        // 普通网格线 - 浅色细线
        this.context.beginPath()
        this.context.strokeStyle = '#BDC3C7'
        this.context.lineWidth = 1
        this.context.moveTo(kuan*(1+x),0 )
        this.context.lineTo(kuan*(1+x),kuan*9 )
        this.context.stroke()
      }
    }
    if(this.initFlag==1){
      //先生成完整解法
      this.initSudus();
      //在完整解法上挖洞
      this.digHoles(this.difficulty);
      //棋子图片渲染棋盘
      for(let i=0;i<9;i++){
        for(let j=0;j<9;j++) {
          let index = this.shud[i][j];
          if(index>0){
            //棋盘的xy坐标系和数组的i j 索引是反的
            this.initDrawingByMedia(this.usedDatas[index-1],i,j);
          }

        }
      }

      console.log("初始化棋盘棋子成功"+JSON.stringify(this.shud))


    }


  }

  initDrawingByMedia(arg0: ChessPieces, x: number, y: number) {
    this.resourceToPixelMap(arg0,this.cellsize).then(
      (pixelMap: image.PixelMap|undefined) => {
        if(pixelMap){
          let tochX=x*this.cellsize+(8/2);
          let tochY=y*this.cellsize+(8/2);
          this.context.drawImage(pixelMap,tochX,tochY);
        }else{
          console.log("没有获取到图片资源")
        }
      }
    )

  }

  digHoles(difficulty: 'easy' | 'medium' | 'hard'): void {
    let holes = difficulty === 'easy' ? 36 : difficulty === 'medium' ? 55 : 64
    while (holes > 0) {
      const row = Math.floor(Math.random() * 9)
      const col = Math.floor(Math.random() * 9)
      if (this.shud[row][col] !== 0) {
        const temp = this.shud[row][col]
        this.shud[row][col] = 0
        holes--
      }
    }
  }
  private initSudus(): void {


    // 随机填充第一行作为种子
    const firstRow = [1,2,3,4,5,6,7,8,9]
      .sort(() => Math.random() - 0.5)
    this.shud[0] = [...firstRow]

    const backtrack = (row: number, col: number): boolean => {
      if (row === 9) return true
      if (col === 9) return backtrack(row + 1, 0)
      if (this.shud[row][col] !== 0) return backtrack(row, col + 1)

      // 随机尝试数字
      const nums = [1,2,3,4,5,6,7,8,9]
        .sort(() => Math.random() - 0.5)

      for (const num of nums) {
        if (this.isValid(this.shud, row, col, num)) {
          this.shud[row][col] = num
          if (backtrack(row, col + 1)) return true
          this.shud[row][col] = 0 // 回溯
        }
      }
      return false
    }

    backtrack(1, 0) // 从第二行开始填充
  }

  private isValid(board: number[][], row: number, col: number, num: number): boolean {
    // 检查行
    if (board[row].includes(num)) return false

    // 检查列
    if (board.some(r => r[col] === num)) return false

    // 检查3x3宫格
    const boxRow = Math.floor(row / 3) * 3
    const boxCol = Math.floor(col / 3) * 3
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        if (board[boxRow + i][boxCol + j] === num) return false
      }
    }
    return true
  }
}