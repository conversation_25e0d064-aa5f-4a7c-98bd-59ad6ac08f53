{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "df5a355c-11aa-4bb7-8e0d-9550340ced1b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683361024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0df59f-ecb5-4fb5-bb1b-1d0537e844c5", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683378801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64121425-ba80-49c5-b0c8-5a98c3ea4c24", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683402266700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a690aa2-9186-44da-acc8-9608a544d0a0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26683402598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8957d269-2dc5-4cd2-838a-e524fd6c2e00", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805777616600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805785160600, "endTime": 26806526474700}, "additional": {"children": ["0d31b4ba-3ebf-417e-adcb-d0f59fbb6662", "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "d42d65d5-29a3-437a-9e89-0f428fbbf583", "0d50b3e5-641e-4cfa-9f13-e86318e5542d", "5f34ffb5-2377-425d-bf73-39e0a138a680", "2ec6f38a-aac9-4697-a115-68e374142e75", "6feb17bb-a057-478d-aeb9-63eecc9ee954"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d31b4ba-3ebf-417e-adcb-d0f59fbb6662", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805785165200, "endTime": 26805801978300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "81216783-2f9a-41be-9cf6-aae8acc0e28a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805802002800, "endTime": 26806524710500}, "additional": {"children": ["69da556c-da3d-4e37-81f6-3412cbfd67e4", "a26a292d-f5af-40a3-a3ff-9cd21463dc1a", "34968ef0-419f-4881-9bb4-3db715513081", "fcb8b78c-695a-43f2-a2b6-d7cf95fc698a", "9a212314-cdfd-4a4b-bdd1-a977363fd769", "c404eccc-6ca4-411f-bd7b-63aeb1180445", "4c58b5da-fdb8-4cd0-a006-cb44dd089b3f", "2a244192-43ca-486c-8c35-2eb898d0313a", "3f6e84d4-1014-4bd1-8318-56726f43ed89"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d42d65d5-29a3-437a-9e89-0f428fbbf583", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806524748600, "endTime": 26806526451300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "b1520fe2-0c19-4425-ba04-1d92b0aca92b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d50b3e5-641e-4cfa-9f13-e86318e5542d", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806526459500, "endTime": 26806526469800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "b4e3cf0b-79c2-4b0f-974f-4298f0b17d8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f34ffb5-2377-425d-bf73-39e0a138a680", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805789546000, "endTime": 26805789984400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "8d756253-fac2-4945-86de-d849f8f652ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d756253-fac2-4945-86de-d849f8f652ee", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805789546000, "endTime": 26805789984400}, "additional": {"logType": "info", "children": [], "durationId": "5f34ffb5-2377-425d-bf73-39e0a138a680", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "2ec6f38a-aac9-4697-a115-68e374142e75", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805796658700, "endTime": 26805796683000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "beb94d29-139c-4311-b710-4ee91345bbec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "beb94d29-139c-4311-b710-4ee91345bbec", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805796658700, "endTime": 26805796683000}, "additional": {"logType": "info", "children": [], "durationId": "2ec6f38a-aac9-4697-a115-68e374142e75", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "18f4b9c8-f5da-4082-80fa-5684f159b698", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805796741000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97fd9d27-6b46-4ed3-b86f-9991275cf492", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805801839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81216783-2f9a-41be-9cf6-aae8acc0e28a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805785165200, "endTime": 26805801978300}, "additional": {"logType": "info", "children": [], "durationId": "0d31b4ba-3ebf-417e-adcb-d0f59fbb6662", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "69da556c-da3d-4e37-81f6-3412cbfd67e4", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805807853500, "endTime": 26805807868300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "416706ac-ba53-4248-bb85-1cb699d51781"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a26a292d-f5af-40a3-a3ff-9cd21463dc1a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805807888700, "endTime": 26805815261300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "980b79cd-6825-4b7c-b50c-7e801565e146"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34968ef0-419f-4881-9bb4-3db715513081", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805815380000, "endTime": 26806120162100}, "additional": {"children": ["f56101ab-f8cc-44e3-a59e-67ba54798f79", "d911eb3e-b472-41bd-838f-a13cbdb9a9ab", "05c853d1-c965-4b66-8985-496a7db3b0e0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "10be992e-3bc3-4131-806b-529f4d5574b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcb8b78c-695a-43f2-a2b6-d7cf95fc698a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806120176300, "endTime": 26806163475800}, "additional": {"children": ["bcc96a18-3df2-42b8-8cbe-3ad03d196302"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "4cd97f13-3828-493b-b177-b749b93e3913"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a212314-cdfd-4a4b-bdd1-a977363fd769", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806163484300, "endTime": 26806434323300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "565f079f-ba12-446e-8abb-a0615e9ecb0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c404eccc-6ca4-411f-bd7b-63aeb1180445", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806435699000, "endTime": 26806512661900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "4ff37005-0350-4ae7-840f-0119b995bd8b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c58b5da-fdb8-4cd0-a006-cb44dd089b3f", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806512713300, "endTime": 26806524215600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "27c64f41-c882-4458-87c2-5d43017474e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a244192-43ca-486c-8c35-2eb898d0313a", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806524317600, "endTime": 26806524636000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "9a06786c-4d84-4db5-89f7-7e4661a28ca7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "416706ac-ba53-4248-bb85-1cb699d51781", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805807853500, "endTime": 26805807868300}, "additional": {"logType": "info", "children": [], "durationId": "69da556c-da3d-4e37-81f6-3412cbfd67e4", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "980b79cd-6825-4b7c-b50c-7e801565e146", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805807888700, "endTime": 26805815261300}, "additional": {"logType": "info", "children": [], "durationId": "a26a292d-f5af-40a3-a3ff-9cd21463dc1a", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "f56101ab-f8cc-44e3-a59e-67ba54798f79", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805816605900, "endTime": 26805816832300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34968ef0-419f-4881-9bb4-3db715513081", "logId": "d6740fa4-94f1-4ec2-b16f-4929139873c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6740fa4-94f1-4ec2-b16f-4929139873c1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805816605900, "endTime": 26805816832300}, "additional": {"logType": "info", "children": [], "durationId": "f56101ab-f8cc-44e3-a59e-67ba54798f79", "parent": "10be992e-3bc3-4131-806b-529f4d5574b0"}}, {"head": {"id": "d911eb3e-b472-41bd-838f-a13cbdb9a9ab", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805820250100, "endTime": 26806119479600}, "additional": {"children": ["5addc760-3470-4dd4-984e-1c4628881576", "b6846d9f-c4eb-4055-a178-d514e9893453"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34968ef0-419f-4881-9bb4-3db715513081", "logId": "088f3b0a-abfd-4d1a-a165-dfeecb22cf83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5addc760-3470-4dd4-984e-1c4628881576", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805820252200, "endTime": 26805826921000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d911eb3e-b472-41bd-838f-a13cbdb9a9ab", "logId": "bc6fe0db-538c-4069-91e6-66e4af462952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6846d9f-c4eb-4055-a178-d514e9893453", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805826946600, "endTime": 26806119462700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d911eb3e-b472-41bd-838f-a13cbdb9a9ab", "logId": "c66d1237-f230-4748-80c1-ca323c28faef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "083109cc-dc67-4b80-a54b-43432c1224aa", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805820261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8565db7d-1b62-4b44-88b6-ba94e11257f8", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805826696700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc6fe0db-538c-4069-91e6-66e4af462952", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805820252200, "endTime": 26805826921000}, "additional": {"logType": "info", "children": [], "durationId": "5addc760-3470-4dd4-984e-1c4628881576", "parent": "088f3b0a-abfd-4d1a-a165-dfeecb22cf83"}}, {"head": {"id": "08b3334f-43d0-4604-b3f6-b00655e17d68", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805826960500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3e44797-a990-418e-a411-3fbb156793a8", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805834152000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9454271-85ae-4e46-9b0c-66ee3b2328ab", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805834289800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6210fb44-a69c-43da-835c-ccb4e23c2b6d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805834423000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d404e68-0d1d-4b48-971a-d64d2b0090cd", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805834514000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12531b1e-82f1-44cd-a6db-79bf8f0ce197", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805836349600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f9a25b6-f03c-4e84-b4f2-e73fd537a645", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805842062200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d5e9a37-9dd1-480d-8af0-2682835f1061", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805862190000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76afa4fb-298e-47b3-8faf-1781434a0318", "name": "Sdk init in 215 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806059032500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9848d074-9f06-4f95-8ea8-0ebe44b78e39", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806061725300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 56}, "markType": "other"}}, {"head": {"id": "af6ed852-7479-4722-b098-a1db59ace83c", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806062769200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 56}, "markType": "other"}}, {"head": {"id": "870c9b53-f6e7-40ea-b0a3-d1c3fcf8822a", "name": "Project task initialization takes 34 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806119040200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7a4bde-e060-4927-ace6-7ab8889dbf5b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806119212800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e1e79e-6c4e-4707-897d-dfc1b01df9b2", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806119321100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "997ef880-5de9-429b-b390-acabff031429", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806119377000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66d1237-f230-4748-80c1-ca323c28faef", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805826946600, "endTime": 26806119462700}, "additional": {"logType": "info", "children": [], "durationId": "b6846d9f-c4eb-4055-a178-d514e9893453", "parent": "088f3b0a-abfd-4d1a-a165-dfeecb22cf83"}}, {"head": {"id": "088f3b0a-abfd-4d1a-a165-dfeecb22cf83", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805820250100, "endTime": 26806119479600}, "additional": {"logType": "info", "children": ["bc6fe0db-538c-4069-91e6-66e4af462952", "c66d1237-f230-4748-80c1-ca323c28faef"], "durationId": "d911eb3e-b472-41bd-838f-a13cbdb9a9ab", "parent": "10be992e-3bc3-4131-806b-529f4d5574b0"}}, {"head": {"id": "05c853d1-c965-4b66-8985-496a7db3b0e0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806120128800, "endTime": 26806120144700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34968ef0-419f-4881-9bb4-3db715513081", "logId": "3ea01452-6726-4058-94aa-35cde0f67fdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ea01452-6726-4058-94aa-35cde0f67fdf", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806120128800, "endTime": 26806120144700}, "additional": {"logType": "info", "children": [], "durationId": "05c853d1-c965-4b66-8985-496a7db3b0e0", "parent": "10be992e-3bc3-4131-806b-529f4d5574b0"}}, {"head": {"id": "10be992e-3bc3-4131-806b-529f4d5574b0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805815380000, "endTime": 26806120162100}, "additional": {"logType": "info", "children": ["d6740fa4-94f1-4ec2-b16f-4929139873c1", "088f3b0a-abfd-4d1a-a165-dfeecb22cf83", "3ea01452-6726-4058-94aa-35cde0f67fdf"], "durationId": "34968ef0-419f-4881-9bb4-3db715513081", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "bcc96a18-3df2-42b8-8cbe-3ad03d196302", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806120746900, "endTime": 26806163457700}, "additional": {"children": ["3e8afb14-ae22-4621-8614-f921432dd28f", "940f8b99-c2a7-4198-a6c1-afeb9f5552c7", "9117a6c6-c066-498a-9f8c-80dd3f3ea280"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fcb8b78c-695a-43f2-a2b6-d7cf95fc698a", "logId": "83b396ac-d756-4f96-af4c-b7eda4e66283"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e8afb14-ae22-4621-8614-f921432dd28f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806125195600, "endTime": 26806125216000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bcc96a18-3df2-42b8-8cbe-3ad03d196302", "logId": "c610e64e-0348-4d2f-991a-87da27c16c16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c610e64e-0348-4d2f-991a-87da27c16c16", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806125195600, "endTime": 26806125216000}, "additional": {"logType": "info", "children": [], "durationId": "3e8afb14-ae22-4621-8614-f921432dd28f", "parent": "83b396ac-d756-4f96-af4c-b7eda4e66283"}}, {"head": {"id": "940f8b99-c2a7-4198-a6c1-afeb9f5552c7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806128133100, "endTime": 26806161841900}, "additional": {"children": ["1d81ea10-a346-407e-b963-362222e2222e", "96063095-3ab5-4f40-a277-72168d50a09c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bcc96a18-3df2-42b8-8cbe-3ad03d196302", "logId": "c5aa7bdf-872c-4690-95a4-04fa65c4cc0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d81ea10-a346-407e-b963-362222e2222e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806128134900, "endTime": 26806133448000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "940f8b99-c2a7-4198-a6c1-afeb9f5552c7", "logId": "989349ea-8ae3-44fc-ba9c-bd76c30391f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96063095-3ab5-4f40-a277-72168d50a09c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806133495000, "endTime": 26806161823400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "940f8b99-c2a7-4198-a6c1-afeb9f5552c7", "logId": "04e9e45d-699b-4543-b910-0a3967360a5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "839474c7-4d0a-44f3-971c-470b72921a89", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806128142300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82f132c2-4390-4f0d-a6fd-5d4917c29360", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806133307100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "989349ea-8ae3-44fc-ba9c-bd76c30391f2", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806128134900, "endTime": 26806133448000}, "additional": {"logType": "info", "children": [], "durationId": "1d81ea10-a346-407e-b963-362222e2222e", "parent": "c5aa7bdf-872c-4690-95a4-04fa65c4cc0f"}}, {"head": {"id": "439e81f8-28f6-4668-bca2-61ed51c80171", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806133516600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c74620-6076-49d6-abb4-bec2c01eac36", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806143267800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14d7d3d-0c0a-4580-be4a-8b8b1d615de1", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806143748800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add73c6e-a26d-484f-b124-52068315d8da", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806144973300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09858327-fa90-4e90-80ee-816f559a4330", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806145215700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32068bee-904c-4f89-9268-2f7b18d64526", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806145359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5c04e4-1df2-4fcb-b199-9b21436bf320", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806145437900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "215d1ccc-691b-4101-a2b5-bb2908ec8b18", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806145546800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7d13df8-b4db-4b97-8c0f-beef7adfd86a", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806160753000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03f26da7-a432-48c7-b5b0-fa37a361fb57", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806161229500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6674f6a1-a7a4-4398-91d4-1a6ed928911d", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806161642700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e21ef8d-9106-4cec-b406-20643a3e0d68", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806161745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04e9e45d-699b-4543-b910-0a3967360a5c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806133495000, "endTime": 26806161823400}, "additional": {"logType": "info", "children": [], "durationId": "96063095-3ab5-4f40-a277-72168d50a09c", "parent": "c5aa7bdf-872c-4690-95a4-04fa65c4cc0f"}}, {"head": {"id": "c5aa7bdf-872c-4690-95a4-04fa65c4cc0f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806128133100, "endTime": 26806161841900}, "additional": {"logType": "info", "children": ["989349ea-8ae3-44fc-ba9c-bd76c30391f2", "04e9e45d-699b-4543-b910-0a3967360a5c"], "durationId": "940f8b99-c2a7-4198-a6c1-afeb9f5552c7", "parent": "83b396ac-d756-4f96-af4c-b7eda4e66283"}}, {"head": {"id": "9117a6c6-c066-498a-9f8c-80dd3f3ea280", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806163416400, "endTime": 26806163434900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bcc96a18-3df2-42b8-8cbe-3ad03d196302", "logId": "765cfdbe-d298-4420-bb49-97804d2b68d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "765cfdbe-d298-4420-bb49-97804d2b68d0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806163416400, "endTime": 26806163434900}, "additional": {"logType": "info", "children": [], "durationId": "9117a6c6-c066-498a-9f8c-80dd3f3ea280", "parent": "83b396ac-d756-4f96-af4c-b7eda4e66283"}}, {"head": {"id": "83b396ac-d756-4f96-af4c-b7eda4e66283", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806120746900, "endTime": 26806163457700}, "additional": {"logType": "info", "children": ["c610e64e-0348-4d2f-991a-87da27c16c16", "c5aa7bdf-872c-4690-95a4-04fa65c4cc0f", "765cfdbe-d298-4420-bb49-97804d2b68d0"], "durationId": "bcc96a18-3df2-42b8-8cbe-3ad03d196302", "parent": "4cd97f13-3828-493b-b177-b749b93e3913"}}, {"head": {"id": "4cd97f13-3828-493b-b177-b749b93e3913", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806120176300, "endTime": 26806163475800}, "additional": {"logType": "info", "children": ["83b396ac-d756-4f96-af4c-b7eda4e66283"], "durationId": "fcb8b78c-695a-43f2-a2b6-d7cf95fc698a", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "a2025bfa-0e68-4c85-a38a-a440bd97a898", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806288712700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d12445c-a672-4721-a6f1-42f0c08e4a94", "name": "hvigorfile, resolve hvigorfile dependencies in 270 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806433489500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "565f079f-ba12-446e-8abb-a0615e9ecb0f", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806163484300, "endTime": 26806434323300}, "additional": {"logType": "info", "children": [], "durationId": "9a212314-cdfd-4a4b-bdd1-a977363fd769", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "3f6e84d4-1014-4bd1-8318-56726f43ed89", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806435345100, "endTime": 26806435677500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "logId": "e6c0c269-9189-49dd-884f-1fc941b51888"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5914d633-077e-4c70-bbc2-a68ece0d4303", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806435400000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6c0c269-9189-49dd-884f-1fc941b51888", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806435345100, "endTime": 26806435677500}, "additional": {"logType": "info", "children": [], "durationId": "3f6e84d4-1014-4bd1-8318-56726f43ed89", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "71d5b4d1-4009-49f1-b9e2-c845116c0a66", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806436886200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f28d009e-adcd-4361-8cdc-8b847c12d450", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806511638200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff37005-0350-4ae7-840f-0119b995bd8b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806435699000, "endTime": 26806512661900}, "additional": {"logType": "info", "children": [], "durationId": "c404eccc-6ca4-411f-bd7b-63aeb1180445", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "f2291468-5fd5-497f-aac8-a1228d9c20e8", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806517810500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb624a3f-e43b-44d6-afb4-8fc1864c0417", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806517947300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b48c1214-a805-44cb-a793-625fc46e252c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806519685200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2272460-c56c-4549-a151-136838d9eb54", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806519916800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c64f41-c882-4458-87c2-5d43017474e9", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806512713300, "endTime": 26806524215600}, "additional": {"logType": "info", "children": [], "durationId": "4c58b5da-fdb8-4cd0-a006-cb44dd089b3f", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "86ee6d7f-ac7a-47ea-9bda-9c3c0307dadb", "name": "Configuration phase cost:717 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806524384100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a06786c-4d84-4db5-89f7-7e4661a28ca7", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806524317600, "endTime": 26806524636000}, "additional": {"logType": "info", "children": [], "durationId": "2a244192-43ca-486c-8c35-2eb898d0313a", "parent": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2"}}, {"head": {"id": "fd6dad14-78fa-407d-8df9-a12f68a7c5d2", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805802002800, "endTime": 26806524710500}, "additional": {"logType": "info", "children": ["416706ac-ba53-4248-bb85-1cb699d51781", "980b79cd-6825-4b7c-b50c-7e801565e146", "10be992e-3bc3-4131-806b-529f4d5574b0", "4cd97f13-3828-493b-b177-b749b93e3913", "565f079f-ba12-446e-8abb-a0615e9ecb0f", "4ff37005-0350-4ae7-840f-0119b995bd8b", "27c64f41-c882-4458-87c2-5d43017474e9", "9a06786c-4d84-4db5-89f7-7e4661a28ca7", "e6c0c269-9189-49dd-884f-1fc941b51888"], "durationId": "b02d8bdc-2824-4757-ad90-1b6cebce8fa7", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "6feb17bb-a057-478d-aeb9-63eecc9ee954", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806526404600, "endTime": 26806526429400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a2ffee8-ed90-4eda-9d42-28c06754cc92", "logId": "15cc0e0d-b269-453a-b55f-d0bd14d339c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15cc0e0d-b269-453a-b55f-d0bd14d339c1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806526404600, "endTime": 26806526429400}, "additional": {"logType": "info", "children": [], "durationId": "6feb17bb-a057-478d-aeb9-63eecc9ee954", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "b1520fe2-0c19-4425-ba04-1d92b0aca92b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806524748600, "endTime": 26806526451300}, "additional": {"logType": "info", "children": [], "durationId": "d42d65d5-29a3-437a-9e89-0f428fbbf583", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "b4e3cf0b-79c2-4b0f-974f-4298f0b17d8b", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806526459500, "endTime": 26806526469800}, "additional": {"logType": "info", "children": [], "durationId": "0d50b3e5-641e-4cfa-9f13-e86318e5542d", "parent": "e51773bf-755d-4288-8bff-e73b8eeaecc6"}}, {"head": {"id": "e51773bf-755d-4288-8bff-e73b8eeaecc6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805785160600, "endTime": 26806526474700}, "additional": {"logType": "info", "children": ["81216783-2f9a-41be-9cf6-aae8acc0e28a", "fd6dad14-78fa-407d-8df9-a12f68a7c5d2", "b1520fe2-0c19-4425-ba04-1d92b0aca92b", "b4e3cf0b-79c2-4b0f-974f-4298f0b17d8b", "8d756253-fac2-4945-86de-d849f8f652ee", "beb94d29-139c-4311-b710-4ee91345bbec", "15cc0e0d-b269-453a-b55f-d0bd14d339c1"], "durationId": "8a2ffee8-ed90-4eda-9d42-28c06754cc92"}}, {"head": {"id": "f6eefd5a-bfce-415c-af69-4830765352ac", "name": "Configuration task cost before running: 746 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806526646800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621821ab-56a1-4a04-8012-c75234f59ad5", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806533195000, "endTime": 26806542238800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "96a8c774-7f4e-40e8-bb27-c2ef2abe1f82", "logId": "c9d59385-3dd2-481d-b2b4-5b380a02bf16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96a8c774-7f4e-40e8-bb27-c2ef2abe1f82", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806528494400}, "additional": {"logType": "detail", "children": [], "durationId": "621821ab-56a1-4a04-8012-c75234f59ad5"}}, {"head": {"id": "42da7310-27ae-4cbe-b70d-3c4786950be1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806529158600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5913c6b6-dad4-4e1b-813f-19b7791160d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806529350700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8370c78-1fc5-4002-bc4e-15d292171781", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806533217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0897223a-1e63-4589-9420-b633cd36d7de", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806540979100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e3c2a6-e116-4004-8fd8-f09b28b68c80", "name": "entry : default@PreBuild cost memory 0.31282806396484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806541897000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9d59385-3dd2-481d-b2b4-5b380a02bf16", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806533195000, "endTime": 26806542238800}, "additional": {"logType": "info", "children": [], "durationId": "621821ab-56a1-4a04-8012-c75234f59ad5"}}, {"head": {"id": "22601ba3-f8ff-481a-9a06-63b81f2d724d", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806554075600, "endTime": 26806556396200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "32b38f2c-c214-487c-b65d-6db69f895d33", "logId": "13096ca4-4ccb-49f6-9c27-ddf3d534b15c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32b38f2c-c214-487c-b65d-6db69f895d33", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806548643100}, "additional": {"logType": "detail", "children": [], "durationId": "22601ba3-f8ff-481a-9a06-63b81f2d724d"}}, {"head": {"id": "962a2fe2-536d-42c5-999a-7fde559b7674", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806549823000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448ee41f-539a-4c9e-8331-91a174887b47", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806550005200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9915645a-ee7e-4f4e-ab64-22a380a0226b", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806554092400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67f3f76e-1eaa-4d88-8138-d68ab99d33d6", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806555204000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8df6a142-6f7a-4361-bc43-bae94f3a1822", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806556105500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c8fc44f-5aac-47ce-aeb4-c827df07deb8", "name": "entry : default@GenerateMetadata cost memory 0.09583282470703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806556250200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13096ca4-4ccb-49f6-9c27-ddf3d534b15c", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806554075600, "endTime": 26806556396200}, "additional": {"logType": "info", "children": [], "durationId": "22601ba3-f8ff-481a-9a06-63b81f2d724d"}}, {"head": {"id": "c644cd29-84bb-4916-8a16-115c325aaeb1", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558658400, "endTime": 26806559283500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "90ffe162-f84a-406e-b28d-a61156911b38", "logId": "757f1de9-1509-4ab9-baa9-1fb7b992ab27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90ffe162-f84a-406e-b28d-a61156911b38", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558023000}, "additional": {"logType": "detail", "children": [], "durationId": "c644cd29-84bb-4916-8a16-115c325aaeb1"}}, {"head": {"id": "c1ab89eb-74d1-47fe-bd95-748b4de5ef57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558396800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d3ddec-b1dc-46d8-96d9-d28e12c462b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f2b6fb-b4dd-4ff0-8eab-178cac2b975b", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558667400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb2c96a6-0393-454c-aa17-5e11e21a2818", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558759800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46999917-0a1d-4bbb-876f-2dd951cc2b32", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558855100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a326ba6-e1df-461b-8368-e40087f4c3b5", "name": "entry : default@ConfigureCmake cost memory 0.03692626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558941100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca97c522-7f10-4fc2-84cc-43fea41a2914", "name": "runTaskFromQueue task cost before running: 778 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806559206800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757f1de9-1509-4ab9-baa9-1fb7b992ab27", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806558658400, "endTime": 26806559283500, "totalTime": 509500}, "additional": {"logType": "info", "children": [], "durationId": "c644cd29-84bb-4916-8a16-115c325aaeb1"}}, {"head": {"id": "a6a4f119-b0e1-4f91-b70e-adfe43596cbe", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806562635600, "endTime": 26806564344700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "72c5b694-9bf3-4698-9fab-6b1ebbcfa696", "logId": "e0f2f3b6-d878-44eb-9235-7370e41041cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72c5b694-9bf3-4698-9fab-6b1ebbcfa696", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806561444600}, "additional": {"logType": "detail", "children": [], "durationId": "a6a4f119-b0e1-4f91-b70e-adfe43596cbe"}}, {"head": {"id": "8debb1b0-a5a8-45ce-801c-25899ecd4a47", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806561899100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e384de-82c7-40d1-acf9-00e335482098", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806562009000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de78e0af-4331-4a7b-8198-ede4f6062403", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806562649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e890b4-2452-4a06-aa80-42799ff64d8e", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806564072300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92f1e15e-0086-4c78-a97a-bd3b5f2c7497", "name": "entry : default@MergeProfile cost memory 0.107391357421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806564241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0f2f3b6-d878-44eb-9235-7370e41041cc", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806562635600, "endTime": 26806564344700}, "additional": {"logType": "info", "children": [], "durationId": "a6a4f119-b0e1-4f91-b70e-adfe43596cbe"}}, {"head": {"id": "83bc6212-223e-410f-a5a5-0471e667bb79", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806567573000, "endTime": 26806569982500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5ac3de03-ce73-40b6-bbb8-ea9c61789b0b", "logId": "9936a2e8-ecbd-4790-b52a-9386f3940287"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ac3de03-ce73-40b6-bbb8-ea9c61789b0b", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806566211100}, "additional": {"logType": "detail", "children": [], "durationId": "83bc6212-223e-410f-a5a5-0471e667bb79"}}, {"head": {"id": "c57dc5df-8b60-43a8-bb06-86868a8f5ae0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806566656300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc6054dc-9c7a-4ac1-9f72-36ceced57d49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806566786200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac84277f-dedf-4f4d-bbd1-865a63300d0a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806567590100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d930ae58-c695-4f42-9a3a-a1760fbc50db", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806568551800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52f58d11-426a-41fc-8a3a-e482ab69cc40", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806569640100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05b9c32e-3860-41dc-9c7e-34dc1b431000", "name": "entry : default@CreateBuildProfile cost memory 0.1051177978515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806569839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9936a2e8-ecbd-4790-b52a-9386f3940287", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806567573000, "endTime": 26806569982500}, "additional": {"logType": "info", "children": [], "durationId": "83bc6212-223e-410f-a5a5-0471e667bb79"}}, {"head": {"id": "6e49a22d-eccc-4aa3-a308-aa89bf8bd1c7", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806573885400, "endTime": 26806574429400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e4b02296-c548-4f63-bd70-42034ce2d3e4", "logId": "ddeb498e-380d-4f10-86e6-515db7dfb3b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4b02296-c548-4f63-bd70-42034ce2d3e4", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806571708600}, "additional": {"logType": "detail", "children": [], "durationId": "6e49a22d-eccc-4aa3-a308-aa89bf8bd1c7"}}, {"head": {"id": "bcc3775b-dd65-4a3c-b76e-469de3f8f9a4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806572188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce3dd455-a460-4441-8056-ac8c289c4556", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806572374400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885f1cf5-6df8-4bd4-bd4f-43bf679441f7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806573898600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d72be06a-3559-46e9-bdde-a0caa0860c6f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806574048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff3a1df3-7515-453c-8bd0-d0532a6d0788", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806574111900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c632f098-ec05-477e-8e7f-be60988221ba", "name": "entry : default@PreCheckSyscap cost memory 0.03714752197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806574201700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc7b7353-a6ff-4ef3-bb18-a79560716a60", "name": "runTaskFromQueue task cost before running: 793 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806574341800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddeb498e-380d-4f10-86e6-515db7dfb3b5", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806573885400, "endTime": 26806574429400, "totalTime": 377900}, "additional": {"logType": "info", "children": [], "durationId": "6e49a22d-eccc-4aa3-a308-aa89bf8bd1c7"}}, {"head": {"id": "074e3cf0-c8dc-4d55-96b5-a3d8274dfcca", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806583957500, "endTime": 26806584719600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7a5b7486-0e01-42cc-88dd-f670c9435268", "logId": "d6d9b24e-7591-47c1-99c7-12b300d9fd2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7a5b7486-0e01-42cc-88dd-f670c9435268", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806577468500}, "additional": {"logType": "detail", "children": [], "durationId": "074e3cf0-c8dc-4d55-96b5-a3d8274dfcca"}}, {"head": {"id": "e4118530-5198-4c65-af76-cbbec825e0ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806578085400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a5abced-6ab0-4327-ab00-0635daf8939d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806578222800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "100522d8-bde0-4034-99a1-e18dfb463cfe", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806583975800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89598ec1-8d0c-4fa1-905a-5c4bae0664fc", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806584225100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d667e0c5-9c7c-4260-bf07-6e6f87007a77", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03936767578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806584441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea806be-feb6-4a46-a2a6-434e7cef25e4", "name": "runTaskFromQueue task cost before running: 804 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806584625100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6d9b24e-7591-47c1-99c7-12b300d9fd2c", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806583957500, "endTime": 26806584719600, "totalTime": 603400}, "additional": {"logType": "info", "children": [], "durationId": "074e3cf0-c8dc-4d55-96b5-a3d8274dfcca"}}, {"head": {"id": "1ace1844-0f9c-4827-ac87-55a9aef0c5f5", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806593511000, "endTime": 26806595946800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "15e5a6ba-cc31-455a-b064-a2cfb1350509", "logId": "128fb508-5e06-422c-a862-d2fe33f38e17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15e5a6ba-cc31-455a-b064-a2cfb1350509", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806590644000}, "additional": {"logType": "detail", "children": [], "durationId": "1ace1844-0f9c-4827-ac87-55a9aef0c5f5"}}, {"head": {"id": "5f38a3cc-4eae-4438-ac80-657cf1ab7058", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806591208100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d35e99d7-157f-4d54-8cf6-40d24775576f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806591327700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce5367d-59fe-4f96-b877-c15b9406660a", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806593539200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee9dd60-2f13-48e2-ba60-bb7233113e3c", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806595343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "219985e2-f93e-4a8f-a46b-ef8deb863c79", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806595502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a35c927a-f59d-4585-a2df-8b9409af0231", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806595624100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d2d00f-3c85-4f22-9a9c-9e2682adf8c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806595679200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d641f302-68a1-4b1d-9139-73ba7dcb7d7c", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1196136474609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806595760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c188a04d-82ef-4786-96dd-7a20c1dce0d9", "name": "runTaskFromQueue task cost before running: 815 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806595879900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "128fb508-5e06-422c-a862-d2fe33f38e17", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806593511000, "endTime": 26806595946800, "totalTime": 2371700}, "additional": {"logType": "info", "children": [], "durationId": "1ace1844-0f9c-4827-ac87-55a9aef0c5f5"}}, {"head": {"id": "ef1ce28a-b874-437c-bf66-28df70a5e837", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599120500, "endTime": 26806599806400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6bb76307-ef50-4391-8af6-2e13037d696a", "logId": "b6f10eef-7071-4b93-82d9-d2dc091c82c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bb76307-ef50-4391-8af6-2e13037d696a", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806597933100}, "additional": {"logType": "detail", "children": [], "durationId": "ef1ce28a-b874-437c-bf66-28df70a5e837"}}, {"head": {"id": "99db2e54-da66-4e40-9087-d25390f93e1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806598248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd666f60-46b5-490f-9d13-864de6786ec8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806598374900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4355de4e-ad2e-4d9d-aa74-def10c475959", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599132200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1262f519-4548-485d-a4b7-4f021db5ac9a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599241200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b320369a-1aca-4733-ace1-d8a0868bfbec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599341100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9b07adc-b40d-4d58-9cea-b22898113b1d", "name": "entry : default@BuildNativeWithCmake cost memory 0.0379791259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599494100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff4d8c87-7470-464c-b138-65bced0f5fb9", "name": "runTaskFromQueue task cost before running: 819 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599593300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f10eef-7071-4b93-82d9-d2dc091c82c4", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806599120500, "endTime": 26806599806400, "totalTime": 456400}, "additional": {"logType": "info", "children": [], "durationId": "ef1ce28a-b874-437c-bf66-28df70a5e837"}}, {"head": {"id": "4c1eab75-7f25-4e30-a07e-a033e3b4eaf0", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806604266400, "endTime": 26806609617800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a09706b-1c79-4b64-96e6-8342c592e10e", "logId": "d034fad3-fdbb-4807-8bbe-01c3b4520e4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a09706b-1c79-4b64-96e6-8342c592e10e", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806602889400}, "additional": {"logType": "detail", "children": [], "durationId": "4c1eab75-7f25-4e30-a07e-a033e3b4eaf0"}}, {"head": {"id": "5f6d5fe0-5a6e-454c-8f78-718e05bf69c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806603350400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef352ff-43f5-463a-b4b9-6d6cf7feb999", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806603484100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feca43af-571b-40a0-9a4e-804fa2521eba", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806604282000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85e8dfd9-118f-4b11-bf06-ec3667750560", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806609195600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a6597f6-0706-438f-bd26-8e1186ca0f2a", "name": "entry : default@MakePackInfo cost memory 0.140655517578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806609490100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d034fad3-fdbb-4807-8bbe-01c3b4520e4e", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806604266400, "endTime": 26806609617800}, "additional": {"logType": "info", "children": [], "durationId": "4c1eab75-7f25-4e30-a07e-a033e3b4eaf0"}}, {"head": {"id": "c46ee61a-6540-42d9-b815-5b676c73ca66", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806614881500, "endTime": 26806618206500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "52c6f520-d16e-4a46-a665-7754e2b84764", "logId": "297dd2e1-f24d-4d02-95b4-e260d26644d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52c6f520-d16e-4a46-a665-7754e2b84764", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806613182800}, "additional": {"logType": "detail", "children": [], "durationId": "c46ee61a-6540-42d9-b815-5b676c73ca66"}}, {"head": {"id": "004c5bb2-11ba-458e-9078-f7e7b7f597cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806613572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0696a46f-ddc0-4c4f-9399-f8942564bc1b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806613687100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdcc1bf8-b716-4ea5-b565-133be78ede31", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806614894200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19859677-8ba1-43ff-a0d8-e5c6da2ecab4", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806615037500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc4868d3-ad69-45b6-9086-6c0cbbb26654", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806615605800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ed7f427-a737-445c-baea-5f924c3b86d5", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806616932100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12cbfa2-e216-4907-a60b-cc4c8183a518", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806617050200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd33f98a-db99-4a42-973d-a03e55faf910", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806617138400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cad66b4b-4fa7-4549-90f0-c3e1ee2b2dc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806617195700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2426af38-6700-4027-8dfb-e9ad0d5575eb", "name": "entry : default@SyscapTransform cost memory 0.1545562744140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806617342900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be575bb1-f42c-4cbc-8726-390e2074de7a", "name": "runTaskFromQueue task cost before running: 837 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806617646300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "297dd2e1-f24d-4d02-95b4-e260d26644d7", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806614881500, "endTime": 26806618206500, "totalTime": 2740400}, "additional": {"logType": "info", "children": [], "durationId": "c46ee61a-6540-42d9-b815-5b676c73ca66"}}, {"head": {"id": "28359d85-7f50-41ed-b1c5-579c99cb153c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806622041500, "endTime": 26806623518900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3b6cf40b-b28b-4bb9-9d3e-92d59fbb8ec6", "logId": "235af7c0-c447-4e87-b079-354f0a8fbe6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b6cf40b-b28b-4bb9-9d3e-92d59fbb8ec6", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806620516500}, "additional": {"logType": "detail", "children": [], "durationId": "28359d85-7f50-41ed-b1c5-579c99cb153c"}}, {"head": {"id": "936950af-71f1-4658-a05f-bcd86a469273", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806620914400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716c6fba-4c61-41a3-82d8-f95d6dd56dea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806621021800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1562557-a64e-46b6-93d5-9a93c05217ca", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806622053500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db743d11-f3b6-4c38-abd0-c9a46ebc2b6d", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806623256200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec468c93-1506-4cd3-9754-7e5c1ea666d1", "name": "entry : default@ProcessProfile cost memory 0.06215667724609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806623431400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "235af7c0-c447-4e87-b079-354f0a8fbe6a", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806622041500, "endTime": 26806623518900}, "additional": {"logType": "info", "children": [], "durationId": "28359d85-7f50-41ed-b1c5-579c99cb153c"}}, {"head": {"id": "dc678a42-849d-4d57-b46c-fc00a49f3d06", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806630850800, "endTime": 26806640357500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "81e92cad-7803-426d-a282-cd7ef237017f", "logId": "d24773b4-9f8e-4994-8fed-d0e56c25ebb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81e92cad-7803-426d-a282-cd7ef237017f", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806628557200}, "additional": {"logType": "detail", "children": [], "durationId": "dc678a42-849d-4d57-b46c-fc00a49f3d06"}}, {"head": {"id": "bc8bf112-1cd2-445e-918f-a37a51418439", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806628942700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a662bdb-f79b-4c57-a2ea-63531f647048", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806629062800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b16e30ed-0ff6-44d1-a055-3793efc00526", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806630871200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ff7a67-1ef0-4085-92cd-0975ada198d5", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806640115500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94937965-61f9-42ee-af1b-1a2f80cfdf09", "name": "entry : default@ProcessRouterMap cost memory 0.28092193603515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806640272500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d24773b4-9f8e-4994-8fed-d0e56c25ebb2", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806630850800, "endTime": 26806640357500}, "additional": {"logType": "info", "children": [], "durationId": "dc678a42-849d-4d57-b46c-fc00a49f3d06"}}, {"head": {"id": "4bb4a971-867f-4ead-8301-1cb4d5700118", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806646853400, "endTime": 26806647882900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5bd04246-abc0-4839-bd0c-c5816c184cfe", "logId": "7b24c3ca-83ae-4313-a029-8823fbaf9e95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bd04246-abc0-4839-bd0c-c5816c184cfe", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806645490500}, "additional": {"logType": "detail", "children": [], "durationId": "4bb4a971-867f-4ead-8301-1cb4d5700118"}}, {"head": {"id": "3ae0e8bd-d5c7-4509-a61e-d0a2e44c4b94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806645943000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0305df2-8416-4cd3-b5fc-4508b2371f24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806646071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecfd3e41-b7b6-4ca9-8ed1-5c247bf7ab0d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806646867000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ac2bf80-7d06-42e7-9ffa-596d4c821972", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806646988100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57905ebf-1a9c-4f6e-80b3-7d1027d73ba8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806647049300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c86ba7a-65ac-4eab-beb8-74053fa489ca", "name": "entry : default@BuildNativeWithNinja cost memory 0.05785369873046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806647666900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a516f9af-7d61-4276-a228-5cda824a62c9", "name": "runTaskFromQueue task cost before running: 867 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806647805900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b24c3ca-83ae-4313-a029-8823fbaf9e95", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806646853400, "endTime": 26806647882900, "totalTime": 903500}, "additional": {"logType": "info", "children": [], "durationId": "4bb4a971-867f-4ead-8301-1cb4d5700118"}}, {"head": {"id": "8a5211b5-9da1-43de-89ef-d28940856057", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806657338000, "endTime": 26806665977700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "fc3a9b06-bfeb-40c0-9177-94d86414cc34", "logId": "c5bd6789-7ae6-4e4b-94f1-7c5c797e1c29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc3a9b06-bfeb-40c0-9177-94d86414cc34", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806651455700}, "additional": {"logType": "detail", "children": [], "durationId": "8a5211b5-9da1-43de-89ef-d28940856057"}}, {"head": {"id": "b25dba4c-39be-4a2e-9f1d-8bf767b95638", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806651877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ceae8a1-2384-4188-a744-a7ac5f187c7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806652266900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f8a24eb-9483-4bcf-89dc-24e5ee7ded57", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806653710400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0384a5e7-b7ba-439a-8c23-4bbcbd55e3d5", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806662219500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b09040-99d3-44bd-bc52-537982a19aac", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806663888400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ee0497-31fc-461c-9e00-f90803a74d18", "name": "entry : default@ProcessResource cost memory 0.169921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806664023000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5bd6789-7ae6-4e4b-94f1-7c5c797e1c29", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806657338000, "endTime": 26806665977700}, "additional": {"logType": "info", "children": [], "durationId": "8a5211b5-9da1-43de-89ef-d28940856057"}}, {"head": {"id": "0bb5033b-3e5f-40d6-aac1-cba8a5496443", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806672385100, "endTime": 26806686750900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9f1cc078-429e-4776-8197-636756b874f2", "logId": "9176006a-a3c4-45ed-94c1-6ef3ba71cf4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f1cc078-429e-4776-8197-636756b874f2", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806669028500}, "additional": {"logType": "detail", "children": [], "durationId": "0bb5033b-3e5f-40d6-aac1-cba8a5496443"}}, {"head": {"id": "3a8dc32c-b05a-4b34-8f1e-f8fc4911ed1a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806669461200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b3592ec-70ce-42e7-973d-ae367532dacb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806669581700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c118a3a8-97c8-4b71-9e8c-99ed64b1f6df", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806672402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e706736-f9dc-45cd-a1dc-ed67f9ee66fe", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806686514100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b61bb0-1936-42e0-9324-9b30f3a1098c", "name": "entry : default@GenerateLoaderJson cost memory 0.78369140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806686675100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9176006a-a3c4-45ed-94c1-6ef3ba71cf4b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806672385100, "endTime": 26806686750900}, "additional": {"logType": "info", "children": [], "durationId": "0bb5033b-3e5f-40d6-aac1-cba8a5496443"}}, {"head": {"id": "95d77af6-5428-4547-aa6b-53c802ef0602", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806697872500, "endTime": 26806702722100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5dec8357-5a96-49ea-ac2b-c1325ddb8ae3", "logId": "b1c454b8-b573-44cc-b822-2f313c322144"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dec8357-5a96-49ea-ac2b-c1325ddb8ae3", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806695858000}, "additional": {"logType": "detail", "children": [], "durationId": "95d77af6-5428-4547-aa6b-53c802ef0602"}}, {"head": {"id": "f8173383-b2ad-47ff-840f-9334851c6bc2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806696415200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "109d88c9-7ba5-4dd0-aaf8-f84508d5e8ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806696566900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c57f7d-8b56-44ae-8a3a-d49164859d51", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806697925500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d65494-1c46-435e-9671-1cf66692a90a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806701112100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72dbaee3-37c0-473a-80c9-9eebf66c4f42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806701346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98a8e552-1c31-4e98-87f4-300e94220c34", "name": "entry : default@ProcessLibs cost memory 0.1272125244140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806702408100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d06187-666b-4e6c-848d-bee197922032", "name": "runTaskFromQueue task cost before running: 922 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806702563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1c454b8-b573-44cc-b822-2f313c322144", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806697872500, "endTime": 26806702722100, "totalTime": 4675600}, "additional": {"logType": "info", "children": [], "durationId": "95d77af6-5428-4547-aa6b-53c802ef0602"}}, {"head": {"id": "c81c36cb-2b4d-461c-9be4-606a15b91a82", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806714780800, "endTime": 26806741245500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4a38daa1-7bce-446d-84c2-5de28f5fbfb3", "logId": "8fea83ef-cced-457d-9bc6-cd2742a744d8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a38daa1-7bce-446d-84c2-5de28f5fbfb3", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806709045900}, "additional": {"logType": "detail", "children": [], "durationId": "c81c36cb-2b4d-461c-9be4-606a15b91a82"}}, {"head": {"id": "af0eb95d-5b01-4931-aa18-a2e29fb74593", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806709629600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afe3be81-9706-4f77-8a79-1e5c666388e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806709854900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4944ac98-ecdc-426e-9315-b2cc8d5e4162", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806711474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46576299-73a5-4d97-916b-7a3f5d36d58b", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806714810200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a11c7235-9a47-4f05-89ac-e9987ace21b4", "name": "Incremental task entry:default@CompileResource pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806741006500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a4d354-521c-4647-b531-5dc0092702b0", "name": "entry : default@CompileResource cost memory 1.4018402099609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806741155700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fea83ef-cced-457d-9bc6-cd2742a744d8", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806714780800, "endTime": 26806741245500}, "additional": {"logType": "info", "children": [], "durationId": "c81c36cb-2b4d-461c-9be4-606a15b91a82"}}, {"head": {"id": "08bbb50e-f86b-494b-b13e-03e39dd09f6c", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806749540900, "endTime": 26806750915000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4fd069fe-b90e-4f05-bbe5-dd086622458d", "logId": "7c50cc69-91fe-4535-a5bc-1b684aad0c83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fd069fe-b90e-4f05-bbe5-dd086622458d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806746569800}, "additional": {"logType": "detail", "children": [], "durationId": "08bbb50e-f86b-494b-b13e-03e39dd09f6c"}}, {"head": {"id": "fd8bae91-0ddd-4ded-ad57-4a408ae246e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806747082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5419b4c5-8d59-4746-a307-73313da38706", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806747201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b5f979f-3815-498d-accf-8de13ee0e15c", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806749552600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877a1186-9c9a-485d-b545-9ed2faa2f300", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806749828600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eed11f81-134f-4133-a6f2-85a84a649e0c", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806750715800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52573da1-01cc-48cd-b892-a47c88663062", "name": "entry : default@DoNativeStrip cost memory 0.07740020751953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806750838900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c50cc69-91fe-4535-a5bc-1b684aad0c83", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806749540900, "endTime": 26806750915000}, "additional": {"logType": "info", "children": [], "durationId": "08bbb50e-f86b-494b-b13e-03e39dd09f6c"}}, {"head": {"id": "a29c31fe-d881-4f16-b608-45bdb8a4374a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806760612900, "endTime": 26808416016400}, "additional": {"children": ["5b3263ec-4fff-4034-bcc4-714fc3387e6f", "857fa5c6-17a8-4c26-b7aa-08f276cc3867"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "68debd6b-81c2-40a4-9d70-0a3af813926b", "logId": "3cebc9f3-47ec-44e9-af1a-58458715defb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68debd6b-81c2-40a4-9d70-0a3af813926b", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806752663000}, "additional": {"logType": "detail", "children": [], "durationId": "a29c31fe-d881-4f16-b608-45bdb8a4374a"}}, {"head": {"id": "9986b859-e892-4e9c-90a2-979c0620e9f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806753049200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d275bc13-2819-4018-b075-cd7f14fe93ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806753162500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd5cac9-ebcd-495b-aecf-eee4c9929a04", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806760626500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad97becd-dd78-4606-9c29-7906024ae5a5", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806776059100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090ba624-7345-43a4-bcff-e2a6d0ecf346", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806776265100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af705b07-5437-4c5a-81c8-f13733405916", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806796318600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6d6656-c739-4532-89af-16e0fb83e00f", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806796804100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef90f8cd-21fa-43bc-b855-2034b52b695a", "name": "default@CompileArkTS work[24] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806798052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26807021308100, "endTime": 26808194092200}, "additional": {"children": ["5456d670-033e-4ff5-a263-cec979279dab", "e9aa5a9f-4e67-455a-9a03-6b34d5dec991", "55786b00-3e56-4e93-8db7-394f5115857c", "b01e03f2-e4db-49e4-9330-05a188741a12", "13d5748d-a401-4d21-b5a4-e687fb81bf93", "c4286228-e99b-4028-ad22-a3fd5bf74381"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a29c31fe-d881-4f16-b608-45bdb8a4374a", "logId": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f73e39f3-e5af-4567-a9bb-783c9214f712", "name": "default@CompileArkTS work[24] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806798970700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6ce6425-cefe-42e5-8c3a-6c75c6b10c04", "name": "default@CompileArkTS work[24] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806799093100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41e348d9-5b57-4223-b159-2a4b735c84bd", "name": "CopyResources startTime: 26806799231100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806799234900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db08ce53-efd7-4560-bd60-24cca79a6fbe", "name": "default@CompileArkTS work[25] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806799499800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "857fa5c6-17a8-4c26-b7aa-08f276cc3867", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26808397946600, "endTime": 26808415009900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a29c31fe-d881-4f16-b608-45bdb8a4374a", "logId": "6f0c299f-862d-497e-8845-3ac3be5878a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b6f848d-b32d-4b7f-93c6-7378af527c96", "name": "default@CompileArkTS work[25] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806800418600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "652fea22-7922-4b05-8dbd-27892b377f52", "name": "default@CompileArkTS work[25] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806800533100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "884d6e75-4d4e-49fa-beac-05be0b8147e9", "name": "entry : default@CompileArkTS cost memory 1.5291366577148438", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806800637800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c03c2e-e335-458c-bb37-27a1ab3cc570", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806811440600, "endTime": 26806815044800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "fa7adfd5-d747-45bb-a9ef-e4bfbb4ce441", "logId": "961a0d90-35af-42a6-aa3f-1411bb41ce19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa7adfd5-d747-45bb-a9ef-e4bfbb4ce441", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806802462700}, "additional": {"logType": "detail", "children": [], "durationId": "c6c03c2e-e335-458c-bb37-27a1ab3cc570"}}, {"head": {"id": "f70d2b6c-6935-428a-b0b1-021c7f61f9da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806802964300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00fb701f-f999-4184-ae9a-448b5ca07898", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806803094300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393b664f-9dfe-44c7-9061-bbd5c260e57e", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806811456100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "648653a0-7b3f-4f0d-ac01-2c0d652dfeb1", "name": "entry : default@BuildJS cost memory 0.1287994384765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806814823700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adf5ff02-0b78-47f3-8e38-af09ec092b23", "name": "runTaskFromQueue task cost before running: 1 s 34 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806814976700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "961a0d90-35af-42a6-aa3f-1411bb41ce19", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806811440600, "endTime": 26806815044800, "totalTime": 3513900}, "additional": {"logType": "info", "children": [], "durationId": "c6c03c2e-e335-458c-bb37-27a1ab3cc570"}}, {"head": {"id": "1d8f7c8d-e108-496a-8d7c-d14994367cfa", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806821673700, "endTime": 26806824787200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ed2ba37a-c1f5-4d3b-92d5-a4d1836e0338", "logId": "3725bcc4-3915-4e9b-a1d8-3daf75987e90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed2ba37a-c1f5-4d3b-92d5-a4d1836e0338", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806817263400}, "additional": {"logType": "detail", "children": [], "durationId": "1d8f7c8d-e108-496a-8d7c-d14994367cfa"}}, {"head": {"id": "39b878a5-fc9c-46e4-8fb8-abdf564fb142", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806817624400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d31287b5-aa99-4de9-a248-a8259e69b2f4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806817755500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73164d73-6298-44cf-98e6-aa88cf77d97b", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806821689200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c10131-ddbc-45ce-a6bb-46deaaebc6f6", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806822071200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "214c4003-d2af-44ba-a5dc-5991a7e942b8", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806824386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1fb5d1d-4648-4c97-9a71-39826e19ce2e", "name": "entry : default@CacheNativeLibs cost memory 0.091949462890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806824590700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3725bcc4-3915-4e9b-a1d8-3daf75987e90", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806821673700, "endTime": 26806824787200}, "additional": {"logType": "info", "children": [], "durationId": "1d8f7c8d-e108-496a-8d7c-d14994367cfa"}}, {"head": {"id": "5e454126-cf46-4b4e-b8cb-263a23660cff", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807020544300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8282745-da9b-41b6-8c6c-75644d1f9f87", "name": "default@CompileArkTS work[24] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807021063300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c25312c3-6819-4cb9-8b8b-bec6c0ae1d22", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807129182300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e73097a-031e-4edb-9fc0-3dbd2bf6ad0a", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807129327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15bbbcad-639a-400a-a2a9-3dcba43b028f", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807129402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1f2571-2805-4426-a9f8-da9d5f6aace9", "name": "default@CompileArkTS work[25] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807130338400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18b2045c-1319-4742-93ba-9d0e953b7f98", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808194573100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5456d670-033e-4ff5-a263-cec979279dab", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26807021439200, "endTime": 26807027561000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "logId": "d109a4e9-7205-42c1-9344-453629<PERSON>de3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d109a4e9-7205-42c1-9344-453629<PERSON>de3", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807021439200, "endTime": 26807027561000}, "additional": {"logType": "info", "children": [], "durationId": "5456d670-033e-4ff5-a263-cec979279dab", "parent": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}}, {"head": {"id": "e9aa5a9f-4e67-455a-9a03-6b34d5dec991", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26807027581400, "endTime": 26807027695900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "logId": "2e131dce-d151-4fa0-a614-4e18698fe5e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e131dce-d151-4fa0-a614-4e18698fe5e1", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807027581400, "endTime": 26807027695900}, "additional": {"logType": "info", "children": [], "durationId": "e9aa5a9f-4e67-455a-9a03-6b34d5dec991", "parent": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}}, {"head": {"id": "55786b00-3e56-4e93-8db7-394f5115857c", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26807027704000, "endTime": 26807027742900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "logId": "d85e9d8c-d674-4ae1-9a28-140b5205c3fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d85e9d8c-d674-4ae1-9a28-140b5205c3fd", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807027704000, "endTime": 26807027742900}, "additional": {"logType": "info", "children": [], "durationId": "55786b00-3e56-4e93-8db7-394f5115857c", "parent": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}}, {"head": {"id": "b01e03f2-e4db-49e4-9330-05a188741a12", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26807027756400, "endTime": 26808111864000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "logId": "952a8f7f-1772-4b94-8840-7e7c87a5cb59"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "952a8f7f-1772-4b94-8840-7e7c87a5cb59", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26807027756400, "endTime": 26808111864000}, "additional": {"logType": "info", "children": [], "durationId": "b01e03f2-e4db-49e4-9330-05a188741a12", "parent": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}}, {"head": {"id": "13d5748d-a401-4d21-b5a4-e687fb81bf93", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808111888900, "endTime": 26808120548500}, "additional": {"children": ["0f6803cf-3782-4b3b-9831-3b179354117c", "05794ed1-2f6c-426a-8da2-bbcaa9a32aa0", "a7746b59-e9fe-4132-aa0b-d324283e9558"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "logId": "4bcd6475-d5a2-4906-b4fb-ce1024160c9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bcd6475-d5a2-4906-b4fb-ce1024160c9e", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808111888900, "endTime": 26808120548500}, "additional": {"logType": "info", "children": ["391b37fe-a89d-4018-a475-b183d7cc3c7c", "0e290084-bbcf-4634-8a55-9f26af8858fe", "9d91b732-4d31-4255-aae9-e9ae1734ccd1"], "durationId": "13d5748d-a401-4d21-b5a4-e687fb81bf93", "parent": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}}, {"head": {"id": "0f6803cf-3782-4b3b-9831-3b179354117c", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808111911700, "endTime": 26808111917100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "13d5748d-a401-4d21-b5a4-e687fb81bf93", "logId": "391b37fe-a89d-4018-a475-b183d7cc3c7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "391b37fe-a89d-4018-a475-b183d7cc3c7c", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808111911700, "endTime": 26808111917100}, "additional": {"logType": "info", "children": [], "durationId": "0f6803cf-3782-4b3b-9831-3b179354117c", "parent": "4bcd6475-d5a2-4906-b4fb-ce1024160c9e"}}, {"head": {"id": "05794ed1-2f6c-426a-8da2-bbcaa9a32aa0", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808111920400, "endTime": 26808114266500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "13d5748d-a401-4d21-b5a4-e687fb81bf93", "logId": "0e290084-bbcf-4634-8a55-9f26af8858fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e290084-bbcf-4634-8a55-9f26af8858fe", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808111920400, "endTime": 26808114266500}, "additional": {"logType": "info", "children": [], "durationId": "05794ed1-2f6c-426a-8da2-bbcaa9a32aa0", "parent": "4bcd6475-d5a2-4906-b4fb-ce1024160c9e"}}, {"head": {"id": "a7746b59-e9fe-4132-aa0b-d324283e9558", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808114270700, "endTime": 26808120534700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "13d5748d-a401-4d21-b5a4-e687fb81bf93", "logId": "9d91b732-4d31-4255-aae9-e9ae1734ccd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d91b732-4d31-4255-aae9-e9ae1734ccd1", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808114270700, "endTime": 26808120534700}, "additional": {"logType": "info", "children": [], "durationId": "a7746b59-e9fe-4132-aa0b-d324283e9558", "parent": "4bcd6475-d5a2-4906-b4fb-ce1024160c9e"}}, {"head": {"id": "c4286228-e99b-4028-ad22-a3fd5bf74381", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808120563600, "endTime": 26808193926600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "logId": "40f0ba78-4adf-4ca2-9c19-66a0bf14812a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40f0ba78-4adf-4ca2-9c19-66a0bf14812a", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808120563600, "endTime": 26808193926600}, "additional": {"logType": "info", "children": [], "durationId": "c4286228-e99b-4028-ad22-a3fd5bf74381", "parent": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1"}}, {"head": {"id": "fe3653d7-efeb-40f8-bf52-7bbe0f82e217", "name": "default@CompileArkTS work[24] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808201735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26807021308100, "endTime": 26808194092200}, "additional": {"logType": "info", "children": ["d109a4e9-7205-42c1-9344-453629<PERSON>de3", "2e131dce-d151-4fa0-a614-4e18698fe5e1", "d85e9d8c-d674-4ae1-9a28-140b5205c3fd", "952a8f7f-1772-4b94-8840-7e7c87a5cb59", "4bcd6475-d5a2-4906-b4fb-ce1024160c9e", "40f0ba78-4adf-4ca2-9c19-66a0bf14812a"], "durationId": "5b3263ec-4fff-4034-bcc4-714fc3387e6f", "parent": "3cebc9f3-47ec-44e9-af1a-58458715defb"}}, {"head": {"id": "e0fa7263-4892-4d43-9889-1f0f83962e1b", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808415178700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a753f2c6-e0a1-438f-be9c-fce9da8940d1", "name": "CopyResources is end, endTime: 26808415474500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808415484600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92613595-0988-41e5-a0ef-7b56986abc8d", "name": "default@CompileArkTS work[25] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808415821200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0c299f-862d-497e-8845-3ac3be5878a9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26808397946600, "endTime": 26808415009900}, "additional": {"logType": "info", "children": [], "durationId": "857fa5c6-17a8-4c26-b7aa-08f276cc3867", "parent": "3cebc9f3-47ec-44e9-af1a-58458715defb"}}, {"head": {"id": "142d145b-219f-4675-b904-7d56dfce8d7a", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808415933800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cebc9f3-47ec-44e9-af1a-58458715defb", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26806760612900, "endTime": 26808416016400, "totalTime": 1229942500}, "additional": {"logType": "info", "children": ["4b2f435e-c0f9-4ff8-9c67-379fd17eb6f1", "6f0c299f-862d-497e-8845-3ac3be5878a9"], "durationId": "a29c31fe-d881-4f16-b608-45bdb8a4374a"}}, {"head": {"id": "e4d3af28-3435-4e7c-a20a-3eb4cb07cc53", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808426013700, "endTime": 26808428547600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "2e17e800-aed4-46e3-a9c5-9fff804999fc", "logId": "062500fc-781d-424b-a4cc-fba2ad14531f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e17e800-aed4-46e3-a9c5-9fff804999fc", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808423543300}, "additional": {"logType": "detail", "children": [], "durationId": "e4d3af28-3435-4e7c-a20a-3eb4cb07cc53"}}, {"head": {"id": "a93d33e9-dc0a-43bb-b29f-91fc9baaab60", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808424134600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "249b65d0-84d5-4c1b-a6fd-edda7549467b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808424249900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04387bc2-fcc9-4f90-b7dd-1f9f8161acd5", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808426094300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dd1f1b4-4977-42d8-a030-93c4ead2b02e", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808427323300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bce65cd7-28eb-44c7-86db-708e9ca40502", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808428231200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff595ef1-41c1-4802-97af-9e140b5bb56c", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07558441162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808428413000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "062500fc-781d-424b-a4cc-fba2ad14531f", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808426013700, "endTime": 26808428547600}, "additional": {"logType": "info", "children": [], "durationId": "e4d3af28-3435-4e7c-a20a-3eb4cb07cc53"}}, {"head": {"id": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808438943300, "endTime": 26808940912500}, "additional": {"children": ["f820865f-27e3-4640-9cdc-5db10fa1c377", "771449f6-d44f-4a8c-9398-c918680b88cb", "e3b2efc2-e823-4a4c-b102-30f6e8ff8b4c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "83fe2877-1bc8-4157-8c8b-2e1b38178ba3", "logId": "434dbd66-11c8-4afa-80f5-600f4cc074a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83fe2877-1bc8-4157-8c8b-2e1b38178ba3", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808431324700}, "additional": {"logType": "detail", "children": [], "durationId": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b"}}, {"head": {"id": "881f6388-4357-43d3-b1a1-11f8cee75c3c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808432011200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6945d8aa-a3e6-4304-8711-cdfc6fab9c9c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808432136300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dee85d0-580e-413d-b729-9427cf8beff8", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808438978000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5094c6c1-532d-4e8c-b9ab-a6581c931a6b", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808451122100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "973b3462-6e38-4732-a694-25e94bc35451", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808451267200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36fe54c7-f761-44cf-9e26-8283940decc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808451394200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647837ae-9b8c-48c9-ad81-ce95676b7fdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808451457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f820865f-27e3-4640-9cdc-5db10fa1c377", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808452184600, "endTime": 26808453351500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b", "logId": "ae8927dd-4ae5-4bc4-bc3f-b8a915acb225"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93768d73-029f-4ce4-b815-86a486e57f5d", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808453178500}, "additional": {"logType": "debug", "children": [], "durationId": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b"}}, {"head": {"id": "ae8927dd-4ae5-4bc4-bc3f-b8a915acb225", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808452184600, "endTime": 26808453351500}, "additional": {"logType": "info", "children": [], "durationId": "f820865f-27e3-4640-9cdc-5db10fa1c377", "parent": "434dbd66-11c8-4afa-80f5-600f4cc074a1"}}, {"head": {"id": "771449f6-d44f-4a8c-9398-c918680b88cb", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808453928600, "endTime": 26808457224400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b", "logId": "3b7a4b8e-b32d-416d-a775-feda70f9abac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3aab0f1-681b-4c5d-be66-3269f7938b7c", "name": "default@PackageHap work[26] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808454578300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b2efc2-e823-4a4c-b102-30f6e8ff8b4c", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808457133000, "endTime": 26808940355700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b", "logId": "a061678f-44d2-4eab-81de-903ae126f3e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0c3cb6e-a5bf-4d27-a033-7fbb2c7d0c20", "name": "default@PackageHap work[26] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808455400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fc32026-93a5-4064-aebe-f42adc3ecc78", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808455736200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147a5923-748e-4316-ad5a-45410a32228d", "name": "default@PackageHap work[26] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808456978000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29fc913b-07c6-492a-8fc3-744c1989cddf", "name": "default@PackageHap work[26] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808457147600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7a4b8e-b32d-416d-a775-feda70f9abac", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808453928600, "endTime": 26808457224400}, "additional": {"logType": "info", "children": [], "durationId": "771449f6-d44f-4a8c-9398-c918680b88cb", "parent": "434dbd66-11c8-4afa-80f5-600f4cc074a1"}}, {"head": {"id": "523dbc2e-b0c1-4dac-aca7-8cdef324866d", "name": "entry : default@PackageHap cost memory 1.3269271850585938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808465418500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e5b2e0-4363-433d-b20a-a73df11a367d", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808465610300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeddbcd1-57db-4324-9b3a-6176ecad6713", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808940443900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6fb04a7-37ad-48fd-980c-44ff903f1746", "name": "default@PackageHap work[26] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808940595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a061678f-44d2-4eab-81de-903ae126f3e6", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26808457133000, "endTime": 26808940355700}, "additional": {"logType": "info", "children": [], "durationId": "e3b2efc2-e823-4a4c-b102-30f6e8ff8b4c", "parent": "434dbd66-11c8-4afa-80f5-600f4cc074a1"}}, {"head": {"id": "6d2dd6ae-0e78-472b-a6b0-553359003bc0", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808940679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "434dbd66-11c8-4afa-80f5-600f4cc074a1", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808438943300, "endTime": 26808940912500, "totalTime": 501427200}, "additional": {"logType": "info", "children": ["ae8927dd-4ae5-4bc4-bc3f-b8a915acb225", "3b7a4b8e-b32d-416d-a775-feda70f9abac", "a061678f-44d2-4eab-81de-903ae126f3e6"], "durationId": "b2b9c0c6-c9da-4870-9c0b-c81e7827c96b"}}, {"head": {"id": "6b7d4687-25c4-4e5e-9c5c-3234eee7e34b", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808948499400, "endTime": 26808950118400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "25bbfd41-b169-4229-be34-c7a03490ec1b", "logId": "a716ae3d-e410-4e85-9338-ba80c6cfe34d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25bbfd41-b169-4229-be34-c7a03490ec1b", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808945456100}, "additional": {"logType": "detail", "children": [], "durationId": "6b7d4687-25c4-4e5e-9c5c-3234eee7e34b"}}, {"head": {"id": "b9dbd838-e47c-474f-9682-c5ffe1cba592", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808945859700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf409c32-21b2-4ec8-9f87-cd53cbf3eaff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808945973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d00247-984e-4924-bd63-2ecc263333a1", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808948510700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6551362-d94b-44df-8e3e-285820cccdab", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808948873200}, "additional": {"logType": "warn", "children": [], "durationId": "6b7d4687-25c4-4e5e-9c5c-3234eee7e34b"}}, {"head": {"id": "07fcf93d-4ea9-42e4-a899-3d72da25540f", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808949457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a4b2140-edf4-448d-953f-25a9e049b141", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808949559600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec155ba-eab9-49e0-92f2-65aef242261d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808949644400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c94d2ee-52a2-42c2-80d3-381caee33fb9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808949702600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a519e3b7-6d68-4412-987c-cd6c34a7d86e", "name": "entry : default@SignHap cost memory 0.1184844970703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808949949800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f2e5070-87a6-4287-ad0d-b25b746dc2e1", "name": "runTaskFromQueue task cost before running: 3 s 169 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808950055100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a716ae3d-e410-4e85-9338-ba80c6cfe34d", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808948499400, "endTime": 26808950118400, "totalTime": 1535400}, "additional": {"logType": "info", "children": [], "durationId": "6b7d4687-25c4-4e5e-9c5c-3234eee7e34b"}}, {"head": {"id": "35b03153-5697-4c7c-9a01-7a296dc2542f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808953049200, "endTime": 26808958411800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8aa4d30c-6555-4e9f-b05f-895b4f998df8", "logId": "c3243032-20ef-4470-806b-bce49be34c1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8aa4d30c-6555-4e9f-b05f-895b4f998df8", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808951815700}, "additional": {"logType": "detail", "children": [], "durationId": "35b03153-5697-4c7c-9a01-7a296dc2542f"}}, {"head": {"id": "a4061a45-88b0-46bb-b4a6-ffee676d3a87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808952143400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b5d1efb-157f-4b9f-a798-3bb9efa2dffc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808952235400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40732646-ed9d-40fb-a300-f6575ec209c2", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808953058500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a3361d5-a973-415b-a377-b9cc38ec2259", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808957725800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d3f859-c949-49f5-b265-0cecd511b62e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808957967200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a576101-106b-49a0-98d5-a89adac11045", "name": "entry : default@CollectDebugSymbol cost memory 0.24160003662109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808958102000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f4420a-ec92-4d6c-b660-8826d49a62f5", "name": "runTaskFromQueue task cost before running: 3 s 177 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808958254400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3243032-20ef-4470-806b-bce49be34c1b", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808953049200, "endTime": 26808958411800, "totalTime": 5164600}, "additional": {"logType": "info", "children": [], "durationId": "35b03153-5697-4c7c-9a01-7a296dc2542f"}}, {"head": {"id": "c6582bfd-fd9c-486c-ace9-cd04a1a72d15", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808961201600, "endTime": 26808961516000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "24eac1e4-7d3c-485f-97b1-6722de098c9f", "logId": "0623050b-a00c-410e-b540-149d5e579059"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24eac1e4-7d3c-485f-97b1-6722de098c9f", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808961148300}, "additional": {"logType": "detail", "children": [], "durationId": "c6582bfd-fd9c-486c-ace9-cd04a1a72d15"}}, {"head": {"id": "cc1bf83d-12b6-4fc5-a370-d7b20a773a7e", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808961209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408abfb8-7b46-4c40-bcf8-96a4cd61db03", "name": "entry : assembleHap cost memory 0.011688232421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808961366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "543605ad-6fc0-4a00-aa91-d0461097b87f", "name": "runTaskFromQueue task cost before running: 3 s 181 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808961459600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0623050b-a00c-410e-b540-149d5e579059", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808961201600, "endTime": 26808961516000, "totalTime": 237100}, "additional": {"logType": "info", "children": [], "durationId": "c6582bfd-fd9c-486c-ace9-cd04a1a72d15"}}, {"head": {"id": "de92ea47-cc0b-4bb8-82b1-7071653d1ba3", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808968588400, "endTime": 26808968611200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a6b8903-7ee5-4807-b013-189294b00a92", "logId": "21529c15-e24e-4a18-bebe-b5a8540d4ff5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21529c15-e24e-4a18-bebe-b5a8540d4ff5", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808968588400, "endTime": 26808968611200}, "additional": {"logType": "info", "children": [], "durationId": "de92ea47-cc0b-4bb8-82b1-7071653d1ba3"}}, {"head": {"id": "cee83927-6dab-4785-af71-9de743e31987", "name": "BUILD SUCCESSFUL in 3 s 188 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808968648800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "106c691c-d91c-44ad-8f87-b4a1669da4ec", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26805781423900, "endTime": 26808968965200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "71c1346a-350b-44fe-8cd0-18150285d57c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808968998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3668e9d4-47b0-4e60-bfb7-a5e227ca3ce7", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808969078800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82877f6-f4c8-4793-ba8a-ef60c6f2f611", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808969137000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7daa395e-e06b-48c9-b612-c02cd5a6fa66", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808969210700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80c48384-98f1-492c-9a22-adaed864b8f8", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808969274400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53998af4-b664-439e-95a7-c0e0657d7b6e", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808969668900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98680f5-94c3-4d88-a228-bbd3dddec378", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808970378500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc600ea1-8783-4677-afd2-08e5d34bd496", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808970645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "844e8afd-a272-45e9-a042-89b603a0d63a", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808970736500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2e67df-bbc0-4f9b-bcdc-83d32c67dd02", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808970870200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f199b5-d636-4df6-8aab-4f3ca199986a", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808971118700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c7e6b8e-b79e-4c98-9c03-d64d81a3b833", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808972369900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2ec6607-80c6-4af2-8299-7ae436a879a7", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808972838200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c80a67-141a-4826-99e0-0c477661f5ee", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808973084100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1354eff-8943-44ef-b2bf-d7cac43040cf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808973192400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f58c624-efe2-4872-bbca-2268139b5305", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808973430300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f340623-f9f0-4961-8f76-84c8b27a18e4", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808973506300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce69f022-0f78-44c2-8743-1da193be9484", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808973999300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c58b1f1-8c78-4926-a034-91d1892ff659", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808974530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6abb95a0-2aaa-422f-882f-87377929ff84", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808974759100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afd5c37-ad05-46f5-9f62-93bb943e8c7c", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808975104100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "831bdec9-0f68-4aa3-8ef9-eea782e08100", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808975182000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db622763-6d81-47e9-be53-ed34213b1998", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808975248700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b000fa1c-809f-4157-8996-bf8970bfce92", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808978035800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a6573c-da1a-47d6-a744-5d610f76ef13", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808978462600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5238ad5-c0eb-4513-9504-f4f9c5fe7607", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808979189500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5697bf-547f-43df-9421-6c8d5f3ade7e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808979400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ccfe2ed-30c6-4a61-b636-74dc5fc4071f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808979585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57e96767-441e-4b91-861f-87c7e41e7557", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808980086600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "952e0c87-6f3e-4870-8172-2ff2e50ba3ea", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808980161100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "555b2e84-9265-4c8d-b921-7a7736920f18", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808980328400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecbdb659-4fe1-4655-a082-754e1b5cd30e", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808980520300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c34472c8-9a33-47fe-abd4-69fc73e0f4db", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808981160700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03fd35d9-6cd7-4ec0-b2ac-c401ce993f56", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808982211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079d0172-82c9-49d9-8b05-191346af561a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808982665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "846aa04a-5de1-4560-8f37-a45c9e8e8e46", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808984604700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78842918-0d55-4a6f-a33b-125163108d2f", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808985171500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43fb39f4-605c-457e-a83f-e1ece3b427ea", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808985506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "061ca410-f7a2-4b48-820c-19eaacd5c3c2", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808986100700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d061413d-5293-4313-8b87-786ea786b8da", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808986387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c97ee7-845f-4d06-9860-d37e3c80a7d1", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808986475300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29759289-39fb-4894-a7a3-642f0c15729c", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808986532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0f0182e-72cc-447e-8753-26784dad5897", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808987285900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89b30528-2c9d-4373-850a-58f9e8e23e56", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808987523300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff5534b8-f7d1-4bb3-bfc3-7ae806feb579", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808987706800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e93f2f6-b93a-4b48-800f-1cf9922e36fd", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26808999312600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e474d455-32ea-4927-a471-38d2859c02a7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809000171400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cf30a8c-39a6-4bde-9f58-ebd98819f7f3", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809001044800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0da2a4-8cf8-4701-a95f-3b7a3324b533", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809001217200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "497f4386-735e-4f88-b98f-9a3af6138240", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809001506300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00ff5642-3442-4d2d-9d9a-db71a41913c9", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809002160600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7632c6e-aead-48b9-81f1-4b13190cd05d", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809002488200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f47cae52-080e-49fa-b48e-4aa7eee0be64", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809002680100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a03a4baa-3573-4168-90d5-969976c2d97c", "name": "Incremental task entry:default@PackageHap post-execution cost:17 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809002904100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8fd5f12-2f78-4503-a46b-c3d85f1f2920", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809003101600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a535adc-4ad0-4403-8123-30564ba9572d", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809003190400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32700a4c-5fe7-48ab-88d2-0a49c0730ed4", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809003418400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aedffd05-3c3b-4ac2-98f7-f8b5171e70ac", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809010825600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75fa192-0a38-4733-aca1-df04affffd54", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809011204100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ee4a20-ca2d-41c4-aea1-e9e702064cd3", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809011487100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d30fae83-4308-429d-aa77-02a61baab54a", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26809011725500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}