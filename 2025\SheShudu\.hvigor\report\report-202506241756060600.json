{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "98067828-50ed-4824-b9c4-05bee461f2d2", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930024963000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a268bdd-4c09-4184-b8fd-27005d3f527b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930031664800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aaac504-83f6-45e8-ab7b-366a018c27cd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930032515400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53711fe5-6681-401e-a1e5-10f67ef76bfe", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33930039487900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ecc7b52-b0a0-487b-8a53-82a29dc1e6ee", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997953075400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997962313200, "endTime": 33998266983600}, "additional": {"children": ["be8c0781-726a-446e-bfd7-86dce4bd9b46", "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "0610b3bd-0ee7-41ce-a3f8-686496ff6cfe", "cf038a60-2d10-4695-81f4-f619b7eba389", "d0b11ba2-96db-4406-8327-57640716804f", "031229ef-8f77-4f91-84d2-739612847b62", "68975922-8307-40ff-821a-6bf728701cbb"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be8c0781-726a-446e-bfd7-86dce4bd9b46", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997962317200, "endTime": 33997976927800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "7dc3c184-d36e-451a-a517-ec629c9f51b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997976948500, "endTime": 33998265831200}, "additional": {"children": ["3223c955-ee61-484e-9cd9-9ef5eaafe603", "17263318-cdef-4893-bf08-1959c1a024fe", "291d96d5-6a8b-4d24-b7ea-a970aee579b6", "a73994bb-d51b-4f40-892e-a8ba58d38fbe", "b6944985-0629-45b9-bbc8-de26369686d0", "c2988a25-088f-4faf-854b-0ca2d84dcfaf", "a2ae098b-0799-41ed-af29-37328d6d40cf", "fad133b5-62af-4061-8a1e-dcfb56d09210", "8ec48f32-9df1-4b18-87c4-bb0dcc9521b7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0610b3bd-0ee7-41ce-a3f8-686496ff6cfe", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998265856800, "endTime": 33998266977100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "172ceb9b-8e5c-4ff1-a2e8-d64da8dd35f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf038a60-2d10-4695-81f4-f619b7eba389", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998266980600, "endTime": 33998266981500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "f18baedd-6326-4d53-8861-6b9f56469b16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0b11ba2-96db-4406-8327-57640716804f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997965764800, "endTime": 33997965805200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "31f56a37-135b-42cf-a79c-2b6e29e0db5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31f56a37-135b-42cf-a79c-2b6e29e0db5e", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997965764800, "endTime": 33997965805200}, "additional": {"logType": "info", "children": [], "durationId": "d0b11ba2-96db-4406-8327-57640716804f", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "031229ef-8f77-4f91-84d2-739612847b62", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997972005300, "endTime": 33997972029600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "59f72ab7-8911-442b-9ed1-7edecac6f356"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59f72ab7-8911-442b-9ed1-7edecac6f356", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997972005300, "endTime": 33997972029600}, "additional": {"logType": "info", "children": [], "durationId": "031229ef-8f77-4f91-84d2-739612847b62", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "17a746e4-8cb7-4f99-993a-31fff205c4ab", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997972083200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d544fe78-51db-4bc6-9d98-b3277138867b", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997976787400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dc3c184-d36e-451a-a517-ec629c9f51b9", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997962317200, "endTime": 33997976927800}, "additional": {"logType": "info", "children": [], "durationId": "be8c0781-726a-446e-bfd7-86dce4bd9b46", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "3223c955-ee61-484e-9cd9-9ef5eaafe603", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997982705600, "endTime": 33997982714200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "fcbb02a2-747a-4ade-8825-4600e696d47a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17263318-cdef-4893-bf08-1959c1a024fe", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997982728900, "endTime": 33997988552000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "07e0c9a4-a501-44e5-8bcc-6993a510c754"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "291d96d5-6a8b-4d24-b7ea-a970aee579b6", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997988572200, "endTime": 33998137796000}, "additional": {"children": ["b619b46d-7993-4d4b-9cad-163516addc85", "38f3a1e7-4e96-43d3-ae4f-babceb9bf101", "465691dd-73ae-47ff-8712-ed98e891f2d3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "f830191e-638c-400b-9670-8e806f5df57d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a73994bb-d51b-4f40-892e-a8ba58d38fbe", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998137809000, "endTime": 33998163850500}, "additional": {"children": ["f23373e3-2cad-4863-8dbc-3f54dace2897"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "58055d11-7262-49e2-89b5-aa2261785b78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6944985-0629-45b9-bbc8-de26369686d0", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998163865100, "endTime": 33998246169800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "47da3899-2c70-4133-9a6e-7926a4dcf4c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2988a25-088f-4faf-854b-0ca2d84dcfaf", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998247322300, "endTime": 33998256358700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "74464121-8195-48dd-96c2-080b3cf39f46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2ae098b-0799-41ed-af29-37328d6d40cf", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998256382200, "endTime": 33998265588800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "297c67fe-e59d-44ad-b1e9-2956e794754f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fad133b5-62af-4061-8a1e-dcfb56d09210", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998265606300, "endTime": 33998265817600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "21315fec-1298-42f5-8a1d-44ad9f36a916"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcbb02a2-747a-4ade-8825-4600e696d47a", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997982705600, "endTime": 33997982714200}, "additional": {"logType": "info", "children": [], "durationId": "3223c955-ee61-484e-9cd9-9ef5eaafe603", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "07e0c9a4-a501-44e5-8bcc-6993a510c754", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997982728900, "endTime": 33997988552000}, "additional": {"logType": "info", "children": [], "durationId": "17263318-cdef-4893-bf08-1959c1a024fe", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "b619b46d-7993-4d4b-9cad-163516addc85", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997989315500, "endTime": 33997989336000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "291d96d5-6a8b-4d24-b7ea-a970aee579b6", "logId": "dc81dc4a-7cc5-43e0-a674-2226c9f51e3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc81dc4a-7cc5-43e0-a674-2226c9f51e3d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997989315500, "endTime": 33997989336000}, "additional": {"logType": "info", "children": [], "durationId": "b619b46d-7993-4d4b-9cad-163516addc85", "parent": "f830191e-638c-400b-9670-8e806f5df57d"}}, {"head": {"id": "38f3a1e7-4e96-43d3-ae4f-babceb9bf101", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997991354800, "endTime": 33998136541800}, "additional": {"children": ["10f89f2b-11e6-459d-bc4c-c37f394e0656", "0a360fb2-bf1d-4651-a462-48cb5e08a677"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "291d96d5-6a8b-4d24-b7ea-a970aee579b6", "logId": "3881d1b8-3406-470e-bc96-d47c7dbe7edd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10f89f2b-11e6-459d-bc4c-c37f394e0656", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997991356500, "endTime": 33997996946300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38f3a1e7-4e96-43d3-ae4f-babceb9bf101", "logId": "30d6a1a3-d15b-4261-ab4e-ef6f7ee087a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a360fb2-bf1d-4651-a462-48cb5e08a677", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997996965600, "endTime": 33998136525900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "38f3a1e7-4e96-43d3-ae4f-babceb9bf101", "logId": "200b60d3-ad2d-4a2d-b807-2daa4a03d46e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85f30b01-9cff-4ce6-ad13-bb933b2ad334", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997991361500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2887490-15da-42e7-bcec-9bffa129c422", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997996797000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d6a1a3-d15b-4261-ab4e-ef6f7ee087a8", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997991356500, "endTime": 33997996946300}, "additional": {"logType": "info", "children": [], "durationId": "10f89f2b-11e6-459d-bc4c-c37f394e0656", "parent": "3881d1b8-3406-470e-bc96-d47c7dbe7edd"}}, {"head": {"id": "d64e5626-567a-4030-9167-92c6398c2ba2", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997996978400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684e2863-faad-4743-b2e8-44f535a3104d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998003971600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342677ca-aca1-4a88-b257-8b5721a436a3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998004146400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e4af5df-3644-4cd8-91fc-a6e584225761", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998004314500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c73820e-d893-456a-a842-7b8e80b4f16b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998004417100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ad18c9-ce95-42ee-8b83-31de11e56341", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998005860400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f92befc-e1cd-4259-817b-595347234ee8", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998010352600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5445776b-1e73-4925-b41a-6dc2dacc44fc", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998023575900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3d53a58-7b7d-4cc8-8f6f-14e69722c4c5", "name": "Sdk init in 96 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998106969800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba640276-ee67-4093-9abd-8666547a7d1f", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998107149900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 56}, "markType": "other"}}, {"head": {"id": "0c13399d-4c8e-4457-a24d-160d4849abcd", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998107165900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 56}, "markType": "other"}}, {"head": {"id": "baffa073-cbbe-492a-b036-d991e9d2c523", "name": "Project task initialization takes 28 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998136170200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6881c13e-4af6-4dd3-b8cc-03b36f10a250", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998136339500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0008954-3df7-41f6-9b3d-3744ae17ee45", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998136422900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9efff10e-96c8-4957-8314-d8e41f996bab", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998136475500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200b60d3-ad2d-4a2d-b807-2daa4a03d46e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997996965600, "endTime": 33998136525900}, "additional": {"logType": "info", "children": [], "durationId": "0a360fb2-bf1d-4651-a462-48cb5e08a677", "parent": "3881d1b8-3406-470e-bc96-d47c7dbe7edd"}}, {"head": {"id": "3881d1b8-3406-470e-bc96-d47c7dbe7edd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997991354800, "endTime": 33998136541800}, "additional": {"logType": "info", "children": ["30d6a1a3-d15b-4261-ab4e-ef6f7ee087a8", "200b60d3-ad2d-4a2d-b807-2daa4a03d46e"], "durationId": "38f3a1e7-4e96-43d3-ae4f-babceb9bf101", "parent": "f830191e-638c-400b-9670-8e806f5df57d"}}, {"head": {"id": "465691dd-73ae-47ff-8712-ed98e891f2d3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998137759900, "endTime": 33998137778900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "291d96d5-6a8b-4d24-b7ea-a970aee579b6", "logId": "40650012-9d3b-4399-abdf-ee9c32512e18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40650012-9d3b-4399-abdf-ee9c32512e18", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998137759900, "endTime": 33998137778900}, "additional": {"logType": "info", "children": [], "durationId": "465691dd-73ae-47ff-8712-ed98e891f2d3", "parent": "f830191e-638c-400b-9670-8e806f5df57d"}}, {"head": {"id": "f830191e-638c-400b-9670-8e806f5df57d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997988572200, "endTime": 33998137796000}, "additional": {"logType": "info", "children": ["dc81dc4a-7cc5-43e0-a674-2226c9f51e3d", "3881d1b8-3406-470e-bc96-d47c7dbe7edd", "40650012-9d3b-4399-abdf-ee9c32512e18"], "durationId": "291d96d5-6a8b-4d24-b7ea-a970aee579b6", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "f23373e3-2cad-4863-8dbc-3f54dace2897", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998138493800, "endTime": 33998163828400}, "additional": {"children": ["42b4681d-f075-4378-8aa0-5a12ad4d4c41", "64639b22-13a5-460e-bc6b-099ecc78e63d", "688e411d-ff69-4882-b8b3-9c9ed268abaf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a73994bb-d51b-4f40-892e-a8ba58d38fbe", "logId": "cbf0ad44-6de8-4c2a-a795-40f2ff144cc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42b4681d-f075-4378-8aa0-5a12ad4d4c41", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998142399000, "endTime": 33998142418700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f23373e3-2cad-4863-8dbc-3f54dace2897", "logId": "2b3a04d6-92f6-48f5-bfc3-a4f4c242c695"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b3a04d6-92f6-48f5-bfc3-a4f4c242c695", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998142399000, "endTime": 33998142418700}, "additional": {"logType": "info", "children": [], "durationId": "42b4681d-f075-4378-8aa0-5a12ad4d4c41", "parent": "cbf0ad44-6de8-4c2a-a795-40f2ff144cc7"}}, {"head": {"id": "64639b22-13a5-460e-bc6b-099ecc78e63d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998144627100, "endTime": 33998162221500}, "additional": {"children": ["49c12157-267f-4d0b-a46b-0eaba7e10430", "cd90c0e3-284b-40b5-8fe4-b22b75304927"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f23373e3-2cad-4863-8dbc-3f54dace2897", "logId": "3c98b53b-455e-4b10-8863-95ed08ffb533"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49c12157-267f-4d0b-a46b-0eaba7e10430", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998144629100, "endTime": 33998149768400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64639b22-13a5-460e-bc6b-099ecc78e63d", "logId": "1373e889-eadc-4ddc-860f-2a62ca64f972"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd90c0e3-284b-40b5-8fe4-b22b75304927", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998149787900, "endTime": 33998162204300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64639b22-13a5-460e-bc6b-099ecc78e63d", "logId": "d6a451a8-c281-4053-927c-7be14aa40527"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59690636-4c5b-482b-ba7e-a12982a6ff78", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998144636000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2edd158-33d7-4255-b3fa-b6131fc0adc5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998149646300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1373e889-eadc-4ddc-860f-2a62ca64f972", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998144629100, "endTime": 33998149768400}, "additional": {"logType": "info", "children": [], "durationId": "49c12157-267f-4d0b-a46b-0eaba7e10430", "parent": "3c98b53b-455e-4b10-8863-95ed08ffb533"}}, {"head": {"id": "a7bc76c1-dc01-4a04-9b53-9f6300f5f0a8", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998149795500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdd593a6-d12f-478f-bce7-206f0de351e4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998157625400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4682fef0-39de-482c-9f9a-03802086d706", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998157764500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "066479f0-cdf6-41bc-a96d-1276e8473da7", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998157988100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6e481b0-c09c-43b7-9d6b-6cfb9a58f890", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998158156200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d39cd46b-9093-4d3b-a809-6af48a8650f0", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998158233700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22918e73-412b-449f-a459-ee9b7de19791", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998158283700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e01c182-9a14-47c0-9e46-a7abc825ecb0", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998158338000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6571a5d5-89bd-4ffe-bbc8-5a3985fc9099", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998161733000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac12b48-6466-46b1-84f9-5a4d216c2e80", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998161900200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e91d50-54ee-43b0-9406-f74ef7d72d57", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998161959400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e01c6c45-3c4b-4d89-89be-2ccec7c82bed", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998162015600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a451a8-c281-4053-927c-7be14aa40527", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998149787900, "endTime": 33998162204300}, "additional": {"logType": "info", "children": [], "durationId": "cd90c0e3-284b-40b5-8fe4-b22b75304927", "parent": "3c98b53b-455e-4b10-8863-95ed08ffb533"}}, {"head": {"id": "3c98b53b-455e-4b10-8863-95ed08ffb533", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998144627100, "endTime": 33998162221500}, "additional": {"logType": "info", "children": ["1373e889-eadc-4ddc-860f-2a62ca64f972", "d6a451a8-c281-4053-927c-7be14aa40527"], "durationId": "64639b22-13a5-460e-bc6b-099ecc78e63d", "parent": "cbf0ad44-6de8-4c2a-a795-40f2ff144cc7"}}, {"head": {"id": "688e411d-ff69-4882-b8b3-9c9ed268abaf", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998163768600, "endTime": 33998163786400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f23373e3-2cad-4863-8dbc-3f54dace2897", "logId": "7c4b6ce1-0220-42ca-85cd-7e34035df590"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c4b6ce1-0220-42ca-85cd-7e34035df590", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998163768600, "endTime": 33998163786400}, "additional": {"logType": "info", "children": [], "durationId": "688e411d-ff69-4882-b8b3-9c9ed268abaf", "parent": "cbf0ad44-6de8-4c2a-a795-40f2ff144cc7"}}, {"head": {"id": "cbf0ad44-6de8-4c2a-a795-40f2ff144cc7", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998138493800, "endTime": 33998163828400}, "additional": {"logType": "info", "children": ["2b3a04d6-92f6-48f5-bfc3-a4f4c242c695", "3c98b53b-455e-4b10-8863-95ed08ffb533", "7c4b6ce1-0220-42ca-85cd-7e34035df590"], "durationId": "f23373e3-2cad-4863-8dbc-3f54dace2897", "parent": "58055d11-7262-49e2-89b5-aa2261785b78"}}, {"head": {"id": "58055d11-7262-49e2-89b5-aa2261785b78", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998137809000, "endTime": 33998163850500}, "additional": {"logType": "info", "children": ["cbf0ad44-6de8-4c2a-a795-40f2ff144cc7"], "durationId": "a73994bb-d51b-4f40-892e-a8ba58d38fbe", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "4e661c63-d629-4d3b-83bc-86740fdeec0e", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998190273400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f8a3b99-0ba2-41ab-acc6-cf5040399963", "name": "hvigorfile, resolve hvigorfile dependencies in 83 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998246047500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47da3899-2c70-4133-9a6e-7926a4dcf4c1", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998163865100, "endTime": 33998246169800}, "additional": {"logType": "info", "children": [], "durationId": "b6944985-0629-45b9-bbc8-de26369686d0", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "8ec48f32-9df1-4b18-87c4-bb0dcc9521b7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998246902300, "endTime": 33998247304900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "logId": "6fa372cf-1241-422d-ac85-6b894e8fc8bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46ec231e-c51f-4c0a-975b-76563c495b8e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998246927600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa372cf-1241-422d-ac85-6b894e8fc8bc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998246902300, "endTime": 33998247304900}, "additional": {"logType": "info", "children": [], "durationId": "8ec48f32-9df1-4b18-87c4-bb0dcc9521b7", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "14a63b34-9104-47e3-a7fa-b6ec2fbaa572", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998248362500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25fbdc20-4c66-4fc7-b886-f5a5ade9a9f4", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998255742100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74464121-8195-48dd-96c2-080b3cf39f46", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998247322300, "endTime": 33998256358700}, "additional": {"logType": "info", "children": [], "durationId": "c2988a25-088f-4faf-854b-0ca2d84dcfaf", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "b69b018e-ab2c-480c-9823-e78cbbae1b63", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998260149200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6498cc41-8b59-4e64-bae6-19e814715269", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998260297300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a17d0b7-85cb-4503-8f57-eb6b6a5899d6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998263047600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "333b8dda-caab-40a8-94bd-4834c0ad1632", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998263175600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "297c67fe-e59d-44ad-b1e9-2956e794754f", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998256382200, "endTime": 33998265588800}, "additional": {"logType": "info", "children": [], "durationId": "a2ae098b-0799-41ed-af29-37328d6d40cf", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "ac3beece-1c57-43ba-8b61-e4f29e8459ff", "name": "Configuration phase cost:283 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998265671200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21315fec-1298-42f5-8a1d-44ad9f36a916", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998265606300, "endTime": 33998265817600}, "additional": {"logType": "info", "children": [], "durationId": "fad133b5-62af-4061-8a1e-dcfb56d09210", "parent": "07f244cd-8bb2-44b2-987e-a36d8a5aade1"}}, {"head": {"id": "07f244cd-8bb2-44b2-987e-a36d8a5aade1", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997976948500, "endTime": 33998265831200}, "additional": {"logType": "info", "children": ["fcbb02a2-747a-4ade-8825-4600e696d47a", "07e0c9a4-a501-44e5-8bcc-6993a510c754", "f830191e-638c-400b-9670-8e806f5df57d", "58055d11-7262-49e2-89b5-aa2261785b78", "47da3899-2c70-4133-9a6e-7926a4dcf4c1", "74464121-8195-48dd-96c2-080b3cf39f46", "297c67fe-e59d-44ad-b1e9-2956e794754f", "21315fec-1298-42f5-8a1d-44ad9f36a916", "6fa372cf-1241-422d-ac85-6b894e8fc8bc"], "durationId": "7bead1d8-0fa7-4d05-9aef-bad5568c93ae", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "68975922-8307-40ff-821a-6bf728701cbb", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998266951000, "endTime": 33998266966900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4", "logId": "a228cb37-a81d-43d5-a360-63e58f7629e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a228cb37-a81d-43d5-a360-63e58f7629e2", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998266951000, "endTime": 33998266966900}, "additional": {"logType": "info", "children": [], "durationId": "68975922-8307-40ff-821a-6bf728701cbb", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "172ceb9b-8e5c-4ff1-a2e8-d64da8dd35f1", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998265856800, "endTime": 33998266977100}, "additional": {"logType": "info", "children": [], "durationId": "0610b3bd-0ee7-41ce-a3f8-686496ff6cfe", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "f18baedd-6326-4d53-8861-6b9f56469b16", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998266980600, "endTime": 33998266981500}, "additional": {"logType": "info", "children": [], "durationId": "cf038a60-2d10-4695-81f4-f619b7eba389", "parent": "64eb8629-fcc6-4ecc-a606-c5397acd5356"}}, {"head": {"id": "64eb8629-fcc6-4ecc-a606-c5397acd5356", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997962313200, "endTime": 33998266983600}, "additional": {"logType": "info", "children": ["7dc3c184-d36e-451a-a517-ec629c9f51b9", "07f244cd-8bb2-44b2-987e-a36d8a5aade1", "172ceb9b-8e5c-4ff1-a2e8-d64da8dd35f1", "f18baedd-6326-4d53-8861-6b9f56469b16", "31f56a37-135b-42cf-a79c-2b6e29e0db5e", "59f72ab7-8911-442b-9ed1-7edecac6f356", "a228cb37-a81d-43d5-a360-63e58f7629e2"], "durationId": "9b08adf2-2de2-40e9-b0b2-26c43fdde3a4"}}, {"head": {"id": "e51d66a3-8e08-4fc0-acdd-36254feb54e6", "name": "Configuration task cost before running: 310 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998267182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7633a95-3548-4eb4-a3a8-1b2590b64755", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998271312000, "endTime": 33998278296100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4e223687-31e3-4e9e-97cf-d9f8495b3d18", "logId": "13db43fe-b551-44c7-bab4-a99f39af01f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e223687-31e3-4e9e-97cf-d9f8495b3d18", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998268689900}, "additional": {"logType": "detail", "children": [], "durationId": "a7633a95-3548-4eb4-a3a8-1b2590b64755"}}, {"head": {"id": "12049d79-d676-40da-99a4-8c0e082f53bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998269081200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502003ba-37e8-45c6-ac76-a8ecc57d9e52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998269207300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb9116d4-f7fa-4341-bdc1-0e02ad786599", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998271322400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37a8565b-4a0e-4a34-a2e4-82ccf887809d", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998278081100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c86d8b0-75aa-4f8f-92b1-5e7357d1cf99", "name": "entry : default@PreBuild cost memory 0.30144500732421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998278223900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13db43fe-b551-44c7-bab4-a99f39af01f7", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998271312000, "endTime": 33998278296100}, "additional": {"logType": "info", "children": [], "durationId": "a7633a95-3548-4eb4-a3a8-1b2590b64755"}}, {"head": {"id": "bf19549a-75eb-46cf-9ef6-df83313849b4", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998282711200, "endTime": 33998284298800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "45374790-23bd-4a28-821b-62be5b49a2bf", "logId": "2d91c975-4f9e-459e-a8cb-c8a45fdfef82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45374790-23bd-4a28-821b-62be5b49a2bf", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998281544700}, "additional": {"logType": "detail", "children": [], "durationId": "bf19549a-75eb-46cf-9ef6-df83313849b4"}}, {"head": {"id": "b22fa9f4-37d3-4ccc-9064-83752b2823b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998281895900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d202558-1c5d-43ea-bee9-9872061ec37e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998281992600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a7455ec-f3c3-4262-bff4-2d8732d7d9c0", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998282720600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64384c7e-991b-468a-8bf3-943fee8a9e4a", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998283352900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d14b9286-b2bb-490a-af16-0a8da03c07b4", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998284133500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8187c5e5-7381-4e2e-a283-c4d61f139657", "name": "entry : default@GenerateMetadata cost memory 0.090728759765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998284232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d91c975-4f9e-459e-a8cb-c8a45fdfef82", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998282711200, "endTime": 33998284298800}, "additional": {"logType": "info", "children": [], "durationId": "bf19549a-75eb-46cf-9ef6-df83313849b4"}}, {"head": {"id": "564b1342-1dc2-4612-a0ea-8048df27e61c", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286542400, "endTime": 33998287123700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c0b3edee-a9e7-4f6f-9f1a-244819973b5b", "logId": "953be08e-d3b0-4108-adb8-b35642acd037"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0b3edee-a9e7-4f6f-9f1a-244819973b5b", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998285866700}, "additional": {"logType": "detail", "children": [], "durationId": "564b1342-1dc2-4612-a0ea-8048df27e61c"}}, {"head": {"id": "0ccf4122-1985-48dc-94df-9dda13e9f0ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286206700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fd93ae1-47bd-4656-8d04-c4d94601b67b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286304500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bfa1ae9-869d-4553-a42b-4818d7938907", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286550600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "575ca8c9-ffc9-4b1c-bd02-52feda3621a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286758500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfbb6e1c-0e75-413a-9ee6-d57ef1d6fe08", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d972beab-8adc-4b16-9d2a-7ca626c0e2d0", "name": "entry : default@ConfigureCmake cost memory 0.0359344482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286991200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97da539e-2444-4e3f-966b-96ad65342de8", "name": "runTaskFromQueue task cost before running: 330 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998287067000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953be08e-d3b0-4108-adb8-b35642acd037", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998286542400, "endTime": 33998287123700, "totalTime": 504600}, "additional": {"logType": "info", "children": [], "durationId": "564b1342-1dc2-4612-a0ea-8048df27e61c"}}, {"head": {"id": "d2dd465e-c2a7-4a46-87c0-b8f01ce23a21", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998289985800, "endTime": 33998292765200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "84e004f1-ee9e-46b6-8535-4c9ac52dae29", "logId": "56c847d8-7eb6-498f-a025-f8e93249c964"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84e004f1-ee9e-46b6-8535-4c9ac52dae29", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998288527000}, "additional": {"logType": "detail", "children": [], "durationId": "d2dd465e-c2a7-4a46-87c0-b8f01ce23a21"}}, {"head": {"id": "06672e72-e699-4237-a349-202cb3ee3dca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998288855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1538813-8737-47e7-b5d2-6a399906e16b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998288992500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b23af158-2e99-4d16-be79-e2e485c8ab2b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998289997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05315e0-caa5-48b6-b01d-0f3eac2d68e6", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998291993500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f9fe03f-e369-48f5-a206-30d61f4e5d2d", "name": "entry : default@MergeProfile cost memory 0.10117340087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998292658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56c847d8-7eb6-498f-a025-f8e93249c964", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998289985800, "endTime": 33998292765200}, "additional": {"logType": "info", "children": [], "durationId": "d2dd465e-c2a7-4a46-87c0-b8f01ce23a21"}}, {"head": {"id": "526c8a8c-7742-4f6e-91e1-7089296f1143", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998296003200, "endTime": 33998297974100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "35cb20df-5bb4-4e4e-a229-b2f323fb2a24", "logId": "63d0a1b6-6444-470a-aae7-8ce50a7c1a1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35cb20df-5bb4-4e4e-a229-b2f323fb2a24", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998294673500}, "additional": {"logType": "detail", "children": [], "durationId": "526c8a8c-7742-4f6e-91e1-7089296f1143"}}, {"head": {"id": "c9fbeb9b-55d5-4173-89e3-a0c34f5b568b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998295002800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c33e7f-4e87-48a5-a9b5-ab7412ab36f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998295096900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca5dded-9eda-4757-b865-b60e112a7569", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998296014400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "623fa8ef-6bc5-4546-8089-0f1ba51f6d3d", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998296853700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97d928eb-f0c0-419c-b5e3-52da6225e980", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998297811000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4fc4726-ae2c-4277-895a-3733ebfbba4b", "name": "entry : default@CreateBuildProfile cost memory 0.09816741943359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998297910200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d0a1b6-6444-470a-aae7-8ce50a7c1a1c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998296003200, "endTime": 33998297974100}, "additional": {"logType": "info", "children": [], "durationId": "526c8a8c-7742-4f6e-91e1-7089296f1143"}}, {"head": {"id": "24d3d509-cc2f-4ba0-8057-7ba69afb67ae", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300648000, "endTime": 33998301008700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "af5a90fd-3e8a-4440-93db-38b0a39e3869", "logId": "dfbcf328-0e8f-47ef-acc0-fe32e770e9be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af5a90fd-3e8a-4440-93db-38b0a39e3869", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998299520300}, "additional": {"logType": "detail", "children": [], "durationId": "24d3d509-cc2f-4ba0-8057-7ba69afb67ae"}}, {"head": {"id": "1914b8a6-0651-41b2-86cc-42990e46d752", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998299959300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ae2b8a7-fa1d-469f-994b-0b1ea3d9b2c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300052200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1580f4cc-d832-4300-9570-b11ffab36df7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300655200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "838ce19a-dcdc-44cd-85ff-90e8d1144bb3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300762000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5842fd-338e-43a3-8095-5d148d9c4e15", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300814500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4263daa3-fa7f-4332-bf49-8f37c0c691f1", "name": "entry : default@PreCheckSyscap cost memory 0.03615570068359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300880700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3537ade-b0e5-43f3-b585-955ee0cc4b48", "name": "runTaskFromQueue task cost before running: 344 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300949600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfbcf328-0e8f-47ef-acc0-fe32e770e9be", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998300648000, "endTime": 33998301008700, "totalTime": 285100}, "additional": {"logType": "info", "children": [], "durationId": "24d3d509-cc2f-4ba0-8057-7ba69afb67ae"}}, {"head": {"id": "a191502a-8fb2-4637-9211-ce2bdc315141", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998306264700, "endTime": 33998306805800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "44c3da22-d51b-4786-92d5-4f0c830b028d", "logId": "04413577-d63a-42cd-8888-7d415006f3e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c3da22-d51b-4786-92d5-4f0c830b028d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998302409700}, "additional": {"logType": "detail", "children": [], "durationId": "a191502a-8fb2-4637-9211-ce2bdc315141"}}, {"head": {"id": "ee5f775f-e5e2-46c4-ba6c-05e41dc3b020", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998302735400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0df9edf-be5c-44b7-abd7-dde411daaf90", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998302824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a0b64e-f41c-4680-832b-31cece892c44", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998306275800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24057b22-44ab-4e38-b818-41a70fff5f40", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998306451700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b2beac-794e-46f9-bf91-5d1f818995d6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03679656982421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998306654000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c02b77d-4eac-4169-90ac-756eb3528114", "name": "runTaskFromQueue task cost before running: 350 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998306749300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04413577-d63a-42cd-8888-7d415006f3e8", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998306264700, "endTime": 33998306805800, "totalTime": 467000}, "additional": {"logType": "info", "children": [], "durationId": "a191502a-8fb2-4637-9211-ce2bdc315141"}}, {"head": {"id": "626a6d1e-8919-49a0-8941-295243cd1d72", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998312496400, "endTime": 33998314534600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "be2c45b0-318e-4f9f-937e-54531aa46470", "logId": "c90e9857-835a-4fcd-a0e6-f06fc728042b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be2c45b0-318e-4f9f-937e-54531aa46470", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998310116900}, "additional": {"logType": "detail", "children": [], "durationId": "626a6d1e-8919-49a0-8941-295243cd1d72"}}, {"head": {"id": "95dfaa38-9d7a-49b6-a79f-5bed80c67a8f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998310510300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf89b253-c6cb-4e98-86b2-204819a7d6f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998310644700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8d09987-1a3a-44cc-83f5-0b1ce20edf6a", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998312507000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2cc72dc-9cf6-4735-b2e2-67e12058e468", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998314021000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f73b3cb9-202c-4f2e-bd48-f889ec5d546d", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998314147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b142a987-9129-42ed-a6f0-fdeaa3b5edd7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998314234100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64922e79-062c-4d80-97b2-4206a36f0f54", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998314282200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cced906-1b20-432f-9e4a-bab1b49daa4d", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1177215576171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998314386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fafb87b1-cfb2-4a20-bef5-dd5e2b09d792", "name": "runTaskFromQueue task cost before running: 357 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998314477300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c90e9857-835a-4fcd-a0e6-f06fc728042b", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998312496400, "endTime": 33998314534600, "totalTime": 1965300}, "additional": {"logType": "info", "children": [], "durationId": "626a6d1e-8919-49a0-8941-295243cd1d72"}}, {"head": {"id": "ee6bb215-aad7-4a72-81ea-81d6a92d098e", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318309600, "endTime": 33998318762600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ae6afab8-b7fd-41f8-b9cf-af69a3442721", "logId": "891cda52-0208-4fbf-96cd-306325889f35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae6afab8-b7fd-41f8-b9cf-af69a3442721", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998317099800}, "additional": {"logType": "detail", "children": [], "durationId": "ee6bb215-aad7-4a72-81ea-81d6a92d098e"}}, {"head": {"id": "9f083b24-04c6-4c78-9ba6-b74fe3f28ab6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998317466100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b544df-a488-4e84-befa-0de0fc513b1d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998317566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13da14ba-4b8d-4e6f-a91e-38a9e8466468", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318321800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07d211c6-af70-4cd6-bd17-8d63c4eebdb6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318449900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c0c6f3-cceb-47d9-991a-5a446d2591ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318509400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0096fb4a-e652-4821-b4f6-0d8ad76f06cb", "name": "entry : default@BuildNativeWithCmake cost memory 0.0369873046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318579100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2884be-9db6-41cb-bbba-3bf6c66b67fa", "name": "runTaskFromQueue task cost before running: 362 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318691900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "891cda52-0208-4fbf-96cd-306325889f35", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998318309600, "endTime": 33998318762600, "totalTime": 359600}, "additional": {"logType": "info", "children": [], "durationId": "ee6bb215-aad7-4a72-81ea-81d6a92d098e"}}, {"head": {"id": "9cb71f52-31f4-4d5b-84e5-9a02224b47dd", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998321497300, "endTime": 33998325852900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3a65938f-9ca9-41f7-8553-cde12797b007", "logId": "a648577a-bc29-41c8-8987-893ed122d1ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a65938f-9ca9-41f7-8553-cde12797b007", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998320476700}, "additional": {"logType": "detail", "children": [], "durationId": "9cb71f52-31f4-4d5b-84e5-9a02224b47dd"}}, {"head": {"id": "fa78d1c5-157a-4c8f-add9-3cbc3f765d23", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998320824100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c823061-5a8d-47a5-9cba-20e51d0569bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998320929400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "357fe2b2-66b2-4efe-8bec-5e8692bef5db", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998321503800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e007eb0-338e-4beb-91ed-fc5708d3e8eb", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998325009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d30dcb17-fe23-4c0d-90bd-e898f571ed3d", "name": "entry : default@MakePackInfo cost memory 0.1360931396484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998325704700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a648577a-bc29-41c8-8987-893ed122d1ee", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998321497300, "endTime": 33998325852900}, "additional": {"logType": "info", "children": [], "durationId": "9cb71f52-31f4-4d5b-84e5-9a02224b47dd"}}, {"head": {"id": "2e695968-70cc-4206-a3a6-c47f572badaa", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998329600400, "endTime": 33998331965400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "e33deca5-5d8a-40ae-839f-8193d9c317a9", "logId": "430a6c45-62a2-4b6a-95fc-ca33b6479dc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e33deca5-5d8a-40ae-839f-8193d9c317a9", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998328113900}, "additional": {"logType": "detail", "children": [], "durationId": "2e695968-70cc-4206-a3a6-c47f572badaa"}}, {"head": {"id": "439df88d-4e11-4f67-9f16-5cb419da60b3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998328463900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c507f0ab-ef59-4d19-827d-c07eca79abdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998328553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2afc2689-3776-4af0-acaa-376e69776ea3", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998329612600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d8b44a2-0391-458d-94cc-940718f8818c", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998329736600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46cc7a27-184d-4851-955e-45d20eba1425", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998330309300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4f17857-2d41-4d2f-9377-cfc7ac510cdc", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998331469800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c3349a9-a8f8-4821-a34b-dfeadc559323", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998331555800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde3d972-6f40-48bd-bb59-c45b4ca51b84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998331693600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef407d28-6cc5-43fe-9c20-8ccd6351a133", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998331766900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76bfcace-fed2-4b9c-a87d-c8d379c6d8f2", "name": "entry : default@SyscapTransform cost memory 0.15020751953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998331842400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579d5c3d-bb27-438a-89d1-5dfd0a5e2f3b", "name": "runTaskFromQueue task cost before running: 375 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998331911400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "430a6c45-62a2-4b6a-95fc-ca33b6479dc2", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998329600400, "endTime": 33998331965400, "totalTime": 2295900}, "additional": {"logType": "info", "children": [], "durationId": "2e695968-70cc-4206-a3a6-c47f572badaa"}}, {"head": {"id": "18249830-44c3-478a-9a15-268cb1b15588", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998336122700, "endTime": 33998337051500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "71d4a117-daf5-4375-a8c5-fb70040fa989", "logId": "5f341a46-5d7f-4f8a-9152-9fc2f06cb1b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71d4a117-daf5-4375-a8c5-fb70040fa989", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998334688400}, "additional": {"logType": "detail", "children": [], "durationId": "18249830-44c3-478a-9a15-268cb1b15588"}}, {"head": {"id": "6dcfe57e-42a8-4295-81ee-8b615052eeed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998335098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32c9fef1-b9a6-487f-a6cb-3804d81df709", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998335196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de1a3a6-f85e-4945-9b26-472e4a107cd2", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998336137300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2525856-8310-4028-a854-06f2b2bdb662", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998336903000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23dabb08-735a-4832-afad-0bd52954fd9e", "name": "entry : default@ProcessProfile cost memory 0.0578765869140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998336995400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f341a46-5d7f-4f8a-9152-9fc2f06cb1b0", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998336122700, "endTime": 33998337051500}, "additional": {"logType": "info", "children": [], "durationId": "18249830-44c3-478a-9a15-268cb1b15588"}}, {"head": {"id": "94464a16-b459-4da4-bef3-9ad08b974009", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998340990000, "endTime": 33998345244400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8f12fd3d-1612-47fc-a18b-fa98cae51bfe", "logId": "48f07135-8769-46f8-a999-361eb7e3fcb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f12fd3d-1612-47fc-a18b-fa98cae51bfe", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998338663800}, "additional": {"logType": "detail", "children": [], "durationId": "94464a16-b459-4da4-bef3-9ad08b974009"}}, {"head": {"id": "fdbe6a62-7e63-4f40-a38a-41b4161eb933", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998339077800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff8edcd-2354-436d-8a06-ed006de07804", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998339218200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0393b778-2c34-417c-826a-e3ed908ec5fe", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998341000800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0473943-59ca-44b6-9f6a-3aec5718c3ee", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998345056700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41287c80-d532-47f7-bccc-5a81e45f06c8", "name": "entry : default@ProcessRouterMap cost memory 0.19971466064453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998345182100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f07135-8769-46f8-a999-361eb7e3fcb3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998340990000, "endTime": 33998345244400}, "additional": {"logType": "info", "children": [], "durationId": "94464a16-b459-4da4-bef3-9ad08b974009"}}, {"head": {"id": "a0f2316f-ca7b-4e6a-8549-6221e6e5af6e", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349062400, "endTime": 33998350049400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8dda04ea-50d4-4728-9a89-c1bef6ad6899", "logId": "761b3547-3b8e-4601-84d8-f260055ee3ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dda04ea-50d4-4728-9a89-c1bef6ad6899", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998347804800}, "additional": {"logType": "detail", "children": [], "durationId": "a0f2316f-ca7b-4e6a-8549-6221e6e5af6e"}}, {"head": {"id": "47f74fe7-9f38-4fd5-a4dd-300d2cda0652", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998348233200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b523ec0-1ff8-4ce6-9f71-2828938ee478", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998348338000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "098cfdb9-1a52-4bd6-81dd-6636d16f14ab", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349073400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d434cc88-5bec-47c1-90ad-f736a15bc95a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349203300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c7eff4a-1788-4b77-a309-968fd20d86e1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349253100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3faae7c0-608a-4315-9795-ea149542fd4d", "name": "entry : default@BuildNativeWithNinja cost memory 0.05658721923828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349886900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd556a8-635f-4ed4-be5f-1cd302192f96", "name": "runTaskFromQueue task cost before running: 393 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349988400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761b3547-3b8e-4601-84d8-f260055ee3ad", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998349062400, "endTime": 33998350049400, "totalTime": 909000}, "additional": {"logType": "info", "children": [], "durationId": "a0f2316f-ca7b-4e6a-8549-6221e6e5af6e"}}, {"head": {"id": "b36fbfbe-7f49-4480-901f-8abc9d652b31", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998354692700, "endTime": 33998359663300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8ebb1a7d-1f9d-48b9-948b-4fb9d435b77c", "logId": "821b8ddd-c457-4a2a-98ce-924742db45cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ebb1a7d-1f9d-48b9-948b-4fb9d435b77c", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998352304800}, "additional": {"logType": "detail", "children": [], "durationId": "b36fbfbe-7f49-4480-901f-8abc9d652b31"}}, {"head": {"id": "e28f4f69-8e23-458c-a848-fd2a7efdd610", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998352733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b1c898-8f5f-4f50-9f15-d2818c47048e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998352840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1baba55e-6a3b-487b-96f8-6ea47ff019ca", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998353608300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc35279b-5e61-4ef6-a04f-abef5120de46", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998356149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb1c7e2-067c-4f65-8372-3f23893f5e03", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998357807300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea50271-b1ef-4c14-9b45-6284d09dc4c7", "name": "entry : default@ProcessResource cost memory 0.1672821044921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998357908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "821b8ddd-c457-4a2a-98ce-924742db45cb", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998354692700, "endTime": 33998359663300}, "additional": {"logType": "info", "children": [], "durationId": "b36fbfbe-7f49-4480-901f-8abc9d652b31"}}, {"head": {"id": "697eb204-28c2-42b4-a51d-ba5521901d27", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998366582200, "endTime": 33998382469700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c2260fbc-3e34-40b0-9fe3-e8d845ea1ade", "logId": "df8c21e3-abb0-469b-8983-ede4c8682c80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2260fbc-3e34-40b0-9fe3-e8d845ea1ade", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998363296300}, "additional": {"logType": "detail", "children": [], "durationId": "697eb204-28c2-42b4-a51d-ba5521901d27"}}, {"head": {"id": "a199ed30-ee51-4bf0-9ac2-2d458194e869", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998363674600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039100a2-ef1c-478f-a9a0-8f374285a67d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998363766600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1ffa90f-901d-42a2-a987-63a96579233e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998366593200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af195a8a-d18e-40eb-98c2-733e230a46f5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998382268400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcce521e-2d81-4ab6-81d1-243f439542d2", "name": "entry : default@GenerateLoaderJson cost memory 0.7623672485351562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998382402500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8c21e3-abb0-469b-8983-ede4c8682c80", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998366582200, "endTime": 33998382469700}, "additional": {"logType": "info", "children": [], "durationId": "697eb204-28c2-42b4-a51d-ba5521901d27"}}, {"head": {"id": "e5914c2e-79af-4257-8758-baa52008e6a4", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998392844000, "endTime": 33998395742000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "02700916-9c6f-4317-bc7f-cb2e0363c833", "logId": "d1a1fa07-48bc-4472-8073-6251abb06c36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02700916-9c6f-4317-bc7f-cb2e0363c833", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998391255900}, "additional": {"logType": "detail", "children": [], "durationId": "e5914c2e-79af-4257-8758-baa52008e6a4"}}, {"head": {"id": "edc2b8b8-af61-417f-b09b-715571a68d07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998391599000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c915dd8f-62a9-4d80-a469-78356e394966", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998391704200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cf1ff33-e959-4796-bd62-a07877ba1d84", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998392859300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc98cd61-2a7d-497b-81f6-eccc2eb99717", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998394745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76099721-4f41-4fe1-bb02-dd147e591d20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998394832500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc12076d-12a9-43f4-9243-520c2ece1673", "name": "entry : default@ProcessLibs cost memory 0.12541961669921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998395574700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b596b8d1-fc15-4809-8ed0-8e62e92b773b", "name": "runTaskFromQueue task cost before running: 439 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998395683700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a1fa07-48bc-4472-8073-6251abb06c36", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998392844000, "endTime": 33998395742000, "totalTime": 2817800}, "additional": {"logType": "info", "children": [], "durationId": "e5914c2e-79af-4257-8758-baa52008e6a4"}}, {"head": {"id": "25f9bb7a-149a-4a51-84c5-4721152cae9e", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998401697500, "endTime": 33998420516900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5287e5e4-13d2-4489-9d4d-ebc6de32b4c3", "logId": "c339cac5-7629-46b5-b491-8bc91bd8b3ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5287e5e4-13d2-4489-9d4d-ebc6de32b4c3", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998398017000}, "additional": {"logType": "detail", "children": [], "durationId": "25f9bb7a-149a-4a51-84c5-4721152cae9e"}}, {"head": {"id": "f4081cde-7911-416a-b850-30a4ff5b1521", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998398466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28955e72-31cd-4ab3-af9f-8102257a4756", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998398564600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6251544-36b9-466b-af09-84901c897a5d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998399261800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52c98099-395a-4620-8393-261af570bda4", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998401724200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7f621dd-d4e3-43bd-a642-84b344268c5c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998420309800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1395385-d8c1-43ae-86a8-30a4b54f21ee", "name": "entry : default@CompileResource cost memory 1.3953475952148438", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998420435600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c339cac5-7629-46b5-b491-8bc91bd8b3ec", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998401697500, "endTime": 33998420516900}, "additional": {"logType": "info", "children": [], "durationId": "25f9bb7a-149a-4a51-84c5-4721152cae9e"}}, {"head": {"id": "84596a09-5162-4ad0-bb37-81188b5752b9", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998426150900, "endTime": 33998427621000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "960a821c-407d-4468-a692-b6bf1285e41c", "logId": "ae8961fa-085b-48b6-8616-20d2b718ea60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "960a821c-407d-4468-a692-b6bf1285e41c", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998423056800}, "additional": {"logType": "detail", "children": [], "durationId": "84596a09-5162-4ad0-bb37-81188b5752b9"}}, {"head": {"id": "8311a7ca-2348-41e9-a1b7-a0f397da63ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998423447000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0741be-ac8f-42b1-983d-0e6bfda20307", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998423535600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d64ad92-c72b-4db7-a03f-f35c125d75e1", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998426164700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e2142fc-aa67-4e09-9b56-c27875269939", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998426439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e021987a-7135-4493-86d6-e4fc2e510867", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998427440500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f01bb58c-cd1b-45fd-b5e9-6ca95fcfd9c0", "name": "entry : default@DoNativeStrip cost memory 0.07300567626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998427552400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae8961fa-085b-48b6-8616-20d2b718ea60", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998426150900, "endTime": 33998427621000}, "additional": {"logType": "info", "children": [], "durationId": "84596a09-5162-4ad0-bb37-81188b5752b9"}}, {"head": {"id": "3103caa6-a991-48e3-9c9f-859dae9f67b2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998433663400, "endTime": 34000412410500}, "additional": {"children": ["21475829-2bad-4ab9-ae83-a140a4ff4f76", "e60a50eb-72c8-471b-b5d6-b4bfef949f44"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "a794bdb8-b72c-4649-b9bb-4f01d41c7596", "logId": "83119d12-4ced-43cc-bbfa-d375bad0ff17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a794bdb8-b72c-4649-b9bb-4f01d41c7596", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998429220200}, "additional": {"logType": "detail", "children": [], "durationId": "3103caa6-a991-48e3-9c9f-859dae9f67b2"}}, {"head": {"id": "f69d35e8-33b7-4bd9-bbb9-be47faf67252", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 3399**********}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4aaf947-6eec-4d44-a5eb-e7e7831b971c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998429665000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57534e9f-1013-4fe4-9460-6db2ae56af03", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998433675800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c1946e-33f8-43f6-ad42-d25b8eff219e", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998445357700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953e8327-035e-49a7-9ced-8c0c22e54ab1", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998445500600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b37d2b39-39d0-46f8-9f41-a304c6b8ce04", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998463869900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2499d0cb-9bdb-4cfb-aad8-9d8720a81892", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998464467800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a3bacb6-0d2c-406b-b080-3eed4b27ec55", "name": "default@CompileArkTS work[112] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998466056200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33998572848500, "endTime": 34000405276800}, "additional": {"children": ["56a34bf3-ed17-4c40-9fdd-8c685fa76985", "a25f41ec-0107-47e1-a527-bc3640659793", "1705fde5-167d-42ee-96c1-2ed27f2005bc", "0f32d38f-d7af-4f7f-bce2-3ec9f7506a8a", "b48ab1b5-d839-4c91-b187-42aab884fb69", "223bc401-0fe4-4f5a-b005-ea557b760e48"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3103caa6-a991-48e3-9c9f-859dae9f67b2", "logId": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03a87828-d6aa-4ef6-8d21-0d59d45fac08", "name": "default@CompileArkTS work[112] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998467322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621c76c4-4c6d-4c37-8565-aebcf975a5fb", "name": "default@CompileArkTS work[112] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998467448500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cf9c5ce-3bbf-4590-bd28-39366f560f57", "name": "CopyResources startTime: 33998467507300", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998467509600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32676ab-5e2c-427c-a4de-ccdd374d9651", "name": "default@CompileArkTS work[113] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998467561700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e60a50eb-72c8-471b-b5d6-b4bfef949f44", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33999734600000, "endTime": 33999756852100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3103caa6-a991-48e3-9c9f-859dae9f67b2", "logId": "010b9097-f76c-4c02-a03a-6491d246dda5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26104f7b-cefc-44ea-890f-21c70bc34801", "name": "default@CompileArkTS work[113] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998468463900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3adeab32-f2c6-415a-bec0-428a96a7637b", "name": "default@CompileArkTS work[113] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998468554500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee249ec-453a-4ae9-b154-6a856766720e", "name": "entry : default@CompileArkTS cost memory -4.811370849609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998468637900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa1eca7-84e5-4da4-bffe-2c023151d7b4", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998479332900, "endTime": 33998484497300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "4146d115-bb9c-492c-a60a-949c75d7030a", "logId": "8eefd8d2-5205-4dca-b785-8fe6d0943c1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4146d115-bb9c-492c-a60a-949c75d7030a", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998470396100}, "additional": {"logType": "detail", "children": [], "durationId": "baa1eca7-84e5-4da4-bffe-2c023151d7b4"}}, {"head": {"id": "a04586ca-fd99-4175-a812-e9780389faeb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998470964700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c20ef540-cf7d-4c86-91c3-23f97430be38", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998471109700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bacf008-d982-44c9-b1ad-08d521286fd1", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998479347100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da53e94c-9b21-40a6-9bde-18136d514170", "name": "entry : default@BuildJS cost memory 0.12694549560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998484082300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c23e1c17-e8c3-4e2b-9d33-2ed8c1c7770c", "name": "runTaskFromQueue task cost before running: 527 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998484414000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eefd8d2-5205-4dca-b785-8fe6d0943c1f", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998479332900, "endTime": 33998484497300, "totalTime": 5050500}, "additional": {"logType": "info", "children": [], "durationId": "baa1eca7-84e5-4da4-bffe-2c023151d7b4"}}, {"head": {"id": "6f941f87-067d-4782-8d46-e895e052faf0", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998496767800, "endTime": 33998500361700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3cfc70bf-6e4a-4e47-a336-143e14156a0e", "logId": "5de14a1b-a6a7-4dc5-a61f-9bafc1c3da71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cfc70bf-6e4a-4e47-a336-143e14156a0e", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998487291100}, "additional": {"logType": "detail", "children": [], "durationId": "6f941f87-067d-4782-8d46-e895e052faf0"}}, {"head": {"id": "362970ad-6751-47f6-8e19-ffb7c0d150a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998488204500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac87b57-02a4-4f34-b8a0-56ebf5a61633", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998488317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf87b4d-f0e3-42e7-bc11-77f67d76f693", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998496894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ec97a38-04cb-4240-a17e-10214b1ec62d", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998497727100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b2c718-6f76-430a-a19f-bbdb0d016c68", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998500101600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "033d4214-8a0f-4635-b658-268b22156612", "name": "entry : default@CacheNativeLibs cost memory 0.08712005615234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998500291000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de14a1b-a6a7-4dc5-a61f-9bafc1c3da71", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998496767800, "endTime": 33998500361700}, "additional": {"logType": "info", "children": [], "durationId": "6f941f87-067d-4782-8d46-e895e052faf0"}}, {"head": {"id": "d6086b38-9edb-4707-aded-ed605e3e66c4", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998572289000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26ca778-bdfd-4c20-a779-6cfb23da8d5d", "name": "default@CompileArkTS work[112] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998572702100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f39300e6-8144-445e-922f-43002e1450bd", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998572951300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e2cb334-f0d9-45c2-a0a1-326116a782fc", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998573064700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62481427-e585-46c3-9775-542297e8474d", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998573239700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce061cb3-9b26-415a-9c30-3a5b614dc771", "name": "default@CompileArkTS work[113] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998574436200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf7b91d-8784-4794-a004-7220aa174378", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33999757059800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b81eb4ba-6607-47c1-b8cf-ef8ae72c7c4e", "name": "CopyResources is end, endTime: 33999757295300", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33999757302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da70ca0b-d1cc-456f-9916-4551178706d2", "name": "default@CompileArkTS work[113] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33999757407500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010b9097-f76c-4c02-a03a-6491d246dda5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33999734600000, "endTime": 33999756852100}, "additional": {"logType": "info", "children": [], "durationId": "e60a50eb-72c8-471b-b5d6-b4bfef949f44", "parent": "83119d12-4ced-43cc-bbfa-d375bad0ff17"}}, {"head": {"id": "d91ad3e2-6aa4-4196-b2bd-3ebf1956938a", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33999882865700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f043183c-d63e-4d79-86d3-3421f0e3fd4e", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000405838300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56a34bf3-ed17-4c40-9fdd-8c685fa76985", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33998572966800, "endTime": 33998579811700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "logId": "1e0b82f6-d188-4f23-97d5-dce36814a468"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e0b82f6-d188-4f23-97d5-dce36814a468", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998572966800, "endTime": 33998579811700}, "additional": {"logType": "info", "children": [], "durationId": "56a34bf3-ed17-4c40-9fdd-8c685fa76985", "parent": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}}, {"head": {"id": "a25f41ec-0107-47e1-a527-bc3640659793", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33998579829200, "endTime": 33998579988200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "logId": "3747203c-1314-49b1-90bf-ffb49d17e970"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3747203c-1314-49b1-90bf-ffb49d17e970", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998579829200, "endTime": 33998579988200}, "additional": {"logType": "info", "children": [], "durationId": "a25f41ec-0107-47e1-a527-bc3640659793", "parent": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}}, {"head": {"id": "1705fde5-167d-42ee-96c1-2ed27f2005bc", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33998580004700, "endTime": 33998580042300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "logId": "40da3255-e836-4ab5-83aa-6057c12af047"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40da3255-e836-4ab5-83aa-6057c12af047", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998580004700, "endTime": 33998580042300}, "additional": {"logType": "info", "children": [], "durationId": "1705fde5-167d-42ee-96c1-2ed27f2005bc", "parent": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}}, {"head": {"id": "0f32d38f-d7af-4f7f-bce2-3ec9f7506a8a", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33998580057500, "endTime": 34000316297100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "logId": "a50e0efc-6739-487a-a310-1d31a80afc1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a50e0efc-6739-487a-a310-1d31a80afc1b", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998580057500, "endTime": 34000316297100}, "additional": {"logType": "info", "children": [], "durationId": "0f32d38f-d7af-4f7f-bce2-3ec9f7506a8a", "parent": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}}, {"head": {"id": "b48ab1b5-d839-4c91-b187-42aab884fb69", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000316316500, "endTime": 34000320375100}, "additional": {"children": ["6fc0ecc9-8c1c-4962-b665-ad3674c97b34", "43c8ea81-8439-4636-830f-95102c42b72e", "2911a049-0582-4bc5-beeb-33aa476cc3a8"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "logId": "e38c6f58-d897-4fd6-8b1f-df38abc5634e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e38c6f58-d897-4fd6-8b1f-df38abc5634e", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000316316500, "endTime": 34000320375100}, "additional": {"logType": "info", "children": ["36d14be9-0254-431a-b672-2bdc433e737c", "dbdc12bd-8016-49ae-8085-a07da9445343", "4fc310e3-5c49-4fc8-a459-1bc5266500bf"], "durationId": "b48ab1b5-d839-4c91-b187-42aab884fb69", "parent": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}}, {"head": {"id": "6fc0ecc9-8c1c-4962-b665-ad3674c97b34", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000316337700, "endTime": 34000316345300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b48ab1b5-d839-4c91-b187-42aab884fb69", "logId": "36d14be9-0254-431a-b672-2bdc433e737c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36d14be9-0254-431a-b672-2bdc433e737c", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000316337700, "endTime": 34000316345300}, "additional": {"logType": "info", "children": [], "durationId": "6fc0ecc9-8c1c-4962-b665-ad3674c97b34", "parent": "e38c6f58-d897-4fd6-8b1f-df38abc5634e"}}, {"head": {"id": "43c8ea81-8439-4636-830f-95102c42b72e", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000316349000, "endTime": 34000318331600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b48ab1b5-d839-4c91-b187-42aab884fb69", "logId": "dbdc12bd-8016-49ae-8085-a07da9445343"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbdc12bd-8016-49ae-8085-a07da9445343", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000316349000, "endTime": 34000318331600}, "additional": {"logType": "info", "children": [], "durationId": "43c8ea81-8439-4636-830f-95102c42b72e", "parent": "e38c6f58-d897-4fd6-8b1f-df38abc5634e"}}, {"head": {"id": "2911a049-0582-4bc5-beeb-33aa476cc3a8", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000318336900, "endTime": 34000320362600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b48ab1b5-d839-4c91-b187-42aab884fb69", "logId": "4fc310e3-5c49-4fc8-a459-1bc5266500bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fc310e3-5c49-4fc8-a459-1bc5266500bf", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000318336900, "endTime": 34000320362600}, "additional": {"logType": "info", "children": [], "durationId": "2911a049-0582-4bc5-beeb-33aa476cc3a8", "parent": "e38c6f58-d897-4fd6-8b1f-df38abc5634e"}}, {"head": {"id": "223bc401-0fe4-4f5a-b005-ea557b760e48", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000320389700, "endTime": 34000405125500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "logId": "346e18b0-170f-4e2b-85f7-134a352b67a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "346e18b0-170f-4e2b-85f7-134a352b67a4", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000320389700, "endTime": 34000405125500}, "additional": {"logType": "info", "children": [], "durationId": "223bc401-0fe4-4f5a-b005-ea557b760e48", "parent": "0ed92f23-9f2d-40f0-b330-235f67fe455d"}}, {"head": {"id": "69de1da9-fb09-40ba-83f3-c058de57e3cf", "name": "default@CompileArkTS work[112] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000412186500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed92f23-9f2d-40f0-b330-235f67fe455d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33998572848500, "endTime": 34000405276800}, "additional": {"logType": "info", "children": ["1e0b82f6-d188-4f23-97d5-dce36814a468", "3747203c-1314-49b1-90bf-ffb49d17e970", "40da3255-e836-4ab5-83aa-6057c12af047", "a50e0efc-6739-487a-a310-1d31a80afc1b", "e38c6f58-d897-4fd6-8b1f-df38abc5634e", "346e18b0-170f-4e2b-85f7-134a352b67a4"], "durationId": "21475829-2bad-4ab9-ae83-a140a4ff4f76", "parent": "83119d12-4ced-43cc-bbfa-d375bad0ff17"}}, {"head": {"id": "26845dc2-c5b5-4e61-b6fe-7c7e71783789", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000412336700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83119d12-4ced-43cc-bbfa-d375bad0ff17", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33998433663400, "endTime": 34000412410500, "totalTime": 1867456900}, "additional": {"logType": "info", "children": ["0ed92f23-9f2d-40f0-b330-235f67fe455d", "010b9097-f76c-4c02-a03a-6491d246dda5"], "durationId": "3103caa6-a991-48e3-9c9f-859dae9f67b2"}}, {"head": {"id": "47f3d8d5-57a8-4d15-83d6-8ce5578c73a9", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000418009900, "endTime": 34000419123100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "dd704ccb-331b-4ddf-9c4d-8cca290a401d", "logId": "d01aaaf8-6128-468d-a75e-dee37521262b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd704ccb-331b-4ddf-9c4d-8cca290a401d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000416683000}, "additional": {"logType": "detail", "children": [], "durationId": "47f3d8d5-57a8-4d15-83d6-8ce5578c73a9"}}, {"head": {"id": "e0c763b6-6821-46b1-8d0b-7160bf203ba0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000417105400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bbf2890-d56d-4700-bea4-55aa425d5e24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000417213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40400261-35e9-436f-bb7e-1b6035dc2150", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000418017800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b36091e-0afe-499a-a0ab-a5f10bb8f452", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000418274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b02af42-83ea-4eac-86ce-c67c8b87bbfb", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000418973000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02fa2986-133f-4b8c-8366-caa3d8474d80", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07173919677734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000419055600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01aaaf8-6128-468d-a75e-dee37521262b", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000418009900, "endTime": 34000419123100}, "additional": {"logType": "info", "children": [], "durationId": "47f3d8d5-57a8-4d15-83d6-8ce5578c73a9"}}, {"head": {"id": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000430229200, "endTime": 34000923791900}, "additional": {"children": ["678d9f67-f8a2-4821-a0a3-b06e733c358b", "11a460ab-7043-423e-a46d-c5c8320124ae", "8e09705a-a86d-4d14-8812-25624d941290"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "b5d98146-eae0-4b0a-9454-d2601bf8a912", "logId": "dc7e2db3-196f-40f7-be65-8722555b6ad5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5d98146-eae0-4b0a-9454-d2601bf8a912", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000420974400}, "additional": {"logType": "detail", "children": [], "durationId": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061"}}, {"head": {"id": "55ff38ba-deb4-4684-abca-57e36cbf5431", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000421432900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bc0807-1ab4-478d-a1f6-1dd9dae07f5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000421530700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aadab38-1608-4926-94e8-e138c16b24eb", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000430240800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "430dca96-77e1-4db4-819a-8048f4087d30", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000441732000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59299568-05ac-4478-9906-6e225ae9452f", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000441990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25afafc-4057-4c3b-bb89-fa24bc152420", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000442142900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad7c58e4-1d95-411c-8699-6349bf7b6d49", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000442206800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678d9f67-f8a2-4821-a0a3-b06e733c358b", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000442958800, "endTime": 34000444062000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061", "logId": "fed0be8d-b586-468a-8564-9b315f8a00c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52dc8dab-ab2b-48ed-8b2b-8651d6fd084e", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000443932700}, "additional": {"logType": "debug", "children": [], "durationId": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061"}}, {"head": {"id": "fed0be8d-b586-468a-8564-9b315f8a00c8", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000442958800, "endTime": 34000444062000}, "additional": {"logType": "info", "children": [], "durationId": "678d9f67-f8a2-4821-a0a3-b06e733c358b", "parent": "dc7e2db3-196f-40f7-be65-8722555b6ad5"}}, {"head": {"id": "11a460ab-7043-423e-a46d-c5c8320124ae", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000444854500, "endTime": 34000446660100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061", "logId": "997fd2b4-60e9-4a69-841d-c536211e84a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a04f2e12-2e68-4489-809e-c35575088f63", "name": "default@PackageHap work[114] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000445482400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e09705a-a86d-4d14-8812-25624d941290", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000446490700, "endTime": 34000923441200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061", "logId": "ad3a03d3-3d06-4130-9202-607c3081a7c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85fd5395-99f8-4c1e-9447-22c2680f0a4c", "name": "default@PackageHap work[114] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000446222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd248662-953f-49fa-aa46-c6a21cc69581", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000446314800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c03841-8dda-4001-b54a-ae07e892c4ee", "name": "default@PackageHap work[114] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000446412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "918601b1-0e48-4d30-9992-5e858b5ea568", "name": "default@PackageHap work[114] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000446561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "997fd2b4-60e9-4a69-841d-c536211e84a6", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000444854500, "endTime": 34000446660100}, "additional": {"logType": "info", "children": [], "durationId": "11a460ab-7043-423e-a46d-c5c8320124ae", "parent": "dc7e2db3-196f-40f7-be65-8722555b6ad5"}}, {"head": {"id": "ca6f7d3e-0e80-4572-8551-01b2d078a5a9", "name": "entry : default@PackageHap cost memory 1.2711715698242188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000450769700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e52c5e92-ba5c-4b1d-84f1-5279a281b507", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000923518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51ea932-5159-4bb2-91e5-0a0225d0ba71", "name": "default@PackageHap work[114] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000923660600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad3a03d3-3d06-4130-9202-607c3081a7c5", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 34000446490700, "endTime": 34000923441200}, "additional": {"logType": "info", "children": [], "durationId": "8e09705a-a86d-4d14-8812-25624d941290", "parent": "dc7e2db3-196f-40f7-be65-8722555b6ad5"}}, {"head": {"id": "db9277e4-d104-4f76-a5c2-14ded74e98c9", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000923735700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7e2db3-196f-40f7-be65-8722555b6ad5", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000430229200, "endTime": 34000923791900, "totalTime": 493214500}, "additional": {"logType": "info", "children": ["fed0be8d-b586-468a-8564-9b315f8a00c8", "997fd2b4-60e9-4a69-841d-c536211e84a6", "ad3a03d3-3d06-4130-9202-607c3081a7c5"], "durationId": "7ea237bd-d92d-486a-9a4c-4bb36c0a2061"}}, {"head": {"id": "6a3f2d27-cb7b-492e-84d5-0fa3ffa06b13", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000931132500, "endTime": 34000932517800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "78648815-5d53-40bd-a660-6805e77213b9", "logId": "1762748e-fbe9-4200-95a1-5a755b6288e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78648815-5d53-40bd-a660-6805e77213b9", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000928222300}, "additional": {"logType": "detail", "children": [], "durationId": "6a3f2d27-cb7b-492e-84d5-0fa3ffa06b13"}}, {"head": {"id": "a179e577-352f-442c-ae6b-bb0ef1d029ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000928561700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f25a822-5fd5-47f4-aa67-2ecfb0e42415", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000928820100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ab127a-a22a-4ef0-aaf2-6d99c87e43de", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000931141800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a957ef8f-03ee-4ead-b901-797c82e742b5", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000931411000}, "additional": {"logType": "warn", "children": [], "durationId": "6a3f2d27-cb7b-492e-84d5-0fa3ffa06b13"}}, {"head": {"id": "7e8a1656-0b17-45a6-86ba-aeb4cf137a64", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000931898700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c79be317-cd4a-41b3-b27b-3bceffdd97ab", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000931978400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe2357a-715b-47d8-b3ae-0e3f790f4e48", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000932048900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea921119-4be6-4c4c-917b-407d2bada54f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000932097300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "988ca74d-0cc2-4dda-b671-d7d472288b9f", "name": "entry : default@SignHap cost memory 0.114532470703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000932357800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b5b685e-d111-4b2f-99bd-9cd658dc947a", "name": "runTaskFromQueue task cost before running: 2 s 975 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000932454500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1762748e-fbe9-4200-95a1-5a755b6288e0", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000931132500, "endTime": 34000932517800, "totalTime": 1303500}, "additional": {"logType": "info", "children": [], "durationId": "6a3f2d27-cb7b-492e-84d5-0fa3ffa06b13"}}, {"head": {"id": "e77b2c53-2f2e-449f-931d-ac9c0c09be23", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000936718500, "endTime": 34000941756300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "50e0ee68-03d4-4b65-a975-efbcb61874cb", "logId": "29a522c8-c541-4247-ab81-fb760bd7d3a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50e0ee68-03d4-4b65-a975-efbcb61874cb", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000934434900}, "additional": {"logType": "detail", "children": [], "durationId": "e77b2c53-2f2e-449f-931d-ac9c0c09be23"}}, {"head": {"id": "05e661ce-be12-484e-9b79-23dbcfecfbea", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000935549600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f80d822-12f6-4464-a659-6c4ea5f7a62a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000935682000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7231694-e69c-45e2-89bd-86d3a3da7c2c", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000936729400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea18856f-5f33-4208-91ea-8392e91d7fc3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000941388900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab5f47c-4e68-47eb-b650-d1c4cc6478a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000941490300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09f4a190-2899-4571-a240-3e745c19ecc9", "name": "entry : default@CollectDebugSymbol cost memory 0.23934173583984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000941563300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "525add49-1e87-4cfa-a802-6157e4c790b7", "name": "runTaskFromQueue task cost before running: 2 s 985 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000941676900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a522c8-c541-4247-ab81-fb760bd7d3a7", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000936718500, "endTime": 34000941756300, "totalTime": 4933500}, "additional": {"logType": "info", "children": [], "durationId": "e77b2c53-2f2e-449f-931d-ac9c0c09be23"}}, {"head": {"id": "862cc545-1ce7-49b5-a5bb-94aae7a0b77b", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000944519000, "endTime": 34000944769900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "bed8211e-f778-4402-8461-b5e777343a20", "logId": "89ee9728-8972-4969-bc39-8a86f605d76c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bed8211e-f778-4402-8461-b5e777343a20", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000944474800}, "additional": {"logType": "detail", "children": [], "durationId": "862cc545-1ce7-49b5-a5bb-94aae7a0b77b"}}, {"head": {"id": "e48e0aff-5668-496d-9732-d92dddd421ec", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000944525300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f9a1da3-3430-45fb-9526-9ec4d4182634", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000944646700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e611e478-e506-40d8-b0c5-a10f3311f988", "name": "runTaskFromQueue task cost before running: 2 s 988 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000944719200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ee9728-8972-4969-bc39-8a86f605d76c", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000944519000, "endTime": 34000944769900, "totalTime": 181700}, "additional": {"logType": "info", "children": [], "durationId": "862cc545-1ce7-49b5-a5bb-94aae7a0b77b"}}, {"head": {"id": "d43dd81a-6ae0-47aa-a64e-5d7d8eb1e941", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952156000, "endTime": 34000952176900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3283186e-668a-466d-b5cd-6cce38723563", "logId": "5a45afa1-e52b-411d-86d2-3efb223eaf31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a45afa1-e52b-411d-86d2-3efb223eaf31", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952156000, "endTime": 34000952176900}, "additional": {"logType": "info", "children": [], "durationId": "d43dd81a-6ae0-47aa-a64e-5d7d8eb1e941"}}, {"head": {"id": "0673f13b-7dc4-4671-add9-639<PERSON>a2effe", "name": "BUILD SUCCESSFUL in 2 s 995 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952212200}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "aea639c8-2429-4bc4-8cdd-8c4cb8400782", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33997957501600, "endTime": 34000952446300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 56}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "6cd8d730-a0a2-4814-a557-dca33625fcc9", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899e5d6e-a536-477f-9690-4d1b7cf06f99", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952534200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3e72462-8c21-47c7-b454-67bcd96e5d96", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952581000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f88d415-2392-4863-85bd-45a6a5dbcfc1", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952720100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ee0201-5394-486c-9ed1-74b6cf7b6074", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000952887900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b862240-523f-4d9c-868a-1e4407eb3986", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000953221800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90bed2a4-e78c-49ba-a15d-853d5ee22864", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000953819300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e930533d-8716-4705-9980-e9a532f41830", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000954051200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8cd1d12-73ef-432b-962c-f024584066fb", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000954167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f23e2be-0e24-4f63-bd31-ec20a1fefc4f", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000954301600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0f8f7a-f74c-4a47-8611-42e0df181c45", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000954566100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54568b73-8584-4703-8fde-8cae86418c44", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000955473400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc19331a-8e74-4f02-955c-88b8199ff5ec", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000955792200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac847d91-8572-42ef-b822-729956f2e89e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000955872000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3723df79-0499-45a8-8a53-a1a567973592", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000955923900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f2b3ad4-fb27-4b8f-9d7a-200df160aaaf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000955970100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71d23885-90e6-4b70-804b-0eb467ce72bf", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956014200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d202dd9f-41d4-41b5-a324-d97671668c0b", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956275700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c40b76e7-127b-4401-bad5-b16f14c61050", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956450200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f28084e-252f-4264-8d1e-1198c4c5534c", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956614700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c2b96dd-1fd6-4f7c-8cbb-606208242e7e", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956849000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a537224d-be93-48f0-acc8-9a3b008ce485", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1488f7c3-b543-4502-a343-17d8dc17a316", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000956965300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a4eeb3c-6590-4312-a304-1f53d41489f4", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000959558200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "748275f2-7b33-4a3b-9f78-10d1b43bdfc1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000960056800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4114d029-d900-4e61-96f1-217e6cc676c6", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000960677400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab52951-493e-4ea8-bb00-47aa4137f749", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000960850300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c5b9bd-190a-4dba-8bab-91c623285b03", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000961010400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b6d9112-81cf-446a-ba1b-06980d283148", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000961577200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f52e1fd6-b724-4793-bfe2-65ea88d638fd", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000961659700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a876625-917b-4987-a398-cf5370f4f4a5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000961914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f15077a-0271-49ee-bdaa-15c016d0f586", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000962304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a1089c-ba4f-4ed0-bd6e-5e3d41defd3a", "name": "Incremental task entry:default@CompileArkTS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000963072300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0423b917-2572-4aa1-80a0-a4bd59df6881", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000964462900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a6ca05-9eb5-422f-9865-7c074ddf9715", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000965059200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591f89ec-2c9c-4169-b1ef-e40451832941", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000966055700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c88f323d-7e32-4f85-af5b-a534902d9a56", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000966472700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "513e1eda-85be-4924-be06-21fd741e1607", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000967182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46924bf7-232d-4dd9-9f5d-813776ba29d4", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000968025500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2f891e9-b9b9-4754-8353-e89c873e3e30", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000968365300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51ecf74-211e-4541-833a-038426b24e11", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000968541300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "204ea6cc-00d6-4b51-bbc0-c52a788a59d5", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000969039400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e107d7a-fc64-40bc-9233-024d92184e4e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000970092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44307ef-c61b-4de9-97a0-e30debc29bbf", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000970385500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "640fc034-0718-4862-8577-06beb574e6b2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000970583400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df8b542-17e2-4735-a1d8-3424d7a35aea", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000977069900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a1d66c5-f642-46e6-a52e-385f5e12f558", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000977414300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6269a68f-b271-4ef3-881b-588a3c27390d", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000977655100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a21a6c-1d64-449b-8ebe-1a7b57dd95b1", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000977731800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dd265de-5702-4608-9d26-370640c82f06", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000978013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7abe30e5-7130-4aa0-9234-d5c79036a3a1", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000978733800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae963c61-5c3b-446b-be8c-757812115933", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000978985800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a49774fa-15de-4988-83eb-78c784b32c0b", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000979195200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "893f2b6c-3c4e-41eb-8225-5f4b25e01a82", "name": "Incremental task entry:default@PackageHap post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000980535900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0adae3f-c286-448c-8111-a88bf0f2a0ff", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000980700800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d88d52c-2927-40fa-900a-3ef6c956e746", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000980767100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba677635-2d37-4090-b573-525e3c59bbe9", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000980947000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba82b353-3a0c-41ac-8c52-7502d26349d8", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000983320900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86d89c3-022d-40da-bdbe-548bb30928fc", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000983582900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bf69f33-926d-47c0-87ce-5040ae9e28e7", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000983865800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1879804a-7f8a-4e57-be10-6fc4b080f16b", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 34000984074600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}