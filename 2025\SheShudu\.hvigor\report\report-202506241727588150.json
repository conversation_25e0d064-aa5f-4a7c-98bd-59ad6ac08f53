{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "280e117a-9d7a-4c0a-bf05-cbf236e56308", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281774660900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9102029d-6151-41a6-99d5-dd258a4b6317", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281779565700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac5a3f6f-1366-4b86-b579-609f3458c4e9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281780011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "414924a5-4cad-423b-8487-6fc47f47b47d", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281785595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e5d8063-fceb-4063-a0b7-33836e6768d3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310708100900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce5af40f-6a53-4247-806c-695034d5edc3", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310716772700, "endTime": 32311257560000}, "additional": {"children": ["a5f4c1e7-fdcd-40df-be89-f11867e453db", "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "b565c0e7-942c-43c7-aa58-03c42e7f4270", "338648fc-6cc1-47e0-9989-d7efc2992ee2", "8ced28ec-cb22-4a7b-80b7-127ac2fce30d", "781b97d4-be7b-4b2e-96e3-3ba0a4c39305", "86a54e4a-7908-48c0-a8aa-108690fe0fa9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5f4c1e7-fdcd-40df-be89-f11867e453db", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310716774600, "endTime": 32310733371100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "9009e3ae-cda3-4124-9819-85a1b0b57c0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310733395800, "endTime": 32311256381400}, "additional": {"children": ["0bbb8572-b682-4ac6-9026-8cf16de46996", "ddd57acc-1cb9-4c65-926c-43a0d55c80fc", "fe2d76af-0e78-49bc-8976-e690ba0f2d18", "8312fbf3-3bff-43f2-9c27-24b5fd3ddbce", "4f4c5250-f7e1-4f68-87fe-9c1921da0cca", "50d2b2db-ef11-4d79-a23f-6ef16112b794", "5d0e5e23-efde-4815-bae8-2a43f4106750", "863c2681-c09b-42d5-a7dc-c4737d1a1a0d", "53d1ed4b-ec16-4339-ad61-92ee04a1e517"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b565c0e7-942c-43c7-aa58-03c42e7f4270", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311256403800, "endTime": 32311257550900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "8d5e7b8d-20bc-484e-b7fd-715dfb6963cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "338648fc-6cc1-47e0-9989-d7efc2992ee2", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311257555700, "endTime": 32311257556800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "f0dd78b1-1560-4e85-a98f-a4cb25041554"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ced28ec-cb22-4a7b-80b7-127ac2fce30d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310720817200, "endTime": 32310720858000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "b4776db5-7cd3-4944-9161-b526a338394c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4776db5-7cd3-4944-9161-b526a338394c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310720817200, "endTime": 32310720858000}, "additional": {"logType": "info", "children": [], "durationId": "8ced28ec-cb22-4a7b-80b7-127ac2fce30d", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "781b97d4-be7b-4b2e-96e3-3ba0a4c39305", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310725885300, "endTime": 32310725917800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "eb65209a-9a2a-4dc1-9fed-252bb39bea60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb65209a-9a2a-4dc1-9fed-252bb39bea60", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310725885300, "endTime": 32310725917800}, "additional": {"logType": "info", "children": [], "durationId": "781b97d4-be7b-4b2e-96e3-3ba0a4c39305", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "21f27148-fa7b-4174-ae57-2527d3b36d7e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310726166900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd4fc8f-daf0-46fb-bdf9-d9f1016ce19f", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310733203100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9009e3ae-cda3-4124-9819-85a1b0b57c0d", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310716774600, "endTime": 32310733371100}, "additional": {"logType": "info", "children": [], "durationId": "a5f4c1e7-fdcd-40df-be89-f11867e453db", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "0bbb8572-b682-4ac6-9026-8cf16de46996", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310739326400, "endTime": 32310739334900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "9c1a2cb2-7617-4bde-80ab-7882ed8ca388"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddd57acc-1cb9-4c65-926c-43a0d55c80fc", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310739353400, "endTime": 32310751058200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "d525181f-1a77-4605-8a1b-ea317bcdf933"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe2d76af-0e78-49bc-8976-e690ba0f2d18", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310751077900, "endTime": 32310915878800}, "additional": {"children": ["9e5dc102-73a8-49c3-9fbe-6ba09e1e6fcc", "5d215ecc-b122-4658-b6b2-ba236f209ce3", "16b77bf9-3040-419b-940a-5737dc42392d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "16df1ae0-2af4-471a-9f04-108d542c0301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8312fbf3-3bff-43f2-9c27-24b5fd3ddbce", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310915946000, "endTime": 32311010989900}, "additional": {"children": ["1728f098-9455-47be-8833-87206d0a0eba"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "28ba88f9-c615-43f4-ad39-e3332812c96e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f4c5250-f7e1-4f68-87fe-9c1921da0cca", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311010998800, "endTime": 32311193700200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "1f14f9f0-a1b5-443f-b890-46bcc3a824a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50d2b2db-ef11-4d79-a23f-6ef16112b794", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311195040400, "endTime": 32311227043600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "2c159178-26d5-4088-b7e2-20365a7939bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d0e5e23-efde-4815-bae8-2a43f4106750", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311227065300, "endTime": 32311256099500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "258935bf-103e-4b3b-a329-317502253a99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "863c2681-c09b-42d5-a7dc-c4737d1a1a0d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311256158600, "endTime": 32311256366700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "c076a08b-ffcd-49ad-b22c-2a48b1dce083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c1a2cb2-7617-4bde-80ab-7882ed8ca388", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310739326400, "endTime": 32310739334900}, "additional": {"logType": "info", "children": [], "durationId": "0bbb8572-b682-4ac6-9026-8cf16de46996", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "d525181f-1a77-4605-8a1b-ea317bcdf933", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310739353400, "endTime": 32310751058200}, "additional": {"logType": "info", "children": [], "durationId": "ddd57acc-1cb9-4c65-926c-43a0d55c80fc", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "9e5dc102-73a8-49c3-9fbe-6ba09e1e6fcc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310752329900, "endTime": 32310752351800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe2d76af-0e78-49bc-8976-e690ba0f2d18", "logId": "d7a4294d-6619-436d-a41a-6124d022f991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7a4294d-6619-436d-a41a-6124d022f991", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310752329900, "endTime": 32310752351800}, "additional": {"logType": "info", "children": [], "durationId": "9e5dc102-73a8-49c3-9fbe-6ba09e1e6fcc", "parent": "16df1ae0-2af4-471a-9f04-108d542c0301"}}, {"head": {"id": "5d215ecc-b122-4658-b6b2-ba236f209ce3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310756561300, "endTime": 32310913423000}, "additional": {"children": ["a473150e-e41a-4ab3-8ca6-16dacb30d3ae", "45f7c603-58d1-4eae-832a-0fb75e3eceeb"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe2d76af-0e78-49bc-8976-e690ba0f2d18", "logId": "ec16f0d0-7218-4762-a49a-32d4f54d4f5f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a473150e-e41a-4ab3-8ca6-16dacb30d3ae", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310756563300, "endTime": 32310766052300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5d215ecc-b122-4658-b6b2-ba236f209ce3", "logId": "c00e3209-5546-4beb-9761-ae0439308877"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45f7c603-58d1-4eae-832a-0fb75e3eceeb", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310766071200, "endTime": 32310913395500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5d215ecc-b122-4658-b6b2-ba236f209ce3", "logId": "ef2b6974-23fe-423d-a241-fa9f4db33461"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d5ff843-d901-470f-8a3a-be9c07e2c624", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310756569300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db0cdb94-106d-404f-aa2b-844564a46c90", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310765913800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c00e3209-5546-4beb-9761-ae0439308877", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310756563300, "endTime": 32310766052300}, "additional": {"logType": "info", "children": [], "durationId": "a473150e-e41a-4ab3-8ca6-16dacb30d3ae", "parent": "ec16f0d0-7218-4762-a49a-32d4f54d4f5f"}}, {"head": {"id": "761043c4-f438-42a8-83ed-7d2c71686e25", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310766083100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea59f3e6-796c-4d7f-95d0-78224c7196e0", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310775965500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d0fc24-21da-4eb7-a71a-aa994702e997", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310776148700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8cb6a0-43b4-4e51-a56d-ec22a76aa743", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310776357200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f71a06-9596-46ee-acc3-ce6f9d8f2a0a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310776555900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52d0d79f-dbab-4f1c-8b2e-a6c5e35fbebd", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310778290900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b4da7f-506b-4f5c-b09a-a7059b210d0d", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310785508300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4872d97b-f984-4040-a135-5162f6679120", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310824765700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "892bde6c-e2d0-4bda-8e18-e89d158b33df", "name": "Sdk init in 79 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310868718600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c070166-908c-4c40-86ad-440caf07c8e5", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310868873700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 27}, "markType": "other"}}, {"head": {"id": "d5274b09-f475-4a66-9796-8d1abf63ad18", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310868929500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 27}, "markType": "other"}}, {"head": {"id": "5a345656-beec-43f1-a587-c0d33323ce2e", "name": "Project task initialization takes 41 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310911435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8ce23e-c198-47b7-9e6e-65eefd441ed7", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310912685900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6f15f14-f586-4a98-ab00-6895f8cf9f37", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310913050300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796b2e56-d729-46b6-bd63-9cf7ca4e0edb", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310913209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef2b6974-23fe-423d-a241-fa9f4db33461", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310766071200, "endTime": 32310913395500}, "additional": {"logType": "info", "children": [], "durationId": "45f7c603-58d1-4eae-832a-0fb75e3eceeb", "parent": "ec16f0d0-7218-4762-a49a-32d4f54d4f5f"}}, {"head": {"id": "ec16f0d0-7218-4762-a49a-32d4f54d4f5f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310756561300, "endTime": 32310913423000}, "additional": {"logType": "info", "children": ["c00e3209-5546-4beb-9761-ae0439308877", "ef2b6974-23fe-423d-a241-fa9f4db33461"], "durationId": "5d215ecc-b122-4658-b6b2-ba236f209ce3", "parent": "16df1ae0-2af4-471a-9f04-108d542c0301"}}, {"head": {"id": "16b77bf9-3040-419b-940a-5737dc42392d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310915826600, "endTime": 32310915849000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fe2d76af-0e78-49bc-8976-e690ba0f2d18", "logId": "10f118a9-a42b-476b-8521-7aff1e9928f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10f118a9-a42b-476b-8521-7aff1e9928f3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310915826600, "endTime": 32310915849000}, "additional": {"logType": "info", "children": [], "durationId": "16b77bf9-3040-419b-940a-5737dc42392d", "parent": "16df1ae0-2af4-471a-9f04-108d542c0301"}}, {"head": {"id": "16df1ae0-2af4-471a-9f04-108d542c0301", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310751077900, "endTime": 32310915878800}, "additional": {"logType": "info", "children": ["d7a4294d-6619-436d-a41a-6124d022f991", "ec16f0d0-7218-4762-a49a-32d4f54d4f5f", "10f118a9-a42b-476b-8521-7aff1e9928f3"], "durationId": "fe2d76af-0e78-49bc-8976-e690ba0f2d18", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "1728f098-9455-47be-8833-87206d0a0eba", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310918897100, "endTime": 32311010977400}, "additional": {"children": ["06708538-37f3-4ef4-9b3f-e3a541c4dea3", "a1dba2cf-7cae-4360-b682-3fcfaaaac2e0", "db0ac1fb-a908-4ce8-b865-f34eb8a050b3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8312fbf3-3bff-43f2-9c27-24b5fd3ddbce", "logId": "b54e49bd-2f1f-4d08-b87b-1e8fbebe0ca2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06708538-37f3-4ef4-9b3f-e3a541c4dea3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310939032800, "endTime": 32310939056900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1728f098-9455-47be-8833-87206d0a0eba", "logId": "7c809579-beba-40ae-87d0-a72972db6d76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c809579-beba-40ae-87d0-a72972db6d76", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310939032800, "endTime": 32310939056900}, "additional": {"logType": "info", "children": [], "durationId": "06708538-37f3-4ef4-9b3f-e3a541c4dea3", "parent": "b54e49bd-2f1f-4d08-b87b-1e8fbebe0ca2"}}, {"head": {"id": "a1dba2cf-7cae-4360-b682-3fcfaaaac2e0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310946333900, "endTime": 32311005596300}, "additional": {"children": ["e5ea9a2d-5322-487c-9a2f-293a1853d0b4", "c277fa8c-2103-4201-b7a2-c12bbe27258a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1728f098-9455-47be-8833-87206d0a0eba", "logId": "087fbd3b-4012-48da-aa88-e7176052f24d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5ea9a2d-5322-487c-9a2f-293a1853d0b4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310946336100, "endTime": 32310968234500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1dba2cf-7cae-4360-b682-3fcfaaaac2e0", "logId": "8aeca31c-bb7b-4960-9026-77b613f58f0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c277fa8c-2103-4201-b7a2-c12bbe27258a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310968272900, "endTime": 32311005575200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1dba2cf-7cae-4360-b682-3fcfaaaac2e0", "logId": "a08a9368-4c35-4c61-8c2c-46eecb454b31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26331025-f085-46c0-9972-68297974014b", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310946342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdd4f1fa-7c87-4945-bda5-5d09c2035d6e", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310967552900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aeca31c-bb7b-4960-9026-77b613f58f0c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310946336100, "endTime": 32310968234500}, "additional": {"logType": "info", "children": [], "durationId": "e5ea9a2d-5322-487c-9a2f-293a1853d0b4", "parent": "087fbd3b-4012-48da-aa88-e7176052f24d"}}, {"head": {"id": "36fbfda2-aeaa-4e7c-a7e3-7e0ae8ee6a0e", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310968297700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44f0c4c-1ea3-401e-966b-8d013291cbed", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310992008000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac748e49-9d63-4a0b-9a4c-35ec79d3cda6", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310993362700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "659e0039-5454-4a1a-b2dc-88a6a08df215", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310993962400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb19f021-332e-4218-ad69-e7f359c8d6e7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310994308000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "020d7c83-8d9e-404b-9127-a12e8369598d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310994479400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03926a86-bc97-4be6-8a81-ef9ad46a2b9f", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310994673700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a02029ba-e74d-4288-85c9-e49e0306b2d9", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310994872100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cee79c84-fdd6-4c8b-bb08-20134dc775f8", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311004825100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad18d3f4-2d0b-4b01-8596-0d9e8741a759", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311005030400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d245af-16d1-4c7c-bde1-75d86aa2ea8f", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311005098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "532591b6-3451-4e7a-b674-e63b0e7df887", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311005452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a08a9368-4c35-4c61-8c2c-46eecb454b31", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310968272900, "endTime": 32311005575200}, "additional": {"logType": "info", "children": [], "durationId": "c277fa8c-2103-4201-b7a2-c12bbe27258a", "parent": "087fbd3b-4012-48da-aa88-e7176052f24d"}}, {"head": {"id": "087fbd3b-4012-48da-aa88-e7176052f24d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310946333900, "endTime": 32311005596300}, "additional": {"logType": "info", "children": ["8aeca31c-bb7b-4960-9026-77b613f58f0c", "a08a9368-4c35-4c61-8c2c-46eecb454b31"], "durationId": "a1dba2cf-7cae-4360-b682-3fcfaaaac2e0", "parent": "b54e49bd-2f1f-4d08-b87b-1e8fbebe0ca2"}}, {"head": {"id": "db0ac1fb-a908-4ce8-b865-f34eb8a050b3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311010940900, "endTime": 32311010961900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1728f098-9455-47be-8833-87206d0a0eba", "logId": "6320f2d3-b734-40a5-a40c-0df705e6d647"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6320f2d3-b734-40a5-a40c-0df705e6d647", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311010940900, "endTime": 32311010961900}, "additional": {"logType": "info", "children": [], "durationId": "db0ac1fb-a908-4ce8-b865-f34eb8a050b3", "parent": "b54e49bd-2f1f-4d08-b87b-1e8fbebe0ca2"}}, {"head": {"id": "b54e49bd-2f1f-4d08-b87b-1e8fbebe0ca2", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310918897100, "endTime": 32311010977400}, "additional": {"logType": "info", "children": ["7c809579-beba-40ae-87d0-a72972db6d76", "087fbd3b-4012-48da-aa88-e7176052f24d", "6320f2d3-b734-40a5-a40c-0df705e6d647"], "durationId": "1728f098-9455-47be-8833-87206d0a0eba", "parent": "28ba88f9-c615-43f4-ad39-e3332812c96e"}}, {"head": {"id": "28ba88f9-c615-43f4-ad39-e3332812c96e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310915946000, "endTime": 32311010989900}, "additional": {"logType": "info", "children": ["b54e49bd-2f1f-4d08-b87b-1e8fbebe0ca2"], "durationId": "8312fbf3-3bff-43f2-9c27-24b5fd3ddbce", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "5cce32bd-db4f-4615-8828-cd4ae4a31147", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311058454600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f205371-03f2-4def-b705-103861226695", "name": "hvigorfile, resolve hvigorfile dependencies in 183 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311193098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f14f9f0-a1b5-443f-b890-46bcc3a824a2", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311010998800, "endTime": 32311193700200}, "additional": {"logType": "info", "children": [], "durationId": "4f4c5250-f7e1-4f68-87fe-9c1921da0cca", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "53d1ed4b-ec16-4339-ad61-92ee04a1e517", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311194751400, "endTime": 32311195022200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "logId": "c5954b10-e40b-4537-90ba-8e59dabf3b10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40cc6ae7-ca5e-469c-9c77-1047532ff8d2", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311194781500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5954b10-e40b-4537-90ba-8e59dabf3b10", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311194751400, "endTime": 32311195022200}, "additional": {"logType": "info", "children": [], "durationId": "53d1ed4b-ec16-4339-ad61-92ee04a1e517", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "8d245b8f-c36c-4484-b951-4b8ccfde4d28", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311202468300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d06587-2b84-4a82-93e1-7a9252da95e7", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311222330000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c159178-26d5-4088-b7e2-20365a7939bd", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311195040400, "endTime": 32311227043600}, "additional": {"logType": "info", "children": [], "durationId": "50d2b2db-ef11-4d79-a23f-6ef16112b794", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "c9b1ff34-383c-448e-ba20-e0bc9ef8b988", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311244070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e326cd-5614-4188-8fbf-fe0c29f8e78c", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311244278300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "927793e9-a89f-4585-8158-68ca2e8591b0", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311251731700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41411f36-9555-4ff6-b0fa-8b877c54d437", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311251894100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "258935bf-103e-4b3b-a329-317502253a99", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311227065300, "endTime": 32311256099500}, "additional": {"logType": "info", "children": [], "durationId": "5d0e5e23-efde-4815-bae8-2a43f4106750", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "61b70acc-bb18-4c9b-bcc0-2c39b89a92da", "name": "Configuration phase cost:517 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311256183200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c076a08b-ffcd-49ad-b22c-2a48b1dce083", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311256158600, "endTime": 32311256366700}, "additional": {"logType": "info", "children": [], "durationId": "863c2681-c09b-42d5-a7dc-c4737d1a1a0d", "parent": "c1edc3a5-ba1f-4978-98f6-0739a83da94d"}}, {"head": {"id": "c1edc3a5-ba1f-4978-98f6-0739a83da94d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310733395800, "endTime": 32311256381400}, "additional": {"logType": "info", "children": ["9c1a2cb2-7617-4bde-80ab-7882ed8ca388", "d525181f-1a77-4605-8a1b-ea317bcdf933", "16df1ae0-2af4-471a-9f04-108d542c0301", "28ba88f9-c615-43f4-ad39-e3332812c96e", "1f14f9f0-a1b5-443f-b890-46bcc3a824a2", "2c159178-26d5-4088-b7e2-20365a7939bd", "258935bf-103e-4b3b-a329-317502253a99", "c076a08b-ffcd-49ad-b22c-2a48b1dce083", "c5954b10-e40b-4537-90ba-8e59dabf3b10"], "durationId": "cdbf33f7-0c1c-4582-83e2-9391a5f4024f", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "86a54e4a-7908-48c0-a8aa-108690fe0fa9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311257528100, "endTime": 32311257542200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ce5af40f-6a53-4247-806c-695034d5edc3", "logId": "6fc432ab-acac-4d2c-bc0c-4bfcbb3359c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fc432ab-acac-4d2c-bc0c-4bfcbb3359c1", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311257528100, "endTime": 32311257542200}, "additional": {"logType": "info", "children": [], "durationId": "86a54e4a-7908-48c0-a8aa-108690fe0fa9", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "8d5e7b8d-20bc-484e-b7fd-715dfb6963cd", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311256403800, "endTime": 32311257550900}, "additional": {"logType": "info", "children": [], "durationId": "b565c0e7-942c-43c7-aa58-03c42e7f4270", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "f0dd78b1-1560-4e85-a98f-a4cb25041554", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311257555700, "endTime": 32311257556800}, "additional": {"logType": "info", "children": [], "durationId": "338648fc-6cc1-47e0-9989-d7efc2992ee2", "parent": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b"}}, {"head": {"id": "7e994c31-e9fd-4acd-92d2-898c4c5dd26b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310716772700, "endTime": 32311257560000}, "additional": {"logType": "info", "children": ["9009e3ae-cda3-4124-9819-85a1b0b57c0d", "c1edc3a5-ba1f-4978-98f6-0739a83da94d", "8d5e7b8d-20bc-484e-b7fd-715dfb6963cd", "f0dd78b1-1560-4e85-a98f-a4cb25041554", "b4776db5-7cd3-4944-9161-b526a338394c", "eb65209a-9a2a-4dc1-9fed-252bb39bea60", "6fc432ab-acac-4d2c-bc0c-4bfcbb3359c1"], "durationId": "ce5af40f-6a53-4247-806c-695034d5edc3"}}, {"head": {"id": "e7ce2deb-de8e-410e-88a0-f04575b70dac", "name": "Configuration task cost before running: 546 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311257841000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d83cace-eeb0-4783-991d-2b5ba134dc44", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311262910100, "endTime": 32311272505500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "10e29656-9337-433d-849d-b4ebc412939f", "logId": "baaccdd0-1d43-454a-97ef-0dd13dd57f82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10e29656-9337-433d-849d-b4ebc412939f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311259640600}, "additional": {"logType": "detail", "children": [], "durationId": "6d83cace-eeb0-4783-991d-2b5ba134dc44"}}, {"head": {"id": "cf3e7581-c314-4292-811c-3a8827a9ef00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311260006000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a245273c-76cd-40c0-b59e-d1a7f703b607", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311260173500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbae46e0-2d11-4217-aea0-83b4c73d19e7", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311262923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dabd4cf1-be0e-4f78-a36e-41b14ec4d77b", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311272130500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bb445a-2c0e-4a49-b843-961d43cba0c2", "name": "entry : default@PreBuild cost memory 0.5305404663085938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311272410000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baaccdd0-1d43-454a-97ef-0dd13dd57f82", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311262910100, "endTime": 32311272505500}, "additional": {"logType": "info", "children": [], "durationId": "6d83cace-eeb0-4783-991d-2b5ba134dc44"}}, {"head": {"id": "5105feae-23c6-4484-a025-542e8a8ac0a6", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311276561300, "endTime": 32311279722700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "db4387f7-a7cc-49b5-a822-2aa408577bd4", "logId": "90d2135e-5f88-453f-a72f-ba78072946b0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db4387f7-a7cc-49b5-a822-2aa408577bd4", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311275471300}, "additional": {"logType": "detail", "children": [], "durationId": "5105feae-23c6-4484-a025-542e8a8ac0a6"}}, {"head": {"id": "8e70b37f-8eb8-4129-bef6-0e1f0e6d2ed2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311275798900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571bbb3e-ec1a-494d-90df-23295fb7501a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311275892700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5f69d78-0cde-46e8-9966-846c0916964d", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311276569400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763ee256-66ad-4e43-93a0-02b4951fd872", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311277229600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d18b82-1630-4086-8656-0e45af1c475c", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311279521800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfb0ac5b-69c4-40da-b1d7-385e225cab9d", "name": "entry : default@GenerateMetadata cost memory -3.8796005249023438", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311279651500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d2135e-5f88-453f-a72f-ba78072946b0", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311276561300, "endTime": 32311279722700}, "additional": {"logType": "info", "children": [], "durationId": "5105feae-23c6-4484-a025-542e8a8ac0a6"}}, {"head": {"id": "999c4f05-9f55-45c8-877b-5cc16c879fec", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282716800, "endTime": 32311283078000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a150fc71-5bb1-44bc-a361-e4a5482fdf8e", "logId": "a7efaf88-9cbd-415f-8f5e-f0135bf6e61a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a150fc71-5bb1-44bc-a361-e4a5482fdf8e", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311281954800}, "additional": {"logType": "detail", "children": [], "durationId": "999c4f05-9f55-45c8-877b-5cc16c879fec"}}, {"head": {"id": "444c08cb-786c-475f-958d-0f394a4ff7d3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282441400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df2331ec-2f42-4e28-a21a-5dc066b9e079", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282560400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5acaccf1-6ae6-476a-b221-628b8e09a2b1", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282723700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0580e2d5-7ae0-40ab-a13b-87a698d60374", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282820500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0868614-e428-4de5-bde6-f16c29f8b151", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282870300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6166a025-ab57-4b19-ae22-6712c059934a", "name": "entry : default@ConfigureCmake cost memory 0.0360565185546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282945400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f29dcc85-ff20-4f5e-97e4-99e161437187", "name": "runTaskFromQueue task cost before running: 571 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311283019800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7efaf88-9cbd-415f-8f5e-f0135bf6e61a", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311282716800, "endTime": 32311283078000, "totalTime": 285000}, "additional": {"logType": "info", "children": [], "durationId": "999c4f05-9f55-45c8-877b-5cc16c879fec"}}, {"head": {"id": "c3433a74-d888-4794-b10e-8cb5f276906b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311285475800, "endTime": 32311286928800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a1874f35-47e6-4001-9a11-85f45040c5d3", "logId": "0cf864e4-0565-4ca0-981a-0ea874a780d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1874f35-47e6-4001-9a11-85f45040c5d3", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311284495700}, "additional": {"logType": "detail", "children": [], "durationId": "c3433a74-d888-4794-b10e-8cb5f276906b"}}, {"head": {"id": "cc74b897-2f44-4bb7-a2ab-18fe6772886e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311284836400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "107514f9-081d-4c37-be45-d76867f60d9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311284969200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd1b09b7-23b0-4ddd-b106-486a08c0ca7b", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311285484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f755530-f972-411f-9993-9b25c0d3e270", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311286783300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54bfb0d6-640a-49bd-8017-ad7404ce0cae", "name": "entry : default@MergeProfile cost memory 0.10509490966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311286872500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cf864e4-0565-4ca0-981a-0ea874a780d5", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311285475800, "endTime": 32311286928800}, "additional": {"logType": "info", "children": [], "durationId": "c3433a74-d888-4794-b10e-8cb5f276906b"}}, {"head": {"id": "b77eca66-b783-47cd-814a-231eed013a3c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311289109900, "endTime": 32311290689700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "089a51f2-1cd1-4f14-852f-3e3dbaca7678", "logId": "02afb786-3290-48a9-821e-52d82414b61b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "089a51f2-1cd1-4f14-852f-3e3dbaca7678", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311288165400}, "additional": {"logType": "detail", "children": [], "durationId": "b77eca66-b783-47cd-814a-231eed013a3c"}}, {"head": {"id": "e9ddd6b4-2104-4e60-8fb6-ce14dd00d745", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311288476900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5fb5fa2-801b-4878-b9a1-1caad554b018", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311288556900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de9992ee-29ea-4d1e-8168-e736e2521287", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311289119200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e30275-8206-46a3-8443-35149eaa24f2", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311289780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a64b4e-0c57-41b1-936b-71a2faeacc0e", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311290549300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50f60b14-492b-4f2e-813f-a372b588f3e4", "name": "entry : default@CreateBuildProfile cost memory 0.10121917724609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311290630600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02afb786-3290-48a9-821e-52d82414b61b", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311289109900, "endTime": 32311290689700}, "additional": {"logType": "info", "children": [], "durationId": "b77eca66-b783-47cd-814a-231eed013a3c"}}, {"head": {"id": "3864c62a-de6e-4e8d-8ef8-4d6c559f2465", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292805000, "endTime": 32311293126000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a1c6abd3-417d-43ff-97ec-b7a66b067405", "logId": "04f83d43-40a0-451b-86b9-8b0328190eaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1c6abd3-417d-43ff-97ec-b7a66b067405", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311291867600}, "additional": {"logType": "detail", "children": [], "durationId": "3864c62a-de6e-4e8d-8ef8-4d6c559f2465"}}, {"head": {"id": "be193531-428a-4cc7-a0a7-3c03c42ad6ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292181600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e94e7c-6cce-4489-8879-a19e54316966", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292265100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d88f41d-f22d-4893-ba58-774b9a37b53e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abfdca18-cd89-4367-b99f-d0b1cce3639f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292903400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ab88768-13d7-4fcf-828a-685fddd7ff8d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26c011ba-a206-4daa-bdbd-47781fb67d5f", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311293011600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c829c5e7-f2d0-4304-bdcb-07c31b04bd3c", "name": "runTaskFromQueue task cost before running: 581 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311293074200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04f83d43-40a0-451b-86b9-8b0328190eaa", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311292805000, "endTime": 32311293126000, "totalTime": 255600}, "additional": {"logType": "info", "children": [], "durationId": "3864c62a-de6e-4e8d-8ef8-4d6c559f2465"}}, {"head": {"id": "b26798e7-83f4-41cd-9b8f-d9c9b061e2ab", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311298782400, "endTime": 32311299491300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3c7a05de-7cba-4492-8e38-f319b6ae4bdc", "logId": "90baf432-e570-4cc5-ab12-11b2da9c030f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c7a05de-7cba-4492-8e38-f319b6ae4bdc", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311294377600}, "additional": {"logType": "detail", "children": [], "durationId": "b26798e7-83f4-41cd-9b8f-d9c9b061e2ab"}}, {"head": {"id": "9c7dd149-edb7-4918-9ef3-36ca611f92eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311294698100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "260d66f4-beab-4bef-86b1-c4bcd02c3e45", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311294794100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3270ebb0-4976-49c4-a3f7-a666e1b2054c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311298794900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30c54a3-7115-40ed-937a-f59673eba5d0", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311299015500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93fc564e-202b-4bdb-93fa-6de1435cedc3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.038543701171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311299324100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf69aa5a-ab97-4a0d-8d7e-5f24066b9bfe", "name": "runTaskFromQueue task cost before running: 588 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311299434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90baf432-e570-4cc5-ab12-11b2da9c030f", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311298782400, "endTime": 32311299491300, "totalTime": 634900}, "additional": {"logType": "info", "children": [], "durationId": "b26798e7-83f4-41cd-9b8f-d9c9b061e2ab"}}, {"head": {"id": "a103acef-e1b2-4cce-81b7-091ac4e72199", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311305139800, "endTime": 32311306801600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "f37b23e2-2664-4a2c-82a9-ada25eda3e91", "logId": "1931274e-08a8-4024-b456-b4a5b22c9493"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f37b23e2-2664-4a2c-82a9-ada25eda3e91", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311302013700}, "additional": {"logType": "detail", "children": [], "durationId": "a103acef-e1b2-4cce-81b7-091ac4e72199"}}, {"head": {"id": "5b52e68a-3e3a-45d0-b04a-c3a408fdaa9e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311302685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b4ca0ff-9287-47c5-be7a-0c54a9ebf775", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311302896500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ae7c4cc-15f2-48d9-afd2-f233682eab6f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311305151500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ad835f-f943-4a1c-b744-8c4aac38c32f", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311306391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d85fdd6a-8ad3-4059-9312-a58bb67cef41", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311306493800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2b7761a-5902-40d3-acaa-7b77792b5397", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311306569400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbfd745f-167c-4389-9f92-022da5b2d458", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311306621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7705253-e43c-44fa-b757-f3903993c8f4", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1171417236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311306687200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f52b0e5-67c5-4af0-983a-7159666e50b7", "name": "runTaskFromQueue task cost before running: 595 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311306750900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1931274e-08a8-4024-b456-b4a5b22c9493", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311305139800, "endTime": 32311306801600, "totalTime": 1600200}, "additional": {"logType": "info", "children": [], "durationId": "a103acef-e1b2-4cce-81b7-091ac4e72199"}}, {"head": {"id": "8bcd252e-435d-4c29-ac86-b48f9c5915cc", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311309756600, "endTime": 32311310138400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6fdcf411-a710-4a50-88f2-29db9b86b7e5", "logId": "9c196fbb-989e-43bd-aa9a-833281a5e392"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6fdcf411-a710-4a50-88f2-29db9b86b7e5", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311308514000}, "additional": {"logType": "detail", "children": [], "durationId": "8bcd252e-435d-4c29-ac86-b48f9c5915cc"}}, {"head": {"id": "654dc4a6-1bff-4a33-a221-01c6ec7b357c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311308892600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b831440-9dea-47b5-bfd2-1c6dcc69b742", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311308983700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a381d7-4e41-41e0-8640-031e5b7af4b6", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311309767600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32bc308-18bc-4d9d-89af-8a452452d068", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311309886100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a085ff67-b5db-4817-911d-3c8b4be2647b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311309943100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eb4c22d-406c-4978-afcc-4b66f9f9c405", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311310011700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55929027-e5da-47ec-8924-1ffd2aac7cce", "name": "runTaskFromQueue task cost before running: 598 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311310083800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c196fbb-989e-43bd-aa9a-833281a5e392", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311309756600, "endTime": 32311310138400, "totalTime": 310900}, "additional": {"logType": "info", "children": [], "durationId": "8bcd252e-435d-4c29-ac86-b48f9c5915cc"}}, {"head": {"id": "1ff437b7-92f3-4b05-9ac2-24377c34e3d3", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311312566400, "endTime": 32311316271500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a8e3988e-70f0-46a9-b866-784594ebedd9", "logId": "5c95fbc9-575e-442a-a6db-1db59f644b24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8e3988e-70f0-46a9-b866-784594ebedd9", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311311580700}, "additional": {"logType": "detail", "children": [], "durationId": "1ff437b7-92f3-4b05-9ac2-24377c34e3d3"}}, {"head": {"id": "e806b70a-6669-4ae7-8979-120b124af609", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311311925700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8306125-7138-4a17-b3fb-92c0adfeb9f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311312011500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d43299bf-f77a-426a-afd0-fc83a3dd072c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311312573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b0c7d08-c2f6-4f97-9621-697e5795102d", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311316084900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d51398-b74a-4938-9943-8b42941c276a", "name": "entry : default@MakePackInfo cost memory 0.138458251953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311316207600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c95fbc9-575e-442a-a6db-1db59f644b24", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311312566400, "endTime": 32311316271500}, "additional": {"logType": "info", "children": [], "durationId": "1ff437b7-92f3-4b05-9ac2-24377c34e3d3"}}, {"head": {"id": "082e52fe-6688-4652-86dd-f6e98a4831a0", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311319489200, "endTime": 32311322068900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "d1aeef1b-41d6-4bbb-8631-766ac95e581a", "logId": "ecc4be64-bdf8-4f16-a6f8-2f358a68d949"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1aeef1b-41d6-4bbb-8631-766ac95e581a", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311318026900}, "additional": {"logType": "detail", "children": [], "durationId": "082e52fe-6688-4652-86dd-f6e98a4831a0"}}, {"head": {"id": "1078dcbe-72b3-49bb-a1a8-a8f3a55bc654", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311318370900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5116eee-33e1-459c-9762-a754556f503b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311318455600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e255d41-0016-4416-856b-e3e6c0171f0f", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311319497300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684bc078-8aa8-406f-bf96-814ab70c857d", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311319627900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88f11062-aa44-4861-8948-8d4f115feb04", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311320188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc356158-a429-484c-b5d7-dc27d7b0c484", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311321645800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04f34d3c-4194-4ee2-a2a8-ad63fd8524b2", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311321744200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b594e464-ae10-44ec-bbf4-da3d1913f04c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311321825800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e0524e7-3c71-47bc-99a9-ea4ce9c86b95", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311321881400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bcaf692-8a02-44fc-94d0-f7e1aaf5fcc2", "name": "entry : default@SyscapTransform cost memory 0.15123748779296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311321950100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae8aee18-174e-4e92-aec3-a901079b6742", "name": "runTaskFromQueue task cost before running: 610 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311322016900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecc4be64-bdf8-4f16-a6f8-2f358a68d949", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311319489200, "endTime": 32311322068900, "totalTime": 2515100}, "additional": {"logType": "info", "children": [], "durationId": "082e52fe-6688-4652-86dd-f6e98a4831a0"}}, {"head": {"id": "bf37ae3c-b2d8-425d-aff5-cf556319fbaa", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311324834700, "endTime": 32311325710800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "4b502adb-db62-4cd1-b27b-6cae6b581236", "logId": "136d8c59-d884-41ea-a99e-59735aa86b9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b502adb-db62-4cd1-b27b-6cae6b581236", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311323556900}, "additional": {"logType": "detail", "children": [], "durationId": "bf37ae3c-b2d8-425d-aff5-cf556319fbaa"}}, {"head": {"id": "9b827760-9fec-4ffb-af22-b94d956b452d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311323878700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d7f08f-2261-42c5-83ab-c8af167874f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311323966200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fb02563-6670-4a43-9866-58ab98d67794", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311324843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb1512ea-6102-422f-a6f8-5942b35bf131", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311325548200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "410ffae8-6778-42bb-b2f7-03d7ab15f693", "name": "entry : default@ProcessProfile cost memory 0.05930328369140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311325646900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "136d8c59-d884-41ea-a99e-59735aa86b9c", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311324834700, "endTime": 32311325710800}, "additional": {"logType": "info", "children": [], "durationId": "bf37ae3c-b2d8-425d-aff5-cf556319fbaa"}}, {"head": {"id": "73bf689d-50ee-4469-a5c2-8fc177591a8d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311329220400, "endTime": 32311336120400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5be86d13-cfe1-4172-b151-18b50c2c3edb", "logId": "716391ad-4983-4a3b-806f-34309587f6a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5be86d13-cfe1-4172-b151-18b50c2c3edb", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311327134800}, "additional": {"logType": "detail", "children": [], "durationId": "73bf689d-50ee-4469-a5c2-8fc177591a8d"}}, {"head": {"id": "2cf00270-8db7-43ea-b34c-1821c2f78c7f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311327456600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f88426cc-8ab7-440b-a360-361844debd89", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311327547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d3e5dd1-0a13-4073-b431-7d774dd1fe42", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311329231400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ce14db-b2d5-4fae-90c9-c99c97e7e3b3", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311335926300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4852d8a6-32a6-4ecb-9ef3-9f521751ce8e", "name": "entry : default@ProcessRouterMap cost memory 0.20148468017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311336048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716391ad-4983-4a3b-806f-34309587f6a1", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311329220400, "endTime": 32311336120400}, "additional": {"logType": "info", "children": [], "durationId": "73bf689d-50ee-4469-a5c2-8fc177591a8d"}}, {"head": {"id": "1557d005-1c40-4726-bb92-a4851818d8ae", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340055100, "endTime": 32311341061800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "af2accd2-a3fb-4245-9448-ee783b0a8998", "logId": "bd9d8b6f-f5d5-4cc7-80da-f951f5858334"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af2accd2-a3fb-4245-9448-ee783b0a8998", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311338714200}, "additional": {"logType": "detail", "children": [], "durationId": "1557d005-1c40-4726-bb92-a4851818d8ae"}}, {"head": {"id": "54816761-a5bf-468b-a080-d3988dacd3c6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311339032800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30a7a65e-fb81-43ed-83fa-fce81a0467a4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311339387700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d51ebb54-f340-41de-abc2-71e476317966", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340063000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64d88e51-16d3-4d97-a5a6-e1fbc9365291", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340176100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0268bd8c-4ba1-4146-8b64-53cf519a2b7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340231200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d78886e-33ca-4288-9480-df4879a751a7", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340798800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55ad2e35-941c-4407-afc6-71b23c32879d", "name": "runTaskFromQueue task cost before running: 629 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340978200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd9d8b6f-f5d5-4cc7-80da-f951f5858334", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311340055100, "endTime": 32311341061800, "totalTime": 829400}, "additional": {"logType": "info", "children": [], "durationId": "1557d005-1c40-4726-bb92-a4851818d8ae"}}, {"head": {"id": "86b13c46-e7d7-4b7d-ae18-deb48d53de6d", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311345338700, "endTime": 32311351406000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "873f86ec-04cf-4f2d-b1d7-2796c7df1545", "logId": "936de6e8-1ddb-4481-bf36-546fd1de125a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "873f86ec-04cf-4f2d-b1d7-2796c7df1545", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311343010500}, "additional": {"logType": "detail", "children": [], "durationId": "86b13c46-e7d7-4b7d-ae18-deb48d53de6d"}}, {"head": {"id": "2cc47bff-3f9a-4c59-bee9-3e4996f7e638", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311343351000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22eab0b3-1864-462a-931f-6e90d081f317", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311343437700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488ba64f-d314-4ad4-a2f7-77ef0a325791", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311344381600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a9c5fb-fe06-4949-84aa-e958caa81e38", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311347550700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38394af-119d-4637-ba0f-a690174fd3d5", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311350003700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1c38dd-0b4a-4867-80f8-2f126122bfc5", "name": "entry : default@ProcessResource cost memory 0.17124176025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311350133000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "936de6e8-1ddb-4481-bf36-546fd1de125a", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311345338700, "endTime": 32311351406000}, "additional": {"logType": "info", "children": [], "durationId": "86b13c46-e7d7-4b7d-ae18-deb48d53de6d"}}, {"head": {"id": "2411c998-e353-4d5c-b69d-d017ad704fcd", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311359637200, "endTime": 32311371705900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "163cf762-5b5c-43e0-baeb-e77e5aa1b5c4", "logId": "7ff0a126-1e18-47df-9c8a-47c0c2fc8393"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "163cf762-5b5c-43e0-baeb-e77e5aa1b5c4", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311354786700}, "additional": {"logType": "detail", "children": [], "durationId": "2411c998-e353-4d5c-b69d-d017ad704fcd"}}, {"head": {"id": "fbdad4cd-012b-4e98-90a9-89d7954399f6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311355182200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77020312-fd53-4a9e-80b2-64038cce4d68", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311355286100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a1134f-758f-4218-b970-51312159776e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311359651400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8beae0f3-bccd-468f-bd3b-69de10aed9c7", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311371522300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc6e647a-fbbf-46d9-bf57-99b6d26967f2", "name": "entry : default@GenerateLoaderJson cost memory 0.7693023681640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311371643000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff0a126-1e18-47df-9c8a-47c0c2fc8393", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311359637200, "endTime": 32311371705900}, "additional": {"logType": "info", "children": [], "durationId": "2411c998-e353-4d5c-b69d-d017ad704fcd"}}, {"head": {"id": "18b0bbba-ecaf-4944-b642-9aeceff951e5", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311378262900, "endTime": 32311381341900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "bee8bda1-12c9-4501-8bac-84e69c587be0", "logId": "43b908b4-623e-4e06-9a5b-e37c91159432"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bee8bda1-12c9-4501-8bac-84e69c587be0", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311377291000}, "additional": {"logType": "detail", "children": [], "durationId": "18b0bbba-ecaf-4944-b642-9aeceff951e5"}}, {"head": {"id": "522efcac-9f04-4428-a076-a533186f7644", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311377598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c05f22-930a-4830-a2a3-5d776f00af8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311377685600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "914da9c1-c840-4902-a511-ab7e65feaa3d", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311378274200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ece283db-7709-4589-a357-1596885c41ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311380076600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d586315-248c-4f10-ac59-e8ca1794c24b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311380217500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11212f2-4d2d-4ae5-8c8d-9f692aeab983", "name": "entry : default@ProcessLibs cost memory 0.12564849853515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311381149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51cb4c54-9dc7-43f6-a7b5-ceeb1850d077", "name": "runTaskFromQueue task cost before running: 670 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311381281000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43b908b4-623e-4e06-9a5b-e37c91159432", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311378262900, "endTime": 32311381341900, "totalTime": 2991000}, "additional": {"logType": "info", "children": [], "durationId": "18b0bbba-ecaf-4944-b642-9aeceff951e5"}}, {"head": {"id": "1f4ba5bb-44a7-49f6-87ba-1d60e6ea1a0f", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311386428100, "endTime": 32311406109900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "235f941d-f877-4437-8d0d-a8c4304a3d0f", "logId": "32b1f6ed-b172-40ae-b999-1be6ef7f4144"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "235f941d-f877-4437-8d0d-a8c4304a3d0f", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311383411500}, "additional": {"logType": "detail", "children": [], "durationId": "1f4ba5bb-44a7-49f6-87ba-1d60e6ea1a0f"}}, {"head": {"id": "d6fef298-0525-4fb7-8fb1-a604e2778ccb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311383744200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92812f77-15fb-49f7-a8b3-bdc8a0010816", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311383830100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147ce283-641e-4ec8-9193-8c38316da43b", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311384444700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b78fdb0c-b5d4-44bb-9708-b65caf76efb2", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311386446600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "034dee26-3be4-41f0-b90d-0d27de761e7c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311405491900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ee9518-6e28-47d5-89b5-8ddf5e802398", "name": "entry : default@CompileResource cost memory 1.4075851440429688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311405920200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32b1f6ed-b172-40ae-b999-1be6ef7f4144", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311386428100, "endTime": 32311406109900}, "additional": {"logType": "info", "children": [], "durationId": "1f4ba5bb-44a7-49f6-87ba-1d60e6ea1a0f"}}, {"head": {"id": "1c531cb0-e957-4e3c-aa5f-107d797254a5", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311410891600, "endTime": 32311411928400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "057352cc-fcf3-46dc-b4c9-f3e3f0344ca9", "logId": "08ea65e0-460e-4dca-80a7-fa3afbcfa859"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "057352cc-fcf3-46dc-b4c9-f3e3f0344ca9", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311408611600}, "additional": {"logType": "detail", "children": [], "durationId": "1c531cb0-e957-4e3c-aa5f-107d797254a5"}}, {"head": {"id": "0b7f7fa2-066a-44b0-aa6b-5e648e07fb2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311408933400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44d245cd-2fcc-4935-b06d-c378ae573b0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311409015300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4258860b-de38-4768-ae3c-d1d0145af115", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311410899600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25c3d715-f94b-4870-9bbd-1893087bb69a", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311411108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d1520d8-db68-466d-8490-3d0f136e91da", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311411795300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "851ad1a4-ead3-4bf5-a180-63826f3098cd", "name": "entry : default@DoNativeStrip cost memory 0.0745697021484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311411871200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ea65e0-460e-4dca-80a7-fa3afbcfa859", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311410891600, "endTime": 32311411928400}, "additional": {"logType": "info", "children": [], "durationId": "1c531cb0-e957-4e3c-aa5f-107d797254a5"}}, {"head": {"id": "b62c3b7f-cfe6-41a4-969e-cd8be821d490", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311417347000, "endTime": 32313046763200}, "additional": {"children": ["a38f05e1-40e4-48c1-9674-f55227a4c0be", "3df0ddc1-9ff6-4f19-a759-a2212143402d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "b43b0185-6dfb-4389-9f79-f3f5c99e018a", "logId": "4c5a5c8f-ec93-4490-a088-7602eef3726a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b43b0185-6dfb-4389-9f79-f3f5c99e018a", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311413278400}, "additional": {"logType": "detail", "children": [], "durationId": "b62c3b7f-cfe6-41a4-969e-cd8be821d490"}}, {"head": {"id": "f94b43bd-d116-40f0-8d78-58989786c7b6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311413864300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f9201f9-b6be-4a42-8017-f7fa0e0525de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311413964000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b222ef6-2770-4ad9-a3e6-31c743ad5d84", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311417355700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91dc7aea-eaa8-4790-a8b3-e054e00e1629", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311427563500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e415a28d-5a2c-4b58-a7ef-4397fcd5c5d1", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311427688100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ced77878-cdf3-4bfd-9eb8-ea041d96d6ce", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311439214700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d96ed03-20ae-4575-877f-6cb01d6f318a", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311439839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "906b384c-bc5a-453b-81a8-0ed705709b97", "name": "default@CompileArkTS work[80] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311440908600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32311546405600, "endTime": 32313040614400}, "additional": {"children": ["1a93fffa-f872-4c1b-b3d9-3b012c31c3c8", "3d7b89b6-3f4d-46fe-96d2-e5d79d1e71e3", "2d639669-8bce-43a0-8a5f-1d5009534a9c", "ecaa895c-35a0-4244-bc66-34b637e4049c", "4188993a-eec6-4352-ac82-1c9aa93361e2", "67d287bd-5f8f-494e-bbeb-a466e991e0f8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b62c3b7f-cfe6-41a4-969e-cd8be821d490", "logId": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cfc54c4-097f-4637-b4f4-eaace25ec5be", "name": "default@CompileArkTS work[80] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311441769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "868633e8-a851-4960-aac5-a9a2ed49af03", "name": "default@CompileArkTS work[80] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311441881900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be1ef335-ae51-4d0b-aee6-430b46fa0476", "name": "CopyResources startTime: 32311441939700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311441941800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b34812-6814-4a2f-84af-22a3916c2ddb", "name": "default@CompileArkTS work[81] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311442016700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df0ddc1-9ff6-4f19-a759-a2212143402d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32312573442700, "endTime": 32312584995100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b62c3b7f-cfe6-41a4-969e-cd8be821d490", "logId": "c023914d-eb0c-4fee-905a-30b865b47f42"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b20dc793-80e8-4aa4-a604-b801202ae2d3", "name": "default@CompileArkTS work[81] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311442673000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be68fc41-5d59-4ec9-9843-f279c5929b0c", "name": "default@CompileArkTS work[81] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311442748400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69bbae1f-8f77-4426-9128-70a88d18cd51", "name": "entry : default@CompileArkTS cost memory 1.5870361328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311442826700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eb69199-48f1-4988-8a4e-c11ba5c242dc", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311448266700, "endTime": 32311451032600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0a4aeab7-0a69-41c6-a233-116210913d42", "logId": "be3a4418-377a-416e-a17d-5f7eaa114d05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a4aeab7-0a69-41c6-a233-116210913d42", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311444108000}, "additional": {"logType": "detail", "children": [], "durationId": "4eb69199-48f1-4988-8a4e-c11ba5c242dc"}}, {"head": {"id": "a056873b-6edd-484d-b180-e9d73f0d5c87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311444485700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118a05f0-d4c6-4d78-9611-00bda8dc07fe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311444575500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897ddbd2-8897-4f28-865a-d4b18fe1e241", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311448280700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eae30845-dffd-42f9-8180-5f94756ba74f", "name": "entry : default@BuildJS cost memory 0.12688446044921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311450839000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec680246-1ff5-49f4-9606-8f9b3ac12985", "name": "runTaskFromQueue task cost before running: 739 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311450968900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3a4418-377a-416e-a17d-5f7eaa114d05", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311448266700, "endTime": 32311451032600, "totalTime": 2680200}, "additional": {"logType": "info", "children": [], "durationId": "4eb69199-48f1-4988-8a4e-c11ba5c242dc"}}, {"head": {"id": "55c1b605-40e9-4898-9bdd-9ccec6e46415", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311455297700, "endTime": 32311456726000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "87517d22-33cf-489d-ad85-5d92e88e19ac", "logId": "595ede6f-f82d-4ed5-ae05-aedc4a6e673d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87517d22-33cf-489d-ad85-5d92e88e19ac", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311452788300}, "additional": {"logType": "detail", "children": [], "durationId": "55c1b605-40e9-4898-9bdd-9ccec6e46415"}}, {"head": {"id": "c1cf1548-cb5d-4141-bae1-26999a995296", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311453120100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89c45274-fa82-46ac-bd04-8fd3194c58ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311453214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea476b3a-96f4-4e54-9c61-f66cb29ec3a3", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311455306300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17023388-a99a-4b4e-ae32-8ce9018cf230", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311455584400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a748a10c-5628-49f9-bc4e-fa79ee6c307b", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311456540600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c619386-796f-4793-a301-ffc5ffd8980c", "name": "entry : default@CacheNativeLibs cost memory 0.08788299560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311456656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595ede6f-f82d-4ed5-ae05-aedc4a6e673d", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311455297700, "endTime": 32311456726000}, "additional": {"logType": "info", "children": [], "durationId": "55c1b605-40e9-4898-9bdd-9ccec6e46415"}}, {"head": {"id": "ad7ad2f5-4fc7-440a-af2d-a89d3789b89f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311545714300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a91481-ad2a-46d6-8753-26b8e0ab8c0a", "name": "default@CompileArkTS work[80] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311545990800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964e052c-8228-4494-b239-92c66c032ef3", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311546067700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef5d800-e041-473f-8a44-a49f222f3b79", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311546160800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fabd1fc-275f-4b69-bb10-0bb0ecbe3900", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311546274900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aba0595-b35e-42b3-843c-ec3dc15f5764", "name": "default@CompileArkTS work[81] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311548081700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7db34cd9-a3a9-4b12-9fe2-f545caf74525", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312596746900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88e6fff8-e5e3-4d5c-86c3-d96f890c5f20", "name": "CopyResources is end, endTime: 32312596959900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312596967000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21b975c-a83f-4907-b966-a94e194a2c12", "name": "default@CompileArkTS work[81] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312597069700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c023914d-eb0c-4fee-905a-30b865b47f42", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32312573442700, "endTime": 32312584995100}, "additional": {"logType": "info", "children": [], "durationId": "3df0ddc1-9ff6-4f19-a759-a2212143402d", "parent": "4c5a5c8f-ec93-4490-a088-7602eef3726a"}}, {"head": {"id": "deb0be1e-74fb-477b-9e9a-7b12913f6385", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312708272600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e65ab9c9-7f99-4f50-b960-988d90890b19", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313040858200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a93fffa-f872-4c1b-b3d9-3b012c31c3c8", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32311546594300, "endTime": 32311553243000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "logId": "db4783e6-2a97-499c-8e85-10b33c598234"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db4783e6-2a97-499c-8e85-10b33c598234", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311546594300, "endTime": 32311553243000}, "additional": {"logType": "info", "children": [], "durationId": "1a93fffa-f872-4c1b-b3d9-3b012c31c3c8", "parent": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}}, {"head": {"id": "3d7b89b6-3f4d-46fe-96d2-e5d79d1e71e3", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32311553265200, "endTime": 32311553528500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "logId": "e33ef3bc-a979-45cc-9a32-0cfcfca85c1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e33ef3bc-a979-45cc-9a32-0cfcfca85c1a", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311553265200, "endTime": 32311553528500}, "additional": {"logType": "info", "children": [], "durationId": "3d7b89b6-3f4d-46fe-96d2-e5d79d1e71e3", "parent": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}}, {"head": {"id": "2d639669-8bce-43a0-8a5f-1d5009534a9c", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32311553580100, "endTime": 32311553620300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "logId": "14438ad7-59a2-42f5-ac5d-089e9cca5999"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14438ad7-59a2-42f5-ac5d-089e9cca5999", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311553580100, "endTime": 32311553620300}, "additional": {"logType": "info", "children": [], "durationId": "2d639669-8bce-43a0-8a5f-1d5009534a9c", "parent": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}}, {"head": {"id": "ecaa895c-35a0-4244-bc66-34b637e4049c", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32311553640500, "endTime": 32312962188400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "logId": "5c620691-80b4-4840-a8a8-e7bfc4eb4e07"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c620691-80b4-4840-a8a8-e7bfc4eb4e07", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311553640500, "endTime": 32312962188400}, "additional": {"logType": "info", "children": [], "durationId": "ecaa895c-35a0-4244-bc66-34b637e4049c", "parent": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}}, {"head": {"id": "4188993a-eec6-4352-ac82-1c9aa93361e2", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32312962207200, "endTime": 32312970372000}, "additional": {"children": ["cf776515-54fb-4e23-9dc3-3158a00b2519", "a70b615e-c178-4dfc-89a8-96ab3ebbf6e5", "26bb2488-e242-4aa3-8057-c054f88df057"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "logId": "9fcb8b35-1a08-4971-a434-544a1a8f1c5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fcb8b35-1a08-4971-a434-544a1a8f1c5b", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312962207200, "endTime": 32312970372000}, "additional": {"logType": "info", "children": ["024f012c-9bca-4011-ba19-3465fe3fc3c4", "160cd6d4-1809-47e0-97c5-b6f3400374f6", "65d119d4-ac6f-4c42-8198-cf5e3c54c244"], "durationId": "4188993a-eec6-4352-ac82-1c9aa93361e2", "parent": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}}, {"head": {"id": "cf776515-54fb-4e23-9dc3-3158a00b2519", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32312962219700, "endTime": 32312962226300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4188993a-eec6-4352-ac82-1c9aa93361e2", "logId": "024f012c-9bca-4011-ba19-3465fe3fc3c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "024f012c-9bca-4011-ba19-3465fe3fc3c4", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312962219700, "endTime": 32312962226300}, "additional": {"logType": "info", "children": [], "durationId": "cf776515-54fb-4e23-9dc3-3158a00b2519", "parent": "9fcb8b35-1a08-4971-a434-544a1a8f1c5b"}}, {"head": {"id": "a70b615e-c178-4dfc-89a8-96ab3ebbf6e5", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32312962229300, "endTime": 32312967455800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4188993a-eec6-4352-ac82-1c9aa93361e2", "logId": "160cd6d4-1809-47e0-97c5-b6f3400374f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "160cd6d4-1809-47e0-97c5-b6f3400374f6", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312962229300, "endTime": 32312967455800}, "additional": {"logType": "info", "children": [], "durationId": "a70b615e-c178-4dfc-89a8-96ab3ebbf6e5", "parent": "9fcb8b35-1a08-4971-a434-544a1a8f1c5b"}}, {"head": {"id": "26bb2488-e242-4aa3-8057-c054f88df057", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32312967461500, "endTime": 32312970359100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4188993a-eec6-4352-ac82-1c9aa93361e2", "logId": "65d119d4-ac6f-4c42-8198-cf5e3c54c244"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65d119d4-ac6f-4c42-8198-cf5e3c54c244", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312967461500, "endTime": 32312970359100}, "additional": {"logType": "info", "children": [], "durationId": "26bb2488-e242-4aa3-8057-c054f88df057", "parent": "9fcb8b35-1a08-4971-a434-544a1a8f1c5b"}}, {"head": {"id": "67d287bd-5f8f-494e-bbeb-a466e991e0f8", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32312970388100, "endTime": 32313040474400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "logId": "208f21a8-dd2e-4c67-92ce-956c70e7729c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "208f21a8-dd2e-4c67-92ce-956c70e7729c", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32312970388100, "endTime": 32313040474400}, "additional": {"logType": "info", "children": [], "durationId": "67d287bd-5f8f-494e-bbeb-a466e991e0f8", "parent": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2"}}, {"head": {"id": "67433e3a-360d-426e-94bf-52b8aefc59ef", "name": "default@CompileArkTS work[80] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313046428400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9009ca7c-a0df-4d6e-82df-c7572b3db9e2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32311546405600, "endTime": 32313040614400}, "additional": {"logType": "info", "children": ["db4783e6-2a97-499c-8e85-10b33c598234", "e33ef3bc-a979-45cc-9a32-0cfcfca85c1a", "14438ad7-59a2-42f5-ac5d-089e9cca5999", "5c620691-80b4-4840-a8a8-e7bfc4eb4e07", "9fcb8b35-1a08-4971-a434-544a1a8f1c5b", "208f21a8-dd2e-4c67-92ce-956c70e7729c"], "durationId": "a38f05e1-40e4-48c1-9674-f55227a4c0be", "parent": "4c5a5c8f-ec93-4490-a088-7602eef3726a"}}, {"head": {"id": "22858eac-fa99-4718-9561-43f57c0ce461", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313046638100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5a5c8f-ec93-4490-a088-7602eef3726a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32311417347000, "endTime": 32313046763200, "totalTime": 1519742400}, "additional": {"logType": "info", "children": ["9009ca7c-a0df-4d6e-82df-c7572b3db9e2", "c023914d-eb0c-4fee-905a-30b865b47f42"], "durationId": "b62c3b7f-cfe6-41a4-969e-cd8be821d490"}}, {"head": {"id": "8ac02403-0843-4393-847c-9a3cd175514a", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313051615100, "endTime": 32313052719900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "ce473733-9f91-450f-96c2-5aea66cd722e", "logId": "de87998a-5bf7-4250-a059-a9e47625c941"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce473733-9f91-450f-96c2-5aea66cd722e", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313050371700}, "additional": {"logType": "detail", "children": [], "durationId": "8ac02403-0843-4393-847c-9a3cd175514a"}}, {"head": {"id": "e3962ada-5b8a-4cf3-b9b2-34ef60a3a2c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313050736600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89df6ed1-eb3a-4790-bb99-6e9eb9417a0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313050827600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df066a21-6640-4745-954a-3764e5841af4", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313051622700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a60300ec-549f-4458-b15b-e02dbe27ee34", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313051803700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dba7877-28de-4e41-9d8a-8c53eee62329", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313052526700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a495019a-149b-4e5f-800b-758cd89cee10", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07546234130859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313052633200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de87998a-5bf7-4250-a059-a9e47625c941", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313051615100, "endTime": 32313052719900}, "additional": {"logType": "info", "children": [], "durationId": "8ac02403-0843-4393-847c-9a3cd175514a"}}, {"head": {"id": "b3f7966e-a832-4180-a4aa-38c740eeee33", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313060627300, "endTime": 32313497247600}, "additional": {"children": ["38aa8b19-b84f-44aa-88ee-cd7e3a432169", "d8043a97-c87f-4f77-991b-507f6c5fc911", "a703efef-f5f6-479b-b6f3-68e1402d568a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "230b18b1-4ea9-4b4a-8f4f-fc8f4d422d4d", "logId": "fad12112-b17e-4257-86e6-ce4448ad9dd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "230b18b1-4ea9-4b4a-8f4f-fc8f4d422d4d", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313054489900}, "additional": {"logType": "detail", "children": [], "durationId": "b3f7966e-a832-4180-a4aa-38c740eeee33"}}, {"head": {"id": "64177944-561c-41ab-984e-5cb0c6ef3c88", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313054887200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83374c8e-d08f-4acd-af22-852d2d9d76b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313054975400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24177b71-0197-4b35-b6a3-5deb2073bccc", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313060637200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91652bc2-60dd-4aed-ae7d-14445747bf66", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313070806000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d2be7f9-1e68-46e5-9305-a7d832d869b1", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313070946600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dedab9ed-7e4e-40e7-b81d-db447f58489a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313071036300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b51740-2f50-4fcf-8ca2-3ee949e45b7d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313071091000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38aa8b19-b84f-44aa-88ee-cd7e3a432169", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313071762900, "endTime": 32313072947800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b3f7966e-a832-4180-a4aa-38c740eeee33", "logId": "974a48e5-f2d9-4f76-b3a4-ae4e8165fd9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc8bed22-09cc-4fc3-9b03-f06dec212ab0", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313072815200}, "additional": {"logType": "debug", "children": [], "durationId": "b3f7966e-a832-4180-a4aa-38c740eeee33"}}, {"head": {"id": "974a48e5-f2d9-4f76-b3a4-ae4e8165fd9c", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313071762900, "endTime": 32313072947800}, "additional": {"logType": "info", "children": [], "durationId": "38aa8b19-b84f-44aa-88ee-cd7e3a432169", "parent": "fad12112-b17e-4257-86e6-ce4448ad9dd8"}}, {"head": {"id": "d8043a97-c87f-4f77-991b-507f6c5fc911", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313073524500, "endTime": 32313075038000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b3f7966e-a832-4180-a4aa-38c740eeee33", "logId": "6e523553-86dd-438c-8f1c-e8dead85be66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb5f31a3-b86a-47dd-885c-e5191617812e", "name": "default@PackageHap work[82] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313074087500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a703efef-f5f6-479b-b6f3-68e1402d568a", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32313075003400, "endTime": 32313496300000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b3f7966e-a832-4180-a4aa-38c740eeee33", "logId": "0b30d8cb-261f-4d64-881f-a66f5b12469f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a49049e4-a22d-43aa-84cf-d02eb6d2359a", "name": "default@PackageHap work[82] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313074765100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf992350-abd4-4d42-9dc9-9087ce1297ca", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313074840600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c4603eb-22d2-44a8-896e-53ee8a8741b6", "name": "default@PackageHap work[82] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313074931900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16cc01ad-cb0c-4a84-9747-9348618e977f", "name": "default@PackageHap work[82] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313074989200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e523553-86dd-438c-8f1c-e8dead85be66", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313073524500, "endTime": 32313075038000}, "additional": {"logType": "info", "children": [], "durationId": "d8043a97-c87f-4f77-991b-507f6c5fc911", "parent": "fad12112-b17e-4257-86e6-ce4448ad9dd8"}}, {"head": {"id": "6c8aff17-a9a0-4bd2-8152-ce7c24871a5a", "name": "entry : default@PackageHap cost memory 1.3076324462890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313078392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e785f6ba-b419-471b-8693-e6becf1cd61c", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313496390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dba22e5f-8985-4b30-bf2a-49bedcdef926", "name": "default@PackageHap work[82] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313496677300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b30d8cb-261f-4d64-881f-a66f5b12469f", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32313075003400, "endTime": 32313496300000}, "additional": {"logType": "info", "children": [], "durationId": "a703efef-f5f6-479b-b6f3-68e1402d568a", "parent": "fad12112-b17e-4257-86e6-ce4448ad9dd8"}}, {"head": {"id": "f5d8a3d3-9bae-465c-b804-986c85fe9460", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313497035100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad12112-b17e-4257-86e6-ce4448ad9dd8", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313060627300, "endTime": 32313497247600, "totalTime": 435674700}, "additional": {"logType": "info", "children": ["974a48e5-f2d9-4f76-b3a4-ae4e8165fd9c", "6e523553-86dd-438c-8f1c-e8dead85be66", "0b30d8cb-261f-4d64-881f-a66f5b12469f"], "durationId": "b3f7966e-a832-4180-a4aa-38c740eeee33"}}, {"head": {"id": "61490943-692d-4c4e-b571-93d91ceea863", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313503348200, "endTime": 32313505404200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "f69401dc-018f-4956-9881-90e6d9ceb78a", "logId": "fd6d7169-b3e0-4f9d-af4c-10cf9b70dc24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f69401dc-018f-4956-9881-90e6d9ceb78a", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313500743000}, "additional": {"logType": "detail", "children": [], "durationId": "61490943-692d-4c4e-b571-93d91ceea863"}}, {"head": {"id": "a5588c17-8d5b-4107-92b8-c9cb2dd17dcd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313501167200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf68b9c3-4c73-4bcf-87e6-93a1a839905b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313501270200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9369e662-4508-4e18-8822-1925590e507c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313503359100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d28cab3-ce9a-4c1b-b133-3bf66f76faf8", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313503731800}, "additional": {"logType": "warn", "children": [], "durationId": "61490943-692d-4c4e-b571-93d91ceea863"}}, {"head": {"id": "4a4cf739-f7ee-4a02-88ee-a0413262c57b", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313504527600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cead7778-a946-4641-8e2b-99959a1cbebc", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313504822400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f2422fb-5461-47c8-a850-8f0ac0079e5d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313504925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d2fe7c2-266d-4d14-af15-1e98bb8df6f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313504986500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "519dfe65-d088-471c-8cd6-e360bd3ccd41", "name": "entry : default@SignHap cost memory 0.1169281005859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313505245300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a225e432-7226-4372-9ec6-d25a0bcd7775", "name": "runTaskFromQueue task cost before running: 2 s 794 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313505339000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd6d7169-b3e0-4f9d-af4c-10cf9b70dc24", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313503348200, "endTime": 32313505404200, "totalTime": 1971600}, "additional": {"logType": "info", "children": [], "durationId": "61490943-692d-4c4e-b571-93d91ceea863"}}, {"head": {"id": "14cc429e-4e0a-4aac-bf77-2d0972aba9bb", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313508508700, "endTime": 32313513891900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e171e97a-8ed9-4612-8c9e-d9edfdb2aabf", "logId": "d9e4c5a2-d3c5-42ca-853d-ac86b23a745c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e171e97a-8ed9-4612-8c9e-d9edfdb2aabf", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313507258200}, "additional": {"logType": "detail", "children": [], "durationId": "14cc429e-4e0a-4aac-bf77-2d0972aba9bb"}}, {"head": {"id": "5286de78-6401-4c63-87cf-bf3eb62bed50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313507650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95ebced-f50f-45f3-8726-a1f985c1211f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313507759900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d0d35a-c5a3-487d-b94b-0cf5a5dd7231", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313508517800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b36296f-642e-4600-bd79-7c06922e7e60", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313513221500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59067368-dbc0-4ea1-b730-40757b7eef29", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313513401800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c29b7fe8-1a98-4539-896e-22d983db37b4", "name": "entry : default@CollectDebugSymbol cost memory 0.2393646240234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313513509500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b716ef3d-f059-4946-8d9e-fa7992afc6ee", "name": "runTaskFromQueue task cost before running: 2 s 802 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313513603600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e4c5a2-d3c5-42ca-853d-ac86b23a745c", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313508508700, "endTime": 32313513891900, "totalTime": 5069900}, "additional": {"logType": "info", "children": [], "durationId": "14cc429e-4e0a-4aac-bf77-2d0972aba9bb"}}, {"head": {"id": "fdc90bcf-6005-4aba-9662-dca79ff6bc61", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313516005200, "endTime": 32313516283700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "4f10d88f-74c8-4884-9205-9904ec9e79fc", "logId": "49a68253-834a-4a54-b4ed-6f6192d6fe8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f10d88f-74c8-4884-9205-9904ec9e79fc", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313515951500}, "additional": {"logType": "detail", "children": [], "durationId": "fdc90bcf-6005-4aba-9662-dca79ff6bc61"}}, {"head": {"id": "4b51f247-6ad6-4d59-8f72-18de936104d5", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313516011800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae715f72-78c3-478b-9a02-7de2342a6308", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313516148200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a68d67a-ffb5-49f0-8b3a-7eb01d3d5dfd", "name": "runTaskFromQueue task cost before running: 2 s 804 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313516226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a68253-834a-4a54-b4ed-6f6192d6fe8a", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313516005200, "endTime": 32313516283700, "totalTime": 202400}, "additional": {"logType": "info", "children": [], "durationId": "fdc90bcf-6005-4aba-9662-dca79ff6bc61"}}, {"head": {"id": "a639e516-bcbf-45cf-8db2-c8d97bc25bfd", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313523964500, "endTime": 32313523986600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb96eef9-9ec1-4e8b-ac20-318657e4d81b", "logId": "83985e49-67a9-441e-8ab1-c782c6828b2c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83985e49-67a9-441e-8ab1-c782c6828b2c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313523964500, "endTime": 32313523986600}, "additional": {"logType": "info", "children": [], "durationId": "a639e516-bcbf-45cf-8db2-c8d97bc25bfd"}}, {"head": {"id": "1c6079dc-2d48-4b25-aa02-6df002002536", "name": "BUILD SUCCESSFUL in 2 s 812 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524023600}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "81e0d16b-91e8-4baf-9b83-f842bba26843", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32310712239500, "endTime": 32313524333200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 28}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "a96623ae-c6ed-43b7-bb1e-c9aaf654d96a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524367500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52c7972a-dafc-4b11-a342-b47b30dc8352", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524434400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ddba0a6-6ce9-4893-a0e5-029c9aca15a7", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa94471-590d-4dce-b2bb-4d3023591871", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed96317-eec1-458a-9c44-d9274ae50fbc", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524614900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd98dc6-f96e-4471-b133-6a96a11ea6be", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313524923600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a95014e0-afda-4628-aa1b-b2bbce542bed", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313525511600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd77a655-ee57-43cf-b7b5-2396550f6e11", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313525742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b16ce64-5afc-4700-b379-01c4d18220ce", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313525811300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d76c4b4-9a77-452d-914e-e8d7f5ed3179", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313525875400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "076bf85f-502d-483d-9d4e-8b2bb986db62", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313526144300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11bab2c0-d739-4f7c-967c-f408ba9f8317", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313527122400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e707c98-ebdd-42ea-99a4-ab7113bc4e2a", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313527457000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66574a7b-6527-49cc-a490-998d82191276", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313527539400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e6fc09d-9047-4fd7-92d4-c61a427a0297", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313527597300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a5ea0a-fd20-4624-8482-34103c4fbce3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313527698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a586847e-8b56-4867-9583-d69be3217369", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313527763200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4590f027-477e-49ae-a824-3accef35b14d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313528070300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d631925-e251-459b-ab42-718a652e110e", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313528320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c9253a-8270-4da5-8f81-92a47d5af482", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313528513900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfb2db0d-1a22-426f-ab47-2dbaa1a85757", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313528785200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c7fac40-4685-4d3c-bb42-a392a640da0e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313528849300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25bdcf89-5c66-4ff6-beda-4b165e9b9039", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313528902200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c305b5d-d476-4f33-80f6-8ef0848eac4f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313532788700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb9ab7eb-eaab-4ffe-89c8-01cf059d1af5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313533579300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a32ef2c-e8b9-427b-a3af-e5bdc4c3014d", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313534377800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712eb5b7-847a-4287-b8ab-9fae4879e0b5", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313534612700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9670bbd2-67a6-46fc-a8b8-941b7a686e95", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313534795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b6a0bb0-649e-48eb-b4c6-e12ac14a9457", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313535233400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b51aedeb-202c-490a-97c4-d2f18cf36728", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313535305200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f429a3-6d9c-4f9a-9196-5596a95ff4cc", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313535463400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074362e7-2ae2-4378-8baa-be094e0f2c6a", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313535679600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ae5946d-9013-45ff-90f3-13f6379a5fd7", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313536256500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a23b333-7cee-41db-a5a1-a574f3e3b613", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313537259100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8092801-0cbb-4d2d-b9be-b54535e21467", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313537816800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64567eed-dc3f-4bc5-9ff3-b63401147b2d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313538761400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9023cd-55b1-4c1f-8c87-53efa9af4387", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313538969500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d0b0593-cf0f-492c-84b8-b2794094e09a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313539331500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2be6f2a4-31b7-45b8-aafc-dace314419b2", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313539897500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ed0c82-2a58-495e-8046-5df901e2dd7d", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313540140800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0b5b90-2134-4398-b1a1-10a5c882c572", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313540216500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a12bf6-05ce-4b05-a283-81d5d68fadad", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313540269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e029c97d-3078-4c51-a0ab-cf1b96edda7a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313541125000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3111fd86-cda3-4d20-bc75-bd650d6bb101", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313541386500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0b691a0-62a9-4f46-a3ea-8b8cb1725675", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313541590400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df62dc00-411a-4b1f-8b24-d45f19635f12", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313549963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6c71e6d-a77d-48eb-9cdc-c7d13e5a02e7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313550213800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8b64ff-fa18-48f4-b867-54e4adaab4e1", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313550417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccf2ff77-f177-4514-8bfd-d5a2014a31a9", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313550488000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4862e12e-b8fc-42e6-9054-84f644451ba5", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313550669000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f1e4cc-dbed-4ca5-935c-f7aec6709efb", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313551344300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75f050c-a56b-44bd-93ea-5a5e8b5b29b5", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313551581900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac4103a-485b-4ad2-9f20-70a1c7986f9f", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313551778600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18b3f6b-22de-4c06-9cc8-cefac06fe0f3", "name": "Incremental task entry:default@PackageHap post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313552010700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "991ba597-a707-41b8-b57e-184b4c7081c7", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313552167200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a36f9105-0d82-47b3-b9ab-f2c124185952", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313552241700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb8275d-ae79-41cb-95cf-9e34eb52df8d", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313552427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc7b489-609d-4d1c-b049-4529a25cf81d", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313554599000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b848e6c4-1fb2-4d93-8d65-fc751a9f584c", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313554881800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89749eba-99c1-4401-a867-6ece97c87174", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313555317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f7658e3-c3e6-4d7d-ad14-92015f44ef1d", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32313555568900}, "additional": {"logType": "debug", "children": []}}], "workLog": []}