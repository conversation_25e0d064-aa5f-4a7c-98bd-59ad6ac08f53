{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "c6497569-dbdd-477d-a791-e9220a32576a", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982316990300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f96773d-7ba6-463b-9de0-a3aac03c28a6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982322206600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b77c5341-7658-4057-8800-85f1451f4440", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982322487600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18d0a58-f9b9-4bc8-8eb1-16198df13537", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982326842500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce5ea99b-e887-423f-bd27-5d6dfdd74b88", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184925110500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a74c31-6536-4808-87f1-fa58d06089e9", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184934755400, "endTime": 28185400369100}, "additional": {"children": ["8e474f7d-b577-4491-8d2f-544546e9f6ec", "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "5062106b-bad3-4ffd-9795-dca658691363", "ade0361f-c377-4ef5-9bb9-b473061407b6", "d797ad9f-0847-48d2-b5da-7452308b1dc7", "b616316d-d979-4fbe-8ac0-c5a34ca59c7e", "fe03ecf9-d8c9-4a40-aa68-cdec8d947cb0"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "6e16c905-eccd-4f22-81e2-1190e761f8db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e474f7d-b577-4491-8d2f-544546e9f6ec", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184934757300, "endTime": 28184954585300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "d8ca72f9-6603-4b7c-b83d-2043bab94515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184954607600, "endTime": 28185398606400}, "additional": {"children": ["8bd83d9b-7355-4144-b47f-494745c4e4a8", "36dda40b-be8d-462d-9315-c01282d1d995", "215491d6-e160-431e-be00-4e5794871f7c", "4034d1cf-7ef4-49bc-bff4-3d60f1b600fe", "c6f0f5d4-786a-4b59-a5e5-a08c1dc97b0e", "cc4eccde-b999-4339-b4f7-3467656beded", "73057775-73f6-4f59-9547-3acd51d202d4", "da6a5566-fce8-46c6-a660-662d2a669cf7", "57463c0a-5e99-457a-841c-c4412f47ed4b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5062106b-bad3-4ffd-9795-dca658691363", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185398633200, "endTime": 28185400356300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "86728200-33ef-4411-95b9-a0c2355b958b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ade0361f-c377-4ef5-9bb9-b473061407b6", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185400360700, "endTime": 28185400361900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "65b14362-5ffd-4913-8b90-19ad3091c8d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d797ad9f-0847-48d2-b5da-7452308b1dc7", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184941142500, "endTime": 28184941188400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "047d3441-7b1a-46ef-a72e-72d91c4cf923"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "047d3441-7b1a-46ef-a72e-72d91c4cf923", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184941142500, "endTime": 28184941188400}, "additional": {"logType": "info", "children": [], "durationId": "d797ad9f-0847-48d2-b5da-7452308b1dc7", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "b616316d-d979-4fbe-8ac0-c5a34ca59c7e", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184946878600, "endTime": 28184946898800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "12306e8d-bd8b-412d-aeec-66bd43cf841d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12306e8d-bd8b-412d-aeec-66bd43cf841d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184946878600, "endTime": 28184946898800}, "additional": {"logType": "info", "children": [], "durationId": "b616316d-d979-4fbe-8ac0-c5a34ca59c7e", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "088525c5-9a03-4c1c-b1ed-cc05c9de7300", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184947215100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f4829dd-5717-4a6d-bb56-558452cbb0d2", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184954282900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ca72f9-6603-4b7c-b83d-2043bab94515", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184934757300, "endTime": 28184954585300}, "additional": {"logType": "info", "children": [], "durationId": "8e474f7d-b577-4491-8d2f-544546e9f6ec", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "8bd83d9b-7355-4144-b47f-494745c4e4a8", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184961792400, "endTime": 28184961803700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "0dffd496-ea8c-4046-84c6-9ff64fe3dce5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36dda40b-be8d-462d-9315-c01282d1d995", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184961879800, "endTime": 28184970357500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "834cab0a-1304-4fa1-a5cc-e0d2bbc7eca0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "215491d6-e160-431e-be00-4e5794871f7c", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184970373700, "endTime": 28185136669000}, "additional": {"children": ["c180a21c-4139-4b91-b66d-a7b2d7d4b0f1", "47a5bd56-fcf0-4f37-8ca1-b278c0c57f67", "436dd08e-c5bb-4bed-8afe-c88b1d4e8ecd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "fb6639b9-b1cb-49a5-af9b-ec999ee2a4bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4034d1cf-7ef4-49bc-bff4-3d60f1b600fe", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185136682900, "endTime": 28185196893800}, "additional": {"children": ["42f6f5b9-8626-467c-bd67-5b7bcad3160c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "f6ca0154-f822-4058-b3f5-11965b601f4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6f0f5d4-786a-4b59-a5e5-a08c1dc97b0e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185197018000, "endTime": 28185359658300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "2eba7d38-1813-4702-83d2-c9d2a3b3e033"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc4eccde-b999-4339-b4f7-3467656beded", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185361106900, "endTime": 28185381663500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "e3bb4e16-87a2-4faa-9107-1826adb76a14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73057775-73f6-4f59-9547-3acd51d202d4", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185381701000, "endTime": 28185398045400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "14c07d7b-c64d-42dc-92d6-055f3438c9bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da6a5566-fce8-46c6-a660-662d2a669cf7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185398070900, "endTime": 28185398588100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "031e5479-3c28-4537-bad5-d3b209816386"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dffd496-ea8c-4046-84c6-9ff64fe3dce5", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184961792400, "endTime": 28184961803700}, "additional": {"logType": "info", "children": [], "durationId": "8bd83d9b-7355-4144-b47f-494745c4e4a8", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "834cab0a-1304-4fa1-a5cc-e0d2bbc7eca0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184961879800, "endTime": 28184970357500}, "additional": {"logType": "info", "children": [], "durationId": "36dda40b-be8d-462d-9315-c01282d1d995", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "c180a21c-4139-4b91-b66d-a7b2d7d4b0f1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184972444100, "endTime": 28184972468300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "215491d6-e160-431e-be00-4e5794871f7c", "logId": "23c5d009-20c8-4dd0-af43-6bccac61bf65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23c5d009-20c8-4dd0-af43-6bccac61bf65", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184972444100, "endTime": 28184972468300}, "additional": {"logType": "info", "children": [], "durationId": "c180a21c-4139-4b91-b66d-a7b2d7d4b0f1", "parent": "fb6639b9-b1cb-49a5-af9b-ec999ee2a4bf"}}, {"head": {"id": "47a5bd56-fcf0-4f37-8ca1-b278c0c57f67", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184974949800, "endTime": 28185135878300}, "additional": {"children": ["98837b28-1bde-4357-9813-9aaa88072e91", "84b0009e-1678-497d-bf7f-a21f376febb1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "215491d6-e160-431e-be00-4e5794871f7c", "logId": "b75a631c-3e23-4d48-9d87-d689c4cd9918"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98837b28-1bde-4357-9813-9aaa88072e91", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184974951600, "endTime": 28185001101400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47a5bd56-fcf0-4f37-8ca1-b278c0c57f67", "logId": "64ac1d9f-dff8-4289-aeb8-723493352049"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84b0009e-1678-497d-bf7f-a21f376febb1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185001117500, "endTime": 28185135864700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "47a5bd56-fcf0-4f37-8ca1-b278c0c57f67", "logId": "06330ca3-ed7d-4be9-acd7-67d188b5a06c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "976e4a4f-a514-4ef6-9465-872be9e5f0d6", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184974956500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0ba93c-d4dc-42ef-abda-a9f411496aa8", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185000961200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ac1d9f-dff8-4289-aeb8-723493352049", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184974951600, "endTime": 28185001101400}, "additional": {"logType": "info", "children": [], "durationId": "98837b28-1bde-4357-9813-9aaa88072e91", "parent": "b75a631c-3e23-4d48-9d87-d689c4cd9918"}}, {"head": {"id": "e9d243cd-f2aa-4a4f-a465-85fe2beef0fd", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185001151000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0923cf-20a5-4f0e-8ade-6d78849acddb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185028210800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f287baf1-94ce-45bf-bb87-4e04c3050864", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185028473300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56291c7b-77c2-4018-ac19-381009d25877", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185028670700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd2f5e0-e800-47ed-93d4-257b40335818", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185029141200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa38189-a3ce-4afe-8490-528d0521feed", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185033605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7810c44c-f083-4371-8769-d74e01e9aa7e", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185045297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78431c7c-7dda-4088-8a63-b2320df3dbed", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185071600900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "203fcbce-0c70-4bea-8218-072face62b8f", "name": "Sdk init in 63 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185109355200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53685dbd-376d-40b8-90cf-2d3c71b56c94", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185109564200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 19}, "markType": "other"}}, {"head": {"id": "75dcf3d0-217e-4529-8ae2-f75081801cf4", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185109620100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 19}, "markType": "other"}}, {"head": {"id": "d73ca8d5-c840-44f7-9a3f-08dad87982b1", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185135561100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de86e40-b90a-4a80-917a-3554bef8780f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185135682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1db8ee1-3f96-432a-acd5-faaa6d8b443d", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185135739200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f8e7e4-9091-482b-b69f-e677e60385b4", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185135791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06330ca3-ed7d-4be9-acd7-67d188b5a06c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185001117500, "endTime": 28185135864700}, "additional": {"logType": "info", "children": [], "durationId": "84b0009e-1678-497d-bf7f-a21f376febb1", "parent": "b75a631c-3e23-4d48-9d87-d689c4cd9918"}}, {"head": {"id": "b75a631c-3e23-4d48-9d87-d689c4cd9918", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184974949800, "endTime": 28185135878300}, "additional": {"logType": "info", "children": ["64ac1d9f-dff8-4289-aeb8-723493352049", "06330ca3-ed7d-4be9-acd7-67d188b5a06c"], "durationId": "47a5bd56-fcf0-4f37-8ca1-b278c0c57f67", "parent": "fb6639b9-b1cb-49a5-af9b-ec999ee2a4bf"}}, {"head": {"id": "436dd08e-c5bb-4bed-8afe-c88b1d4e8ecd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185136640400, "endTime": 28185136656300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "215491d6-e160-431e-be00-4e5794871f7c", "logId": "8008f8ad-6669-4dab-89d5-ec6e41203214"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8008f8ad-6669-4dab-89d5-ec6e41203214", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185136640400, "endTime": 28185136656300}, "additional": {"logType": "info", "children": [], "durationId": "436dd08e-c5bb-4bed-8afe-c88b1d4e8ecd", "parent": "fb6639b9-b1cb-49a5-af9b-ec999ee2a4bf"}}, {"head": {"id": "fb6639b9-b1cb-49a5-af9b-ec999ee2a4bf", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184970373700, "endTime": 28185136669000}, "additional": {"logType": "info", "children": ["23c5d009-20c8-4dd0-af43-6bccac61bf65", "b75a631c-3e23-4d48-9d87-d689c4cd9918", "8008f8ad-6669-4dab-89d5-ec6e41203214"], "durationId": "215491d6-e160-431e-be00-4e5794871f7c", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "42f6f5b9-8626-467c-bd67-5b7bcad3160c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185137392800, "endTime": 28185196879700}, "additional": {"children": ["30790705-921b-4032-8ef3-d576da940f01", "fda82be0-8f56-4696-99f0-05365d594af7", "31b95a04-41de-4938-898b-23e15cd6f1d6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4034d1cf-7ef4-49bc-bff4-3d60f1b600fe", "logId": "89968967-bd09-4000-8ad8-8b8518439f28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30790705-921b-4032-8ef3-d576da940f01", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185141873700, "endTime": 28185141896000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42f6f5b9-8626-467c-bd67-5b7bcad3160c", "logId": "bbb53da6-39d6-4238-a1f7-861dea980aa2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbb53da6-39d6-4238-a1f7-861dea980aa2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185141873700, "endTime": 28185141896000}, "additional": {"logType": "info", "children": [], "durationId": "30790705-921b-4032-8ef3-d576da940f01", "parent": "89968967-bd09-4000-8ad8-8b8518439f28"}}, {"head": {"id": "fda82be0-8f56-4696-99f0-05365d594af7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185145709200, "endTime": 28185194157100}, "additional": {"children": ["eec042ac-8172-487f-aaee-2d757cd93f3d", "865a9e41-75bf-4e4f-8c14-484fe4733761"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42f6f5b9-8626-467c-bd67-5b7bcad3160c", "logId": "3813d024-d33c-45d3-b1dd-a60923d147d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eec042ac-8172-487f-aaee-2d757cd93f3d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185145711600, "endTime": 28185151396700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda82be0-8f56-4696-99f0-05365d594af7", "logId": "9ca19f41-da19-49f9-9afe-58a00dc23703"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "865a9e41-75bf-4e4f-8c14-484fe4733761", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185151411600, "endTime": 28185194142700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fda82be0-8f56-4696-99f0-05365d594af7", "logId": "95d32f00-0040-4e40-9c43-69808449bec9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52cedb40-b34e-4438-9467-4ca09713794c", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185145717200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b605dd-0142-46d8-8d35-50c05517a553", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185151261600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ca19f41-da19-49f9-9afe-58a00dc23703", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185145711600, "endTime": 28185151396700}, "additional": {"logType": "info", "children": [], "durationId": "eec042ac-8172-487f-aaee-2d757cd93f3d", "parent": "3813d024-d33c-45d3-b1dd-a60923d147d6"}}, {"head": {"id": "d5716e5b-8ffa-4df1-8433-570fc8339537", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185151419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e53166a-f2dd-41fc-97e6-b8a088f92f83", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185178063100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3218da27-0863-4053-a3a7-48baeed02b19", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185179430900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7b6d2e-17e2-4205-b689-2b707fc4993c", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185179857400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd6fb534-cb5b-4f66-8163-ad0a567eeea9", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185180043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b318ae8c-0f91-4b6d-a5c3-20a78d04b505", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185180113500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdd6d898-aba3-4ec7-9682-945862718606", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185180167300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a5e8d60-1479-410f-b0eb-bf7896629f77", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185180622600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47a1c29-b9c5-4de5-b03b-d33c6d132c64", "name": "Module entry task initialization takes 8 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185193634300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2654d9b4-4bac-4f49-a01e-3d5e751184d4", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185193861700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac00b08-e9d6-48ae-af41-425db23cf414", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185193972300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "242169ab-fe92-4477-bf5f-0be65bd53baf", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185194072200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d32f00-0040-4e40-9c43-69808449bec9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185151411600, "endTime": 28185194142700}, "additional": {"logType": "info", "children": [], "durationId": "865a9e41-75bf-4e4f-8c14-484fe4733761", "parent": "3813d024-d33c-45d3-b1dd-a60923d147d6"}}, {"head": {"id": "3813d024-d33c-45d3-b1dd-a60923d147d6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185145709200, "endTime": 28185194157100}, "additional": {"logType": "info", "children": ["9ca19f41-da19-49f9-9afe-58a00dc23703", "95d32f00-0040-4e40-9c43-69808449bec9"], "durationId": "fda82be0-8f56-4696-99f0-05365d594af7", "parent": "89968967-bd09-4000-8ad8-8b8518439f28"}}, {"head": {"id": "31b95a04-41de-4938-898b-23e15cd6f1d6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185196841400, "endTime": 28185196863400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "42f6f5b9-8626-467c-bd67-5b7bcad3160c", "logId": "c31ece52-114b-4fda-a4b1-013ae16de2d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c31ece52-114b-4fda-a4b1-013ae16de2d6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185196841400, "endTime": 28185196863400}, "additional": {"logType": "info", "children": [], "durationId": "31b95a04-41de-4938-898b-23e15cd6f1d6", "parent": "89968967-bd09-4000-8ad8-8b8518439f28"}}, {"head": {"id": "89968967-bd09-4000-8ad8-8b8518439f28", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185137392800, "endTime": 28185196879700}, "additional": {"logType": "info", "children": ["bbb53da6-39d6-4238-a1f7-861dea980aa2", "3813d024-d33c-45d3-b1dd-a60923d147d6", "c31ece52-114b-4fda-a4b1-013ae16de2d6"], "durationId": "42f6f5b9-8626-467c-bd67-5b7bcad3160c", "parent": "f6ca0154-f822-4058-b3f5-11965b601f4d"}}, {"head": {"id": "f6ca0154-f822-4058-b3f5-11965b601f4d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185136682900, "endTime": 28185196893800}, "additional": {"logType": "info", "children": ["89968967-bd09-4000-8ad8-8b8518439f28"], "durationId": "4034d1cf-7ef4-49bc-bff4-3d60f1b600fe", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "d45f18c4-fff1-4eaa-b693-ab1376cd5b6e", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185228363800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d734bea-2886-4829-9f7f-3d51d732b115", "name": "hvigorfile, resolve hvigorfile dependencies in 163 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185359500700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eba7d38-1813-4702-83d2-c9d2a3b3e033", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185197018000, "endTime": 28185359658300}, "additional": {"logType": "info", "children": [], "durationId": "c6f0f5d4-786a-4b59-a5e5-a08c1dc97b0e", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "57463c0a-5e99-457a-841c-c4412f47ed4b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185360520300, "endTime": 28185361085600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "logId": "02155b56-dadb-4ef5-9ba0-a4c06be299b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c9660ab-03e1-46e2-bae0-e03a351d5af3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185360647500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02155b56-dadb-4ef5-9ba0-a4c06be299b6", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185360520300, "endTime": 28185361085600}, "additional": {"logType": "info", "children": [], "durationId": "57463c0a-5e99-457a-841c-c4412f47ed4b", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "32f25e20-ce11-4792-8f38-7638a0bdcbb3", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185366315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58de5d80-94d6-499a-8947-39bc597ecb88", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185379921100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3bb4e16-87a2-4faa-9107-1826adb76a14", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185361106900, "endTime": 28185381663500}, "additional": {"logType": "info", "children": [], "durationId": "cc4eccde-b999-4339-b4f7-3467656beded", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "78208e10-f98c-465d-80a2-03b9e1de3733", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185387135500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6d6910-4cbb-43d7-807f-09cfef875820", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185387713900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49f3f782-5db8-4c5a-93ba-f779894075de", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185391413800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "209bebc9-ff80-47c2-b167-2cc3b03cab38", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185391741600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c07d7b-c64d-42dc-92d6-055f3438c9bd", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185381701000, "endTime": 28185398045400}, "additional": {"logType": "info", "children": [], "durationId": "73057775-73f6-4f59-9547-3acd51d202d4", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "a7c90f28-d6c5-46ef-b545-a19d13e509ec", "name": "Configuration phase cost:437 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185398104600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "031e5479-3c28-4537-bad5-d3b209816386", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185398070900, "endTime": 28185398588100}, "additional": {"logType": "info", "children": [], "durationId": "da6a5566-fce8-46c6-a660-662d2a669cf7", "parent": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088"}}, {"head": {"id": "f9fe4211-1774-4be7-bcc5-f2e47dd6b088", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184954607600, "endTime": 28185398606400}, "additional": {"logType": "info", "children": ["0dffd496-ea8c-4046-84c6-9ff64fe3dce5", "834cab0a-1304-4fa1-a5cc-e0d2bbc7eca0", "fb6639b9-b1cb-49a5-af9b-ec999ee2a4bf", "f6ca0154-f822-4058-b3f5-11965b601f4d", "2eba7d38-1813-4702-83d2-c9d2a3b3e033", "e3bb4e16-87a2-4faa-9107-1826adb76a14", "14c07d7b-c64d-42dc-92d6-055f3438c9bd", "031e5479-3c28-4537-bad5-d3b209816386", "02155b56-dadb-4ef5-9ba0-a4c06be299b6"], "durationId": "c6f1f766-1f21-4e83-bdae-6b9a4c71c531", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "fe03ecf9-d8c9-4a40-aa68-cdec8d947cb0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185400327600, "endTime": 28185400345200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a74c31-6536-4808-87f1-fa58d06089e9", "logId": "013e768e-9dc9-4e91-a0ae-6e3b931de99c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "013e768e-9dc9-4e91-a0ae-6e3b931de99c", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185400327600, "endTime": 28185400345200}, "additional": {"logType": "info", "children": [], "durationId": "fe03ecf9-d8c9-4a40-aa68-cdec8d947cb0", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "86728200-33ef-4411-95b9-a0c2355b958b", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185398633200, "endTime": 28185400356300}, "additional": {"logType": "info", "children": [], "durationId": "5062106b-bad3-4ffd-9795-dca658691363", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "65b14362-5ffd-4913-8b90-19ad3091c8d1", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185400360700, "endTime": 28185400361900}, "additional": {"logType": "info", "children": [], "durationId": "ade0361f-c377-4ef5-9bb9-b473061407b6", "parent": "6e16c905-eccd-4f22-81e2-1190e761f8db"}}, {"head": {"id": "6e16c905-eccd-4f22-81e2-1190e761f8db", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184934755400, "endTime": 28185400369100}, "additional": {"logType": "info", "children": ["d8ca72f9-6603-4b7c-b83d-2043bab94515", "f9fe4211-1774-4be7-bcc5-f2e47dd6b088", "86728200-33ef-4411-95b9-a0c2355b958b", "65b14362-5ffd-4913-8b90-19ad3091c8d1", "047d3441-7b1a-46ef-a72e-72d91c4cf923", "12306e8d-bd8b-412d-aeec-66bd43cf841d", "013e768e-9dc9-4e91-a0ae-6e3b931de99c"], "durationId": "e7a74c31-6536-4808-87f1-fa58d06089e9"}}, {"head": {"id": "c72cf10b-9a9e-42ad-9439-52fe776fbef0", "name": "Configuration task cost before running: 486 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185416323400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cbb490e-10eb-49f3-9d32-0078daca5f3e", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185423406100, "endTime": 28185435891000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "7856bc17-0de5-40c9-bbcf-8a59ae11a2ee", "logId": "bbe544dd-2a88-4c42-952b-334f8a32a25c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7856bc17-0de5-40c9-bbcf-8a59ae11a2ee", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185418920900}, "additional": {"logType": "detail", "children": [], "durationId": "7cbb490e-10eb-49f3-9d32-0078daca5f3e"}}, {"head": {"id": "5ee13cbc-a075-4892-812f-c93a2ecef675", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185419269900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0dd9084-d572-48a4-b8c4-79e2484b9b53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185419423800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82129540-cf14-4858-8c1e-c23d930e772e", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185423428000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4922f79b-02a1-4898-8d9e-40d4e29d2acb", "name": "Incremental task entry:default@PreBuild pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185435666100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d571b45b-1050-49bd-b785-baaa6fd7d4ea", "name": "entry : default@PreBuild cost memory 0.31740570068359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185435815300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe544dd-2a88-4c42-952b-334f8a32a25c", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185423406100, "endTime": 28185435891000}, "additional": {"logType": "info", "children": [], "durationId": "7cbb490e-10eb-49f3-9d32-0078daca5f3e"}}, {"head": {"id": "aae18a80-1ecc-470c-b0a8-a343bcbb5950", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185443387700, "endTime": 28185446675800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "82f4775a-cf4d-4e97-ac57-9f9c09329110", "logId": "1f4657f4-d3e9-4088-93a6-fc002536fb1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82f4775a-cf4d-4e97-ac57-9f9c09329110", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185441448800}, "additional": {"logType": "detail", "children": [], "durationId": "aae18a80-1ecc-470c-b0a8-a343bcbb5950"}}, {"head": {"id": "1be68bc2-f3f5-4209-b35a-bc2ad5989829", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185441996300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8af80e9-2691-4809-83ce-b03587090d24", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185442154900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11ba1c3-d0fc-47d1-a057-17669883003f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185443468600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1120d9d-2c71-454b-a48a-2576fefa5afb", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185445531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e358437-4cd6-4657-ab50-53f387e48a3b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185446472200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff91d5e-5c0e-4b6d-8aec-420c5edc5e9e", "name": "entry : default@GenerateMetadata cost memory 0.09586334228515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185446600600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f4657f4-d3e9-4088-93a6-fc002536fb1a", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185443387700, "endTime": 28185446675800}, "additional": {"logType": "info", "children": [], "durationId": "aae18a80-1ecc-470c-b0a8-a343bcbb5950"}}, {"head": {"id": "99c091d5-977a-4a8a-9867-228fc748eea3", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450736300, "endTime": 28185451110800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2c188bd7-9500-42a5-9722-9d0e08151056", "logId": "59fe31f4-6bc4-4387-860f-56677990a425"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c188bd7-9500-42a5-9722-9d0e08151056", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185449600200}, "additional": {"logType": "detail", "children": [], "durationId": "99c091d5-977a-4a8a-9867-228fc748eea3"}}, {"head": {"id": "1b65619a-1d9a-41f5-9aad-4167f36dd210", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450327600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f5dfdc8-f5e7-46e2-87ca-c8b8c8b2eee2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450441900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa9f82c6-0da7-47c4-9106-4d61fba52f4e", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450745400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aa14336-73c1-48af-bd8c-2205a6c25894", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450849900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "502ce3f8-25db-495a-acf9-d8c2312083e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450901200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "842ceb72-386f-437c-8b35-b99e9f97fa51", "name": "entry : default@ConfigureCmake cost memory 0.036651611328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450969900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df29edd-a544-4cab-adbc-374f4c27d882", "name": "runTaskFromQueue task cost before running: 521 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185451046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59fe31f4-6bc4-4387-860f-56677990a425", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185450736300, "endTime": 28185451110800, "totalTime": 294800}, "additional": {"logType": "info", "children": [], "durationId": "99c091d5-977a-4a8a-9867-228fc748eea3"}}, {"head": {"id": "0130acbc-aa8a-4041-a44a-f9b333625e13", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185453903600, "endTime": 28185456001200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "e9669db7-b6bd-4ebc-943f-6aa8eae2e3d7", "logId": "c3ba3b92-d057-4603-8b2e-848f167eb952"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9669db7-b6bd-4ebc-943f-6aa8eae2e3d7", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185452748500}, "additional": {"logType": "detail", "children": [], "durationId": "0130acbc-aa8a-4041-a44a-f9b333625e13"}}, {"head": {"id": "e50d3edb-35e7-4d88-baf7-d35119807f8b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185453164100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82dd471c-2305-4363-a696-230a69a338d4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185453260000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4d125de-0276-478a-b082-4a1f0c7ea7ba", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185453914600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ca33d16-05fc-42c7-ab8b-abab2b5017ed", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185455644600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c457ef0b-9fa4-4830-9877-44a0c266906b", "name": "entry : default@MergeProfile cost memory 0.10699462890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185455791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3ba3b92-d057-4603-8b2e-848f167eb952", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185453903600, "endTime": 28185456001200}, "additional": {"logType": "info", "children": [], "durationId": "0130acbc-aa8a-4041-a44a-f9b333625e13"}}, {"head": {"id": "ba6cb242-bfdf-4d25-90de-515899fdf719", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185468619700, "endTime": 28185470819600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "328633a2-f2e2-4628-b567-c2b044e0115c", "logId": "f2d72861-f744-442a-9deb-343f49e717ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "328633a2-f2e2-4628-b567-c2b044e0115c", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185463464600}, "additional": {"logType": "detail", "children": [], "durationId": "ba6cb242-bfdf-4d25-90de-515899fdf719"}}, {"head": {"id": "4d567145-c3e5-4384-8ead-6b6d9b52f46c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185464570500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea1c28e-d1c0-4c76-9f15-0121785b3332", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185464769700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c225db8-c6d8-4dfd-bfae-c5e0d5c820bc", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185468638100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a2f663-9c41-4a25-b919-e0a13d4b14aa", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185469616000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e5eb16-e0be-4a3f-a8e8-333c3f709f4c", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185470621100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d5668f-592d-4f9b-9a7d-9a957eb8ab93", "name": "entry : default@CreateBuildProfile cost memory 0.10462188720703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185470740300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d72861-f744-442a-9deb-343f49e717ee", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185468619700, "endTime": 28185470819600}, "additional": {"logType": "info", "children": [], "durationId": "ba6cb242-bfdf-4d25-90de-515899fdf719"}}, {"head": {"id": "46c061ab-faf3-4dab-bc40-c6139c1d230a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185475520800, "endTime": 28185476485600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "511b574e-4cb6-40a6-a527-66b7bb115b6f", "logId": "16f913ae-427a-4ec0-b962-e0fcc7952fb7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "511b574e-4cb6-40a6-a527-66b7bb115b6f", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185473734600}, "additional": {"logType": "detail", "children": [], "durationId": "46c061ab-faf3-4dab-bc40-c6139c1d230a"}}, {"head": {"id": "16207cee-f337-4f4f-b432-69e11126db2f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185474242600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393eba4a-27ec-49b7-a173-aae91d16869e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185474368000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dfbf7ec-45bb-4cf2-aa54-66e20121468a", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185475543200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c2eb58f-36c1-45ae-af6f-053f657a8c67", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185475842300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c06be0fd-4648-4d0e-b297-1af8c591398e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185475941800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf4d276-2a7e-474f-85d4-42c97faa99bf", "name": "entry : default@PreCheckSyscap cost memory 0.03687286376953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185476209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00d0044e-8524-417c-aecc-37a490055992", "name": "runTaskFromQueue task cost before running: 546 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185476336900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16f913ae-427a-4ec0-b962-e0fcc7952fb7", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185475520800, "endTime": 28185476485600, "totalTime": 789500}, "additional": {"logType": "info", "children": [], "durationId": "46c061ab-faf3-4dab-bc40-c6139c1d230a"}}, {"head": {"id": "5fff62d6-1c32-415c-b38d-94c4fc4f49a8", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185487217500, "endTime": 28185488570700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d06a3f0a-6736-49b8-ae28-347aa8349758", "logId": "38c2fb63-d7f2-4052-bafa-191ab19b2cb8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d06a3f0a-6736-49b8-ae28-347aa8349758", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185479896200}, "additional": {"logType": "detail", "children": [], "durationId": "5fff62d6-1c32-415c-b38d-94c4fc4f49a8"}}, {"head": {"id": "59d94182-be71-4d2a-baa1-180ae280826c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185480963000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99f0b758-9365-4cca-8602-c7427945d750", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185481176600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9321674f-a1a8-4390-9d60-f5c5282ecb82", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185487233800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcf5d4da-ffb1-4f9c-bc46-e413fbb2a4fe", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185487860500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afa571d9-fa64-4acc-93cd-790c1fe7e521", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03920745849609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185488381100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04b8889c-3255-4141-b7a6-26e41e62ed34", "name": "runTaskFromQueue task cost before running: 558 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185488504400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c2fb63-d7f2-4052-bafa-191ab19b2cb8", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185487217500, "endTime": 28185488570700, "totalTime": 1266500}, "additional": {"logType": "info", "children": [], "durationId": "5fff62d6-1c32-415c-b38d-94c4fc4f49a8"}}, {"head": {"id": "3508c92c-254a-4c39-a997-09556007c0eb", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185507200400, "endTime": 28185511196900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "888aaf92-2bdf-47a4-8d7e-5a57a063fd8b", "logId": "5c6c39f4-16a6-4fe0-a10f-39d3fecfce95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "888aaf92-2bdf-47a4-8d7e-5a57a063fd8b", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185503106700}, "additional": {"logType": "detail", "children": [], "durationId": "3508c92c-254a-4c39-a997-09556007c0eb"}}, {"head": {"id": "e9982ac9-a34f-4c3b-8655-f7b3d1ce0bc6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185503740400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fce40e05-4db7-43ab-8ff2-b26525f74b3a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185503970900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe5a7c9-beab-4fe6-9ba3-d9f52031ef53", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185507219100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a46dbc5-822c-4c1b-b386-fa6ead08d163", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185510265700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d210d85-41b2-45b6-862f-f91a55422930", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185510599600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d22d6bd8-78c3-4dd1-bcc8-44871efa684e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185510883000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d57017-c24e-4590-bffd-527df766b776", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185510963500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a0b64b4-dbed-4f86-a4c3-4c5a54cd3729", "name": "entry : default@ProcessIntegratedHsp cost memory 0.118988037109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185511055300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3739263e-abda-4254-a539-4beced26438e", "name": "runTaskFromQueue task cost before running: 581 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185511140400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6c39f4-16a6-4fe0-a10f-39d3fecfce95", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185507200400, "endTime": 28185511196900, "totalTime": 3923800}, "additional": {"logType": "info", "children": [], "durationId": "3508c92c-254a-4c39-a997-09556007c0eb"}}, {"head": {"id": "301a4c95-0160-4ebc-b5ee-bc2f7f25f7b2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516052800, "endTime": 28185516658400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cf5cee54-922f-4b66-9806-4971d4ef0fa5", "logId": "88ea7113-cec8-4d69-9a5e-5a57f81a526b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf5cee54-922f-4b66-9806-4971d4ef0fa5", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185514571500}, "additional": {"logType": "detail", "children": [], "durationId": "301a4c95-0160-4ebc-b5ee-bc2f7f25f7b2"}}, {"head": {"id": "df158610-16ef-473d-a26b-667f812cfabd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185515154100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f86def4-65aa-41f0-b50d-ccb886d546b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185515263000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be0809d-501c-44ef-b605-d26ff2cc00dd", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "849b54bb-d2b9-4ac8-91c5-88cda00e50bd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516175600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2262c83-e12e-42f7-b164-6be8e03741ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516243000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fcc0259-c996-428c-a924-560404154c62", "name": "entry : default@BuildNativeWithCmake cost memory 0.0377197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516504200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98c002c5-3066-4135-b15f-3bb62140b5eb", "name": "runTaskFromQueue task cost before running: 586 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516600800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ea7113-cec8-4d69-9a5e-5a57f81a526b", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185516052800, "endTime": 28185516658400, "totalTime": 530700}, "additional": {"logType": "info", "children": [], "durationId": "301a4c95-0160-4ebc-b5ee-bc2f7f25f7b2"}}, {"head": {"id": "21900afd-cdfe-48cd-9b5d-f90dfe814c30", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185520359600, "endTime": 28185528167800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e37abb8d-8c88-4089-8a11-1d9d646533e0", "logId": "ce187c79-adeb-4e7f-ab64-19d81460555b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e37abb8d-8c88-4089-8a11-1d9d646533e0", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185518522400}, "additional": {"logType": "detail", "children": [], "durationId": "21900afd-cdfe-48cd-9b5d-f90dfe814c30"}}, {"head": {"id": "dcab52f0-a526-4d5c-8875-bc76f3bd240e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185519471200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddf9c829-e3ab-436a-9b88-bfddb9037e21", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185519578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f81efd1f-ea56-43c1-831c-1c5b99cccd71", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185520370300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ac84a1-37aa-4a8f-b566-b5e656d3b499", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185524754500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f08a1a6-3e1a-48a9-a030-327e94c418b4", "name": "entry : default@MakePackInfo cost memory 0.13983917236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185527781800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce187c79-adeb-4e7f-ab64-19d81460555b", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185520359600, "endTime": 28185528167800}, "additional": {"logType": "info", "children": [], "durationId": "21900afd-cdfe-48cd-9b5d-f90dfe814c30"}}, {"head": {"id": "c40ef8f8-f07d-4a09-b6b5-6a1b753d0b64", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185536187500, "endTime": 28185541123900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "de608f02-d3d0-4954-b344-dea1cc0dcbed", "logId": "47a96cfe-effe-4162-a3f2-b71f1b4cd60e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de608f02-d3d0-4954-b344-dea1cc0dcbed", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185533691800}, "additional": {"logType": "detail", "children": [], "durationId": "c40ef8f8-f07d-4a09-b6b5-6a1b753d0b64"}}, {"head": {"id": "ceea895f-74e9-4efd-b24b-a1adf117f720", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185534337700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9695c74-0eff-4315-996e-f831e1914ef0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185534464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0fceea3-4a0d-474b-a9bd-13fd9c8b453e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185536199900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a39b154-e230-4d64-b713-1f724f326ee4", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185536389100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5c93281-e001-4a49-b01b-9a0ff7a16976", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185537435500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a48f0d1-3d16-4f8e-a53d-72a041fbb8dd", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185540259300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e6c85f-926d-4a5e-88ab-b39179a430d9", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185540561800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa5d87d-3c79-4ac0-a115-9cd31efe1cb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185540683100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c47d4c-4e7c-4754-a3cb-d4578164a330", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185540825000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8a8f9f1-b310-41ed-bc1e-09262cf409f2", "name": "entry : default@SyscapTransform cost memory 0.1553955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185540933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f466fe33-2820-4cc4-a672-78670973dd2f", "name": "runTaskFromQueue task cost before running: 611 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185541015700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a96cfe-effe-4162-a3f2-b71f1b4cd60e", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185536187500, "endTime": 28185541123900, "totalTime": 4810600}, "additional": {"logType": "info", "children": [], "durationId": "c40ef8f8-f07d-4a09-b6b5-6a1b753d0b64"}}, {"head": {"id": "3f0f3ef6-36a4-455f-8952-f770b9d78be4", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185546960100, "endTime": 28185548576100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a128b456-29eb-44cf-90e9-68c4c3237867", "logId": "98e99185-b4a4-4742-9bbb-55e6802b65da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a128b456-29eb-44cf-90e9-68c4c3237867", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185543548000}, "additional": {"logType": "detail", "children": [], "durationId": "3f0f3ef6-36a4-455f-8952-f770b9d78be4"}}, {"head": {"id": "07192c84-5c13-44db-9ec0-583c6c092dbe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185544839400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e6e5c26-71ce-40b1-87c8-62701994340b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185545014700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec48ee1-f0e0-4557-ba9a-3aa9477f8b64", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185546976900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d0881a-6503-4e50-9e87-309a1e672e0d", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185548351400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e336eb-20a2-4475-94bc-9dde2c6b60d0", "name": "entry : default@ProcessProfile cost memory 0.06122589111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185548499000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e99185-b4a4-4742-9bbb-55e6802b65da", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185546960100, "endTime": 28185548576100}, "additional": {"logType": "info", "children": [], "durationId": "3f0f3ef6-36a4-455f-8952-f770b9d78be4"}}, {"head": {"id": "63a0b7c2-780a-4c24-8b54-a4f93e68b264", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185553530200, "endTime": 28185563508000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2ebc5aa5-409a-41ea-a6c4-8c6c8114e692", "logId": "7dcba13a-8076-45a9-a6fe-aa975d083b1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ebc5aa5-409a-41ea-a6c4-8c6c8114e692", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185551058800}, "additional": {"logType": "detail", "children": [], "durationId": "63a0b7c2-780a-4c24-8b54-a4f93e68b264"}}, {"head": {"id": "2bd8bde6-1840-4c39-8b4b-8fcd053cc4be", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185551656200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f927fb-8f04-4f0f-a464-a61f41458330", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185551796200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f4de0c-a17e-410b-b793-d3e529e637d2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185553545300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7a35f4-4caa-450a-812d-5e85862033b0", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185563257300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64cc0daa-034a-418b-b204-2fdcd278d33e", "name": "entry : default@ProcessRouterMap cost memory 0.29998779296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185563429100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dcba13a-8076-45a9-a6fe-aa975d083b1e", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185553530200, "endTime": 28185563508000}, "additional": {"logType": "info", "children": [], "durationId": "63a0b7c2-780a-4c24-8b54-a4f93e68b264"}}, {"head": {"id": "e29af861-6580-4ef6-9e59-7d4de20481f0", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185569531500, "endTime": 28185570569600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a1c502f2-9fdb-4067-b9ab-fec23a5da50e", "logId": "36531447-5576-4709-a96f-a477057cefcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1c502f2-9fdb-4067-b9ab-fec23a5da50e", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185568260900}, "additional": {"logType": "detail", "children": [], "durationId": "e29af861-6580-4ef6-9e59-7d4de20481f0"}}, {"head": {"id": "5c5c0de6-4c4c-4d3d-9941-61ad38e94aaa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185568637600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20f2a62-fa55-45cc-9659-d4a61a1a76f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185568736900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcb4a45c-8d7d-4f32-b4a9-106cdd7c1865", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185569542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e775424e-dcc5-4fa8-b178-d6458224bd2c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185569657500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66c1703a-19b5-4b9c-9974-aa5826ea12c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185569711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145ee424-094d-43c7-96d5-8a5645d83ed2", "name": "entry : default@BuildNativeWithNinja cost memory 0.05725860595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185570399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "450fce7c-dcc5-48ee-a486-0555f8c26dfc", "name": "runTaskFromQueue task cost before running: 640 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185570507500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36531447-5576-4709-a96f-a477057cefcd", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185569531500, "endTime": 28185570569600, "totalTime": 957000}, "additional": {"logType": "info", "children": [], "durationId": "e29af861-6580-4ef6-9e59-7d4de20481f0"}}, {"head": {"id": "c3746e68-6373-4709-8d73-dddb89cf5902", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185578908500, "endTime": 28185586586700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "880550fb-5c0c-46b7-b226-b7f48e47d0f8", "logId": "40b526e3-2e3b-44cf-b4f1-a63f842f08c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "880550fb-5c0c-46b7-b226-b7f48e47d0f8", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185573039900}, "additional": {"logType": "detail", "children": [], "durationId": "c3746e68-6373-4709-8d73-dddb89cf5902"}}, {"head": {"id": "b1ece029-1d56-4683-bf25-1d61eadd67e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185573650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd843f91-ea2e-4cc7-93e2-672936682326", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185573800600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1040fe86-f021-47e5-8a58-698068b09610", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185575010100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c07c42d-6e0d-4447-b87e-4fc5f73f7869", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185581952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1680b167-cc82-459a-a539-31f4cf47bca8", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185584517300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a9caea3-9afd-4920-a97d-babe815d16b2", "name": "entry : default@ProcessResource cost memory 0.17034149169921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185584705800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b526e3-2e3b-44cf-b4f1-a63f842f08c5", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185578908500, "endTime": 28185586586700}, "additional": {"logType": "info", "children": [], "durationId": "c3746e68-6373-4709-8d73-dddb89cf5902"}}, {"head": {"id": "41fc5a9b-4d59-42ba-bdb0-82ad96736cec", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185597936600, "endTime": 28185620084000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7c9c473b-f252-4691-b991-81053a3ccc46", "logId": "7ad3f7eb-c3d0-42bc-aa9b-d2b460405682"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c9c473b-f252-4691-b991-81053a3ccc46", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185592080000}, "additional": {"logType": "detail", "children": [], "durationId": "41fc5a9b-4d59-42ba-bdb0-82ad96736cec"}}, {"head": {"id": "1874a4e4-00a3-4ebf-a144-a1a3c0b1b37f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185592676300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "765dfaf8-7f47-4c22-8007-348e7376d019", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185593172300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5cf21f8-6097-4f31-a547-ff25f5a21914", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185597956500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b818f24c-fcf0-42d4-a406-e438b1d310de", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185619876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18619e1-5b56-4698-9068-d414d22b9745", "name": "entry : default@GenerateLoaderJson cost memory 0.7681884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185620010100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad3f7eb-c3d0-42bc-aa9b-d2b460405682", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185597936600, "endTime": 28185620084000}, "additional": {"logType": "info", "children": [], "durationId": "41fc5a9b-4d59-42ba-bdb0-82ad96736cec"}}, {"head": {"id": "0f4e4e33-b92a-454b-9f1d-33c2e149a141", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185629618700, "endTime": 28185633946200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "ac2ca446-7895-4bd6-ab7b-633a6a227194", "logId": "45a6269f-5797-4ecb-8f16-5489674d5346"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac2ca446-7895-4bd6-ab7b-633a6a227194", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185628165900}, "additional": {"logType": "detail", "children": [], "durationId": "0f4e4e33-b92a-454b-9f1d-33c2e149a141"}}, {"head": {"id": "a27cfdf5-381a-457b-b76f-b5960800a386", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185628543000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17786292-42a1-4352-9496-b8aad1762498", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185628656700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f5eb4e-cd61-4093-9b8b-3ba4b58d93d5", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185629662200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adeabd53-5d70-4aab-8a74-b459245b7121", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185633006600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d3defef-8a87-4651-a668-e4b9f8800d50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185633118900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aeff280-4349-402e-8c49-6bd378b86e62", "name": "entry : default@ProcessLibs cost memory 0.12631988525390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185633762300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "395ad6c0-da6e-4fb4-9e9f-7aad6dab22ec", "name": "runTaskFromQueue task cost before running: 704 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185633878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45a6269f-5797-4ecb-8f16-5489674d5346", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185629618700, "endTime": 28185633946200, "totalTime": 4254700}, "additional": {"logType": "info", "children": [], "durationId": "0f4e4e33-b92a-454b-9f1d-33c2e149a141"}}, {"head": {"id": "1c191801-5cf1-4d3a-84a8-66a5645984fc", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185640740400, "endTime": 28185680248900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "96191f07-6549-44ad-9656-63207a0d07a3", "logId": "f8f205f8-d545-46ce-8251-df3850c607ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96191f07-6549-44ad-9656-63207a0d07a3", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185636120000}, "additional": {"logType": "detail", "children": [], "durationId": "1c191801-5cf1-4d3a-84a8-66a5645984fc"}}, {"head": {"id": "4ff661ec-4586-4211-8a5b-389b59cc00ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185636480400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b187fe-fdb3-42c6-92e1-faf963a2978d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185636590200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2611bb3-5d2c-4d60-b58e-e2b26484c43d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185637312900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "615475e6-e086-4713-9a57-927fc2ca6ff5", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185640769700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c77ef73-b7b0-4f98-a464-cb0fdf94cbc8", "name": "Incremental task entry:default@CompileResource pre-execution cost: 38 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185679628000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e64cdd9-cef2-489c-ac24-4079e38f0672", "name": "entry : default@CompileResource cost memory -4.378837585449219", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185679970300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f205f8-d545-46ce-8251-df3850c607ed", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185640740400, "endTime": 28185680248900}, "additional": {"logType": "info", "children": [], "durationId": "1c191801-5cf1-4d3a-84a8-66a5645984fc"}}, {"head": {"id": "fa1b7ce2-e1cd-4d1c-b26f-17d5b6d0fa9d", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185700431800, "endTime": 28185701705900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b6eda9eb-b7af-4b41-965f-732847c12954", "logId": "3f3474e5-edfc-457d-8091-34168ca464ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6eda9eb-b7af-4b41-965f-732847c12954", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185686529900}, "additional": {"logType": "detail", "children": [], "durationId": "fa1b7ce2-e1cd-4d1c-b26f-17d5b6d0fa9d"}}, {"head": {"id": "a8d1a2f8-566c-42db-a7c0-04baf57c8049", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185686923800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ef8609-f76e-478b-9355-4760ff5d9d05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185687033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5f012af-f90c-43fd-a0c3-ace1a10acbbc", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185700444700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "731e5006-e780-4f58-9fd5-e6a39b72524c", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185700784200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798c5b2f-ecbd-4cfa-baca-2fe893a237de", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185701541500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dcc42e2-92d2-4a5b-92e1-616cb999e3c6", "name": "entry : default@DoNativeStrip cost memory 0.07706451416015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185701639100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f3474e5-edfc-457d-8091-34168ca464ae", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185700431800, "endTime": 28185701705900}, "additional": {"logType": "info", "children": [], "durationId": "fa1b7ce2-e1cd-4d1c-b26f-17d5b6d0fa9d"}}, {"head": {"id": "8c8d567f-187d-4ce5-bdff-6ce7e71d8bfe", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185710084000, "endTime": 28187447780500}, "additional": {"children": ["3124948d-594d-4107-abb9-f8453abfb9ab", "05cfe5f9-9e7f-4750-86d7-c2a9e6fcf8cc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "aefed8b9-8470-43ad-82a7-76896ee61bf7", "logId": "6aee29cc-fbb4-4032-8a68-dfa2f26763e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aefed8b9-8470-43ad-82a7-76896ee61bf7", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185703463000}, "additional": {"logType": "detail", "children": [], "durationId": "8c8d567f-187d-4ce5-bdff-6ce7e71d8bfe"}}, {"head": {"id": "911dd399-aa0a-4220-abd6-e3bd9cd0202b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185703796700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2541ad-291f-4bae-ab58-76d33d735a94", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185704050000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af8a3c42-c942-4228-aabb-a171f1ffba59", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185710097300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb80238-bdb1-45ee-9e77-4ae2eb873ff2", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185722392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e11b8c-1607-4370-9965-a2b8ef947196", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185722540100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac1e4bc4-0b91-4c54-9f15-e21c497c3060", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185741177600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd58b2bf-950a-4896-b6b0-b12829efc2f1", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185741882000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea8817c-a10b-431f-af80-593194c6917e", "name": "default@CompileArkTS work[42] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185767679700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3124948d-594d-4107-abb9-f8453abfb9ab", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28185774893900, "endTime": 28187295331100}, "additional": {"children": ["0e1edac7-132c-4100-b506-8d4147b6f607", "d808c865-5839-4736-8801-1a231c70efc9", "97e23519-a2bf-4a32-9bef-8b8c9c68c87d", "52770c79-c00f-48de-8c09-a9e27fcf3994", "28e9bc1f-8ea4-41e7-b2f9-b69c1c68d9bd", "eb2fdbb7-12d8-4b1e-a4c9-f9490fe0176b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8c8d567f-187d-4ce5-bdff-6ce7e71d8bfe", "logId": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "64a0da2f-df1c-464e-9238-64fcdaa0b63b", "name": "default@CompileArkTS work[42] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185770769300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef25d3f5-3fe0-4c67-963a-9163a6a0f1b8", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185772917000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a65d378c-7613-490c-9f0e-6068b2afedd7", "name": "default@CompileArkTS work[42] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185773727500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "594f067a-8802-4122-a358-c32d7f943126", "name": "default@CompileArkTS work[42] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185774042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a94c7e-d3b1-44b3-b3d0-4d03b0de4543", "name": "CopyResources startTime: 28185774272500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185774278700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9a39460-7bf4-4cb1-845a-e54e38bee90a", "name": "default@CompileArkTS work[43] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185774554700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05cfe5f9-9e7f-4750-86d7-c2a9e6fcf8cc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 28187435575900, "endTime": 28187446601800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8c8d567f-187d-4ce5-bdff-6ce7e71d8bfe", "logId": "b7a46187-1a08-45a1-a87e-496c5f0f5d9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ede1609-7eab-4d2b-b49b-e689ae13e63f", "name": "default@CompileArkTS work[43] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185777626200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff5384c6-f7da-4ddd-a4a6-89ebbc23de0f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185777731400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "897df19b-467c-4507-abc8-d08ec1d334cd", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185777782600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "873f0f27-bc91-455e-9afa-9c643b163d2f", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185778092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "063f0de2-148c-4a17-b4ba-aa41ffd26da6", "name": "default@CompileArkTS work[43] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185780137500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68d322ca-531a-4ded-a8c8-8c3d542d103c", "name": "default@CompileArkTS work[43] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185780305700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a29abde-7559-4ab5-bfb4-a4fcfee08cd9", "name": "entry : default@CompileArkTS cost memory 1.8479385375976562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185780876600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "231d7fb1-667b-4448-a394-57a760e26890", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185788231300, "endTime": 28185792562900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "c1bd427f-2ad7-4d11-9bbb-98724eb3cd11", "logId": "9943c013-bcfd-4f0d-90dd-18a28c719db5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1bd427f-2ad7-4d11-9bbb-98724eb3cd11", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185783632900}, "additional": {"logType": "detail", "children": [], "durationId": "231d7fb1-667b-4448-a394-57a760e26890"}}, {"head": {"id": "f014f184-2260-4b6a-870b-4aaccf497833", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185784022900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "440dc7b8-edd4-45bc-bb3e-24adaacc5997", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185784122500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d314d9b0-adf5-46e2-8cd3-e61319a2bab1", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185788248200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71866dbd-0ee6-42d2-bbe7-85427431dbbc", "name": "entry : default@BuildJS cost memory 0.13067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185792324300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d3447d6-79c8-49b3-a1b0-c0bbc506af21", "name": "runTaskFromQueue task cost before running: 862 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185792493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9943c013-bcfd-4f0d-90dd-18a28c719db5", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185788231300, "endTime": 28185792562900, "totalTime": 4236200}, "additional": {"logType": "info", "children": [], "durationId": "231d7fb1-667b-4448-a394-57a760e26890"}}, {"head": {"id": "23c1caaa-5405-48aa-b2bf-cd0269aa86ba", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185800852300, "endTime": 28185804441200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e05b1077-0064-40b4-8af1-e7d71abeeb09", "logId": "932baaee-c890-4fd5-86c7-38c9112fe83b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e05b1077-0064-40b4-8af1-e7d71abeeb09", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185795099800}, "additional": {"logType": "detail", "children": [], "durationId": "23c1caaa-5405-48aa-b2bf-cd0269aa86ba"}}, {"head": {"id": "995a90fd-38b5-4194-88b6-5cb862829b87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185795553000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a88808-a5cd-459e-8726-dba694b81853", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185795727200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c4d62fd-c4e3-4e77-879f-8e832d76295b", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185800868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdb6462-6603-41b0-953e-859b292d2d4f", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185801275400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f778b12b-31fc-45e8-ab38-10344247054e", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185804185400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a84fab-90b4-4464-b097-add7d7c6f4c4", "name": "entry : default@CacheNativeLibs cost memory 0.09393310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185804348600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "932baaee-c890-4fd5-86c7-38c9112fe83b", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185800852300, "endTime": 28185804441200}, "additional": {"logType": "info", "children": [], "durationId": "23c1caaa-5405-48aa-b2bf-cd0269aa86ba"}}, {"head": {"id": "a974e820-d147-41d5-a28e-dec6130e09c8", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187296086200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e1edac7-132c-4100-b506-8d4147b6f607", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28185775081600, "endTime": 28185783446300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3124948d-594d-4107-abb9-f8453abfb9ab", "logId": "371f2512-0dd2-49e0-9e8e-079a8919a833"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "371f2512-0dd2-49e0-9e8e-079a8919a833", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185775081600, "endTime": 28185783446300}, "additional": {"logType": "info", "children": [], "durationId": "0e1edac7-132c-4100-b506-8d4147b6f607", "parent": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}}, {"head": {"id": "d808c865-5839-4736-8801-1a231c70efc9", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28185783462400, "endTime": 28185783561300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3124948d-594d-4107-abb9-f8453abfb9ab", "logId": "3ccf652b-1c07-4530-bebc-dc2e66f85234"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ccf652b-1c07-4530-bebc-dc2e66f85234", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185783462400, "endTime": 28185783561300}, "additional": {"logType": "info", "children": [], "durationId": "d808c865-5839-4736-8801-1a231c70efc9", "parent": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}}, {"head": {"id": "97e23519-a2bf-4a32-9bef-8b8c9c68c87d", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28185783571900, "endTime": 28185783604200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3124948d-594d-4107-abb9-f8453abfb9ab", "logId": "496dea25-885e-4533-8ac2-a6d6113b5e64"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "496dea25-885e-4533-8ac2-a6d6113b5e64", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185783571900, "endTime": 28185783604200}, "additional": {"logType": "info", "children": [], "durationId": "97e23519-a2bf-4a32-9bef-8b8c9c68c87d", "parent": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}}, {"head": {"id": "52770c79-c00f-48de-8c09-a9e27fcf3994", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28185783621100, "endTime": 28187158744800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3124948d-594d-4107-abb9-f8453abfb9ab", "logId": "01d6346f-f663-42e7-b34f-11875e8e79a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01d6346f-f663-42e7-b34f-11875e8e79a8", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185783621100, "endTime": 28187158744800}, "additional": {"logType": "info", "children": [], "durationId": "52770c79-c00f-48de-8c09-a9e27fcf3994", "parent": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}}, {"head": {"id": "28e9bc1f-8ea4-41e7-b2f9-b69c1c68d9bd", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187158771000, "endTime": 28187176558300}, "additional": {"children": ["59ed9160-9866-4269-8d2f-692b0d1421df", "cec8cd19-e06b-4779-beb8-fe0a2541bc96", "7a452e18-1354-4528-a041-24c9a1034ef5"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3124948d-594d-4107-abb9-f8453abfb9ab", "logId": "c4c99799-ac26-4d41-b888-2236b4f291b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4c99799-ac26-4d41-b888-2236b4f291b2", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187158771000, "endTime": 28187176558300}, "additional": {"logType": "info", "children": ["cd08110a-5753-48d5-83e7-6ea903372b1f", "11d9d937-355c-4b0f-9d54-d2e1bc1ff105", "c6176f8c-1889-4c17-afce-deb9d9f9c74b"], "durationId": "28e9bc1f-8ea4-41e7-b2f9-b69c1c68d9bd", "parent": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}}, {"head": {"id": "59ed9160-9866-4269-8d2f-692b0d1421df", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187158789600, "endTime": 28187158794800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "28e9bc1f-8ea4-41e7-b2f9-b69c1c68d9bd", "logId": "cd08110a-5753-48d5-83e7-6ea903372b1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd08110a-5753-48d5-83e7-6ea903372b1f", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187158789600, "endTime": 28187158794800}, "additional": {"logType": "info", "children": [], "durationId": "59ed9160-9866-4269-8d2f-692b0d1421df", "parent": "c4c99799-ac26-4d41-b888-2236b4f291b2"}}, {"head": {"id": "cec8cd19-e06b-4779-beb8-fe0a2541bc96", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187158798400, "endTime": 28187165294100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "28e9bc1f-8ea4-41e7-b2f9-b69c1c68d9bd", "logId": "11d9d937-355c-4b0f-9d54-d2e1bc1ff105"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11d9d937-355c-4b0f-9d54-d2e1bc1ff105", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187158798400, "endTime": 28187165294100}, "additional": {"logType": "info", "children": [], "durationId": "cec8cd19-e06b-4779-beb8-fe0a2541bc96", "parent": "c4c99799-ac26-4d41-b888-2236b4f291b2"}}, {"head": {"id": "7a452e18-1354-4528-a041-24c9a1034ef5", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187165299700, "endTime": 28187176536300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "28e9bc1f-8ea4-41e7-b2f9-b69c1c68d9bd", "logId": "c6176f8c-1889-4c17-afce-deb9d9f9c74b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6176f8c-1889-4c17-afce-deb9d9f9c74b", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187165299700, "endTime": 28187176536300}, "additional": {"logType": "info", "children": [], "durationId": "7a452e18-1354-4528-a041-24c9a1034ef5", "parent": "c4c99799-ac26-4d41-b888-2236b4f291b2"}}, {"head": {"id": "eb2fdbb7-12d8-4b1e-a4c9-f9490fe0176b", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187176579600, "endTime": 28187295084200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "3124948d-594d-4107-abb9-f8453abfb9ab", "logId": "c22a54ba-edac-463d-bad1-9362ec73f494"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c22a54ba-edac-463d-bad1-9362ec73f494", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187176579600, "endTime": 28187295084200}, "additional": {"logType": "info", "children": [], "durationId": "eb2fdbb7-12d8-4b1e-a4c9-f9490fe0176b", "parent": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd"}}, {"head": {"id": "4986cf11-4298-41a6-a800-3ba2138ba1e5", "name": "default@CompileArkTS work[42] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187305437600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb51f3e1-6c03-4b2e-b176-b82409f2b4dd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28185774893900, "endTime": 28187295331100}, "additional": {"logType": "info", "children": ["371f2512-0dd2-49e0-9e8e-079a8919a833", "3ccf652b-1c07-4530-bebc-dc2e66f85234", "496dea25-885e-4533-8ac2-a6d6113b5e64", "01d6346f-f663-42e7-b34f-11875e8e79a8", "c4c99799-ac26-4d41-b888-2236b4f291b2", "c22a54ba-edac-463d-bad1-9362ec73f494"], "durationId": "3124948d-594d-4107-abb9-f8453abfb9ab", "parent": "6aee29cc-fbb4-4032-8a68-dfa2f26763e0"}}, {"head": {"id": "a02f566c-33eb-41a5-adb1-57c70a8dd99c", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187446755400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff63807-18a3-4c87-a760-35d78d1e0ef1", "name": "CopyResources is end, endTime: 28187447460000", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187447473100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6678c352-640c-4924-a0d2-05ccc9a09a7e", "name": "default@CompileArkTS work[43] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187447660300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a46187-1a08-45a1-a87e-496c5f0f5d9f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 28187435575900, "endTime": 28187446601800}, "additional": {"logType": "info", "children": [], "durationId": "05cfe5f9-9e7f-4750-86d7-c2a9e6fcf8cc", "parent": "6aee29cc-fbb4-4032-8a68-dfa2f26763e0"}}, {"head": {"id": "6aee29cc-fbb4-4032-8a68-dfa2f26763e0", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28185710084000, "endTime": 28187447780500, "totalTime": 1596278700}, "additional": {"logType": "info", "children": ["eb51f3e1-6c03-4b2e-b176-b82409f2b4dd", "b7a46187-1a08-45a1-a87e-496c5f0f5d9f"], "durationId": "8c8d567f-187d-4ce5-bdff-6ce7e71d8bfe"}}, {"head": {"id": "ff4fdf5b-812c-45cd-aa1a-f0ea321d07e2", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187453526700, "endTime": 28187454593300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "420e0021-a2ce-480e-bb09-5468e488e96b", "logId": "e9c5098c-2974-4029-ae0f-c01d8e4b47c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "420e0021-a2ce-480e-bb09-5468e488e96b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187452206800}, "additional": {"logType": "detail", "children": [], "durationId": "ff4fdf5b-812c-45cd-aa1a-f0ea321d07e2"}}, {"head": {"id": "194e2c11-9e60-4c32-9fb2-e31025935b0c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187452618500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cff6fb5-ac18-4a31-bb8a-47fe3a7818c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187452716700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bae689f6-5679-419c-a820-3300da860edf", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187453536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80aab3f7-4f66-49d3-b759-3322650809aa", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187453747300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "833caf6f-ef44-428b-9516-48597f1e7c34", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187454450700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b21fbc3a-d953-47f2-b384-b1a4dfc62244", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0768890380859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187454531100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9c5098c-2974-4029-ae0f-c01d8e4b47c0", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187453526700, "endTime": 28187454593300}, "additional": {"logType": "info", "children": [], "durationId": "ff4fdf5b-812c-45cd-aa1a-f0ea321d07e2"}}, {"head": {"id": "4ff64740-f46b-4bb7-b043-55fa44ac6132", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187465281000, "endTime": 28188065210900}, "additional": {"children": ["ebedae3a-f3c1-476e-abee-358083b549e2", "53cad27e-ec63-4c4a-a5c6-37d6611f7a28", "4619c779-6cc3-4e26-b9fa-a8ba67200284"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "6e6b05de-cf9a-4f46-8bf0-0abb46abcbe9", "logId": "579376d5-de9a-4db0-ae07-d751832fffd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e6b05de-cf9a-4f46-8bf0-0abb46abcbe9", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187458162600}, "additional": {"logType": "detail", "children": [], "durationId": "4ff64740-f46b-4bb7-b043-55fa44ac6132"}}, {"head": {"id": "ed90c55e-0677-4758-aedc-239fe7fdb96a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187458586400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a720db20-9824-4aa1-a7f2-f7a61de5e8bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187458694700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8715d41-c36f-4b1e-8660-f8126bd49455", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187465362300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "765a09fc-a2e8-49d9-b69b-e245f4a3b8ab", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187477322700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d389a3-988c-43f3-b3f5-594372ab27a6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187477471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b6fb013-c1f2-46ac-9f56-c3e4f0221fc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187477566100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1de2c56f-0ce4-4087-8bc3-dd161fc21471", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187477619000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebedae3a-f3c1-476e-abee-358083b549e2", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187478524200, "endTime": 28187479957800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ff64740-f46b-4bb7-b043-55fa44ac6132", "logId": "e9b43213-0b0f-4f01-ae03-e479cc656120"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e7cd074-0c4d-4c6b-a22f-4205c362af47", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187479820700}, "additional": {"logType": "debug", "children": [], "durationId": "4ff64740-f46b-4bb7-b043-55fa44ac6132"}}, {"head": {"id": "e9b43213-0b0f-4f01-ae03-e479cc656120", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187478524200, "endTime": 28187479957800}, "additional": {"logType": "info", "children": [], "durationId": "ebedae3a-f3c1-476e-abee-358083b549e2", "parent": "579376d5-de9a-4db0-ae07-d751832fffd4"}}, {"head": {"id": "53cad27e-ec63-4c4a-a5c6-37d6611f7a28", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187481545400, "endTime": 28187484262500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ff64740-f46b-4bb7-b043-55fa44ac6132", "logId": "c8bec7e4-9fe4-41e5-9d22-3e162d43b9bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06f29405-fdd6-4d51-a3b2-34d7cc52c722", "name": "default@PackageHap work[44] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187483121500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4619c779-6cc3-4e26-b9fa-a8ba67200284", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187518121400, "endTime": 28188064429100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4ff64740-f46b-4bb7-b043-55fa44ac6132", "logId": "1fde02c5-b7ba-4fcc-91b4-c1eef4f42f33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fdad9f5-9df3-453a-be0b-2c8e875c7714", "name": "default@PackageHap work[44] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187484107500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5395255c-f1f2-4357-9a3b-f57d452629f9", "name": "default@PackageHap work[44] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187484202600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8bec7e4-9fe4-41e5-9d22-3e162d43b9bf", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187481545400, "endTime": 28187484262500}, "additional": {"logType": "info", "children": [], "durationId": "53cad27e-ec63-4c4a-a5c6-37d6611f7a28", "parent": "579376d5-de9a-4db0-ae07-d751832fffd4"}}, {"head": {"id": "9eac5bd9-a02b-42ca-846c-e90a561c1fdb", "name": "entry : default@PackageHap cost memory 1.2855148315429688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187487885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab38aeb-42cb-40df-a444-af40863c33c2", "name": "default@PackageHap work[44] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187518048100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23919477-4fca-476b-a9dc-30f65fffb5bb", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187548929100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab310ab-40d7-43d8-8e00-8165de1cebc4", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187595696900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b826b888-69d8-40ae-995e-76b49ea7f4a1", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187595857500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc7ddb9-49c2-445d-8a11-a06871bc8555", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187595918000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49697816-01ff-4793-a3b1-761e4c4d59d2", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187595975100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea08ce64-0477-49a1-b1e9-a1f08f1ef4a1", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187596024000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41b6ba61-f434-4a08-87bc-54ef387a1d64", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187596082300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f095e1a5-6ce4-48f6-b04e-b5b7e5e0de76", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188064527000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1967e4c6-740f-4c52-a1f1-dc117ea45c2c", "name": "default@PackageHap work[44] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188064882900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fde02c5-b7ba-4fcc-91b4-c1eef4f42f33", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 28187518121400, "endTime": 28188064429100}, "additional": {"logType": "info", "children": [], "durationId": "4619c779-6cc3-4e26-b9fa-a8ba67200284", "parent": "579376d5-de9a-4db0-ae07-d751832fffd4"}}, {"head": {"id": "579376d5-de9a-4db0-ae07-d751832fffd4", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28187465281000, "endTime": 28188065210900, "totalTime": 569043600}, "additional": {"logType": "info", "children": ["e9b43213-0b0f-4f01-ae03-e479cc656120", "c8bec7e4-9fe4-41e5-9d22-3e162d43b9bf", "1fde02c5-b7ba-4fcc-91b4-c1eef4f42f33"], "durationId": "4ff64740-f46b-4bb7-b043-55fa44ac6132"}}, {"head": {"id": "2664957a-6c4d-43e2-8bd3-7067c6b713d5", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188077064000, "endTime": 28188079659200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "e7002da5-af52-4740-8220-bffbac186289", "logId": "074af870-9126-4624-93f2-b94cea042c40"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7002da5-af52-4740-8220-bffbac186289", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188069988300}, "additional": {"logType": "detail", "children": [], "durationId": "2664957a-6c4d-43e2-8bd3-7067c6b713d5"}}, {"head": {"id": "53abc506-ae31-40e5-bd74-98aeea18d686", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188070787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f2c34cc-4190-47b7-b193-0f0199168977", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188070962700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "515127b8-7759-4de1-a544-3e25f6648cae", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188077081600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fc84824-654b-4b59-8509-5ea3b1fade0d", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188077739600}, "additional": {"logType": "warn", "children": [], "durationId": "2664957a-6c4d-43e2-8bd3-7067c6b713d5"}}, {"head": {"id": "6b541633-86a6-4926-8afd-6588e393354a", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188078465900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2021f871-a223-4c8a-9b9e-ab2196a9f83d", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188078577000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7881e290-1751-4e40-95fd-a129e86053c2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188078668100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beac2da2-3d43-4da9-a311-510dac2eadbe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188078760000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dfc5d3a-46db-4956-a342-a8965f763a83", "name": "entry : default@SignHap cost memory 0.1238555908203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188079465000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "908c0d30-5601-4026-9983-7dd1d8f211fd", "name": "runTaskFromQueue task cost before running: 3 s 149 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188079592300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074af870-9126-4624-93f2-b94cea042c40", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188077064000, "endTime": 28188079659200, "totalTime": 2505800}, "additional": {"logType": "info", "children": [], "durationId": "2664957a-6c4d-43e2-8bd3-7067c6b713d5"}}, {"head": {"id": "4dd9d211-76cc-4c3d-bf86-192a8ba1c75a", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188084860100, "endTime": 28188092055800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f271ab69-af77-4747-94ae-d692971e3983", "logId": "fdb0d2b1-ddaf-40a2-a131-7f22405cc175"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f271ab69-af77-4747-94ae-d692971e3983", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188083224400}, "additional": {"logType": "detail", "children": [], "durationId": "4dd9d211-76cc-4c3d-bf86-192a8ba1c75a"}}, {"head": {"id": "adb401c9-e2e8-46d9-a3ce-d036f7151b4a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188083652300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbf27b8-e2f4-4dc8-96d8-9fb19fb5bd1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188083754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c329292-f36d-464f-9180-ebffe7c1ee09", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188084875000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af7740e-a274-4baf-9288-4047e3dabb92", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188091186900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb91a052-0521-4d71-bf5e-67df486789ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188091668100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "074650eb-d31f-40c3-83c8-ce635e932225", "name": "entry : default@CollectDebugSymbol cost memory 0.240509033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188091812000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cb2cca4-cb7b-402d-b143-0c078c0d73f8", "name": "runTaskFromQueue task cost before running: 3 s 162 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188091936000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb0d2b1-ddaf-40a2-a131-7f22405cc175", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188084860100, "endTime": 28188092055800, "totalTime": 7027300}, "additional": {"logType": "info", "children": [], "durationId": "4dd9d211-76cc-4c3d-bf86-192a8ba1c75a"}}, {"head": {"id": "3a4b79d3-850f-43f0-85ba-339584e9e157", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188098201000, "endTime": 28188099140000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d79a4100-56a0-4e2b-b2c2-f18efc9e61e4", "logId": "63d82b9c-a8ed-4dcd-b8e7-47facd500d01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d79a4100-56a0-4e2b-b2c2-f18efc9e61e4", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188098121900}, "additional": {"logType": "detail", "children": [], "durationId": "3a4b79d3-850f-43f0-85ba-339584e9e157"}}, {"head": {"id": "5225896a-d9ca-4df6-b0bd-ee0e12e0b402", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188098215600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f6f63c7-a118-4d7b-b122-648a0d1dee51", "name": "entry : assembleHap cost memory 0.0115814208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188098819700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "118e3082-5e7b-4465-8f44-5adf7e73a76f", "name": "runTaskFromQueue task cost before running: 3 s 169 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188098975400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d82b9c-a8ed-4dcd-b8e7-47facd500d01", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188098201000, "endTime": 28188099140000, "totalTime": 743600}, "additional": {"logType": "info", "children": [], "durationId": "3a4b79d3-850f-43f0-85ba-339584e9e157"}}, {"head": {"id": "df8b73aa-6f61-4ad1-b3e3-173612f1de54", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188112766300, "endTime": 28188112802500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fa94d5c1-78b5-4d16-b877-3997a6157fd5", "logId": "5cacd70e-4cb1-4d4f-bbc1-f870da119f88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cacd70e-4cb1-4d4f-bbc1-f870da119f88", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188112766300, "endTime": 28188112802500}, "additional": {"logType": "info", "children": [], "durationId": "df8b73aa-6f61-4ad1-b3e3-173612f1de54"}}, {"head": {"id": "6a5d6a0e-b8ee-4e82-91c0-c6e3ba904dee", "name": "BUILD SUCCESSFUL in 3 s 183 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188112983800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "98f201d4-da2d-428d-b9a5-77fd8320240a", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28184930841000, "endTime": 28188114565900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 19}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ccab0c43-5e12-46d8-b643-363fd36886ce", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188114749400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4211b44a-9608-4381-9030-2248ff63d5ac", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188115395300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e2152d9-733d-46e5-9d30-97767f7efeae", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188115817400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "defbb5a9-e305-4758-97b1-badcc479716f", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188115895900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96f4e24c-587a-423f-bae2-8ef265c3fa63", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188115975900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f66ac457-e36d-4505-b5f4-f7c63e62b6d5", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188116417600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdcaa983-bdd2-4615-ae57-d1c4b9d0c7a5", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188117095700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12fb367f-ac8f-443c-b5db-ff6383faaad4", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188117386400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c223e2ee-ead1-4c71-b260-6fd3e301ddc7", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188117461700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5edff900-bcaa-4211-b1a4-b8d686e310a3", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188117525300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b29ab57-ae08-4b45-8725-ff95b19c9825", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188117790500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ab6c484-f234-4433-934e-bf32a356e5b1", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188118681100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d11c5f-d07d-49f2-bbf4-74fc4d6a9110", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188119031200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd28226-f60f-4854-a896-5605a76cfd79", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188119187200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0edf28ae-ca9b-4ec6-a3bd-22f55682c78e", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188119467600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2559e590-8d0a-47ff-8481-902170c523f2", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188119536300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b947453d-3734-4872-8c14-a774158b1674", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188119593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3324575-e75f-45a3-b7e7-dba12c69bbed", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188120055600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20df12c-904b-4b65-8ec2-ddeb1227f03d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188120343500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8873375a-cab1-41bd-8ce4-a61c427b9373", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188120621500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feb582bc-c3b1-4153-9faa-8962217d515a", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188120909100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "969ed165-0961-4ab3-8b48-71c26c4f2138", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188120980400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20460197-58c9-469e-88ed-4fcaf92d58c2", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188121030600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed0e9ce3-f73b-41b6-84dc-20ad27e28bc2", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188124174000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7577b8aa-6516-41c6-801d-025a5fab7afe", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188124687700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7d3e5d-2528-4a58-bd0d-4f8575a38919", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188125579400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20b0035-3c83-4bea-909b-310fb23d3fc1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188125842600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b57eb5-8d65-4740-a032-de79b789a9f6", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188126146600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed4dc670-04df-4730-aec4-fe66bb91355a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188126850500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd729b02-2077-4781-b399-f531707ca392", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188127196600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3091769-d42d-4fc0-9e25-34404ca503f3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188127836200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2818cd4a-96b2-4297-ba06-0bf45ec90e53", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188128094700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c469c481-927d-4a9f-9533-d59cc424b0fa", "name": "Incremental task entry:default@CompileArkTS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188128742800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87a92cb9-3945-4a19-a153-b5727cdb7e13", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188130033400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5580ed-c3e2-4f7a-b508-3a24a6dd0d29", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188131602600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36a4ec99-3227-4881-a93d-ee239b8707a8", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188133344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6b654b3-f37a-4ad0-817e-3143b43f0edb", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188133612000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee78d0e-4bc0-4f54-a598-02e92c0f4660", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188133852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5bc4775-c2e0-4bed-b77a-c2e5d2f73234", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188134418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d23f242-6320-4c62-b4a2-6ef68a87cbae", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188134696300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f77ef14b-7f39-43c8-88d9-69c6bea85e2f", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188134997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9651990d-160f-48ed-b521-7e24ae6fd199", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188135079100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6d435ce-abe3-40d2-b866-d2e16bc38c7c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188136153600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ecd0bdc-c077-41c4-9fce-fc6db9d13003", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188136529800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50482a95-5f2a-4755-9dd9-4f1ffba498db", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188136911300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80694de-f2c3-4a77-96e8-1c52c5e38039", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188145079500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b365636d-de60-4210-a286-76b97c8b12f2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188145547400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2094c7-98b6-424a-ae2a-a9458bde5020", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188145768400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f8929d0-fcf6-4812-b083-062e135659db", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188145883100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d48bdb71-2fde-4eb9-80f5-30662e9cca18", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188146641600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6402ff1b-41b3-483a-be58-6ca65daaf751", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188147703000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493e4d07-dcda-4666-be22-80330deb2471", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188148043700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2f9ff2-5172-410d-ba9b-922212ef7cc7", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188148424100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb58f924-bde5-4500-a78a-2bb6dc71e45e", "name": "Incremental task entry:default@PackageHap post-execution cost:14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188149162000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f485e247-9a92-4455-ae9b-79b06b1e1124", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188149734800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54118d63-e6d3-4d4e-953b-c190bff7b6fe", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188150194100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01cfec1e-3876-4ade-8d5d-42a6b3ac1783", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188150651900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6154a0d-9a77-4ff5-aeae-93828bf5be41", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188164432300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f18c09c-01de-4e86-93dd-99f25446517a", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188164936800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b1e8b67-9659-402d-8a7f-f57078500cac", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188165396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6309e15-493a-4099-9121-031528c7aa82", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 28188165850700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}