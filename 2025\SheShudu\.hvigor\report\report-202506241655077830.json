{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "7583314d-03fe-4c31-89d2-346d1a38318c", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323890268100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe15fbc-cad4-469e-99e1-9c86f42504d4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323895072400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f492152-f045-4161-8d07-59040a951fe3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323895350800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12fb48b0-81c2-42be-837b-8f1c897854ff", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30323909277000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f21e8dd5-5a62-4133-bdfe-36014b2d27e9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339676016400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af58a61e-634a-431d-8cf7-258580e7bc30", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339683750300, "endTime": 30340086611000}, "additional": {"children": ["f3973713-f732-4630-8cb8-f857dfdf4dfa", "e7c5545f-00c2-47b6-98ab-821bc0739a36", "a732c4dc-b827-4516-a5f8-9c17d8319ffa", "96960350-e332-40ea-8e8b-06428c6b07f0", "c7a0acd5-4e64-42b2-9875-386ab57a20ae", "8c717c68-add2-425d-86cf-1da8e2e73fe2", "313bb57b-28f4-4665-be6e-54367181829d"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3973713-f732-4630-8cb8-f857dfdf4dfa", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339683752800, "endTime": 30339698885400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "677337ac-d371-4324-8c34-0ab2e6905d6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339698902500, "endTime": 30340084807400}, "additional": {"children": ["a0c05d68-de17-4478-a49e-e797c9d0ffd8", "489e8889-03b2-419f-8b86-8121b1956cdd", "0b8545eb-ee2d-4f34-b178-ee218f091f79", "4518c71d-f27c-4c70-b054-e553f92b8925", "e01e40f7-9d45-4886-b651-c44fa478863b", "865d04c9-83c6-4f3b-b340-9a2ac2d043c7", "3b9d80e5-ffb3-472c-a739-922ca313e249", "ab9ee6a3-edb3-4184-9c7c-a5e65d259692", "4573dd08-9d18-4cc9-9ca5-203ccb1a543e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a732c4dc-b827-4516-a5f8-9c17d8319ffa", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340084831100, "endTime": 30340086602500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "a1ef4e6f-592b-4e26-9468-d779e3d6ca55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96960350-e332-40ea-8e8b-06428c6b07f0", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340086606800, "endTime": 30340086608100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "c3c385dd-7cc4-4a43-b586-35e1f2949a53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7a0acd5-4e64-42b2-9875-386ab57a20ae", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339687514500, "endTime": 30339687553000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "15b49553-f6c8-43af-a811-cf8e54ad7123"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15b49553-f6c8-43af-a811-cf8e54ad7123", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339687514500, "endTime": 30339687553000}, "additional": {"logType": "info", "children": [], "durationId": "c7a0acd5-4e64-42b2-9875-386ab57a20ae", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "8c717c68-add2-425d-86cf-1da8e2e73fe2", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339693305800, "endTime": 30339693328700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "6b960d2a-3a2f-41c1-9398-d27a3c706f19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b960d2a-3a2f-41c1-9398-d27a3c706f19", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339693305800, "endTime": 30339693328700}, "additional": {"logType": "info", "children": [], "durationId": "8c717c68-add2-425d-86cf-1da8e2e73fe2", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "f2b0b3d8-788f-410d-895f-2db742fe0838", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339693437900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cb32882-05de-4833-b4a4-5696a76e2f7f", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339698768500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677337ac-d371-4324-8c34-0ab2e6905d6b", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339683752800, "endTime": 30339698885400}, "additional": {"logType": "info", "children": [], "durationId": "f3973713-f732-4630-8cb8-f857dfdf4dfa", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "a0c05d68-de17-4478-a49e-e797c9d0ffd8", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339704826200, "endTime": 30339704834400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "0588d42a-2d6f-492b-ac4b-7c678b4c4a31"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "489e8889-03b2-419f-8b86-8121b1956cdd", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339704849200, "endTime": 30339709331300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "a3a05300-0ebb-45f3-8a00-8db71385c29d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b8545eb-ee2d-4f34-b178-ee218f091f79", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339709350200, "endTime": 30339843437600}, "additional": {"children": ["b884a68e-d4b0-4368-9dbb-5d082abc4d2f", "a63e1a61-b216-4f56-aaf8-8aa1396543be", "11156f18-fd73-4d52-b6fc-7dfd05229678"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "d1b5a75b-26d8-471d-8b28-163d2386e8e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4518c71d-f27c-4c70-b054-e553f92b8925", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339843453400, "endTime": 30339874844100}, "additional": {"children": ["f916a041-897f-4c4a-bf00-64ad7fc5e425"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "a79852f2-ff4d-430f-9fa1-10ec2f4e940d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e01e40f7-9d45-4886-b651-c44fa478863b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339874852600, "endTime": 30340038487400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "7a0f01e3-57ef-45f3-a3fd-97ae96d3d5ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "865d04c9-83c6-4f3b-b340-9a2ac2d043c7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340040348700, "endTime": 30340070564900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "c615763c-6bd6-4a16-a7ad-958b1b201dfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b9d80e5-ffb3-472c-a739-922ca313e249", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340070606500, "endTime": 30340084616400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "9e8a2b83-bcf6-4230-82d7-19083325d2b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab9ee6a3-edb3-4184-9c7c-a5e65d259692", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340084639200, "endTime": 30340084796600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "5e24c40d-fd53-4997-8a9f-04baeee48306"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0588d42a-2d6f-492b-ac4b-7c678b4c4a31", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339704826200, "endTime": 30339704834400}, "additional": {"logType": "info", "children": [], "durationId": "a0c05d68-de17-4478-a49e-e797c9d0ffd8", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "a3a05300-0ebb-45f3-8a00-8db71385c29d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339704849200, "endTime": 30339709331300}, "additional": {"logType": "info", "children": [], "durationId": "489e8889-03b2-419f-8b86-8121b1956cdd", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "b884a68e-d4b0-4368-9dbb-5d082abc4d2f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339710094900, "endTime": 30339710114400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8545eb-ee2d-4f34-b178-ee218f091f79", "logId": "674c1911-5766-429d-8c64-108005efbcfb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "674c1911-5766-429d-8c64-108005efbcfb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339710094900, "endTime": 30339710114400}, "additional": {"logType": "info", "children": [], "durationId": "b884a68e-d4b0-4368-9dbb-5d082abc4d2f", "parent": "d1b5a75b-26d8-471d-8b28-163d2386e8e4"}}, {"head": {"id": "a63e1a61-b216-4f56-aaf8-8aa1396543be", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339714446400, "endTime": 30339842565200}, "additional": {"children": ["93a6c233-4472-4994-8de2-a06c17e4015e", "fc3cb9b0-5568-4346-a906-7c7676280855"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8545eb-ee2d-4f34-b178-ee218f091f79", "logId": "86e20b93-7261-479c-9411-97b7191a6f91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93a6c233-4472-4994-8de2-a06c17e4015e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339714448600, "endTime": 30339726364800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a63e1a61-b216-4f56-aaf8-8aa1396543be", "logId": "a792564a-708b-42d4-8ed7-a4334552b354"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc3cb9b0-5568-4346-a906-7c7676280855", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339726393900, "endTime": 30339842547700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a63e1a61-b216-4f56-aaf8-8aa1396543be", "logId": "a0790537-9b49-4f12-8629-18f2332a6296"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d475f546-b79e-49c3-9f3c-128b3d3c8b72", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339714478100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8ad0040-eba6-41e8-9236-b3b6cdd6a97c", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339726201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a792564a-708b-42d4-8ed7-a4334552b354", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339714448600, "endTime": 30339726364800}, "additional": {"logType": "info", "children": [], "durationId": "93a6c233-4472-4994-8de2-a06c17e4015e", "parent": "86e20b93-7261-479c-9411-97b7191a6f91"}}, {"head": {"id": "52ac747f-d5df-48fd-9377-fcefaed4411d", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339726407600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa0f280-a973-49ba-bd38-e31cb872002e", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339738570300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02b4faf8-8451-4561-aa6a-9efa8aa0afd3", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339738687100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85588337-373a-40c1-9b50-e8115787e2ca", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339738812600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4ad0324-0710-46a1-86a5-23ebf1364a88", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339739041800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6a31e75-96c0-40e9-99d9-41dc69eae6e9", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339742205000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f90e6f0c-deef-4ef1-ad29-d1b094556e34", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339752653600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12ced52a-a483-4102-80f7-11a475c9fb5a", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339782392100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9a9cb0a-70ef-4a84-87fc-4ee001a3d857", "name": "Sdk init in 67 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339820179500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e538a6ba-922e-46e8-9fe3-dfeb4fa9bfca", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339820337900}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 55}, "markType": "other"}}, {"head": {"id": "33535c6b-3efa-4dc8-8a10-396ffb432af5", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339820381500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 55}, "markType": "other"}}, {"head": {"id": "700d5aa2-1a28-4e72-a4d0-a1d0b2ae2cff", "name": "Project task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339842254500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a62caf2-c6ca-451b-813d-6ee0f6cc9a6a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339842374500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ed7293c-115f-408f-b0f1-e953dc86164c", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339842441200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff86822-d8e5-48bf-95af-47596feb421c", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339842492900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0790537-9b49-4f12-8629-18f2332a6296", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339726393900, "endTime": 30339842547700}, "additional": {"logType": "info", "children": [], "durationId": "fc3cb9b0-5568-4346-a906-7c7676280855", "parent": "86e20b93-7261-479c-9411-97b7191a6f91"}}, {"head": {"id": "86e20b93-7261-479c-9411-97b7191a6f91", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339714446400, "endTime": 30339842565200}, "additional": {"logType": "info", "children": ["a792564a-708b-42d4-8ed7-a4334552b354", "a0790537-9b49-4f12-8629-18f2332a6296"], "durationId": "a63e1a61-b216-4f56-aaf8-8aa1396543be", "parent": "d1b5a75b-26d8-471d-8b28-163d2386e8e4"}}, {"head": {"id": "11156f18-fd73-4d52-b6fc-7dfd05229678", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339843394400, "endTime": 30339843417700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0b8545eb-ee2d-4f34-b178-ee218f091f79", "logId": "bdd11746-751d-4bfc-8f2f-d91c9d94b62f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdd11746-751d-4bfc-8f2f-d91c9d94b62f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339843394400, "endTime": 30339843417700}, "additional": {"logType": "info", "children": [], "durationId": "11156f18-fd73-4d52-b6fc-7dfd05229678", "parent": "d1b5a75b-26d8-471d-8b28-163d2386e8e4"}}, {"head": {"id": "d1b5a75b-26d8-471d-8b28-163d2386e8e4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339709350200, "endTime": 30339843437600}, "additional": {"logType": "info", "children": ["674c1911-5766-429d-8c64-108005efbcfb", "86e20b93-7261-479c-9411-97b7191a6f91", "bdd11746-751d-4bfc-8f2f-d91c9d94b62f"], "durationId": "0b8545eb-ee2d-4f34-b178-ee218f091f79", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "f916a041-897f-4c4a-bf00-64ad7fc5e425", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339844467400, "endTime": 30339874830400}, "additional": {"children": ["115297af-fce1-4dba-a028-e033e7e9d5bc", "86181aa2-4e21-400c-9e2e-211eee9f2331", "1943b342-64b6-40b4-9d4e-1ce7ee598130"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4518c71d-f27c-4c70-b054-e553f92b8925", "logId": "04ab468b-50a8-4843-8a2e-ad17fca5f839"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "115297af-fce1-4dba-a028-e033e7e9d5bc", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339848170200, "endTime": 30339848189400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f916a041-897f-4c4a-bf00-64ad7fc5e425", "logId": "87d28220-8d3f-4212-8d0a-fbf94c329fb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87d28220-8d3f-4212-8d0a-fbf94c329fb3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339848170200, "endTime": 30339848189400}, "additional": {"logType": "info", "children": [], "durationId": "115297af-fce1-4dba-a028-e033e7e9d5bc", "parent": "04ab468b-50a8-4843-8a2e-ad17fca5f839"}}, {"head": {"id": "86181aa2-4e21-400c-9e2e-211eee9f2331", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339852081500, "endTime": 30339870050000}, "additional": {"children": ["ecb50a09-d54e-4fb2-9d9b-b439a3ccc983", "5ee88964-ae08-4c67-983c-700b95d5b0c8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f916a041-897f-4c4a-bf00-64ad7fc5e425", "logId": "f48a252a-a7c0-4e7d-b5b6-f41da47f73a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecb50a09-d54e-4fb2-9d9b-b439a3ccc983", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339852083900, "endTime": 30339856834000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86181aa2-4e21-400c-9e2e-211eee9f2331", "logId": "47088140-ae1a-48e6-a70b-379deb0b00d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ee88964-ae08-4c67-983c-700b95d5b0c8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339856857300, "endTime": 30339870033500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "86181aa2-4e21-400c-9e2e-211eee9f2331", "logId": "ee9b9570-cf5f-48da-aebf-90b00952f9f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7dd2398-6d98-4f03-a5e4-04dbc4541837", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339852089700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a565e3-1ef3-4f3c-ab3e-b7f6343c852b", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339856569500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47088140-ae1a-48e6-a70b-379deb0b00d0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339852083900, "endTime": 30339856834000}, "additional": {"logType": "info", "children": [], "durationId": "ecb50a09-d54e-4fb2-9d9b-b439a3ccc983", "parent": "f48a252a-a7c0-4e7d-b5b6-f41da47f73a3"}}, {"head": {"id": "1b8e461d-ca29-4829-a4af-15ac69f21c56", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339856891300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6951c7e-cbba-4764-a65e-5a254ff217d7", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339864935800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d04406-bb77-4cb8-9262-f702b994b2ff", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339865078900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd3d3cb-52c7-4bc1-b0bd-a327e9ef9b99", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339865333300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f1c6471-f372-49c0-9fe0-08440eec1db7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339865534200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74d05019-1183-4fc3-8cb9-ab4338862708", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339865618700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61221306-f207-4939-a313-53e5e8180f4c", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339865680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a2b4885-721c-4505-ac9c-9503d08e305b", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339865746200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4514dff-c4c6-4c0e-b35f-50aba026dc51", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339869269400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92bb25cf-bf8d-4ebb-a932-f2dda98a771c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339869716200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8a9238-dfb2-455c-b77e-fde205f9a32a", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339869893500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2c48e9-a3ee-4425-93b1-31dd4e92d2b8", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339869979500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee9b9570-cf5f-48da-aebf-90b00952f9f3", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339856857300, "endTime": 30339870033500}, "additional": {"logType": "info", "children": [], "durationId": "5ee88964-ae08-4c67-983c-700b95d5b0c8", "parent": "f48a252a-a7c0-4e7d-b5b6-f41da47f73a3"}}, {"head": {"id": "f48a252a-a7c0-4e7d-b5b6-f41da47f73a3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339852081500, "endTime": 30339870050000}, "additional": {"logType": "info", "children": ["47088140-ae1a-48e6-a70b-379deb0b00d0", "ee9b9570-cf5f-48da-aebf-90b00952f9f3"], "durationId": "86181aa2-4e21-400c-9e2e-211eee9f2331", "parent": "04ab468b-50a8-4843-8a2e-ad17fca5f839"}}, {"head": {"id": "1943b342-64b6-40b4-9d4e-1ce7ee598130", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339874786500, "endTime": 30339874807000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f916a041-897f-4c4a-bf00-64ad7fc5e425", "logId": "561d5a60-56c6-4a03-8235-24c6a25b638b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "561d5a60-56c6-4a03-8235-24c6a25b638b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339874786500, "endTime": 30339874807000}, "additional": {"logType": "info", "children": [], "durationId": "1943b342-64b6-40b4-9d4e-1ce7ee598130", "parent": "04ab468b-50a8-4843-8a2e-ad17fca5f839"}}, {"head": {"id": "04ab468b-50a8-4843-8a2e-ad17fca5f839", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339844467400, "endTime": 30339874830400}, "additional": {"logType": "info", "children": ["87d28220-8d3f-4212-8d0a-fbf94c329fb3", "f48a252a-a7c0-4e7d-b5b6-f41da47f73a3", "561d5a60-56c6-4a03-8235-24c6a25b638b"], "durationId": "f916a041-897f-4c4a-bf00-64ad7fc5e425", "parent": "a79852f2-ff4d-430f-9fa1-10ec2f4e940d"}}, {"head": {"id": "a79852f2-ff4d-430f-9fa1-10ec2f4e940d", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339843453400, "endTime": 30339874844100}, "additional": {"logType": "info", "children": ["04ab468b-50a8-4843-8a2e-ad17fca5f839"], "durationId": "4518c71d-f27c-4c70-b054-e553f92b8925", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "be5102ea-78a5-4938-8fe2-47d2cdab21d7", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339902427400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f946c14d-f8f7-476c-9ad4-357c63145ad1", "name": "hvigorfile, resolve hvigorfile dependencies in 164 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340038259800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a0f01e3-57ef-45f3-a3fd-97ae96d3d5ba", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339874852600, "endTime": 30340038487400}, "additional": {"logType": "info", "children": [], "durationId": "e01e40f7-9d45-4886-b651-c44fa478863b", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "4573dd08-9d18-4cc9-9ca5-203ccb1a543e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340040089600, "endTime": 30340040330900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "logId": "8885359b-e751-45a3-a677-04804d78ee7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe9620fc-7608-4622-901c-7c88703b6a14", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340040123100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8885359b-e751-45a3-a677-04804d78ee7a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340040089600, "endTime": 30340040330900}, "additional": {"logType": "info", "children": [], "durationId": "4573dd08-9d18-4cc9-9ca5-203ccb1a543e", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "309ba3aa-cf55-4031-9389-fc5c283e9c47", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340041857900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7ef20c-3aca-4151-a16b-79aad20f3e28", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340069652600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c615763c-6bd6-4a16-a7ad-958b1b201dfd", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340040348700, "endTime": 30340070564900}, "additional": {"logType": "info", "children": [], "durationId": "865d04c9-83c6-4f3b-b340-9a2ac2d043c7", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "9d2f9557-2bc2-40db-b62f-f82877364960", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340076869900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9059657-777d-436d-940b-b80b83612d86", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340077269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33a93c2e-3b9e-4a65-8c9c-397e6b29c079", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340080175000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42ecafa4-6d46-4ad6-9dab-8ec9d717560f", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340080299900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8a2b83-bcf6-4230-82d7-19083325d2b1", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340070606500, "endTime": 30340084616400}, "additional": {"logType": "info", "children": [], "durationId": "3b9d80e5-ffb3-472c-a739-922ca313e249", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "7bc325d8-dc87-4680-af9d-8448d497eb4a", "name": "Configuration phase cost:380 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340084667200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e24c40d-fd53-4997-8a9f-04baeee48306", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340084639200, "endTime": 30340084796600}, "additional": {"logType": "info", "children": [], "durationId": "ab9ee6a3-edb3-4184-9c7c-a5e65d259692", "parent": "408d8c09-6bd8-4b9c-b46c-949f5f83860f"}}, {"head": {"id": "408d8c09-6bd8-4b9c-b46c-949f5f83860f", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339698902500, "endTime": 30340084807400}, "additional": {"logType": "info", "children": ["0588d42a-2d6f-492b-ac4b-7c678b4c4a31", "a3a05300-0ebb-45f3-8a00-8db71385c29d", "d1b5a75b-26d8-471d-8b28-163d2386e8e4", "a79852f2-ff4d-430f-9fa1-10ec2f4e940d", "7a0f01e3-57ef-45f3-a3fd-97ae96d3d5ba", "c615763c-6bd6-4a16-a7ad-958b1b201dfd", "9e8a2b83-bcf6-4230-82d7-19083325d2b1", "5e24c40d-fd53-4997-8a9f-04baeee48306", "8885359b-e751-45a3-a677-04804d78ee7a"], "durationId": "e7c5545f-00c2-47b6-98ab-821bc0739a36", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "313bb57b-28f4-4665-be6e-54367181829d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340086566700, "endTime": 30340086586700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af58a61e-634a-431d-8cf7-258580e7bc30", "logId": "209fbc2d-c1aa-4bbf-9a32-b809bf4f7ef6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "209fbc2d-c1aa-4bbf-9a32-b809bf4f7ef6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340086566700, "endTime": 30340086586700}, "additional": {"logType": "info", "children": [], "durationId": "313bb57b-28f4-4665-be6e-54367181829d", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "a1ef4e6f-592b-4e26-9468-d779e3d6ca55", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340084831100, "endTime": 30340086602500}, "additional": {"logType": "info", "children": [], "durationId": "a732c4dc-b827-4516-a5f8-9c17d8319ffa", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "c3c385dd-7cc4-4a43-b586-35e1f2949a53", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340086606800, "endTime": 30340086608100}, "additional": {"logType": "info", "children": [], "durationId": "96960350-e332-40ea-8e8b-06428c6b07f0", "parent": "c3a24b3c-157e-4f28-85b6-d53a8f34c066"}}, {"head": {"id": "c3a24b3c-157e-4f28-85b6-d53a8f34c066", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339683750300, "endTime": 30340086611000}, "additional": {"logType": "info", "children": ["677337ac-d371-4324-8c34-0ab2e6905d6b", "408d8c09-6bd8-4b9c-b46c-949f5f83860f", "a1ef4e6f-592b-4e26-9468-d779e3d6ca55", "c3c385dd-7cc4-4a43-b586-35e1f2949a53", "15b49553-f6c8-43af-a811-cf8e54ad7123", "6b960d2a-3a2f-41c1-9398-d27a3c706f19", "209fbc2d-c1aa-4bbf-9a32-b809bf4f7ef6"], "durationId": "af58a61e-634a-431d-8cf7-258580e7bc30"}}, {"head": {"id": "32578894-9b42-4655-997e-5bf867afc379", "name": "Configuration task cost before running: 407 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340086796500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e2dda1-4b90-4c01-a96a-3aa611b16d19", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340098392900, "endTime": 30340113777000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "eaaa3c30-9eae-4063-b8fe-4f821c5d32f7", "logId": "9321e79b-a27e-4579-bde8-c46cb804dfe1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaaa3c30-9eae-4063-b8fe-4f821c5d32f7", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340093026600}, "additional": {"logType": "detail", "children": [], "durationId": "55e2dda1-4b90-4c01-a96a-3aa611b16d19"}}, {"head": {"id": "5fd9b4fd-c9c1-4927-b43b-aaac66790d6a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340095023000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a23a940-9765-4b8b-860f-a51d8aef9738", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340095630600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12b17f1b-711c-4748-8e9a-ade5a3f9ed10", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340098407500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9969d078-9aa6-4284-8933-bb33d60768a9", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340113418300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e41d1c2-9d29-462c-b56c-fa906c237c4f", "name": "entry : default@PreBuild cost memory 0.30854034423828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340113654200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9321e79b-a27e-4579-bde8-c46cb804dfe1", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340098392900, "endTime": 30340113777000}, "additional": {"logType": "info", "children": [], "durationId": "55e2dda1-4b90-4c01-a96a-3aa611b16d19"}}, {"head": {"id": "856f32de-b39d-47af-9f50-9d2cc50b3e8e", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340125558600, "endTime": 30340129813300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "59d777ca-f905-40ab-81a3-8f41743c7c49", "logId": "353fb821-4a92-4893-8c6b-11096425a808"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59d777ca-f905-40ab-81a3-8f41743c7c49", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340121608900}, "additional": {"logType": "detail", "children": [], "durationId": "856f32de-b39d-47af-9f50-9d2cc50b3e8e"}}, {"head": {"id": "28364e22-d788-4df4-899a-90905a718bc6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340122229900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8fa95ff-7e69-43a2-af21-32f122f4bee0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340122553500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47f9a952-4638-41f1-b2d4-bc8240d3a133", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340125573800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa4b36f-b733-4570-b5bc-b05ebc55364b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340128345700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00659f63-876b-4e7c-b345-c167c7e9ac90", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340129372100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "add6748c-4b67-4241-be00-6032093c4771", "name": "entry : default@GenerateMetadata cost memory 0.09578704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340129710700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "353fb821-4a92-4893-8c6b-11096425a808", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340125558600, "endTime": 30340129813300}, "additional": {"logType": "info", "children": [], "durationId": "856f32de-b39d-47af-9f50-9d2cc50b3e8e"}}, {"head": {"id": "10db0fea-6288-484e-8a89-bb3baa0809e9", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132659100, "endTime": 30340133396100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9b2cca16-8cdc-46ad-83ea-25a395718e41", "logId": "3a6dbfde-9382-4bce-8971-8e7793e8f8e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b2cca16-8cdc-46ad-83ea-25a395718e41", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340131762800}, "additional": {"logType": "detail", "children": [], "durationId": "10db0fea-6288-484e-8a89-bb3baa0809e9"}}, {"head": {"id": "e0c020dd-af6c-443f-bd2c-149a8eb72c26", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132278900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c40b48d-f671-452b-95ae-541dc072c327", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132396500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40ab282-e9ac-4c78-a95e-1f8afa48d1f2", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132673800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1147225e-62c0-4fd2-8916-9ddd4511dabc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132800500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58a9e6c6-33bf-4f60-b698-41e43246257e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132908100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f9d582-a48b-48da-b54c-3ef411337640", "name": "entry : default@ConfigureCmake cost memory 0.0366058349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340133209700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b1132b-b070-457c-a807-d697876c46f4", "name": "runTaskFromQueue task cost before running: 453 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340133327000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6dbfde-9382-4bce-8971-8e7793e8f8e4", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340132659100, "endTime": 30340133396100, "totalTime": 645900}, "additional": {"logType": "info", "children": [], "durationId": "10db0fea-6288-484e-8a89-bb3baa0809e9"}}, {"head": {"id": "034b63c0-ae4b-406e-8be2-289c3a1a1418", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340137704300, "endTime": 30340142058300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f23ac6af-db06-4959-9d4b-fc6df62f298b", "logId": "e8204bb2-195a-49bc-a6a4-e9ae86770b75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f23ac6af-db06-4959-9d4b-fc6df62f298b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340135276100}, "additional": {"logType": "detail", "children": [], "durationId": "034b63c0-ae4b-406e-8be2-289c3a1a1418"}}, {"head": {"id": "39e41ecc-d9eb-4706-979d-32abe4f9681d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340135789300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d693605-1729-4306-8d61-d5b0cf92f83f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340136032400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00bfb88b-3fc5-4384-bd04-abe4aac1d759", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340137724200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cd3b0f6-4e45-4398-9d5b-955133954e33", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340141839800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f33a3f12-39cb-40f4-941b-1c5801c7dbe2", "name": "entry : default@MergeProfile cost memory 0.1067047119140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340141987800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8204bb2-195a-49bc-a6a4-e9ae86770b75", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340137704300, "endTime": 30340142058300}, "additional": {"logType": "info", "children": [], "durationId": "034b63c0-ae4b-406e-8be2-289c3a1a1418"}}, {"head": {"id": "061df337-b206-4857-ae4f-cd480416b0c6", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340146978400, "endTime": 30340149135800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f5aa4bd5-cefa-443c-8b69-ac8e45c14596", "logId": "a562705f-c2da-4922-a32d-88ee203840ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5aa4bd5-cefa-443c-8b69-ac8e45c14596", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340145127000}, "additional": {"logType": "detail", "children": [], "durationId": "061df337-b206-4857-ae4f-cd480416b0c6"}}, {"head": {"id": "66f8dffb-1576-434c-af9c-f89864518412", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340145619200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c26aba1-048b-4170-ae2d-7680607dadb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340145746400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab0d350-bdbf-44c4-8b09-09852bb8079e", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340146997500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb00cdcb-50e1-40ef-b1b4-c6d427762435", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340147840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcdcb1eb-a758-49b0-881b-0d1c1d61cd20", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340148939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c5b90ee-23d1-4a5b-95ce-0d29afa6336c", "name": "entry : default@CreateBuildProfile cost memory 0.1044921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340149058100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a562705f-c2da-4922-a32d-88ee203840ce", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340146978400, "endTime": 30340149135800}, "additional": {"logType": "info", "children": [], "durationId": "061df337-b206-4857-ae4f-cd480416b0c6"}}, {"head": {"id": "759608cd-4ca1-4228-9b38-b2c310af3480", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340151954800, "endTime": 30340152667500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "d1bb146c-7798-473b-b04b-7f8e8a538357", "logId": "10b4d8e5-172b-4926-8205-221cb2d27998"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1bb146c-7798-473b-b04b-7f8e8a538357", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340150813300}, "additional": {"logType": "detail", "children": [], "durationId": "759608cd-4ca1-4228-9b38-b2c310af3480"}}, {"head": {"id": "77659124-4fa9-4ab1-a1b5-31ffda1b3de0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340151175300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca7ce532-d53c-4acc-9b7d-5653616315ee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340151278800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ddd5517-c269-437a-86a5-1c235dc9e3b0", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340151974600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740a764a-b3d3-4f70-9148-f6512211d6ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340152248700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3196fa1-b088-4ce5-a8cf-be7a5f7d41ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340152356000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "335dd379-7322-47ad-b679-e272b31b166c", "name": "entry : default@PreCheckSyscap cost memory 0.03688812255859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340152506200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b093a3b-453f-45a7-8408-85564522d1ed", "name": "runTaskFromQueue task cost before running: 473 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340152608100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b4d8e5-172b-4926-8205-221cb2d27998", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340151954800, "endTime": 30340152667500, "totalTime": 663500}, "additional": {"logType": "info", "children": [], "durationId": "759608cd-4ca1-4228-9b38-b2c310af3480"}}, {"head": {"id": "59c47c60-013d-4b66-ab82-3ac5642851d3", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340158545400, "endTime": 30340159402200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d7339144-6b6a-4209-988c-13125d1b0d6e", "logId": "33fe83d5-9715-429c-8e80-4ed0882b73ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7339144-6b6a-4209-988c-13125d1b0d6e", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340154312100}, "additional": {"logType": "detail", "children": [], "durationId": "59c47c60-013d-4b66-ab82-3ac5642851d3"}}, {"head": {"id": "c5c032f9-8ac5-46b4-bb82-1ff6adddaebf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340154653900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4b46ab0-edc9-40f3-90aa-97f0b1c9d013", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340154808000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c7f8ca-0136-4f00-aeab-32492fe169c0", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340158559100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90316b86-d58c-4eaf-98dd-ade2c3d5c49c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340158765900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59d34b8d-b503-480c-8642-4e510d2c02bb", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03916168212890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340159192700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d2ac5e-f5d4-480e-940e-0373b16515d8", "name": "runTaskFromQueue task cost before running: 479 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340159330300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33fe83d5-9715-429c-8e80-4ed0882b73ec", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340158545400, "endTime": 30340159402200, "totalTime": 758300}, "additional": {"logType": "info", "children": [], "durationId": "59c47c60-013d-4b66-ab82-3ac5642851d3"}}, {"head": {"id": "afa5d0ea-42eb-440f-a39c-b1b7a133d202", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340165188600, "endTime": 30340167557900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "41b5942d-0971-41ff-aa0b-7320dd3eb1b5", "logId": "ccfdf55d-de0b-44ca-9fdf-7249a54cc504"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41b5942d-0971-41ff-aa0b-7320dd3eb1b5", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340162468400}, "additional": {"logType": "detail", "children": [], "durationId": "afa5d0ea-42eb-440f-a39c-b1b7a133d202"}}, {"head": {"id": "26784aa2-02e0-4f7f-907b-bae5dd668bd7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340163062800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9175c8d9-3125-4bd5-af4c-19ed28859797", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340163186600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50c28cd6-600e-43ce-b119-652a8f6a987c", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340165199800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d771a89-5424-4331-9f20-424948412d77", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340166623700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a2e36c4-a7d4-4928-a92a-3d40693ff2fe", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340166746600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a5efba-a4b8-4c4d-87f4-cd92342cda84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340166835600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5240f7fa-ac93-4232-88b7-ce7d574c84e6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340166909600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b0b4e64-64ec-44f6-8c3a-8b9efc171aee", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1175079345703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340167151900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d186724-7aa0-4983-9efe-3f19764f23ab", "name": "runTaskFromQueue task cost before running: 487 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340167285200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccfdf55d-de0b-44ca-9fdf-7249a54cc504", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340165188600, "endTime": 30340167557900, "totalTime": 2066900}, "additional": {"logType": "info", "children": [], "durationId": "afa5d0ea-42eb-440f-a39c-b1b7a133d202"}}, {"head": {"id": "7be8a6a9-201e-481f-ac89-072f60ceb945", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340171482700, "endTime": 30340173673000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "19f334f2-1418-4a41-a4e2-9f216053b63e", "logId": "f1faf298-0a91-4d4f-bcb7-34002b539ec3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19f334f2-1418-4a41-a4e2-9f216053b63e", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340170271300}, "additional": {"logType": "detail", "children": [], "durationId": "7be8a6a9-201e-481f-ac89-072f60ceb945"}}, {"head": {"id": "1ecc2820-2d9e-4b0a-8da6-e34a1a2c5cf0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340170599800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afe09bed-9f5b-4862-b18e-c65994d4b482", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340170698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "494bf8e5-325a-41b8-9a65-d23103090afe", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340171495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "382f027d-96f6-4350-8325-5a58c237db50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340171622000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85438b6f-3aa6-4774-a0ba-1271a3987694", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340171679500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5345765-e0c6-42f4-899e-4703373eaaf5", "name": "entry : default@BuildNativeWithCmake cost memory -4.929237365722656", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340173473500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "174aa57a-8792-4ba0-948a-3ccb340c1733", "name": "runTaskFromQueue task cost before running: 494 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340173610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1faf298-0a91-4d4f-bcb7-34002b539ec3", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340171482700, "endTime": 30340173673000, "totalTime": 2103800}, "additional": {"logType": "info", "children": [], "durationId": "7be8a6a9-201e-481f-ac89-072f60ceb945"}}, {"head": {"id": "e26aeb45-40f4-46e6-a1cf-161453dae17b", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340178382900, "endTime": 30340181257900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b4d0eceb-3ad4-4363-815a-3c77529660d4", "logId": "74be35d5-a3ab-4090-8ab5-7d989702378e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4d0eceb-3ad4-4363-815a-3c77529660d4", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340175938900}, "additional": {"logType": "detail", "children": [], "durationId": "e26aeb45-40f4-46e6-a1cf-161453dae17b"}}, {"head": {"id": "e98a5cd5-0060-4d5c-985c-f07123121658", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340176566500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d41e4019-b66c-48af-b708-35ed060af3bc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340176705300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f58146-83a1-4e89-85cd-5d5910ff2255", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340178395400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33db974c-35cd-4906-a715-b3ef8001c6cc", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340181019500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b37bfd3f-02f3-4237-b817-f1c2c955de62", "name": "entry : default@MakePackInfo cost memory 0.13950347900390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340181179100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74be35d5-a3ab-4090-8ab5-7d989702378e", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340178382900, "endTime": 30340181257900}, "additional": {"logType": "info", "children": [], "durationId": "e26aeb45-40f4-46e6-a1cf-161453dae17b"}}, {"head": {"id": "17d85029-2972-4a47-8afc-9ac8e897c906", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340185351700, "endTime": 30340188922100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "3e4c8f2d-b617-4382-a496-36dd730a89c7", "logId": "e62b84cf-5c10-4c11-8554-fbc5ba5dc578"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e4c8f2d-b617-4382-a496-36dd730a89c7", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340183582600}, "additional": {"logType": "detail", "children": [], "durationId": "17d85029-2972-4a47-8afc-9ac8e897c906"}}, {"head": {"id": "391213aa-a3b3-406f-870d-3d75ec353d94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340184004600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6d42ed-6e2d-431d-8f0c-d3e7a4bdeada", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340184135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "481d16f1-fcf1-4dee-8023-24183cbedf30", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340185364000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "831150b7-9585-4e4e-9f20-a6db1edd736e", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340185510500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5926a360-ea6c-46c8-ac2f-81720c298afd", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340186481200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f854b78-1757-4ae2-bff7-b95cce12b07e", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340188307000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a0afac8-f75f-4458-9a23-393e05e4759c", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340188488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6388e690-2b0b-4701-967f-69d79989d17f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340188594400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6c46252-894d-43fc-9cba-91cd8656c75f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340188674300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "044a84f0-5f54-4502-83ef-f35dd5c5545b", "name": "entry : default@SyscapTransform cost memory 0.15413665771484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340188777400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a70f32c1-2715-4d4c-955e-1ba6888066ec", "name": "runTaskFromQueue task cost before running: 509 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340188862700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e62b84cf-5c10-4c11-8554-fbc5ba5dc578", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340185351700, "endTime": 30340188922100, "totalTime": 3494100}, "additional": {"logType": "info", "children": [], "durationId": "17d85029-2972-4a47-8afc-9ac8e897c906"}}, {"head": {"id": "e97614df-1593-4f50-be24-216c2d0adf6f", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340192915800, "endTime": 30340194292500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "2507e26e-1faf-466c-9889-8e2281e44f0a", "logId": "fa2ac506-4f41-4cd3-afa4-48fe201baa0d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2507e26e-1faf-466c-9889-8e2281e44f0a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340191295900}, "additional": {"logType": "detail", "children": [], "durationId": "e97614df-1593-4f50-be24-216c2d0adf6f"}}, {"head": {"id": "c9668750-bc25-49f0-a303-f243f629d2f0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340191652100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a6ba34-ad93-4877-bee0-9fc3bce90ef9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340191755400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d4d1b6-5a52-4061-896a-064b2d456553", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340192929200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf315f4e-3a8b-4217-bff8-e944cfcd00e4", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340194098300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0a11437-a067-40cc-a5e7-7a688471d0b0", "name": "entry : default@ProcessProfile cost memory 0.0609283447265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340194222100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa2ac506-4f41-4cd3-afa4-48fe201baa0d", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340192915800, "endTime": 30340194292500}, "additional": {"logType": "info", "children": [], "durationId": "e97614df-1593-4f50-be24-216c2d0adf6f"}}, {"head": {"id": "60636af3-b0d9-4f0a-b75d-4367b1464049", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340197867200, "endTime": 30340201762700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "08d06a79-573b-4144-b572-8459f11c4652", "logId": "ed092e63-c372-4c52-9433-6766c0064fca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08d06a79-573b-4144-b572-8459f11c4652", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340195766500}, "additional": {"logType": "detail", "children": [], "durationId": "60636af3-b0d9-4f0a-b75d-4367b1464049"}}, {"head": {"id": "6218c8a4-507b-4922-b30f-92d359037662", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340196114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee219526-6e10-4a58-8ff7-03a32e07b99e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340196215200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78c3a47-b7ea-48e5-90dd-0fcb04adcc2c", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340197878300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d2f130-4e83-49ff-9add-df39ac895155", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340201559600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b5e9b14-9335-4cb1-a89f-f1f244247370", "name": "entry : default@ProcessRouterMap cost memory 0.20322418212890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340201692800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed092e63-c372-4c52-9433-6766c0064fca", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340197867200, "endTime": 30340201762700}, "additional": {"logType": "info", "children": [], "durationId": "60636af3-b0d9-4f0a-b75d-4367b1464049"}}, {"head": {"id": "fdfe9d65-efba-4f26-9579-92d71cb26901", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340206222300, "endTime": 30340207541100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5b148447-91b3-42f0-8528-db8ab6bd83d2", "logId": "10440934-fffe-487a-8d22-890738cde070"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b148447-91b3-42f0-8528-db8ab6bd83d2", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340204448200}, "additional": {"logType": "detail", "children": [], "durationId": "fdfe9d65-efba-4f26-9579-92d71cb26901"}}, {"head": {"id": "a96506d9-6570-433b-ae20-37cf1eba1d8c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340204811700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d9d9df-1737-4746-b832-03407851bfc4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340204969600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a4e9462-4a56-430e-a8e5-1f1a2ba1d2c0", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340206236000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf68f761-1f2a-45e1-a69b-3be6e9511c2d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340206376600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b41cffa7-b8ac-4d94-b8b7-3813af5cd340", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340206443000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc1290c-fb0d-464e-88d3-c3e62834c230", "name": "entry : default@BuildNativeWithNinja cost memory 0.05709075927734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340207255400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a67b4dc-0db7-4db8-9072-0e4cb0d0ada6", "name": "runTaskFromQueue task cost before running: 528 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340207451100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10440934-fffe-487a-8d22-890738cde070", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340206222300, "endTime": 30340207541100, "totalTime": 1179000}, "additional": {"logType": "info", "children": [], "durationId": "fdfe9d65-efba-4f26-9579-92d71cb26901"}}, {"head": {"id": "6b4e956e-09bd-403c-bd5a-b379baf392f6", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340216205200, "endTime": 30340222391000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5690a11f-dd59-47a0-97cf-d48180e8e04d", "logId": "b193d0c2-59d0-40a7-bbb0-b99cbeee4f4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5690a11f-dd59-47a0-97cf-d48180e8e04d", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340212339100}, "additional": {"logType": "detail", "children": [], "durationId": "6b4e956e-09bd-403c-bd5a-b379baf392f6"}}, {"head": {"id": "b58d11de-0c6a-46b7-b4ee-7097c8537506", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340213600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0457b18c-462d-4c10-83e0-bb87799dbf6a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340213740100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d77a6d-c46c-4c44-bf82-776b5c40b40b", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340214972400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64959c5d-ffbe-46dd-9062-74a4fab472d3", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340218072200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48125e36-7738-430c-87ff-0dd1bbe9cebe", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340220269700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e951d02-e056-4bd9-9b4a-bac30162adcf", "name": "entry : default@ProcessResource cost memory 0.16991424560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340220419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b193d0c2-59d0-40a7-bbb0-b99cbeee4f4c", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340216205200, "endTime": 30340222391000}, "additional": {"logType": "info", "children": [], "durationId": "6b4e956e-09bd-403c-bd5a-b379baf392f6"}}, {"head": {"id": "a14504be-0902-459c-ab37-32f682620117", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340232035000, "endTime": 30340260754500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2f9993e0-440d-468d-96b6-19e6a14e51ee", "logId": "5bcd46e8-b3e8-4f11-bd8b-8077dc154ef0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f9993e0-440d-468d-96b6-19e6a14e51ee", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340228090600}, "additional": {"logType": "detail", "children": [], "durationId": "a14504be-0902-459c-ab37-32f682620117"}}, {"head": {"id": "6ab764c8-1837-4c4c-a06d-47491459a7ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340228635800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c15aa9d-5834-479e-a987-8e9303bfbef3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340228753700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "990ca381-454c-4862-a527-a033afbeafa8", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340232046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1859466-8b4f-4809-9285-499c1373981a", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340260325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819df7d9-b213-420d-8e0b-3fc6aaab021a", "name": "entry : default@GenerateLoaderJson cost memory 0.9971694946289062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340260624700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bcd46e8-b3e8-4f11-bd8b-8077dc154ef0", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340232035000, "endTime": 30340260754500}, "additional": {"logType": "info", "children": [], "durationId": "a14504be-0902-459c-ab37-32f682620117"}}, {"head": {"id": "aabaf288-22ce-4d2d-a260-f1a0d2a62292", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340270286100, "endTime": 30340273675300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "408ab398-e139-42ac-955e-5c190fc65944", "logId": "a0c9582f-350b-4a27-a355-d70d41e56fb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "408ab398-e139-42ac-955e-5c190fc65944", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340268345600}, "additional": {"logType": "detail", "children": [], "durationId": "aabaf288-22ce-4d2d-a260-f1a0d2a62292"}}, {"head": {"id": "740b5090-bff6-4f60-a8cd-5d2c377a48b2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340268848400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4798950b-e806-442d-a103-75cf23111593", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340269249900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8805707e-897d-43c1-b494-32158e57e16c", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340270335100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3642ffd-c08b-4213-ab8d-c63987294ece", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340272636600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d968f1-e840-46d7-bea4-521358481099", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340272746700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c2d494b-67af-4ef7-b67b-3b1d35a92a94", "name": "entry : default@ProcessLibs cost memory 0.1260223388671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340273474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d358d8a-adc8-4210-868a-3e8b1a5c9235", "name": "runTaskFromQueue task cost before running: 594 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340273606400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0c9582f-350b-4a27-a355-d70d41e56fb9", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340270286100, "endTime": 30340273675300, "totalTime": 3296400}, "additional": {"logType": "info", "children": [], "durationId": "aabaf288-22ce-4d2d-a260-f1a0d2a62292"}}, {"head": {"id": "50a8f4b3-a154-4fb4-93f1-df2a71b79193", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340283790400, "endTime": 30340314852500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5bb3918a-5619-46a6-87e8-3b64fc77566f", "logId": "7006a3db-9c79-4a10-9cda-b22e95162af7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5bb3918a-5619-46a6-87e8-3b64fc77566f", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340276355900}, "additional": {"logType": "detail", "children": [], "durationId": "50a8f4b3-a154-4fb4-93f1-df2a71b79193"}}, {"head": {"id": "8d9cd139-1b21-45bf-83f7-f1b36828dd2e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340276979700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "461ab552-12df-4f8e-a081-d4eec81eb6b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340277380600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "276faed1-5251-4361-8df3-ce2067b70e6c", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340278853500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1670552e-0a7e-4e90-b11f-6c736d550631", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340283824600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa31003-3dd1-4ca3-9f9e-2cf408c234b5", "name": "Incremental task entry:default@CompileResource pre-execution cost: 30 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340314566300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def1d6c7-394c-41bd-8478-882b7e1a20e4", "name": "entry : default@CompileResource cost memory 1.4132232666015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340314743200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7006a3db-9c79-4a10-9cda-b22e95162af7", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340283790400, "endTime": 30340314852500}, "additional": {"logType": "info", "children": [], "durationId": "50a8f4b3-a154-4fb4-93f1-df2a71b79193"}}, {"head": {"id": "2f15fcc8-3ce8-43c5-8b54-edf8bcfe260f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340322847000, "endTime": 30340324773800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d82bf7db-510f-4510-914f-e6f8a4b6f134", "logId": "f591b839-d8bd-46d3-b5ca-c00e832450be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d82bf7db-510f-4510-914f-e6f8a4b6f134", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340319648400}, "additional": {"logType": "detail", "children": [], "durationId": "2f15fcc8-3ce8-43c5-8b54-edf8bcfe260f"}}, {"head": {"id": "380fbdf9-62c9-454c-a8fa-2978b52a530d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340320112000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e037b4f7-745b-4907-8fd5-c27bfb4906e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340320231300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f763ae-1cdf-4235-bdd5-9f60d0031d29", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340322860500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81e4b2b7-995b-4c37-b019-2856d47bef28", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340323278200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b7502e4-ae21-470b-8fe1-24385b758c41", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340324477700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdd5f992-7f59-4dd3-9f33-224d9d4299a2", "name": "entry : default@DoNativeStrip cost memory 0.07745361328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340324678600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f591b839-d8bd-46d3-b5ca-c00e832450be", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340322847000, "endTime": 30340324773800}, "additional": {"logType": "info", "children": [], "durationId": "2f15fcc8-3ce8-43c5-8b54-edf8bcfe260f"}}, {"head": {"id": "2644ca09-0df6-4e03-a24c-a21dcf6396c9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340334613800, "endTime": 30341961442500}, "additional": {"children": ["61201672-0f8b-46c9-9736-ea0c2c13f725", "d86e17e3-bbf4-4d8a-957e-481894dbe27a"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "5df240dd-5005-41dd-928c-be44657de679", "logId": "bdf06386-9438-415a-80cf-1a4fb008206c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5df240dd-5005-41dd-928c-be44657de679", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340326551200}, "additional": {"logType": "detail", "children": [], "durationId": "2644ca09-0df6-4e03-a24c-a21dcf6396c9"}}, {"head": {"id": "a93a00be-9eda-4556-b62c-13b092fcf378", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340327361000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ffdc601-9e68-4eed-a8ab-0175de77dc06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340327607400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d719500d-40d6-4c65-bdfb-0f1e412df607", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340334628400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ebbbc75-71ce-4d56-a82b-527c3b16b6e9", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340363822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa4ce97-6969-4d39-a005-e3f3b640f462", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 25 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340364049600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c84fa06d-035b-427f-bacb-fc25d7ef980d", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340378099800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1a354e0-9e18-4f10-a731-e17971b2ac01", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340379093500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19ce65c5-a47f-44de-9e8a-c8303bfaec92", "name": "default@CompileArkTS work[48] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340380217000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61201672-0f8b-46c9-9736-ea0c2c13f725", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30340593864300, "endTime": 30341711104700}, "additional": {"children": ["0ef91b36-103f-42f7-9fe5-c62296c1927b", "0cde70e3-86bf-45a3-90a1-ba5d28a26e36", "8737853e-76a2-4f40-8d46-06023b06da5e", "63406a13-bb08-45bf-804a-b13ca4a7ffad", "7c07f961-1d5e-4d37-a9d2-2c35470a5dc3", "b5d1b512-098b-499a-8da2-5bd707b74eaa"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2644ca09-0df6-4e03-a24c-a21dcf6396c9", "logId": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e1199a4-ca56-4e47-8e66-568af9676c09", "name": "default@CompileArkTS work[48] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340381165200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ecdc55-fc71-4aeb-a436-7c4bf1544be3", "name": "default@CompileArkTS work[48] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340381303000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c52379c7-6f39-4df0-99fb-d635b7d95e62", "name": "CopyResources startTime: 30340381402900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340381406300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d599025c-a87e-4641-be25-b3b9c9af90bd", "name": "default@CompileArkTS work[49] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340381580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86e17e3-bbf4-4d8a-957e-481894dbe27a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30341950065700, "endTime": 30341960953500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2644ca09-0df6-4e03-a24c-a21dcf6396c9", "logId": "c2914bf1-acf5-407c-9a69-d855cf77ea4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc02198c-b0ab-4c8f-a3d1-084d6712e67c", "name": "default@CompileArkTS work[49] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340382295400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "043aa795-f5c7-49c1-88d1-7850915075c6", "name": "default@CompileArkTS work[49] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340382377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a8d2d6-c36b-447f-b858-e8db0c97d64a", "name": "entry : default@CompileArkTS cost memory 1.603179931640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340382501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2cb46c-88e8-4c5e-883d-0c5b4e2ec0e4", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340387673000, "endTime": 30340390951600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "a65769ad-f109-4459-b5a8-ceba3352db9a", "logId": "8849a0d1-8bc4-4ff2-94ef-f125441c5b72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a65769ad-f109-4459-b5a8-ceba3352db9a", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340383830000}, "additional": {"logType": "detail", "children": [], "durationId": "bd2cb46c-88e8-4c5e-883d-0c5b4e2ec0e4"}}, {"head": {"id": "cac5f801-21ee-4563-a6c1-9da156cf9f42", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340384155500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db6625e-0590-49c7-9604-2f87ffe112ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340384250200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d50403ee-7a1c-4b95-b729-1473daae9a80", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340387685700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8045ee-5914-406b-85b0-c7b089638475", "name": "entry : default@BuildJS cost memory 0.12786865234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340390708200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43e62d99-6dab-4f2b-a799-53f894099663", "name": "runTaskFromQueue task cost before running: 711 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340390843400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8849a0d1-8bc4-4ff2-94ef-f125441c5b72", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340387673000, "endTime": 30340390951600, "totalTime": 3149500}, "additional": {"logType": "info", "children": [], "durationId": "bd2cb46c-88e8-4c5e-883d-0c5b4e2ec0e4"}}, {"head": {"id": "207e4433-1470-42f1-bf1e-3c72e149a80d", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340396341300, "endTime": 30340398045600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3962b8ef-79a8-4af7-9ca4-7410064b911a", "logId": "99a834e1-5388-48d1-810d-7784b272d286"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3962b8ef-79a8-4af7-9ca4-7410064b911a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340392808300}, "additional": {"logType": "detail", "children": [], "durationId": "207e4433-1470-42f1-bf1e-3c72e149a80d"}}, {"head": {"id": "c17236a4-8343-4d6f-85ee-7c79292a738a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340393240400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd677bbf-3d07-4507-96bd-7a1e6be3dde2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340393343700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab9b237e-1045-4915-8978-78480f1db8c9", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340396352700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3c30d5-007a-4f05-a2b9-245e96a8d853", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340396714500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b466bb6c-b4f4-4fb3-8994-ecb3b3d5c5be", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340397825900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44a78012-af63-4e07-9e07-14da7eda5c7b", "name": "entry : default@CacheNativeLibs cost memory 0.0908660888671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340397956100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a834e1-5388-48d1-810d-7784b272d286", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340396341300, "endTime": 30340398045600}, "additional": {"logType": "info", "children": [], "durationId": "207e4433-1470-42f1-bf1e-3c72e149a80d"}}, {"head": {"id": "01f6a1fc-96c3-4185-bff2-c7b5b37ce992", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340593120200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d129518-e28d-47bb-be04-9e87f49f36bb", "name": "default@CompileArkTS work[48] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340593713000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2bacebf-8a09-4d33-a116-83e2a3659fd5", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340701733100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd0e405-eed4-45e8-be52-c7f7f7df0350", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340702400800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f2161b-a6a8-4a78-81f2-50e621d1e9e4", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340702707000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "046ffac1-6324-4798-848e-c7e7a67f798c", "name": "default@CompileArkTS work[49] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340704273200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145cfbaf-d7b0-4b9a-a647-ef3c37ca0880", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341712045200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef91b36-103f-42f7-9fe5-c62296c1927b", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30340593994600, "endTime": 30340596805000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "61201672-0f8b-46c9-9736-ea0c2c13f725", "logId": "b6bd7063-1626-45eb-8142-50cd1c187ccd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6bd7063-1626-45eb-8142-50cd1c187ccd", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340593994600, "endTime": 30340596805000}, "additional": {"logType": "info", "children": [], "durationId": "0ef91b36-103f-42f7-9fe5-c62296c1927b", "parent": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}}, {"head": {"id": "0cde70e3-86bf-45a3-90a1-ba5d28a26e36", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30340596818500, "endTime": 30340596978000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "61201672-0f8b-46c9-9736-ea0c2c13f725", "logId": "5b5a27a1-7bb2-4214-99ec-39990cbc0a1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b5a27a1-7bb2-4214-99ec-39990cbc0a1b", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340596818500, "endTime": 30340596978000}, "additional": {"logType": "info", "children": [], "durationId": "0cde70e3-86bf-45a3-90a1-ba5d28a26e36", "parent": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}}, {"head": {"id": "8737853e-76a2-4f40-8d46-06023b06da5e", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30340596991400, "endTime": 30340597029600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "61201672-0f8b-46c9-9736-ea0c2c13f725", "logId": "306f9475-8981-497c-be8f-da84e0e2c428"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "306f9475-8981-497c-be8f-da84e0e2c428", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340596991400, "endTime": 30340597029600}, "additional": {"logType": "info", "children": [], "durationId": "8737853e-76a2-4f40-8d46-06023b06da5e", "parent": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}}, {"head": {"id": "63406a13-bb08-45bf-804a-b13ca4a7ffad", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30340597047000, "endTime": 30341625646100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "61201672-0f8b-46c9-9736-ea0c2c13f725", "logId": "8905111d-2e7a-425d-a2e9-98438355f058"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8905111d-2e7a-425d-a2e9-98438355f058", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340597047000, "endTime": 30341625646100}, "additional": {"logType": "info", "children": [], "durationId": "63406a13-bb08-45bf-804a-b13ca4a7ffad", "parent": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}}, {"head": {"id": "7c07f961-1d5e-4d37-a9d2-2c35470a5dc3", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341625668700, "endTime": 30341631291500}, "additional": {"children": ["5d9f7d3e-e07c-400b-be21-6ccb3138726b", "2fc0e3c3-c260-44bf-a7f6-6a8826a63f73", "3ba5a12a-e90c-4197-bc09-c92508fa3f2b"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "61201672-0f8b-46c9-9736-ea0c2c13f725", "logId": "46ba75c9-4214-43e1-91d1-b5cd6e99b51e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46ba75c9-4214-43e1-91d1-b5cd6e99b51e", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341625668700, "endTime": 30341631291500}, "additional": {"logType": "info", "children": ["986fde08-f51b-4753-9a09-dd5f2e4b5c6b", "12bf963d-1982-4179-8915-492f4058e9dd", "041f6def-83de-4029-a1b9-4cd93769bf23"], "durationId": "7c07f961-1d5e-4d37-a9d2-2c35470a5dc3", "parent": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}}, {"head": {"id": "5d9f7d3e-e07c-400b-be21-6ccb3138726b", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341625683200, "endTime": 30341625690300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7c07f961-1d5e-4d37-a9d2-2c35470a5dc3", "logId": "986fde08-f51b-4753-9a09-dd5f2e4b5c6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "986fde08-f51b-4753-9a09-dd5f2e4b5c6b", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341625683200, "endTime": 30341625690300}, "additional": {"logType": "info", "children": [], "durationId": "5d9f7d3e-e07c-400b-be21-6ccb3138726b", "parent": "46ba75c9-4214-43e1-91d1-b5cd6e99b51e"}}, {"head": {"id": "2fc0e3c3-c260-44bf-a7f6-6a8826a63f73", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341625693800, "endTime": 30341626267600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7c07f961-1d5e-4d37-a9d2-2c35470a5dc3", "logId": "12bf963d-1982-4179-8915-492f4058e9dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12bf963d-1982-4179-8915-492f4058e9dd", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341625693800, "endTime": 30341626267600}, "additional": {"logType": "info", "children": [], "durationId": "2fc0e3c3-c260-44bf-a7f6-6a8826a63f73", "parent": "46ba75c9-4214-43e1-91d1-b5cd6e99b51e"}}, {"head": {"id": "3ba5a12a-e90c-4197-bc09-c92508fa3f2b", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341626272500, "endTime": 30341631277700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7c07f961-1d5e-4d37-a9d2-2c35470a5dc3", "logId": "041f6def-83de-4029-a1b9-4cd93769bf23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "041f6def-83de-4029-a1b9-4cd93769bf23", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341626272500, "endTime": 30341631277700}, "additional": {"logType": "info", "children": [], "durationId": "3ba5a12a-e90c-4197-bc09-c92508fa3f2b", "parent": "46ba75c9-4214-43e1-91d1-b5cd6e99b51e"}}, {"head": {"id": "b5d1b512-098b-499a-8da2-5bd707b74eaa", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341631321900, "endTime": 30341710939800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "61201672-0f8b-46c9-9736-ea0c2c13f725", "logId": "90662156-1669-49fe-a8d9-72def117afe1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "90662156-1669-49fe-a8d9-72def117afe1", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341631321900, "endTime": 30341710939800}, "additional": {"logType": "info", "children": [], "durationId": "b5d1b512-098b-499a-8da2-5bd707b74eaa", "parent": "38706f22-ba33-4c97-8d7a-68d3288a4da5"}}, {"head": {"id": "a11f5ab0-7bf0-4fb8-aaf8-a8a527a2e586", "name": "default@CompileArkTS work[48] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341719353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38706f22-ba33-4c97-8d7a-68d3288a4da5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30340593864300, "endTime": 30341711104700}, "additional": {"logType": "info", "children": ["b6bd7063-1626-45eb-8142-50cd1c187ccd", "5b5a27a1-7bb2-4214-99ec-39990cbc0a1b", "306f9475-8981-497c-be8f-da84e0e2c428", "8905111d-2e7a-425d-a2e9-98438355f058", "46ba75c9-4214-43e1-91d1-b5cd6e99b51e", "90662156-1669-49fe-a8d9-72def117afe1"], "durationId": "61201672-0f8b-46c9-9736-ea0c2c13f725", "parent": "bdf06386-9438-415a-80cf-1a4fb008206c"}}, {"head": {"id": "41bd8aa1-9fbd-4444-b696-5a29db51b4de", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341833162800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddd1d428-8888-4a63-8fd0-5497e1769044", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341961113400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e249e11f-e483-408a-b6d3-65fbb350e2cf", "name": "CopyResources is end, endTime: 30341961231400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341961234700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef309077-51b4-4b5b-9712-cc617a1830ba", "name": "default@CompileArkTS work[49] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341961308100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2914bf1-acf5-407c-9a69-d855cf77ea4e", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 30341950065700, "endTime": 30341960953500}, "additional": {"logType": "info", "children": [], "durationId": "d86e17e3-bbf4-4d8a-957e-481894dbe27a", "parent": "bdf06386-9438-415a-80cf-1a4fb008206c"}}, {"head": {"id": "c4cd6faf-fb99-46c3-83cf-5db7e8bb90da", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341961377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf06386-9438-415a-80cf-1a4fb008206c", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30340334613800, "endTime": 30341961442500, "totalTime": 1176081700}, "additional": {"logType": "info", "children": ["38706f22-ba33-4c97-8d7a-68d3288a4da5", "c2914bf1-acf5-407c-9a69-d855cf77ea4e"], "durationId": "2644ca09-0df6-4e03-a24c-a21dcf6396c9"}}, {"head": {"id": "074d4bbc-f67a-4b72-a975-e2e84e3b83fa", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341966311900, "endTime": 30341967437300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "89cf1c65-dc5e-4d82-ae6b-13d2194331cb", "logId": "212b834d-3698-4d52-a717-1ffa72d1f93a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89cf1c65-dc5e-4d82-ae6b-13d2194331cb", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341964930300}, "additional": {"logType": "detail", "children": [], "durationId": "074d4bbc-f67a-4b72-a975-e2e84e3b83fa"}}, {"head": {"id": "bd9c9f2b-72e6-40c0-b932-bca826d09a45", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341965252400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1a0b38-3221-441b-b1dd-f1317ae3d744", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341965349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d6940c-16a4-45a2-a4db-2015971a6a19", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341966320300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fbcdbeb-a36f-41b4-bcf2-a66e92035e98", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341966518600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c562cc8-a1b6-4d54-9e47-0a8dfddfb20a", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341967254100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3fdc000-0cec-4965-bd5e-a81c73595931", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0769500732421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341967352400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "212b834d-3698-4d52-a717-1ffa72d1f93a", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341966311900, "endTime": 30341967437300}, "additional": {"logType": "info", "children": [], "durationId": "074d4bbc-f67a-4b72-a975-e2e84e3b83fa"}}, {"head": {"id": "5203b34f-315a-45cb-908d-eadbb3f687dd", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341976315300, "endTime": 30342432970600}, "additional": {"children": ["6aab6910-7d1d-4507-815c-05d3f34f458e", "43d7fb80-854b-4677-a289-59000c6d925d", "8fffa533-72bf-4a9d-abb2-0be1954015f9"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "a904567e-81d7-4c25-b57d-2d22c5e2756f", "logId": "647f2747-9d23-482b-94a6-06d8bcd95efb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a904567e-81d7-4c25-b57d-2d22c5e2756f", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341969335100}, "additional": {"logType": "detail", "children": [], "durationId": "5203b34f-315a-45cb-908d-eadbb3f687dd"}}, {"head": {"id": "a236e1a5-85fb-4638-8b39-fc8ecd31988d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341969688500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd136ed9-2c23-4506-b81d-80a564c394ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341969781300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "824248b4-48da-4be9-bbb4-b618e7ba8cc8", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341976326600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b8f8d1-9715-49d4-9d19-b06676cceeee", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341988317400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61f058b3-7a61-48fe-bea5-c3ff32dddd26", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341988480600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f1ecbe-2325-44a9-b9c6-1c4eb6f46254", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341988605400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32e7d69d-694c-4b18-9d80-73a5a0e42706", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341988665900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aab6910-7d1d-4507-815c-05d3f34f458e", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341989552000, "endTime": 30341990745700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5203b34f-315a-45cb-908d-eadbb3f687dd", "logId": "c99d1ed7-f8c5-40bb-918b-e1b57c5b092a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ab0d40e-c20a-4cfa-8923-5e69c3bc6a27", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341990599700}, "additional": {"logType": "debug", "children": [], "durationId": "5203b34f-315a-45cb-908d-eadbb3f687dd"}}, {"head": {"id": "c99d1ed7-f8c5-40bb-918b-e1b57c5b092a", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341989552000, "endTime": 30341990745700}, "additional": {"logType": "info", "children": [], "durationId": "6aab6910-7d1d-4507-815c-05d3f34f458e", "parent": "647f2747-9d23-482b-94a6-06d8bcd95efb"}}, {"head": {"id": "43d7fb80-854b-4677-a289-59000c6d925d", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341991403800, "endTime": 30341992949300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5203b34f-315a-45cb-908d-eadbb3f687dd", "logId": "c73029bf-4ce4-4766-a594-cf670949366a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "571248fa-7a65-4535-abf4-9109ed249a93", "name": "default@PackageHap work[50] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341991998900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fffa533-72bf-4a9d-abb2-0be1954015f9", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341992863600, "endTime": 30342419964000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5203b34f-315a-45cb-908d-eadbb3f687dd", "logId": "a263e837-d49a-494f-84cc-6029e19277f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2538162d-184f-47d1-ad79-b44bae4affc9", "name": "default@PackageHap work[50] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341992621800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be592b75-2920-42dc-9605-9c751ea3c880", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341992704900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c52c5513-dcdd-4cbf-b825-ffb74dfb68ca", "name": "default@PackageHap work[50] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341992800100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03bdfe44-6e68-49c9-8c5f-f543ef940aab", "name": "default@PackageHap work[50] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341992858300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c73029bf-4ce4-4766-a594-cf670949366a", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341991403800, "endTime": 30341992949300}, "additional": {"logType": "info", "children": [], "durationId": "43d7fb80-854b-4677-a289-59000c6d925d", "parent": "647f2747-9d23-482b-94a6-06d8bcd95efb"}}, {"head": {"id": "a70235a3-8868-40cb-9a7f-47f0eca05753", "name": "entry : default@PackageHap cost memory 1.3000946044921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341997300000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b59863-36de-4548-8479-f49b3d1e98a6", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342430295300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f3389f-01ef-425a-bd81-5b9a20a60e10", "name": "default@PackageHap work[50] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342431131000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a263e837-d49a-494f-84cc-6029e19277f5", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 30341992863600, "endTime": 30342419964000}, "additional": {"logType": "info", "children": [], "durationId": "8fffa533-72bf-4a9d-abb2-0be1954015f9", "parent": "647f2747-9d23-482b-94a6-06d8bcd95efb"}}, {"head": {"id": "55e1d00b-cfd6-4001-b78b-457609e142c0", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342431784800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "647f2747-9d23-482b-94a6-06d8bcd95efb", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30341976315300, "endTime": 30342432970600, "totalTime": 443655200}, "additional": {"logType": "info", "children": ["c99d1ed7-f8c5-40bb-918b-e1b57c5b092a", "c73029bf-4ce4-4766-a594-cf670949366a", "a263e837-d49a-494f-84cc-6029e19277f5"], "durationId": "5203b34f-315a-45cb-908d-eadbb3f687dd"}}, {"head": {"id": "a0d9beb3-aa78-48c2-b734-42faf31611d0", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342443103300, "endTime": 30342446051200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "07902f0a-ff11-45b1-b65c-28494d24f927", "logId": "7ef1d7eb-1637-4a19-a7d2-1d6404f5fe27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07902f0a-ff11-45b1-b65c-28494d24f927", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342435784800}, "additional": {"logType": "detail", "children": [], "durationId": "a0d9beb3-aa78-48c2-b734-42faf31611d0"}}, {"head": {"id": "2a1ab8bb-e68d-4054-a4ea-c83cc8f731d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342436126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e853cc2d-5c67-4b9e-8da4-b3c3cc6ee1d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342436211200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1747f6e1-ad22-4ff2-8e53-7f4c39ac766c", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342443115600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b03832-f390-483a-8aa2-c934d0ae0a6f", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342443744900}, "additional": {"logType": "warn", "children": [], "durationId": "a0d9beb3-aa78-48c2-b734-42faf31611d0"}}, {"head": {"id": "b8fe798a-fb2c-4b8b-8806-95c1175de183", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342444716200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb53c8c-0c41-4b2b-9c4c-0466ca767e62", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342444876700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "527b28ea-265e-4bff-b435-643759acbcbf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342445071500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6d896ba-d58c-4c3b-876a-8b440a3303cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342445163400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fed0387-3665-4b41-bfb2-7eacb50e32ed", "name": "entry : default@SignHap cost memory 0.11774444580078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342445596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a307403-29ab-484a-971b-659c59b0da61", "name": "runTaskFromQueue task cost before running: 2 s 766 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342445824800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef1d7eb-1637-4a19-a7d2-1d6404f5fe27", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342443103300, "endTime": 30342446051200, "totalTime": 2662800}, "additional": {"logType": "info", "children": [], "durationId": "a0d9beb3-aa78-48c2-b734-42faf31611d0"}}, {"head": {"id": "8339c03c-31c1-4154-b91c-5fa26111de10", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342450122500, "endTime": 30342457516600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "3b39a907-4a5c-47c9-ac0d-190156495a85", "logId": "fd91f475-4d94-47da-b3cb-4f996b502afa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b39a907-4a5c-47c9-ac0d-190156495a85", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342448223100}, "additional": {"logType": "detail", "children": [], "durationId": "8339c03c-31c1-4154-b91c-5fa26111de10"}}, {"head": {"id": "57531a13-7d78-4b12-b3ee-437112de77da", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342448733300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9492f5d9-c80d-487d-8a0f-2520fd38e041", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342448858400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a438c945-9a71-4cf8-a4f7-79b9b083f8f4", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342450196200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efc4b4f7-1a44-4c98-8ae4-657ac6d981e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342457087400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a521781c-0c26-4b67-877f-37dd81fd3d99", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342457227400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5acafcd-8b80-4c82-9948-9bf702c428c7", "name": "entry : default@CollectDebugSymbol cost memory 0.24277496337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342457333000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc2207e-8de8-499a-9877-cd9a6618aa99", "name": "runTaskFromQueue task cost before running: 2 s 778 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342457434800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd91f475-4d94-47da-b3cb-4f996b502afa", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342450122500, "endTime": 30342457516600, "totalTime": 7314700}, "additional": {"logType": "info", "children": [], "durationId": "8339c03c-31c1-4154-b91c-5fa26111de10"}}, {"head": {"id": "98f1bb59-3340-491a-bff9-2702bf149f93", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342460157300, "endTime": 30342460728100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "017b831a-bad1-49ca-87df-c3646f485d1f", "logId": "18c55739-084b-4cfb-9a61-3820e2d42fad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "017b831a-bad1-49ca-87df-c3646f485d1f", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342460031700}, "additional": {"logType": "detail", "children": [], "durationId": "98f1bb59-3340-491a-bff9-2702bf149f93"}}, {"head": {"id": "304c16df-9e5d-4de6-ab8f-a6c447a4bad7", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342460166500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3080c274-effe-4c38-a6db-2f6767c9d8bf", "name": "entry : assembleHap cost memory 0.0115509033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342460356700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd6a471-ecf8-4a3e-ad94-e47a86daea10", "name": "runTaskFromQueue task cost before running: 2 s 781 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342460629400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c55739-084b-4cfb-9a61-3820e2d42fad", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342460157300, "endTime": 30342460728100, "totalTime": 323500}, "additional": {"logType": "info", "children": [], "durationId": "98f1bb59-3340-491a-bff9-2702bf149f93"}}, {"head": {"id": "86c53f10-784a-45a9-89dd-24b650873594", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470218500, "endTime": 30342470242900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6d318e42-49c3-48e9-b5a2-6046a25c0a3c", "logId": "94ad0621-efb6-452e-a327-e124cb7ba590"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94ad0621-efb6-452e-a327-e124cb7ba590", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470218500, "endTime": 30342470242900}, "additional": {"logType": "info", "children": [], "durationId": "86c53f10-784a-45a9-89dd-24b650873594"}}, {"head": {"id": "901263ac-9064-4624-bb4e-5085a3b95999", "name": "BUILD SUCCESSFUL in 2 s 790 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470287500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "7c5bffb1-3f65-44aa-9f8c-fafc3c48dd14", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30339680388900, "endTime": 30342470606100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 55}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "f28beef8-60a8-4f0f-8162-b32f44ba4f2a", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470635400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ccb51d1-0507-46fd-be0c-c035d1c11a87", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470715000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811d3ac9-68fb-45e4-9b9c-8ee98b6a741e", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470769800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "991012ec-ba70-4800-9ab6-d9873c63ea84", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470820800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c84deac-6795-47cc-9a0f-0b12dabfcea2", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342470882400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b78106fd-b985-45fd-8524-41c311fc7dea", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342471195000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed88502-d705-4901-b4e8-1b6403eafe79", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342471824500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a140c9-8ba2-4881-8029-059608193be2", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342472080500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86000d1b-3833-415d-a575-53e5175f5393", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342472157000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d4b59d2-9d7e-4b66-ac5d-50faee933a42", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342472222400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14a39bbd-d0c8-4ee5-8c4e-4991a56f3a5f", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342472645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f958305a-a97e-4f7f-8a4e-10ef1483fceb", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342473600800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b84bf52-cbd7-4f4d-b8a6-09174eb61fd6", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baf765f6-4a9e-4d3c-b27d-075d57a316dd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474113800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a2ce57-bd93-4494-8efe-3efab8f3829b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474180300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26d7071a-941d-441a-a93f-d914199a8e2a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474234400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc976a0-56a3-48ba-a7b9-eed745e66ac3", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb0037d7-b18c-4fde-a39a-bc1471475d8c", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474613900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e072f5cb-fa9f-4a84-9725-e9f1df0e7796", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342474846700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6d7785-4761-4280-a0fb-e70efc095591", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342475050400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b69a985-79a1-4596-92ce-897c488df738", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342475328000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa53fcd-b070-434d-abec-d0179704fe02", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342475399300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47b90a45-ffaf-4975-9ec8-1fc2e027c67c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342475461000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46382378-5c6c-47f5-b1cb-f4a9078c5947", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342477906700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4000c06e-b51c-4b0e-963a-5fa9d3c12d69", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342478405100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e699f14d-4218-4e24-bc56-c72c1e3b1182", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342479226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e3671d1-84f3-4aa5-ae83-56bb1c67e809", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342479459800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7343fba9-e6ce-4982-8339-9120b179d193", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342479654900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea39d971-15cb-48ef-869f-fed05d2e2966", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342480154700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d0d19ed-756a-420d-9146-772465051844", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342480224300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88931f72-6c31-4a47-97c3-fd5928856059", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342480395000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bed81e77-e5ee-4991-9781-8fe62f14f340", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342480667500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9784d5d7-6d1a-42c2-b994-a2a8ebf5a2f6", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342481206400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8744112f-004f-47dd-8436-9caf326afa1d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342482250300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60c86f1c-c4b3-44b6-bfa3-42315ee9c6d0", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342482633000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f1b29b-7b8e-4af4-a476-2d65753f2fa0", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342483369500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621c9d67-8a5f-4b61-8ed7-0bf7c1b707fc", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342483557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5015bc-1368-4747-81e2-8632608fd81f", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342483723200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "708a0876-f083-4400-b580-8bf3386484c2", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342484366000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a976c4e9-d568-4444-bfe3-d3e1e03c031d", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342484678700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e9e44f-7d05-4c6e-a490-31dfa04caa7c", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342484765200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd53b38-fb43-4511-84a2-4ab20b18a34a", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342484818300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd2f6c6-e76e-47b7-a580-d69c6c864195", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342485577900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fc17560-6801-42b6-9e72-84a61b03b94c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342485813200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06da05ee-63d6-49e3-92f5-e8ce27453f98", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342486014700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953c7295-fbbb-451c-8089-1e6a89012656", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342490718800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b479d1a0-e5ab-4839-a502-3875becf8735", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342491067500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba746f87-0394-40ea-a8ac-52290ba241e2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342491288200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c538973f-c360-4d54-8ef9-1ba340143a0d", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342491360100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72385294-7a0a-4876-a72f-9162fbfe6eb2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342491629600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81fdc9c8-42ae-4ff0-bba7-7f27b642ab4f", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342492333700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a3919f1-fd9f-439d-8551-13e00900fc65", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342492575100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "415e2254-0b82-4c80-b87a-64ead5266956", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342492766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a686a52-a51d-4f68-95c4-b1c99a32b6f1", "name": "Incremental task entry:default@PackageHap post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342493064700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bdf55ff-bf77-4874-b713-9509e53ef108", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342493260100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aade1e26-0a80-4e22-bd8d-f1b1d09019d6", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342493336800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eddcf119-8270-4d3d-9e31-93915b40117f", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342493646400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e97536-d41b-45cc-adf3-d71f24e65c93", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342495744900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc568247-775b-4f85-b531-0ff0090a8ccf", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342496005900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac5a3b5-86b4-49d3-bbb4-3cccdd97ca1f", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342496268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f3ead7-0902-4ab5-97e7-40a8cc174b48", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30342496480400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}