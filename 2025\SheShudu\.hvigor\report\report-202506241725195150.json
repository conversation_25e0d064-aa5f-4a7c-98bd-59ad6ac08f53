{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "18f50137-6384-4ea1-a550-cc79c1e70047", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027065482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9aeec00-194c-44ff-a15c-6b6dec82dc2b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027201783100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36d95563-7da8-45b8-bdf4-33258fff7af3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027202022000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344c42e5-b4e6-48c5-906f-97ca0a0c04a6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151408739300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151414859200, "endTime": 32151666874500}, "additional": {"children": ["0e8c7c01-abea-4da5-b1ad-297dbe990eb2", "820526aa-ea3b-4c11-97e4-49d258109c8b", "4b336fab-c521-471f-8014-7f4735665443", "2207c74f-bfec-4181-8c2b-18232a567cd8", "abf6f12d-634d-4598-91c2-78369c171dc6", "2df1bb0f-0e94-4458-b73d-67900bc68f24", "afac1ba2-049b-4fb7-8cfc-400e769c2a39"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c050c03a-03da-4b50-858a-656b9f6acae2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e8c7c01-abea-4da5-b1ad-297dbe990eb2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151414861500, "endTime": 32151429213300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "6981e344-f933-45fd-a0b8-ebbb326e0361"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "820526aa-ea3b-4c11-97e4-49d258109c8b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151429232500, "endTime": 32151665765500}, "additional": {"children": ["a1ab4186-821c-4db8-9814-fa1f8a5317c6", "bf0546cd-ba49-4e7f-8d17-9d4e666fefee", "721406b6-7451-475f-8dd7-8e471adfdde8", "75a87514-28ac-4bd3-9673-d4c2725674b8", "862d1b61-6262-478b-847d-7b3708f9769e", "13d68708-d938-49d8-833f-67a5105aeee5", "77e6521d-ed9b-4365-bf86-ef52140e86b4", "052516ef-f42a-4cdd-a8e6-5f1d4a657bee", "56a79a3b-c602-47cf-90ee-14c11b6d1182"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b336fab-c521-471f-8014-7f4735665443", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151665784000, "endTime": 32151666866000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "d9f9489e-7d69-4319-8892-ca8e3ac09a8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2207c74f-bfec-4181-8c2b-18232a567cd8", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151666869700, "endTime": 32151666870700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "d5e3fa48-3554-4745-8f34-99024cc4c152"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abf6f12d-634d-4598-91c2-78369c171dc6", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151418234900, "endTime": 32151418275500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "405e9613-026e-41ac-a8e4-1fc732f0e5b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "405e9613-026e-41ac-a8e4-1fc732f0e5b1", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151418234900, "endTime": 32151418275500}, "additional": {"logType": "info", "children": [], "durationId": "abf6f12d-634d-4598-91c2-78369c171dc6", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "2df1bb0f-0e94-4458-b73d-67900bc68f24", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151423266800, "endTime": 32151423285900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "d0bf86ea-d507-494b-90f8-a8f0645f9ba7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0bf86ea-d507-494b-90f8-a8f0645f9ba7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151423266800, "endTime": 32151423285900}, "additional": {"logType": "info", "children": [], "durationId": "2df1bb0f-0e94-4458-b73d-67900bc68f24", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "4cd4691e-c37f-4603-acd4-75efef91e0b1", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151423329900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "965758fe-8d9f-4b87-85b0-4d80132355ad", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151429070100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6981e344-f933-45fd-a0b8-ebbb326e0361", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151414861500, "endTime": 32151429213300}, "additional": {"logType": "info", "children": [], "durationId": "0e8c7c01-abea-4da5-b1ad-297dbe990eb2", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "a1ab4186-821c-4db8-9814-fa1f8a5317c6", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151435746500, "endTime": 32151435760900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "4f5e23be-39aa-41ec-a4b1-e8eb20b27477"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf0546cd-ba49-4e7f-8d17-9d4e666fefee", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151435790200, "endTime": 32151441603900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "4d8f128d-a8b7-43ad-ac38-2c9a8bbc4e23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "721406b6-7451-475f-8dd7-8e471adfdde8", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151441623400, "endTime": 32151532534500}, "additional": {"children": ["98c68e15-a1ad-400f-a3e3-8832bfc05c61", "9bb98861-8a7d-4ebb-9faa-50bc83a1bc43", "4f09d02a-5b0b-4010-8858-791a34a2d1a3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "1b2ea0c4-f053-479a-b10e-b75774827a51"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75a87514-28ac-4bd3-9673-d4c2725674b8", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151532546400, "endTime": 32151561788900}, "additional": {"children": ["cdead265-72e2-4661-abbf-cb72e91f4217"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "fee3bf58-8774-4e45-9f1e-06db2e427032"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "862d1b61-6262-478b-847d-7b3708f9769e", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151561797300, "endTime": 32151645417300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "b4121732-d504-4d0c-b10f-3ec83912780f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13d68708-d938-49d8-833f-67a5105aeee5", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151646304400, "endTime": 32151656947000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "a667e3f7-15e1-457f-8d35-34c30c05ea44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77e6521d-ed9b-4365-bf86-ef52140e86b4", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151656967300, "endTime": 32151665598600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "c07a4913-9743-4f0a-8342-7f942097b700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "052516ef-f42a-4cdd-a8e6-5f1d4a657bee", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151665627400, "endTime": 32151665746000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "088e79f4-a2ef-4295-b7a4-4d995dc1c008"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f5e23be-39aa-41ec-a4b1-e8eb20b27477", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151435746500, "endTime": 32151435760900}, "additional": {"logType": "info", "children": [], "durationId": "a1ab4186-821c-4db8-9814-fa1f8a5317c6", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "4d8f128d-a8b7-43ad-ac38-2c9a8bbc4e23", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151435790200, "endTime": 32151441603900}, "additional": {"logType": "info", "children": [], "durationId": "bf0546cd-ba49-4e7f-8d17-9d4e666fefee", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "98c68e15-a1ad-400f-a3e3-8832bfc05c61", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151442164400, "endTime": 32151442183800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "721406b6-7451-475f-8dd7-8e471adfdde8", "logId": "b71e256c-00e1-44cd-b56d-6d8906e99ad8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b71e256c-00e1-44cd-b56d-6d8906e99ad8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151442164400, "endTime": 32151442183800}, "additional": {"logType": "info", "children": [], "durationId": "98c68e15-a1ad-400f-a3e3-8832bfc05c61", "parent": "1b2ea0c4-f053-479a-b10e-b75774827a51"}}, {"head": {"id": "9bb98861-8a7d-4ebb-9faa-50bc83a1bc43", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151443971500, "endTime": 32151531575000}, "additional": {"children": ["162f3687-2e30-4ecc-945d-cdada9020ed9", "75212667-1e88-46eb-b6ed-cd245931b92e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "721406b6-7451-475f-8dd7-8e471adfdde8", "logId": "652b9d8c-66f8-49c8-b5ac-ecc53590bddd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "162f3687-2e30-4ecc-945d-cdada9020ed9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151443973500, "endTime": 32151450574800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9bb98861-8a7d-4ebb-9faa-50bc83a1bc43", "logId": "0c350453-129a-4b4b-84eb-96d553e460f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75212667-1e88-46eb-b6ed-cd245931b92e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151450604300, "endTime": 32151531562700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9bb98861-8a7d-4ebb-9faa-50bc83a1bc43", "logId": "76a7d7a5-c675-4557-9f20-273a2ac156a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "defdf282-f763-4832-9ec9-e07de8682022", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151443978700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80cc9ab-ecd7-4cbd-acba-c7ded8e9fb17", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151450415600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c350453-129a-4b4b-84eb-96d553e460f9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151443973500, "endTime": 32151450574800}, "additional": {"logType": "info", "children": [], "durationId": "162f3687-2e30-4ecc-945d-cdada9020ed9", "parent": "652b9d8c-66f8-49c8-b5ac-ecc53590bddd"}}, {"head": {"id": "97d4615f-79e3-48e0-bc24-c3d870467440", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151450668300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c642387c-5fc6-4b04-8370-b286ff85d259", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151458364700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964d4e5f-8487-4cbc-8a73-66c7f2470872", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151458585200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42e7f05f-579c-48d7-8d1f-e503d4b3a7a2", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151458787000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c1866a-2b24-4027-ae52-ee5771de7c18", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151459147800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2282dc9f-4390-48d8-a44a-187eefe9e3ca", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151461601600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a231389f-8bd3-41c5-9119-d1d524215a73", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151466568000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727d7543-49df-490b-81be-9c2c45b843a6", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151477492400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60285843-530f-4755-a69a-5ce1e7e6eea1", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151504907600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75dcdf7-4783-4257-a70c-fc45c8d516ba", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151505048000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 25}, "markType": "other"}}, {"head": {"id": "8ec996bd-75a2-4517-9b36-aff692067972", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151505136600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 25}, "markType": "other"}}, {"head": {"id": "d25baac2-500b-4f89-a18c-1f55fb2f4d88", "name": "Project task initialization takes 25 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151531299700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c11a5da-0291-4bb5-8d82-038382598d3a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151531415100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30bb34e5-914c-4662-8342-680280b51fa8", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151531469200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b00ec29-7a0b-41b2-a4d1-cd72279a8117", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151531516300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a7d7a5-c675-4557-9f20-273a2ac156a7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151450604300, "endTime": 32151531562700}, "additional": {"logType": "info", "children": [], "durationId": "75212667-1e88-46eb-b6ed-cd245931b92e", "parent": "652b9d8c-66f8-49c8-b5ac-ecc53590bddd"}}, {"head": {"id": "652b9d8c-66f8-49c8-b5ac-ecc53590bddd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151443971500, "endTime": 32151531575000}, "additional": {"logType": "info", "children": ["0c350453-129a-4b4b-84eb-96d553e460f9", "76a7d7a5-c675-4557-9f20-273a2ac156a7"], "durationId": "9bb98861-8a7d-4ebb-9faa-50bc83a1bc43", "parent": "1b2ea0c4-f053-479a-b10e-b75774827a51"}}, {"head": {"id": "4f09d02a-5b0b-4010-8858-791a34a2d1a3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151532507100, "endTime": 32151532522600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "721406b6-7451-475f-8dd7-8e471adfdde8", "logId": "7c03fed5-5472-4ad3-9ce6-a2847cb8681d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c03fed5-5472-4ad3-9ce6-a2847cb8681d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151532507100, "endTime": 32151532522600}, "additional": {"logType": "info", "children": [], "durationId": "4f09d02a-5b0b-4010-8858-791a34a2d1a3", "parent": "1b2ea0c4-f053-479a-b10e-b75774827a51"}}, {"head": {"id": "1b2ea0c4-f053-479a-b10e-b75774827a51", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151441623400, "endTime": 32151532534500}, "additional": {"logType": "info", "children": ["b71e256c-00e1-44cd-b56d-6d8906e99ad8", "652b9d8c-66f8-49c8-b5ac-ecc53590bddd", "7c03fed5-5472-4ad3-9ce6-a2847cb8681d"], "durationId": "721406b6-7451-475f-8dd7-8e471adfdde8", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "cdead265-72e2-4661-abbf-cb72e91f4217", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151533227700, "endTime": 32151561776300}, "additional": {"children": ["d785d2a2-50c4-4362-8b1f-6f1ffd8afd06", "3df6400f-9510-4613-96ab-abe853541404", "6234b3e5-e249-49d6-a04d-68fbc97214b5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "75a87514-28ac-4bd3-9673-d4c2725674b8", "logId": "3b7a8bdb-d0eb-480b-9832-d9a848157b92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d785d2a2-50c4-4362-8b1f-6f1ffd8afd06", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151538407800, "endTime": 32151538426800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdead265-72e2-4661-abbf-cb72e91f4217", "logId": "a6ba6289-246d-48ed-ae94-5f3bf49e6cd0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6ba6289-246d-48ed-ae94-5f3bf49e6cd0", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151538407800, "endTime": 32151538426800}, "additional": {"logType": "info", "children": [], "durationId": "d785d2a2-50c4-4362-8b1f-6f1ffd8afd06", "parent": "3b7a8bdb-d0eb-480b-9832-d9a848157b92"}}, {"head": {"id": "3df6400f-9510-4613-96ab-abe853541404", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151541737800, "endTime": 32151560456300}, "additional": {"children": ["a9a5e6d8-8330-44ec-8bfe-7c8384033a6a", "fc086340-5403-43a8-aa80-8f1d98b79c71"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdead265-72e2-4661-abbf-cb72e91f4217", "logId": "c0d4cf6e-82f6-4d9e-a700-ccd6a5bbe927"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9a5e6d8-8330-44ec-8bfe-7c8384033a6a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151541739600, "endTime": 32151546987800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3df6400f-9510-4613-96ab-abe853541404", "logId": "3fe4945e-dee9-4e92-a75e-a4ab7f902ed9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc086340-5403-43a8-aa80-8f1d98b79c71", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151547003000, "endTime": 32151560442900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3df6400f-9510-4613-96ab-abe853541404", "logId": "e7e96d53-8344-4b31-bfc1-e53515b373d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21ad99e2-f9a8-4cf2-8aa8-ad1e5b36184e", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151541745700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b592a405-4c3c-47ea-91b7-d78d19660a71", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151546857400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fe4945e-dee9-4e92-a75e-a4ab7f902ed9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151541739600, "endTime": 32151546987800}, "additional": {"logType": "info", "children": [], "durationId": "a9a5e6d8-8330-44ec-8bfe-7c8384033a6a", "parent": "c0d4cf6e-82f6-4d9e-a700-ccd6a5bbe927"}}, {"head": {"id": "401c2e85-35cd-4ecd-aa00-a0e3ce877231", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151547015800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc51fb36-77fb-4bf9-ab92-a08c78b8910c", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151556195100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "423f0a2c-b441-465e-b77f-14155ad6f0b1", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151556441400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d8d7de-9651-444e-982d-41737daaf0c4", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151556746700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a097a2f5-4dfb-43f8-8005-8c256f34b2ac", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151556905600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65dccc5f-6a70-455b-ac31-2c19c22c69ae", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151556971600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c34863-1ce8-4614-adfe-d58bc79a6022", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151557021600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73f56471-f75f-477c-9995-844dc5893568", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151557078800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d31b88c-74cb-413b-931c-d8efa71635bb", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151560011700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cea7379-61d1-447b-9265-7cccace7a5b6", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151560270000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c7bcfd2-aa38-4699-8742-b6a48b9fe411", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151560348800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c2eecd-744a-4269-ab9f-d91cf2ba5ba2", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151560397800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e96d53-8344-4b31-bfc1-e53515b373d5", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151547003000, "endTime": 32151560442900}, "additional": {"logType": "info", "children": [], "durationId": "fc086340-5403-43a8-aa80-8f1d98b79c71", "parent": "c0d4cf6e-82f6-4d9e-a700-ccd6a5bbe927"}}, {"head": {"id": "c0d4cf6e-82f6-4d9e-a700-ccd6a5bbe927", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151541737800, "endTime": 32151560456300}, "additional": {"logType": "info", "children": ["3fe4945e-dee9-4e92-a75e-a4ab7f902ed9", "e7e96d53-8344-4b31-bfc1-e53515b373d5"], "durationId": "3df6400f-9510-4613-96ab-abe853541404", "parent": "3b7a8bdb-d0eb-480b-9832-d9a848157b92"}}, {"head": {"id": "6234b3e5-e249-49d6-a04d-68fbc97214b5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151561746900, "endTime": 32151561762000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cdead265-72e2-4661-abbf-cb72e91f4217", "logId": "ca250491-d74b-4ade-a98f-c9d20f5e235a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca250491-d74b-4ade-a98f-c9d20f5e235a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151561746900, "endTime": 32151561762000}, "additional": {"logType": "info", "children": [], "durationId": "6234b3e5-e249-49d6-a04d-68fbc97214b5", "parent": "3b7a8bdb-d0eb-480b-9832-d9a848157b92"}}, {"head": {"id": "3b7a8bdb-d0eb-480b-9832-d9a848157b92", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151533227700, "endTime": 32151561776300}, "additional": {"logType": "info", "children": ["a6ba6289-246d-48ed-ae94-5f3bf49e6cd0", "c0d4cf6e-82f6-4d9e-a700-ccd6a5bbe927", "ca250491-d74b-4ade-a98f-c9d20f5e235a"], "durationId": "cdead265-72e2-4661-abbf-cb72e91f4217", "parent": "fee3bf58-8774-4e45-9f1e-06db2e427032"}}, {"head": {"id": "fee3bf58-8774-4e45-9f1e-06db2e427032", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151532546400, "endTime": 32151561788900}, "additional": {"logType": "info", "children": ["3b7a8bdb-d0eb-480b-9832-d9a848157b92"], "durationId": "75a87514-28ac-4bd3-9673-d4c2725674b8", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "8b7dc784-ef74-486b-8a28-153019f9eecd", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151585270300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33e2dc0d-6353-427a-8626-d776a3e1bf8a", "name": "hvigorfile, resolve hvigorfile dependencies in 84 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151645276100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4121732-d504-4d0c-b10f-3ec83912780f", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151561797300, "endTime": 32151645417300}, "additional": {"logType": "info", "children": [], "durationId": "862d1b61-6262-478b-847d-7b3708f9769e", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "56a79a3b-c602-47cf-90ee-14c11b6d1182", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151646109900, "endTime": 32151646291100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "820526aa-ea3b-4c11-97e4-49d258109c8b", "logId": "02de512d-48bc-4483-ac60-7605a58ace0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40bc7cb6-c047-40f1-9dea-ec6f68e84a38", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151646132800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02de512d-48bc-4483-ac60-7605a58ace0f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151646109900, "endTime": 32151646291100}, "additional": {"logType": "info", "children": [], "durationId": "56a79a3b-c602-47cf-90ee-14c11b6d1182", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "3201460d-42cb-4d04-a480-5a707c81aac2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151647286900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f669849-79a2-4a86-bca5-acdcf7de6353", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151656224900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a667e3f7-15e1-457f-8d35-34c30c05ea44", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151646304400, "endTime": 32151656947000}, "additional": {"logType": "info", "children": [], "durationId": "13d68708-d938-49d8-833f-67a5105aeee5", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "063c146a-408e-4847-8b05-ce3203e9e6ac", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151660593300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "144a5d53-fff4-496a-9f9b-248b795c88b7", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151660735200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c48a6e3-1178-45f9-a985-9ce5e6829c19", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151662429500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271c1879-1e62-4832-b5f2-da13b2e0655d", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151662515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07a4913-9743-4f0a-8342-7f942097b700", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151656967300, "endTime": 32151665598600}, "additional": {"logType": "info", "children": [], "durationId": "77e6521d-ed9b-4365-bf86-ef52140e86b4", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "5a4aa76b-4be2-4bfd-a357-5db4d4b4d413", "name": "Configuration phase cost:230 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151665646300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "088e79f4-a2ef-4295-b7a4-4d995dc1c008", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151665627400, "endTime": 32151665746000}, "additional": {"logType": "info", "children": [], "durationId": "052516ef-f42a-4cdd-a8e6-5f1d4a657bee", "parent": "dfd26c6d-aacc-41df-98ad-cb75406fbd08"}}, {"head": {"id": "dfd26c6d-aacc-41df-98ad-cb75406fbd08", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151429232500, "endTime": 32151665765500}, "additional": {"logType": "info", "children": ["4f5e23be-39aa-41ec-a4b1-e8eb20b27477", "4d8f128d-a8b7-43ad-ac38-2c9a8bbc4e23", "1b2ea0c4-f053-479a-b10e-b75774827a51", "fee3bf58-8774-4e45-9f1e-06db2e427032", "b4121732-d504-4d0c-b10f-3ec83912780f", "a667e3f7-15e1-457f-8d35-34c30c05ea44", "c07a4913-9743-4f0a-8342-7f942097b700", "088e79f4-a2ef-4295-b7a4-4d995dc1c008", "02de512d-48bc-4483-ac60-7605a58ace0f"], "durationId": "820526aa-ea3b-4c11-97e4-49d258109c8b", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "afac1ba2-049b-4fb7-8cfc-400e769c2a39", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151666840900, "endTime": 32151666856700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b", "logId": "8a85b558-70a2-4c7e-a8de-8e699018022f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a85b558-70a2-4c7e-a8de-8e699018022f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151666840900, "endTime": 32151666856700}, "additional": {"logType": "info", "children": [], "durationId": "afac1ba2-049b-4fb7-8cfc-400e769c2a39", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "d9f9489e-7d69-4319-8892-ca8e3ac09a8a", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151665784000, "endTime": 32151666866000}, "additional": {"logType": "info", "children": [], "durationId": "4b336fab-c521-471f-8014-7f4735665443", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "d5e3fa48-3554-4745-8f34-99024cc4c152", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151666869700, "endTime": 32151666870700}, "additional": {"logType": "info", "children": [], "durationId": "2207c74f-bfec-4181-8c2b-18232a567cd8", "parent": "c050c03a-03da-4b50-858a-656b9f6acae2"}}, {"head": {"id": "c050c03a-03da-4b50-858a-656b9f6acae2", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151414859200, "endTime": 32151666874500}, "additional": {"logType": "info", "children": ["6981e344-f933-45fd-a0b8-ebbb326e0361", "dfd26c6d-aacc-41df-98ad-cb75406fbd08", "d9f9489e-7d69-4319-8892-ca8e3ac09a8a", "d5e3fa48-3554-4745-8f34-99024cc4c152", "405e9613-026e-41ac-a8e4-1fc732f0e5b1", "d0bf86ea-d507-494b-90f8-a8f0645f9ba7", "8a85b558-70a2-4c7e-a8de-8e699018022f"], "durationId": "8fcf34a9-6398-4bc4-bfda-aa02c46c2a2b"}}, {"head": {"id": "762f2649-5d52-465d-a2be-7ca3ca8aa3ef", "name": "Configuration task cost before running: 256 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151667042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f9a266b-3b77-46f5-9358-a8bf1a300726", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151671250900, "endTime": 32151677934400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3f8582eb-dc04-435c-8efe-5ebe990c71d7", "logId": "a4799884-3dbd-4a44-876b-b256c043c778"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f8582eb-dc04-435c-8efe-5ebe990c71d7", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151668281600}, "additional": {"logType": "detail", "children": [], "durationId": "3f9a266b-3b77-46f5-9358-a8bf1a300726"}}, {"head": {"id": "e199996f-e711-4ef3-a077-de63b5483a54", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151668844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be454090-4b96-42a4-9f5c-eb1fe762c859", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151669003800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "605969d4-7ab6-4885-b7b6-c930b2bc476c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151671265100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ff184d8-f1da-4c2a-9e12-1f035dd1f626", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151677713600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0bef281-a94e-46e9-b71d-336eb547e8c4", "name": "entry : default@PreBuild cost memory 0.31037139892578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151677852300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4799884-3dbd-4a44-876b-b256c043c778", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151671250900, "endTime": 32151677934400}, "additional": {"logType": "info", "children": [], "durationId": "3f9a266b-3b77-46f5-9358-a8bf1a300726"}}, {"head": {"id": "72ec72ed-27e1-4830-8c1e-ac79ccaa0d68", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151683038000, "endTime": 32151684747100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a9143b78-0e25-4336-b1ec-081000320bfd", "logId": "fe0dac9e-1db3-4a26-86bf-c5e492bd01c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9143b78-0e25-4336-b1ec-081000320bfd", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151681692700}, "additional": {"logType": "detail", "children": [], "durationId": "72ec72ed-27e1-4830-8c1e-ac79ccaa0d68"}}, {"head": {"id": "05190770-7e25-4512-be6f-8eea0dee6761", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151682189300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69fb36f8-8a40-4c48-945d-0b0cb641a024", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151682310600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff3a51a-4f37-4d97-bcdd-8961e88de3b3", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151683046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "702072b3-2afd-4b91-bd8c-9cf39d9175dc", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151683732100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1409884-46ed-4f4b-9092-0cc49c553378", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151684511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97c63ae3-945c-47de-b1c4-1f167528e585", "name": "entry : default@GenerateMetadata cost memory 0.0919342041015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151684640900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe0dac9e-1db3-4a26-86bf-c5e492bd01c2", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151683038000, "endTime": 32151684747100}, "additional": {"logType": "info", "children": [], "durationId": "72ec72ed-27e1-4830-8c1e-ac79ccaa0d68"}}, {"head": {"id": "aa8bdfc7-1007-4f30-a21a-b2fa3d8ba2e6", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688319700, "endTime": 32151688740800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e60fc53d-4fc0-4588-9653-d606834d358d", "logId": "84a66fca-5dc9-4db9-948f-995a159f269b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e60fc53d-4fc0-4588-9653-d606834d358d", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151687714300}, "additional": {"logType": "detail", "children": [], "durationId": "aa8bdfc7-1007-4f30-a21a-b2fa3d8ba2e6"}}, {"head": {"id": "cb41ff9f-1453-41a9-964e-c9fc0fa45596", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688078300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e2ac21-8779-4065-bea8-49f5a8153bcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688182700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789ee3aa-f649-471a-a6d7-f097baa44e94", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688326400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512b1a38-d96e-4299-8ddc-8433be30d20e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688412800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3364061-e19d-4781-b1f2-095a97e524b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688461100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7bcea0-a797-4c64-9e5e-9c0948a40d3c", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688526000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c5edcd-c868-40d8-8ff2-04472bae1def", "name": "runTaskFromQueue task cost before running: 277 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688598100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a66fca-5dc9-4db9-948f-995a159f269b", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151688319700, "endTime": 32151688740800, "totalTime": 260700}, "additional": {"logType": "info", "children": [], "durationId": "aa8bdfc7-1007-4f30-a21a-b2fa3d8ba2e6"}}, {"head": {"id": "48d3940b-acb1-4ff4-9e48-3d1ac32d97c6", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151691405300, "endTime": 32151692908200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3cc8bc19-ce37-4fb1-ad84-cce17ef10a3c", "logId": "6fa1ce2e-2541-4a78-89cc-2a76e80ed143"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cc8bc19-ce37-4fb1-ad84-cce17ef10a3c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151690434400}, "additional": {"logType": "detail", "children": [], "durationId": "48d3940b-acb1-4ff4-9e48-3d1ac32d97c6"}}, {"head": {"id": "c865b8c7-a4eb-4019-9ddb-cfc6b03f33ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151690766700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49efaf24-f789-45aa-84ff-78205cdce913", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151690856100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91feb14b-7133-49ba-9853-814818a40efa", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151691413600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4902c1d3-cef9-4dfd-8538-4c6c3e3955d0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151692745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789b2ccf-bcd7-4002-9846-6128fa3924e8", "name": "entry : default@MergeProfile cost memory 0.10372161865234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151692842900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa1ce2e-2541-4a78-89cc-2a76e80ed143", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151691405300, "endTime": 32151692908200}, "additional": {"logType": "info", "children": [], "durationId": "48d3940b-acb1-4ff4-9e48-3d1ac32d97c6"}}, {"head": {"id": "0a7392c9-574f-48ba-8e2e-c165715f22fb", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151695748100, "endTime": 32151697804500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f42eb6e3-75c9-4432-98d0-b974ab3f49af", "logId": "6e347dd5-5fcb-46b3-aecd-cfbcb5218f0f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f42eb6e3-75c9-4432-98d0-b974ab3f49af", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151694370100}, "additional": {"logType": "detail", "children": [], "durationId": "0a7392c9-574f-48ba-8e2e-c165715f22fb"}}, {"head": {"id": "d61a7530-5952-408c-9496-6b1fa11d0aa1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151694684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be3c6677-4af3-471c-9268-e6b6772e74be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151694770100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b34364ee-14cd-4296-96b5-c9fb60d11832", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151695759500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62286a42-0175-41e7-a984-b9ba5ff955fa", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151696732700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d6f193-a04f-43e7-ad7a-35304aa87ab0", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151697639900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2cc0083-66a5-4545-bf74-a197864a2972", "name": "entry : default@CreateBuildProfile cost memory 0.1004486083984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151697735500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e347dd5-5fcb-46b3-aecd-cfbcb5218f0f", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151695748100, "endTime": 32151697804500}, "additional": {"logType": "info", "children": [], "durationId": "0a7392c9-574f-48ba-8e2e-c165715f22fb"}}, {"head": {"id": "33821e54-a6a0-4be4-805a-774326232710", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151700762100, "endTime": 32151701156300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "a7bd08d3-8f20-4fef-b3ef-8734b784780d", "logId": "fd3aab37-2a38-4c68-bfb4-49f8a06ffcea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7bd08d3-8f20-4fef-b3ef-8734b784780d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151699564700}, "additional": {"logType": "detail", "children": [], "durationId": "33821e54-a6a0-4be4-805a-774326232710"}}, {"head": {"id": "cfff4d77-75f6-4796-901d-b0dd2230b72f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151699899800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f3004cf-58fe-4f50-b510-d294ab84ef52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151699995700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaab1f19-494e-46ba-b77b-a49944d12bf1", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151700770200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d55ba780-4d25-4301-b9c5-f62df18e1a10", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151700880900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c00b12fe-de19-42ae-881b-4d068453162c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151700940200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de5da26-b33a-4acd-81c1-1<PERSON><PERSON><PERSON><PERSON>46", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151701010200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2249e2a-1dcd-4405-8eab-3a7b4484d39a", "name": "runTaskFromQueue task cost before running: 290 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151701079900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd3aab37-2a38-4c68-bfb4-49f8a06ffcea", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151700762100, "endTime": 32151701156300, "totalTime": 301500}, "additional": {"logType": "info", "children": [], "durationId": "33821e54-a6a0-4be4-805a-774326232710"}}, {"head": {"id": "fcb1410d-0fb1-444e-a7de-aaa4c05bd0e7", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151718048700, "endTime": 32151720750700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "935ea04d-9cfa-46eb-8957-35da7f23c407", "logId": "63f0bf1c-b17b-401f-97a4-e7d42f9780a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "935ea04d-9cfa-46eb-8957-35da7f23c407", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151703810600}, "additional": {"logType": "detail", "children": [], "durationId": "fcb1410d-0fb1-444e-a7de-aaa4c05bd0e7"}}, {"head": {"id": "7cd391f3-7b69-48af-a454-fe4f9d1c1e64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151704304500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0491002-67a7-4213-bfc3-cd488063b8d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151705425100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727d3538-0e35-4928-8d5e-4c60fb7cc647", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151718061000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e2256f-3e41-4c3e-94de-c8e2ffa5f4f6", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151718286600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73bd9493-8659-4f80-b97b-deedc2e4af3d", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0382843017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151719408300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdfcdff5-6b5c-42e0-b5f9-e04d29231c35", "name": "runTaskFromQueue task cost before running: 309 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151720579900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f0bf1c-b17b-401f-97a4-e7d42f9780a5", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151718048700, "endTime": 32151720750700, "totalTime": 1892000}, "additional": {"logType": "info", "children": [], "durationId": "fcb1410d-0fb1-444e-a7de-aaa4c05bd0e7"}}, {"head": {"id": "7663940b-fe08-430b-8bb6-1b91bd09eaae", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151730492300, "endTime": 32151732674300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "a7b4ea91-acd8-4163-96a2-2ffafcc6217d", "logId": "dd2bc005-b389-4169-9ffd-ad11e81d992f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7b4ea91-acd8-4163-96a2-2ffafcc6217d", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151725633100}, "additional": {"logType": "detail", "children": [], "durationId": "7663940b-fe08-430b-8bb6-1b91bd09eaae"}}, {"head": {"id": "9ba02230-a717-452a-9c82-1769302d9fad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151726003900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806a4cb3-a818-41a0-9afb-ccbab927d435", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151726261700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8fb6980-59d8-4510-b92b-9bad77c63575", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151730504400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b96dc282-49cf-4deb-b9a0-d3c044f00c0d", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151732039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7289be9-85f0-4279-b977-62c1331ea9b4", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151732268800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "514a89d0-049f-456c-941f-ca0a56cad163", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151732398500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e2127c-b85d-4d12-bf4c-b489125d8d23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151732458000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2ab698-c45b-4e72-b716-109cd5529eb9", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1182098388671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151732538900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ca86d2e-5b9e-4330-908f-40235ec807a8", "name": "runTaskFromQueue task cost before running: 321 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151732616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd2bc005-b389-4169-9ffd-ad11e81d992f", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151730492300, "endTime": 32151732674300, "totalTime": 2104200}, "additional": {"logType": "info", "children": [], "durationId": "7663940b-fe08-430b-8bb6-1b91bd09eaae"}}, {"head": {"id": "a5708b45-4210-4122-9cbc-4bee30681fce", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151736656900, "endTime": 32151737116000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a841638c-da1f-45b6-a1b1-fe8d17c93cf9", "logId": "efea8daa-e35c-4a69-9399-2b347a54f7ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a841638c-da1f-45b6-a1b1-fe8d17c93cf9", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151734954300}, "additional": {"logType": "detail", "children": [], "durationId": "a5708b45-4210-4122-9cbc-4bee30681fce"}}, {"head": {"id": "e70102f5-43a2-483f-b471-28bf9e692ee7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151735460400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c11f1e73-2937-438b-85d5-dcf131156024", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151735586300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8b1af0f-a4ca-4902-8a66-73b01c883d3b", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151736676100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0553c3b5-4894-48e5-af44-0d218abf5fb2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151736845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adbf4907-428e-4ee9-bd7d-a46ea2246591", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151736906400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15c3fcb1-160b-4ffb-93fb-6b75d3308492", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151736983500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eab5dde5-2472-488a-800a-15113fd58802", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151737058600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efea8daa-e35c-4a69-9399-2b347a54f7ca", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151736656900, "endTime": 32151737116000, "totalTime": 382600}, "additional": {"logType": "info", "children": [], "durationId": "a5708b45-4210-4122-9cbc-4bee30681fce"}}, {"head": {"id": "63ee5146-7728-4a06-aa2c-b4ceb41d489a", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151740420700, "endTime": 32151743632700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5f06cd59-8447-49f0-8fe0-b18ae2f07505", "logId": "eb1ca51b-cb26-4f68-a76e-2fc6d99d3c6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f06cd59-8447-49f0-8fe0-b18ae2f07505", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151738673400}, "additional": {"logType": "detail", "children": [], "durationId": "63ee5146-7728-4a06-aa2c-b4ceb41d489a"}}, {"head": {"id": "d69ae70a-f3bb-4fde-aa75-89c40f5099c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151739043500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb109bd-8a33-4f28-b880-131573594f90", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151739205300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab2ed77-48e7-4662-bc40-f3d0600f8449", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151740431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d48f4ef-6038-40e2-b68b-a02208c087bf", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151743366200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67ef8e68-ebb7-4ef2-9bbd-6c0a3c801735", "name": "entry : default@MakePackInfo cost memory 0.13726806640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151743523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb1ca51b-cb26-4f68-a76e-2fc6d99d3c6a", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151740420700, "endTime": 32151743632700}, "additional": {"logType": "info", "children": [], "durationId": "63ee5146-7728-4a06-aa2c-b4ceb41d489a"}}, {"head": {"id": "1946fc6d-e546-4162-ad22-0196742e0eda", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151747079700, "endTime": 32151749599500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "a505c42b-e271-4bf5-8d81-3e7002d55c62", "logId": "e957b0b8-00c5-4a9b-88b9-3957369def32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a505c42b-e271-4bf5-8d81-3e7002d55c62", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151745651000}, "additional": {"logType": "detail", "children": [], "durationId": "1946fc6d-e546-4162-ad22-0196742e0eda"}}, {"head": {"id": "fdd542c3-b337-4ef1-8afb-2277bf643ca0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151745997000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb9ac6e-2a92-483c-8d1d-c209e874f308", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151746094800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df08ace6-4f5b-48ab-8877-a6a95a2fc2b9", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151747088500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cd81ba6-1fae-4e14-89db-d1c6e6b959bb", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151747247800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57335303-c7dc-4dce-b208-a6b2f82ec305", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151747809100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f429b9b-003b-4142-a0e8-ca7c952a9f93", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151749138200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56c39710-b329-4fbd-ace9-2513e498aa91", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151749258500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63cd8e60-fcdf-4f66-bd71-4ff61f69614c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151749352800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99cfb9f4-20be-4501-84b3-6ca44e4f7b42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151749409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a707a30-b60f-40da-a2ac-c916f9e773a6", "name": "entry : default@SyscapTransform cost memory 0.15071868896484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151749482500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faf1d570-8cc0-48cf-9057-b24f795ba934", "name": "runTaskFromQueue task cost before running: 338 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151749549500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e957b0b8-00c5-4a9b-88b9-3957369def32", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151747079700, "endTime": 32151749599500, "totalTime": 2455900}, "additional": {"logType": "info", "children": [], "durationId": "1946fc6d-e546-4162-ad22-0196742e0eda"}}, {"head": {"id": "c6dec98d-1f1d-495b-8ee9-1aac5798047c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151754101100, "endTime": 32151755197000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "78e6d851-061a-47f3-81cb-7ee69e8941f7", "logId": "1f601cdd-dced-4b28-85b6-09a5503674a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78e6d851-061a-47f3-81cb-7ee69e8941f7", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151751245400}, "additional": {"logType": "detail", "children": [], "durationId": "c6dec98d-1f1d-495b-8ee9-1aac5798047c"}}, {"head": {"id": "91a5f909-7681-42b1-a0ce-ac4a2fe37cd5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151752424800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c9c91fc-107c-410f-bdcd-213bdc8b60de", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151752616700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd80979b-68fb-4c18-8237-d0a232d4ffa2", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151754117100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5a1db8-b4ad-4321-a2a2-a4177e42180e", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151754992400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47ce0457-0c5c-47d3-9a27-bdf4ff006d1c", "name": "entry : default@ProcessProfile cost memory 0.05878448486328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151755104900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f601cdd-dced-4b28-85b6-09a5503674a3", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151754101100, "endTime": 32151755197000}, "additional": {"logType": "info", "children": [], "durationId": "c6dec98d-1f1d-495b-8ee9-1aac5798047c"}}, {"head": {"id": "1d8a2aad-bcbf-4b05-a994-4691ccdb09f4", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151760894600, "endTime": 32151765536500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "20cf7363-2eef-4bfc-9ffc-850194d5feb3", "logId": "b3016e57-3c87-4bab-ba95-bb5f071e0608"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20cf7363-2eef-4bfc-9ffc-850194d5feb3", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151757912200}, "additional": {"logType": "detail", "children": [], "durationId": "1d8a2aad-bcbf-4b05-a994-4691ccdb09f4"}}, {"head": {"id": "65f863ee-2891-45c9-a54d-395173c2ace7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151758538400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa1dbd1-f340-44fd-844d-bd044ff08f39", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151758812400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c909eb0c-cc67-461e-8260-38772bee94dc", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151760906500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155f2597-371a-4c7e-a37a-5eb5d0201beb", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151765317700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb121f24-c895-481b-a142-5ecbdcbb9a77", "name": "entry : default@ProcessRouterMap cost memory 0.19947052001953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151765469300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3016e57-3c87-4bab-ba95-bb5f071e0608", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151760894600, "endTime": 32151765536500}, "additional": {"logType": "info", "children": [], "durationId": "1d8a2aad-bcbf-4b05-a994-4691ccdb09f4"}}, {"head": {"id": "8ac27cbe-5751-43e4-a8d8-24fdeb6a4a30", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151769704300, "endTime": 32151770756900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fe789f46-78b3-4b54-9ff7-bd556d25e3ac", "logId": "6a962c09-8e6b-49b3-a31e-440eecd50b44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe789f46-78b3-4b54-9ff7-bd556d25e3ac", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151768221200}, "additional": {"logType": "detail", "children": [], "durationId": "8ac27cbe-5751-43e4-a8d8-24fdeb6a4a30"}}, {"head": {"id": "bbe10aca-dd23-4d60-b690-7168132f17ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151768664700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "178a8b8a-51a4-42e7-af55-c0c65620e728", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151768863500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49697123-b277-42d4-8999-cf836b40b4ea", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151769714000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "597a01ed-2111-4263-bd64-3e3d53403f62", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151769859600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968f8046-041f-4799-9d19-4d88619a91d0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151769935500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e659b692-82a6-433f-8f7f-b46e9ce0bfc4", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151770594400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a35f3c4-7760-4e7e-b2f6-e293bad7c93b", "name": "runTaskFromQueue task cost before running: 359 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151770696300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a962c09-8e6b-49b3-a31e-440eecd50b44", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151769704300, "endTime": 32151770756900, "totalTime": 973200}, "additional": {"logType": "info", "children": [], "durationId": "8ac27cbe-5751-43e4-a8d8-24fdeb6a4a30"}}, {"head": {"id": "4f241f8a-aef5-4a8f-a317-f4c0bfdc006a", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151775672700, "endTime": 32151781175700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "82887443-ba8f-4e62-a121-28a75e342a90", "logId": "255e42c0-abf9-4d6b-9ec7-8b4926d2874e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82887443-ba8f-4e62-a121-28a75e342a90", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151773066300}, "additional": {"logType": "detail", "children": [], "durationId": "4f241f8a-aef5-4a8f-a317-f4c0bfdc006a"}}, {"head": {"id": "4aa1f148-5413-43a1-9ca8-81af48e6d9b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151773705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab708730-1be1-4106-9a09-116f01675869", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151773822200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab216b4-7ddd-48c2-84e7-a815249a82c5", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151774545600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56256c9-3b79-41c5-82e9-4ea416213d43", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151777209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "058a5b04-035c-489c-9144-b360944ab3f6", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151778999200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eea2ef4-093e-423a-8cdd-c7aaba3a4b69", "name": "entry : default@ProcessResource cost memory 0.1685943603515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151779129400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "255e42c0-abf9-4d6b-9ec7-8b4926d2874e", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151775672700, "endTime": 32151781175700}, "additional": {"logType": "info", "children": [], "durationId": "4f241f8a-aef5-4a8f-a317-f4c0bfdc006a"}}, {"head": {"id": "3032bae3-f012-47eb-9fb9-1018225070c7", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151789745900, "endTime": 32151805222800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9e6d9105-8c36-4a69-a71a-81bf150328bd", "logId": "f34c25ed-a5df-44b5-93a8-a318e1bacd82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e6d9105-8c36-4a69-a71a-81bf150328bd", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151784868800}, "additional": {"logType": "detail", "children": [], "durationId": "3032bae3-f012-47eb-9fb9-1018225070c7"}}, {"head": {"id": "8efb1852-986a-4d66-94de-98a34dc24adf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151785466900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8121af76-951c-4718-9ac0-0b92c1688dd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151785658000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f29a91-0589-4c3c-8338-1a24b7c29988", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151789760600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4e29770-9fab-4633-a687-5f86495763ee", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151805006600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6643ee1-f6bb-4182-bbd4-611b25737e48", "name": "entry : default@GenerateLoaderJson cost memory 0.7617263793945312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151805147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f34c25ed-a5df-44b5-93a8-a318e1bacd82", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151789745900, "endTime": 32151805222800}, "additional": {"logType": "info", "children": [], "durationId": "3032bae3-f012-47eb-9fb9-1018225070c7"}}, {"head": {"id": "a8693836-1049-4692-a173-69c9e846b465", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151812284800, "endTime": 32151815412000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2dc54235-421f-46f6-adb6-cd99df2dd696", "logId": "50a0455d-ed90-41fd-af11-0626fad54593"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2dc54235-421f-46f6-adb6-cd99df2dd696", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151811149600}, "additional": {"logType": "detail", "children": [], "durationId": "a8693836-1049-4692-a173-69c9e846b465"}}, {"head": {"id": "886b949a-7230-40cf-86cd-22ecacf855fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151811479700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "410c6ff9-ae0f-4041-beb8-d5b0ed482bb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151811570800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d362d19-bd9f-45cd-820d-6da0863e894f", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151812300200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44a8dab-3263-4b86-ab81-ec4b7a3ca88e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151814281600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f38b85-a322-4e25-be1d-bc618ede90b1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151814402000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6994c87e-ffc8-43b4-b441-9d7fc2766f75", "name": "entry : default@ProcessLibs cost memory 0.1254425048828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151815219500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964ae6af-1193-4348-8497-a7bb749eb2f8", "name": "runTaskFromQueue task cost before running: 404 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151815345700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50a0455d-ed90-41fd-af11-0626fad54593", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151812284800, "endTime": 32151815412000, "totalTime": 3035300}, "additional": {"logType": "info", "children": [], "durationId": "a8693836-1049-4692-a173-69c9e846b465"}}, {"head": {"id": "c92350d1-b045-4d43-a9a3-6a4a10852154", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151827421600, "endTime": 32151869045500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b0840ba8-485e-4be0-8730-23a2b9adb86a", "logId": "0247e1c7-397f-420a-bbf4-db5c2bcd6085"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0840ba8-485e-4be0-8730-23a2b9adb86a", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151818958900}, "additional": {"logType": "detail", "children": [], "durationId": "c92350d1-b045-4d43-a9a3-6a4a10852154"}}, {"head": {"id": "b8140fdd-5cfe-4824-bbc0-7ba467a45d41", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151820199600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5d8189d-071b-40f2-9ddb-e94c88023667", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151820347500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da44edc5-b484-4607-8ce7-c9ec068514d3", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151823514500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40d273f3-2f7a-40ea-b019-bdc3cc1664f6", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151827448900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a38d4c8-41b6-4337-a659-68dfb493777e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 40 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151868393700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37675a99-518d-404d-b690-374f51164102", "name": "entry : default@CompileResource cost memory -4.4891510009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151868826400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0247e1c7-397f-420a-bbf4-db5c2bcd6085", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151827421600, "endTime": 32151869045500}, "additional": {"logType": "info", "children": [], "durationId": "c92350d1-b045-4d43-a9a3-6a4a10852154"}}, {"head": {"id": "428ebe13-b85d-4695-a31a-038353910a82", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151880304900, "endTime": 32151883744100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e002dab0-590a-41a4-9eac-15e310a1cf1d", "logId": "ba62fa75-62b5-4aaa-94b1-595c580441cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e002dab0-590a-41a4-9eac-15e310a1cf1d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151874683700}, "additional": {"logType": "detail", "children": [], "durationId": "428ebe13-b85d-4695-a31a-038353910a82"}}, {"head": {"id": "dd9371ef-ea1e-40e3-81f3-03f708223cb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151875112600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75e9890e-56b0-4349-a71e-93bd886e46a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151875221000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67311c01-04c2-4b2b-8926-331bf08d2fd7", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151880317300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3febcbe2-1b68-460b-9ac1-7f3d41211820", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151880593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "652af402-9524-43a6-aba6-9f8b0fe5f1e2", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151883403900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f32c4c2-9a36-42e3-a25f-1dcca244e0a5", "name": "entry : default@DoNativeStrip cost memory 0.07678985595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151883642700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba62fa75-62b5-4aaa-94b1-595c580441cd", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151880304900, "endTime": 32151883744100}, "additional": {"logType": "info", "children": [], "durationId": "428ebe13-b85d-4695-a31a-038353910a82"}}, {"head": {"id": "580748dc-e1f5-4cb8-867d-349c6b60b5e5", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151892776800, "endTime": 32151919674100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "eee74c54-bc3b-4bd2-af12-bcd1c810b0e8", "logId": "14c7337b-746e-45e3-81ac-992cff3e9e33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eee74c54-bc3b-4bd2-af12-bcd1c810b0e8", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151886479800}, "additional": {"logType": "detail", "children": [], "durationId": "580748dc-e1f5-4cb8-867d-349c6b60b5e5"}}, {"head": {"id": "8abcbb77-c742-4ad5-b610-d2ef12b79566", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151887032700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e661e7d-7537-43df-9426-ef94b0282404", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151887308500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f7ab3e5-9a51-462f-9957-ab90ecd0b130", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151892789100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3584b91-9d4f-408e-b638-c81b33169e6c", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151919130700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdac2c03-a9d5-4ec0-a891-dc5f137436a5", "name": "entry : default@CompileArkTS cost memory 0.6727523803710938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151919284500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c7337b-746e-45e3-81ac-992cff3e9e33", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151892776800, "endTime": 32151919674100}, "additional": {"logType": "info", "children": [], "durationId": "580748dc-e1f5-4cb8-867d-349c6b60b5e5"}}, {"head": {"id": "c7c968c2-aaba-42e9-a4b0-560f6bf8742c", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151940861000, "endTime": 32151944900000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "89d3a64c-415b-4489-a5a3-b0c377789b78", "logId": "b5379d7e-7276-427a-bb83-773d1e837fc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89d3a64c-415b-4489-a5a3-b0c377789b78", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151931309400}, "additional": {"logType": "detail", "children": [], "durationId": "c7c968c2-aaba-42e9-a4b0-560f6bf8742c"}}, {"head": {"id": "eb738af7-b4e0-4232-bef5-c73c1ff49c0a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151931774300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf8640a-d866-4559-a27e-dab2bfef9be9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151932045900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d425703-3874-4880-a013-1e861ef8c66e", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151940873600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49c49ea-960c-42d2-b587-0e0024765160", "name": "entry : default@BuildJS cost memory 0.12720489501953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151944270700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67ee9782-74a1-406e-b6dd-4a7041ce9409", "name": "runTaskFromQueue task cost before running: 533 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151944559500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5379d7e-7276-427a-bb83-773d1e837fc0", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151940861000, "endTime": 32151944900000, "totalTime": 3669000}, "additional": {"logType": "info", "children": [], "durationId": "c7c968c2-aaba-42e9-a4b0-560f6bf8742c"}}, {"head": {"id": "0de55fb7-c7db-462c-a587-5e114647b638", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151951184100, "endTime": 32151954975400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7d4aec31-d633-4cd6-ad96-5cb611ebf459", "logId": "522e1691-5e94-45b5-ad95-9fd1832e65e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d4aec31-d633-4cd6-ad96-5cb611ebf459", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151947541900}, "additional": {"logType": "detail", "children": [], "durationId": "0de55fb7-c7db-462c-a587-5e114647b638"}}, {"head": {"id": "1f8dd196-f4b8-4a14-b78f-825448b4ad04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151947990000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b22d5951-888b-47a2-9815-e5e3ff253e8f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151948101900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4eb08fc6-3dd3-4002-8ade-47fb04e26574", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151951196300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11eed85a-1048-4ec7-a78b-4bec93496576", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151951631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84233f40-1d86-41ba-8e30-0a57b4b7f99c", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151954772000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcfe30b4-7c2e-4136-8b55-790dc025e17d", "name": "entry : default@CacheNativeLibs cost memory 0.08771514892578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151954901200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "522e1691-5e94-45b5-ad95-9fd1832e65e7", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151951184100, "endTime": 32151954975400}, "additional": {"logType": "info", "children": [], "durationId": "0de55fb7-c7db-462c-a587-5e114647b638"}}, {"head": {"id": "c0b56515-4145-4121-8796-061a6fc35243", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151962727400, "endTime": 32151964098900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "2f4ff228-76f2-41f3-be94-7362a310d50e", "logId": "090bb49b-ff83-4202-88b9-ee3233975986"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f4ff228-76f2-41f3-be94-7362a310d50e", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151957999800}, "additional": {"logType": "detail", "children": [], "durationId": "c0b56515-4145-4121-8796-061a6fc35243"}}, {"head": {"id": "68a5e7ca-a05f-4aac-8614-c6c78b1dd7dc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151958445500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0084f25a-5abd-4bf0-9dcf-b80d7ec23087", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151958553200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d604903e-72e2-471c-ada2-49b33a50ebe4", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151962739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcfd8c77-c1d2-4e0f-b910-04dd8ce00d07", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151963009600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dd5722f-e9f5-4f9d-8da9-1348334de6bc", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151963918500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15a0399-9817-4bf7-92f8-c2b8f28cee18", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0699615478515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151964032900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "090bb49b-ff83-4202-88b9-ee3233975986", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151962727400, "endTime": 32151964098900}, "additional": {"logType": "info", "children": [], "durationId": "c0b56515-4145-4121-8796-061a6fc35243"}}, {"head": {"id": "68237751-82ec-4d0a-8ffc-05f172d2cd1e", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151977661700, "endTime": 32151993039200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e81cd005-186d-4f9d-b2bd-8815a9d41ceb", "logId": "90939cc7-46b0-4bae-bcc9-d156e4afc4f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e81cd005-186d-4f9d-b2bd-8815a9d41ceb", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151967473400}, "additional": {"logType": "detail", "children": [], "durationId": "68237751-82ec-4d0a-8ffc-05f172d2cd1e"}}, {"head": {"id": "4ea23011-f767-48b9-886e-a5b3a7d8869b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151968082400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9809d046-4d2a-4a66-9941-d269db372d55", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151968315200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eea9211-1766-4a79-bb1c-522c2d5479f2", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151977672000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6a024ce-7fa8-437b-a7c6-141faa3a09d6", "name": "Incremental task entry:default@PackageHap pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151992846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a8b3b42-67c3-497c-bf3d-fa1a92fde751", "name": "entry : default@PackageHap cost memory 0.8307037353515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151992976400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90939cc7-46b0-4bae-bcc9-d156e4afc4f6", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151977661700, "endTime": 32151993039200}, "additional": {"logType": "info", "children": [], "durationId": "68237751-82ec-4d0a-8ffc-05f172d2cd1e"}}, {"head": {"id": "4937483e-4b90-4354-95f1-ff5134bf1b85", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********8200, "endTime": 32152000364700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "51403cc9-bc86-464d-9efc-ed16fb28e7cf", "logId": "aebf21db-e2e7-4358-b478-a90d84c104dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51403cc9-bc86-464d-9efc-ed16fb28e7cf", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151996039400}, "additional": {"logType": "detail", "children": [], "durationId": "4937483e-4b90-4354-95f1-ff5134bf1b85"}}, {"head": {"id": "65cd96e2-2521-4d92-b172-4718a88ffbd9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151996446600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcc34ab6-15ec-42a4-8030-03c937fde1b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151996541900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e135b7-94c7-497e-99ef-9abca067e75e", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151998517800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9adc9f9-24e7-42f8-a5e5-abfe46c427a2", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151999010900}, "additional": {"logType": "warn", "children": [], "durationId": "4937483e-4b90-4354-95f1-ff5134bf1b85"}}, {"head": {"id": "b334e642-08ff-42e3-a3b6-2e32aaac9bb5", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151999741300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12e01ae3-32af-4256-9e97-79454b35c893", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151999882000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c4c008-d430-46b4-abae-a7bbd4da269c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151999973100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41dba913-ea2b-430f-8123-3280776bdde2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152000030100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b7b5a91-7f61-4c2c-9540-7e152453f6e6", "name": "entry : default@SignHap cost memory 0.11704254150390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152000233800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd9f2a48-0994-4984-a396-530ce70ded91", "name": "runTaskFromQueue task cost before running: 589 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152000311900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebf21db-e2e7-4358-b478-a90d84c104dc", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********8200, "endTime": 32152000364700, "totalTime": 1789200}, "additional": {"logType": "info", "children": [], "durationId": "4937483e-4b90-4354-95f1-ff5134bf1b85"}}, {"head": {"id": "bc4d3154-24fe-4808-9fb2-578ab9275367", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152004183600, "endTime": 32152010318400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fb7ff9fa-73cc-4e78-9a92-42d6ab9db7ca", "logId": "dc38f06d-73a3-4ad1-ba8e-87c3c7b86635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb7ff9fa-73cc-4e78-9a92-42d6ab9db7ca", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152002727000}, "additional": {"logType": "detail", "children": [], "durationId": "bc4d3154-24fe-4808-9fb2-578ab9275367"}}, {"head": {"id": "97673aa1-205c-4051-8ec3-12c2db3bc5b4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152003232400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "360c5278-40cf-4505-9b8d-7b8c09c89a12", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152003336300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07400679-4651-4c93-ae74-3f6c681ea9e2", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152004192000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f28a7c-403c-4b14-b371-2f1de13adb87", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152009806300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81180d5f-7b02-44d8-bd6f-6a5fd891f285", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152009936300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae46a785-baaf-4d71-a8ef-8e8f5912dcaa", "name": "entry : default@CollectDebugSymbol cost memory 0.2393798828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152010023000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c023d584-42fe-4ce1-803d-ac5cfe1079d9", "name": "runTaskFromQueue task cost before running: 599 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152010097000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc38f06d-73a3-4ad1-ba8e-87c3c7b86635", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152004183600, "endTime": 32152010318400, "totalTime": 5895700}, "additional": {"logType": "info", "children": [], "durationId": "bc4d3154-24fe-4808-9fb2-578ab9275367"}}, {"head": {"id": "84c52527-ef28-4dc3-8df7-d1c8723f7b01", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152012125400, "endTime": 32152012375600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d94c1f69-4fbb-4abb-8dd5-a4608c4101fe", "logId": "ae1abede-7acf-4495-a9cd-670915f5160c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d94c1f69-4fbb-4abb-8dd5-a4608c4101fe", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152012076600}, "additional": {"logType": "detail", "children": [], "durationId": "84c52527-ef28-4dc3-8df7-d1c8723f7b01"}}, {"head": {"id": "ceab3729-c231-4a06-9ffa-ce7f9849ef3b", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152012131300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d6ca69-e72b-428c-8117-e1a9e254dc2e", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152012251600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be53096a-b13e-413e-8d68-01175ef2db83", "name": "runTaskFromQueue task cost before running: 601 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152012325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1abede-7acf-4495-a9cd-670915f5160c", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152012125400, "endTime": 32152012375600, "totalTime": 183500}, "additional": {"logType": "info", "children": [], "durationId": "84c52527-ef28-4dc3-8df7-d1c8723f7b01"}}, {"head": {"id": "5c30a56b-c908-4103-8f3d-fb4e52e6b15f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020058800, "endTime": 32152020077300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "208d9dfa-f1e5-42b5-bfb0-47d39a63b93b", "logId": "887981e6-4cc9-4f53-b87d-5dfaffa3129e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "887981e6-4cc9-4f53-b87d-5dfaffa3129e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020058800, "endTime": 32152020077300}, "additional": {"logType": "info", "children": [], "durationId": "5c30a56b-c908-4103-8f3d-fb4e52e6b15f"}}, {"head": {"id": "1b6896c3-ee45-404b-9d1e-ea2ebf9aa375", "name": "BUILD SUCCESSFUL in 609 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020114100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8784a2d0-dca2-4a64-83e0-1e22f5ed2a3a", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32151411951300, "endTime": 32152020348500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 25}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ebd5212d-917f-4d86-a739-cf80703fa611", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020374300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a3fffce-a261-4afe-9e71-b570a92e907d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020440900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a87ab45-6061-47ac-91bf-e752c8a26fa2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020485300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ad85d6a-cc28-41b2-8cb8-365cacbde6cb", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020527000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f30d338-d3b6-402d-9c00-022018cb9284", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152020582000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c470411e-5cfa-4c83-a6e0-3f0a488cfce2", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152021929900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a3a31bf-4e04-4678-a5d1-9492ecc8b94c", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152022756600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b55d1a-6ca1-4fea-8bc8-945f93c241e3", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152023070100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45bb45dc-12ff-438d-b5cd-fe83d87fa930", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152023150500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb16961a-0279-4856-91fb-1db8d7103d96", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152023209000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "014ebb7d-fe29-4fbb-a2be-3aa880337a53", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152023470600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "922bf813-4991-4184-bac6-24937a56430a", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152024359100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1860eb-07dd-483c-965a-a3d50a9235e1", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152024595900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f6f657c-eb41-4ff3-8635-a6969164ea55", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152024664200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a30d3b8-75d9-48c2-9684-d220c4c12787", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152024718900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f38f723-5384-4622-82c8-c8228b3a6772", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152024763000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe2b644c-4545-46bd-9517-8e2a6bdd4ef5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152024807400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7548a15a-0827-4505-8f6a-50902ee6ec12", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025079000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13dc964d-4715-4832-a075-95646ae3f5c4", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025337400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953c7913-6686-4e0d-a2ed-63b74e491f60", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025522800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a7c5b95-e5e5-44a5-abdc-eebd357aacfd", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3b0254-f080-430b-bc04-3518dff37446", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025841400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02bcc3a6-935d-4c3d-a1f5-851db259e012", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025900200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61ed1eb1-132a-4919-94c6-9f17aa746ac2", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152025949800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "077f8bbf-a27c-4c88-a0a1-3dc2396b5ad0", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152027315200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7208f0ce-ce22-4ef5-bb39-a8ef7283d442", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152027861500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c170bf-1e6d-4884-be28-fbdcfd707fb4", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152028725700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebb6bcd0-0ccb-476a-86ff-bfdf2ef5f5b6", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152028951000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed66da5b-db8e-44f2-befd-96176580ce00", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152029159000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62718364-2631-438e-a7c5-a2c7f86edc20", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152029672700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266d2f93-969f-4231-9e29-0ebca165433d", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152029989300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5607e318-823f-4af9-a522-b505a45623fa", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152030059800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cf19d59-2391-4a0e-bd67-5b9e14d71101", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152030113900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb427a8-9105-4029-922b-4f781e0b8794", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152030168900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa1a5f6a-15be-4368-af38-4747bbe4fbbe", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152030316200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afe0890b-649d-4afe-90b9-45b93b730edb", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152030496900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3a39f1d-e2f5-48fc-af14-b4ad2cfa0bc5", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152030723800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b3ecf0-919e-4f87-91b3-2b0f74266640", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152032963300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626e4a19-7140-492d-a5f0-f0c97ce7bcde", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152033308500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3a718c-70ed-4f75-a513-0e5e0c75e343", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152033571300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "288caeb6-6084-4957-8241-b0f93c6ec032", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32152033835200}, "additional": {"logType": "debug", "children": []}}], "workLog": []}