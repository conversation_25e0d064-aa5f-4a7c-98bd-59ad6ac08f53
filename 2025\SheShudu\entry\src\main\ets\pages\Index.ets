import { authentication } from '@kit.AccountKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';

const DOMAIN = 0x0000;

@Entry
@Component
struct Index {
  @State message: string = 'Hello World';
  @Provide('naviStack') pageInfos: NavPathStack = new NavPathStack()

  build() {
    Navigation(this.pageInfos) {
      GridRow({
        columns: {
          sm: 1, // 小屏1列
          md: 2, // 中屏2列
          lg: 4  // 大屏4列
        },
        gutter: { x: 12, y: 12 }
      }) {
        GridCol({
          span: {
            sm: 1, // 小屏占1格
            md: 2, // 中屏占1格
            lg: 4  // 大屏占1格
          }
        }) {
          Column() {
            Text('数海鹅独')

          }.height(200)
          .justifyContent(FlexAlign.Center)

        }
        GridCol({
          span: {
            sm: 1, // 小屏占1格
            md: 1, // 中屏占1格
            lg: 1  // 大屏占1格
          }
        }) {
          Column() {
            Text('游戏开始')
              .onClick(() => {
                //跳转到游戏界面
                this.pageInfos.pushPathByName('shuduGame', null);
              })
          }.height(200)
          .justifyContent(FlexAlign.Center)

        }
        GridCol({
          span: {
            sm: 1, // 小屏占1格
            md: 1, // 中屏占1格
            lg: 1  // 大屏占1格
          }
        }) {
          Column() {
            Text('玩法介绍')
              .onClick(() => {
                //跳转到游戏界面
                this.pageInfos.pushPathByName('gameRules', null);
              })
          }.height(200)
          .justifyContent(FlexAlign.Center)

        }

      }
    }
    .hideTitleBar(true)
    .mode(NavigationMode.Stack)

  }

  aboutToAppear() {
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
    this.loginWithHuaweiID();
  }

  /**
   * Sample code for using HUAWEI ID to log in to atomic service.
   * According to the Atomic Service Review Guide, when a atomic service has an account system,
   * the option to log in with a HUAWEI ID must be provided.
   * The following presets the atomic service to use the HUAWEI ID silent login function.
   * To enable the atomic service to log in successfully using the HUAWEI ID, please refer
   * to the HarmonyOS HUAWEI ID Access Guide to configure the client ID and fingerprint certificate.
   */
  private loginWithHuaweiID() {
    // Create a login request and set parameters
    const loginRequest = new authentication.HuaweiIDProvider().createLoginWithHuaweiIDRequest();
    // Whether to forcibly launch the HUAWEI ID login page when the user is not logged in with the HUAWEI ID
    loginRequest.forceLogin = false;
    // Execute login request
    const controller = new authentication.AuthenticationController();
    controller.executeRequest(loginRequest).then((data) => {
      const loginWithHuaweiIDResponse = data as authentication.LoginWithHuaweiIDResponse;
      const authCode = loginWithHuaweiIDResponse.data?.authorizationCode;
      // Send authCode to the backend in exchange for unionID, session

    }).catch((error: BusinessError) => {
      hilog.error(DOMAIN, 'testTag', 'error: %{public}s', JSON.stringify(error));
      if (error.code === authentication.AuthenticationErrorCode.ACCOUNT_NOT_LOGGED_IN) {
        // HUAWEI ID is not logged in, it is recommended to jump to the login guide page

      }
    });
  }
}