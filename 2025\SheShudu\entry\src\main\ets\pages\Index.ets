import { authentication } from '@kit.AccountKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';

const DOMAIN = 0x0000;

@Entry
@Component
struct Index {
  @State message: string = 'Hello World';
  @Provide('naviStack') pageInfos: NavPathStack = new NavPathStack()
  @State currentTime: string = '';
  private scroller: Scroller = new Scroller();

  build() {
    Navigation(this.pageInfos) {
      Scroll(this.scroller) {
        Column({ space: 0 }) {
          // 顶部欢迎区域
          Column() {
            Column({ space: 16 }) {
              // 游戏Logo和标题
              Row({ space: 12 }) {
                Image($r('app.media.startIcon'))
                  .width(60)
                  .height(60)
                  .borderRadius(12)
                  .shadow({
                    radius: 8,
                    color: '#1F000000',
                    offsetX: 0,
                    offsetY: 2
                  })

                Column({ space: 4 }) {
                  Text('数海鹅独')
                    .fontSize(32)
                    .fontWeight(FontWeight.Bold)
                    .fontColor('#2C3E50')
                    .letterSpacing(2)

                  Text('智慧数独，趣味无穷')
                    .fontSize(14)
                    .fontColor('#7F8C8D')
                    .opacity(0.8)
                }
                .alignItems(HorizontalAlign.Start)
              }
              .width('100%')
              .justifyContent(FlexAlign.Start)

              // 欢迎文字
              Text('欢迎来到数独世界，挑战你的逻辑思维！')
                .fontSize(16)
                .fontColor('#34495E')
                .textAlign(TextAlign.Center)
                .lineHeight(24)
                .margin({ top: 8 })
            }
            .width('90%')
            .padding({ top: 40, bottom: 30 })
          }
          .width('100%')
          .linearGradient({
            angle: 135,
            colors: [['#E8F4FD', 0.0], ['#F0F8FF', 0.5], ['#FFFFFF', 1.0]]
          })

          // 主要功能区域
          Column({ space: 20 }) {
            // 开始游戏按钮
            Button() {
              Row({ space: 12 }) {
                Image($r('app.media.shudu'))
                  .width(24)
                  .height(24)
                  .fillColor('#FFFFFF')

                Text('开始游戏')
                  .fontSize(18)
                  .fontWeight(FontWeight.Medium)
                  .fontColor('#FFFFFF')
              }
            }
            .width('85%')
            .height(56)
            .backgroundColor('#3498DB')
            .borderRadius(28)
            .shadow({
              radius: 12,
              color: '#1F3498DB',
              offsetX: 0,
              offsetY: 4
            })
            .onClick(() => {
              this.pageInfos.pushPathByName('shuduGame', null);
            })

            // 功能卡片区域
            Column({ space: 16 }) {
              Text('游戏特色')
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .fontColor('#2C3E50')
                .margin({ bottom: 8 })

              // 第一行卡片
              Row({ space: 12 }) {
                // 经典模式卡片
                Column({ space: 8 }) {
                  Image($r('app.media.number6'))
                    .width(40)
                    .height(40)

                  Text('经典模式')
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#2C3E50')

                  Text('传统1-9数字\n逻辑推理挑战')
                    .fontSize(12)
                    .fontColor('#7F8C8D')
                    .textAlign(TextAlign.Center)
                    .lineHeight(16)
                }
                .width('48%')
                .height(130)
                .backgroundColor('#FFFFFF')
                .borderRadius(16)
                .padding(16)
                .justifyContent(FlexAlign.Center)
                .shadow({
                  radius: 8,
                  color: '#0F000000',
                  offsetX: 0,
                  offsetY: 2
                })

                // 卡通模式卡片
                Column({ space: 8 }) {
                  Image($r('app.media.animal8'))
                    .width(40)
                    .height(40)

                  Text('卡通模式')
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#2C3E50')

                  Text('可爱动物图标\n趣味数独体验')
                    .fontSize(12)
                    .fontColor('#7F8C8D')
                    .textAlign(TextAlign.Center)
                    .lineHeight(16)
                }
                .width('48%')
                .height(130)
                .backgroundColor('#FFFFFF')
                .borderRadius(16)
                .padding(16)
                .justifyContent(FlexAlign.Center)
                .shadow({
                  radius: 8,
                  color: '#0F000000',
                  offsetX: 0,
                  offsetY: 2
                })
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)

              // 第二行卡片
              Row({ space: 12 }) {
                // 自在模式卡片
                Column({ space: 8 }) {
                  Image($r('app.media.number7'))
                    .width(40)
                    .height(40)

                  Text('自在模式')
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#2C3E50')

                  Text('空白棋盘开始\n自由创作挑战')
                    .fontSize(12)
                    .fontColor('#7F8C8D')
                    .textAlign(TextAlign.Center)
                    .lineHeight(16)
                }
                .width('48%')
                .height(130)
                .backgroundColor('#FFFFFF')
                .borderRadius(16)
                .padding(16)
                .justifyContent(FlexAlign.Center)
                .shadow({
                  radius: 8,
                  color: '#0F000000',
                  offsetX: 0,
                  offsetY: 2
                })

                // 多人协作卡片
                Column({ space: 8 }) {
                  Image($r('app.media.personlize'))
                    .width(40)
                    .height(40)
                    .fillColor('#E74C3C')

                  Text('多人协作')
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#2C3E50')

                  Text('双人轮流填写\n共同完成挑战')
                    .fontSize(12)
                    .fontColor('#7F8C8D')
                    .textAlign(TextAlign.Center)
                    .lineHeight(16)
                }
                .width('48%')
                .height(130)
                .backgroundColor('#FFFFFF')
                .borderRadius(16)
                .padding(16)
                .justifyContent(FlexAlign.Center)
                .shadow({
                  radius: 8,
                  color: '#0F000000',
                  offsetX: 0,
                  offsetY: 2
                })
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
            }
            .width('90%')
            .margin({ top: 20 })

            // 快捷操作区域
            Column({ space: 16 }) {
              Text('快捷操作')
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .fontColor('#2C3E50')
                .margin({ bottom: 8 })

              Row({ space: 12 }) {
                // 玩法介绍按钮
                Button() {
                  Row({ space: 8 }) {
                    Image($r('app.media.doc'))
                      .width(20)
                      .height(20)
                      .fillColor('#FFFFFF')

                    Text('玩法介绍')
                      .fontSize(16)
                      .fontColor('#FFFFFF')
                  }
                }
                .width('48%')
                .height(48)
                .backgroundColor('#27AE60')
                .borderRadius(24)
                .onClick(() => {
                  this.pageInfos.pushPathByName('gameRules', null);
                })

                // 个人中心按钮
                Button() {
                  Row({ space: 8 }) {
                    Image($r('app.media.personlize'))
                      .width(20)
                      .height(20)
                      .fillColor('#FFFFFF')

                    Text('个人中心')
                      .fontSize(16)
                      .fontColor('#FFFFFF')
                  }
                }
                .width('48%')
                .height(48)
                .backgroundColor('#9B59B6')
                .borderRadius(24)
                .onClick(() => {
                  this.pageInfos.pushPathByName('personalization', null);
                })
              }
              .width('100%')
              .justifyContent(FlexAlign.SpaceBetween)
            }
            .width('90%')
            .margin({ top: 20 })

            // 底部装饰区域
            Column({ space: 12 }) {
              Row({ space: 8 }) {
                ForEach([1, 2, 3, 4, 5], (item: number) => {
                  Image($r(`app.media.animal${item}`))
                    .width(32)
                    .height(32)
                    .opacity(0.6)
                }, (item: number) => item.toString())
              }
              .justifyContent(FlexAlign.Center)

              Text('享受数独的乐趣，锻炼逻辑思维')
                .fontSize(14)
                .fontColor('#95A5A6')
                .textAlign(TextAlign.Center)
                .opacity(0.8)
            }
            .width('100%')
            .padding({ top: 30, bottom: 40 })
          }
          .width('100%')
          .backgroundColor('#F8F9FA')
          .padding({ top: 30 })
        }
      }
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)
      .edgeEffect(EdgeEffect.Spring)
    }
    .hideTitleBar(true)
    .mode(NavigationMode.Stack)
    .backgroundColor('#F8F9FA')

  }

  aboutToAppear() {
    hilog.info(DOMAIN, 'testTag', '%{public}s', 'Ability onCreate');
    //this.loginWithHuaweiID();
  }

  /**
   * Sample code for using HUAWEI ID to log in to atomic service.
   * According to the Atomic Service Review Guide, when a atomic service has an account system,
   * the option to log in with a HUAWEI ID must be provided.
   * The following presets the atomic service to use the HUAWEI ID silent login function.
   * To enable the atomic service to log in successfully using the HUAWEI ID, please refer
   * to the HarmonyOS HUAWEI ID Access Guide to configure the client ID and fingerprint certificate.
   */
  private loginWithHuaweiID() {
    // Create a login request and set parameters
    const loginRequest = new authentication.HuaweiIDProvider().createLoginWithHuaweiIDRequest();
    // Whether to forcibly launch the HUAWEI ID login page when the user is not logged in with the HUAWEI ID
    loginRequest.forceLogin = false;
    // Execute login request
    const controller = new authentication.AuthenticationController();
    controller.executeRequest(loginRequest).then((data) => {
      const loginWithHuaweiIDResponse = data as authentication.LoginWithHuaweiIDResponse;
      const authCode = loginWithHuaweiIDResponse.data?.authorizationCode;
      // Send authCode to the backend in exchange for unionID, session

    }).catch((error: BusinessError) => {
      hilog.error(DOMAIN, 'testTag', 'error: %{public}s', JSON.stringify(error));
      if (error.code === authentication.AuthenticationErrorCode.ACCOUNT_NOT_LOGGED_IN) {
        // HUAWEI ID is not logged in, it is recommended to jump to the login guide page

      }
    });
  }
}