{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "bf2c244a-7eaf-4bc3-be8b-c373eef7b0ec", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27615979951700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dc60568-0516-4062-82aa-00d580c71198", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27616011740800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d55b0fdd-a4c5-4dd5-9f66-2c569f8f6bf9", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27616012646700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ad432c-519b-4311-89c9-b7e971e9a9a4", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27616013248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8b066f-ec86-4450-933c-d721643f6872", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854610894300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db51e971-d7bd-4803-901e-b0a400e29ae7", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854618036200, "endTime": 27855216034000}, "additional": {"children": ["ed607c4f-cca6-4775-ba43-a67a43ad0812", "f75468b7-8e0c-4076-9174-0acd78723b6f", "f5ee0724-8276-4481-9a7c-276b6355c41b", "03e12a4b-dac6-481c-bb1b-47692a5ab678", "8e698685-fb3a-4946-9793-0cd7c714761f", "b640528c-d27b-4cf0-ba59-64e956edef1a", "9cdfab1f-5f18-408a-b368-c0e29d5491f0"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed607c4f-cca6-4775-ba43-a67a43ad0812", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854618039500, "endTime": 27854635616000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "91d9fe10-91aa-4eac-829f-929ae7c70cf5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f75468b7-8e0c-4076-9174-0acd78723b6f", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854635644900, "endTime": 27855194821400}, "additional": {"children": ["d28e65dc-116b-474d-80a0-960c2092aab7", "60dfb195-e819-4157-86c5-997bdb970e2b", "bbd265b1-23d6-4d71-89a3-af119dba8bf0", "6018c414-e53d-4a8f-b60b-bb75cacf82c1", "437a161b-58b9-4efa-b118-bbd70d9fd837", "b9f2945e-b591-4cc2-94e9-13239cd7c13f", "a2c193e9-e5bc-4d50-8613-e8410401db54", "4df7fdd8-862c-4652-a848-0dad6c9a27d4", "0f0e0bea-7881-4f45-8c92-1aeb8a9aead7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5ee0724-8276-4481-9a7c-276b6355c41b", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855194855400, "endTime": 27855216022400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "c5cb8d0a-ae7a-4013-9cb1-34cb6111ced6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03e12a4b-dac6-481c-bb1b-47692a5ab678", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855216028100, "endTime": 27855216029900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "58e4b169-27e4-46b2-8555-8258461c29f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e698685-fb3a-4946-9793-0cd7c714761f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854622030900, "endTime": 27854622082000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "87e9a748-0fc6-4a1f-a866-97744d470a9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e9a748-0fc6-4a1f-a866-97744d470a9c", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854622030900, "endTime": 27854622082000}, "additional": {"logType": "info", "children": [], "durationId": "8e698685-fb3a-4946-9793-0cd7c714761f", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "b640528c-d27b-4cf0-ba59-64e956edef1a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854630918500, "endTime": 27854630938400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "84904639-0ab7-42dd-a224-396c4728cdd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84904639-0ab7-42dd-a224-396c4728cdd3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854630918500, "endTime": 27854630938400}, "additional": {"logType": "info", "children": [], "durationId": "b640528c-d27b-4cf0-ba59-64e956edef1a", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "f4c290d1-43c5-4cab-93c6-07801b54ba24", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854630982400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a242a6d4-c167-484f-b9ed-0474fc0fde3d", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854635472000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91d9fe10-91aa-4eac-829f-929ae7c70cf5", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854618039500, "endTime": 27854635616000}, "additional": {"logType": "info", "children": [], "durationId": "ed607c4f-cca6-4775-ba43-a67a43ad0812", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "d28e65dc-116b-474d-80a0-960c2092aab7", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854642839800, "endTime": 27854642855400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "0c5e3c71-8ed0-47d1-a50d-e58337dd0621"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60dfb195-e819-4157-86c5-997bdb970e2b", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854642874200, "endTime": 27854647791200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "c10c1a02-b75d-41eb-8687-320b68a5c1d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbd265b1-23d6-4d71-89a3-af119dba8bf0", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854647842300, "endTime": 27854816201400}, "additional": {"children": ["e65241c4-a5fb-480f-9813-3f8a3ad3910c", "6646924a-1beb-4db1-9955-147216f48fb4", "5bec6814-17a9-4861-9f40-000a6b21f047"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "30977429-5f9b-49c4-b3ce-d676ff62b3e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6018c414-e53d-4a8f-b60b-bb75cacf82c1", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854816215000, "endTime": 27854875031900}, "additional": {"children": ["320d6315-c494-4bc9-9d85-b70a4ded6e9f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "0f6696bf-22c1-4f2b-805f-52a86ef0f6bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "437a161b-58b9-4efa-b118-bbd70d9fd837", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854875039800, "endTime": 27855155537300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "7509199c-7735-4885-a59a-7a4e2befe1bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9f2945e-b591-4cc2-94e9-13239cd7c13f", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855161172600, "endTime": 27855175777700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "f7953c3d-68b1-4227-8716-f879c145e1dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a2c193e9-e5bc-4d50-8613-e8410401db54", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855175807100, "endTime": 27855194467400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "1a6438c6-e366-410d-9201-893d932440a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4df7fdd8-862c-4652-a848-0dad6c9a27d4", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855194496900, "endTime": 27855194763600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "99ad3778-ca70-4457-b72a-854017960179"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c5e3c71-8ed0-47d1-a50d-e58337dd0621", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854642839800, "endTime": 27854642855400}, "additional": {"logType": "info", "children": [], "durationId": "d28e65dc-116b-474d-80a0-960c2092aab7", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "c10c1a02-b75d-41eb-8687-320b68a5c1d4", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854642874200, "endTime": 27854647791200}, "additional": {"logType": "info", "children": [], "durationId": "60dfb195-e819-4157-86c5-997bdb970e2b", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "e65241c4-a5fb-480f-9813-3f8a3ad3910c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854648769200, "endTime": 27854648796000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bbd265b1-23d6-4d71-89a3-af119dba8bf0", "logId": "cd87e843-9d41-45d2-b665-842d422ca3af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd87e843-9d41-45d2-b665-842d422ca3af", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854648769200, "endTime": 27854648796000}, "additional": {"logType": "info", "children": [], "durationId": "e65241c4-a5fb-480f-9813-3f8a3ad3910c", "parent": "30977429-5f9b-49c4-b3ce-d676ff62b3e3"}}, {"head": {"id": "6646924a-1beb-4db1-9955-147216f48fb4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854651315100, "endTime": 27854813667000}, "additional": {"children": ["e9677443-0cd4-4b8b-9444-0aa922317807", "608c01c7-0ea8-468e-9be8-f53b60550049"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bbd265b1-23d6-4d71-89a3-af119dba8bf0", "logId": "691d716b-214d-4fbd-825c-38bb95c8f261"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9677443-0cd4-4b8b-9444-0aa922317807", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854651317300, "endTime": 27854658713200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6646924a-1beb-4db1-9955-147216f48fb4", "logId": "919c4da9-a18d-4bc2-b4c2-8a6664e71078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "608c01c7-0ea8-468e-9be8-f53b60550049", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854658743000, "endTime": 27854813635200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6646924a-1beb-4db1-9955-147216f48fb4", "logId": "bb4c6fd6-924a-4fc3-972e-3ec6e90865f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "858048fa-e763-48d1-a487-a0bce45af11f", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854651324200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa56728-e826-4f9b-a359-bac398d426e3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854657694600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "919c4da9-a18d-4bc2-b4c2-8a6664e71078", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854651317300, "endTime": 27854658713200}, "additional": {"logType": "info", "children": [], "durationId": "e9677443-0cd4-4b8b-9444-0aa922317807", "parent": "691d716b-214d-4fbd-825c-38bb95c8f261"}}, {"head": {"id": "d13dc686-10cc-4265-aaef-c05c65399c21", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854658850800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f01af7e0-de5d-4a89-8771-0c0364122aa4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854665268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed089b1-af80-4f5f-a8b7-4cf95eaeef4f", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854665605700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5597950-9026-4511-bfa0-5c6a7fede1df", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854665857300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37057358-0d0f-4c47-bc84-509343eaa220", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854666054900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1507db8c-4b2b-473a-ac5a-4d92523f1158", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854669322600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37e8fa20-ed52-4e24-94df-d181a017f566", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854675407500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a1973d2-4782-4d6d-b940-d2aecefb8f72", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854695627400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d857811-7bcf-46b1-acf4-a9a0921a669d", "name": "Sdk init in 75 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854751356100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b60bee-e1b3-4e43-b9d8-e4e80d303d79", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854751490100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 13}, "markType": "other"}}, {"head": {"id": "7ac4d9a4-65c2-4d2a-b133-d841997bfac6", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854751503600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 13}, "markType": "other"}}, {"head": {"id": "42063ff7-7e37-482c-b44a-855ebb9b6d5b", "name": "Project task initialization takes 60 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854812178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6425337c-8a06-44f9-8b69-0a3e4f5c37e3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854813061000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "175d16cd-331c-4cf1-9225-3609e6057b68", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854813378300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb666a3-e9d7-40b9-b6f3-27dd9953cd08", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854813472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb4c6fd6-924a-4fc3-972e-3ec6e90865f0", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854658743000, "endTime": 27854813635200}, "additional": {"logType": "info", "children": [], "durationId": "608c01c7-0ea8-468e-9be8-f53b60550049", "parent": "691d716b-214d-4fbd-825c-38bb95c8f261"}}, {"head": {"id": "691d716b-214d-4fbd-825c-38bb95c8f261", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854651315100, "endTime": 27854813667000}, "additional": {"logType": "info", "children": ["919c4da9-a18d-4bc2-b4c2-8a6664e71078", "bb4c6fd6-924a-4fc3-972e-3ec6e90865f0"], "durationId": "6646924a-1beb-4db1-9955-147216f48fb4", "parent": "30977429-5f9b-49c4-b3ce-d676ff62b3e3"}}, {"head": {"id": "5bec6814-17a9-4861-9f40-000a6b21f047", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854816147700, "endTime": 27854816180000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bbd265b1-23d6-4d71-89a3-af119dba8bf0", "logId": "3bd168b5-9dee-4a97-ae60-0b21d7899719"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3bd168b5-9dee-4a97-ae60-0b21d7899719", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854816147700, "endTime": 27854816180000}, "additional": {"logType": "info", "children": [], "durationId": "5bec6814-17a9-4861-9f40-000a6b21f047", "parent": "30977429-5f9b-49c4-b3ce-d676ff62b3e3"}}, {"head": {"id": "30977429-5f9b-49c4-b3ce-d676ff62b3e3", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854647842300, "endTime": 27854816201400}, "additional": {"logType": "info", "children": ["cd87e843-9d41-45d2-b665-842d422ca3af", "691d716b-214d-4fbd-825c-38bb95c8f261", "3bd168b5-9dee-4a97-ae60-0b21d7899719"], "durationId": "bbd265b1-23d6-4d71-89a3-af119dba8bf0", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "320d6315-c494-4bc9-9d85-b70a4ded6e9f", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854817182700, "endTime": 27854875016600}, "additional": {"children": ["1d4f0a22-fa60-4d59-9b04-d981dd6f37da", "fbaabd70-ca3d-4b6e-8022-2065f4bf0485", "5016860f-7674-46d3-8a13-16648d435322"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6018c414-e53d-4a8f-b60b-bb75cacf82c1", "logId": "acf2b46e-0285-4046-a7a9-452362dacdf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d4f0a22-fa60-4d59-9b04-d981dd6f37da", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854831386600, "endTime": 27854831412900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "320d6315-c494-4bc9-9d85-b70a4ded6e9f", "logId": "06ca4d2f-43aa-45da-b5ff-e1757216c812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06ca4d2f-43aa-45da-b5ff-e1757216c812", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854831386600, "endTime": 27854831412900}, "additional": {"logType": "info", "children": [], "durationId": "1d4f0a22-fa60-4d59-9b04-d981dd6f37da", "parent": "acf2b46e-0285-4046-a7a9-452362dacdf1"}}, {"head": {"id": "fbaabd70-ca3d-4b6e-8022-2065f4bf0485", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854837917500, "endTime": 27854870930400}, "additional": {"children": ["bd2deac9-7a28-4765-a885-4fed351e39ef", "ef9ed489-1fb3-4e68-9ab1-f716e3b9421d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "320d6315-c494-4bc9-9d85-b70a4ded6e9f", "logId": "4f6d8ca4-3472-4f62-95db-16831d8efabb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd2deac9-7a28-4765-a885-4fed351e39ef", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854837919800, "endTime": 27854846363900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fbaabd70-ca3d-4b6e-8022-2065f4bf0485", "logId": "c095a9b1-e23d-47fb-88f3-57f3a79da235"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef9ed489-1fb3-4e68-9ab1-f716e3b9421d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854846385700, "endTime": 27854870911700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fbaabd70-ca3d-4b6e-8022-2065f4bf0485", "logId": "a0634029-dce6-46f0-8cbd-c8764bee6c05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43c0808b-cea7-4bd5-9ed0-4cf765d06f81", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854837996500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7544e84-d9ae-4b03-be25-cfd480596b79", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854846183600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c095a9b1-e23d-47fb-88f3-57f3a79da235", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854837919800, "endTime": 27854846363900}, "additional": {"logType": "info", "children": [], "durationId": "bd2deac9-7a28-4765-a885-4fed351e39ef", "parent": "4f6d8ca4-3472-4f62-95db-16831d8efabb"}}, {"head": {"id": "a1658db5-721a-49fa-aad4-e04ecc2df5d0", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854846404700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3e64148-b999-45f8-8afb-aa5dfcd307cf", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854862752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ddcad81-99b8-4bdb-894a-81f92094327c", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854862924100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5aa8e6da-3199-4107-8a94-2a8d2a9dc606", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854863537900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b82834-c9dd-4c80-8d9f-8a79b35f5126", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854863867300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3c534a-5f9a-4af8-98db-3b608102c173", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854863993000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c8aa19-86a1-4d6c-8a8f-b2e82d80f765", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854864100300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663a6d26-4d39-4c8d-807b-ab04b2fd1149", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854864373400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa88e02f-d855-4115-914f-3f3d559dbcb7", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854870256700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d15aa1e8-2c04-4903-8d47-419e2be6ea5e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854870645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75259517-344f-4185-a96e-91bac316a9e0", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854870745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c30a78f7-d087-4370-b5ad-04a97e72416f", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854870860100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0634029-dce6-46f0-8cbd-c8764bee6c05", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854846385700, "endTime": 27854870911700}, "additional": {"logType": "info", "children": [], "durationId": "ef9ed489-1fb3-4e68-9ab1-f716e3b9421d", "parent": "4f6d8ca4-3472-4f62-95db-16831d8efabb"}}, {"head": {"id": "4f6d8ca4-3472-4f62-95db-16831d8efabb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854837917500, "endTime": 27854870930400}, "additional": {"logType": "info", "children": ["c095a9b1-e23d-47fb-88f3-57f3a79da235", "a0634029-dce6-46f0-8cbd-c8764bee6c05"], "durationId": "fbaabd70-ca3d-4b6e-8022-2065f4bf0485", "parent": "acf2b46e-0285-4046-a7a9-452362dacdf1"}}, {"head": {"id": "5016860f-7674-46d3-8a13-16648d435322", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854874975900, "endTime": 27854874999700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "320d6315-c494-4bc9-9d85-b70a4ded6e9f", "logId": "d3d6a96a-f9ee-4e8c-8a7e-b5fdb42b0dd4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3d6a96a-f9ee-4e8c-8a7e-b5fdb42b0dd4", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854874975900, "endTime": 27854874999700}, "additional": {"logType": "info", "children": [], "durationId": "5016860f-7674-46d3-8a13-16648d435322", "parent": "acf2b46e-0285-4046-a7a9-452362dacdf1"}}, {"head": {"id": "acf2b46e-0285-4046-a7a9-452362dacdf1", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854817182700, "endTime": 27854875016600}, "additional": {"logType": "info", "children": ["06ca4d2f-43aa-45da-b5ff-e1757216c812", "4f6d8ca4-3472-4f62-95db-16831d8efabb", "d3d6a96a-f9ee-4e8c-8a7e-b5fdb42b0dd4"], "durationId": "320d6315-c494-4bc9-9d85-b70a4ded6e9f", "parent": "0f6696bf-22c1-4f2b-805f-52a86ef0f6bd"}}, {"head": {"id": "0f6696bf-22c1-4f2b-805f-52a86ef0f6bd", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854816215000, "endTime": 27854875031900}, "additional": {"logType": "info", "children": ["acf2b46e-0285-4046-a7a9-452362dacdf1"], "durationId": "6018c414-e53d-4a8f-b60b-bb75cacf82c1", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "2e4a5267-8118-422b-b053-dc8df480d585", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854983530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1127d5e6-ac59-4cab-9a1d-468483327de9", "name": "hvigorfile, resolve hvigorfile dependencies in 280 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855155079800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7509199c-7735-4885-a59a-7a4e2befe1bf", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854875039800, "endTime": 27855155537300}, "additional": {"logType": "info", "children": [], "durationId": "437a161b-58b9-4efa-b118-bbd70d9fd837", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "0f0e0bea-7881-4f45-8c92-1aeb8a9aead7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855160711400, "endTime": 27855161148000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f75468b7-8e0c-4076-9174-0acd78723b6f", "logId": "382b85f9-d044-467e-b945-37848f9ac412"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a25b426-b8b7-428e-96ee-08f28f712ae5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855160753200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "382b85f9-d044-467e-b945-37848f9ac412", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855160711400, "endTime": 27855161148000}, "additional": {"logType": "info", "children": [], "durationId": "0f0e0bea-7881-4f45-8c92-1aeb8a9aead7", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "b6063b0d-526f-40f0-9d31-aa4bdc755ef2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855162537400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f006524f-bbe8-4888-813d-8f392fd1396c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855174048500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7953c3d-68b1-4227-8716-f879c145e1dc", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855161172600, "endTime": 27855175777700}, "additional": {"logType": "info", "children": [], "durationId": "b9f2945e-b591-4cc2-94e9-13239cd7c13f", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "0ecf5f8c-d24c-4335-b767-bcc169519328", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855182038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21b83fef-ec22-4a58-bd43-2722eaa84073", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855182174700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9751e157-b366-40ca-ae96-d6bd04e78a30", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855184472400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22707a2f-77f9-43ba-a7c9-27a853e45920", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855184618000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a6438c6-e366-410d-9201-893d932440a5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855175807100, "endTime": 27855194467400}, "additional": {"logType": "info", "children": [], "durationId": "a2c193e9-e5bc-4d50-8613-e8410401db54", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "91cd9e11-e0eb-45a1-981d-b5a7121cbe11", "name": "Configuration phase cost:552 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855194528800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99ad3778-ca70-4457-b72a-854017960179", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855194496900, "endTime": 27855194763600}, "additional": {"logType": "info", "children": [], "durationId": "4df7fdd8-862c-4652-a848-0dad6c9a27d4", "parent": "e33f44c3-5860-4ac7-827a-0ecaf3155eba"}}, {"head": {"id": "e33f44c3-5860-4ac7-827a-0ecaf3155eba", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854635644900, "endTime": 27855194821400}, "additional": {"logType": "info", "children": ["0c5e3c71-8ed0-47d1-a50d-e58337dd0621", "c10c1a02-b75d-41eb-8687-320b68a5c1d4", "30977429-5f9b-49c4-b3ce-d676ff62b3e3", "0f6696bf-22c1-4f2b-805f-52a86ef0f6bd", "7509199c-7735-4885-a59a-7a4e2befe1bf", "f7953c3d-68b1-4227-8716-f879c145e1dc", "1a6438c6-e366-410d-9201-893d932440a5", "99ad3778-ca70-4457-b72a-854017960179", "382b85f9-d044-467e-b945-37848f9ac412"], "durationId": "f75468b7-8e0c-4076-9174-0acd78723b6f", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "9cdfab1f-5f18-408a-b368-c0e29d5491f0", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855215957900, "endTime": 27855215980500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "db51e971-d7bd-4803-901e-b0a400e29ae7", "logId": "ee6f8837-b1a9-4fa0-841d-3d3c54f9fe02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee6f8837-b1a9-4fa0-841d-3d3c54f9fe02", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855215957900, "endTime": 27855215980500}, "additional": {"logType": "info", "children": [], "durationId": "9cdfab1f-5f18-408a-b368-c0e29d5491f0", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "c5cb8d0a-ae7a-4013-9cb1-34cb6111ced6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855194855400, "endTime": 27855216022400}, "additional": {"logType": "info", "children": [], "durationId": "f5ee0724-8276-4481-9a7c-276b6355c41b", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "58e4b169-27e4-46b2-8555-8258461c29f0", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855216028100, "endTime": 27855216029900}, "additional": {"logType": "info", "children": [], "durationId": "03e12a4b-dac6-481c-bb1b-47692a5ab678", "parent": "696b39f6-8f85-41d0-9ffe-6a41b9592654"}}, {"head": {"id": "696b39f6-8f85-41d0-9ffe-6a41b9592654", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854618036200, "endTime": 27855216034000}, "additional": {"logType": "info", "children": ["91d9fe10-91aa-4eac-829f-929ae7c70cf5", "e33f44c3-5860-4ac7-827a-0ecaf3155eba", "c5cb8d0a-ae7a-4013-9cb1-34cb6111ced6", "58e4b169-27e4-46b2-8555-8258461c29f0", "87e9a748-0fc6-4a1f-a866-97744d470a9c", "84904639-0ab7-42dd-a224-396c4728cdd3", "ee6f8837-b1a9-4fa0-841d-3d3c54f9fe02"], "durationId": "db51e971-d7bd-4803-901e-b0a400e29ae7"}}, {"head": {"id": "8e565669-eab1-41ac-a4ae-9f69e4f01d8a", "name": "Configuration task cost before running: 602 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855216222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e021c168-197d-4c6c-88bf-6c5f5150d913", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855228638700, "endTime": 27855248444800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "0072bcd7-b1c4-44e2-a314-368abd9b9154", "logId": "9261423e-27bd-4bdd-bf97-9982c8340359"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0072bcd7-b1c4-44e2-a314-368abd9b9154", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855220242300}, "additional": {"logType": "detail", "children": [], "durationId": "e021c168-197d-4c6c-88bf-6c5f5150d913"}}, {"head": {"id": "ed7792da-a8fd-4700-a82e-20789c5dafd7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855220806800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4737f0e-0cab-4390-86c3-1a4ec7b7e2d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855221168500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a527f005-b9b2-4111-9162-008b5821ff80", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855228656900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e99c0d3-dc4a-4045-bec6-b72ca5c56d46", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855244060900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09947d3-474a-4d44-a7f8-8c81d04fe546", "name": "entry : default@PreBuild cost memory 0.31276702880859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855244219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9261423e-27bd-4bdd-bf97-9982c8340359", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855228638700, "endTime": 27855248444800}, "additional": {"logType": "info", "children": [], "durationId": "e021c168-197d-4c6c-88bf-6c5f5150d913"}}, {"head": {"id": "593e0668-9f56-4a60-9df8-da7194a75e47", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855262877600, "endTime": 27855265436500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a580e49d-30d5-4b7b-ae45-c2627bc843db", "logId": "a8816769-fae5-40f8-9e00-d579c0f9241a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a580e49d-30d5-4b7b-ae45-c2627bc843db", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855260744600}, "additional": {"logType": "detail", "children": [], "durationId": "593e0668-9f56-4a60-9df8-da7194a75e47"}}, {"head": {"id": "0e1d6e30-c0fb-4a0d-8c84-08aba15c47a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855261677700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b8dbd3-711b-405d-87f3-451a56499cfc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855261887700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811bd04d-67c9-4faa-8d4b-6fd316c021d8", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855262891600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e95deb1-315f-4ab7-8fd5-bc185c32b978", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855263825400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79475b60-17f6-4708-bb70-f8204e35adc8", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855265228200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac8064c-b233-47d3-9a63-5a5e339ed50d", "name": "entry : default@GenerateMetadata cost memory 0.09598541259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855265362200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8816769-fae5-40f8-9e00-d579c0f9241a", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855262877600, "endTime": 27855265436500}, "additional": {"logType": "info", "children": [], "durationId": "593e0668-9f56-4a60-9df8-da7194a75e47"}}, {"head": {"id": "901532aa-bfc3-4cad-8775-df7bd31fc617", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275398500, "endTime": 27855276226300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0d66f4a9-90ae-4304-8b28-871853f5c8cc", "logId": "541df04b-0933-490f-9bea-82eed4017056"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d66f4a9-90ae-4304-8b28-871853f5c8cc", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855274052500}, "additional": {"logType": "detail", "children": [], "durationId": "901532aa-bfc3-4cad-8775-df7bd31fc617"}}, {"head": {"id": "8eb4b9f0-a064-4dcc-8f8f-8845f5d1e24f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275104600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99479ab9-403c-4001-be53-347f2dbf2c74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275226300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efdf55a3-5677-4af1-a6c7-c75ca506cc0b", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275497900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f859c23-f897-4227-b435-2bc5301679b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90177bde-ede8-41bf-a322-d7d71ea6ad04", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275739600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c8694f7-69ed-42df-889a-65cf7eb2d503", "name": "entry : default@ConfigureCmake cost memory 0.0366058349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275911400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9b7bae-5ab6-489c-89d5-c88aabccb00d", "name": "runTaskFromQueue task cost before running: 662 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855276131500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541df04b-0933-490f-9bea-82eed4017056", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855275398500, "endTime": 27855276226300, "totalTime": 695600}, "additional": {"logType": "info", "children": [], "durationId": "901532aa-bfc3-4cad-8775-df7bd31fc617"}}, {"head": {"id": "c087b536-0ddc-4674-b9cb-a185f634ddf4", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855282558400, "endTime": 27855284890900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1dde9937-6efe-43e3-96ea-726363a1e293", "logId": "46f8a336-b9d8-4166-a7e7-2b1020d329aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dde9937-6efe-43e3-96ea-726363a1e293", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855279568400}, "additional": {"logType": "detail", "children": [], "durationId": "c087b536-0ddc-4674-b9cb-a185f634ddf4"}}, {"head": {"id": "3807d302-2822-406f-bef2-a7fe9a4c6f3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855281209600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5d4d0ce-d8bb-436a-8915-afbddf55797e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855281585900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43a37711-cbe1-44bf-a4ba-33d7c4a4c89f", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855282573400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f00a63-8720-4928-aa44-f0de57d487c7", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855284215300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67564349-050a-4e8e-a042-c7f7bd23fceb", "name": "entry : default@MergeProfile cost memory 0.106964111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855284721900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46f8a336-b9d8-4166-a7e7-2b1020d329aa", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855282558400, "endTime": 27855284890900}, "additional": {"logType": "info", "children": [], "durationId": "c087b536-0ddc-4674-b9cb-a185f634ddf4"}}, {"head": {"id": "90aed194-7997-4cb1-8142-8f2d626a2bb9", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855288469600, "endTime": 27855293374800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5c2fd5c0-a338-4865-b7e3-4661d0a91a06", "logId": "f3af1cb1-0694-47df-a2bf-4c74e229f2a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c2fd5c0-a338-4865-b7e3-4661d0a91a06", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855287138600}, "additional": {"logType": "detail", "children": [], "durationId": "90aed194-7997-4cb1-8142-8f2d626a2bb9"}}, {"head": {"id": "dd772c8e-d28a-4525-94e3-0dafeecd1ddc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855287676400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e752a133-7022-4e9e-bc76-f50805681345", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855287780000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336f4c6b-bda5-4324-a4cc-aad50d5db78f", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855288482800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddc8d17d-f179-45d2-848b-9e0afb1f5fe9", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855291002200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f31428aa-6635-4e98-bbd3-0048b081605a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855292944700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a96e081-4c82-4554-9656-17f9fe3314e5", "name": "entry : default@CreateBuildProfile cost memory 0.105987548828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855293151400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3af1cb1-0694-47df-a2bf-4c74e229f2a9", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855288469600, "endTime": 27855293374800}, "additional": {"logType": "info", "children": [], "durationId": "90aed194-7997-4cb1-8142-8f2d626a2bb9"}}, {"head": {"id": "434c6644-5398-4acb-8671-6399455babe9", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301262700, "endTime": 27855301878000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "87996553-a1eb-4978-aabf-fde8e7f4b362", "logId": "f6daa2a9-aba2-4685-904e-c15843f487b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87996553-a1eb-4978-aabf-fde8e7f4b362", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855295612200}, "additional": {"logType": "detail", "children": [], "durationId": "434c6644-5398-4acb-8671-6399455babe9"}}, {"head": {"id": "70707ef7-a6ea-4546-85d5-c3ee220ab785", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855299670300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f21d0b63-f924-4667-a4a4-79e053bbdd02", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855299803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cf493bc-eb27-46a2-ad5c-fffd0fbdf505", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301284800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00abce02-d686-4aff-b04f-501fce9646d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301439400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8301d320-f7e2-499b-8d2b-1c0c225a6105", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301565600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96c736af-8903-4571-9279-174f59d0b086", "name": "entry : default@PreCheckSyscap cost memory 0.03682708740234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301659000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7eca984-0fe5-42c7-bf1a-b41706ab2d71", "name": "runTaskFromQueue task cost before running: 688 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301739800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6daa2a9-aba2-4685-904e-c15843f487b2", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855301262700, "endTime": 27855301878000, "totalTime": 461300}, "additional": {"logType": "info", "children": [], "durationId": "434c6644-5398-4acb-8671-6399455babe9"}}, {"head": {"id": "ff39611a-8e71-401c-9e4c-1e9e88ad4b9d", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855312471000, "endTime": 27855316696900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "03f5e422-7eb5-4076-8086-cc453a7ac558", "logId": "6cc96f4e-4557-4dfa-b607-4eb90bd1aab1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03f5e422-7eb5-4076-8086-cc453a7ac558", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855304461300}, "additional": {"logType": "detail", "children": [], "durationId": "ff39611a-8e71-401c-9e4c-1e9e88ad4b9d"}}, {"head": {"id": "6dd6dd16-2bd7-4ef1-9c7a-a197446764d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855304922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea1a868-15c7-4f35-8d14-86b02e253aea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855305070400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80b98350-bb36-4acb-ba16-db301cd14fe3", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855312485800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc56ab18-9634-43d2-8f89-8d42de2f4d35", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855312944900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "403cb7d8-2c1a-4dc5-a756-50c8f8e53263", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03917694091796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855316413500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b049f3-f1a2-4919-bf22-27c1c004f54d", "name": "runTaskFromQueue task cost before running: 703 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855316595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cc96f4e-4557-4dfa-b607-4eb90bd1aab1", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855312471000, "endTime": 27855316696900, "totalTime": 4089400}, "additional": {"logType": "info", "children": [], "durationId": "ff39611a-8e71-401c-9e4c-1e9e88ad4b9d"}}, {"head": {"id": "22aaa1b7-4047-4bd2-9d05-b4761d7824a5", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855328835700, "endTime": 27855335225000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "6937c2dd-8c71-4081-933c-5d04e45e5b3b", "logId": "e0372dd0-6777-4488-8834-beb890779ce5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6937c2dd-8c71-4081-933c-5d04e45e5b3b", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855323147100}, "additional": {"logType": "detail", "children": [], "durationId": "22aaa1b7-4047-4bd2-9d05-b4761d7824a5"}}, {"head": {"id": "c391f041-1522-4456-aba1-183f54620551", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855324433800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71c45b62-c7c9-415a-9582-36ecb06ae2f7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855324944100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eac8c037-ab0c-4095-abb2-530fffe67f63", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855328881300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "645b61e9-1e03-4d19-b66e-f077c7014323", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855333422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d612211-c326-42c2-9a65-972578bda093", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855333740600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8343a55-a7b9-4871-b394-2f85e7228938", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855334403400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715fc7a9-af3d-45f6-8908-f0ddaaeb79eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855334922100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2623fb79-74a8-4a56-bffa-4d9e34e37592", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1184234619140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855335070500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8935fdde-7eee-453c-81d8-42480e0a2b6e", "name": "runTaskFromQueue task cost before running: 721 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855335164500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0372dd0-6777-4488-8834-beb890779ce5", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855328835700, "endTime": 27855335225000, "totalTime": 6308400}, "additional": {"logType": "info", "children": [], "durationId": "22aaa1b7-4047-4bd2-9d05-b4761d7824a5"}}, {"head": {"id": "0298ac43-b280-4cf6-9ed6-68be38c73dd1", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855349726200, "endTime": 27855351875600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "a59a5d4d-bd89-4787-ae81-80fcb4756507", "logId": "1d51e301-3b76-4fb6-8d20-160a9e8fae2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a59a5d4d-bd89-4787-ae81-80fcb4756507", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855342141700}, "additional": {"logType": "detail", "children": [], "durationId": "0298ac43-b280-4cf6-9ed6-68be38c73dd1"}}, {"head": {"id": "ec2d9d59-af98-495b-be8f-24ffe27844cc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855345079400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1fb17a4-6e7b-4964-a31d-1a14e04fba18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855345409200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2470432d-b0a1-45d3-950f-f46159e4a1f3", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855349749100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365b773c-7911-44a9-a5ec-fec4c754e907", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855350976200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be59affb-c0dd-4016-8971-591c8f7be983", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855351080400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "123e1b99-3942-4cb8-a48e-4a716c42a73e", "name": "entry : default@BuildNativeWithCmake cost memory 0.03765869140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855351363400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94abe537-a33c-46bf-8a90-575a6c1b5f50", "name": "runTaskFromQueue task cost before running: 738 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855351633800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d51e301-3b76-4fb6-8d20-160a9e8fae2e", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855349726200, "endTime": 27855351875600, "totalTime": 1793200}, "additional": {"logType": "info", "children": [], "durationId": "0298ac43-b280-4cf6-9ed6-68be38c73dd1"}}, {"head": {"id": "900aac67-2222-4799-9b76-0b6df3c88e64", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855356891000, "endTime": 27855361520700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb78ae2c-ab73-4687-984f-825e7e30f529", "logId": "115ba6eb-c664-4212-bbc8-cd612049c9a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb78ae2c-ab73-4687-984f-825e7e30f529", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855354352000}, "additional": {"logType": "detail", "children": [], "durationId": "900aac67-2222-4799-9b76-0b6df3c88e64"}}, {"head": {"id": "12c855ad-5012-430c-83ee-211800ab32a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855354871700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5294f8c4-bfb4-4b25-bacc-0759c6aede0b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855355058200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d74f2cb-b691-463a-b52b-821c2280c082", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855356928200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18b6260-acca-45d1-9c52-4910ba568a55", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855361247100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33afe7ca-ce46-49db-9d2e-8634c3b94dc8", "name": "entry : default@MakePackInfo cost memory 0.13959503173828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855361440800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "115ba6eb-c664-4212-bbc8-cd612049c9a1", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855356891000, "endTime": 27855361520700}, "additional": {"logType": "info", "children": [], "durationId": "900aac67-2222-4799-9b76-0b6df3c88e64"}}, {"head": {"id": "b3c9898f-ea4e-4989-b13a-15f2e214ef25", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855367259100, "endTime": 27855371575200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "448c49c8-01ae-4729-9f6d-7505bddc2ff0", "logId": "f8f4d960-5d95-4b90-a3a0-7407f19e8930"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "448c49c8-01ae-4729-9f6d-7505bddc2ff0", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855364555500}, "additional": {"logType": "detail", "children": [], "durationId": "b3c9898f-ea4e-4989-b13a-15f2e214ef25"}}, {"head": {"id": "630887a0-6947-424d-a748-62baf2739541", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855365173800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6945bc58-00a2-4b34-b0a2-022ad3b91798", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855365467600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fccf94b4-df43-4e4b-8e61-15380ba47a9e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855367294600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "688b0754-cce3-40b0-a12f-e518d0cc74f9", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855367596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b531b17-4835-41e3-8ef8-02793f91cf74", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855368383100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29cd039c-a8f7-40e1-bc46-5e1321712925", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855370861100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd773139-ab56-4199-b5cf-66aed65af0fa", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855371084700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fada6d5-aee4-4ff8-bf14-c5c18c61cac2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855371200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "702436a2-bf85-45ce-8599-00dcdb366a67", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855371252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50559157-c5ac-442c-a697-1ade9c10c11b", "name": "entry : default@SyscapTransform cost memory 0.1543426513671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855371391100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e64adf2d-0f29-46da-977c-698585387834", "name": "runTaskFromQueue task cost before running: 758 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855371492300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f4d960-5d95-4b90-a3a0-7407f19e8930", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855367259100, "endTime": 27855371575200, "totalTime": 4214600}, "additional": {"logType": "info", "children": [], "durationId": "b3c9898f-ea4e-4989-b13a-15f2e214ef25"}}, {"head": {"id": "a1f43db6-0709-49b8-b1c1-3c142d4ab275", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855385249500, "endTime": 27855387011300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f82feaee-e53b-45a3-9e06-46ec0f5e022e", "logId": "49d6ef21-39b3-4019-8c39-295d9e8cbd7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f82feaee-e53b-45a3-9e06-46ec0f5e022e", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855383114000}, "additional": {"logType": "detail", "children": [], "durationId": "a1f43db6-0709-49b8-b1c1-3c142d4ab275"}}, {"head": {"id": "d923f50a-e422-4a5d-ad06-adb50ddb96af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855383589800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8371e20-b5c5-43eb-8d13-e2332fb129e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855383844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e54936-41b8-47c0-bc40-b7f95f052ca4", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855385266000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "277de1f8-35bc-4a5a-9602-55e183773034", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855386692600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4232acf4-c69c-49ee-b43a-549bcb88ad15", "name": "entry : default@ProcessProfile cost memory 0.061279296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855386872700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d6ef21-39b3-4019-8c39-295d9e8cbd7c", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855385249500, "endTime": 27855387011300}, "additional": {"logType": "info", "children": [], "durationId": "a1f43db6-0709-49b8-b1c1-3c142d4ab275"}}, {"head": {"id": "075c4a71-5dce-4d10-acdc-f5656a3c9755", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855396932200, "endTime": 27855403952900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33cf700d-3d11-4d6c-982d-00363e2a15ae", "logId": "40c186c0-d229-479e-b7da-29fcac275c69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33cf700d-3d11-4d6c-982d-00363e2a15ae", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855389672200}, "additional": {"logType": "detail", "children": [], "durationId": "075c4a71-5dce-4d10-acdc-f5656a3c9755"}}, {"head": {"id": "8479fb8e-2d9c-4dc6-8f6c-c91e1f5d189c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855393992900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decf1f9b-366d-4889-926c-f26494e4a168", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855394368300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57b33a6e-a8cd-4a01-9519-e0700fc2ca27", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855396948100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7acbcd-db0e-4bfc-8da8-70e96c2e678b", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855403556900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ea504a-3530-43dc-bd32-d2cdb4277a1a", "name": "entry : default@ProcessRouterMap cost memory 0.20349884033203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855403773700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40c186c0-d229-479e-b7da-29fcac275c69", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855396932200, "endTime": 27855403952900}, "additional": {"logType": "info", "children": [], "durationId": "075c4a71-5dce-4d10-acdc-f5656a3c9755"}}, {"head": {"id": "80af4682-18ab-4722-b065-6dd7333004a1", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855415131800, "endTime": 27855422081300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "71ece1c9-aec3-42db-aa79-f7dd42a42ff5", "logId": "ce077c28-600a-4f85-b545-7c7503473f26"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71ece1c9-aec3-42db-aa79-f7dd42a42ff5", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855412494100}, "additional": {"logType": "detail", "children": [], "durationId": "80af4682-18ab-4722-b065-6dd7333004a1"}}, {"head": {"id": "5cc5004d-14c8-4b28-9594-4f6dbe8a718d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855413396900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcdf8bca-57ea-44b8-b1eb-f5ecb1a220ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855413626000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f1a8e8-e5de-4474-bf63-39a259edf46f", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855415148600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b542def-28c7-4684-af7c-2ea4f519f831", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855415551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a48834-8b9a-4cf8-b6fc-85077bd2d97c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855415849700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6d77a77-c020-47fc-82e3-6f2963b89a72", "name": "entry : default@BuildNativeWithNinja cost memory 0.05718231201171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855420767200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6661efb4-a7be-4210-acb3-6fb5903be83f", "name": "runTaskFromQueue task cost before running: 807 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855421087300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce077c28-600a-4f85-b545-7c7503473f26", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855415131800, "endTime": 27855422081300, "totalTime": 5866400}, "additional": {"logType": "info", "children": [], "durationId": "80af4682-18ab-4722-b065-6dd7333004a1"}}, {"head": {"id": "2525251b-3921-461f-b27f-a790e3ec4cd7", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855442462400, "endTime": 27855469927800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "f5133220-767c-472f-ac63-dd1168a72144", "logId": "14e921d2-3d6c-4b75-bb50-18a382e6bbc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5133220-767c-472f-ac63-dd1168a72144", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855433503100}, "additional": {"logType": "detail", "children": [], "durationId": "2525251b-3921-461f-b27f-a790e3ec4cd7"}}, {"head": {"id": "6df26a17-c993-4079-83db-f4f7bbbf7944", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855435324500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccbfc767-60af-461e-9012-b561849c848e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855435947200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218fb435-2b41-4bc5-9061-a19b2931785e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855438212400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd72dccc-7278-4da9-8dee-5c61f060a00d", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855449029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fba4694-f6e4-4793-9761-25bb755b685a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855454468400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f1d584-405e-4cd8-b99c-59e3545b78a9", "name": "entry : default@ProcessResource cost memory 0.16999053955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855454612200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e921d2-3d6c-4b75-bb50-18a382e6bbc2", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855442462400, "endTime": 27855469927800}, "additional": {"logType": "info", "children": [], "durationId": "2525251b-3921-461f-b27f-a790e3ec4cd7"}}, {"head": {"id": "67572a9c-32be-439d-878a-4a77661f1378", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855499266500, "endTime": 27855531590100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8e44b4bd-ae45-4b6f-b2f6-ce23b09de3d8", "logId": "bc5d36cc-4548-4a53-8a57-b868733da4b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e44b4bd-ae45-4b6f-b2f6-ce23b09de3d8", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855487277100}, "additional": {"logType": "detail", "children": [], "durationId": "67572a9c-32be-439d-878a-4a77661f1378"}}, {"head": {"id": "14732877-e671-41bb-942c-5de09d03999c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855489428700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f574fa5f-1309-43d4-80dd-6e02ebe10f0d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855489605000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733f4c03-4640-4cf4-bf10-d997bfab02d8", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855499301200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08ae774c-2dca-44f3-a7d1-785a25ed0a1f", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855531352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0583d78b-aac8-423c-8aa3-bf26f328c854", "name": "entry : default@GenerateLoaderJson cost memory 0.7648849487304688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855531512900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc5d36cc-4548-4a53-8a57-b868733da4b2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855499266500, "endTime": 27855531590100}, "additional": {"logType": "info", "children": [], "durationId": "67572a9c-32be-439d-878a-4a77661f1378"}}, {"head": {"id": "30c5d916-010b-4c3a-93e1-d0439f66af64", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855555392300, "endTime": 27855566986600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "714e0cbc-9350-424d-baf5-d0fc5fa1b50a", "logId": "4225a612-16c6-49d5-8bd3-93455b5802da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "714e0cbc-9350-424d-baf5-d0fc5fa1b50a", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855552219000}, "additional": {"logType": "detail", "children": [], "durationId": "30c5d916-010b-4c3a-93e1-d0439f66af64"}}, {"head": {"id": "970efd0e-d469-4e8c-aba7-2e267891a25d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855553559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b6f7ca-dc1f-4030-a500-1a050f2d359a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855553791000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb0e2a70-7b5a-46b8-a7cf-a4c2136efab7", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855555416500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98d5cb2-86d4-442a-a994-e42a927edc4e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855565311500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c21e483-8c1b-4fec-9b8d-d32ff2a8d13c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855565610800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c24f5d38-ae3f-4bbd-90f0-b7bd7e38fbb6", "name": "entry : default@ProcessLibs cost memory 0.12601470947265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855566523000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c1c3b4-2c0e-425d-8948-f3c1828d7d9e", "name": "runTaskFromQueue task cost before running: 953 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855566685400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4225a612-16c6-49d5-8bd3-93455b5802da", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855555392300, "endTime": 27855566986600, "totalTime": 11238200}, "additional": {"logType": "info", "children": [], "durationId": "30c5d916-010b-4c3a-93e1-d0439f66af64"}}, {"head": {"id": "83c34e14-25fa-4fa9-a2ce-11a3ae4368f3", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855581540300, "endTime": 27855613736700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "afe90522-5d6b-4ef3-954e-695b16a803ca", "logId": "af0c8d3d-aa66-4277-b10e-846542a760fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afe90522-5d6b-4ef3-954e-695b16a803ca", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855571915700}, "additional": {"logType": "detail", "children": [], "durationId": "83c34e14-25fa-4fa9-a2ce-11a3ae4368f3"}}, {"head": {"id": "455c766b-e7ea-4fd2-a309-d08ba0413cc2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855572591800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7451013c-bf4f-4221-9dc4-470bf39d6f0a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855573803000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b319fc0f-748c-430c-9b00-f4f334c2821d", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855577363900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b443e715-eac3-4f1a-9f10-675405827d5e", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855581571000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "012fd99a-e81e-4c5d-8019-23fb8558ea92", "name": "Incremental task entry:default@CompileResource pre-execution cost: 31 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855613438400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39ee7ddd-7c6a-492c-a1e4-1dab38294d03", "name": "entry : default@CompileResource cost memory 1.4077072143554688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855613577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af0c8d3d-aa66-4277-b10e-846542a760fb", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855581540300, "endTime": 27855613736700}, "additional": {"logType": "info", "children": [], "durationId": "83c34e14-25fa-4fa9-a2ce-11a3ae4368f3"}}, {"head": {"id": "473e42d9-ffad-4fee-abe3-87f16c726cd4", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855620228800, "endTime": 27855624225800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4c6107b5-7c53-498c-bf01-7b0ee1e9a2f5", "logId": "673a4d60-028f-49ef-b358-9b1468a51b98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c6107b5-7c53-498c-bf01-7b0ee1e9a2f5", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855616964600}, "additional": {"logType": "detail", "children": [], "durationId": "473e42d9-ffad-4fee-abe3-87f16c726cd4"}}, {"head": {"id": "ba81d15c-0522-47f2-86ac-84ee47e22e0e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855617351300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9541364-bd41-466a-b295-3424e3a03ffc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855617462900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "869c52a5-810b-4c14-a9e2-645ece1ea5ed", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855620274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c229a980-14d4-46ab-ad62-e401a0dcbad8", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855620967400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d3154f-94d2-44d1-ba3c-c588a4b3bb4c", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855623965400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef3e4a6e-cd7b-408b-a37e-cb6ea7b86de2", "name": "entry : default@DoNativeStrip cost memory 0.07782745361328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855624125100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673a4d60-028f-49ef-b358-9b1468a51b98", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855620228800, "endTime": 27855624225800}, "additional": {"logType": "info", "children": [], "durationId": "473e42d9-ffad-4fee-abe3-87f16c726cd4"}}, {"head": {"id": "965ddc75-9a2a-4d14-a411-70fcc5e92191", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855631767800, "endTime": 27857354516400}, "additional": {"children": ["7a9f8bf5-36e3-4907-b148-69ca03af8e92", "f770207a-dacf-4575-a050-6faeed203b7d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "41be6ac0-0893-45af-a3c1-4b0ea3d56026", "logId": "b6580493-a0a5-4db5-87c2-d481020fab0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41be6ac0-0893-45af-a3c1-4b0ea3d56026", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855627178000}, "additional": {"logType": "detail", "children": [], "durationId": "965ddc75-9a2a-4d14-a411-70fcc5e92191"}}, {"head": {"id": "516e8cbf-c176-4da5-befb-c2f8d3a395e1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855627626100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b219d6e1-4352-44a5-87da-69f5ef31d04d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855627756600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5d51be-c542-4a5f-8a7a-c99bc001b7ad", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855631797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d042c31-92c1-45a0-b36c-f123c50d920d", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855646243900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6875c4f5-798e-477f-8b86-4ff224d6cfac", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855647578000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82306800-edf1-495e-971f-9f53fe5aef0f", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855663386600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d98594-ebaf-4587-ba02-1c10281878c0", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855663975200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb0f68f7-21e7-44f5-8d28-703872b0eb03", "name": "default@CompileArkTS work[36] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855664947500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27855884577000, "endTime": 27857327444500}, "additional": {"children": ["65abd9c8-3c85-4848-89c2-26296470b890", "10044f15-cf4c-4dc1-9194-4adacb85890a", "6cacc906-6c42-4052-8ab8-c35365071bfd", "fbe8bb53-7738-4a33-9ea3-dede9aee115f", "940c9d57-b0fc-4113-bc88-c7de7c26632b", "c09f0eb5-19d7-421f-b50a-ce723eacb8c5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "965ddc75-9a2a-4d14-a411-70fcc5e92191", "logId": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1fa8caa-d643-46b8-9231-868cc6f9282e", "name": "default@CompileArkTS work[36] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855665658600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b41fb0bc-6c09-407b-9afc-a8eea4072d79", "name": "default@CompileArkTS work[36] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855665745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27e98c56-c0bb-4287-8704-45e3727a0e01", "name": "CopyResources startTime: 27855665835900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855665840400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715a0a55-6eea-49ce-b1a3-bedb055fd7fc", "name": "default@CompileArkTS work[37] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855665916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f770207a-dacf-4575-a050-6faeed203b7d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27857342836300, "endTime": 27857353926900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "965ddc75-9a2a-4d14-a411-70fcc5e92191", "logId": "fa4296ed-fbd6-4693-bfe0-3056addc1cc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "678504e5-ab6e-41d5-87c4-da6652905b74", "name": "default@CompileArkTS work[37] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855666657500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8eb92b-fde2-4878-a837-6618bd91cc35", "name": "default@CompileArkTS work[37] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855666778400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2221ace-c788-41e5-985f-509f7fcf2a02", "name": "entry : default@CompileArkTS cost memory -4.8600311279296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855667260900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b3ed11-6834-4ec7-9851-44512caee169", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855675078800, "endTime": 27855679538300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "c569f5ac-3d8f-446e-931f-4501ee399066", "logId": "92ec8ed1-784d-4108-b6d3-1174b4df2852"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c569f5ac-3d8f-446e-931f-4501ee399066", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855669701500}, "additional": {"logType": "detail", "children": [], "durationId": "f4b3ed11-6834-4ec7-9851-44512caee169"}}, {"head": {"id": "c88193b7-5003-4405-a9f2-2f53a88c5caf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855670103800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba8ea55a-903a-4d76-a776-76f7c02635cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855670344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e50a5404-593b-40ea-8704-d630111f02a4", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855675092800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ced1844-19ee-4ba4-aa3f-671545046b54", "name": "entry : default@BuildJS cost memory 0.1280364990234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855679351100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ce7c30-1cee-40cf-a708-91bf72b2d0c3", "name": "runTaskFromQueue task cost before running: 1 s 65 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855679480100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92ec8ed1-784d-4108-b6d3-1174b4df2852", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855675078800, "endTime": 27855679538300, "totalTime": 4381000}, "additional": {"logType": "info", "children": [], "durationId": "f4b3ed11-6834-4ec7-9851-44512caee169"}}, {"head": {"id": "5419ab09-f4b9-4590-899a-7bf4536fe3e1", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855683832300, "endTime": 27855685468700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "58a00a39-429e-47d7-b93d-02bf74634caf", "logId": "e554c9ae-2ea2-47de-a291-df52991efae0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58a00a39-429e-47d7-b93d-02bf74634caf", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855680996300}, "additional": {"logType": "detail", "children": [], "durationId": "5419ab09-f4b9-4590-899a-7bf4536fe3e1"}}, {"head": {"id": "7909c4c9-becf-46b0-afe9-e6e55dcce70a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855681400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f31169-ca65-4f17-b044-56b6d4a04da8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855681494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17eb7e15-9986-4cb4-87d9-633e79da8ef0", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855683843500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e91d075a-c350-4a4d-8402-cc47a3412c28", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855684118400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c0a115-7980-48c1-bd44-ebd1c32d864a", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855685266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d45c444d-35f6-4f23-b0f3-7850b5546f39", "name": "entry : default@CacheNativeLibs cost memory 0.091949462890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855685403500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e554c9ae-2ea2-47de-a291-df52991efae0", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855683832300, "endTime": 27855685468700}, "additional": {"logType": "info", "children": [], "durationId": "5419ab09-f4b9-4590-899a-7bf4536fe3e1"}}, {"head": {"id": "3750a526-c0d2-44b5-93a5-b1a0cd844ada", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855883526600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33c1e695-856a-4e6e-8322-16813a460e40", "name": "default@CompileArkTS work[36] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855883985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bfc2400-f993-47e6-9314-99208be8bfa6", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855884101600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af1ac44-dd44-4aff-8fe6-daba8fb020a0", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855884252000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e7fb5af-6f48-48fc-bab4-099fe03d823b", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855884397600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77069f92-ac42-44d4-8b46-974dfbbd3e03", "name": "default@CompileArkTS work[37] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855885585100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afc1e8b-d7de-4f9c-8f44-096c8cdcfd05", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857328925300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65abd9c8-3c85-4848-89c2-26296470b890", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27855884796200, "endTime": 27855890203800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "logId": "8ac40a8c-e996-452b-8620-8ffec8fd373a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ac40a8c-e996-452b-8620-8ffec8fd373a", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855884796200, "endTime": 27855890203800}, "additional": {"logType": "info", "children": [], "durationId": "65abd9c8-3c85-4848-89c2-26296470b890", "parent": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}}, {"head": {"id": "10044f15-cf4c-4dc1-9194-4adacb85890a", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27855890449900, "endTime": 27855890663000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "logId": "1c97b3a1-d369-4c4c-936d-c96fca942e3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c97b3a1-d369-4c4c-936d-c96fca942e3f", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855890449900, "endTime": 27855890663000}, "additional": {"logType": "info", "children": [], "durationId": "10044f15-cf4c-4dc1-9194-4adacb85890a", "parent": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}}, {"head": {"id": "6cacc906-6c42-4052-8ab8-c35365071bfd", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27855890677800, "endTime": 27855890724900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "logId": "f3bd3e18-5c13-4446-a941-89706341c8b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3bd3e18-5c13-4446-a941-89706341c8b9", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855890677800, "endTime": 27855890724900}, "additional": {"logType": "info", "children": [], "durationId": "6cacc906-6c42-4052-8ab8-c35365071bfd", "parent": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}}, {"head": {"id": "fbe8bb53-7738-4a33-9ea3-dede9aee115f", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27855890744900, "endTime": 27857219610500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "logId": "b552bd22-f92f-453f-856f-dd87605e0125"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b552bd22-f92f-453f-856f-dd87605e0125", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855890744900, "endTime": 27857219610500}, "additional": {"logType": "info", "children": [], "durationId": "fbe8bb53-7738-4a33-9ea3-dede9aee115f", "parent": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}}, {"head": {"id": "940c9d57-b0fc-4113-bc88-c7de7c26632b", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857219848700, "endTime": 27857256905300}, "additional": {"children": ["a46184f6-b16d-41d7-8079-8bc81e27476b", "8d764988-7e79-4fa4-a40a-0e4d1a55706a", "65d64d5a-3fbd-43ea-bff2-f83889464f75"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "logId": "9e9759c5-ac2e-43e0-bc0c-20f7425b0e4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e9759c5-ac2e-43e0-bc0c-20f7425b0e4d", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857219848700, "endTime": 27857256905300}, "additional": {"logType": "info", "children": ["db4bf625-cc3e-4e5a-a0a0-c109bc67f40c", "15d8435c-5bb2-4a60-af98-199169aabf16", "e3e3645f-b260-4675-a9e7-802d513acaf8"], "durationId": "940c9d57-b0fc-4113-bc88-c7de7c26632b", "parent": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}}, {"head": {"id": "a46184f6-b16d-41d7-8079-8bc81e27476b", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857219881700, "endTime": 27857219892900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "940c9d57-b0fc-4113-bc88-c7de7c26632b", "logId": "db4bf625-cc3e-4e5a-a0a0-c109bc67f40c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db4bf625-cc3e-4e5a-a0a0-c109bc67f40c", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857219881700, "endTime": 27857219892900}, "additional": {"logType": "info", "children": [], "durationId": "a46184f6-b16d-41d7-8079-8bc81e27476b", "parent": "9e9759c5-ac2e-43e0-bc0c-20f7425b0e4d"}}, {"head": {"id": "8d764988-7e79-4fa4-a40a-0e4d1a55706a", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857219897200, "endTime": 27857223394000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "940c9d57-b0fc-4113-bc88-c7de7c26632b", "logId": "15d8435c-5bb2-4a60-af98-199169aabf16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15d8435c-5bb2-4a60-af98-199169aabf16", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857219897200, "endTime": 27857223394000}, "additional": {"logType": "info", "children": [], "durationId": "8d764988-7e79-4fa4-a40a-0e4d1a55706a", "parent": "9e9759c5-ac2e-43e0-bc0c-20f7425b0e4d"}}, {"head": {"id": "65d64d5a-3fbd-43ea-bff2-f83889464f75", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857223405900, "endTime": 27857256756100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "940c9d57-b0fc-4113-bc88-c7de7c26632b", "logId": "e3e3645f-b260-4675-a9e7-802d513acaf8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3e3645f-b260-4675-a9e7-802d513acaf8", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857223405900, "endTime": 27857256756100}, "additional": {"logType": "info", "children": [], "durationId": "65d64d5a-3fbd-43ea-bff2-f83889464f75", "parent": "9e9759c5-ac2e-43e0-bc0c-20f7425b0e4d"}}, {"head": {"id": "c09f0eb5-19d7-421f-b50a-ce723eacb8c5", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857256925200, "endTime": 27857327240400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "logId": "5569e5e9-b1e2-40b0-82d1-db0212900250"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5569e5e9-b1e2-40b0-82d1-db0212900250", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857256925200, "endTime": 27857327240400}, "additional": {"logType": "info", "children": [], "durationId": "c09f0eb5-19d7-421f-b50a-ce723eacb8c5", "parent": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024"}}, {"head": {"id": "2b8a5876-c506-4387-b199-62d2ac632297", "name": "default@CompileArkTS work[36] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857334661600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d4fbed7-7c11-466f-81d9-c94ccd1e6024", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27855884577000, "endTime": 27857327444500}, "additional": {"logType": "info", "children": ["8ac40a8c-e996-452b-8620-8ffec8fd373a", "1c97b3a1-d369-4c4c-936d-c96fca942e3f", "f3bd3e18-5c13-4446-a941-89706341c8b9", "b552bd22-f92f-453f-856f-dd87605e0125", "9e9759c5-ac2e-43e0-bc0c-20f7425b0e4d", "5569e5e9-b1e2-40b0-82d1-db0212900250"], "durationId": "7a9f8bf5-36e3-4907-b148-69ca03af8e92", "parent": "b6580493-a0a5-4db5-87c2-d481020fab0a"}}, {"head": {"id": "27910dda-08d9-44fb-a50f-28c2acabe29f", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857354086100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f2d5d6f-6fc1-4fb7-bcf0-3ee026312f5a", "name": "CopyResources is end, endTime: 27857354208600", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857354212300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81cdb222-122c-49cb-a208-b1ef3855acb0", "name": "default@CompileArkTS work[37] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857354307700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa4296ed-fbd6-4693-bfe0-3056addc1cc2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27857342836300, "endTime": 27857353926900}, "additional": {"logType": "info", "children": [], "durationId": "f770207a-dacf-4575-a050-6faeed203b7d", "parent": "b6580493-a0a5-4db5-87c2-d481020fab0a"}}, {"head": {"id": "b6580493-a0a5-4db5-87c2-d481020fab0a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27855631767800, "endTime": 27857354516400, "totalTime": 1489738100}, "additional": {"logType": "info", "children": ["8d4fbed7-7c11-466f-81d9-c94ccd1e6024", "fa4296ed-fbd6-4693-bfe0-3056addc1cc2"], "durationId": "965ddc75-9a2a-4d14-a411-70fcc5e92191"}}, {"head": {"id": "eda7d546-d889-4fdd-ae9c-0d3f5a1a3151", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857361475800, "endTime": 27857362803500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "87b7ba7d-eacf-4b30-a3bc-e8b5f46dac3f", "logId": "d42b7f6b-af11-4bc5-b843-111107d83379"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87b7ba7d-eacf-4b30-a3bc-e8b5f46dac3f", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857359745800}, "additional": {"logType": "detail", "children": [], "durationId": "eda7d546-d889-4fdd-ae9c-0d3f5a1a3151"}}, {"head": {"id": "4b048a01-07b9-45b1-8d3d-5b856db911fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857360248900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f49fa7-b8f9-480a-ad5d-da316ffd91cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857360399200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0871f370-845a-40df-bbe7-e60a9ad53a90", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857361489300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef017e25-f456-414c-aeaf-4f625f695f72", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857361737000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8e28f71-0c6a-4658-bc58-ffdbfe118494", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857362620400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1c8894c-467b-4743-b0ae-3a7657a26e1a", "name": "entry : default@GeneratePkgModuleJson cost memory 0.078704833984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857362728400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d42b7f6b-af11-4bc5-b843-111107d83379", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857361475800, "endTime": 27857362803500}, "additional": {"logType": "info", "children": [], "durationId": "eda7d546-d889-4fdd-ae9c-0d3f5a1a3151"}}, {"head": {"id": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857371265500, "endTime": 27857910854700}, "additional": {"children": ["fdf04d76-ed00-453a-99bb-506b438f19a2", "20324231-45f0-40d7-8c16-21212725b107", "a9e08874-bcb1-4318-8a8f-17f9bdc3ac2c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "4cf9043b-0ee0-4a10-bf28-8f6542066cc3", "logId": "ad63c068-f423-4709-959d-8b9c4be3c5f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cf9043b-0ee0-4a10-bf28-8f6542066cc3", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857364751000}, "additional": {"logType": "detail", "children": [], "durationId": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e"}}, {"head": {"id": "97a422a1-68bd-4811-bdea-1cf711755696", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857365160200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "438d495f-fc5d-4a91-bcc0-5b12485a6bdd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857365259000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d8d9a70-134b-4603-a887-be5f148b171d", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857371293700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "050b2a3d-9057-45e3-a031-d4ac4e8203d8", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857383145000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de833af3-fe19-4686-b9f7-f8b018f8f023", "name": "Incremental task entry:default@PackageHap pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857383280800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad177e2-956b-4b81-a5a4-29d9871a38ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857383368700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7897cd3-d0e0-4f50-844a-205009c33160", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857383417400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf04d76-ed00-453a-99bb-506b438f19a2", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857384156700, "endTime": 27857385330200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e", "logId": "3eb4d62c-b3b6-4d8a-baac-127709582e8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5e5687b-ceb3-42f7-86f2-df2e78988839", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857385158200}, "additional": {"logType": "debug", "children": [], "durationId": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e"}}, {"head": {"id": "3eb4d62c-b3b6-4d8a-baac-127709582e8f", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857384156700, "endTime": 27857385330200}, "additional": {"logType": "info", "children": [], "durationId": "fdf04d76-ed00-453a-99bb-506b438f19a2", "parent": "ad63c068-f423-4709-959d-8b9c4be3c5f3"}}, {"head": {"id": "20324231-45f0-40d7-8c16-21212725b107", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857385890600, "endTime": 27857388030700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e", "logId": "bf2060f1-730d-433e-9739-075898e2e928"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6da5f5e4-e44e-4dbd-9675-985d8ed93e34", "name": "default@PackageHap work[38] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857386964900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e08874-bcb1-4318-8a8f-17f9bdc3ac2c", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857448673400, "endTime": 27857910481300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e", "logId": "5164a425-98d7-420b-8d70-245b71ba1048"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e4e8413-e908-4a8a-8f12-486a25455823", "name": "default@PackageHap work[38] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857387868700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dada6e5-db16-42cd-aa44-280c1214ded6", "name": "default@PackageHap work[38] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857387969600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf2060f1-730d-433e-9739-075898e2e928", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857385890600, "endTime": 27857388030700}, "additional": {"logType": "info", "children": [], "durationId": "20324231-45f0-40d7-8c16-21212725b107", "parent": "ad63c068-f423-4709-959d-8b9c4be3c5f3"}}, {"head": {"id": "67045564-9995-4049-a3c0-68818818bc94", "name": "entry : default@PackageHap cost memory 1.2570877075195312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857392671400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfcfbff7-3f16-46a1-81c1-c4c44f337192", "name": "default@PackageHap work[38] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857448582300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec8256f-c335-4e90-affd-8510c358af70", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857463925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbb4a024-19f8-45dd-abcd-1b551a4cdf21", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857495230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd958998-f2ea-474e-adcd-48801779cf47", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857495422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "880891cf-8b50-48a9-b644-b40532556ae2", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857496152600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f43c414-7361-4565-b6af-b9815188350a", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857496844300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc01ed7d-96c3-4857-bf0a-60b390f5e44e", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857497129900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3c3b51-a421-4606-b563-d7583a4cc1ba", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857497201900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f5e82ac-0bfe-48fa-840a-bdc35b6fb990", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857910569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfee6c7d-300f-4609-b23a-1590f3c32dae", "name": "default@PackageHap work[38] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857910719000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5164a425-98d7-420b-8d70-245b71ba1048", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27857448673400, "endTime": 27857910481300}, "additional": {"logType": "info", "children": [], "durationId": "a9e08874-bcb1-4318-8a8f-17f9bdc3ac2c", "parent": "ad63c068-f423-4709-959d-8b9c4be3c5f3"}}, {"head": {"id": "ad63c068-f423-4709-959d-8b9c4be3c5f3", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857371265500, "endTime": 27857910854700, "totalTime": 483358300}, "additional": {"logType": "info", "children": ["3eb4d62c-b3b6-4d8a-baac-127709582e8f", "bf2060f1-730d-433e-9739-075898e2e928", "5164a425-98d7-420b-8d70-245b71ba1048"], "durationId": "87bf2fb4-cfa3-40a5-9113-f7a1c0f2504e"}}, {"head": {"id": "86b1bee6-9b9e-4fe0-836f-67a021f9e214", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857917039700, "endTime": 27857918562800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "51d2f581-86e1-46e9-92c5-3426838b4dba", "logId": "b78b30a3-771e-4a3e-ad4c-3c89786a80a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51d2f581-86e1-46e9-92c5-3426838b4dba", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857914338500}, "additional": {"logType": "detail", "children": [], "durationId": "86b1bee6-9b9e-4fe0-836f-67a021f9e214"}}, {"head": {"id": "e68d652e-b531-4da6-9f16-34f3f81c06d1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857914694800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e28ec5f3-1a20-4891-b11d-474b8aba1ac8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857914819200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68461573-d52d-4841-8be4-fad5534c570b", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857917052600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8298d6-4893-4f22-abb6-80a0dfb978b7", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857917387400}, "additional": {"logType": "warn", "children": [], "durationId": "86b1bee6-9b9e-4fe0-836f-67a021f9e214"}}, {"head": {"id": "42e94fb3-d713-4177-b328-aa3d517fa5c1", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857917913200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048003c8-b591-45ae-a8ed-cfb9739fbfd0", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857917999500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd0beee2-9c8c-4bfc-8a92-c109c382f9cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857918077400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05aa3eab-c555-4937-a63a-73f47b5ed94d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857918129000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e102e40a-96ea-4a41-aed2-0aa985cd3596", "name": "entry : default@SignHap cost memory 0.11779022216796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857918387100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "289ef971-353b-4990-9c1e-687c550e2f1a", "name": "runTaskFromQueue task cost before running: 3 s 305 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857918499700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b78b30a3-771e-4a3e-ad4c-3c89786a80a2", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857917039700, "endTime": 27857918562800, "totalTime": 1439800}, "additional": {"logType": "info", "children": [], "durationId": "86b1bee6-9b9e-4fe0-836f-67a021f9e214"}}, {"head": {"id": "969c0f4a-416e-4477-9b3a-3e616e1b321f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857922421100, "endTime": 27857933418500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c1df5366-57e6-4624-b00b-e8a0792bc57d", "logId": "b9470fc9-db49-42f5-b7bf-281e90299472"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1df5366-57e6-4624-b00b-e8a0792bc57d", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857920936400}, "additional": {"logType": "detail", "children": [], "durationId": "969c0f4a-416e-4477-9b3a-3e616e1b321f"}}, {"head": {"id": "f5f8c6bb-f7e2-4b98-9bf3-1d7ef5c46656", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857921377300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6b8b033-db24-47c4-8d4b-cc010bec7944", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857921474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8abce33e-5f2e-4d84-8b65-f6b373257fa4", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857922433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39fa783f-e963-474a-9620-3795d5f1a4af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857932675900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4c88656-7924-42d9-9dbe-ec405cb5bd33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857932927800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ce4cb8b-37f3-4ee1-9440-4432a9b94552", "name": "entry : default@CollectDebugSymbol cost memory 0.23937225341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857933194500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa6b394-67b9-4e44-93f3-fa0dfa1cd35b", "name": "runTaskFromQueue task cost before running: 3 s 319 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857933343200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9470fc9-db49-42f5-b7bf-281e90299472", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857922421100, "endTime": 27857933418500, "totalTime": 10865200}, "additional": {"logType": "info", "children": [], "durationId": "969c0f4a-416e-4477-9b3a-3e616e1b321f"}}, {"head": {"id": "260c57ef-a041-43f9-addf-743685c736d6", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857935310100, "endTime": 27857935679600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "ef459b27-f710-45b9-9c3d-abfe87f96b3d", "logId": "6a48d668-e1b5-4418-abb7-141a8b8e30b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef459b27-f710-45b9-9c3d-abfe87f96b3d", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857935244500}, "additional": {"logType": "detail", "children": [], "durationId": "260c57ef-a041-43f9-addf-743685c736d6"}}, {"head": {"id": "707b1be5-e5c3-4638-937d-b27bdb48c08a", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857935319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b7242f6-d535-4ab6-bdc2-1c928bdd4e2d", "name": "entry : assembleHap cost memory 0.0115814208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857935460100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e498a89c-cd42-45f5-a13f-7558f5fbad59", "name": "runTaskFromQueue task cost before running: 3 s 322 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857935547000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a48d668-e1b5-4418-abb7-141a8b8e30b1", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857935310100, "endTime": 27857935679600, "totalTime": 213800}, "additional": {"logType": "info", "children": [], "durationId": "260c57ef-a041-43f9-addf-743685c736d6"}}, {"head": {"id": "17774346-5d6c-4173-8bbd-37e9afbbf45b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857948672100, "endTime": 27857948703800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "235c0a64-855e-4d23-a010-b025d3e00a1f", "logId": "81b4df42-22a0-433a-91de-60475366162e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81b4df42-22a0-433a-91de-60475366162e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857948672100, "endTime": 27857948703800}, "additional": {"logType": "info", "children": [], "durationId": "17774346-5d6c-4173-8bbd-37e9afbbf45b"}}, {"head": {"id": "ee1f6d24-f78b-4e7d-bae3-0cd203c90a70", "name": "BUILD SUCCESSFUL in 3 s 335 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857948754500}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d0f04fcc-5937-4261-8dd7-55d1531ce17e", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27854614463800, "endTime": 27857949101100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 13}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "ebe108ef-fa77-4e1b-a553-d8347b82773f", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857949148300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36854ae8-a8b1-4bdc-a7e0-50be31bbb8f5", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857949372400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ea3f7a-39f5-414f-8814-18f98bdf47e1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857949442300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "541464bf-4191-4e20-8ed5-ef6f62a81914", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857949494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54306bab-69e0-4461-bca2-76165e3aa31a", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857949555600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1748af47-2aed-4d47-9a04-1440bdb08bed", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857950245300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42090846-55e3-479f-ade1-0c3fa11a4504", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857950906900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "189c6aa6-1e5f-4629-a716-3ffed108496d", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857951217100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "589c2e76-04eb-44eb-9999-adbfcb554542", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857951308800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f8281d4-cf81-4e72-95f7-62d8551c29e9", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857951371900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf889d61-598a-4a0e-96ea-ae63fbf30935", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857951636500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b57c861-c1c6-4f43-ae27-16bd90615cb2", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857952706500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfd203a9-1369-4edf-b9c8-47f4bcf69836", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953131400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c585601-e4b4-44b6-bc9f-b8c98d5c9fcf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953214700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e1041af-1c5b-4d1c-a0f1-48b7bbd3c720", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953279100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3938cf86-a5b0-4a21-804f-020b34972b0f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953332200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207c06d5-e626-40ef-ab1f-f483675812f6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953381600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0387eb1b-03e2-4b54-9944-3b94a15d1802", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953672800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d78f76e9-b6e9-44c4-a9f2-f88ec09a80bb", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857953987200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae32d60b-3e90-4a0a-99d1-59de737b9568", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857954261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c1c3d48-9341-4a94-aa79-93ddec77a378", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857954707900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f86bdfb1-c21b-4080-85fb-e6239272fea4", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857954850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24472eb7-70ce-42e7-9687-728dc7c08f20", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857954970400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0315de-f3f0-4f14-9bb7-4e7b3605c096", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857959643500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df502a6a-b12b-4568-a71a-a9ced068c529", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857960636700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a578392-dcbc-43ae-b216-6dc2de35853e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857961515500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfdde97b-a7cf-437a-a2d4-d4d5817cea57", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857961819200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51599283-6b2a-496f-9038-b8365a089f03", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857962175900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f53b2c6-49c3-4bbc-a7af-d842a3069ff0", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857962715400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f75f85-94bc-461f-8d97-1f9527f091ff", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857962794000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a37a8e0-0881-4d81-bb4e-5053d8e1be30", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857963139600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a5f720-7798-4ed5-91d2-561ac8fb25ad", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857963419800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec472f3-4ff2-4f74-9738-8f3431b33243", "name": "Incremental task entry:default@CompileArkTS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857964051700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e7cc5a-8c8f-4963-927c-e21f52035ef6", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857965141300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79b320cd-4b15-45e7-a8dc-4e27e37df6c3", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857965562200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5108822a-f8d0-4c25-a920-ccaa11f4c77c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857966234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b78009ae-8c2d-4705-b915-e5581fd7bc0c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857966427900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1e62585-c31e-490b-9a93-97949812ae59", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857966604800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb0fe7e6-c1bd-42d2-8796-d52dfe2b168b", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857967500000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5566b751-5af9-4523-a72f-c0c8c366fed1", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857967922500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34f05e40-5d2e-4a41-adc7-b3cb0e21a8fd", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857968056600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37cff148-17cf-443f-9ce1-4876d67eab49", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857968134400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413a9d01-3d59-4c23-9f01-dbfc035a674c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857969189400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7f271a-a07b-4b5e-be17-dc9a9b01daa6", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857969568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f936314a-9c4a-486b-93ae-31d1d7f6edfb", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857969927800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02a4c2ea-c4c9-4bb1-bd82-b12d1076c7ee", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857981010600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb656623-9614-4b14-8a60-4c0d87495efd", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857981319500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a853fc8-7964-478b-96da-3a45874a454c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857981530400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bbc9ce6-0a88-45d7-8ea6-8e78b8af87ac", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857981596600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccbd79c4-86aa-411b-8488-a0cac771325a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857981801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "257536e1-57ea-430d-a525-9ec8df9f4a7a", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857982438000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13314e58-d9e0-44a9-905e-6b86d81370a2", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857982653200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3b74e9-7dc5-4b92-92e6-d80c2c93a56e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857982984500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82d19044-88a1-45de-bb7a-fffffcedd932", "name": "Incremental task entry:default@PackageHap post-execution cost:16 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857983352100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c364645-8221-41b7-b54f-4decd0858333", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857983621000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be932bb0-8656-4b9d-8672-9d34709acf13", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857983708300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "157e84d0-890e-4053-bf19-cec66d1e51a2", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857984049100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7641d381-f1ff-4123-ac3f-8c470efaa170", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857986434300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f731dd9f-8f8b-4e07-8ba6-20a8ec2b6baa", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857986818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6583dd2-f89b-49a1-9da6-f883f57b217c", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857987094900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7f7d57-0b55-4fd6-8734-4d9122cc5233", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27857987329100}, "additional": {"logType": "debug", "children": []}}], "workLog": []}