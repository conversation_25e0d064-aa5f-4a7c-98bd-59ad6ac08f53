[2025-06-19T17:11:35.914] [DEBUG] debug-file - default@PackageHap work[23] is submitted.
[2025-06-19T17:11:35.915] [DEBUG] debug-file - default@PackageHap work[23] is pushed to ready queue.
[2025-06-19T17:11:35.916] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-06-19T17:11:35.916] [DEBUG] debug-file - default@PackageHap work[23] has been dispatched to worker[4].
[2025-06-19T17:11:35.916] [DEBUG] debug-file - default@PackageHap work[23] is dispatched.
[2025-06-19T17:11:35.921] [DEBUG] debug-file - entry : default@PackageHap cost memory 1.175384521484375
[2025-06-19T17:11:36.343] [DEBUG] debug-file - worker[4] has one work done.
[2025-06-19T17:11:36.344] [DEBUG] debug-file - default@PackageHap work[23] done.
[2025-06-19T17:11:36.344] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-06-19T17:11:36.345] [INFO] debug-file - Finished :entry:default@PackageHap... after 443 ms 
[2025-06-19T17:11:36.348] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-19T17:11:36.348] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-19T17:11:36.350] [DEBUG] debug-file - Executing task :entry:default@SignHap
[2025-06-19T17:11:36.350] [WARN] debug-file - Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5.
[2025-06-19T17:11:36.351] [DEBUG] debug-file - entry:default@SignHap is not up-to-date, since the input file 'D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap' has been changed.
[2025-06-19T17:11:36.351] [DEBUG] debug-file - Incremental task entry:default@SignHap pre-execution cost: 1 ms .
[2025-06-19T17:11:36.351] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-19T17:11:36.351] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-19T17:11:36.351] [DEBUG] debug-file - entry : default@SignHap cost memory 0.12148284912109375
[2025-06-19T17:11:36.351] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 579 ms 
[2025-06-19T17:11:36.352] [INFO] debug-file - Finished :entry:default@SignHap... after 2 ms 
[2025-06-19T17:11:36.353] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-19T17:11:36.353] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-19T17:11:36.354] [DEBUG] debug-file - Executing task :entry:default@CollectDebugSymbol
[2025-06-19T17:11:36.358] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-19T17:11:36.358] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-19T17:11:36.358] [DEBUG] debug-file - entry : default@CollectDebugSymbol cost memory 0.24501800537109375
[2025-06-19T17:11:36.358] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 586 ms 
[2025-06-19T17:11:36.358] [INFO] debug-file - Finished :entry:default@CollectDebugSymbol... after 4 ms 
[2025-06-19T17:11:36.360] [DEBUG] debug-file - Executing task :entry:assembleHap
[2025-06-19T17:11:36.360] [DEBUG] debug-file - entry : assembleHap cost memory 0.0117034912109375
[2025-06-19T17:11:36.360] [DEBUG] debug-file - runTaskFromQueue task cost before running: 2 s 588 ms 
[2025-06-19T17:11:36.360] [INFO] debug-file - Finished :entry:assembleHap... after 1 ms 
[2025-06-19T17:11:36.367] [DEBUG] debug-file - BUILD SUCCESSFUL in 2 s 595 ms 
[2025-06-19T17:11:36.367] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.
[2025-06-19T17:11:36.367] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.
[2025-06-19T17:11:36.367] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.
[2025-06-19T17:11:36.367] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.
[2025-06-19T17:11:36.367] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-19T17:11:36.368] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-19T17:11:36.368] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build\cache\default\integrated_hsp\integratedHspCache.json cache.
[2025-06-19T17:11:36.368] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-06-19T17:11:36.369] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.
[2025-06-19T17:11:36.369] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\syscap_tool.exe cache by regenerate.
[2025-06-19T17:11:36.369] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:C:\Program Files\Huawei\DevEco Studio\sdk\default\openharmony\ets\api\device-define cache by regenerate.
[2025-06-19T17:11:36.370] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\syscap\default\rpcid.sc cache.
[2025-06-19T17:11:36.370] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:2 ms .
[2025-06-19T17:11:36.370] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.
[2025-06-19T17:11:36.370] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.
[2025-06-19T17:11:36.370] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2025-06-19T17:11:36.370] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.
[2025-06-19T17:11:36.370] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\build-profile.json5 cache by regenerate.
[2025-06-19T17:11:36.371] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build-profile.json5 cache by regenerate.
[2025-06-19T17:11:36.371] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache.
[2025-06-19T17:11:36.371] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:1 ms .
[2025-06-19T17:11:36.371] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.
[2025-06-19T17:11:36.371] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2025-06-19T17:11:36.373] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-19T17:11:36.373] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-19T17:11:36.374] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-19T17:11:36.374] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-19T17:11:36.374] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-19T17:11:36.375] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\main\ets cache from map.
[2025-06-19T17:11:36.375] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-19T17:11:36.375] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-19T17:11:36.375] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache.
[2025-06-19T17:11:36.376] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:5 ms .
[2025-06-19T17:11:36.377] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader\default cache by regenerate.
[2025-06-19T17:11:36.378] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\rawfile cache by regenerate.
[2025-06-19T17:11:36.378] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-19T17:11:36.379] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\ark_module.json cache by regenerate.
[2025-06-19T17:11:36.379] [DEBUG] debug-file - Update task entry:default@BuildJS input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-19T17:11:36.380] [DEBUG] debug-file - Update task entry:default@BuildJS output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\js cache.
[2025-06-19T17:11:36.380] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:4 ms .
[2025-06-19T17:11:36.380] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2025-06-19T17:11:36.380] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.
[2025-06-19T17:11:36.382] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\stripped_native_libs\default cache by regenerate.
[2025-06-19T17:11:36.382] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\module.json cache by regenerate.
[2025-06-19T17:11:36.382] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources cache by regenerate.
[2025-06-19T17:11:36.387] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\res\default\resources.index cache by regenerate.
[2025-06-19T17:11:36.387] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\pack.info cache by regenerate.
[2025-06-19T17:11:36.387] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets cache from map.
[2025-06-19T17:11:36.387] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-19T17:11:36.387] [DEBUG] debug-file - Update task entry:default@PackageHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-19T17:11:36.388] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache.
[2025-06-19T17:11:36.388] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\source_map\default\sourceMaps.map cache.
[2025-06-19T17:11:36.388] [DEBUG] debug-file - Update task entry:default@PackageHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\mapping\sourceMaps.map cache.
[2025-06-19T17:11:36.389] [DEBUG] debug-file - Incremental task entry:default@PackageHap post-execution cost:9 ms .
[2025-06-19T17:11:36.389] [DEBUG] debug-file - Update task entry:default@SignHap input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-unsigned.hap cache from map.
[2025-06-19T17:11:36.389] [DEBUG] debug-file - Update task entry:default@SignHap output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\entry-default-signed.hap cache.
[2025-06-19T17:11:36.389] [DEBUG] debug-file - Incremental task entry:default@SignHap post-execution cost:1 ms .
[2025-06-19T17:11:36.391] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\loader_out\default\ets\sourceMaps.map cache by regenerate.
[2025-06-19T17:11:36.392] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol input file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\intermediates\libs\default cache by regenerate.
[2025-06-19T17:11:36.392] [DEBUG] debug-file - Update task entry:default@CollectDebugSymbol output file:D:\yysrc\sourcecode\mrhp-sharedoc\2025\SheShudu\entry\build\default\outputs\default\symbol cache.
[2025-06-19T17:11:36.392] [DEBUG] debug-file - Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-06-19T17:11:36.412] [DEBUG] debug-file - Cleanup worker 3.
[2025-06-19T17:11:36.413] [DEBUG] debug-file - Worker 3 has been cleaned up.
[2025-06-19T17:11:36.413] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-19T17:11:36.413] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-19T17:11:36.414] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-19T17:11:36.421] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-19T17:11:36.422] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-19T17:11:36.422] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-19T17:11:36.422] [DEBUG] debug-file - worker[3] exits with exit code 1.
[2025-06-19T17:11:36.422] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
