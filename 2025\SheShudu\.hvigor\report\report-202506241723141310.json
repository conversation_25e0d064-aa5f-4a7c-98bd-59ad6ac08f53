{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "f264b1ca-c005-4d97-80db-51ecc447b8e8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999477915800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39476370-7d6a-482a-9031-79ed01a7bbeb", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999482271100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fa785c-6ecd-4fa1-ab80-f13822996746", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999482580100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c50b684-c0a7-416e-8dfd-709a51898195", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31999486075600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "156b9a99-b0b8-411b-8936-c6daa6332a61", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026023942700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ce61d7-3dca-4724-9dbc-daec95046b08", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026030919500, "endTime": 32026544185300}, "additional": {"children": ["d953be11-e947-4b15-b94a-a32b8bf221b6", "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "217f0d9e-7465-4d52-9306-fb78605516ba", "4dd1a4d6-ff3a-4a7c-ac4d-0a93a90ffc14", "92ec44c2-d9b3-470f-bf23-a09da3f6bbd1", "5837c5de-2fac-48d9-8aef-c83daa5e5ecc", "d0475e26-ce9b-4408-88ec-a1590f899749"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "72e5291d-937e-47bb-9708-929378d28ea8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d953be11-e947-4b15-b94a-a32b8bf221b6", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026030921700, "endTime": 32026046675200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "a22a16cd-8624-4ca5-b8a7-6c0b9d2074cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026046703200, "endTime": 32026542531400}, "additional": {"children": ["2f5a57d2-14bb-40fb-825f-aeafd27765d5", "188d0b2a-57f7-4766-96f7-cbdcf06bae75", "916fb504-da7e-42bd-8620-034522810f8b", "79b5fd7b-64df-4a45-b189-daf63ef3a7d6", "259bacd5-215a-4c79-ac3a-e07a35dd2818", "8f3ba087-e51f-4b6c-8eb5-b9a4044d3b5b", "a0323b5f-fd00-4a96-bbb1-1547b8af7f24", "9bd8e512-adf3-41da-aadc-4b4af137f763", "a6cd2e04-3a59-494c-b069-d74f58bfacdf"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "217f0d9e-7465-4d52-9306-fb78605516ba", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026542551700, "endTime": 32026544176200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "05e23e6b-293f-4c55-b5f7-ed0fff07a3d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dd1a4d6-ff3a-4a7c-ac4d-0a93a90ffc14", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026544181900, "endTime": 32026544182900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "3dfb5447-4edc-40cd-a223-8162dde46bc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92ec44c2-d9b3-470f-bf23-a09da3f6bbd1", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026033761800, "endTime": 32026033807700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "32ba43a9-df8a-46a1-8a56-6bd40c5772f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32ba43a9-df8a-46a1-8a56-6bd40c5772f9", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026033761800, "endTime": 32026033807700}, "additional": {"logType": "info", "children": [], "durationId": "92ec44c2-d9b3-470f-bf23-a09da3f6bbd1", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "5837c5de-2fac-48d9-8aef-c83daa5e5ecc", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026041014900, "endTime": 32026041039000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "b5a5c61d-fae9-4c4c-965a-159e03d4ceb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5a5c61d-fae9-4c4c-965a-159e03d4ceb3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026041014900, "endTime": 32026041039000}, "additional": {"logType": "info", "children": [], "durationId": "5837c5de-2fac-48d9-8aef-c83daa5e5ecc", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "e18b109d-2acb-4c5c-8634-cb90f36798ea", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026041093800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d298d6-454e-424a-bbc4-9d37d7c14920", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026046474400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a22a16cd-8624-4ca5-b8a7-6c0b9d2074cf", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026030921700, "endTime": 32026046675200}, "additional": {"logType": "info", "children": [], "durationId": "d953be11-e947-4b15-b94a-a32b8bf221b6", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "2f5a57d2-14bb-40fb-825f-aeafd27765d5", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026051241300, "endTime": 32026051248300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "63ed8dd3-df6e-428f-8c37-06eea3e00265"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "188d0b2a-57f7-4766-96f7-cbdcf06bae75", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026051267100, "endTime": 32026054656400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "1c0f7f10-a9b2-4484-b1fc-9bba01d966e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "916fb504-da7e-42bd-8620-034522810f8b", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026054676100, "endTime": 32026241381300}, "additional": {"children": ["2c999f50-5fa6-4c83-b90f-9adcf3915de1", "6f3e97e3-3a14-44c0-99b7-4c21eb8749bd", "96beb46d-7d34-4a09-9fcf-277a816f3da8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "93264be0-7a0d-44d9-8ce1-299258162da1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79b5fd7b-64df-4a45-b189-daf63ef3a7d6", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026241406200, "endTime": 32026293586400}, "additional": {"children": ["a01530c8-4b49-42ac-a48e-a2a0da52bb47"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "f018c44f-c926-4edf-a721-2027b0b47265"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "259bacd5-215a-4c79-ac3a-e07a35dd2818", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026293598900, "endTime": 32026437291800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "fec30402-44f4-4579-ad6e-8de412a7e0a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f3ba087-e51f-4b6c-8eb5-b9a4044d3b5b", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026438156700, "endTime": 32026488086100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "304f20e1-f308-4033-8aaf-0e9c61998d12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0323b5f-fd00-4a96-bbb1-1547b8af7f24", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026492568200, "endTime": 32026542324800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "9186126c-bd29-492f-bc29-dae17b6211cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bd8e512-adf3-41da-aadc-4b4af137f763", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026542350900, "endTime": 32026542519600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "992c39e6-52bd-4ed5-acec-79a819c54500"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "63ed8dd3-df6e-428f-8c37-06eea3e00265", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026051241300, "endTime": 32026051248300}, "additional": {"logType": "info", "children": [], "durationId": "2f5a57d2-14bb-40fb-825f-aeafd27765d5", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "1c0f7f10-a9b2-4484-b1fc-9bba01d966e8", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026051267100, "endTime": 32026054656400}, "additional": {"logType": "info", "children": [], "durationId": "188d0b2a-57f7-4766-96f7-cbdcf06bae75", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "2c999f50-5fa6-4c83-b90f-9adcf3915de1", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026055209200, "endTime": 32026055228000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "916fb504-da7e-42bd-8620-034522810f8b", "logId": "03672023-5ab1-469a-b4cb-534627d82178"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03672023-5ab1-469a-b4cb-534627d82178", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026055209200, "endTime": 32026055228000}, "additional": {"logType": "info", "children": [], "durationId": "2c999f50-5fa6-4c83-b90f-9adcf3915de1", "parent": "93264be0-7a0d-44d9-8ce1-299258162da1"}}, {"head": {"id": "6f3e97e3-3a14-44c0-99b7-4c21eb8749bd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026057292100, "endTime": 32026240064800}, "additional": {"children": ["a7f18bd1-4a60-472a-92c7-ee56c8d3bc5b", "cef7a606-2960-44eb-8a87-ccfe163c84ae"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "916fb504-da7e-42bd-8620-034522810f8b", "logId": "d338e66f-e03e-41fe-bb28-c4e61702387e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7f18bd1-4a60-472a-92c7-ee56c8d3bc5b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026057293800, "endTime": 32026063683600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f3e97e3-3a14-44c0-99b7-4c21eb8749bd", "logId": "404c15dc-c24d-40d7-94a0-c5061ff3d284"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cef7a606-2960-44eb-8a87-ccfe163c84ae", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026063704900, "endTime": 32026240048600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f3e97e3-3a14-44c0-99b7-4c21eb8749bd", "logId": "94d7305f-b449-4a2f-adfe-3d19ffbf80e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ae6bba4-d84a-47cb-9990-45d8248a2abd", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026057299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "661fb37c-375a-4b6b-871f-af0b260cc9ed", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026063537100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "404c15dc-c24d-40d7-94a0-c5061ff3d284", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026057293800, "endTime": 32026063683600}, "additional": {"logType": "info", "children": [], "durationId": "a7f18bd1-4a60-472a-92c7-ee56c8d3bc5b", "parent": "d338e66f-e03e-41fe-bb28-c4e61702387e"}}, {"head": {"id": "683d4dff-8df3-492f-b4ad-de0c534b9e67", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026063714400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f8dd5eb-6cbf-4fe4-837f-18e798e99389", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026073911500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e7611e-d9bb-45fe-af6e-230edb98cade", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026074180700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6120a2df-6aa6-4053-ad84-b8bf7c46bf40", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026074669400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a86e41-27ef-4034-813c-54013276c597", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026075455600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3fc7558-feaf-430b-9620-c2f3e0bbd7ac", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026078320100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa72ccad-d768-46fc-8348-29dcad2857cd", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026084463700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1196ffd-e836-4a52-a62f-9040319f2025", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026104158900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e46a95e4-2820-452b-893a-47048a0868a9", "name": "Sdk init in 69 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026155412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61ec09e-d7a4-4381-bc7f-411a15364231", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026155658500}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 23}, "markType": "other"}}, {"head": {"id": "5efa6bc9-81d2-4ea6-ad38-2d4305041841", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026155683700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 23}, "markType": "other"}}, {"head": {"id": "02fb2a5c-1561-41e8-8387-9463ac723a2c", "name": "Project task initialization takes 79 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026237969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cb1827f-a768-4bbe-a9e4-951c073c9815", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026238641400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1f5d65-d54c-4fbb-a178-f77327777fb8", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026239718700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e548c3b-d2d3-4e18-a06c-e669c69788ba", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026239984400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94d7305f-b449-4a2f-adfe-3d19ffbf80e2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026063704900, "endTime": 32026240048600}, "additional": {"logType": "info", "children": [], "durationId": "cef7a606-2960-44eb-8a87-ccfe163c84ae", "parent": "d338e66f-e03e-41fe-bb28-c4e61702387e"}}, {"head": {"id": "d338e66f-e03e-41fe-bb28-c4e61702387e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026057292100, "endTime": 32026240064800}, "additional": {"logType": "info", "children": ["404c15dc-c24d-40d7-94a0-c5061ff3d284", "94d7305f-b449-4a2f-adfe-3d19ffbf80e2"], "durationId": "6f3e97e3-3a14-44c0-99b7-4c21eb8749bd", "parent": "93264be0-7a0d-44d9-8ce1-299258162da1"}}, {"head": {"id": "96beb46d-7d34-4a09-9fcf-277a816f3da8", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026241326900, "endTime": 32026241345300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "916fb504-da7e-42bd-8620-034522810f8b", "logId": "c56fc111-7f34-4c26-91cb-0efcddbd5537"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c56fc111-7f34-4c26-91cb-0efcddbd5537", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026241326900, "endTime": 32026241345300}, "additional": {"logType": "info", "children": [], "durationId": "96beb46d-7d34-4a09-9fcf-277a816f3da8", "parent": "93264be0-7a0d-44d9-8ce1-299258162da1"}}, {"head": {"id": "93264be0-7a0d-44d9-8ce1-299258162da1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026054676100, "endTime": 32026241381300}, "additional": {"logType": "info", "children": ["03672023-5ab1-469a-b4cb-534627d82178", "d338e66f-e03e-41fe-bb28-c4e61702387e", "c56fc111-7f34-4c26-91cb-0efcddbd5537"], "durationId": "916fb504-da7e-42bd-8620-034522810f8b", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "a01530c8-4b49-42ac-a48e-a2a0da52bb47", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026242806500, "endTime": 32026293574300}, "additional": {"children": ["cf1b9ef2-b082-4651-b506-ed8c4d328137", "e39f97ad-43a8-4e62-aae7-86bdd9d266b6", "ee510577-cb5e-45c1-bc43-64d1fd472093"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "79b5fd7b-64df-4a45-b189-daf63ef3a7d6", "logId": "0a2906ad-b879-4e16-8974-e17caaa14033"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf1b9ef2-b082-4651-b506-ed8c4d328137", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026265794300, "endTime": 32026265853300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a01530c8-4b49-42ac-a48e-a2a0da52bb47", "logId": "bc8238ae-7cb5-41ac-855c-a90b55f766f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc8238ae-7cb5-41ac-855c-a90b55f766f4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026265794300, "endTime": 32026265853300}, "additional": {"logType": "info", "children": [], "durationId": "cf1b9ef2-b082-4651-b506-ed8c4d328137", "parent": "0a2906ad-b879-4e16-8974-e17caaa14033"}}, {"head": {"id": "e39f97ad-43a8-4e62-aae7-86bdd9d266b6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026270989900, "endTime": 32026291470800}, "additional": {"children": ["35c05fa7-395b-4d6d-85ef-f2005219dd01", "e33b524e-358c-46ba-92e6-29304ad7c838"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a01530c8-4b49-42ac-a48e-a2a0da52bb47", "logId": "074f1097-8677-4137-90f0-97f04a06d28c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35c05fa7-395b-4d6d-85ef-f2005219dd01", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026270992100, "endTime": 32026278310300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e39f97ad-43a8-4e62-aae7-86bdd9d266b6", "logId": "a98ba8aa-3bcc-491d-a983-a2f38d7fe725"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e33b524e-358c-46ba-92e6-29304ad7c838", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026278333600, "endTime": 32026291455500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e39f97ad-43a8-4e62-aae7-86bdd9d266b6", "logId": "0d82f34b-cb26-4563-9bbb-d927b1b06b1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3686738c-a35b-4274-9d59-b87bbba31dd9", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026270997900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36f6a73-f711-4c8c-9e2f-6a3c93adbe5a", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026278135700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a98ba8aa-3bcc-491d-a983-a2f38d7fe725", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026270992100, "endTime": 32026278310300}, "additional": {"logType": "info", "children": [], "durationId": "35c05fa7-395b-4d6d-85ef-f2005219dd01", "parent": "074f1097-8677-4137-90f0-97f04a06d28c"}}, {"head": {"id": "2ba33038-2280-4a09-9176-3fcd151f3518", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026278387900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e30eac-5a64-43c5-89d1-0e9c1fbfcc05", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026285788700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7374c98-ea3b-4ff1-8aac-e3424c5751b4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026285952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a782ab-ebdf-46a9-9249-be48fb6c231e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026286216600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c05df99b-e887-449e-b6d0-dbd8c9b772a2", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026286499000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f67603d-08da-4852-9cc6-20d6658b901d", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026286577300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92fbe00-0a85-4c8e-8f67-6d9ab292f32a", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026286663900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2337fec-9401-4b26-a7d8-4f526d2352f0", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026286788900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d198b351-ff6a-4651-96d5-f7771615cff7", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026291098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f13548c-5409-45d2-87ee-d5217293156f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026291283200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f911a5f-5c4a-4dbc-a4e9-89b5e6b4161d", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026291346400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6908e733-b0ac-4ddc-ab7f-f6591b8685ea", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026291395300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d82f34b-cb26-4563-9bbb-d927b1b06b1d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026278333600, "endTime": 32026291455500}, "additional": {"logType": "info", "children": [], "durationId": "e33b524e-358c-46ba-92e6-29304ad7c838", "parent": "074f1097-8677-4137-90f0-97f04a06d28c"}}, {"head": {"id": "074f1097-8677-4137-90f0-97f04a06d28c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026270989900, "endTime": 32026291470800}, "additional": {"logType": "info", "children": ["a98ba8aa-3bcc-491d-a983-a2f38d7fe725", "0d82f34b-cb26-4563-9bbb-d927b1b06b1d"], "durationId": "e39f97ad-43a8-4e62-aae7-86bdd9d266b6", "parent": "0a2906ad-b879-4e16-8974-e17caaa14033"}}, {"head": {"id": "ee510577-cb5e-45c1-bc43-64d1fd472093", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026293537800, "endTime": 32026293558600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a01530c8-4b49-42ac-a48e-a2a0da52bb47", "logId": "54ed75f0-5fd0-4122-9715-42ebc4d28988"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54ed75f0-5fd0-4122-9715-42ebc4d28988", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026293537800, "endTime": 32026293558600}, "additional": {"logType": "info", "children": [], "durationId": "ee510577-cb5e-45c1-bc43-64d1fd472093", "parent": "0a2906ad-b879-4e16-8974-e17caaa14033"}}, {"head": {"id": "0a2906ad-b879-4e16-8974-e17caaa14033", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026242806500, "endTime": 32026293574300}, "additional": {"logType": "info", "children": ["bc8238ae-7cb5-41ac-855c-a90b55f766f4", "074f1097-8677-4137-90f0-97f04a06d28c", "54ed75f0-5fd0-4122-9715-42ebc4d28988"], "durationId": "a01530c8-4b49-42ac-a48e-a2a0da52bb47", "parent": "f018c44f-c926-4edf-a721-2027b0b47265"}}, {"head": {"id": "f018c44f-c926-4edf-a721-2027b0b47265", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026241406200, "endTime": 32026293586400}, "additional": {"logType": "info", "children": ["0a2906ad-b879-4e16-8974-e17caaa14033"], "durationId": "79b5fd7b-64df-4a45-b189-daf63ef3a7d6", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "5e248275-37e4-43ee-8f8a-7ca9cc2ba241", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026341106500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b06b818-9efa-4b2a-ba7e-25da8159d51c", "name": "hvigorfile, resolve hvigorfile dependencies in 144 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026437162600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec30402-44f4-4579-ad6e-8de412a7e0a0", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026293598900, "endTime": 32026437291800}, "additional": {"logType": "info", "children": [], "durationId": "259bacd5-215a-4c79-ac3a-e07a35dd2818", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "a6cd2e04-3a59-494c-b069-d74f58bfacdf", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026437952000, "endTime": 32026438145500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "logId": "c1f5bb76-337e-4d7b-a4ec-b8c1fac2cda8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3371a821-5a62-4ce9-ad26-1b48cbb179f3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026437984600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f5bb76-337e-4d7b-a4ec-b8c1fac2cda8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026437952000, "endTime": 32026438145500}, "additional": {"logType": "info", "children": [], "durationId": "a6cd2e04-3a59-494c-b069-d74f58bfacdf", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "5c106947-33af-47b5-b156-fa3a6668f6d4", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026439854100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c5bc37a-f151-4e75-8a8b-b8f6c2fdbbd7", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026486876400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304f20e1-f308-4033-8aaf-0e9c61998d12", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026438156700, "endTime": 32026488086100}, "additional": {"logType": "info", "children": [], "durationId": "8f3ba087-e51f-4b6c-8eb5-b9a4044d3b5b", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "518ef2a9-7a97-4f07-9e9a-8a8a41fd8a8f", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026518575900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3093201-b430-49c6-8d0a-d2d05d0f64d5", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026518749900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34c4cb4-49c2-4e58-84d9-c2b9e365f317", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026530654800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74dcb2c1-449d-441e-83ee-b760272adc53", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026530983100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9186126c-bd29-492f-bc29-dae17b6211cb", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026492568200, "endTime": 32026542324800}, "additional": {"logType": "info", "children": [], "durationId": "a0323b5f-fd00-4a96-bbb1-1547b8af7f24", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "fb47767b-adbc-4b72-be95-d4e9258269fb", "name": "Configuration phase cost:492 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026542375300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "992c39e6-52bd-4ed5-acec-79a819c54500", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026542350900, "endTime": 32026542519600}, "additional": {"logType": "info", "children": [], "durationId": "9bd8e512-adf3-41da-aadc-4b4af137f763", "parent": "9cf3221f-4cc2-4e13-8c94-03de40b163f6"}}, {"head": {"id": "9cf3221f-4cc2-4e13-8c94-03de40b163f6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026046703200, "endTime": 32026542531400}, "additional": {"logType": "info", "children": ["63ed8dd3-df6e-428f-8c37-06eea3e00265", "1c0f7f10-a9b2-4484-b1fc-9bba01d966e8", "93264be0-7a0d-44d9-8ce1-299258162da1", "f018c44f-c926-4edf-a721-2027b0b47265", "fec30402-44f4-4579-ad6e-8de412a7e0a0", "304f20e1-f308-4033-8aaf-0e9c61998d12", "9186126c-bd29-492f-bc29-dae17b6211cb", "992c39e6-52bd-4ed5-acec-79a819c54500", "c1f5bb76-337e-4d7b-a4ec-b8c1fac2cda8"], "durationId": "f4a6e345-0aa0-4389-bd9f-f3dddb43a0db", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "d0475e26-ce9b-4408-88ec-a1590f899749", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026544146500, "endTime": 32026544164400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "78ce61d7-3dca-4724-9dbc-daec95046b08", "logId": "f25b40f0-fdcb-4b85-a83a-f41229fe734b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f25b40f0-fdcb-4b85-a83a-f41229fe734b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026544146500, "endTime": 32026544164400}, "additional": {"logType": "info", "children": [], "durationId": "d0475e26-ce9b-4408-88ec-a1590f899749", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "05e23e6b-293f-4c55-b5f7-ed0fff07a3d6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026542551700, "endTime": 32026544176200}, "additional": {"logType": "info", "children": [], "durationId": "217f0d9e-7465-4d52-9306-fb78605516ba", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "3dfb5447-4edc-40cd-a223-8162dde46bc9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026544181900, "endTime": 32026544182900}, "additional": {"logType": "info", "children": [], "durationId": "4dd1a4d6-ff3a-4a7c-ac4d-0a93a90ffc14", "parent": "72e5291d-937e-47bb-9708-929378d28ea8"}}, {"head": {"id": "72e5291d-937e-47bb-9708-929378d28ea8", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026030919500, "endTime": 32026544185300}, "additional": {"logType": "info", "children": ["a22a16cd-8624-4ca5-b8a7-6c0b9d2074cf", "9cf3221f-4cc2-4e13-8c94-03de40b163f6", "05e23e6b-293f-4c55-b5f7-ed0fff07a3d6", "3dfb5447-4edc-40cd-a223-8162dde46bc9", "32ba43a9-df8a-46a1-8a56-6bd40c5772f9", "b5a5c61d-fae9-4c4c-965a-159e03d4ceb3", "f25b40f0-fdcb-4b85-a83a-f41229fe734b"], "durationId": "78ce61d7-3dca-4724-9dbc-daec95046b08"}}, {"head": {"id": "391a124b-eefb-4292-b5e9-5a544d15faa6", "name": "Configuration task cost before running: 517 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026544374800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13be6f93-a4ad-4317-947b-c945a34b269b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026550240300, "endTime": 32026573833400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "56c5c5db-10f8-48f2-9195-31faf6b72eaf", "logId": "3735dc16-7abb-45e9-a1a7-5f420dd26eb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56c5c5db-10f8-48f2-9195-31faf6b72eaf", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026546006400}, "additional": {"logType": "detail", "children": [], "durationId": "13be6f93-a4ad-4317-947b-c945a34b269b"}}, {"head": {"id": "65c7a379-abe1-44b0-8a3f-2494eda6e104", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026546445100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1eee1d6-e53e-4f5c-97ae-a076ac40dae7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026546544000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b663b5b1-c551-41fe-b64c-ca648ab03922", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026550253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8ecbe53-7bae-4d90-adc7-8566502efbda", "name": "Incremental task entry:default@PreBuild pre-execution cost: 13 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026573416300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c831fd55-782c-42f6-bd08-57c8382fc07a", "name": "entry : default@PreBuild cost memory 0.31459808349609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026573677300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3735dc16-7abb-45e9-a1a7-5f420dd26eb6", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026550240300, "endTime": 32026573833400}, "additional": {"logType": "info", "children": [], "durationId": "13be6f93-a4ad-4317-947b-c945a34b269b"}}, {"head": {"id": "9147b650-f67f-405d-b6cd-df19b9df3af0", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026592162700, "endTime": 32026599471100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b5d84413-9ff3-43dd-a42c-af423ac66267", "logId": "00d7ead5-2f21-4fa0-b147-682fb3993a6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b5d84413-9ff3-43dd-a42c-af423ac66267", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026588371900}, "additional": {"logType": "detail", "children": [], "durationId": "9147b650-f67f-405d-b6cd-df19b9df3af0"}}, {"head": {"id": "637d4c37-009c-41fc-83fd-b7231eb8df01", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026589859600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6848f1c9-30d2-4710-b648-5b5352315937", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026590068500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11905a4-f0e6-4868-8724-074ff347195f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026592175600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "494d190e-ec5e-4fc2-be7c-8941d771bce1", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026594661600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4469c2-670d-4714-8b2c-87b8e62d4a4a", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026596872100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c941a95-5351-4129-bc24-a2b84d3d0859", "name": "entry : default@GenerateMetadata cost memory 0.09410858154296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026598443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00d7ead5-2f21-4fa0-b147-682fb3993a6e", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026592162700, "endTime": 32026599471100}, "additional": {"logType": "info", "children": [], "durationId": "9147b650-f67f-405d-b6cd-df19b9df3af0"}}, {"head": {"id": "697062c4-e128-4613-9ab5-dab9c2d83d18", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026621226900, "endTime": 32026622092100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c2f04e83-f9e9-4bfa-b6ff-4a21f2696464", "logId": "8979bdfb-e328-424e-a2e7-6d0dc4c89a93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c2f04e83-f9e9-4bfa-b6ff-4a21f2696464", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026618981300}, "additional": {"logType": "detail", "children": [], "durationId": "697062c4-e128-4613-9ab5-dab9c2d83d18"}}, {"head": {"id": "5279dbef-e008-4714-9315-95b8381e34b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026620409200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c281e5-96ee-4e9f-93e3-9eb3a9857a59", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026620785400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a95c9f7-14a8-4f4b-8264-59167c6803ca", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026621248400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b71abfc2-bd87-4d30-8356-94ee173e843f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026621434400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7286a62c-13ab-4585-8948-62e124887d51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026621816500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a560ed3-c477-4e29-8210-f64302e50b38", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026621942400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3032fa58-7aa3-42f9-95d7-1dbd8d8f9b25", "name": "runTaskFromQueue task cost before running: 595 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026622029900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8979bdfb-e328-424e-a2e7-6d0dc4c89a93", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026621226900, "endTime": 32026622092100, "totalTime": 782300}, "additional": {"logType": "info", "children": [], "durationId": "697062c4-e128-4613-9ab5-dab9c2d83d18"}}, {"head": {"id": "d4bf4638-ce2c-47bc-8594-5d84abfa06d3", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026631513400, "endTime": 32026638579400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "dfc34d50-19c9-4c3f-9487-11605a9e35bd", "logId": "20ca7f97-e442-439a-afed-d0a1b61b9c5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfc34d50-19c9-4c3f-9487-11605a9e35bd", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026627497000}, "additional": {"logType": "detail", "children": [], "durationId": "d4bf4638-ce2c-47bc-8594-5d84abfa06d3"}}, {"head": {"id": "c35fac40-2cdf-43c7-827c-bd2a7395ba36", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026629062600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04146f4-263d-4f97-aca7-c1e807ccd15d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026629203300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4d6b006-336d-4f9b-85c5-a91a10b8e55d", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026631525500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cbc8f91-6050-4bc1-ae06-2612d936a5b1", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026637110800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e56f3e76-5203-497d-871b-bc7f3b9a5490", "name": "entry : default@MergeProfile cost memory 0.1060028076171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026638186700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ca7f97-e442-439a-afed-d0a1b61b9c5b", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026631513400, "endTime": 32026638579400}, "additional": {"logType": "info", "children": [], "durationId": "d4bf4638-ce2c-47bc-8594-5d84abfa06d3"}}, {"head": {"id": "2fa5d5db-4950-4af8-accb-2a19ebb68953", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026648330100, "endTime": 32026654205100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d846d597-77a5-4e4d-91cc-d3b42b8a509f", "logId": "c7a3e17f-988f-4ac4-89db-8b2b2cdfc4a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d846d597-77a5-4e4d-91cc-d3b42b8a509f", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026644580600}, "additional": {"logType": "detail", "children": [], "durationId": "2fa5d5db-4950-4af8-accb-2a19ebb68953"}}, {"head": {"id": "1ff0f4e5-8b95-4047-bc43-9178f1b3e4c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026645063000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca941c5-5c1c-4aa0-913a-2e268716eb88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026645896000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04cb2f9f-11e7-4fe0-8309-10dccc509140", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026648344300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f645d9b-f640-4a9f-8999-2d3e2ea12659", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026650153500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb788b28-571a-408a-bbe5-4285110381c8", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026653668300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa07fdd8-1b5d-4bf5-b60b-6bff2cb4f5c8", "name": "entry : default@CreateBuildProfile cost memory 0.10306549072265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026654016900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a3e17f-988f-4ac4-89db-8b2b2cdfc4a4", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026648330100, "endTime": 32026654205100}, "additional": {"logType": "info", "children": [], "durationId": "2fa5d5db-4950-4af8-accb-2a19ebb68953"}}, {"head": {"id": "0092c2a1-c5ae-4690-82c4-685c5ed57ba6", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026664655900, "endTime": 32026668122000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "17454b76-8c95-4c0e-ba2d-98a65834368d", "logId": "42440a53-c5bd-45d0-91f3-766564c9da48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17454b76-8c95-4c0e-ba2d-98a65834368d", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026659192400}, "additional": {"logType": "detail", "children": [], "durationId": "0092c2a1-c5ae-4690-82c4-685c5ed57ba6"}}, {"head": {"id": "4f9e0dc8-384a-4934-b803-c363d79c2228", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026660987600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f242bf-a945-4af7-ad06-dc405665ef91", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026661371600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35c039e7-26d2-415d-8ea8-90ec25656749", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026664679400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1fed62d-eb4e-4b35-8e07-9c5459e2f300", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026666400200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e510f77-cebb-4e1e-81e3-1e6ea64a2939", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026667101100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30699264-cbda-4733-8948-1fbb3f6da2d7", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026667565100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa7fbc9-4040-4b9b-9160-04a3ea040cd1", "name": "runTaskFromQueue task cost before running: 640 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026667944400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42440a53-c5bd-45d0-91f3-766564c9da48", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026664655900, "endTime": 32026668122000, "totalTime": 3254300}, "additional": {"logType": "info", "children": [], "durationId": "0092c2a1-c5ae-4690-82c4-685c5ed57ba6"}}, {"head": {"id": "a6324e6e-321b-40d3-9cfd-963eb1e96069", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026691220200, "endTime": 32026692895100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "9255f388-c4a4-4b83-8dd9-01aaf4ab9675", "logId": "dff800d0-2074-463e-8327-324446716a02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9255f388-c4a4-4b83-8dd9-01aaf4ab9675", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026673961900}, "additional": {"logType": "detail", "children": [], "durationId": "a6324e6e-321b-40d3-9cfd-963eb1e96069"}}, {"head": {"id": "f66e50b8-7500-44d4-9cb1-6ff91f1ed847", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026677572900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "597e9b05-ad5c-4da4-b86a-58bc2732429b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026680231500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca4550c-bd93-43c0-abc9-5275d4170c03", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026691361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fa54b3d-4b1d-4636-ade6-1738891008ef", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026691950400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94cbd61-763d-4454-b53e-aa81a280ee99", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03856658935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026692651000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50402b87-ddaf-4f0c-b94b-9a3ba850413a", "name": "runTaskFromQueue task cost before running: 665 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026692817400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff800d0-2074-463e-8327-324446716a02", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026691220200, "endTime": 32026692895100, "totalTime": 1570400}, "additional": {"logType": "info", "children": [], "durationId": "a6324e6e-321b-40d3-9cfd-963eb1e96069"}}, {"head": {"id": "d6fb0448-ced7-4ff7-8b6f-ea58fa937d1a", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026711637600, "endTime": 32026723006500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "f26e8ecb-0e33-4c8d-a5c4-69ad4d5bf521", "logId": "2e3e57a7-b263-4bc1-a640-63a02c90ee32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f26e8ecb-0e33-4c8d-a5c4-69ad4d5bf521", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026701572000}, "additional": {"logType": "detail", "children": [], "durationId": "d6fb0448-ced7-4ff7-8b6f-ea58fa937d1a"}}, {"head": {"id": "ba573b6c-933a-4a97-8ad6-0cf7da8e2276", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026703409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c7baaf-9bf0-4777-8eb6-3c78a34bd622", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026703545700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "518c39a5-c5c8-4e0d-b55d-09286a7f853b", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026711699300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fdab3a1-9832-4e9d-8f72-9a19a34fc17e", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026720819400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d2959e-7949-4eaf-9eba-aa5e0fec0e55", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026721081500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb261c87-3f9d-40b0-9e1a-ca71e2a30dc1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026721724500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "971fda7c-db0c-4ecd-9665-1d27bf0f91aa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026722047100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0587e7-1c15-4b60-8122-06cbbd603287", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1179046630859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026722299700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "149d9c06-25b8-430b-a4c3-90a3c4131483", "name": "runTaskFromQueue task cost before running: 695 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026722424700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3e57a7-b263-4bc1-a640-63a02c90ee32", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026711637600, "endTime": 32026723006500, "totalTime": 10775300}, "additional": {"logType": "info", "children": [], "durationId": "d6fb0448-ced7-4ff7-8b6f-ea58fa937d1a"}}, {"head": {"id": "9c9ebdd7-6d18-4318-bc81-ea2455b5fd99", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026731543100, "endTime": 32026732408300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "961718cb-e82c-4f5d-bedb-8122bf89ff05", "logId": "58d8520a-6bbb-44f7-9302-38fab44e51ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "961718cb-e82c-4f5d-bedb-8122bf89ff05", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026727698300}, "additional": {"logType": "detail", "children": [], "durationId": "9c9ebdd7-6d18-4318-bc81-ea2455b5fd99"}}, {"head": {"id": "ee7ffe93-41ff-48e4-b313-08d39c2c423e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026728409300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b59b516-b5a2-4c51-81bf-333697b6bafc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026728609900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca1b815c-0e3c-4b42-b423-857ebd882048", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026731558400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7f7d42-2728-4fe1-a2c1-15e95e86b57d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026731829900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a35013d6-f7b3-4eb2-a5ce-d1d785d88a13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026731908000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0085b1df-6c3a-455c-be7a-5bdeef28e256", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026732038700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c3efddf-1722-4329-a7d1-1152bfcdf177", "name": "runTaskFromQueue task cost before running: 705 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026732260600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d8520a-6bbb-44f7-9302-38fab44e51ff", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026731543100, "endTime": 32026732408300, "totalTime": 687600}, "additional": {"logType": "info", "children": [], "durationId": "9c9ebdd7-6d18-4318-bc81-ea2455b5fd99"}}, {"head": {"id": "8f1ff0f3-e82a-42ae-b7ff-2e2c25adb6bb", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026742648500, "endTime": 32026748904600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ff5a8e08-0c3b-4914-8b0b-50bdea208787", "logId": "c18b4204-ce0c-4e49-bde0-5959dbb87d24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff5a8e08-0c3b-4914-8b0b-50bdea208787", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026737719700}, "additional": {"logType": "detail", "children": [], "durationId": "8f1ff0f3-e82a-42ae-b7ff-2e2c25adb6bb"}}, {"head": {"id": "201ad242-412a-4377-af49-f60829ae79af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026739053800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac7449d3-4c99-469b-848e-c3c2471b52da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026741000400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4773fba-2a84-4614-8b3b-5f6437774401", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026742696300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afde3f38-5781-435f-87e8-c91cc4ba9f91", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026748669700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f5b7fc-3576-4b82-a0f4-39449e4a898b", "name": "entry : default@MakePackInfo cost memory 0.13898468017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026748823400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c18b4204-ce0c-4e49-bde0-5959dbb87d24", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026742648500, "endTime": 32026748904600}, "additional": {"logType": "info", "children": [], "durationId": "8f1ff0f3-e82a-42ae-b7ff-2e2c25adb6bb"}}, {"head": {"id": "5638c3f3-0b69-4e58-9bca-f0c340db27c2", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026763004900, "endTime": 32026769519600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "fa344f69-06b0-40e2-9e46-7b4a9f5362df", "logId": "fb416129-c17b-456b-a701-18c2d7c43f28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa344f69-06b0-40e2-9e46-7b4a9f5362df", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026758769000}, "additional": {"logType": "detail", "children": [], "durationId": "5638c3f3-0b69-4e58-9bca-f0c340db27c2"}}, {"head": {"id": "c783f880-f317-4d7c-b86e-0296d0af00ad", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026759584900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc9a957-92e5-493b-b7fb-bb54454b9523", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026760086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8be015f-c3b1-4464-acac-b9c27aeeebc5", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026763018500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de5afbf3-f664-4844-a86f-729190c4fd2f", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026763459000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c25a554-94ac-4fab-9fd8-bc9f704a4832", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 3 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026765914400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663b5596-b884-4d4c-be29-6a11bd133bb1", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026768392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44f178c8-8066-47c2-b19d-92a5f12a232b", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026768551800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05c9d751-26d5-4a2e-a5b9-2653c91944d4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026768894300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5256f38c-4766-4b67-a303-c4512c7858ff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026769012900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7276577-7c93-40e8-a716-3b058b895cbb", "name": "entry : default@SyscapTransform cost memory 0.15178680419921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026769108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90c236fd-6e31-41a4-8c96-90ed20bf39d1", "name": "runTaskFromQueue task cost before running: 742 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026769238500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb416129-c17b-456b-a701-18c2d7c43f28", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026763004900, "endTime": 32026769519600, "totalTime": 6209000}, "additional": {"logType": "info", "children": [], "durationId": "5638c3f3-0b69-4e58-9bca-f0c340db27c2"}}, {"head": {"id": "a84343ab-8be8-4f5a-ac22-4b64276979c0", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026777210200, "endTime": 32026778386300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3a7d5d34-c14a-4d28-94f8-91d3ab5ee5c3", "logId": "6122fd99-6244-4ffd-9790-218da8a9c4a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a7d5d34-c14a-4d28-94f8-91d3ab5ee5c3", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026775507200}, "additional": {"logType": "detail", "children": [], "durationId": "a84343ab-8be8-4f5a-ac22-4b64276979c0"}}, {"head": {"id": "bdb45614-e653-4ded-8dd5-c2dc2dcf3ab1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026776020500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a057cbe2-ace4-4122-90fc-c5a7962eb756", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026776127300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "672a403c-a68a-4bcb-afd8-6fcf9dd88aa4", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026777219100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2372d7d-4d6f-404e-ae14-5ee57b050952", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026778197400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faeea6a2-7c9e-4c63-a171-8ca174f0ccca", "name": "entry : default@ProcessProfile cost memory 0.05995941162109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026778308400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6122fd99-6244-4ffd-9790-218da8a9c4a2", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026777210200, "endTime": 32026778386300}, "additional": {"logType": "info", "children": [], "durationId": "a84343ab-8be8-4f5a-ac22-4b64276979c0"}}, {"head": {"id": "51d26d90-f8b2-46c4-8e5e-d2001b570f42", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026783896500, "endTime": 32026790815500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d471ce94-2da2-49c7-bad3-baa3bbf90c58", "logId": "568a5491-c70c-4910-9220-78e27b897159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d471ce94-2da2-49c7-bad3-baa3bbf90c58", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026780598900}, "additional": {"logType": "detail", "children": [], "durationId": "51d26d90-f8b2-46c4-8e5e-d2001b570f42"}}, {"head": {"id": "d57b0b10-6882-4ff3-afe3-2564c399246c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026781107000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "600a1033-6d0b-4c43-a200-a277fc3eff7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026781381200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93889b2d-fa31-4059-82ad-2e1b2a656e23", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026783908700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20ca540d-5041-48e1-a102-0a02c86b9684", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026790076000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3edc1158-cb32-4e1d-978a-055986a0f534", "name": "entry : default@ProcessRouterMap cost memory 0.2026519775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026790361300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "568a5491-c70c-4910-9220-78e27b897159", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026783896500, "endTime": 32026790815500}, "additional": {"logType": "info", "children": [], "durationId": "51d26d90-f8b2-46c4-8e5e-d2001b570f42"}}, {"head": {"id": "4a2e554a-c34a-42ac-a678-657fccf23e16", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026798023300, "endTime": 32026800050400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "19577eea-cd8e-4841-8862-4c2e0f92cd8e", "logId": "d04b16c5-e5c9-46e1-b3e0-8b6ec8b02020"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19577eea-cd8e-4841-8862-4c2e0f92cd8e", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026795140800}, "additional": {"logType": "detail", "children": [], "durationId": "4a2e554a-c34a-42ac-a678-657fccf23e16"}}, {"head": {"id": "4e40b814-3487-41f8-8ba5-0d5735e7bfe4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026795858200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14be6e72-a3bb-401b-893f-0e7428077612", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026796066200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02eb0306-91ea-49cc-85a1-ec2ea9e9c665", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026798039700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe60576d-f77b-49df-81ac-0b2a30a1183e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026798237000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7831cc1d-3cca-4bd8-8d9d-0c64436bf22e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026798359400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f83649ef-81cd-48af-8ec9-f33f88b3ef61", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026799768800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c26a69d-54df-4419-aee4-0a65f07b724c", "name": "runTaskFromQueue task cost before running: 772 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026799939000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d04b16c5-e5c9-46e1-b3e0-8b6ec8b02020", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026798023300, "endTime": 32026800050400, "totalTime": 1886700}, "additional": {"logType": "info", "children": [], "durationId": "4a2e554a-c34a-42ac-a678-657fccf23e16"}}, {"head": {"id": "b46f299e-fad6-4f9b-b8d4-99b958d4f28e", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026809904700, "endTime": 32026821007000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2c09d83d-023d-4824-be75-c1da5ab15df2", "logId": "fdb25149-7bf4-44fd-b787-deeca3ea43c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c09d83d-023d-4824-be75-c1da5ab15df2", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026804036200}, "additional": {"logType": "detail", "children": [], "durationId": "b46f299e-fad6-4f9b-b8d4-99b958d4f28e"}}, {"head": {"id": "a525f8b6-d175-4855-b066-1157784d81cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026805101700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "187216e1-cbc3-4dce-99f5-f837fb1b8845", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026805266800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9100944-2410-4425-9a17-914b6140fdc9", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026807857200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b38b6fc0-7534-4379-80d6-af0934f86201", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026812325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7f7aabc-d04c-45a2-9f0e-905ab47cfd1a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026817724800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a04c3e91-5892-4654-bee8-5363f01d0703", "name": "entry : default@ProcessResource cost memory 0.16980743408203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026818456800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdb25149-7bf4-44fd-b787-deeca3ea43c9", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026809904700, "endTime": 32026821007000}, "additional": {"logType": "info", "children": [], "durationId": "b46f299e-fad6-4f9b-b8d4-99b958d4f28e"}}, {"head": {"id": "5343aed3-c10e-4642-a6b2-bd50b79427be", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026833643000, "endTime": 32026847960500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5f00607c-ca99-4558-8810-41fe5d35c499", "logId": "42f1dfb5-059c-4660-91be-aa69116a893f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f00607c-ca99-4558-8810-41fe5d35c499", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026828807800}, "additional": {"logType": "detail", "children": [], "durationId": "5343aed3-c10e-4642-a6b2-bd50b79427be"}}, {"head": {"id": "da6d222c-1f08-4e98-ade1-fcb8c73f9c99", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026829283100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b267dc5f-410b-4f93-9b31-2281f5da6037", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026829398300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baa8eda6-f794-47e9-9f6b-60e2413bca5a", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026833653700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69492dc0-c4d5-4a5c-a803-cf6efa28e8b8", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026847746800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5431ff88-3af2-45b5-acaf-9f3333848017", "name": "entry : default@GenerateLoaderJson cost memory 0.7640533447265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026847892500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f1dfb5-059c-4660-91be-aa69116a893f", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026833643000, "endTime": 32026847960500}, "additional": {"logType": "info", "children": [], "durationId": "5343aed3-c10e-4642-a6b2-bd50b79427be"}}, {"head": {"id": "cf48f53c-7da4-44fa-a6d6-a5d899511127", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026860752400, "endTime": 32026865116000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c3b22f17-823d-4638-9a8c-03f32637950f", "logId": "95ae94d0-66c8-4228-8427-e3b8e7dae009"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3b22f17-823d-4638-9a8c-03f32637950f", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026858427900}, "additional": {"logType": "detail", "children": [], "durationId": "cf48f53c-7da4-44fa-a6d6-a5d899511127"}}, {"head": {"id": "a26cb10e-50bf-4b52-a5cf-999599383e0d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026859215600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad90267e-e279-40a4-83b3-252b323f55ef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026859409500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3030032b-38e0-470a-98a2-9742c363baf2", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026860787500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaacd066-32af-4a72-b9f6-ddb9dffcb7dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026863834100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "749e1a30-53ff-4242-b39a-9f9a8a1be5ea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026863995100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c890d9b-94db-4b8b-832c-fed6008c04ec", "name": "entry : default@ProcessLibs cost memory 0.1255645751953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026864899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9150101-ea1b-405f-a0ff-89211cd033bf", "name": "runTaskFromQueue task cost before running: 838 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026865043300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95ae94d0-66c8-4228-8427-e3b8e7dae009", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026860752400, "endTime": 32026865116000, "totalTime": 4266600}, "additional": {"logType": "info", "children": [], "durationId": "cf48f53c-7da4-44fa-a6d6-a5d899511127"}}, {"head": {"id": "e5c9eed5-31e5-42bc-9380-7d98d504c064", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026871595400, "endTime": 32026894260300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2b8461c7-664c-487a-a02a-3e7905ee7926", "logId": "2e0dead4-1c2f-418c-adaa-fa994e5778cb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b8461c7-664c-487a-a02a-3e7905ee7926", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026867971300}, "additional": {"logType": "detail", "children": [], "durationId": "e5c9eed5-31e5-42bc-9380-7d98d504c064"}}, {"head": {"id": "673afa21-5c26-44d4-83cf-641abd30b790", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026868399000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db0a1cc4-97fe-4255-afda-564c972d4a6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026868505000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af72080c-337f-4b0a-88f4-243b2d7fe070", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026869232100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "835619ae-1ed7-4a36-a134-c27f020fa2a8", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026871620700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb89c6b-e645-4808-b000-de7c6169a09b", "name": "Incremental task entry:default@CompileResource pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026893961000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fbd3c2e-3fb8-4d2e-a79b-ff45c2e916d7", "name": "entry : default@CompileResource cost memory 1.4048843383789062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026894097800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0dead4-1c2f-418c-adaa-fa994e5778cb", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026871595400, "endTime": 32026894260300}, "additional": {"logType": "info", "children": [], "durationId": "e5c9eed5-31e5-42bc-9380-7d98d504c064"}}, {"head": {"id": "cc1b64b0-a240-4b3b-8dd0-ea71c0afced2", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026900079500, "endTime": 32026901391600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "7201595f-d1fc-42be-929d-6cbaa866a524", "logId": "af9350d7-1408-4173-99a1-e650202be673"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7201595f-d1fc-42be-929d-6cbaa866a524", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026897344900}, "additional": {"logType": "detail", "children": [], "durationId": "cc1b64b0-a240-4b3b-8dd0-ea71c0afced2"}}, {"head": {"id": "ddb418f0-177b-4a41-8d80-8c41a73623ef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026897754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c633af63-67ac-4325-a9b6-5624cc07f391", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026897863900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c44c1d00-a933-4dc9-8f0e-98a6f52c5b4b", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026900089400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3633244-8d4f-4bb7-8094-fff0024e341e", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026900344800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9e6ba3c-13e5-4d41-9c78-b5cfaf861ce9", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026901223200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de38a4f-042b-443f-bc29-206f69164fc0", "name": "entry : default@DoNativeStrip cost memory 0.07353973388671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026901324500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af9350d7-1408-4173-99a1-e650202be673", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026900079500, "endTime": 32026901391600}, "additional": {"logType": "info", "children": [], "durationId": "cc1b64b0-a240-4b3b-8dd0-ea71c0afced2"}}, {"head": {"id": "ac74e79f-e848-4ae0-936b-0742871be578", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026908166200, "endTime": 32026922863600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "cd8940db-ccf1-43db-bb99-8e3d3b772978", "logId": "e30b706e-ffd9-4caa-8154-7c52b1641685"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd8940db-ccf1-43db-bb99-8e3d3b772978", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026902917000}, "additional": {"logType": "detail", "children": [], "durationId": "ac74e79f-e848-4ae0-936b-0742871be578"}}, {"head": {"id": "f3654d1b-06cb-40ea-bc0a-e9b8c342799a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026903326400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fd605a-e4b2-4fce-994c-483bc068250e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026903429700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e3b501b-e52b-46b3-824c-bce87ef84ae2", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026908179200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9055161-e2cb-43e4-b6c1-7403e9d33775", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026922308600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ece3f53-2067-47d9-9c01-1fc3ee0c3f53", "name": "entry : default@CompileArkTS cost memory 0.6725387573242188", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026922543500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e30b706e-ffd9-4caa-8154-7c52b1641685", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026908166200, "endTime": 32026922863600}, "additional": {"logType": "info", "children": [], "durationId": "ac74e79f-e848-4ae0-936b-0742871be578"}}, {"head": {"id": "8fcfed0e-d4e9-4ad6-9e6d-95b79f76a156", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026933490600, "endTime": 32026936534100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0c666780-f663-4b67-9474-6f6a2c91c031", "logId": "a20747ae-f918-43be-8174-c19440d1c33c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c666780-f663-4b67-9474-6f6a2c91c031", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026929044600}, "additional": {"logType": "detail", "children": [], "durationId": "8fcfed0e-d4e9-4ad6-9e6d-95b79f76a156"}}, {"head": {"id": "bc012157-7fa7-488e-b274-80665cb503d2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026929452500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b01e75-3196-496c-b877-5af34369abfc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026929555700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ae1205-557a-4da9-9495-d88bc5cd52b7", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026933501700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9170e6a-365a-4d6c-b4e1-44da5c942895", "name": "entry : default@BuildJS cost memory 0.12706756591796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026936341700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40695d6c-0d27-4741-819c-b503b9fc3794", "name": "runTaskFromQueue task cost before running: 909 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026936470500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a20747ae-f918-43be-8174-c19440d1c33c", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026933490600, "endTime": 32026936534100, "totalTime": 2960900}, "additional": {"logType": "info", "children": [], "durationId": "8fcfed0e-d4e9-4ad6-9e6d-95b79f76a156"}}, {"head": {"id": "29eab55e-417c-48e9-95ec-46bec032a1de", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026942428800, "endTime": 32026943795500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6abbaf0f-8add-4ec8-9099-193f481dd89e", "logId": "59b4f621-a2fe-4419-b756-1e985bedb848"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6abbaf0f-8add-4ec8-9099-193f481dd89e", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026938301300}, "additional": {"logType": "detail", "children": [], "durationId": "29eab55e-417c-48e9-95ec-46bec032a1de"}}, {"head": {"id": "7b0ddd88-796d-491d-8c3a-edfabd822ed7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026938705800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7fc7f5b-9cda-4322-a011-08b7f1b7536e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026938809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ae4238-22aa-4324-89bd-d71d90e6584a", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026942439900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f4c15a-c03e-423d-aef9-bc07d4975fb9", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026942742600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e49110df-358c-4948-9575-a6573f77e2cd", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026943641900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f25bf915-05ea-4838-9128-4c209abd7cbc", "name": "entry : default@CacheNativeLibs cost memory 0.08721160888671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026943732300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59b4f621-a2fe-4419-b756-1e985bedb848", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026942428800, "endTime": 32026943795500}, "additional": {"logType": "info", "children": [], "durationId": "29eab55e-417c-48e9-95ec-46bec032a1de"}}, {"head": {"id": "47b6b6c3-36be-4944-a558-70d276a7437a", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026946674200, "endTime": 32026947735100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "846b29ea-2fcd-4561-a4c1-0fadc4b061f7", "logId": "d737d11b-b627-43d6-bed3-26bbb1a4f235"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "846b29ea-2fcd-4561-a4c1-0fadc4b061f7", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026945315600}, "additional": {"logType": "detail", "children": [], "durationId": "47b6b6c3-36be-4944-a558-70d276a7437a"}}, {"head": {"id": "73000575-c393-40d9-a8f0-a5ee231f0825", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026945666200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ae5b85d-72bd-4d4a-a759-522cc97bcf10", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026945761600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5249dbd1-e918-49c6-a1cf-6f9478b72bc1", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026946684400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0afeca8a-0a46-49d1-bbe7-91eeaa110d17", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026946890100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e363a83f-0427-4647-9505-c815752872d6", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026947509600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6155ece-f7b5-4080-a6be-1842cb0a39c3", "name": "entry : default@GeneratePkgModuleJson cost memory 0.069915771484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026947599100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d737d11b-b627-43d6-bed3-26bbb1a4f235", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026946674200, "endTime": 32026947735100}, "additional": {"logType": "info", "children": [], "durationId": "47b6b6c3-36be-4944-a558-70d276a7437a"}}, {"head": {"id": "b7152290-59ff-4955-a64b-a4a2a5cd654b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026958904800, "endTime": 32026976082100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "56422694-e92f-437a-8a48-99a26ced114a", "logId": "e9d9d89e-830b-464c-9146-c369307734a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56422694-e92f-437a-8a48-99a26ced114a", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026949644900}, "additional": {"logType": "detail", "children": [], "durationId": "b7152290-59ff-4955-a64b-a4a2a5cd654b"}}, {"head": {"id": "6d7071eb-936c-4c63-87e4-50d8ecc302e7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026949942200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c30773c8-5505-415e-839f-fa541d75417d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026950029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20fbc9e-3b82-4914-ab0c-40055c33eb8f", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026958916800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d032d259-3a7a-41cc-849e-b86fd99c4ed3", "name": "Incremental task entry:default@PackageHap pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026974856000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f028d8a3-73ca-4a13-b14b-6a07224e1718", "name": "entry : default@PackageHap cost memory 0.8801422119140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026975209100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9d9d89e-830b-464c-9146-c369307734a2", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026958904800, "endTime": 32026976082100}, "additional": {"logType": "info", "children": [], "durationId": "b7152290-59ff-4955-a64b-a4a2a5cd654b"}}, {"head": {"id": "cde70c6a-6b7d-4389-a3d4-872e9b023a4b", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026983003000, "endTime": 32026986665200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist."], "detailId": "a9f4c2f2-fa04-49c5-a5bc-c1c92b16624e", "logId": "41fe8627-bbca-4a63-b588-c8c3e845056e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9f4c2f2-fa04-49c5-a5bc-c1c92b16624e", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026979273900}, "additional": {"logType": "detail", "children": [], "durationId": "cde70c6a-6b7d-4389-a3d4-872e9b023a4b"}}, {"head": {"id": "dae361a5-715a-49a7-ab5e-385b3950fc3f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026979637800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7ad993a-8f83-4ec0-bd59-826ea07d40a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026979916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfd34fc1-5903-418f-9fb3-24468a70a142", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026983015000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90d59811-9e7d-4d19-8a8a-4d6c4d2808f6", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026984272800}, "additional": {"logType": "warn", "children": [], "durationId": "cde70c6a-6b7d-4389-a3d4-872e9b023a4b"}}, {"head": {"id": "6ec5af9b-e95a-4733-94b4-3420772a8dcb", "name": "entry:default@SignHap is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026985566700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368e8041-c1f3-4f47-a25b-72da4ea1ab2b", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026985730800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1631d3e-11d3-4386-b008-4a700eef185c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026985835100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ed3ad2a-241e-4037-8ef9-c74198ab11dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026985913100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "484dee45-dcfc-402b-a68d-b3c1677f9228", "name": "entry : default@SignHap cost memory 0.117706298828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026986415100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64dd95de-6af1-4b74-8dfd-8d14afd2c3a9", "name": "runTaskFromQueue task cost before running: 959 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026986566900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41fe8627-bbca-4a63-b588-c8c3e845056e", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026983003000, "endTime": 32026986665200, "totalTime": 3544600}, "additional": {"logType": "info", "children": [], "durationId": "cde70c6a-6b7d-4389-a3d4-872e9b023a4b"}}, {"head": {"id": "b2520f78-fb74-4652-aef6-a26b4380fa9f", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026993061500, "endTime": 32026999179800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a276aa22-cd0a-4b8d-8619-73283addbdd8", "logId": "06a8b1f1-f4fd-41a3-a774-443aa11532ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a276aa22-cd0a-4b8d-8619-73283addbdd8", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026991492600}, "additional": {"logType": "detail", "children": [], "durationId": "b2520f78-fb74-4652-aef6-a26b4380fa9f"}}, {"head": {"id": "92f8ab51-3b8a-415f-8432-27ecb7ef1ca1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026992010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c81777d-3937-4466-8d22-171436fb311e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026992127200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f05f75a6-ba26-4074-90d6-1a464674e9de", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026993073700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e2da279-12ce-4941-af04-a1633da36895", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026998851600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74770a99-151c-4b18-8822-22d3069fcd0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026998964400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a31ac1bb-b9dd-4503-b1f8-d0a21e585b67", "name": "entry : default@CollectDebugSymbol cost memory 0.23957061767578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026999042700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934498bc-c88b-49ce-8806-88042494ce0e", "name": "runTaskFromQueue task cost before running: 972 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026999121500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a8b1f1-f4fd-41a3-a774-443aa11532ca", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026993061500, "endTime": 32026999179800, "totalTime": 6037300}, "additional": {"logType": "info", "children": [], "durationId": "b2520f78-fb74-4652-aef6-a26b4380fa9f"}}, {"head": {"id": "4f7b8ebc-9fe1-471f-a12e-1225c3fbd9f9", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027001450600, "endTime": 32027001829900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "10cb577f-8772-4a62-992f-aad020eba40e", "logId": "200d4f7c-7e71-4619-8393-ca8175e0c20c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10cb577f-8772-4a62-992f-aad020eba40e", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027001413700}, "additional": {"logType": "detail", "children": [], "durationId": "4f7b8ebc-9fe1-471f-a12e-1225c3fbd9f9"}}, {"head": {"id": "bf959fc2-04e7-48ca-ba46-c7255752aa40", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027001457500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "759a923a-9c84-41be-9fde-4f0cedc20ac4", "name": "entry : assembleHap cost memory 0.01145172119140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027001593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d39631-4b88-4ed8-a2ce-f6a8677aff2a", "name": "runTaskFromQueue task cost before running: 974 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027001760700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200d4f7c-7e71-4619-8393-ca8175e0c20c", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027001450600, "endTime": 32027001829900, "totalTime": 290000}, "additional": {"logType": "info", "children": [], "durationId": "4f7b8ebc-9fe1-471f-a12e-1225c3fbd9f9"}}, {"head": {"id": "ba5e0aea-f3b6-4cb8-9614-a33ef887da04", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027011572700, "endTime": 32027011595100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "68025af9-4b3d-4baf-95d8-79f1fd2ee6c1", "logId": "2b6b71fe-904c-46eb-9c95-9faf7c19a06e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b6b71fe-904c-46eb-9c95-9faf7c19a06e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027011572700, "endTime": 32027011595100}, "additional": {"logType": "info", "children": [], "durationId": "ba5e0aea-f3b6-4cb8-9614-a33ef887da04"}}, {"head": {"id": "206d696f-e3b1-4714-884e-dbcc93187cff", "name": "BUILD SUCCESSFUL in 984 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027011644900}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "707dcbad-d3e6-4b2f-a9e2-8fa0032c0cf9", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32026027973500, "endTime": 32027011858400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 23}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "29076ddd-8c21-436b-bbb2-f7a2041df1d4", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027011876000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9a9e70-be0d-481e-992e-87c968869e00", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027011935900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1fa39a4-714a-46c7-8d18-1633dc775733", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027011978600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19227dc2-d69f-4ee4-b5f6-3e859f7f7ca8", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027012020300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da187aff-70c9-452b-902a-d9ea616c0d20", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027012070400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c03a338-c77e-4705-a195-8956ada8f5b6", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027012769400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fce4820-e31e-4560-b9df-55609a0809d9", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027014194100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6b14428-cf07-4e8b-8348-94175e70d28b", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027015024400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a438227-b9d1-410e-8e47-02a786bd48b1", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027015251000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427a8cae-0e1f-48a9-a8e7-1fc4f3bef966", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027015723600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf28d83-1a47-45de-b37f-8429eb426d80", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027016206700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c23a31d-f0c9-4560-ab2e-0e43010115cf", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027018097500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c73a81-0479-4c35-8183-f25237de69f6", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027018583100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe1862f-cfdb-4ce1-aa99-fe858b30d2c8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027019084900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4bf9335-c588-406e-bb37-b2a114d29449", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027019374500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61bc2803-9e7c-4de5-8e1c-5f0be42eecff", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027019561100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bb923f5-2748-4f8f-9ea7-18206c498bc9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027019826200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98c68cf-9de1-432d-84d5-2f75f67cac1c", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027021446100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8fa2ebb-92ab-45e8-b707-f59638faeafb", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027022234500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c29258c5-4a80-47e3-afa2-3c80f7e6800c", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027024464100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64d909f4-517d-4911-b302-48fde5255798", "name": "Incremental task entry:default@ProcessLibs post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027026531900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cfe50d1-c9c0-4def-9562-849359717f76", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027027195700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0132bd87-de08-4986-9e5c-047b6cd5204d", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027027675200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "144e9c48-c42e-4814-a712-8fcbd941ebfe", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027027854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38f77ed3-d320-4eb0-ba50-ae27339715e7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027029877900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4279e0f-49b9-430a-972c-876fae33886b", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027030524300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f77641e2-b5c2-44d4-a1d2-912ff6daa56c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027031666500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b13cb4c6-6503-48d6-b440-a70355e400a1", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027031943100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b5be783-3814-40ae-a192-365b413b610e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027032177700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abf68a5d-1aef-49f6-977d-3d74446069fb", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027033092000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e23b0ed-77cb-446b-ba05-4b2af4695c58", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027033497900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3734579-60de-4b91-864c-643ed4e12281", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027033610600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "769ee39d-ddd1-4c94-9b3d-b5fb4a0c525c", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027033886700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0d863d-4dd9-4cae-9e64-803c814b4118", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027034010300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7abba7e-ebe5-40b4-8087-91574aa86626", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027034434600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489a259e-02b4-4029-9d24-f6147a1dbeda", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027035171800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696bc51b-bcec-4e73-92b7-0aaf036f0de1", "name": "Incremental task entry:default@SignHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027035712400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b168c72-7e27-411c-a091-8bcea2ad5e8e", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027042040900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "581013b3-d509-473f-8dc6-3a2f7e9498fb", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027042704100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56344331-187b-4609-9292-58e3a7db2604", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027043176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa7c8bff-79b7-4996-a6ba-bbe6a78b7b29", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32027043891400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}