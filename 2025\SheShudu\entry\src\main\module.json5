{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet"], "deliveryWithInstall": true, "installationFree": true, "pages": "$profile:main_pages", "routerMap": "$profile:router_map", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:startIcon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "orientation": "auto_rotation", "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}], "extensionAbilities": [{"name": "EntryFormAbility", "srcEntry": "./ets/entryformability/EntryFormAbility.ets", "label": "$string:EntryFormAbility_label", "description": "$string:EntryFormAbility_desc", "type": "form", "metadata": [{"name": "ohos.extension.form", "resource": "$profile:form_config"}]}]}}