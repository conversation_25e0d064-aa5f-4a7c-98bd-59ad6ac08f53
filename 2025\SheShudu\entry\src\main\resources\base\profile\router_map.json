{"routerMap": [{"name": "s<PERSON><PERSON><PERSON><PERSON>", "pageSourceFile": "src/main/ets/view/ShuduGame.ets", "buildFunction": "ShuduGameBuilder", "data": {"description": "this is <PERSON><PERSON><PERSON><PERSON>"}}, {"name": "personalization", "pageSourceFile": "src/main/ets/view/Personalization/Personalization.ets", "buildFunction": "PersonalizationBuilder", "data": {"description": "this is personalization"}}, {"name": "gameRules", "pageSourceFile": "src/main/ets/view/Personalization/GameRules.ets", "buildFunction": "GameRulesBuilder", "data": {"description": "this is game rules"}}, {"name": "gameSettings", "pageSourceFile": "src/main/ets/view/Personalization/GameSettings.ets", "buildFunction": "GameSettingsBuilder", "data": {"description": "this is game settings"}}]}