{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "57494be0-a200-4c3d-b561-223f47fc41a3", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933078174400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe9a7da-d0be-44dd-825d-939386970173", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933196045700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d99111ab-336f-4d93-aeb0-8358ecda3219", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27933196455400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6394294d-3b05-455a-9841-cfc8111d363b", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978765106500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29ab284-93ba-4bff-ab1f-7553561340d4", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978774059700, "endTime": 27979199208900}, "additional": {"children": ["acd0b270-a76d-4b05-8a07-c39567b597f8", "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "aa4b4357-2156-4070-82f0-0bd80656239a", "60dc1d40-d82e-42e2-bb32-dff485569627", "9920121b-4ec4-417e-a7dc-8464f8dab37d", "5d7033a7-6872-4376-8570-4b508e61be81", "a5daca4b-872e-4b79-b1aa-b7487a443444"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "acd0b270-a76d-4b05-8a07-c39567b597f8", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978774061900, "endTime": 27978789167400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "8f2f9ac5-75ea-453b-95ca-c6da8cf9dab4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978789189900, "endTime": 27979193165400}, "additional": {"children": ["9037a633-8025-4b22-b953-48df08d2a17a", "4832878e-f34b-4efb-94f0-e94a3e967057", "34688f2b-c797-418f-88e8-a575e71005fe", "1ab8bd0c-bef1-402e-91b6-96b3941b051d", "303d9833-16a6-4f7b-9fe0-57a30575fc35", "663de782-a36a-458e-8ff9-80b078ce64c8", "153cc9c1-c3f5-43f1-a1b2-b0a18995bd53", "0001dc8b-1c01-46fe-8873-c63a30a31171", "deedbe64-6242-4bca-9745-06d8bc142b0c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa4b4357-2156-4070-82f0-0bd80656239a", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979193191400, "endTime": 27979199023800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "6c93d09e-99cb-4e84-a8c7-47e646246978"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60dc1d40-d82e-42e2-bb32-dff485569627", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979199159100, "endTime": 27979199174200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "473b15dd-1e3e-4179-9113-291541f4ba34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9920121b-4ec4-417e-a7dc-8464f8dab37d", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978777030600, "endTime": 27978777079000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "a73dd354-3bfb-445b-952c-8641f728ab14"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a73dd354-3bfb-445b-952c-8641f728ab14", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978777030600, "endTime": 27978777079000}, "additional": {"logType": "info", "children": [], "durationId": "9920121b-4ec4-417e-a7dc-8464f8dab37d", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "5d7033a7-6872-4376-8570-4b508e61be81", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978782445000, "endTime": 27978782465400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "71988500-e538-46ab-939a-1f58b35583b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71988500-e538-46ab-939a-1f58b35583b5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978782445000, "endTime": 27978782465400}, "additional": {"logType": "info", "children": [], "durationId": "5d7033a7-6872-4376-8570-4b508e61be81", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "1a273c22-baef-44bd-9946-c556235bcea3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978782508200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea596ff2-e616-4ed9-95ed-31fbfb313c63", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978788994800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f2f9ac5-75ea-453b-95ca-c6da8cf9dab4", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978774061900, "endTime": 27978789167400}, "additional": {"logType": "info", "children": [], "durationId": "acd0b270-a76d-4b05-8a07-c39567b597f8", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "9037a633-8025-4b22-b953-48df08d2a17a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978794830000, "endTime": 27978794838300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "0f133bae-0bd7-4ba2-a538-99368675b51c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4832878e-f34b-4efb-94f0-e94a3e967057", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978794855400, "endTime": 27978798655000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "0a5c9726-9035-4d66-a612-3f730d730534"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "34688f2b-c797-418f-88e8-a575e71005fe", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978798668400, "endTime": 27978919212100}, "additional": {"children": ["65b65554-002c-4c19-9430-edbe21490e77", "25520e26-547f-452b-a397-ba5262c65ae3", "7fe8af7d-4e1b-4c32-bd45-da79b51bf029"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "a7c686d7-b01a-4951-a461-cda824b2ada0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ab8bd0c-bef1-402e-91b6-96b3941b051d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978919228300, "endTime": 27978983233500}, "additional": {"children": ["4c978813-a3e0-4693-81da-b50ca065e02c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "7779a366-55bb-4e09-828e-36567eed3d9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "303d9833-16a6-4f7b-9fe0-57a30575fc35", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978983247900, "endTime": 27979151529000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "c845d829-586e-4ff6-85ca-3c847d0c7aeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "663de782-a36a-458e-8ff9-80b078ce64c8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979152678900, "endTime": 27979174847700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "8b25040a-a828-4d4d-b706-6ccc47cd10fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "153cc9c1-c3f5-43f1-a1b2-b0a18995bd53", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979174878400, "endTime": 27979192856500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "8fbef0f5-52bd-4ef3-a513-d766591cb7b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0001dc8b-1c01-46fe-8873-c63a30a31171", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979192890200, "endTime": 27979193123400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "77befdc0-6e6a-4e35-bf2f-e1e4cfe752e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f133bae-0bd7-4ba2-a538-99368675b51c", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978794830000, "endTime": 27978794838300}, "additional": {"logType": "info", "children": [], "durationId": "9037a633-8025-4b22-b953-48df08d2a17a", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "0a5c9726-9035-4d66-a612-3f730d730534", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978794855400, "endTime": 27978798655000}, "additional": {"logType": "info", "children": [], "durationId": "4832878e-f34b-4efb-94f0-e94a3e967057", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "65b65554-002c-4c19-9430-edbe21490e77", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978799246600, "endTime": 27978799265500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34688f2b-c797-418f-88e8-a575e71005fe", "logId": "afdda194-7a89-400d-8bd8-4ce643e4193b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afdda194-7a89-400d-8bd8-4ce643e4193b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978799246600, "endTime": 27978799265500}, "additional": {"logType": "info", "children": [], "durationId": "65b65554-002c-4c19-9430-edbe21490e77", "parent": "a7c686d7-b01a-4951-a461-cda824b2ada0"}}, {"head": {"id": "25520e26-547f-452b-a397-ba5262c65ae3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978801402800, "endTime": 27978918333600}, "additional": {"children": ["6851a573-5b00-427a-95e3-b4031e6452fe", "395c9775-5e18-4b18-beea-f158a1fe4151"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34688f2b-c797-418f-88e8-a575e71005fe", "logId": "3dd2ee64-ca81-496f-88c7-01711743ba48"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6851a573-5b00-427a-95e3-b4031e6452fe", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978801404600, "endTime": 27978807169700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "25520e26-547f-452b-a397-ba5262c65ae3", "logId": "4928fa40-ccba-4a47-906d-bde1350cbc6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "395c9775-5e18-4b18-beea-f158a1fe4151", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978807187600, "endTime": 27978918310600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "25520e26-547f-452b-a397-ba5262c65ae3", "logId": "b1a89271-49fe-42f3-9121-4cd94ffa691a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54540675-9c57-4487-834b-fbc5af9cb18f", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978801409900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c602fd6-f6ce-494d-8e4a-51dc7761741d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978807007500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4928fa40-ccba-4a47-906d-bde1350cbc6b", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978801404600, "endTime": 27978807169700}, "additional": {"logType": "info", "children": [], "durationId": "6851a573-5b00-427a-95e3-b4031e6452fe", "parent": "3dd2ee64-ca81-496f-88c7-01711743ba48"}}, {"head": {"id": "3dfba83f-6de2-4cde-b5ba-d16a9ca6520f", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978807202100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcc273c3-0021-4e90-a3f8-f8d71aac9ea2", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978816348600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda4bc8a-a78a-47d0-9128-c06f1966b071", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978816500800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3693b007-f73a-4680-8d48-0c23381b476d", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978816673300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d33b5e8-994e-4d0e-83b6-b1b4422a9400", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978816775200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9659bbfe-b657-47f3-b0cb-031c54979847", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978818521400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c3dbce-ae7b-479d-8cd3-6f10e1acad3b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978826585200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2a09f0-a9ad-4616-b40b-b1642f8e4b99", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978840467300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7345d3ae-7e5c-4769-a6ec-5324549d3790", "name": "Sdk init in 46 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978873682800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb667d6-2b0a-434e-9996-9fbdded043fe", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978873920200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 15}, "markType": "other"}}, {"head": {"id": "dfc2f112-ed22-4e90-8e4c-119c4959b6cd", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978873983200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 15}, "markType": "other"}}, {"head": {"id": "1e92004f-7ccd-4bfc-b7c3-41868b7674a3", "name": "Project task initialization takes 43 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978917892200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4f43abd-e449-4eca-b59d-6bf5c7824d8c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978918108800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9267db74-7aae-4bb6-b3e2-40b001ae6856", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978918187300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0b53cb-e3e3-4943-808b-ba009fa9d9bb", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978918246100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a89271-49fe-42f3-9121-4cd94ffa691a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978807187600, "endTime": 27978918310600}, "additional": {"logType": "info", "children": [], "durationId": "395c9775-5e18-4b18-beea-f158a1fe4151", "parent": "3dd2ee64-ca81-496f-88c7-01711743ba48"}}, {"head": {"id": "3dd2ee64-ca81-496f-88c7-01711743ba48", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978801402800, "endTime": 27978918333600}, "additional": {"logType": "info", "children": ["4928fa40-ccba-4a47-906d-bde1350cbc6b", "b1a89271-49fe-42f3-9121-4cd94ffa691a"], "durationId": "25520e26-547f-452b-a397-ba5262c65ae3", "parent": "a7c686d7-b01a-4951-a461-cda824b2ada0"}}, {"head": {"id": "7fe8af7d-4e1b-4c32-bd45-da79b51bf029", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978919173800, "endTime": 27978919195600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "34688f2b-c797-418f-88e8-a575e71005fe", "logId": "96d25182-548a-4254-bac4-a90bdf00503c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96d25182-548a-4254-bac4-a90bdf00503c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978919173800, "endTime": 27978919195600}, "additional": {"logType": "info", "children": [], "durationId": "7fe8af7d-4e1b-4c32-bd45-da79b51bf029", "parent": "a7c686d7-b01a-4951-a461-cda824b2ada0"}}, {"head": {"id": "a7c686d7-b01a-4951-a461-cda824b2ada0", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978798668400, "endTime": 27978919212100}, "additional": {"logType": "info", "children": ["afdda194-7a89-400d-8bd8-4ce643e4193b", "3dd2ee64-ca81-496f-88c7-01711743ba48", "96d25182-548a-4254-bac4-a90bdf00503c"], "durationId": "34688f2b-c797-418f-88e8-a575e71005fe", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "4c978813-a3e0-4693-81da-b50ca065e02c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978919950500, "endTime": 27978983208900}, "additional": {"children": ["3f4cecf2-b9ff-447d-866c-a5922cd842b6", "77835235-ae34-48ef-b3a3-92e7af9e60c8", "a5f744ba-f709-43d2-a122-caaad425fce1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ab8bd0c-bef1-402e-91b6-96b3941b051d", "logId": "52dfb3d9-4040-4a94-9d32-230d25cfd3f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f4cecf2-b9ff-447d-866c-a5922cd842b6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978926799700, "endTime": 27978926822400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c978813-a3e0-4693-81da-b50ca065e02c", "logId": "ffd2a237-5cc1-4daf-925a-7c841b284409"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ffd2a237-5cc1-4daf-925a-7c841b284409", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978926799700, "endTime": 27978926822400}, "additional": {"logType": "info", "children": [], "durationId": "3f4cecf2-b9ff-447d-866c-a5922cd842b6", "parent": "52dfb3d9-4040-4a94-9d32-230d25cfd3f3"}}, {"head": {"id": "77835235-ae34-48ef-b3a3-92e7af9e60c8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978931105100, "endTime": 27978980047800}, "additional": {"children": ["4eeca933-466a-46e6-8ec5-69d7e6b8fdd4", "6f4a32d6-8703-426e-853c-773b83aa677d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c978813-a3e0-4693-81da-b50ca065e02c", "logId": "ae77439f-9c51-460a-8178-d0aea9f1dc90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4eeca933-466a-46e6-8ec5-69d7e6b8fdd4", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978931108000, "endTime": 27978936409700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77835235-ae34-48ef-b3a3-92e7af9e60c8", "logId": "b036de35-d7c8-40c7-8504-7586d1ec3f45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f4a32d6-8703-426e-853c-773b83aa677d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978936436000, "endTime": 27978980029500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77835235-ae34-48ef-b3a3-92e7af9e60c8", "logId": "a356bf1b-5d87-4049-a1b3-714b76a28ebe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c0bd9f7-9acb-4aa7-b0bd-7e2da01e88be", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978931115400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "924d2fc7-478f-4f57-abff-a86a7c1745b5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978935980100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b036de35-d7c8-40c7-8504-7586d1ec3f45", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978931108000, "endTime": 27978936409700}, "additional": {"logType": "info", "children": [], "durationId": "4eeca933-466a-46e6-8ec5-69d7e6b8fdd4", "parent": "ae77439f-9c51-460a-8178-d0aea9f1dc90"}}, {"head": {"id": "9dd6cd8f-1695-475e-af99-0bfaf0789960", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978936465300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be14d59-1d26-4a8f-994b-bafe9ffb5266", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978964952800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21c430a7-cb50-4327-bb2d-63f98a1dcd7b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978965961500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19bd8a0c-c7d2-4077-87ba-5a27a90c6357", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978967319200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "461f239e-2b75-4f04-ad77-65064d753a16", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978968146600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73538724-afe5-4602-97a6-903174cea33f", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978968426900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d856c926-c729-44aa-bf79-7f44926d673d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978968496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a3bab88-d60e-4e6f-a626-468db4c31971", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978969988200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "618ecb27-5488-4fcd-a4da-0705a7a6f985", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978979220900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a76dbb7f-9b76-4afb-a514-6ca3e28a4887", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978979515100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75cad85c-5205-4bab-a5be-3923129bfd65", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978979715400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddb3ee0e-4b5b-47c4-b28f-149253a0fa66", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978979960400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a356bf1b-5d87-4049-a1b3-714b76a28ebe", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978936436000, "endTime": 27978980029500}, "additional": {"logType": "info", "children": [], "durationId": "6f4a32d6-8703-426e-853c-773b83aa677d", "parent": "ae77439f-9c51-460a-8178-d0aea9f1dc90"}}, {"head": {"id": "ae77439f-9c51-460a-8178-d0aea9f1dc90", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978931105100, "endTime": 27978980047800}, "additional": {"logType": "info", "children": ["b036de35-d7c8-40c7-8504-7586d1ec3f45", "a356bf1b-5d87-4049-a1b3-714b76a28ebe"], "durationId": "77835235-ae34-48ef-b3a3-92e7af9e60c8", "parent": "52dfb3d9-4040-4a94-9d32-230d25cfd3f3"}}, {"head": {"id": "a5f744ba-f709-43d2-a122-caaad425fce1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978983147700, "endTime": 27978983183500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4c978813-a3e0-4693-81da-b50ca065e02c", "logId": "d40fa938-933b-4136-b7d5-56284d1c9162"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d40fa938-933b-4136-b7d5-56284d1c9162", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978983147700, "endTime": 27978983183500}, "additional": {"logType": "info", "children": [], "durationId": "a5f744ba-f709-43d2-a122-caaad425fce1", "parent": "52dfb3d9-4040-4a94-9d32-230d25cfd3f3"}}, {"head": {"id": "52dfb3d9-4040-4a94-9d32-230d25cfd3f3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978919950500, "endTime": 27978983208900}, "additional": {"logType": "info", "children": ["ffd2a237-5cc1-4daf-925a-7c841b284409", "ae77439f-9c51-460a-8178-d0aea9f1dc90", "d40fa938-933b-4136-b7d5-56284d1c9162"], "durationId": "4c978813-a3e0-4693-81da-b50ca065e02c", "parent": "7779a366-55bb-4e09-828e-36567eed3d9f"}}, {"head": {"id": "7779a366-55bb-4e09-828e-36567eed3d9f", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978919228300, "endTime": 27978983233500}, "additional": {"logType": "info", "children": ["52dfb3d9-4040-4a94-9d32-230d25cfd3f3"], "durationId": "1ab8bd0c-bef1-402e-91b6-96b3941b051d", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "d011ee28-ca74-4178-ae77-6fac8e970a68", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979048044000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "965d25ab-af23-4f88-8764-ad82fee2084d", "name": "hvigorfile, resolve hvigorfile dependencies in 169 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979151397900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c845d829-586e-4ff6-85ca-3c847d0c7aeb", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978983247900, "endTime": 27979151529000}, "additional": {"logType": "info", "children": [], "durationId": "303d9833-16a6-4f7b-9fe0-57a30575fc35", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "deedbe64-6242-4bca-9745-06d8bc142b0c", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979152429300, "endTime": 27979152663800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "logId": "f3419fdc-317d-4f14-916e-fa66494716c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9786d0af-69dd-468f-a737-013355ddfe1e", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979152466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3419fdc-317d-4f14-916e-fa66494716c2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979152429300, "endTime": 27979152663800}, "additional": {"logType": "info", "children": [], "durationId": "deedbe64-6242-4bca-9745-06d8bc142b0c", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "93e79fae-4497-4265-950a-039417ee10f8", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979157062400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de58cabe-d49d-42e7-a943-635f344f908f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979173639800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b25040a-a828-4d4d-b706-6ccc47cd10fc", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979152678900, "endTime": 27979174847700}, "additional": {"logType": "info", "children": [], "durationId": "663de782-a36a-458e-8ff9-80b078ce64c8", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "fcf13767-0402-4f14-ad31-763ac80cd28b", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979182607900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c64b98c-940a-4af4-b747-bf026dbbb278", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979182907900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "141aabeb-b6e6-4cd6-b248-1df913247536", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979185310000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f43fe4ca-a1b0-48f7-b22c-f2fac0e151d2", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979185444600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fbef0f5-52bd-4ef3-a513-d766591cb7b5", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979174878400, "endTime": 27979192856500}, "additional": {"logType": "info", "children": [], "durationId": "153cc9c1-c3f5-43f1-a1b2-b0a18995bd53", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "9e855972-82e1-4a54-88ad-c060901d8d34", "name": "Configuration phase cost:399 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979192957500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77befdc0-6e6a-4e35-bf2f-e1e4cfe752e6", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979192890200, "endTime": 27979193123400}, "additional": {"logType": "info", "children": [], "durationId": "0001dc8b-1c01-46fe-8873-c63a30a31171", "parent": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453"}}, {"head": {"id": "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978789189900, "endTime": 27979193165400}, "additional": {"logType": "info", "children": ["0f133bae-0bd7-4ba2-a538-99368675b51c", "0a5c9726-9035-4d66-a612-3f730d730534", "a7c686d7-b01a-4951-a461-cda824b2ada0", "7779a366-55bb-4e09-828e-36567eed3d9f", "c845d829-586e-4ff6-85ca-3c847d0c7aeb", "8b25040a-a828-4d4d-b706-6ccc47cd10fc", "8fbef0f5-52bd-4ef3-a513-d766591cb7b5", "77befdc0-6e6a-4e35-bf2f-e1e4cfe752e6", "f3419fdc-317d-4f14-916e-fa66494716c2"], "durationId": "d412d0d4-c2b8-42e1-8cd7-fc7ebc428fc3", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "a5daca4b-872e-4b79-b1aa-b7487a443444", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979198876200, "endTime": 27979198934200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a29ab284-93ba-4bff-ab1f-7553561340d4", "logId": "ab7fce1f-10f8-4c4b-9023-333c4140e729"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab7fce1f-10f8-4c4b-9023-333c4140e729", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979198876200, "endTime": 27979198934200}, "additional": {"logType": "info", "children": [], "durationId": "a5daca4b-872e-4b79-b1aa-b7487a443444", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "6c93d09e-99cb-4e84-a8c7-47e646246978", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979193191400, "endTime": 27979199023800}, "additional": {"logType": "info", "children": [], "durationId": "aa4b4357-2156-4070-82f0-0bd80656239a", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "473b15dd-1e3e-4179-9113-291541f4ba34", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979199159100, "endTime": 27979199174200}, "additional": {"logType": "info", "children": [], "durationId": "60dc1d40-d82e-42e2-bb32-dff485569627", "parent": "feca2a1c-f9e4-43ae-b683-6283c5d67c96"}}, {"head": {"id": "feca2a1c-f9e4-43ae-b683-6283c5d67c96", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978774059700, "endTime": 27979199208900}, "additional": {"logType": "info", "children": ["8f2f9ac5-75ea-453b-95ca-c6da8cf9dab4", "2eeff1d4-9b5f-42bc-9e8a-b8fffd614453", "6c93d09e-99cb-4e84-a8c7-47e646246978", "473b15dd-1e3e-4179-9113-291541f4ba34", "a73dd354-3bfb-445b-952c-8641f728ab14", "71988500-e538-46ab-939a-1f58b35583b5", "ab7fce1f-10f8-4c4b-9023-333c4140e729"], "durationId": "a29ab284-93ba-4bff-ab1f-7553561340d4"}}, {"head": {"id": "cba90da2-c5c4-42f2-af70-5e5dfe28731b", "name": "Configuration task cost before running: 432 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979199683000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e3d8f3-d939-43c2-bd7e-6e73a42a0472", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979267490200, "endTime": 27979303557800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "be9d69dc-25a1-4ed9-81b2-abb28700b1d9", "logId": "a1e5b8da-8283-43d8-bec7-2f484b002686"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be9d69dc-25a1-4ed9-81b2-abb28700b1d9", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979203783600}, "additional": {"logType": "detail", "children": [], "durationId": "a6e3d8f3-d939-43c2-bd7e-6e73a42a0472"}}, {"head": {"id": "5f43bb18-0d8e-422c-96e6-70c15156f6a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979262126400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "345538a9-b583-4cf2-b822-9473db23a4d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979262490700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856cb078-da9d-4ebd-97c0-51d31f16c0ab", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979267509200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001d505d-4998-40d2-b98b-d5a8f3e11bad", "name": "Incremental task entry:default@PreBuild pre-execution cost: 22 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979302964800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac9176c1-1f32-4a88-86aa-5f16272ff51f", "name": "entry : default@PreBuild cost memory 0.314483642578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979303207800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1e5b8da-8283-43d8-bec7-2f484b002686", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979267490200, "endTime": 27979303557800}, "additional": {"logType": "info", "children": [], "durationId": "a6e3d8f3-d939-43c2-bd7e-6e73a42a0472"}}, {"head": {"id": "5d213953-f1bb-4eac-b905-cd426b6cfba2", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979334094000, "endTime": 27979347346900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6052c568-0f06-4ac0-ad61-b7995898188b", "logId": "1ef37a37-070c-4df9-85d5-a15fbf573917"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6052c568-0f06-4ac0-ad61-b7995898188b", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979318534100}, "additional": {"logType": "detail", "children": [], "durationId": "5d213953-f1bb-4eac-b905-cd426b6cfba2"}}, {"head": {"id": "dea2b5d4-b895-47a6-98cc-9046fb87e594", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979327756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6aa556cb-815f-40f5-9c97-6f88a7495154", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979331055900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "513bf1bd-8c43-4f3c-9289-ef6470118daf", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979334109200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e53de498-276e-4c1d-89e4-daf908723f8f", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 8 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979343122200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "334760e8-eb7c-49dc-9271-5a2d15fc2658", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979346832000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9858638-65f3-47cc-8f25-3cf222e53226", "name": "entry : default@GenerateMetadata cost memory 0.097198486328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979346999900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ef37a37-070c-4df9-85d5-a15fbf573917", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979334094000, "endTime": 27979347346900}, "additional": {"logType": "info", "children": [], "durationId": "5d213953-f1bb-4eac-b905-cd426b6cfba2"}}, {"head": {"id": "0dd5667a-ef6f-4f9f-8cda-0147c3819e1c", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979351837500, "endTime": 27979353222000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "33a7256f-21c4-4283-a6ed-b6dfaec85712", "logId": "be2fcff9-612a-4218-ac0f-f1a677bc77f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33a7256f-21c4-4283-a6ed-b6dfaec85712", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979350768800}, "additional": {"logType": "detail", "children": [], "durationId": "0dd5667a-ef6f-4f9f-8cda-0147c3819e1c"}}, {"head": {"id": "ed0da4a7-e4a5-4c9a-acdf-265775809475", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979351402800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63349d38-6c95-4f4d-8d58-c273874038fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979351587500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6ded67-31e1-4a7d-bb61-ffd5a3875670", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979351938000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d414567-f3b7-4352-ba36-346a42a635c9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979352277600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7363154-67b8-459b-93a6-9bc1192a5bbe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979352568800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b629c2-e072-4286-98c4-333e51bbb193", "name": "entry : default@ConfigureCmake cost memory 0.0367431640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979352950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff5d6ff9-f251-43b9-ad57-f898ff4ffa31", "name": "runTaskFromQueue task cost before running: 586 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979353140200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be2fcff9-612a-4218-ac0f-f1a677bc77f6", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979351837500, "endTime": 27979353222000, "totalTime": 1325900}, "additional": {"logType": "info", "children": [], "durationId": "0dd5667a-ef6f-4f9f-8cda-0147c3819e1c"}}, {"head": {"id": "0bab2eb2-574d-4c16-b0a5-810c3793dc59", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979359691000, "endTime": 27979363617900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "bbce4138-9d56-465d-be7f-15c71328c021", "logId": "a0b222bb-aada-41b3-8de2-dd895701cf83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbce4138-9d56-465d-be7f-15c71328c021", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979357870700}, "additional": {"logType": "detail", "children": [], "durationId": "0bab2eb2-574d-4c16-b0a5-810c3793dc59"}}, {"head": {"id": "a4f8cce8-caf1-4de2-9884-bb5cddb4ea15", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979358486600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3612c41d-b802-4472-a79b-ac8c603fcebd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979358635800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8435832a-3310-4418-a672-cbbf5ae8bdb8", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979359772400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "176486e4-a08d-44ad-9d45-d9d85c92aed2", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979363322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e5299f-7d74-492b-8575-e28b1ea00e50", "name": "entry : default@MergeProfile cost memory 0.107086181640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979363522000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b222bb-aada-41b3-8de2-dd895701cf83", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979359691000, "endTime": 27979363617900}, "additional": {"logType": "info", "children": [], "durationId": "0bab2eb2-574d-4c16-b0a5-810c3793dc59"}}, {"head": {"id": "9e0e412d-250a-460f-b437-fb904da83af6", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979367684300, "endTime": 27979369422900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "88988444-a1b6-4d2e-9f39-67302731edc8", "logId": "71cf15c5-baa9-4988-bd0a-0b7215466481"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88988444-a1b6-4d2e-9f39-67302731edc8", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979366235600}, "additional": {"logType": "detail", "children": [], "durationId": "9e0e412d-250a-460f-b437-fb904da83af6"}}, {"head": {"id": "8a59b504-02c5-4b6a-bbd5-846561e26ea9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979366690400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60aa47f-5be2-4604-98a2-7008048f968d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979366847600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f79bd0e1-7985-48db-9f0a-08a6b732da1b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979367699000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af092055-f40d-46cf-b6f4-7340073c882a", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979368414100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad59f49-eb35-484b-bf7a-f43401b36cf4", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979369256600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f02ce5-04ab-49aa-be92-d5f93b4c1a4c", "name": "entry : default@CreateBuildProfile cost memory 0.1049041748046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979369354200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71cf15c5-baa9-4988-bd0a-0b7215466481", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979367684300, "endTime": 27979369422900}, "additional": {"logType": "info", "children": [], "durationId": "9e0e412d-250a-460f-b437-fb904da83af6"}}, {"head": {"id": "619be58f-d19e-4a24-9604-4d02f756ae3c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979373825300, "endTime": 27979374309300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "2c9fa789-4e0f-4b24-aa95-1024ab551493", "logId": "edaef9b8-a21d-45bc-ad72-4dc5a5cc910f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c9fa789-4e0f-4b24-aa95-1024ab551493", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979371420600}, "additional": {"logType": "detail", "children": [], "durationId": "619be58f-d19e-4a24-9604-4d02f756ae3c"}}, {"head": {"id": "a47cafd5-e181-4d33-9338-76bba3ba2b35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979371916900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77dda466-0cb5-4fbf-b9b4-a7d9baa35690", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979372522900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cba66f3-bf32-41b1-b560-7218b47a9d34", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979373847100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "610d2522-3956-4e73-883c-45af4956f4f1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979374005700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be6bed60-88f0-42cf-8b31-edfc1549da98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979374068200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a0b3af2-6957-4c57-9e38-d30026079ba2", "name": "entry : default@PreCheckSyscap cost memory 0.03696441650390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979374152000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "429ef1cd-7755-4384-9fff-d816c3959ef1", "name": "runTaskFromQueue task cost before running: 607 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979374238200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edaef9b8-a21d-45bc-ad72-4dc5a5cc910f", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979373825300, "endTime": 27979374309300, "totalTime": 438800}, "additional": {"logType": "info", "children": [], "durationId": "619be58f-d19e-4a24-9604-4d02f756ae3c"}}, {"head": {"id": "ce64515c-b868-4321-8ce0-9657e5636336", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979380609400, "endTime": 27979381606900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "541dc042-a836-46d2-b7a5-958365674ac8", "logId": "396dd3e0-c1e2-4c03-919f-7f1f45d3d7d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "541dc042-a836-46d2-b7a5-958365674ac8", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979375958700}, "additional": {"logType": "detail", "children": [], "durationId": "ce64515c-b868-4321-8ce0-9657e5636336"}}, {"head": {"id": "0dab6fc5-30f9-4ed0-99af-e68f44613951", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979376422600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "590ebd59-8d50-4e17-b046-d0632b85abe6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979376535700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "493c54b6-1898-400b-affc-46eee5045316", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979380628600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bece60bc-a627-460b-96a5-f80bd9b75080", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979381102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703fd26d-7691-4287-b7fa-095fe76eb5e6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03926849365234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979381398800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b9dca47-8bac-4bee-a271-5842d3679bd4", "name": "runTaskFromQueue task cost before running: 614 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979381533500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "396dd3e0-c1e2-4c03-919f-7f1f45d3d7d4", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979380609400, "endTime": 27979381606900, "totalTime": 901100}, "additional": {"logType": "info", "children": [], "durationId": "ce64515c-b868-4321-8ce0-9657e5636336"}}, {"head": {"id": "2096f96b-0875-4384-9ae1-7be6356f1a6c", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979391529700, "endTime": 27979393849700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "5d1ae5a5-1078-4efc-aa3c-6e09e5ef469b", "logId": "a2c3e668-2eda-4f57-8aef-1b5ee35e032b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d1ae5a5-1078-4efc-aa3c-6e09e5ef469b", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979384005300}, "additional": {"logType": "detail", "children": [], "durationId": "2096f96b-0875-4384-9ae1-7be6356f1a6c"}}, {"head": {"id": "7814828b-1220-4a17-be73-0c030bd1a2b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979384665600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e852c44f-d00d-4360-b10b-89b9392af366", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979384808700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e4db292-473b-4397-b167-66422a1dd68e", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979391547600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d3fbab-3d71-4f91-9d8e-58dd5cba4733", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979393164900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aab6334-6e1f-467d-bdf1-10109dc6db2b", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979393353100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2662ec4-6d57-486a-b71b-92b0a9cf78fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979393503500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48681ab8-6317-4ede-a502-e4e0bd8d2200", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979393594400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e05a919e-cab4-49af-9924-afe777f18ec9", "name": "entry : default@ProcessIntegratedHsp cost memory 0.119293212890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979393701800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3228f8f-4295-4fcb-a30e-9c7fba2c9cf4", "name": "runTaskFromQueue task cost before running: 626 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979393791500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c3e668-2eda-4f57-8aef-1b5ee35e032b", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979391529700, "endTime": 27979393849700, "totalTime": 2238600}, "additional": {"logType": "info", "children": [], "durationId": "2096f96b-0875-4384-9ae1-7be6356f1a6c"}}, {"head": {"id": "4bec3765-b9f2-48f0-8b3a-1478c9370eef", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397427600, "endTime": 27979397983200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e6f43bbb-5b6b-473d-801d-75616a3b297d", "logId": "6006ef9d-e3a3-4c33-a991-d4162538297c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6f43bbb-5b6b-473d-801d-75616a3b297d", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979396020400}, "additional": {"logType": "detail", "children": [], "durationId": "4bec3765-b9f2-48f0-8b3a-1478c9370eef"}}, {"head": {"id": "1677c76f-3ff7-4534-95e1-cdfdc58de09f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979396573300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e96519c-23e8-4ef4-84dc-03b0dc3dfab1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979396695000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "275d72b5-3c1d-4180-b3da-2f503230caf2", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397441100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06d50a77-8e07-4a56-9e5f-5bbbcbbd02bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6cf815-e326-4871-b477-f6381c12f826", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397677500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9964476-e667-4a21-9e28-ef92dcdaa070", "name": "entry : default@BuildNativeWithCmake cost memory 0.0377960205078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2714b09-75d0-4d27-a8f0-8560f43de205", "name": "runTaskFromQueue task cost before running: 630 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397899900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6006ef9d-e3a3-4c33-a991-d4162538297c", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979397427600, "endTime": 27979397983200, "totalTime": 424300}, "additional": {"logType": "info", "children": [], "durationId": "4bec3765-b9f2-48f0-8b3a-1478c9370eef"}}, {"head": {"id": "0c3b58e8-f8a3-4544-bda8-6355f39daad1", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979400737300, "endTime": 27979403208800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f9d4de07-2846-4292-a2fb-ea324f5b305c", "logId": "41a5c733-344a-4efc-b29a-3dfe55e26e75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9d4de07-2846-4292-a2fb-ea324f5b305c", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979399638000}, "additional": {"logType": "detail", "children": [], "durationId": "0c3b58e8-f8a3-4544-bda8-6355f39daad1"}}, {"head": {"id": "67d433ef-2829-4ece-be66-fcc491d09124", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979399993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fcf1a0b-df31-4a92-b2b7-3e1642c3e787", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979400088900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9db2c57-1634-47ea-b9ac-acc05de20fa8", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979400747900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374c9e9c-7632-4ae0-abd4-5fc912318cb6", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979403000800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "686b41d2-a823-43c8-ac0b-d7f9155e281a", "name": "entry : default@MakePackInfo cost memory 0.14012908935546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979403123000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41a5c733-344a-4efc-b29a-3dfe55e26e75", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979400737300, "endTime": 27979403208800}, "additional": {"logType": "info", "children": [], "durationId": "0c3b58e8-f8a3-4544-bda8-6355f39daad1"}}, {"head": {"id": "99e12b07-f83b-427c-9312-5edd61508ac7", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979407845600, "endTime": 27979410384400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "3225e02a-d3c6-430b-a378-a36069efbfc0", "logId": "84a71a66-b9ec-4a51-99e4-30a22bed4e81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3225e02a-d3c6-430b-a378-a36069efbfc0", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979406107300}, "additional": {"logType": "detail", "children": [], "durationId": "99e12b07-f83b-427c-9312-5edd61508ac7"}}, {"head": {"id": "92bf3def-1d43-4839-a505-6eaea67738cb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979406639000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5444a5a-d2b4-4cc9-a037-3a88a8fcbdd8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979406788000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea35bf4-61cf-4c38-950b-7550c1f27a3f", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979407858100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a5ce2c-7e9f-4db9-bac5-19a631a043a1", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979407984400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "390578be-aa60-4e89-8b04-4af7ee8955d1", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979408510400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89687fda-50d0-4b7d-93a7-9dede4e1990e", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979409825000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "380410fd-f064-4969-95b0-a359424d83b8", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979409961300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "166c3bf7-0892-4323-ba52-61d37dc4e75a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979410061700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e4ff97-0d63-46dd-8fae-9e16997af7ac", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979410114400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337f00fe-5a0d-46c5-975d-ba2383db53e0", "name": "entry : default@SyscapTransform cost memory 0.1790313720703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979410213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7ff1e4-9c4c-4989-896d-cab05cf6e9d1", "name": "runTaskFromQueue task cost before running: 643 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979410314100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a71a66-b9ec-4a51-99e4-30a22bed4e81", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979407845600, "endTime": 27979410384400, "totalTime": 2428700}, "additional": {"logType": "info", "children": [], "durationId": "99e12b07-f83b-427c-9312-5edd61508ac7"}}, {"head": {"id": "2718f867-4c2f-4ec4-a38f-02ee312dbba6", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979413895800, "endTime": 27979414893000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "07730338-d51d-4cde-b0d9-f2b200e6b6af", "logId": "fc2b19f2-6572-4624-b4f2-460910b2caf8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07730338-d51d-4cde-b0d9-f2b200e6b6af", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979411998800}, "additional": {"logType": "detail", "children": [], "durationId": "2718f867-4c2f-4ec4-a38f-02ee312dbba6"}}, {"head": {"id": "aa1b9c54-e599-409e-903d-da27dfee590a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979412459300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fa74e8-0f6d-4ba3-800c-54be3fb9d3da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979412580600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d1dce6-70db-44cc-a60d-7472b1d6340d", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979413910900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "581dde83-f329-4351-b805-688001d106ff", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979414679300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93326a4-906b-43f2-9efc-1f790fd9e5a6", "name": "entry : default@ProcessProfile cost memory 0.06061553955078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979414791000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc2b19f2-6572-4624-b4f2-460910b2caf8", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979413895800, "endTime": 27979414893000}, "additional": {"logType": "info", "children": [], "durationId": "2718f867-4c2f-4ec4-a38f-02ee312dbba6"}}, {"head": {"id": "a9f91c8c-e458-403e-9846-a6ae72629257", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979418878700, "endTime": 27979426107600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0427c53e-e3d9-4081-b6fd-5491bcbb93ec", "logId": "73fcc0f3-0b47-4603-bfe7-a120449b1078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0427c53e-e3d9-4081-b6fd-5491bcbb93ec", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979416692300}, "additional": {"logType": "detail", "children": [], "durationId": "a9f91c8c-e458-403e-9846-a6ae72629257"}}, {"head": {"id": "7c5343f4-5eb2-48a0-a844-a17297c50825", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979417133900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b252502e-5a88-4c06-b8bb-fae974b529d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979417273600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "302aecbe-faea-42fb-bdfd-6fafa11713ec", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979418890000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602e9fa4-423d-4a42-8d45-def10008f2d1", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979425863100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3225403-0d67-4280-be19-2a5811da5687", "name": "entry : default@ProcessRouterMap cost memory 0.33205413818359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979426036200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73fcc0f3-0b47-4603-bfe7-a120449b1078", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979418878700, "endTime": 27979426107600}, "additional": {"logType": "info", "children": [], "durationId": "a9f91c8c-e458-403e-9846-a6ae72629257"}}, {"head": {"id": "1026887e-bc7b-44ae-9eb3-ffbf3baac887", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979432145700, "endTime": 27979433386900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2e4ab403-cb40-417e-a89c-b0c7366bb199", "logId": "2b0ba7a8-b78e-43b3-9395-f6755004c55c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e4ab403-cb40-417e-a89c-b0c7366bb199", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979430569900}, "additional": {"logType": "detail", "children": [], "durationId": "1026887e-bc7b-44ae-9eb3-ffbf3baac887"}}, {"head": {"id": "298f9522-a151-404c-8784-2f6e0017d670", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979431107800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8bb4389-4ece-4c73-9295-09a088b9c13e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979431217900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cd474dd-c9a9-4e84-8bba-9984c8726a49", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979432160400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3616562b-17c9-46a5-8d5d-ca66be985b1f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979432320500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0cfe69-a31e-46ec-ac45-59d4ffdb9f7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979432394400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b069f560-8aeb-4884-b645-da640a079cff", "name": "entry : default@BuildNativeWithNinja cost memory 0.05739593505859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979433110100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf635838-28d0-4250-a29e-0cbfc46f22af", "name": "runTaskFromQueue task cost before running: 666 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979433250600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b0ba7a8-b78e-43b3-9395-f6755004c55c", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979432145700, "endTime": 27979433386900, "totalTime": 1074200}, "additional": {"logType": "info", "children": [], "durationId": "1026887e-bc7b-44ae-9eb3-ffbf3baac887"}}, {"head": {"id": "0c298b1f-a616-4800-a45a-98b0c95c4226", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979440870300, "endTime": 27979446588600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "db631c93-3111-444c-a2c4-66fceaa0889b", "logId": "445c3056-2d51-42d1-a299-ad956cd59f6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db631c93-3111-444c-a2c4-66fceaa0889b", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979435779800}, "additional": {"logType": "detail", "children": [], "durationId": "0c298b1f-a616-4800-a45a-98b0c95c4226"}}, {"head": {"id": "91c92b1a-39ec-4e08-93fa-a6668e8e42c5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979436141800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f562a4f-6620-451a-9028-eb6212799a0c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979436337200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af8dbd3-6dc4-44a4-be0b-a3bc5c40400c", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979437500000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b298f9c-d899-4de0-8739-c2f7ee65cb85", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979442653800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "533c3b10-d4f6-437e-a880-d5ec3a1a04f3", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979444731900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d1013b-0a35-4b84-9aa4-801f41a46563", "name": "entry : default@ProcessResource cost memory 0.1704864501953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979444887500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "445c3056-2d51-42d1-a299-ad956cd59f6c", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979440870300, "endTime": 27979446588600}, "additional": {"logType": "info", "children": [], "durationId": "0c298b1f-a616-4800-a45a-98b0c95c4226"}}, {"head": {"id": "99d971c1-96d1-4cd3-a523-3c770492a10a", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979456947800, "endTime": 27979470223100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "d037b011-b240-49bb-9500-54f1763598ca", "logId": "5595a38c-93ae-487d-8229-afa216531b82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d037b011-b240-49bb-9500-54f1763598ca", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979451085000}, "additional": {"logType": "detail", "children": [], "durationId": "99d971c1-96d1-4cd3-a523-3c770492a10a"}}, {"head": {"id": "c668260f-085c-487b-a86b-08e647cc9350", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979451508900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d81b94e-e29c-4c70-8885-b764d4848fb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979451620200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4818e2a3-4086-4a19-b693-c84f56cae771", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979456970100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a413fbb2-3606-40ca-bf40-dd0ae0148eac", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979469976200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15aa67ae-ece2-48f8-b8c4-ea3043f548e0", "name": "entry : default@GenerateLoaderJson cost memory 0.7652130126953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979470143500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5595a38c-93ae-487d-8229-afa216531b82", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979456947800, "endTime": 27979470223100}, "additional": {"logType": "info", "children": [], "durationId": "99d971c1-96d1-4cd3-a523-3c770492a10a"}}, {"head": {"id": "41d95a01-672d-4859-9813-1b5719281d16", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979478370600, "endTime": 27979481623600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5af4013b-b361-4b7c-aae6-533cdd657167", "logId": "fbbd753f-3af3-4992-bc02-3ac21110731c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5af4013b-b361-4b7c-aae6-533cdd657167", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979477163900}, "additional": {"logType": "detail", "children": [], "durationId": "41d95a01-672d-4859-9813-1b5719281d16"}}, {"head": {"id": "ca1a201b-e74c-48fb-98fd-627db2d0bd0f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979477603400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b501b1e9-6990-4268-a77d-83aea78479f3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979477711700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92881c68-9c5e-45c6-b452-ab520367d8b6", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979478385300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c0e2a0-8f4d-4578-aec0-95aa1995dc0f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979480478900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d271f878-6b07-4650-8c66-333b1d0639b6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979480581400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e8e80ff-18ec-406a-9089-ba94ed018029", "name": "entry : default@ProcessLibs cost memory 0.12625885009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979481427800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5269096d-c2c5-456b-bceb-cb7ced0c37c1", "name": "runTaskFromQueue task cost before running: 714 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979481556300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbbd753f-3af3-4992-bc02-3ac21110731c", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979478370600, "endTime": 27979481623600, "totalTime": 3161900}, "additional": {"logType": "info", "children": [], "durationId": "41d95a01-672d-4859-9813-1b5719281d16"}}, {"head": {"id": "79f8b88b-5728-427d-a874-508f7dbcd37a", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979487696000, "endTime": 27979513336900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c1b64164-fb91-4ca5-9819-4d3f269cea8f", "logId": "9625033d-3c53-42b4-89d3-43e7e2827b2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1b64164-fb91-4ca5-9819-4d3f269cea8f", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979483942700}, "additional": {"logType": "detail", "children": [], "durationId": "79f8b88b-5728-427d-a874-508f7dbcd37a"}}, {"head": {"id": "35d69bd1-9666-4c0a-b22f-8a59b76362ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979484270800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8230d4e-30c4-4c09-a725-9218c8428293", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979484412900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f816fc1e-e3e6-472c-b44e-2f5a9fdbff2a", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979485135900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95d5fed3-2dab-4143-8f65-78a892580916", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979487725400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3538ea75-7d34-4aba-87b4-13cb20b09cdd", "name": "Incremental task entry:default@CompileResource pre-execution cost: 23 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979513090600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0db3584-2753-4820-80b2-bae6e3b4b917", "name": "entry : default@CompileResource cost memory -4.412139892578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979513223700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9625033d-3c53-42b4-89d3-43e7e2827b2a", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979487696000, "endTime": 27979513336900}, "additional": {"logType": "info", "children": [], "durationId": "79f8b88b-5728-427d-a874-508f7dbcd37a"}}, {"head": {"id": "828b4c7d-65d3-4367-9906-425262363ad1", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979518249400, "endTime": 27979519859500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0a0bb72a-7087-4c49-8672-a8b20ff2b5fa", "logId": "1fbfbc93-52de-403a-a11d-fdf3a6df73ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a0bb72a-7087-4c49-8672-a8b20ff2b5fa", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979515686600}, "additional": {"logType": "detail", "children": [], "durationId": "828b4c7d-65d3-4367-9906-425262363ad1"}}, {"head": {"id": "5585d126-3cca-4c27-afdb-a1208c7668f3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979516100500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a01c04-6bcb-4d59-abba-d8d469056673", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979516205100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ababd88-8d2c-4f46-a4ff-626930f3a73e", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979518360100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5feba752-d401-43d2-8f80-ed8860e1c47f", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979518698800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3370691a-63d7-4882-ad25-25909787bd9b", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979519669300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c32add5-de82-4dc1-bb95-a91d0a628759", "name": "entry : default@DoNativeStrip cost memory 0.0776519775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979519783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbfbc93-52de-403a-a11d-fdf3a6df73ef", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979518249400, "endTime": 27979519859500}, "additional": {"logType": "info", "children": [], "durationId": "828b4c7d-65d3-4367-9906-425262363ad1"}}, {"head": {"id": "fc44ad7d-901d-4d1f-8f25-1ee58218fd71", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979531906200, "endTime": 27981671698400}, "additional": {"children": ["917afe22-463f-4d30-b1cf-6cdbab698abc", "a51c5a26-6f9e-4007-9e1f-8618a7d52fff"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "3c4e5dbd-a880-4829-be4b-364c3a2fc094", "logId": "b901ea51-24e0-4dd3-bb70-ecf5f8d5c1df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3c4e5dbd-a880-4829-be4b-364c3a2fc094", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979522425900}, "additional": {"logType": "detail", "children": [], "durationId": "fc44ad7d-901d-4d1f-8f25-1ee58218fd71"}}, {"head": {"id": "365cc8d1-d290-4d83-9226-876f26e9fbf8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979523343200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386c215c-9886-43f9-b733-651594a5e187", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979523511700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e37cf2c2-bb16-4851-af48-99c4bb97e021", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979531920200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcfb5ea-13e2-49cf-8d4e-dac922274172", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979552579800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eca92dc0-079f-40d6-a816-9a06358dbc61", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979552724300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbc6361-7697-4f34-98aa-73e38d85dd7e", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979577130100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ba5dfe-96c0-4bf0-a69a-e229213d03ec", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979577799400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d257004b-ed8a-4952-9b55-9aa5bc5225ac", "name": "default@CompileArkTS work[39] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979579046500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917afe22-463f-4d30-b1cf-6cdbab698abc", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27979800110100, "endTime": 27981665498000}, "additional": {"children": ["d971fa03-0ad9-4c11-a627-a0888dd687d1", "3a06f8b0-e2f3-4b08-ac70-1be03e708d51", "a6da1a7b-a167-412b-805c-f7171cc65dab", "aa8abef7-c2ad-4079-8e74-64ffd0cf61ba", "ed7662f2-5a1f-4109-964e-b5a89c6597c8", "f5029732-076c-45f7-b6a8-c2fc02d859c6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "fc44ad7d-901d-4d1f-8f25-1ee58218fd71", "logId": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d014a88-49ee-4c29-8b91-b9aa750c6d4e", "name": "default@CompileArkTS work[39] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979579919500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebf07210-5394-4a3c-8ded-72165d34b51f", "name": "default@CompileArkTS work[39] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979580024800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "007dcb81-9d8a-4b66-9f8a-18f4a0247958", "name": "CopyResources startTime: 27979580088500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979580091300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff910bc8-d55b-47cc-abe9-7ca857e0d7d1", "name": "default@CompileArkTS work[40] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979580172500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51c5a26-6f9e-4007-9e1f-8618a7d52fff", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27981029976400, "endTime": 27981044980200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "fc44ad7d-901d-4d1f-8f25-1ee58218fd71", "logId": "aba4f53e-6595-47c4-a3a5-02c583b5f0b2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f27c985-f016-43f9-af2a-1b1e5d0ac9ae", "name": "default@CompileArkTS work[40] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979581226900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0330d91f-f59e-41f9-a57f-1ab680819e78", "name": "default@CompileArkTS work[40] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979581339700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa61864-e3f2-41f8-b9d1-599f78d0127e", "name": "entry : default@CompileArkTS cost memory 1.6032867431640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979581695900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462ee84e-7851-401f-b07e-45bcf7234f22", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979592139200, "endTime": 27979595546300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0a66c150-a35d-4c81-886a-5e9229b1922f", "logId": "e2ca9d42-d8de-45e8-ab3d-65a1486bc92b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a66c150-a35d-4c81-886a-5e9229b1922f", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979584049300}, "additional": {"logType": "detail", "children": [], "durationId": "462ee84e-7851-401f-b07e-45bcf7234f22"}}, {"head": {"id": "652cd6fa-66e4-4a33-8fe1-d4b6f676f662", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979584487000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec7ee0c-b030-4c39-ae70-7a8fd911ae77", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979584594300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31d2006f-9b6a-4f5b-93fd-6a69854af772", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979592155600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48fd30c5-9e12-4c9b-b33e-50dd558255bd", "name": "entry : default@BuildJS cost memory 0.12842559814453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979595245900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6896e602-b3c0-4c87-af6f-cf3660becbfe", "name": "runTaskFromQueue task cost before running: 828 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979595460400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ca9d42-d8de-45e8-ab3d-65a1486bc92b", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979592139200, "endTime": 27979595546300, "totalTime": 3268900}, "additional": {"logType": "info", "children": [], "durationId": "462ee84e-7851-401f-b07e-45bcf7234f22"}}, {"head": {"id": "af35624e-9d82-4dbc-a1ce-1951fe39ca05", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979601011500, "endTime": 27979602939400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c383c1a1-01ef-4d83-966a-f4391129b9c3", "logId": "f875d352-5b5e-4050-899b-6df41d4a1159"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c383c1a1-01ef-4d83-966a-f4391129b9c3", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979597725400}, "additional": {"logType": "detail", "children": [], "durationId": "af35624e-9d82-4dbc-a1ce-1951fe39ca05"}}, {"head": {"id": "0fcb225c-f364-4637-9a7e-4127746a2dc9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979598269200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5657fc-ceb9-4ece-bfcc-1904e313d921", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979598388300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89e063f-a918-4c20-a36a-2086767e6047", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979601027500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364ff3bf-be72-4668-ae7a-ee29227ef95a", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979601566200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d933476-c00a-492b-9bf5-e786de8db106", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979602617300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59e37afc-6222-45dd-a476-8ae22657ad0f", "name": "entry : default@CacheNativeLibs cost memory 0.099456787109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979602737100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f875d352-5b5e-4050-899b-6df41d4a1159", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979601011500, "endTime": 27979602939400}, "additional": {"logType": "info", "children": [], "durationId": "af35624e-9d82-4dbc-a1ce-1951fe39ca05"}}, {"head": {"id": "ce7b27a6-dcd9-470a-beec-57e097043da5", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979799456500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6f3c2f-0181-4f39-84a4-54ec6943b881", "name": "default@CompileArkTS work[39] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979799811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44a5917b-e4a5-437e-adc7-d0bbe562a8cc", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979799904200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e82488d-04f8-49ca-8021-269b52b6f519", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979799957700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bcf95de-172f-49b7-a124-6630e530491d", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979800197600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f291be4-6805-4b48-aaae-7e89b14ea32d", "name": "default@CompileArkTS work[40] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979802121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b5a7188-7d4a-4ba2-a00b-c9e03ea3b35b", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981045176000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "012a8f7b-b94e-4698-a786-d43ac5cdb766", "name": "CopyResources is end, endTime: 27981045609100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981045616600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc0fff5-282f-4158-9f9a-dd0f7f32c96c", "name": "default@CompileArkTS work[40] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981045902000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba4f53e-6595-47c4-a3a5-02c583b5f0b2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 27981029976400, "endTime": 27981044980200}, "additional": {"logType": "info", "children": [], "durationId": "a51c5a26-6f9e-4007-9e1f-8618a7d52fff", "parent": "b901ea51-24e0-4dd3-bb70-ecf5f8d5c1df"}}, {"head": {"id": "44381c0b-97be-4e96-af48-c04cc3a6f9c1", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981275419000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a3eb2b4-19aa-426e-86fb-702aaca5a018", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981665719600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d971fa03-0ad9-4c11-a627-a0888dd687d1", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27979800352100, "endTime": 27979804953000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "917afe22-463f-4d30-b1cf-6cdbab698abc", "logId": "a05575c4-6220-429b-9236-3a3ec61c1dba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a05575c4-6220-429b-9236-3a3ec61c1dba", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979800352100, "endTime": 27979804953000}, "additional": {"logType": "info", "children": [], "durationId": "d971fa03-0ad9-4c11-a627-a0888dd687d1", "parent": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}}, {"head": {"id": "3a06f8b0-e2f3-4b08-ac70-1be03e708d51", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27979804973100, "endTime": 27979805157200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "917afe22-463f-4d30-b1cf-6cdbab698abc", "logId": "14b71fa8-354c-4cf7-8e3e-1944db6d8bce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14b71fa8-354c-4cf7-8e3e-1944db6d8bce", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979804973100, "endTime": 27979805157200}, "additional": {"logType": "info", "children": [], "durationId": "3a06f8b0-e2f3-4b08-ac70-1be03e708d51", "parent": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}}, {"head": {"id": "a6da1a7b-a167-412b-805c-f7171cc65dab", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27979805170400, "endTime": 27979805205800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "917afe22-463f-4d30-b1cf-6cdbab698abc", "logId": "085f9168-8921-47a4-b99a-856bf3a3b9e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "085f9168-8921-47a4-b99a-856bf3a3b9e4", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979805170400, "endTime": 27979805205800}, "additional": {"logType": "info", "children": [], "durationId": "a6da1a7b-a167-412b-805c-f7171cc65dab", "parent": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}}, {"head": {"id": "aa8abef7-c2ad-4079-8e74-64ffd0cf61ba", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27979805227500, "endTime": 27981550686400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "917afe22-463f-4d30-b1cf-6cdbab698abc", "logId": "b263bdcc-86d2-4e23-b71f-0334ea775c5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b263bdcc-86d2-4e23-b71f-0334ea775c5e", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979805227500, "endTime": 27981550686400}, "additional": {"logType": "info", "children": [], "durationId": "aa8abef7-c2ad-4079-8e74-64ffd0cf61ba", "parent": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}}, {"head": {"id": "ed7662f2-5a1f-4109-964e-b5a89c6597c8", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981550736000, "endTime": 27981585780800}, "additional": {"children": ["1863fc42-10f2-4d9c-b542-9696cf1b39f6", "d116f949-de84-4e56-a968-9199e4e3d2f5", "7f39ae8d-38d6-49b8-876c-3df4af7b2217"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "917afe22-463f-4d30-b1cf-6cdbab698abc", "logId": "6bbfdc3e-c7c7-4b20-aea6-a0794d82c71e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bbfdc3e-c7c7-4b20-aea6-a0794d82c71e", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981550736000, "endTime": 27981585780800}, "additional": {"logType": "info", "children": ["5059db9d-97fc-4754-8d56-877e7a6252b6", "8fe0d253-8892-4b88-b598-1e16adaf1f3b", "1794b190-2eaf-43d3-8ec8-15afeaafdd12"], "durationId": "ed7662f2-5a1f-4109-964e-b5a89c6597c8", "parent": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}}, {"head": {"id": "1863fc42-10f2-4d9c-b542-9696cf1b39f6", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981550775100, "endTime": 27981550848200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ed7662f2-5a1f-4109-964e-b5a89c6597c8", "logId": "5059db9d-97fc-4754-8d56-877e7a6252b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5059db9d-97fc-4754-8d56-877e7a6252b6", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981550775100, "endTime": 27981550848200}, "additional": {"logType": "info", "children": [], "durationId": "1863fc42-10f2-4d9c-b542-9696cf1b39f6", "parent": "6bbfdc3e-c7c7-4b20-aea6-a0794d82c71e"}}, {"head": {"id": "d116f949-de84-4e56-a968-9199e4e3d2f5", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981550860900, "endTime": 27981556185600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ed7662f2-5a1f-4109-964e-b5a89c6597c8", "logId": "8fe0d253-8892-4b88-b598-1e16adaf1f3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8fe0d253-8892-4b88-b598-1e16adaf1f3b", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981550860900, "endTime": 27981556185600}, "additional": {"logType": "info", "children": [], "durationId": "d116f949-de84-4e56-a968-9199e4e3d2f5", "parent": "6bbfdc3e-c7c7-4b20-aea6-a0794d82c71e"}}, {"head": {"id": "7f39ae8d-38d6-49b8-876c-3df4af7b2217", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981556196900, "endTime": 27981585761300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ed7662f2-5a1f-4109-964e-b5a89c6597c8", "logId": "1794b190-2eaf-43d3-8ec8-15afeaafdd12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1794b190-2eaf-43d3-8ec8-15afeaafdd12", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981556196900, "endTime": 27981585761300}, "additional": {"logType": "info", "children": [], "durationId": "7f39ae8d-38d6-49b8-876c-3df4af7b2217", "parent": "6bbfdc3e-c7c7-4b20-aea6-a0794d82c71e"}}, {"head": {"id": "f5029732-076c-45f7-b6a8-c2fc02d859c6", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981585839600, "endTime": 27981665324900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "917afe22-463f-4d30-b1cf-6cdbab698abc", "logId": "6559a976-62a9-4a35-9107-59c0d8b903f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6559a976-62a9-4a35-9107-59c0d8b903f4", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981585839600, "endTime": 27981665324900}, "additional": {"logType": "info", "children": [], "durationId": "f5029732-076c-45f7-b6a8-c2fc02d859c6", "parent": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd"}}, {"head": {"id": "59925964-5abf-4bad-b4e6-f7a4af75e2b6", "name": "default@CompileArkTS work[39] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981671506000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27979800110100, "endTime": 27981665498000}, "additional": {"logType": "info", "children": ["a05575c4-6220-429b-9236-3a3ec61c1dba", "14b71fa8-354c-4cf7-8e3e-1944db6d8bce", "085f9168-8921-47a4-b99a-856bf3a3b9e4", "b263bdcc-86d2-4e23-b71f-0334ea775c5e", "6bbfdc3e-c7c7-4b20-aea6-a0794d82c71e", "6559a976-62a9-4a35-9107-59c0d8b903f4"], "durationId": "917afe22-463f-4d30-b1cf-6cdbab698abc", "parent": "b901ea51-24e0-4dd3-bb70-ecf5f8d5c1df"}}, {"head": {"id": "b901ea51-24e0-4dd3-bb70-ecf5f8d5c1df", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27979531906200, "endTime": 27981671698400, "totalTime": 1915287800}, "additional": {"logType": "info", "children": ["c5d7e1a3-b5ec-4fc3-9ad4-975ce6aaf8dd", "aba4f53e-6595-47c4-a3a5-02c583b5f0b2"], "durationId": "fc44ad7d-901d-4d1f-8f25-1ee58218fd71"}}, {"head": {"id": "863106ed-c7b2-4a21-9d69-c02c7ee26101", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981678337900, "endTime": 27981679465200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "a942bcbc-3924-44a0-968b-38457d3c3e3b", "logId": "9c235fce-8b17-4b6f-93c6-0af2623dba13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a942bcbc-3924-44a0-968b-38457d3c3e3b", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981676016500}, "additional": {"logType": "detail", "children": [], "durationId": "863106ed-c7b2-4a21-9d69-c02c7ee26101"}}, {"head": {"id": "6f793db9-0016-4a86-933d-928cbd900417", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981676998200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3602b003-8aaa-4bbe-9c55-b151058d26db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981677119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "783ac6d5-b7bd-4aeb-8414-41da8cd8bdc8", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981678352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff51057d-5498-420e-a750-a7b9c166fdbd", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981678592400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6369d93d-1dea-49b8-b067-c18d64f2a073", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981679264700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "786eeecf-ed96-4799-9958-d0b079a805da", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07538604736328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981679389000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c235fce-8b17-4b6f-93c6-0af2623dba13", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981678337900, "endTime": 27981679465200}, "additional": {"logType": "info", "children": [], "durationId": "863106ed-c7b2-4a21-9d69-c02c7ee26101"}}, {"head": {"id": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981691713600, "endTime": 27982230936100}, "additional": {"children": ["b9676222-a99c-4cbd-9bc2-1fdcde5d88c8", "bf40d1c7-3440-4d87-8962-3d051f918d9e", "37faac2b-a0d8-4401-bc50-59aaca3ff72f"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "10b7a94b-72db-444a-a4bd-734d16f4ea11", "logId": "6a50ddaa-29de-4ff8-8625-630fa44a602c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "10b7a94b-72db-444a-a4bd-734d16f4ea11", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981681508000}, "additional": {"logType": "detail", "children": [], "durationId": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff"}}, {"head": {"id": "2bf827ee-a8c6-4b11-afae-d04fc8d3a8a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981681856500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd53f480-10a4-4f36-8431-e3dfb0ddf1f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981681958100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9530894-0535-4cf0-9216-91a29dab849b", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981691727500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8514cc55-9d55-4310-a387-397d17c13587", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981702629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1908f627-083e-4088-a11f-ea36bcc35c2e", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981702770500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee53d52f-15b4-49cf-9358-a9bb55b36e32", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981702867600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd260662-a485-4da7-9278-e221e20f9f48", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981702922800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9676222-a99c-4cbd-9bc2-1fdcde5d88c8", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981703724700, "endTime": 27981706267800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff", "logId": "5261d909-9d9a-472e-94e8-16aecf448cc4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b001354-c589-4372-9b90-c18e3aa00998", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981705707100}, "additional": {"logType": "debug", "children": [], "durationId": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff"}}, {"head": {"id": "5261d909-9d9a-472e-94e8-16aecf448cc4", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981703724700, "endTime": 27981706267800}, "additional": {"logType": "info", "children": [], "durationId": "b9676222-a99c-4cbd-9bc2-1fdcde5d88c8", "parent": "6a50ddaa-29de-4ff8-8625-630fa44a602c"}}, {"head": {"id": "bf40d1c7-3440-4d87-8962-3d051f918d9e", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981707735600, "endTime": 27981709953100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff", "logId": "0e15f4a6-fd4b-4528-820e-0f0e013283d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8210f7b8-f64a-4dc4-8d14-42f1d702b2d0", "name": "default@PackageHap work[41] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981708889400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37faac2b-a0d8-4401-bc50-59aaca3ff72f", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981783608000, "endTime": 27982230554100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff", "logId": "7fed7eed-eb2f-4c69-9fd4-08b366f3f33f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74fd7dc6-5ba3-493d-9682-faca36336322", "name": "default@PackageHap work[41] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981709756700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18e9836e-990f-4ed9-9d18-bf8526c96afd", "name": "default@PackageHap work[41] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981709886400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e15f4a6-fd4b-4528-820e-0f0e013283d2", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981707735600, "endTime": 27981709953100}, "additional": {"logType": "info", "children": [], "durationId": "bf40d1c7-3440-4d87-8962-3d051f918d9e", "parent": "6a50ddaa-29de-4ff8-8625-630fa44a602c"}}, {"head": {"id": "76f8d714-4a0b-4d2d-9f43-6c5d24a5bfb8", "name": "entry : default@PackageHap cost memory 1.258056640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981713838000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9bd256-6e84-4f3c-91b4-d0cb85850c54", "name": "default@PackageHap work[41] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981783558500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a097b82b-d861-438f-836a-5f3452597af2", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981814435700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "968f8c15-e73f-446c-89bb-faefe72291f9", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981814577500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a34066-beb5-44ba-8693-1adfc663e326", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981814639300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47829c65-ecfa-47d8-b290-5e0bc272be60", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981814689800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d004c2ef-7d82-4b5c-b743-8e03eec95f2e", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981814736200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a3a2bb-c6f1-4201-b6d2-0e4a37dda53a", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981814822500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d95cf44-9a30-411c-abad-20c73e9c6658", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982230644200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1aa963f4-41a0-4546-a81c-6a873411674f", "name": "default@PackageHap work[41] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982230838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fed7eed-eb2f-4c69-9fd4-08b366f3f33f", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 27981783608000, "endTime": 27982230554100}, "additional": {"logType": "info", "children": [], "durationId": "37faac2b-a0d8-4401-bc50-59aaca3ff72f", "parent": "6a50ddaa-29de-4ff8-8625-630fa44a602c"}}, {"head": {"id": "6a50ddaa-29de-4ff8-8625-630fa44a602c", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27981691713600, "endTime": 27982230936100, "totalTime": 469204300}, "additional": {"logType": "info", "children": ["5261d909-9d9a-472e-94e8-16aecf448cc4", "0e15f4a6-fd4b-4528-820e-0f0e013283d2", "7fed7eed-eb2f-4c69-9fd4-08b366f3f33f"], "durationId": "67da7ad6-e637-4d38-9ddd-84b0dadc53ff"}}, {"head": {"id": "3fd50e47-49e7-4752-8efc-65dcba7f3e44", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982237805600, "endTime": 27982241345900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "eb70b611-7bd4-4d55-818e-e39505256749", "logId": "2cbd1dd4-dc11-4128-8c3f-fe794eb1622d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb70b611-7bd4-4d55-818e-e39505256749", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982234789600}, "additional": {"logType": "detail", "children": [], "durationId": "3fd50e47-49e7-4752-8efc-65dcba7f3e44"}}, {"head": {"id": "f040a6d0-8df7-478b-8b6f-9eccce6ceed9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982235166300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afd55835-bf38-42b1-bd4e-c169730695dc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982235269500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7f60b2-8d71-4684-a222-e97f89f35de1", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982237826100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36bd83f2-f22c-42a5-94a3-42656c12e698", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982238227100}, "additional": {"logType": "warn", "children": [], "durationId": "3fd50e47-49e7-4752-8efc-65dcba7f3e44"}}, {"head": {"id": "aa4c272f-64da-4d91-8f00-4c4607563c77", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982239117500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c203cb06-31eb-43c8-b04a-0c99e97cf476", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982239392000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "314a4bf1-8ff8-46ca-9b54-b2da45636a56", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982239548600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2805228-2744-4cd1-ab94-e94a818ea636", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982240567000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e20732f-bf14-43f4-9096-5e5fcc75ed8f", "name": "entry : default@SignHap cost memory 0.1181182861328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982241092800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdfce474-1223-413f-90f6-f0192f3793c4", "name": "runTaskFromQueue task cost before running: 3 s 474 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982241232300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cbd1dd4-dc11-4128-8c3f-fe794eb1622d", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982237805600, "endTime": 27982241345900, "totalTime": 3408400}, "additional": {"logType": "info", "children": [], "durationId": "3fd50e47-49e7-4752-8efc-65dcba7f3e44"}}, {"head": {"id": "9d8d7f1c-d763-4fe5-8759-b86758dc4dad", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982246258800, "endTime": 27982253038100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "902ee328-35db-4502-aa53-27dc4d6b9ca8", "logId": "67a68f89-edbe-4b59-90ee-aab47a6219b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "902ee328-35db-4502-aa53-27dc4d6b9ca8", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982244497200}, "additional": {"logType": "detail", "children": [], "durationId": "9d8d7f1c-d763-4fe5-8759-b86758dc4dad"}}, {"head": {"id": "3ffdb541-446a-41ea-bdb8-270b3549ed82", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982244969300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4180a5a4-219b-47be-b0e2-69d606a04d9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982245086000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c642bd29-6e30-457b-a793-5e9154b10539", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982246271300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f8113f-f7cc-4e33-ad37-d39cf5b14a31", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982252559800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8ddf42-0975-4e58-b2b7-339032086198", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982252687400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d12f2b-5049-4bb2-9a3c-5f868d8ce80b", "name": "entry : default@CollectDebugSymbol cost memory 0.24078369140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982252775300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3133591b-ad32-4cc4-86c3-1af55129106a", "name": "runTaskFromQueue task cost before running: 3 s 486 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982252955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67a68f89-edbe-4b59-90ee-aab47a6219b5", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982246258800, "endTime": 27982253038100, "totalTime": 6663300}, "additional": {"logType": "info", "children": [], "durationId": "9d8d7f1c-d763-4fe5-8759-b86758dc4dad"}}, {"head": {"id": "f8338658-9de5-4b81-9824-135d6b9947b9", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982259124100, "endTime": 27982259611000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "88257c2f-3b5a-4a99-98e7-52271d50de1a", "logId": "3231d4cc-12f4-4ff8-989d-7378f5a5b447"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88257c2f-3b5a-4a99-98e7-52271d50de1a", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982259065000}, "additional": {"logType": "detail", "children": [], "durationId": "f8338658-9de5-4b81-9824-135d6b9947b9"}}, {"head": {"id": "f0382885-4810-4fbf-ad5e-a73e9c582e27", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982259133600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2972f81e-1d29-494a-a23b-a8fd8e34bd98", "name": "entry : assembleHap cost memory 0.0115966796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982259454900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3476c29-058b-46e9-8aa4-05ba41d6c86f", "name": "runTaskFromQueue task cost before running: 3 s 492 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982259550500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3231d4cc-12f4-4ff8-989d-7378f5a5b447", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982259124100, "endTime": 27982259611000, "totalTime": 403400}, "additional": {"logType": "info", "children": [], "durationId": "f8338658-9de5-4b81-9824-135d6b9947b9"}}, {"head": {"id": "54b83d4f-29e8-4bca-8911-b10147193af2", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982267925500, "endTime": 27982267953800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3d90cb01-e855-4e4c-94d3-7bf0032ffb84", "logId": "d1bd3c06-6693-4ed8-b15d-0a43dda5100e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1bd3c06-6693-4ed8-b15d-0a43dda5100e", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982267925500, "endTime": 27982267953800}, "additional": {"logType": "info", "children": [], "durationId": "54b83d4f-29e8-4bca-8911-b10147193af2"}}, {"head": {"id": "d5619463-712d-4504-bea9-fcf5ff947f8e", "name": "BUILD SUCCESSFUL in 3 s 501 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982268001800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "79f25f88-a20f-4294-8c2b-bd60b09ef5db", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27978767857400, "endTime": 27982268343700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 16, "minute": 15}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "a89c7f2a-73fc-4536-a13f-8262f03b2863", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982268387800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a72b6b1d-c021-4668-ab73-7a544e837f65", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982268482700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "549bae5f-6e11-47e8-96aa-fc7a9d4ff140", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982268540700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d24cf931-90c3-4380-9974-a8cc26981749", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982268589100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "977988b9-c647-4d63-917d-8496bec1a775", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982268654100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5e83275-29d0-47e7-bb3a-436819455c3e", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982269086500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b9dfe0-65c7-4361-a238-3efd96818c85", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982269816100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d3a24d9-ec6f-4e59-8094-1a9e02a435b6", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982270074400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f8311fd-b805-468d-aa1c-381e41adffe0", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982270149100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91821fc4-421a-4ed3-968e-443b576e44aa", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982270219700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d34bfc-f0ad-47f7-8fed-06f57f06f380", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982271327500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba934cf-08a6-41dd-b89b-66e3afc45f49", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982272583400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78adc56b-e133-4476-8aac-879cba3a5133", "name": "Incremental task entry:default@SyscapTransform post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982273107600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "880e3326-c1ff-4c5f-b596-853db3e8d0e6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982273259400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8387193-78d8-4643-bbb5-a31b46c0eca6", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982273383000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7bb26d3-8ee0-4d2b-b670-e9c7f2c8dd25", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982273448400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c0700e-b2bd-4e03-911b-42ae6542ab18", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982273505500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa2159a-179c-4f52-9981-3252f753587f", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982273912700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97bc107-a3b2-4985-a67e-3185f2f434a4", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982274157100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "101a6d33-40cd-40cb-b3ba-2b81fb855345", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982274555200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8854e7ef-2373-4d6a-bb08-a7fcf3791a69", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982274904300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9bc397b-1ca4-4d46-88c8-5fad508cf41a", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982274985700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b0be958-87cb-4618-bd4e-a509f589759c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982275044700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d7d12a3-734b-49ac-971a-4c8c92faaa86", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982276979400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0708b3-3f64-43e6-809d-8ff247b64476", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982277404100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a489f86-9484-42e9-80a6-e9285925f6da", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982278033700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4651137-e9fe-438d-ad52-5f378e0bc957", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982278210500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc70593-8525-44c3-8186-bbe6e873c47a", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982278498600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef01be04-5578-47ea-8452-349245521357", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982279053100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f00eba6-b329-4190-98f4-b77f2232c709", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982279131700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "348cbfca-5c64-4acf-b9ba-cbb9c35060b1", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982279426100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4259357a-b6ba-43b2-a8fe-5de23ace4bb6", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982279678500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ecb7e0-ee42-4cb3-934e-d81f60d3a116", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982280403100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc4418c5-bed5-4656-9bd6-59a91fe42a2c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982281528600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "538b7469-d7f2-4dbe-b98f-11ded7a2159a", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982281955800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd9196a-9e9b-4e4f-8abf-3d777438e453", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982282738100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32906cb3-777a-4c07-add2-86c04ac9e90f", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982282948300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02e9fed-bf03-4ec5-bb82-2a5b1dc111d5", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982283125900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa1b3fec-71ff-4f4c-b1cc-c0b16c58d9c8", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982283672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516235a4-bc28-44a5-b03e-992e0b9570a0", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982283924400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc74d84a-eaf7-43d0-a9d3-14aed83d93d7", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982283998600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82820d3a-5660-43bf-9d17-1dab18ae3fc9", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982284053900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c541fc0e-2cb9-4d00-b94a-cec0682ce8f1", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982284912900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d9d28ae-726d-4907-99d9-806adfe67fa3", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982285205800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480e6e28-263c-4387-adc1-1baf37dc222f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982285424900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de191faa-e55c-4c9f-9494-16b87eb862c9", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982292285200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c13f62f0-a5ac-46c1-bda7-737055675467", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982292564800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60d96625-0c4f-4c6e-9dc9-bbf4a86a1b1a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982292741900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488df639-77c0-4f01-bb9d-1df472021645", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982292814800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "213df371-29e9-466c-a989-c2d98a31c58c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982292973500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ce3d053-05f0-40d1-a372-5c5dafa1ea05", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982293685800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1564c03d-1899-4bfe-9b32-70331bf0158e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982293932100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8438ee9a-2e70-4cba-92f6-67f863482928", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982294128900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b35ccbc-0fc5-4e6d-8185-0852ea467a86", "name": "Incremental task entry:default@PackageHap post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982294600600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f26e54-927f-4f33-af8c-85ab73d87d8f", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982294939700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a3bf924-e139-477a-9561-fd6da39ff4e1", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982295043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915f438e-2823-41dc-834f-339466e4d444", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982295344900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "955b5a9a-3107-455f-a6f5-9b1a94258ec5", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982297687100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6705f0b7-9c1d-47cc-8b47-cbed09dedac8", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982298001300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2490251a-559f-4b8e-bd5a-61b4b554142e", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982298376400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663f7e29-ddef-469c-abeb-0b8bd4221b52", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 27982298595700}, "additional": {"logType": "debug", "children": []}}], "workLog": []}