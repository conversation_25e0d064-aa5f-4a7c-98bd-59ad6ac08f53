{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "9b48e0df-926c-407b-80e6-25610296f049", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038983909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f37a77-c2d5-4501-8612-9f9cabb6a09e", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038994367700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976733f3-2b72-4f52-a2a2-3c6a9f835865", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038994791100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7ee4ba-8863-423e-a5cd-c621550a692c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038995041400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe922939-1411-4ff6-9d18-f833c2f60338", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073267586300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073275347900, "endTime": 31073700573400}, "additional": {"children": ["9549ffe7-c222-4db1-893b-afef5e7264b7", "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "31d17ca0-8f4d-4452-ad68-38d4fd6ec003", "0fae344d-0f38-4b04-8a13-dbdfd8d3f1d7", "0f7762ba-147c-44a3-8364-a0135ba5ebb8", "c9d6f59c-9179-4add-a638-ae8fa84891af", "d930d768-79ea-4170-ad6b-00c73f7e3018"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9549ffe7-c222-4db1-893b-afef5e7264b7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073275350400, "endTime": 31073292547800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "1fe65adb-c65b-4442-9eee-0f52066d0650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073292575600, "endTime": 31073699416200}, "additional": {"children": ["cccc45c6-0084-41c1-ae3d-b9cd4716f105", "f11d65c7-87e5-4cb6-b9db-731de27e46a3", "8f354bc0-64bd-4e9b-9a7f-c6802d7554ed", "8436d073-c261-469d-8db3-6db0d45727c7", "44c98dde-6314-45af-b7f3-25c2aabda311", "600c387d-739a-46a2-8fe9-f0c334890ad6", "7d1d7e49-65fa-4205-a1ef-7b03a7dab31d", "aac599ff-35e1-40ea-b49d-15ef317d8ba2", "dad2906e-5a38-4c6e-bf7c-9dddb522dd79"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31d17ca0-8f4d-4452-ad68-38d4fd6ec003", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073699437700, "endTime": 31073700563700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "fa4b73c0-c317-4271-b5ad-a2e6ca27899f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fae344d-0f38-4b04-8a13-dbdfd8d3f1d7", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073700567500, "endTime": 31073700568200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "fdd7e78a-c9ad-40a9-9e7d-caa522fd9e46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f7762ba-147c-44a3-8364-a0135ba5ebb8", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073279685300, "endTime": 31073279736100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "0b276c01-44e5-41d9-9737-3076414e7ded"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b276c01-44e5-41d9-9737-3076414e7ded", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073279685300, "endTime": 31073279736100}, "additional": {"logType": "info", "children": [], "durationId": "0f7762ba-147c-44a3-8364-a0135ba5ebb8", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "c9d6f59c-9179-4add-a638-ae8fa84891af", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073286418500, "endTime": 31073286442900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "148d2d8d-00ca-41c4-84b1-f7ee092d5689"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "148d2d8d-00ca-41c4-84b1-f7ee092d5689", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073286418500, "endTime": 31073286442900}, "additional": {"logType": "info", "children": [], "durationId": "c9d6f59c-9179-4add-a638-ae8fa84891af", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "c5171f61-6c5b-46ec-8a07-f96ea58fc9f6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073286495000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c89b504a-4d71-4090-aaed-113e44ae9b31", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073292379500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fe65adb-c65b-4442-9eee-0f52066d0650", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073275350400, "endTime": 31073292547800}, "additional": {"logType": "info", "children": [], "durationId": "9549ffe7-c222-4db1-893b-afef5e7264b7", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "cccc45c6-0084-41c1-ae3d-b9cd4716f105", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073298993700, "endTime": 31073299002100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "828f972b-d733-48ce-abac-b7f8f8f200e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f11d65c7-87e5-4cb6-b9db-731de27e46a3", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073299025400, "endTime": 31073304098800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "3de9032c-5301-4580-a439-1c9f14123ad2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f354bc0-64bd-4e9b-9a7f-c6802d7554ed", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073304124900, "endTime": 31073521684400}, "additional": {"children": ["d5c6e12c-d1d3-442d-a9e5-82252936b84f", "d38d5ba2-72ec-4413-abac-bee482d5a520", "07eb99b7-9d49-4074-905c-0d1c526fe5e3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "ca5949a6-f3bb-4031-b19d-095e982397f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8436d073-c261-469d-8db3-6db0d45727c7", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073521703500, "endTime": 31073583381700}, "additional": {"children": ["1b1630ee-4830-41b9-9f32-5d2e42f46a5c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "d5a59ffa-e8b8-487b-a1b7-5b445bb5018a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44c98dde-6314-45af-b7f3-25c2aabda311", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073583391800, "endTime": 31073674112600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "15452910-7de8-4d75-bc5d-b55d3a1f1ccf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "600c387d-739a-46a2-8fe9-f0c334890ad6", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073675257200, "endTime": 31073690763500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "9a374eff-6624-45b6-adfa-8bbf311c8e5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d1d7e49-65fa-4205-a1ef-7b03a7dab31d", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073690787200, "endTime": 31073699257300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "b99f56a2-5892-484e-8d16-97b73214d1e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aac599ff-35e1-40ea-b49d-15ef317d8ba2", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073699276200, "endTime": 31073699406800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "9ee2fac1-7767-4130-9905-d4f359f600a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "828f972b-d733-48ce-abac-b7f8f8f200e5", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073298993700, "endTime": 31073299002100}, "additional": {"logType": "info", "children": [], "durationId": "cccc45c6-0084-41c1-ae3d-b9cd4716f105", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "3de9032c-5301-4580-a439-1c9f14123ad2", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073299025400, "endTime": 31073304098800}, "additional": {"logType": "info", "children": [], "durationId": "f11d65c7-87e5-4cb6-b9db-731de27e46a3", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "d5c6e12c-d1d3-442d-a9e5-82252936b84f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073305011200, "endTime": 31073305033200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f354bc0-64bd-4e9b-9a7f-c6802d7554ed", "logId": "b144e206-1a05-4b09-bd24-5b3e5ade309e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b144e206-1a05-4b09-bd24-5b3e5ade309e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073305011200, "endTime": 31073305033200}, "additional": {"logType": "info", "children": [], "durationId": "d5c6e12c-d1d3-442d-a9e5-82252936b84f", "parent": "ca5949a6-f3bb-4031-b19d-095e982397f4"}}, {"head": {"id": "d38d5ba2-72ec-4413-abac-bee482d5a520", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073307424300, "endTime": 31073520059000}, "additional": {"children": ["83472fc5-4c29-4946-b01c-24b8f9ed1ea9", "04b51a14-9cc5-4acb-aeab-f22c63f03cd2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f354bc0-64bd-4e9b-9a7f-c6802d7554ed", "logId": "35b83dbf-66a4-4d49-bbb3-dc289c147683"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "83472fc5-4c29-4946-b01c-24b8f9ed1ea9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073307426200, "endTime": 31073315096400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d38d5ba2-72ec-4413-abac-bee482d5a520", "logId": "f57ce2e5-ff30-4ffd-9f75-3e8c8df60959"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04b51a14-9cc5-4acb-aeab-f22c63f03cd2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073315121200, "endTime": 31073520040600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d38d5ba2-72ec-4413-abac-bee482d5a520", "logId": "ccbf79f8-6d63-4b5d-914d-a9313f29d49a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2df186c0-9f15-41b1-905c-3749ea89ff69", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073307431600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb37d0c6-eb10-4c20-bc9a-eae0b9ec7987", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073314890900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f57ce2e5-ff30-4ffd-9f75-3e8c8df60959", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073307426200, "endTime": 31073315096400}, "additional": {"logType": "info", "children": [], "durationId": "83472fc5-4c29-4946-b01c-24b8f9ed1ea9", "parent": "35b83dbf-66a4-4d49-bbb3-dc289c147683"}}, {"head": {"id": "e7434f6e-bbea-4c9c-bf19-8e4d39b19c41", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073315145100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4affd59-2ff3-4b4a-b985-43b5c26b9dcb", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073322573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0bf830-39e5-496c-b0f9-25028a762686", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073322962900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ba7c5de-ae3a-4e22-8813-ad58406c5703", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073323230000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "765db4d7-25b1-4376-8031-282b604ae696", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073323360300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "791674cb-2b36-46ef-b190-aaeeba567f58", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073326192100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b316ab04-b599-42d8-ba72-06c40e35b9b5", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073330801400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd1e556-8462-4ad1-a395-dba36d53a842", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073365445800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "233a8c95-d40b-4a24-960a-c58348537533", "name": "Sdk init in 121 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073453048900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d743f67-12db-40e6-9d3c-50ab087228a7", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073454213400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 7}, "markType": "other"}}, {"head": {"id": "920c13e2-31b0-4904-a54b-fc178ce4c315", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073454364300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 7}, "markType": "other"}}, {"head": {"id": "e86667a1-a23d-4768-b026-88947057275b", "name": "Project task initialization takes 60 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073518258900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94bc39ae-4a6c-47eb-94da-1e55d274119e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073519546900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d866caa-2066-4568-81b0-2c47b70adaa7", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073519855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2da2c7f0-178a-402c-9fee-596d7c7da560", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073519971400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccbf79f8-6d63-4b5d-914d-a9313f29d49a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073315121200, "endTime": 31073520040600}, "additional": {"logType": "info", "children": [], "durationId": "04b51a14-9cc5-4acb-aeab-f22c63f03cd2", "parent": "35b83dbf-66a4-4d49-bbb3-dc289c147683"}}, {"head": {"id": "35b83dbf-66a4-4d49-bbb3-dc289c147683", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073307424300, "endTime": 31073520059000}, "additional": {"logType": "info", "children": ["f57ce2e5-ff30-4ffd-9f75-3e8c8df60959", "ccbf79f8-6d63-4b5d-914d-a9313f29d49a"], "durationId": "d38d5ba2-72ec-4413-abac-bee482d5a520", "parent": "ca5949a6-f3bb-4031-b19d-095e982397f4"}}, {"head": {"id": "07eb99b7-9d49-4074-905c-0d1c526fe5e3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073521619600, "endTime": 31073521663200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f354bc0-64bd-4e9b-9a7f-c6802d7554ed", "logId": "31ab3838-5dd1-4127-a51d-d2e69df69d99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31ab3838-5dd1-4127-a51d-d2e69df69d99", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073521619600, "endTime": 31073521663200}, "additional": {"logType": "info", "children": [], "durationId": "07eb99b7-9d49-4074-905c-0d1c526fe5e3", "parent": "ca5949a6-f3bb-4031-b19d-095e982397f4"}}, {"head": {"id": "ca5949a6-f3bb-4031-b19d-095e982397f4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073304124900, "endTime": 31073521684400}, "additional": {"logType": "info", "children": ["b144e206-1a05-4b09-bd24-5b3e5ade309e", "35b83dbf-66a4-4d49-bbb3-dc289c147683", "31ab3838-5dd1-4127-a51d-d2e69df69d99"], "durationId": "8f354bc0-64bd-4e9b-9a7f-c6802d7554ed", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "1b1630ee-4830-41b9-9f32-5d2e42f46a5c", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073524736400, "endTime": 31073583362000}, "additional": {"children": ["a7c7d15c-4314-40a9-bfc9-51615e6cb915", "7d422055-3f57-432d-84cd-632230711647", "47baebc6-012d-4ac1-9419-cebb331e0ced"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8436d073-c261-469d-8db3-6db0d45727c7", "logId": "bde970f4-5723-476d-94ae-7f9f9b78dd5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7c7d15c-4314-40a9-bfc9-51615e6cb915", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073536375100, "endTime": 31073536507400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b1630ee-4830-41b9-9f32-5d2e42f46a5c", "logId": "f7068fea-fbc0-49ed-a964-a68f057e6a29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7068fea-fbc0-49ed-a964-a68f057e6a29", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073536375100, "endTime": 31073536507400}, "additional": {"logType": "info", "children": [], "durationId": "a7c7d15c-4314-40a9-bfc9-51615e6cb915", "parent": "bde970f4-5723-476d-94ae-7f9f9b78dd5c"}}, {"head": {"id": "7d422055-3f57-432d-84cd-632230711647", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073541729000, "endTime": 31073580867200}, "additional": {"children": ["09900b2a-33c8-4d8b-9975-72b1f581ce75", "7cd9439a-6f0a-46eb-8846-de1f75f7cfd5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b1630ee-4830-41b9-9f32-5d2e42f46a5c", "logId": "6cd6199a-1e69-49c1-a1cb-b3755ebe48b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09900b2a-33c8-4d8b-9975-72b1f581ce75", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073541730900, "endTime": 31073552756100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d422055-3f57-432d-84cd-632230711647", "logId": "105b2be9-ad2e-4552-8f3b-215f989acb37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7cd9439a-6f0a-46eb-8846-de1f75f7cfd5", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073552778700, "endTime": 31073580848700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d422055-3f57-432d-84cd-632230711647", "logId": "e2bc5f2a-7bf2-4ada-93c5-db7588d44f34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20f7de53-97cd-48c4-99ea-fbbd37cd5161", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073541736100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63b59d2a-ee3a-4f95-bb41-73c0ecb0bde5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073552478500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "105b2be9-ad2e-4552-8f3b-215f989acb37", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073541730900, "endTime": 31073552756100}, "additional": {"logType": "info", "children": [], "durationId": "09900b2a-33c8-4d8b-9975-72b1f581ce75", "parent": "6cd6199a-1e69-49c1-a1cb-b3755ebe48b1"}}, {"head": {"id": "6a1cfe27-b9b5-49a4-ac77-a099d274552a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073552794700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27876666-458e-4f2d-970a-0df9a424cc8a", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073574893000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44854527-368b-48d1-b960-d207af8efc10", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073575207000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd330019-d547-46cb-95cb-0ffd87491f42", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073575551000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef413af3-d2b4-42ec-a3a6-8777434140e5", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073575725800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7c22b8e-ebe0-46fa-bed3-e3580ee71cfa", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073575798500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2963baf7-2def-47aa-9332-ea11f1fbbc49", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073575859400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa821be5-9427-40b9-a529-6ac59a0d938c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073575926100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cc3ab2a-a946-48f1-841b-80819bcde047", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073580152400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "651df7d8-d352-4844-ba94-d69d48fc7ab0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073580646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87fe3ae9-8cb7-493e-a6b3-79388d4a9183", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073580738200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0de83d21-d010-43d0-af99-4a5dc85330ef", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073580792000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2bc5f2a-7bf2-4ada-93c5-db7588d44f34", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073552778700, "endTime": 31073580848700}, "additional": {"logType": "info", "children": [], "durationId": "7cd9439a-6f0a-46eb-8846-de1f75f7cfd5", "parent": "6cd6199a-1e69-49c1-a1cb-b3755ebe48b1"}}, {"head": {"id": "6cd6199a-1e69-49c1-a1cb-b3755ebe48b1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073541729000, "endTime": 31073580867200}, "additional": {"logType": "info", "children": ["105b2be9-ad2e-4552-8f3b-215f989acb37", "e2bc5f2a-7bf2-4ada-93c5-db7588d44f34"], "durationId": "7d422055-3f57-432d-84cd-632230711647", "parent": "bde970f4-5723-476d-94ae-7f9f9b78dd5c"}}, {"head": {"id": "47baebc6-012d-4ac1-9419-cebb331e0ced", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073583244000, "endTime": 31073583265100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1b1630ee-4830-41b9-9f32-5d2e42f46a5c", "logId": "490a0bbe-9cf3-46c6-b4db-6f9202a3ede1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "490a0bbe-9cf3-46c6-b4db-6f9202a3ede1", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073583244000, "endTime": 31073583265100}, "additional": {"logType": "info", "children": [], "durationId": "47baebc6-012d-4ac1-9419-cebb331e0ced", "parent": "bde970f4-5723-476d-94ae-7f9f9b78dd5c"}}, {"head": {"id": "bde970f4-5723-476d-94ae-7f9f9b78dd5c", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073524736400, "endTime": 31073583362000}, "additional": {"logType": "info", "children": ["f7068fea-fbc0-49ed-a964-a68f057e6a29", "6cd6199a-1e69-49c1-a1cb-b3755ebe48b1", "490a0bbe-9cf3-46c6-b4db-6f9202a3ede1"], "durationId": "1b1630ee-4830-41b9-9f32-5d2e42f46a5c", "parent": "d5a59ffa-e8b8-487b-a1b7-5b445bb5018a"}}, {"head": {"id": "d5a59ffa-e8b8-487b-a1b7-5b445bb5018a", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073521703500, "endTime": 31073583381700}, "additional": {"logType": "info", "children": ["bde970f4-5723-476d-94ae-7f9f9b78dd5c"], "durationId": "8436d073-c261-469d-8db3-6db0d45727c7", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "86a6b60e-6417-44b1-b2a5-8a9a961d4c2b", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073611014700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2171ce4d-6d50-47c7-9168-5889dd3a0a51", "name": "hvigorfile, resolve hvigorfile dependencies in 91 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073673750700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15452910-7de8-4d75-bc5d-b55d3a1f1ccf", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073583391800, "endTime": 31073674112600}, "additional": {"logType": "info", "children": [], "durationId": "44c98dde-6314-45af-b7f3-25c2aabda311", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "dad2906e-5a38-4c6e-bf7c-9dddb522dd79", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073674936500, "endTime": 31073675238600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "logId": "53e71ada-7357-46a5-a858-ca32e9f07b43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6545694e-8b3d-45bc-9ac4-3a5d0105d0cc", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073674965000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53e71ada-7357-46a5-a858-ca32e9f07b43", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073674936500, "endTime": 31073675238600}, "additional": {"logType": "info", "children": [], "durationId": "dad2906e-5a38-4c6e-bf7c-9dddb522dd79", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "25f54140-c279-444e-bcb1-8c6b3033199b", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073676433800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23cefc6c-83d2-453e-9612-0cf02f6332bb", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073690048300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a374eff-6624-45b6-adfa-8bbf311c8e5b", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073675257200, "endTime": 31073690763500}, "additional": {"logType": "info", "children": [], "durationId": "600c387d-739a-46a2-8fe9-f0c334890ad6", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "59fbe5bc-0a1e-4cd4-8862-54cee14ba813", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073694531300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfcc2d7f-8d59-45fa-8f45-a942c7e8d30d", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073694646000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55d25298-6de6-4823-bbef-66d86e853374", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073696602000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7ff8e42-d129-412e-9345-e4ba96f7bed2", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073696728200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99f56a2-5892-484e-8d16-97b73214d1e8", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073690787200, "endTime": 31073699257300}, "additional": {"logType": "info", "children": [], "durationId": "7d1d7e49-65fa-4205-a1ef-7b03a7dab31d", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "6e44249f-886a-4d66-ab4e-ab77431876ab", "name": "Configuration phase cost:401 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073699296400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee2fac1-7767-4130-9905-d4f359f600a7", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073699276200, "endTime": 31073699406800}, "additional": {"logType": "info", "children": [], "durationId": "aac599ff-35e1-40ea-b49d-15ef317d8ba2", "parent": "730cf90b-3b44-47b2-b00f-62c5f90d88da"}}, {"head": {"id": "730cf90b-3b44-47b2-b00f-62c5f90d88da", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073292575600, "endTime": 31073699416200}, "additional": {"logType": "info", "children": ["828f972b-d733-48ce-abac-b7f8f8f200e5", "3de9032c-5301-4580-a439-1c9f14123ad2", "ca5949a6-f3bb-4031-b19d-095e982397f4", "d5a59ffa-e8b8-487b-a1b7-5b445bb5018a", "15452910-7de8-4d75-bc5d-b55d3a1f1ccf", "9a374eff-6624-45b6-adfa-8bbf311c8e5b", "b99f56a2-5892-484e-8d16-97b73214d1e8", "9ee2fac1-7767-4130-9905-d4f359f600a7", "53e71ada-7357-46a5-a858-ca32e9f07b43"], "durationId": "968b0151-8c31-4af7-a4b2-81e8b51c59c0", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "d930d768-79ea-4170-ad6b-00c73f7e3018", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073700538300, "endTime": 31073700553800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e9a9470-761e-49df-bdca-a41c6bb450ea", "logId": "f6c8354f-fef5-4070-9f59-d9088b85757f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6c8354f-fef5-4070-9f59-d9088b85757f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073700538300, "endTime": 31073700553800}, "additional": {"logType": "info", "children": [], "durationId": "d930d768-79ea-4170-ad6b-00c73f7e3018", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "fa4b73c0-c317-4271-b5ad-a2e6ca27899f", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073699437700, "endTime": 31073700563700}, "additional": {"logType": "info", "children": [], "durationId": "31d17ca0-8f4d-4452-ad68-38d4fd6ec003", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "fdd7e78a-c9ad-40a9-9e7d-caa522fd9e46", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073700567500, "endTime": 31073700568200}, "additional": {"logType": "info", "children": [], "durationId": "0fae344d-0f38-4b04-8a13-dbdfd8d3f1d7", "parent": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b"}}, {"head": {"id": "e11e634a-1f7d-4939-b2c2-e4dd92fd0d3b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073275347900, "endTime": 31073700573400}, "additional": {"logType": "info", "children": ["1fe65adb-c65b-4442-9eee-0f52066d0650", "730cf90b-3b44-47b2-b00f-62c5f90d88da", "fa4b73c0-c317-4271-b5ad-a2e6ca27899f", "fdd7e78a-c9ad-40a9-9e7d-caa522fd9e46", "0b276c01-44e5-41d9-9737-3076414e7ded", "148d2d8d-00ca-41c4-84b1-f7ee092d5689", "f6c8354f-fef5-4070-9f59-d9088b85757f"], "durationId": "9e9a9470-761e-49df-bdca-a41c6bb450ea"}}, {"head": {"id": "3ed58de9-cdb9-4aa9-a349-499e973945fc", "name": "Configuration task cost before running: 429 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073700839100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ec186a2-3997-4093-9bf6-2377cbe925b2", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073706948100, "endTime": 31073713535800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "220528a3-46b9-47dd-ad34-94e75673a5a0", "logId": "42d3910c-38e1-4208-bf49-11cd77f3abb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "220528a3-46b9-47dd-ad34-94e75673a5a0", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073702221800}, "additional": {"logType": "detail", "children": [], "durationId": "3ec186a2-3997-4093-9bf6-2377cbe925b2"}}, {"head": {"id": "5bb7364c-6a33-4248-8e96-15acef93b0a5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073702548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8148ebe1-dea6-4901-935b-130462c10f77", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073702650700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53713b8d-6483-41b7-85ff-741b3abb2038", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073706961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adbd7f5f-e730-4adb-a2d0-6aa282b497a0", "name": "Incremental task entry:default@PreBuild pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073713342300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e79729a-fbfb-4cc4-98ca-8d2bf29b42aa", "name": "entry : default@PreBuild cost memory 0.3161468505859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073713466200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42d3910c-38e1-4208-bf49-11cd77f3abb9", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073706948100, "endTime": 31073713535800}, "additional": {"logType": "info", "children": [], "durationId": "3ec186a2-3997-4093-9bf6-2377cbe925b2"}}, {"head": {"id": "aa7034f6-e441-4086-ac58-d0900a81e810", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073717772700, "endTime": 31073719447600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "949a1e81-77bd-412f-93f9-19cabcab0e50", "logId": "efcf3d2a-bb54-4640-99ba-eeac6a60a816"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "949a1e81-77bd-412f-93f9-19cabcab0e50", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073716568000}, "additional": {"logType": "detail", "children": [], "durationId": "aa7034f6-e441-4086-ac58-d0900a81e810"}}, {"head": {"id": "d4c6e58f-6096-47da-b3fc-5584ca04877e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073716923900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "234419f6-10bf-458a-ad8f-f4f718aabf33", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073717020800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d8ccd2-2219-4068-814a-a9d61017a8e2", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073717782200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d7c6a9-1a87-4893-a7d5-f62880cbdc9c", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073718451500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8811a95c-4ebe-4035-b32b-29215e5dbdc3", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073719253700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca10975d-98a8-4d91-8568-d129c64c73ec", "name": "entry : default@GenerateMetadata cost memory 0.094482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073719369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efcf3d2a-bb54-4640-99ba-eeac6a60a816", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073717772700, "endTime": 31073719447600}, "additional": {"logType": "info", "children": [], "durationId": "aa7034f6-e441-4086-ac58-d0900a81e810"}}, {"head": {"id": "265093a6-394c-4087-aa6f-82f0da8bf11c", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722625300, "endTime": 31073723002500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9dc75c6d-8cb3-4466-a6f7-198b77d73bcc", "logId": "08858410-364c-4913-aab2-84a94df9ad1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9dc75c6d-8cb3-4466-a6f7-198b77d73bcc", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073721892300}, "additional": {"logType": "detail", "children": [], "durationId": "265093a6-394c-4087-aa6f-82f0da8bf11c"}}, {"head": {"id": "9994162a-072f-4055-a029-dc6d181e757d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722371100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeba793f-6a52-4c35-b4f7-df4f363e5fd5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722478300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c76567c3-e988-4ddb-a133-ba15fde99edb", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722651000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "712ed2d1-5088-4a32-bf20-7715c121f7b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722740000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc031bde-cee0-4863-ae10-62c76d1c1264", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722788100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0059f3f9-f37f-4a5c-b6ab-4178a0ca0442", "name": "entry : default@ConfigureCmake cost memory 0.03612518310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722850900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8d08805-fb04-4bf1-88e3-af3a735df94a", "name": "runTaskFromQueue task cost before running: 452 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722941000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08858410-364c-4913-aab2-84a94df9ad1c", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073722625300, "endTime": 31073723002500, "totalTime": 295900}, "additional": {"logType": "info", "children": [], "durationId": "265093a6-394c-4087-aa6f-82f0da8bf11c"}}, {"head": {"id": "c9faa396-880f-4ea3-b9d4-a2409446453b", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073725529300, "endTime": 31073727061000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8925a187-13c9-4a06-b186-f924add98343", "logId": "6c6c1624-06e2-4cce-b891-f550d524bcaf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8925a187-13c9-4a06-b186-f924add98343", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073724567100}, "additional": {"logType": "detail", "children": [], "durationId": "c9faa396-880f-4ea3-b9d4-a2409446453b"}}, {"head": {"id": "68a50edf-64a1-4e10-9b78-83802413398a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073724911900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "796d2e2a-a5eb-41d6-83ef-3031473d87a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073725006000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa837bb-cc58-4b58-b562-30321eae0e21", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073725537800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcab3367-fa2b-41c0-a5b2-0e0ef87a74be", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073726907600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6840b27e-19b4-4402-9301-f9c14f2d84ee", "name": "entry : default@MergeProfile cost memory 0.**********", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073726996900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c6c1624-06e2-4cce-b891-f550d524bcaf", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073725529300, "endTime": 31073727061000}, "additional": {"logType": "info", "children": [], "durationId": "c9faa396-880f-4ea3-b9d4-a2409446453b"}}, {"head": {"id": "ad0f7928-2f12-4073-a5e9-fd02ea2e7d82", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073729587500, "endTime": 31073732051600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8620312e-1a52-41c4-b183-9ebd19849dc5", "logId": "aadc6caa-75ce-46f5-a907-aa103e902c1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8620312e-1a52-41c4-b183-9ebd19849dc5", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073728511000}, "additional": {"logType": "detail", "children": [], "durationId": "ad0f7928-2f12-4073-a5e9-fd02ea2e7d82"}}, {"head": {"id": "1b888941-ced6-4a5d-aab2-c6b481d668dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073728842300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbaedb04-9a12-49fd-b823-0bbb34592230", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073728933300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1be2b19-6b4a-41c2-adbe-dfe402167856", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073729595600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78733aa8-4bbb-4261-8386-9f113e117175", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073730328600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381eb7de-3e11-49ea-bf7e-42b7ad85a6b7", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073731819100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a87fe6-082a-4052-b80f-b8b89679923c", "name": "entry : default@CreateBuildProfile cost memory 0.10443878173828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073731974500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aadc6caa-75ce-46f5-a907-aa103e902c1c", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073729587500, "endTime": 31073732051600}, "additional": {"logType": "info", "children": [], "durationId": "ad0f7928-2f12-4073-a5e9-fd02ea2e7d82"}}, {"head": {"id": "f656a396-588b-4223-8c1b-419aad04df9b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073735894500, "endTime": 31073736855100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "773aa04b-ad90-4d23-8146-b153966a9fac", "logId": "6224e6c2-f685-46a3-bdea-18f13502f353"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "773aa04b-ad90-4d23-8146-b153966a9fac", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073734373200}, "additional": {"logType": "detail", "children": [], "durationId": "f656a396-588b-4223-8c1b-419aad04df9b"}}, {"head": {"id": "9c7195e7-0fe0-4d3a-9f2b-472f0f93cf1e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073734941100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eb03c86-0df9-47f8-84a3-65387a613c90", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073735070100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f75c37a-00c6-48ba-a224-0a0559d679e5", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073735905100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f9dd15-fe50-427f-a28c-3aa9389bbf57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073736040700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8fcb1c0-31e5-45f2-a4f6-fc1a856fd8f0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073736103000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1c79f5e-9a48-4efa-bfa2-197a1931b3d7", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073736341300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31526529-c298-4346-b83b-73c7b6a446d4", "name": "runTaskFromQueue task cost before running: 465 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073736664300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6224e6c2-f685-46a3-bdea-18f13502f353", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073735894500, "endTime": 31073736855100, "totalTime": 739500}, "additional": {"logType": "info", "children": [], "durationId": "f656a396-588b-4223-8c1b-419aad04df9b"}}, {"head": {"id": "c4892fa0-f898-4cb5-9f54-819732ec29ea", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073744504100, "endTime": 31073745739100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8a1baab9-37b3-43e2-8108-8d6c951c07a2", "logId": "25140881-74cd-470b-be07-b31a7885e04a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a1baab9-37b3-43e2-8108-8d6c951c07a2", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073740003700}, "additional": {"logType": "detail", "children": [], "durationId": "c4892fa0-f898-4cb5-9f54-819732ec29ea"}}, {"head": {"id": "8465a54a-c7d4-400a-a363-210cb625a140", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073740489100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a8e8319-28d3-4650-ae4e-48f714626d88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073740602400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09c9773a-31fb-47b9-b1df-28edbecdf876", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073744516400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d2fb2a-5f2f-4eee-aefb-289926a75d9e", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073744891400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9690e71f-3e8d-4d7c-b537-63f8c4558c85", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03830718994140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073745499300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc17fdf-6024-4c45-ba41-09ee8ceefd2a", "name": "runTaskFromQueue task cost before running: 474 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073745653900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25140881-74cd-470b-be07-b31a7885e04a", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073744504100, "endTime": 31073745739100, "totalTime": 1123500}, "additional": {"logType": "info", "children": [], "durationId": "c4892fa0-f898-4cb5-9f54-819732ec29ea"}}, {"head": {"id": "0d6024ad-842f-4320-9d4d-d661467519bb", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073752022800, "endTime": 31073753940000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "15f61727-c8ed-4e21-9d58-a1402c3c40c9", "logId": "4500c265-7e05-487c-932f-ac32f09a2b17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15f61727-c8ed-4e21-9d58-a1402c3c40c9", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073749622300}, "additional": {"logType": "detail", "children": [], "durationId": "0d6024ad-842f-4320-9d4d-d661467519bb"}}, {"head": {"id": "20e63e72-b800-4168-893e-97d8523f1a69", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073750028300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f88ef1-f229-41d3-b9fa-02ed8c2cec50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073750138900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "480e652d-34c1-48ee-ba82-52a794d5502c", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073752033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cfb62a8-5538-4082-a558-3e3fb9f9439e", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073753337600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8e353e-3955-4eaa-aac1-05530efd7fbc", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073753501600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd755f86-231b-4bf2-a6b0-84d02b745e3d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073753634500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a4250dc-9665-41b8-9afe-7add4bde6e27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073753698000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0533dcc4-e8de-47ba-bd6a-16468557191b", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1171722412109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073753785900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2462f1f-d374-41c1-bd22-37527ab46537", "name": "runTaskFromQueue task cost before running: 482 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073753868800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4500c265-7e05-487c-932f-ac32f09a2b17", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073752022800, "endTime": 31073753940000, "totalTime": 1824200}, "additional": {"logType": "info", "children": [], "durationId": "0d6024ad-842f-4320-9d4d-d661467519bb"}}, {"head": {"id": "1cd29825-8742-42d6-9f78-bc084cf9d50a", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757167600, "endTime": 31073757516000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "aa939c31-31d7-4d71-b0f0-820eab2364b8", "logId": "5c1b88f7-23ff-428e-b783-d266812f1579"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa939c31-31d7-4d71-b0f0-820eab2364b8", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073756174900}, "additional": {"logType": "detail", "children": [], "durationId": "1cd29825-8742-42d6-9f78-bc084cf9d50a"}}, {"head": {"id": "eb91fc16-69f0-418d-9844-d6ea1dafbe7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073756493900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56456ee4-3f44-4ebf-9639-396e5b61c8b3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073756584300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20d9e205-a17a-45e6-ab36-bdba740b6f9d", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28b88bb2-df1f-4285-a1e7-29b0167c46b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757284900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e27dcb1-aedf-4e70-a1c6-9a7889b6f4fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757336800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54ff8ffb-d9ee-4287-b045-58edf0d71720", "name": "entry : default@BuildNativeWithCmake cost memory 0.1989288330078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757399700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4196cfc2-d13f-4176-af44-4714245f5788", "name": "runTaskFromQueue task cost before running: 486 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757466300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c1b88f7-23ff-428e-b783-d266812f1579", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073757167600, "endTime": 31073757516000, "totalTime": 282700}, "additional": {"logType": "info", "children": [], "durationId": "1cd29825-8742-42d6-9f78-bc084cf9d50a"}}, {"head": {"id": "01bb960b-d9f4-42e1-a8f1-0687c4c0a00c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073760187200, "endTime": 31073763322200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2a8ddc6e-193f-4338-aa92-f22f104791e7", "logId": "83fb5b46-5b19-4db1-9868-855e16efbb28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a8ddc6e-193f-4338-aa92-f22f104791e7", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073759086300}, "additional": {"logType": "detail", "children": [], "durationId": "01bb960b-d9f4-42e1-a8f1-0687c4c0a00c"}}, {"head": {"id": "262ebafe-ee44-46e0-b20b-9a5cba714b75", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073759444300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0960d868-ad6b-4262-ba88-b4dc1ba20919", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073759535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e261e23-a737-4b0a-8a2c-1516a967d259", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073760195500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbfda659-f8f6-4243-9eba-41184014f5e4", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073763095600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0de503-eb30-4a8f-8405-52a8c78f8227", "name": "entry : default@MakePackInfo cost memory 0.139251708984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073763252300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83fb5b46-5b19-4db1-9868-855e16efbb28", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073760187200, "endTime": 31073763322200}, "additional": {"logType": "info", "children": [], "durationId": "01bb960b-d9f4-42e1-a8f1-0687c4c0a00c"}}, {"head": {"id": "98edc97f-57f2-47eb-93ac-334d343673d2", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073766610500, "endTime": 31073769691900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "b3617ed0-79dd-44e3-9cf5-52b8f6821046", "logId": "6815f28c-a63c-420f-a5b3-19c540e4d292"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3617ed0-79dd-44e3-9cf5-52b8f6821046", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073765214500}, "additional": {"logType": "detail", "children": [], "durationId": "98edc97f-57f2-47eb-93ac-334d343673d2"}}, {"head": {"id": "c0003ef5-7f32-4cd0-8bd7-74d357d08971", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073765567800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06c047a0-74c2-4103-b816-556d71e1cb69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073765657900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764b15cd-0e99-4ef4-9a48-95febbbf4e8e", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073766645000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf9efc4-252e-461c-a115-54f12995610a", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073766777900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a42a75c-aa30-4498-a683-f60713a46a43", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073767369000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "507006a3-440d-49ae-9880-36bc54e9ef40", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073769154700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e13832cf-d6ff-4fa0-9841-f2d6e2b76ff7", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073769326900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89a0e23d-49a6-48f2-b3d4-b66d5a928c6b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073769433100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776bbd88-349d-4529-a7c8-1f86b8e93137", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073769486400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "580aaf58-3c09-40e5-93bb-7db9f03c028a", "name": "entry : default@SyscapTransform cost memory 0.15300750732421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073769559400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b411470-eba3-47cc-8b9e-b6de4208c652", "name": "runTaskFromQueue task cost before running: 498 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073769629900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6815f28c-a63c-420f-a5b3-19c540e4d292", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073766610500, "endTime": 31073769691900, "totalTime": 3004800}, "additional": {"logType": "info", "children": [], "durationId": "98edc97f-57f2-47eb-93ac-334d343673d2"}}, {"head": {"id": "e70e5e92-d1ff-4702-8238-87519f3f2643", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073772936400, "endTime": 31073773745800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "351190c6-9a28-43f1-8532-ebcedefe29bc", "logId": "f654140d-0f62-484d-84d8-4c8001bfa76c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "351190c6-9a28-43f1-8532-ebcedefe29bc", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073771502300}, "additional": {"logType": "detail", "children": [], "durationId": "e70e5e92-d1ff-4702-8238-87519f3f2643"}}, {"head": {"id": "8217c2f9-ae3a-4495-846d-1446d3b2edfd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073771940100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a746b4c4-f4dd-421d-bb38-7dbff245097e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073772033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "088e4c92-c4df-421e-baab-76d106004d6d", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073772944500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f3e0d0-9ac0-49e0-8f13-677e306c40f5", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073773602000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a50fae9-c141-45d2-9e0d-4af11267daec", "name": "entry : default@ProcessProfile cost memory 0.05997467041015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073773684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f654140d-0f62-484d-84d8-4c8001bfa76c", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073772936400, "endTime": 31073773745800}, "additional": {"logType": "info", "children": [], "durationId": "e70e5e92-d1ff-4702-8238-87519f3f2643"}}, {"head": {"id": "74da2514-8a2b-4f8a-b33a-ce7a47f2382d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073776907900, "endTime": 31073780942600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7e5bc12b-4a85-4af9-9e7d-0db6bcc9ff3d", "logId": "45699ff4-146a-4ec7-a021-6b973e9e9425"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e5bc12b-4a85-4af9-9e7d-0db6bcc9ff3d", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073775090000}, "additional": {"logType": "detail", "children": [], "durationId": "74da2514-8a2b-4f8a-b33a-ce7a47f2382d"}}, {"head": {"id": "81513829-003a-4072-a975-9a2096e2e2a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073775438800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e193bff-a92a-4534-b161-339d34a0b2f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073775524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "887b9401-7cb8-4f22-9476-4f067de25bfb", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073776919500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1059e29-8f5c-417f-9b5b-9fd8466ad835", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073780752700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb594f68-36a3-41f2-b3f5-456d030b9655", "name": "entry : default@ProcessRouterMap cost memory 0.20290374755859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073780873200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45699ff4-146a-4ec7-a021-6b973e9e9425", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073776907900, "endTime": 31073780942600}, "additional": {"logType": "info", "children": [], "durationId": "74da2514-8a2b-4f8a-b33a-ce7a47f2382d"}}, {"head": {"id": "0885bcb6-1c1f-4510-9396-e4f3b463e5fd", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073784262200, "endTime": 31073785243700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "866d775e-96be-4f72-bd5f-992ca6572dd6", "logId": "0a8b4a77-5aaf-49d8-8681-00f681caccf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "866d775e-96be-4f72-bd5f-992ca6572dd6", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073783227300}, "additional": {"logType": "detail", "children": [], "durationId": "0885bcb6-1c1f-4510-9396-e4f3b463e5fd"}}, {"head": {"id": "3724fd47-0236-45fb-b1bb-a208ff400882", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073783543100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729f8afc-08e3-416e-bc88-317025dca467", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073783632800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d76ff5-c0c4-4c37-aba2-0f96c36c07b1", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073784269900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41ec238d-6cfd-4cfe-809b-d4574ece6aff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073784383700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23376c3c-1db7-479f-8239-ed6031da7c18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073784439700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40fe9d43-2241-448e-8386-9421c5e93b0b", "name": "entry : default@BuildNativeWithNinja cost memory 0.05667877197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073784994200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca79179a-c1fd-4d3f-a15b-0a1764c51ceb", "name": "runTaskFromQueue task cost before running: 514 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073785103700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a8b4a77-5aaf-49d8-8681-00f681caccf6", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073784262200, "endTime": 31073785243700, "totalTime": 815200}, "additional": {"logType": "info", "children": [], "durationId": "0885bcb6-1c1f-4510-9396-e4f3b463e5fd"}}, {"head": {"id": "e2b67458-076a-422f-a102-307c36fadfdc", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073790540800, "endTime": 31073795299100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "3d7fb3ab-0920-4745-a1a2-ebaee88dd670", "logId": "fe1991d7-aa63-4757-a08f-0ba6b67bc6ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d7fb3ab-0920-4745-a1a2-ebaee88dd670", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073787919400}, "additional": {"logType": "detail", "children": [], "durationId": "e2b67458-076a-422f-a102-307c36fadfdc"}}, {"head": {"id": "28e220c3-4003-4eeb-92ed-fa9c42f47c35", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073788834200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "537bd18a-a99f-4fed-9b55-28e19451eeba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073788944100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c129fd0-0f1e-4d1e-a8f5-3fe6fbc96b5e", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073789684200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8e5ebc-5d34-4436-9b93-01e6218a1557", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073791931200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49d9062-ed43-4e41-a5d9-9cab98563031", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073793703500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebbd7497-fe1a-471c-814b-9e3cdab269f2", "name": "entry : default@ProcessResource cost memory 0.1695404052734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073793820300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe1991d7-aa63-4757-a08f-0ba6b67bc6ad", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073790540800, "endTime": 31073795299100}, "additional": {"logType": "info", "children": [], "durationId": "e2b67458-076a-422f-a102-307c36fadfdc"}}, {"head": {"id": "3a68418c-fc07-4c59-bb0c-ae717c8e8f6b", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073801942200, "endTime": 31073815805900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e5c73137-462d-4b84-a28f-6e7e2466dccd", "logId": "16934ea8-7b14-4fb1-9a4d-642ca7100061"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5c73137-462d-4b84-a28f-6e7e2466dccd", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073798963600}, "additional": {"logType": "detail", "children": [], "durationId": "3a68418c-fc07-4c59-bb0c-ae717c8e8f6b"}}, {"head": {"id": "2f05fd8b-4e4d-41ae-a3b3-3a7c3337b0df", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073799406100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac895cb1-9cc2-4b27-9b8f-b6e18939a3ed", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073799502900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9787dd3-a3f4-4148-9579-99bb480898e8", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073801950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a927e8-d240-4c06-8a11-36da80effb0c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073815463600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd674a62-44fc-42f2-a256-e831e2096bb2", "name": "entry : default@GenerateLoaderJson cost memory 0.7664260864257812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073815589700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16934ea8-7b14-4fb1-9a4d-642ca7100061", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073801942200, "endTime": 31073815805900}, "additional": {"logType": "info", "children": [], "durationId": "3a68418c-fc07-4c59-bb0c-ae717c8e8f6b"}}, {"head": {"id": "5f17050d-5f7c-4102-ba1d-f7b0ca930f0a", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073823682100, "endTime": 31073826938400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "98fc2c77-9817-4b1a-8611-968b49bde90d", "logId": "b2236183-ad85-4c87-a5fc-81e3344ca6cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98fc2c77-9817-4b1a-8611-968b49bde90d", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073822211200}, "additional": {"logType": "detail", "children": [], "durationId": "5f17050d-5f7c-4102-ba1d-f7b0ca930f0a"}}, {"head": {"id": "e6f52b63-4c73-4649-8917-8189bc932b71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073822901600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5c46e89-65b3-4ca6-bca0-607a2ab39723", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073823011400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1ce041e-4122-419c-b08f-23fd8d8500c2", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073823695200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "568db964-7531-40fb-8226-788e4e0e4441", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073825713400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7944a58-8d93-482e-baeb-3e63440620f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073825820200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a7206f2-e551-4c24-bf61-52e74a09029f", "name": "entry : default@ProcessLibs cost memory 0.126190185546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073826734800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "512f1227-cfca-4a59-b771-9325c478cecb", "name": "runTaskFromQueue task cost before running: 555 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073826871400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2236183-ad85-4c87-a5fc-81e3344ca6cc", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073823682100, "endTime": 31073826938400, "totalTime": 3164200}, "additional": {"logType": "info", "children": [], "durationId": "5f17050d-5f7c-4102-ba1d-f7b0ca930f0a"}}, {"head": {"id": "aec5b762-8fc5-4c35-892a-d3360bdf505a", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073832461000, "endTime": 31073852911500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "61429e5b-7634-42f8-9ce2-dd0d772bdeb0", "logId": "5fbe305d-0763-4e37-825b-78cf6e9bd6af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61429e5b-7634-42f8-9ce2-dd0d772bdeb0", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073829065500}, "additional": {"logType": "detail", "children": [], "durationId": "aec5b762-8fc5-4c35-892a-d3360bdf505a"}}, {"head": {"id": "c5c13e2f-8593-4868-a439-5f2d9744b1f2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073829394900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e77972d-1fac-4faa-b730-8c23b95e6d5a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073829484100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303a9b0e-d586-40d7-9c41-53b9f768f950", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073830282800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a0bc1a-3c21-48c9-a928-5ea3cd155078", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073832483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6adb54-fb75-4ef8-acca-5ba949a09f6c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073852702400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ff2e5b-75d6-4ece-992b-216f5bd3ec1c", "name": "entry : default@CompileResource cost memory -4.338508605957031", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073852829900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fbe305d-0763-4e37-825b-78cf6e9bd6af", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073832461000, "endTime": 31073852911500}, "additional": {"logType": "info", "children": [], "durationId": "aec5b762-8fc5-4c35-892a-d3360bdf505a"}}, {"head": {"id": "aadeffc6-88f5-4429-a761-5107ab96d86c", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073859514000, "endTime": 31073861026300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e1ee3027-cca8-4345-ae5b-654d4b74bc8e", "logId": "9f910506-dc98-4c60-8285-3fbb32da34e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1ee3027-cca8-4345-ae5b-654d4b74bc8e", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073856884300}, "additional": {"logType": "detail", "children": [], "durationId": "aadeffc6-88f5-4429-a761-5107ab96d86c"}}, {"head": {"id": "8e014576-7b3c-4689-8cd0-5a7d51a7e0ee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073857364200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b42adbf-4740-4734-b439-b4eb085d68e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073857473400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15cdb7e9-a382-40e6-a517-4afb7550510b", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073859523700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07150005-7224-43b0-87aa-f3eb6997d13d", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073859808100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431b6e61-a90e-41f9-b2a3-5bda74e367e1", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073860693000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cbf8b4b-f421-48ce-beb8-41833561a0a1", "name": "entry : default@DoNativeStrip cost memory 0.07598114013671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073860943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f910506-dc98-4c60-8285-3fbb32da34e2", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073859514000, "endTime": 31073861026300}, "additional": {"logType": "info", "children": [], "durationId": "aadeffc6-88f5-4429-a761-5107ab96d86c"}}, {"head": {"id": "1cdc1f7b-dc2b-4b08-8582-cfede1ae234f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073867358800, "endTime": 31077525694100}, "additional": {"children": ["5b9b3339-7048-4782-9478-12e4eb0027b9", "84dcd088-4379-437d-b65e-9ca8c39e3a9d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "e74afa83-2787-4bf2-993d-5cb809510177", "logId": "469485b3-eea8-456c-b04f-aa15aa8af802"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e74afa83-2787-4bf2-993d-5cb809510177", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073862792100}, "additional": {"logType": "detail", "children": [], "durationId": "1cdc1f7b-dc2b-4b08-8582-cfede1ae234f"}}, {"head": {"id": "15a1781d-e336-44bb-92c1-7e53099a82a3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073863285700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84458a31-f34d-4e00-83db-1277c16083df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073863609500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "521da28c-9575-4d33-9320-ff0f13aed841", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073867369800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8ed5147-99bd-4bdd-a050-38d662404972", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073881548800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dafc0992-b8ce-447f-8b03-4505518faf01", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073881736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51f12b1e-3594-4f29-a040-fddcf85cfe23", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073895032300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6383f0-8cef-47eb-ae83-fafd58963659", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073895481500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2409b034-dd1c-4cc3-bbc1-3cd1865b54e9", "name": "default@CompileArkTS work[60] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073896399400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9b3339-7048-4782-9478-12e4eb0027b9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31074010869500, "endTime": 31077518470300}, "additional": {"children": ["f92cfcd3-dffd-4324-8f32-57c60f30ac0b", "f9a7e75c-153d-4387-9dd7-9cc7ad55bb2c", "1a2deb45-5a41-40d8-923f-ae078d880374", "87cd13e8-caef-4ba5-b10a-4eec84ac558e", "f2026a99-565e-4ba8-bbef-76f12ef47059", "0e2e885b-db72-4d3c-83cb-71fb0f2860aa"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "1cdc1f7b-dc2b-4b08-8582-cfede1ae234f", "logId": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21f8c44a-8ab3-45af-92aa-71f5e6337c73", "name": "default@CompileArkTS work[60] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073897194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "575f3db4-22ac-4a7a-a491-46442461ca50", "name": "default@CompileArkTS work[60] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073897286900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbd64502-9926-4ccc-ba8c-6d7307f19193", "name": "CopyResources startTime: 31073897347500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073897349400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcc182c3-d4cf-4f87-b941-457232f652fa", "name": "default@CompileArkTS work[61] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073897405300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84dcd088-4379-437d-b65e-9ca8c39e3a9d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31075177362100, "endTime": 31075189440600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "1cdc1f7b-dc2b-4b08-8582-cfede1ae234f", "logId": "2772613c-f6cb-4e13-a37b-12d12958a616"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a15c724c-eb7b-4a6d-b612-edcab4d4ed7e", "name": "default@CompileArkTS work[61] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073897988700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4a72323-676a-4cd9-9411-f3bb68c85e1b", "name": "default@CompileArkTS work[61] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073898065600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df8c395-6138-4486-91e7-07c79bab8156", "name": "entry : default@CompileArkTS cost memory 1.6007156372070312", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073898189100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7731486-1271-447e-b779-897ee2807e82", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073903897300, "endTime": 31073907381600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "c88d937a-0a10-43fe-b651-011f044e7975", "logId": "a29e32d6-d50e-4835-be6c-b5ff7342dc5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c88d937a-0a10-43fe-b651-011f044e7975", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073899555700}, "additional": {"logType": "detail", "children": [], "durationId": "b7731486-1271-447e-b779-897ee2807e82"}}, {"head": {"id": "42fece35-cc92-43b8-8edc-f4b17344a02b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073899883400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca1008d-2884-4f41-97ca-c1703b9d0ea6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073899959000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21f4afc5-7502-47c4-9e87-43902c75f389", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073903915300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72242b45-921d-42f4-8114-b311314a9956", "name": "entry : default@BuildJS cost memory 0.1278839111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073907179300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397af0f6-55f0-4249-83fb-8b3502f15ddc", "name": "runTaskFromQueue task cost before running: 636 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073907316800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29e32d6-d50e-4835-be6c-b5ff7342dc5a", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073903897300, "endTime": 31073907381600, "totalTime": 3402200}, "additional": {"logType": "info", "children": [], "durationId": "b7731486-1271-447e-b779-897ee2807e82"}}, {"head": {"id": "a48ab779-d031-4af8-ba4c-0b7acbdd05d3", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073911438900, "endTime": 31073913112500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "73003a63-e4a1-4482-a841-32d0a9919c17", "logId": "bb385340-77bd-42c8-a3d4-a6ec68d4b38f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73003a63-e4a1-4482-a841-32d0a9919c17", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073908840100}, "additional": {"logType": "detail", "children": [], "durationId": "a48ab779-d031-4af8-ba4c-0b7acbdd05d3"}}, {"head": {"id": "e5a662e3-cc5d-402f-afdc-ddeea1f25949", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073909179200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6183d33f-504b-41a4-865e-b6db84d9cfa7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073909274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bd2a19d-0db2-49ae-b544-1057dd3703e7", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073911451600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6210e38-9378-4a32-80f1-931758499e57", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073911816000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b34f3e-7289-492b-b32c-c395fae4677b", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073912930100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d83f054c-5d62-47f6-a0c6-29a2066feb48", "name": "entry : default@CacheNativeLibs cost memory 0.0903778076171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073913037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb385340-77bd-42c8-a3d4-a6ec68d4b38f", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073911438900, "endTime": 31073913112500}, "additional": {"logType": "info", "children": [], "durationId": "a48ab779-d031-4af8-ba4c-0b7acbdd05d3"}}, {"head": {"id": "c9c50510-66d7-4053-8b70-a423877db162", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074010388600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd02d8ed-f1f9-49d4-b14c-8e274906b8d6", "name": "default@CompileArkTS work[60] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074010751400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eff7ebdf-6c95-47ca-9a26-728980755e99", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074010844700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c42e455f-cdf4-4b65-9202-c0f94ee32d51", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074010895800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72682c5c-23a3-4b77-a014-fa58746f3b01", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074010954100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "532ddebb-bcad-453f-80dd-97702a44e9c4", "name": "default@CompileArkTS work[61] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074012170700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42724746-8ef1-4316-9e60-17a2feafcbe2", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31075189629100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07e13fa6-a419-4bf4-8be4-c95a97f18ed2", "name": "CopyResources is end, endTime: 31075189752500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31075189755900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c4861f-176a-42df-b6e8-262398b5fd7f", "name": "default@CompileArkTS work[61] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31075189831600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2772613c-f6cb-4e13-a37b-12d12958a616", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31075177362100, "endTime": 31075189440600}, "additional": {"logType": "info", "children": [], "durationId": "84dcd088-4379-437d-b65e-9ca8c39e3a9d", "parent": "469485b3-eea8-456c-b04f-aa15aa8af802"}}, {"head": {"id": "c5281691-c380-4fb2-87eb-68f6c638daa5", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31075189912400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b88d2f-38df-4301-9af1-00f7b1f9a1db", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077518900600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f92cfcd3-dffd-4324-8f32-57c60f30ac0b", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31074010953200, "endTime": 31074015202700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b9b3339-7048-4782-9478-12e4eb0027b9", "logId": "86044108-e84b-4275-816f-8c5e1da61042"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86044108-e84b-4275-816f-8c5e1da61042", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074010953200, "endTime": 31074015202700}, "additional": {"logType": "info", "children": [], "durationId": "f92cfcd3-dffd-4324-8f32-57c60f30ac0b", "parent": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}}, {"head": {"id": "f9a7e75c-153d-4387-9dd7-9cc7ad55bb2c", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31074015218900, "endTime": 31074015321200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b9b3339-7048-4782-9478-12e4eb0027b9", "logId": "dbc5c525-c663-4b96-adb2-fe8f6587fbd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbc5c525-c663-4b96-adb2-fe8f6587fbd2", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074015218900, "endTime": 31074015321200}, "additional": {"logType": "info", "children": [], "durationId": "f9a7e75c-153d-4387-9dd7-9cc7ad55bb2c", "parent": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}}, {"head": {"id": "1a2deb45-5a41-40d8-923f-ae078d880374", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31074015333700, "endTime": 31074015368000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b9b3339-7048-4782-9478-12e4eb0027b9", "logId": "87aadeb4-036a-4e6d-98ae-a34dc58fd23a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87aadeb4-036a-4e6d-98ae-a34dc58fd23a", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074015333700, "endTime": 31074015368000}, "additional": {"logType": "info", "children": [], "durationId": "1a2deb45-5a41-40d8-923f-ae078d880374", "parent": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}}, {"head": {"id": "87cd13e8-caef-4ba5-b10a-4eec84ac558e", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31074015381700, "endTime": 31077448814500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b9b3339-7048-4782-9478-12e4eb0027b9", "logId": "6bb5f8df-c7de-434e-b6d3-505994135d01"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bb5f8df-c7de-434e-b6d3-505994135d01", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31074015381700, "endTime": 31077448814500}, "additional": {"logType": "info", "children": [], "durationId": "87cd13e8-caef-4ba5-b10a-4eec84ac558e", "parent": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}}, {"head": {"id": "f2026a99-565e-4ba8-bbef-76f12ef47059", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077448832500, "endTime": 31077452762700}, "additional": {"children": ["40c4dde0-4d39-49ef-a343-e76d33ee5da1", "69ca8e37-5c7d-413e-a88e-24b50dbac304", "dd6ee576-1b83-4795-bb86-7f69a825596f"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b9b3339-7048-4782-9478-12e4eb0027b9", "logId": "68caa9fa-9393-4374-99e0-16b7d354f592"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68caa9fa-9393-4374-99e0-16b7d354f592", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077448832500, "endTime": 31077452762700}, "additional": {"logType": "info", "children": ["71aca86f-2255-4d4b-8dc8-9319e1b6d14d", "04f24cd6-5a54-4476-b15e-514ece6c6af0", "59f567a6-ff8b-4a1f-abf8-24430a7b9d97"], "durationId": "f2026a99-565e-4ba8-bbef-76f12ef47059", "parent": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}}, {"head": {"id": "40c4dde0-4d39-49ef-a343-e76d33ee5da1", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077448851600, "endTime": 31077448857700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f2026a99-565e-4ba8-bbef-76f12ef47059", "logId": "71aca86f-2255-4d4b-8dc8-9319e1b6d14d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71aca86f-2255-4d4b-8dc8-9319e1b6d14d", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077448851600, "endTime": 31077448857700}, "additional": {"logType": "info", "children": [], "durationId": "40c4dde0-4d39-49ef-a343-e76d33ee5da1", "parent": "68caa9fa-9393-4374-99e0-16b7d354f592"}}, {"head": {"id": "69ca8e37-5c7d-413e-a88e-24b50dbac304", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077448861100, "endTime": 31077450117800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f2026a99-565e-4ba8-bbef-76f12ef47059", "logId": "04f24cd6-5a54-4476-b15e-514ece6c6af0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04f24cd6-5a54-4476-b15e-514ece6c6af0", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077448861100, "endTime": 31077450117800}, "additional": {"logType": "info", "children": [], "durationId": "69ca8e37-5c7d-413e-a88e-24b50dbac304", "parent": "68caa9fa-9393-4374-99e0-16b7d354f592"}}, {"head": {"id": "dd6ee576-1b83-4795-bb86-7f69a825596f", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077450121900, "endTime": 31077452751200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f2026a99-565e-4ba8-bbef-76f12ef47059", "logId": "59f567a6-ff8b-4a1f-abf8-24430a7b9d97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59f567a6-ff8b-4a1f-abf8-24430a7b9d97", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077450121900, "endTime": 31077452751200}, "additional": {"logType": "info", "children": [], "durationId": "dd6ee576-1b83-4795-bb86-7f69a825596f", "parent": "68caa9fa-9393-4374-99e0-16b7d354f592"}}, {"head": {"id": "0e2e885b-db72-4d3c-83cb-71fb0f2860aa", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077452775900, "endTime": 31077518336500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "5b9b3339-7048-4782-9478-12e4eb0027b9", "logId": "a0f0b8db-03e8-44d5-b759-522a4f4362f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0f0b8db-03e8-44d5-b759-522a4f4362f8", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077452775900, "endTime": 31077518336500}, "additional": {"logType": "info", "children": [], "durationId": "0e2e885b-db72-4d3c-83cb-71fb0f2860aa", "parent": "690f322f-fb9f-49d9-a173-d816b0f3c1ee"}}, {"head": {"id": "1c572e7a-ef36-4815-80bb-485c3b4e9428", "name": "default@CompileArkTS work[60] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077525513700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "690f322f-fb9f-49d9-a173-d816b0f3c1ee", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31074010869500, "endTime": 31077518470300}, "additional": {"logType": "info", "children": ["86044108-e84b-4275-816f-8c5e1da61042", "dbc5c525-c663-4b96-adb2-fe8f6587fbd2", "87aadeb4-036a-4e6d-98ae-a34dc58fd23a", "6bb5f8df-c7de-434e-b6d3-505994135d01", "68caa9fa-9393-4374-99e0-16b7d354f592", "a0f0b8db-03e8-44d5-b759-522a4f4362f8"], "durationId": "5b9b3339-7048-4782-9478-12e4eb0027b9", "parent": "469485b3-eea8-456c-b04f-aa15aa8af802"}}, {"head": {"id": "469485b3-eea8-456c-b04f-aa15aa8af802", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073867358800, "endTime": 31077525694100, "totalTime": 3538579200}, "additional": {"logType": "info", "children": ["690f322f-fb9f-49d9-a173-d816b0f3c1ee", "2772613c-f6cb-4e13-a37b-12d12958a616"], "durationId": "1cdc1f7b-dc2b-4b08-8582-cfede1ae234f"}}, {"head": {"id": "ec572a86-5387-4c9b-80d2-7fe7e3fa0cda", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077531175700, "endTime": 31077532328400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "ed8a6077-b6ba-4c86-bba5-3fed0e908908", "logId": "6d2f2a53-6cf1-407f-a18a-941f8a3d1328"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed8a6077-b6ba-4c86-bba5-3fed0e908908", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077529629400}, "additional": {"logType": "detail", "children": [], "durationId": "ec572a86-5387-4c9b-80d2-7fe7e3fa0cda"}}, {"head": {"id": "4d0c2d50-bb39-4081-8782-6104288c4c00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077530001500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d20bf688-c6f5-443a-affb-759c5f44a1af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077530105100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e1468b3-760b-4cdf-9fd7-323d3dad8530", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077531186000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f47153-225f-4cc3-8b28-f45e42bc7b2a", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077531449200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92758358-4f92-4617-b0f3-faf276197480", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077532167400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899c6242-2aa0-4a2c-bee0-cd2375abccc2", "name": "entry : default@GeneratePkgModuleJson cost memory 0.08103179931640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077532263200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d2f2a53-6cf1-407f-a18a-941f8a3d1328", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077531175700, "endTime": 31077532328400}, "additional": {"logType": "info", "children": [], "durationId": "ec572a86-5387-4c9b-80d2-7fe7e3fa0cda"}}, {"head": {"id": "718be56b-bded-433b-b6db-430703e8c5de", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077541908700, "endTime": 31078098740100}, "additional": {"children": ["5ec4e71e-fba0-41ea-82af-398e49209913", "50221709-b7af-4cff-85a5-a5e3194e0cc0", "4ea9d74e-2d04-44bc-ae97-4f609fe495a6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "18b75b47-f5ef-436c-a68d-50d5883a7103", "logId": "a621fdde-365e-4420-b0c2-b86d26b71699"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18b75b47-f5ef-436c-a68d-50d5883a7103", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077534546900}, "additional": {"logType": "detail", "children": [], "durationId": "718be56b-bded-433b-b6db-430703e8c5de"}}, {"head": {"id": "09b64faf-cb73-487f-86f0-beda15cc99ae", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077534890900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a9f6dd-2c86-44e1-859b-a3ec507b527d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077535020100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae433cb6-2a22-427e-92ed-a184079ff6bb", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077541919200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81d9df62-1d47-46d3-be5f-c733d8a96188", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077552730100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52b5a64-0f20-47d7-9c83-a7f0a9d5d608", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077552857000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3de33a18-91d7-4a00-8774-926efd7a57c3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077553004200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc6c4c7-1db8-4528-8bc2-528a65526f06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077553081000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec4e71e-fba0-41ea-82af-398e49209913", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077554013200, "endTime": 31077555462300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "718be56b-bded-433b-b6db-430703e8c5de", "logId": "5eb4f42d-0f8a-4984-9e37-e234f391c5e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8aeb79c-71be-4a79-abab-72de7a061d9e", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077555283300}, "additional": {"logType": "debug", "children": [], "durationId": "718be56b-bded-433b-b6db-430703e8c5de"}}, {"head": {"id": "5eb4f42d-0f8a-4984-9e37-e234f391c5e5", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077554013200, "endTime": 31077555462300}, "additional": {"logType": "info", "children": [], "durationId": "5ec4e71e-fba0-41ea-82af-398e49209913", "parent": "a621fdde-365e-4420-b0c2-b86d26b71699"}}, {"head": {"id": "50221709-b7af-4cff-85a5-a5e3194e0cc0", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077556205000, "endTime": 31077557503300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "718be56b-bded-433b-b6db-430703e8c5de", "logId": "3caf2255-81fe-492b-a449-d56996ad40f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1bd2ee7-f81c-4821-9c31-f3dc930b0501", "name": "default@PackageHap work[62] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077556778900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea9d74e-2d04-44bc-ae97-4f609fe495a6", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077637491100, "endTime": 31078098423000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "718be56b-bded-433b-b6db-430703e8c5de", "logId": "544476b7-adbb-49e1-96df-6ddd6fa2964e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5700be02-a8f1-4a33-87ba-ec5ad37e61ae", "name": "default@PackageHap work[62] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077557366500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce0e288-0d24-413e-8a37-9f677b2e8cb3", "name": "default@PackageHap work[62] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077557446400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3caf2255-81fe-492b-a449-d56996ad40f2", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077556205000, "endTime": 31077557503300}, "additional": {"logType": "info", "children": [], "durationId": "50221709-b7af-4cff-85a5-a5e3194e0cc0", "parent": "a621fdde-365e-4420-b0c2-b86d26b71699"}}, {"head": {"id": "b429f2fb-df63-414a-b8e4-323a139b96e7", "name": "entry : default@PackageHap cost memory 1.3971328735351562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077561344700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "744f6722-2e8e-4ffe-8fea-871c9d83cb94", "name": "default@PackageHap work[62] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077637380400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d0ec081-b2cf-4520-a30f-ab96da645b96", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077668287200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24beacc4-3f38-4352-9aaa-02edb1676da8", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077668441700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb364b2e-f6cb-4a4d-8ed2-cc2a0fce25a3", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077668506600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3bcf3c4-e268-4617-8dc5-09104af738c9", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077668558700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "335de029-28c2-43fc-b2c4-4ef624a2b7d4", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077668606500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "583960fb-93fe-41a6-9cd3-9c8ddd4c0b03", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077668723000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "294f140c-2136-4a6e-8fcc-36c417ec28d1", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078098501300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97cf6b1c-74e4-4cb5-a30d-36be62df4ca2", "name": "default@PackageHap work[62] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078098649000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "544476b7-adbb-49e1-96df-6ddd6fa2964e", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31077637491100, "endTime": 31078098423000}, "additional": {"logType": "info", "children": [], "durationId": "4ea9d74e-2d04-44bc-ae97-4f609fe495a6", "parent": "a621fdde-365e-4420-b0c2-b86d26b71699"}}, {"head": {"id": "a621fdde-365e-4420-b0c2-b86d26b71699", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31077541908700, "endTime": 31078098740100, "totalTime": 480496900}, "additional": {"logType": "info", "children": ["5eb4f42d-0f8a-4984-9e37-e234f391c5e5", "3caf2255-81fe-492b-a449-d56996ad40f2", "544476b7-adbb-49e1-96df-6ddd6fa2964e"], "durationId": "718be56b-bded-433b-b6db-430703e8c5de"}}, {"head": {"id": "2ed8c7cb-b8d7-4256-b9bd-a49910e8bef0", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078105255400, "endTime": 31078107001900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "6ce8f385-90fd-47cb-85a2-7753210ec8e9", "logId": "284f7b5b-3d51-4657-a2e8-0faa256d7560"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ce8f385-90fd-47cb-85a2-7753210ec8e9", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078101861700}, "additional": {"logType": "detail", "children": [], "durationId": "2ed8c7cb-b8d7-4256-b9bd-a49910e8bef0"}}, {"head": {"id": "b3ba1b75-b06b-46f3-9f40-6f41e741f616", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078102430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8472460b-8a51-4d0d-a422-1dfe3566387b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078102548000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a765a3-d890-4c3f-a198-a87ca595472e", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078105265400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f662e24-4daa-411e-a694-1cfecfb128b7", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078105558200}, "additional": {"logType": "warn", "children": [], "durationId": "2ed8c7cb-b8d7-4256-b9bd-a49910e8bef0"}}, {"head": {"id": "73459694-0eab-40a6-b5c4-098b740d5389", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078106411300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92486340-38dd-4ee1-96d6-98df7084dd58", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078106515600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3add01f-5dbc-47ba-8da8-15632a38c05c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078106601800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df8c3ef7-4f52-4769-a791-94fd0876817f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078106660500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a022b9b-1e03-4cba-a0b0-03832e0222d0", "name": "entry : default@SignHap cost memory 0.11679840087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078106862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843b0eaa-83e1-4c68-a523-742c0e563d0f", "name": "runTaskFromQueue task cost before running: 4 s 836 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078106945800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "284f7b5b-3d51-4657-a2e8-0faa256d7560", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078105255400, "endTime": 31078107001900, "totalTime": 1673500}, "additional": {"logType": "info", "children": [], "durationId": "2ed8c7cb-b8d7-4256-b9bd-a49910e8bef0"}}, {"head": {"id": "73bad330-3f36-49eb-9410-b6f59c5d2cff", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078109928100, "endTime": 31078114408000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "816658b7-fb6e-41a7-9cbb-3c4d96fd5b23", "logId": "d9183cff-9b47-46ce-8c0f-bbb72d1935c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "816658b7-fb6e-41a7-9cbb-3c4d96fd5b23", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078108791100}, "additional": {"logType": "detail", "children": [], "durationId": "73bad330-3f36-49eb-9410-b6f59c5d2cff"}}, {"head": {"id": "f145a082-7d30-4aea-b720-fc1ff06ac1f6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078109153800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7d99c27-e0ab-45d2-b431-c3501246be4a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078109248500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a01db052-04e2-4d70-b51d-ca1982e685be", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078109935700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2cb46c6-9fc2-4938-9f30-ff15a352320d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078114102200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5a9c75-ed4d-40f9-854c-8c54b5af5547", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078114205600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64e392e0-2dda-42c9-880d-23ede4576d44", "name": "entry : default@CollectDebugSymbol cost memory 0.23990631103515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078114279400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995b4b13-05f2-43d2-8e34-95bf19570dda", "name": "runTaskFromQueue task cost before running: 4 s 843 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078114354100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9183cff-9b47-46ce-8c0f-bbb72d1935c3", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078109928100, "endTime": 31078114408000, "totalTime": 4406900}, "additional": {"logType": "info", "children": [], "durationId": "73bad330-3f36-49eb-9410-b6f59c5d2cff"}}, {"head": {"id": "02c989c2-5871-459b-b41c-f21eafbc564e", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078115882600, "endTime": 31078116127500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "adcbde63-388a-42a3-90a8-573c5a69d9fb", "logId": "4bca16a6-dd9c-4a45-873a-a086e37754cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adcbde63-388a-42a3-90a8-573c5a69d9fb", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078115842300}, "additional": {"logType": "detail", "children": [], "durationId": "02c989c2-5871-459b-b41c-f21eafbc564e"}}, {"head": {"id": "f90c6154-5a9a-4678-ad8d-5f6c6c9b29f9", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078115887700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dda3a345-ac86-4b22-8910-0397ed538750", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078115995600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9548d52-7ea9-4e26-a1f5-55f5b5ad4199", "name": "runTaskFromQueue task cost before running: 4 s 845 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078116069800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bca16a6-dd9c-4a45-873a-a086e37754cd", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078115882600, "endTime": 31078116127500, "totalTime": 170200}, "additional": {"logType": "info", "children": [], "durationId": "02c989c2-5871-459b-b41c-f21eafbc564e"}}, {"head": {"id": "ad558c77-bf61-4bff-aad6-5beaa1e3d1f7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078123992600, "endTime": 31078124012400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84861bc5-5235-4c83-98a9-1bffe386e7aa", "logId": "eedebe6c-fdaa-4608-b7fb-6966e3464a13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eedebe6c-fdaa-4608-b7fb-6966e3464a13", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078123992600, "endTime": 31078124012400}, "additional": {"logType": "info", "children": [], "durationId": "ad558c77-bf61-4bff-aad6-5beaa1e3d1f7"}}, {"head": {"id": "56f50ea7-3dca-413a-9cfa-98c648dafcb3", "name": "BUILD SUCCESSFUL in 4 s 853 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078124050300}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "60970d59-0db7-459b-adeb-1dbba6089128", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31073271919500, "endTime": 31078124459700}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 7}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "cee5dd0b-3cae-4b19-b2ec-5ce72fe4918c", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078124494800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "510b0ba0-e73a-41b8-9b82-490e0c847148", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078124568400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d59a35-6317-47c4-802d-977b4b5236c2", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078124628300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c46ca53-3288-4b00-824a-882facd02a80", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078124680400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a18982c-a463-4a7b-8f1b-6589997e1f10", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078124748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c150b4-b47b-4e3a-8351-e3facc94654c", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078125055400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b17d99e-31dd-4936-8902-89303fd2551f", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078125768800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f99ea81-9755-4f23-a9f0-d7c06084156f", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078126000300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7b3650-1e27-40de-9b2e-3c6191224281", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078126069800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8da373ab-2a3f-482a-af3a-4c7819002111", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078126140000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e3eefed-3e5a-41d4-9c22-8d19104848af", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078126389800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01565b0f-c210-4444-996f-fa7955826faa", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078127376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e67e5e-09ee-4198-8fed-2f653be8c9dc", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078127651500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71a1962d-0de7-4a5f-9872-ddac465312b0", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078127760400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d55794e-8a44-4097-92e5-fc7e9402b505", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078127826500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4624e20-e23c-4d47-9d0f-1f33ee5b7b8d", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078127880100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ea3220a-ba02-485a-bda2-df3229b108ba", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078127933000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "970f78a0-4b78-4417-b49a-686c424805b2", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078128211200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba1a606-4e47-452f-bc5a-b51b980a38d0", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078128400100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f27204d-072d-473e-a8cc-5c1d81b1428e", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078128574900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2db5e07-a2c0-4b8d-ae83-63bc5e69ebe3", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078129005500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6763494-d461-42a6-abed-a0c6880a996a", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078129088900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "146ea31b-7455-46d0-95d5-f7f3c9697209", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078129152300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e2549f1-58ec-45f6-a22a-9c4fd983376f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078130969500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8591bba5-8882-4fdc-82d0-deda8375802b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078131490400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6929a7d1-07b9-4e62-b5c0-9a94f353cbf3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078132146900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b0d829c-1d61-4066-a56c-ea407a597650", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078132321400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71370d28-a97b-4aec-85ae-ee045a970f38", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078132498100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f747fe95-a2a5-45f2-8be2-8ebbb9f67304", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078133056000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "869fd39a-aceb-497e-9697-aafbff8e4df3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078133170200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb1053f-aa7a-44aa-bd8b-1a1f850efd79", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078133406900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "318ad105-2895-46db-a005-ffb4d3d8296a", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078133631000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d01064fc-af51-4cea-9c4b-6223be852d58", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078134200000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b9d74e3-0854-4303-920f-245695c9eb89", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078135294600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd6986bf-2e6f-4857-93dd-a06f327a6545", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078135745000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff56eae2-6712-435f-8ff3-4a35a172f511", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078136534200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b711d8-f404-47bf-a779-65d583c84d5e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078136829200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a54f62e-d83b-45bb-9bc6-f5353213ebcf", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078137013500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0022c0a5-0740-4fd0-b641-498a4c1f90dd", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078137600500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b28aec9-8016-4212-a6fe-4962cdf36d11", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078137855900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5be40b8-c7ea-4779-b6ea-5fdef4b9b211", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078137931800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1610d7ff-cad8-42ac-9b4c-52f4fc1749c4", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078137981800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ca081f-8981-4dca-8027-7f077fe79a6c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078139223300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb1931b9-904b-4635-8b4f-21461e04ec3c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078139498900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87fadbda-1ab4-419b-884d-45b849e902aa", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078139836800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a4b864f-912c-4e45-ae63-dc9e3bda6abe", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078145818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d49cf38f-53bb-46c8-ac70-681780477c64", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078146040000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f2c02b-a3f5-4c85-b687-385c9c44be95", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078146300100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1023bf3-9320-4043-bd91-ce13684e7bc3", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078146380900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "463855e3-d1d0-49e8-aa81-c85dda988b07", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078146568500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dbf89b1-90b8-488c-a089-178e518eb93b", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078147260600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99f5e247-56d4-4bb1-b264-dcfea42abb6c", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078149682100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afecc50-c5f9-42a4-90d2-bd0de1bfcfa9", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078149931500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ad66b7d-d5e5-4bb8-8b00-8e4ad056010e", "name": "Incremental task entry:default@PackageHap post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078150178000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b0b338-f551-48e5-9864-a47ac74cd6c4", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078150338100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fab3c096-408a-46a3-9c3f-a3a419df0d76", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078150407700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a6fade-0f30-45f7-8123-28072b84db05", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078150626500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7ce1b0-3c0e-44f2-9bff-a840260948bd", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078152959700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20152ca3-2c06-4226-a055-56cb8e56dda1", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078153325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c3efa4-b4fd-47c5-9d07-646716997ede", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078153662500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6ee9cb8-d0ec-427e-bd9f-b4db311b1b8f", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31078153906400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}