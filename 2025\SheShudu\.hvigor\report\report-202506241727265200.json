{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "0b6f9428-77c8-49ad-9077-d1c24eea5e21", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228875744500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "829e051d-f5ae-45c7-8c70-e713f1df8607", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228975092200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5721909-8599-4d98-95bd-be478db25a55", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32228975564400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00aa443a-a02f-4c4a-a8af-cb90285134e4", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278413429500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278419428100, "endTime": 32278755700300}, "additional": {"children": ["7fa1b724-1f03-4258-93cf-53050986fac7", "36096613-b89a-4530-ba1b-22d4623658d3", "111e5d56-9b7c-4ce3-a751-fb1aa7cbca3f", "218235ec-d088-4ac7-8c8a-0741dabef7ec", "a7f491b6-c041-4bff-88c9-ed3f16bd7e62", "86fd5a5f-aa02-4bcc-a5fd-a3ec43cc9d50", "02474f61-6e97-482e-83b1-9ca29cd4ddc9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a0862775-474a-4577-a439-21bf4cb98e85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fa1b724-1f03-4258-93cf-53050986fac7", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278419430200, "endTime": 32278433091800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "52c698ae-2848-47ba-80c6-e44060c2c1d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36096613-b89a-4530-ba1b-22d4623658d3", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278433118400, "endTime": 32278754268200}, "additional": {"children": ["540ad9d0-0e19-4935-bbfd-e8b3ed416e4a", "5c3c6dfb-12e8-405c-b7f0-bd01195f6bf7", "b54505a8-9355-4f90-ac14-d0a663471e4d", "d3befde1-de9d-41e9-aee7-74a0b4717e3a", "c0b10b48-ddca-4108-b5f7-fb1fcad4dd11", "4baab368-7f9e-48bc-9c6c-2dd115b9dcc7", "b1d60ffe-f250-4087-b3b5-a034d9874191", "be805158-5d0b-4945-a7cf-631080dd67b8", "eb8d8df6-a736-4516-958d-307d813f9dc7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "f348e713-23e0-41a0-80ba-3b85f6645493"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "111e5d56-9b7c-4ce3-a751-fb1aa7cbca3f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278754289600, "endTime": 32278755693100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "11094875-4f8e-4592-80ff-d578d23535ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "218235ec-d088-4ac7-8c8a-0741dabef7ec", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278755696200, "endTime": 32278755697400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "a1dd45e6-b77d-4795-a56d-a43b811cd6fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7f491b6-c041-4bff-88c9-ed3f16bd7e62", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278422086900, "endTime": 32278422173800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "7ed29222-1641-4a8a-ad33-d3be6a0bb6f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ed29222-1641-4a8a-ad33-d3be6a0bb6f5", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278422086900, "endTime": 32278422173800}, "additional": {"logType": "info", "children": [], "durationId": "a7f491b6-c041-4bff-88c9-ed3f16bd7e62", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "86fd5a5f-aa02-4bcc-a5fd-a3ec43cc9d50", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278427533700, "endTime": 32278427555600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "c677f698-afbf-406b-be92-4075cfa802ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c677f698-afbf-406b-be92-4075cfa802ce", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278427533700, "endTime": 32278427555600}, "additional": {"logType": "info", "children": [], "durationId": "86fd5a5f-aa02-4bcc-a5fd-a3ec43cc9d50", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "be4a7178-6ca9-48d4-8315-11224bfe4db3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278427600000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a45b4e4-cf30-43b3-a681-23fb482a1e21", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278432908200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52c698ae-2848-47ba-80c6-e44060c2c1d3", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278419430200, "endTime": 32278433091800}, "additional": {"logType": "info", "children": [], "durationId": "7fa1b724-1f03-4258-93cf-53050986fac7", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "540ad9d0-0e19-4935-bbfd-e8b3ed416e4a", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278439179700, "endTime": 32278439189000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "16a41f20-fb51-4c09-b8ee-0ccbeefe4e78"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c3c6dfb-12e8-405c-b7f0-bd01195f6bf7", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278439212200, "endTime": 32278443402800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "c32c4346-7bf8-4dcc-a239-2c05cfd1bcc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b54505a8-9355-4f90-ac14-d0a663471e4d", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278443419100, "endTime": 32278566119100}, "additional": {"children": ["0b6d6526-9be5-452b-b4cb-085f6c0aeba8", "39539dba-057c-4351-b20e-d28ea3e0ed0d", "3d4f2150-6983-42aa-b8ea-e9b2dd1d1d14"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "2cd1b204-f4d6-4380-9ed0-abb2f698ec36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3befde1-de9d-41e9-aee7-74a0b4717e3a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278566176700, "endTime": 32278600879100}, "additional": {"children": ["2604acdd-900d-4e3e-874a-292d9d03a330"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "e68f31c2-5fde-4438-b4b4-ca80fd4d5ed4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0b10b48-ddca-4108-b5f7-fb1fcad4dd11", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278600888200, "endTime": 32278715739500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "44042fb3-e889-4a4a-a950-9b8cdee963a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4baab368-7f9e-48bc-9c6c-2dd115b9dcc7", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278717007100, "endTime": 32278735868400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "c9aaac96-4c5c-41df-98b7-f951e2242698"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1d60ffe-f250-4087-b3b5-a034d9874191", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278735888600, "endTime": 32278753686400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "3ecaccc9-cbb4-4e18-a07d-c090fe2d12d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be805158-5d0b-4945-a7cf-631080dd67b8", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278753707800, "endTime": 32278754240500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "a6db3fe0-8690-4fad-80b0-33fae45c6fcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16a41f20-fb51-4c09-b8ee-0ccbeefe4e78", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278439179700, "endTime": 32278439189000}, "additional": {"logType": "info", "children": [], "durationId": "540ad9d0-0e19-4935-bbfd-e8b3ed416e4a", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "c32c4346-7bf8-4dcc-a239-2c05cfd1bcc9", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278439212200, "endTime": 32278443402800}, "additional": {"logType": "info", "children": [], "durationId": "5c3c6dfb-12e8-405c-b7f0-bd01195f6bf7", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "0b6d6526-9be5-452b-b4cb-085f6c0aeba8", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278444350200, "endTime": 32278444371200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b54505a8-9355-4f90-ac14-d0a663471e4d", "logId": "14004983-9c5c-4ca6-8431-67c178844710"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14004983-9c5c-4ca6-8431-67c178844710", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278444350200, "endTime": 32278444371200}, "additional": {"logType": "info", "children": [], "durationId": "0b6d6526-9be5-452b-b4cb-085f6c0aeba8", "parent": "2cd1b204-f4d6-4380-9ed0-abb2f698ec36"}}, {"head": {"id": "39539dba-057c-4351-b20e-d28ea3e0ed0d", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278447341200, "endTime": 32278563090900}, "additional": {"children": ["66087c97-0edc-4c30-afa3-4bbc203a624c", "fff77d9a-b72d-40f4-b2fb-e716cc63a3d1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b54505a8-9355-4f90-ac14-d0a663471e4d", "logId": "74fbd305-6d95-4c96-bf01-7c3d4af7aab1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66087c97-0edc-4c30-afa3-4bbc203a624c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278447344400, "endTime": 32278455277500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39539dba-057c-4351-b20e-d28ea3e0ed0d", "logId": "028ba3d2-7450-4a10-97fc-f343037a2f73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fff77d9a-b72d-40f4-b2fb-e716cc63a3d1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278455292200, "endTime": 32278563074400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "39539dba-057c-4351-b20e-d28ea3e0ed0d", "logId": "abdefd04-144d-428f-830c-a99a25e4e22b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50d8c531-5971-4f9e-80b0-2e8fc56d16b8", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278447351400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5158b167-c83b-417f-9b39-b98803b57795", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278455156000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "028ba3d2-7450-4a10-97fc-f343037a2f73", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278447344400, "endTime": 32278455277500}, "additional": {"logType": "info", "children": [], "durationId": "66087c97-0edc-4c30-afa3-4bbc203a624c", "parent": "74fbd305-6d95-4c96-bf01-7c3d4af7aab1"}}, {"head": {"id": "210e050b-d802-4ed2-8723-a12927b46486", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278455304700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0b4e698-a9f8-4893-916f-dcf0a01c077c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278461455700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dafba685-06d5-4a2a-8cd5-29bf30e96d63", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278461586800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5f26f57-737b-4a5d-b728-dd7f27aac2e7", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278461852600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46419030-f325-4010-a45e-a5341e95195b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278461948800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "503a21fb-b539-492a-98ee-bb5bbde69009", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278463723900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb509f01-4e5d-472d-8246-709042c43096", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278469796800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0abd1f3d-ea94-4aab-8990-aad98b67c841", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278479421000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09c84987-a89a-4b75-b93d-cf150e31e269", "name": "Sdk init in 58 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278528497500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983b7fa6-43db-4c0b-b2f7-bdd12343b766", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278528908000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 27}, "markType": "other"}}, {"head": {"id": "b7ec8eb7-d18b-4957-80d3-5fd4adc57cca", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278528953100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 27}, "markType": "other"}}, {"head": {"id": "035f9776-7a90-4e47-9184-85abe0628a52", "name": "Project task initialization takes 32 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278562546300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51f8cc6-6995-4a27-8139-069bb940e2be", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278562842300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cfe6e86-35eb-40fa-a15f-02d526b47463", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278562926800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d015931-a225-4172-a938-a0009503da41", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278563008800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abdefd04-144d-428f-830c-a99a25e4e22b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278455292200, "endTime": 32278563074400}, "additional": {"logType": "info", "children": [], "durationId": "fff77d9a-b72d-40f4-b2fb-e716cc63a3d1", "parent": "74fbd305-6d95-4c96-bf01-7c3d4af7aab1"}}, {"head": {"id": "74fbd305-6d95-4c96-bf01-7c3d4af7aab1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278447341200, "endTime": 32278563090900}, "additional": {"logType": "info", "children": ["028ba3d2-7450-4a10-97fc-f343037a2f73", "abdefd04-144d-428f-830c-a99a25e4e22b"], "durationId": "39539dba-057c-4351-b20e-d28ea3e0ed0d", "parent": "2cd1b204-f4d6-4380-9ed0-abb2f698ec36"}}, {"head": {"id": "3d4f2150-6983-42aa-b8ea-e9b2dd1d1d14", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278565898100, "endTime": 32278565961100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b54505a8-9355-4f90-ac14-d0a663471e4d", "logId": "770c5a8a-168c-4391-9d47-ccb0973b3a49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "770c5a8a-168c-4391-9d47-ccb0973b3a49", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278565898100, "endTime": 32278565961100}, "additional": {"logType": "info", "children": [], "durationId": "3d4f2150-6983-42aa-b8ea-e9b2dd1d1d14", "parent": "2cd1b204-f4d6-4380-9ed0-abb2f698ec36"}}, {"head": {"id": "2cd1b204-f4d6-4380-9ed0-abb2f698ec36", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278443419100, "endTime": 32278566119100}, "additional": {"logType": "info", "children": ["14004983-9c5c-4ca6-8431-67c178844710", "74fbd305-6d95-4c96-bf01-7c3d4af7aab1", "770c5a8a-168c-4391-9d47-ccb0973b3a49"], "durationId": "b54505a8-9355-4f90-ac14-d0a663471e4d", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "2604acdd-900d-4e3e-874a-292d9d03a330", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278567697700, "endTime": 32278600863400}, "additional": {"children": ["88fbd517-856d-4ebe-84f3-5e18abc0dc92", "f2221e3a-1d0a-414d-8aa1-bb0af4a91d46", "ab314716-e17b-483c-af0f-093c84fcbc41"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d3befde1-de9d-41e9-aee7-74a0b4717e3a", "logId": "75da87db-37f7-4172-afdf-9b158c1ca6d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88fbd517-856d-4ebe-84f3-5e18abc0dc92", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278574187900, "endTime": 32278574207000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2604acdd-900d-4e3e-874a-292d9d03a330", "logId": "a9e24fb4-71f9-47d1-a4aa-17b7c3eb712a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9e24fb4-71f9-47d1-a4aa-17b7c3eb712a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278574187900, "endTime": 32278574207000}, "additional": {"logType": "info", "children": [], "durationId": "88fbd517-856d-4ebe-84f3-5e18abc0dc92", "parent": "75da87db-37f7-4172-afdf-9b158c1ca6d9"}}, {"head": {"id": "f2221e3a-1d0a-414d-8aa1-bb0af4a91d46", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278577169600, "endTime": 32278598627200}, "additional": {"children": ["15ed8b9c-8e87-4b10-a539-b386efbe04d2", "03a688be-6521-4098-a3c6-d122310c73c2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2604acdd-900d-4e3e-874a-292d9d03a330", "logId": "880114b1-0bc4-4bc3-80da-204ad9fd4ab8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15ed8b9c-8e87-4b10-a539-b386efbe04d2", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278577172100, "endTime": 32278583953400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2221e3a-1d0a-414d-8aa1-bb0af4a91d46", "logId": "93566e8f-d132-4211-8045-691456cfd007"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03a688be-6521-4098-a3c6-d122310c73c2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278583977100, "endTime": 32278598617900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f2221e3a-1d0a-414d-8aa1-bb0af4a91d46", "logId": "0eb5d2eb-dedb-4b70-b705-bf0472a22d18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ef8f9202-3c52-4417-b368-473c869760ba", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278577177700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9f3934-a820-4d46-9f6c-d1a3b6738b38", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278583788500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93566e8f-d132-4211-8045-691456cfd007", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278577172100, "endTime": 32278583953400}, "additional": {"logType": "info", "children": [], "durationId": "15ed8b9c-8e87-4b10-a539-b386efbe04d2", "parent": "880114b1-0bc4-4bc3-80da-204ad9fd4ab8"}}, {"head": {"id": "d9fab4f9-a3ad-4927-82d4-5f58e4997697", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278583988700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552df9b5-b96e-40c8-8fea-785f9ff29522", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278592085600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feecffbf-79a9-49e3-8aca-cb5aac6e80f8", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278592476400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "321cf568-c86f-462d-bc1d-4ccefbb11f15", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278594102300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8621551c-9df0-4523-9b11-424afdf2f255", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278594305500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2571f7ad-8727-408b-bc0d-173ca937e380", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278594370300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3622f877-e7f4-40d6-acb1-8ab19ba505cd", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278594416700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "741e3116-956c-449b-9a74-02e10fa1c8fa", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278594466000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54e7ad7-740d-482f-b165-9bfb967ab4f2", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278597875800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80801b5-fd0b-49ae-8e80-96612ab768a6", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278598279300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaf13e1e-f8f5-4aa0-8991-401e6148a068", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278598493000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89ff67d3-7739-415a-a4d4-7f1ad9123b53", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278598560100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eb5d2eb-dedb-4b70-b705-bf0472a22d18", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278583977100, "endTime": 32278598617900}, "additional": {"logType": "info", "children": [], "durationId": "03a688be-6521-4098-a3c6-d122310c73c2", "parent": "880114b1-0bc4-4bc3-80da-204ad9fd4ab8"}}, {"head": {"id": "880114b1-0bc4-4bc3-80da-204ad9fd4ab8", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278577169600, "endTime": 32278598627200}, "additional": {"logType": "info", "children": ["93566e8f-d132-4211-8045-691456cfd007", "0eb5d2eb-dedb-4b70-b705-bf0472a22d18"], "durationId": "f2221e3a-1d0a-414d-8aa1-bb0af4a91d46", "parent": "75da87db-37f7-4172-afdf-9b158c1ca6d9"}}, {"head": {"id": "ab314716-e17b-483c-af0f-093c84fcbc41", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278600821000, "endTime": 32278600847000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2604acdd-900d-4e3e-874a-292d9d03a330", "logId": "46d16d80-0b37-460a-a0fe-c0506a062a29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "46d16d80-0b37-460a-a0fe-c0506a062a29", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278600821000, "endTime": 32278600847000}, "additional": {"logType": "info", "children": [], "durationId": "ab314716-e17b-483c-af0f-093c84fcbc41", "parent": "75da87db-37f7-4172-afdf-9b158c1ca6d9"}}, {"head": {"id": "75da87db-37f7-4172-afdf-9b158c1ca6d9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278567697700, "endTime": 32278600863400}, "additional": {"logType": "info", "children": ["a9e24fb4-71f9-47d1-a4aa-17b7c3eb712a", "880114b1-0bc4-4bc3-80da-204ad9fd4ab8", "46d16d80-0b37-460a-a0fe-c0506a062a29"], "durationId": "2604acdd-900d-4e3e-874a-292d9d03a330", "parent": "e68f31c2-5fde-4438-b4b4-ca80fd4d5ed4"}}, {"head": {"id": "e68f31c2-5fde-4438-b4b4-ca80fd4d5ed4", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278566176700, "endTime": 32278600879100}, "additional": {"logType": "info", "children": ["75da87db-37f7-4172-afdf-9b158c1ca6d9"], "durationId": "d3befde1-de9d-41e9-aee7-74a0b4717e3a", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "7e9243c0-cabd-4c91-bc50-ce0b208c1d0a", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278635996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6077edae-648a-4142-aaa0-cd372b18085b", "name": "hvigorfile, resolve hvigorfile dependencies in 115 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278715602900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44042fb3-e889-4a4a-a950-9b8cdee963a9", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278600888200, "endTime": 32278715739500}, "additional": {"logType": "info", "children": [], "durationId": "c0b10b48-ddca-4108-b5f7-fb1fcad4dd11", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "eb8d8df6-a736-4516-958d-307d813f9dc7", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278716810200, "endTime": 32278716997300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "36096613-b89a-4530-ba1b-22d4623658d3", "logId": "c6e91723-aa09-498d-ab2c-51d412761092"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b95a2a57-910b-4f73-a091-f59477d135f1", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278716834500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e91723-aa09-498d-ab2c-51d412761092", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278716810200, "endTime": 32278716997300}, "additional": {"logType": "info", "children": [], "durationId": "eb8d8df6-a736-4516-958d-307d813f9dc7", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "35459ec0-e6fa-4b9f-9451-723961dd7a47", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278718017700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02945b28-0b6d-4c8d-9d92-dbeeee3196ed", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278725371600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9aaac96-4c5c-41df-98b7-f951e2242698", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278717007100, "endTime": 32278735868400}, "additional": {"logType": "info", "children": [], "durationId": "4baab368-7f9e-48bc-9c6c-2dd115b9dcc7", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "d3f8fd37-7ea7-431d-bdb7-cd211ef9a02b", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278742169100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ba64cd7-4271-47b8-9cd2-02f816626cbe", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278742304400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5137ea5-f892-44ee-b7bc-071921988feb", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278746271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cd3722-abd1-47c2-beee-d2f64692b31b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278746386900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ecaccc9-cbb4-4e18-a07d-c090fe2d12d1", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278735888600, "endTime": 32278753686400}, "additional": {"logType": "info", "children": [], "durationId": "b1d60ffe-f250-4087-b3b5-a034d9874191", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "e256e81b-da63-4555-b9f4-89a9193449c3", "name": "Configuration phase cost:315 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278754014300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6db3fe0-8690-4fad-80b0-33fae45c6fcb", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278753707800, "endTime": 32278754240500}, "additional": {"logType": "info", "children": [], "durationId": "be805158-5d0b-4945-a7cf-631080dd67b8", "parent": "f348e713-23e0-41a0-80ba-3b85f6645493"}}, {"head": {"id": "f348e713-23e0-41a0-80ba-3b85f6645493", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278433118400, "endTime": 32278754268200}, "additional": {"logType": "info", "children": ["16a41f20-fb51-4c09-b8ee-0ccbeefe4e78", "c32c4346-7bf8-4dcc-a239-2c05cfd1bcc9", "2cd1b204-f4d6-4380-9ed0-abb2f698ec36", "e68f31c2-5fde-4438-b4b4-ca80fd4d5ed4", "44042fb3-e889-4a4a-a950-9b8cdee963a9", "c9aaac96-4c5c-41df-98b7-f951e2242698", "3ecaccc9-cbb4-4e18-a07d-c090fe2d12d1", "a6db3fe0-8690-4fad-80b0-33fae45c6fcb", "c6e91723-aa09-498d-ab2c-51d412761092"], "durationId": "36096613-b89a-4530-ba1b-22d4623658d3", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "02474f61-6e97-482e-83b1-9ca29cd4ddc9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278755669000, "endTime": 32278755685400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3", "logId": "1df0d8bd-4a09-425c-a17c-1e994f9a8907"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1df0d8bd-4a09-425c-a17c-1e994f9a8907", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278755669000, "endTime": 32278755685400}, "additional": {"logType": "info", "children": [], "durationId": "02474f61-6e97-482e-83b1-9ca29cd4ddc9", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "11094875-4f8e-4592-80ff-d578d23535ed", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278754289600, "endTime": 32278755693100}, "additional": {"logType": "info", "children": [], "durationId": "111e5d56-9b7c-4ce3-a751-fb1aa7cbca3f", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "a1dd45e6-b77d-4795-a56d-a43b811cd6fe", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278755696200, "endTime": 32278755697400}, "additional": {"logType": "info", "children": [], "durationId": "218235ec-d088-4ac7-8c8a-0741dabef7ec", "parent": "a0862775-474a-4577-a439-21bf4cb98e85"}}, {"head": {"id": "a0862775-474a-4577-a439-21bf4cb98e85", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278419428100, "endTime": 32278755700300}, "additional": {"logType": "info", "children": ["52c698ae-2848-47ba-80c6-e44060c2c1d3", "f348e713-23e0-41a0-80ba-3b85f6645493", "11094875-4f8e-4592-80ff-d578d23535ed", "a1dd45e6-b77d-4795-a56d-a43b811cd6fe", "7ed29222-1641-4a8a-ad33-d3be6a0bb6f5", "c677f698-afbf-406b-be92-4075cfa802ce", "1df0d8bd-4a09-425c-a17c-1e994f9a8907"], "durationId": "947fa29b-8ef5-4f9c-83bc-04d37f7622d3"}}, {"head": {"id": "8a3c6bef-f4d9-476a-97c8-6ca3905fa018", "name": "Configuration task cost before running: 340 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278755850100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db5109a0-2246-44d6-bb69-214e2479d38a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278786585000, "endTime": 32278796942300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ba151df2-4523-47ce-a14e-6310de3c37c6", "logId": "1eb15d0c-b93b-4f4f-9e70-8f7a2eaf94c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba151df2-4523-47ce-a14e-6310de3c37c6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278757524200}, "additional": {"logType": "detail", "children": [], "durationId": "db5109a0-2246-44d6-bb69-214e2479d38a"}}, {"head": {"id": "0d9f89d3-5042-444e-ab46-43df6033022f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278767530000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17d75fe7-9ca6-485a-bb09-e761820ead45", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278767857700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19f763b9-e5d3-41fe-8501-1c5f35b2691f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278786754300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d67cce9-f874-465e-97ff-149990814918", "name": "Incremental task entry:default@PreBuild pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278796746900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae833365-500d-4477-bf57-2d65c5c22536", "name": "entry : default@PreBuild cost memory 0.31528472900390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278796876700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eb15d0c-b93b-4f4f-9e70-8f7a2eaf94c8", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278786585000, "endTime": 32278796942300}, "additional": {"logType": "info", "children": [], "durationId": "db5109a0-2246-44d6-bb69-214e2479d38a"}}, {"head": {"id": "20e065b0-c8e2-4d61-9e7c-aea8ae24a027", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278804199900, "endTime": 32278808058100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "341ec847-199f-4e3d-ad55-ac7186972b73", "logId": "8cc72713-9bbb-46e0-be3b-9d1c088c71db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "341ec847-199f-4e3d-ad55-ac7186972b73", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278802619400}, "additional": {"logType": "detail", "children": [], "durationId": "20e065b0-c8e2-4d61-9e7c-aea8ae24a027"}}, {"head": {"id": "e8402b11-5550-4e1d-ade5-23e515564873", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278803034900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0204bd9e-dcf2-4209-b73f-753fcb0ea111", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278803141900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf09a52-83a1-4189-83f6-74631351abb7", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278804212500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aae40a42-22a6-450d-aa46-07d1359d9760", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278805053200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "966dd266-187e-4c30-b6a9-c8ae527d7c8f", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278807545400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec225ac-19bd-472e-a452-beb0d1354309", "name": "entry : default@GenerateMetadata cost memory 0.0932769775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278807963400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cc72713-9bbb-46e0-be3b-9d1c088c71db", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278804199900, "endTime": 32278808058100}, "additional": {"logType": "info", "children": [], "durationId": "20e065b0-c8e2-4d61-9e7c-aea8ae24a027"}}, {"head": {"id": "71f0d4d1-945f-4e06-8fcf-1633efb7050f", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278820598500, "endTime": 32278823629600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "04c65c7c-5010-4a98-abca-0b8efeeb62a5", "logId": "8532ad16-0a8a-41f6-850d-ff4d72fe5a87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "04c65c7c-5010-4a98-abca-0b8efeeb62a5", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278819517700}, "additional": {"logType": "detail", "children": [], "durationId": "71f0d4d1-945f-4e06-8fcf-1633efb7050f"}}, {"head": {"id": "46855ffd-2c22-4f26-b041-8f57e6bc7d1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278820125500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7cbb2d-7979-4817-bfc6-e6596ca5e4a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278820236000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a91992c-5a18-429c-8016-c45b1b8fcd39", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278820651000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d9095c6-4765-41c2-8de8-4fbba575fc19", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278821907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f56c5d5e-51c3-45d9-9ea6-5c269a15c924", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278822015400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9cb3cb-8579-4575-b3c2-64ddf4b9c67d", "name": "entry : default@ConfigureCmake cost memory 0.0359344482421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278822395400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcebbe34-eed7-4c07-8e6b-173a133b1434", "name": "runTaskFromQueue task cost before running: 407 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278823294100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8532ad16-0a8a-41f6-850d-ff4d72fe5a87", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278820598500, "endTime": 32278823629600, "totalTime": 2552900}, "additional": {"logType": "info", "children": [], "durationId": "71f0d4d1-945f-4e06-8fcf-1633efb7050f"}}, {"head": {"id": "afaf576c-6269-4401-81be-f6be79ef596e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278833827400, "endTime": 32278839767100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a28a0a0b-a265-4fb8-b621-e97fcdaa1e51", "logId": "310110fa-630d-4a56-89cc-7d15aad27f36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a28a0a0b-a265-4fb8-b621-e97fcdaa1e51", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278828360200}, "additional": {"logType": "detail", "children": [], "durationId": "afaf576c-6269-4401-81be-f6be79ef596e"}}, {"head": {"id": "8aba730e-77a7-43e5-b2c3-9e49fef83199", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278829189500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9d9c77-afa9-449b-bddf-62bf3a73edf3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278829346400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cf57c12-b382-4bb9-9488-2cb4b84752b6", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278833846600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4828e67e-6b0f-4fea-8e80-91ac71acaea0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278839469000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc8d1686-aa42-47b5-bc8e-95bf0129f99c", "name": "entry : default@MergeProfile cost memory 0.1051025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278839692700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "310110fa-630d-4a56-89cc-7d15aad27f36", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278833827400, "endTime": 32278839767100}, "additional": {"logType": "info", "children": [], "durationId": "afaf576c-6269-4401-81be-f6be79ef596e"}}, {"head": {"id": "e5b466ac-27e4-49bb-bdeb-633ea5ec0c0d", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278845385300, "endTime": 32278847385500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e2f375f4-562c-45c9-8635-4a6140213995", "logId": "1e40913c-7c7a-4c71-af86-8f603d97ad57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2f375f4-562c-45c9-8635-4a6140213995", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278843665600}, "additional": {"logType": "detail", "children": [], "durationId": "e5b466ac-27e4-49bb-bdeb-633ea5ec0c0d"}}, {"head": {"id": "55abd77d-4b12-49dd-b473-3eb82d6c3dbb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278844059100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e0e44d-5ab1-4f65-bdd4-81c368ae4597", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278844418200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fda3c84c-1218-4963-b612-e4da2c93cfe7", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278845397200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce441fa-b826-420e-b677-c218b3e53279", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278846259500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6548aa20-2053-49ed-bfca-808a30da05cb", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278847218100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12fc51e2-2eae-4579-9b88-5b03ecd966b3", "name": "entry : default@CreateBuildProfile cost memory 0.1008453369140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278847318800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e40913c-7c7a-4c71-af86-8f603d97ad57", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278845385300, "endTime": 32278847385500}, "additional": {"logType": "info", "children": [], "durationId": "e5b466ac-27e4-49bb-bdeb-633ea5ec0c0d"}}, {"head": {"id": "588a6c0f-9b49-45ca-864d-22f4844cf084", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278852931900, "endTime": 32278853397000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "c7695c4f-9b8c-4414-81d6-54e2d46f052c", "logId": "76485d4a-cb4a-4e7e-baa4-c881e24bfeaa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c7695c4f-9b8c-4414-81d6-54e2d46f052c", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278851429100}, "additional": {"logType": "detail", "children": [], "durationId": "588a6c0f-9b49-45ca-864d-22f4844cf084"}}, {"head": {"id": "1d73fdc9-df0f-4f17-b0db-57e9627f1c08", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278851884800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db41a7eb-f941-42f3-bb45-538920d21704", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278851991900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a79a6bf-aa44-4cea-b101-d17f4fd80d47", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278852942700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785c355e-ae45-4076-9a23-d4c6cebff9c4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278853085400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bc22b40-17c8-4016-aae4-3d48a61f6908", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278853178000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f8d2b85-57b7-4164-9673-3e351df1199c", "name": "entry : default@PreCheckSyscap cost memory 0.03615570068359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278853263100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04ba5fe6-d2d5-4739-b016-d85b9bbc7ece", "name": "runTaskFromQueue task cost before running: 437 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278853337700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76485d4a-cb4a-4e7e-baa4-c881e24bfeaa", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278852931900, "endTime": 32278853397000, "totalTime": 386100}, "additional": {"logType": "info", "children": [], "durationId": "588a6c0f-9b49-45ca-864d-22f4844cf084"}}, {"head": {"id": "cdf69826-813f-4a3d-891f-4e27b394e780", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278863193400, "endTime": 32278864191600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "26655a05-e5c4-4aa3-8e7d-f8a4a4471451", "logId": "0664dfa2-73ba-4d31-b5d4-175574c5aab2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "26655a05-e5c4-4aa3-8e7d-f8a4a4471451", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278856290500}, "additional": {"logType": "detail", "children": [], "durationId": "cdf69826-813f-4a3d-891f-4e27b394e780"}}, {"head": {"id": "a7297963-cb6b-46a6-8e83-a79fe7d93ed2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278856779600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73b7bca6-40b9-48fa-a04d-2f5d5dd2d01c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278856891600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819c2b10-71dc-4a09-a250-c21bf0c76f69", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278863206200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f105b03c-5c9c-48f8-a0e0-7bda7e470ec2", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278863544700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd9220e0-fdff-4223-9ac9-ad9b70f0098c", "name": "entry : default@GeneratePkgContextInfo cost memory 0.038482666015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278863975600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1367433-d51a-4962-b5be-b1270a7d3188", "name": "runTaskFromQueue task cost before running: 448 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278864090700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0664dfa2-73ba-4d31-b5d4-175574c5aab2", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278863193400, "endTime": 32278864191600, "totalTime": 876700}, "additional": {"logType": "info", "children": [], "durationId": "cdf69826-813f-4a3d-891f-4e27b394e780"}}, {"head": {"id": "bc74f421-8420-46aa-8b61-0853e46661de", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278870910000, "endTime": 32278877100800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "cf217693-33d5-4ba5-97a8-ee69faaf9bc4", "logId": "b4c08cde-1f9a-4ae5-841a-ecb531efa223"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf217693-33d5-4ba5-97a8-ee69faaf9bc4", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278868040700}, "additional": {"logType": "detail", "children": [], "durationId": "bc74f421-8420-46aa-8b61-0853e46661de"}}, {"head": {"id": "fcc2a97f-5a85-4885-8f8c-19df82e3beb3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278868577400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a8a2a0-7464-4625-9322-468a5a08f826", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278868910400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6d2d7fa-01f0-4f83-bc81-6a13669116b5", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278870924900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf80c5e-5423-4b79-909e-c25bfcb5ec79", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278875801100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e58d2a5b-5962-4112-9779-beb2510b7d18", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278876069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfa39bc5-ab04-4e29-a0c2-559234d5466d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278876210200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb1c461-7924-4ead-95f7-6342f46540ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278876384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9e0a23-aab6-4293-8c91-b6ae8e95f3f8", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11795806884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278876933500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e719824-c67f-4bd1-9849-cfa18a5a796d", "name": "runTaskFromQueue task cost before running: 461 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278877038000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4c08cde-1f9a-4ae5-841a-ecb531efa223", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278870910000, "endTime": 32278877100800, "totalTime": 6108200}, "additional": {"logType": "info", "children": [], "durationId": "bc74f421-8420-46aa-8b61-0853e46661de"}}, {"head": {"id": "d8873ccb-3cd5-4598-9aec-25adefe50dbd", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278886176400, "endTime": 32278890768200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ceb45f42-45f2-431a-83cd-c4caad9c44de", "logId": "67df03b5-c0a4-4ce5-8a11-00355f80fd9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ceb45f42-45f2-431a-83cd-c4caad9c44de", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278880973400}, "additional": {"logType": "detail", "children": [], "durationId": "d8873ccb-3cd5-4598-9aec-25adefe50dbd"}}, {"head": {"id": "e51ff85d-0693-40c7-a198-79591f91579a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278883055100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afb76d1c-3c9f-48a0-b4f8-eeace249e08f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278883740900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b04435b-f981-4358-9f98-3525f5e53eff", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278886194500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ccdc95c-4a6b-4470-8d92-ba0420ca6f71", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278886467500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c04639d-fa11-4f74-bc6b-366096e3ee01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278887461200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd739766-8ded-41a8-a6da-441c63fe1373", "name": "entry : default@BuildNativeWithCmake cost memory 0.0369873046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278889642600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f27ac9cb-b9c5-4125-9cdc-0efd47d31809", "name": "runTaskFromQueue task cost before running: 474 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278890255700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67df03b5-c0a4-4ce5-8a11-00355f80fd9b", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278886176400, "endTime": 32278890768200, "totalTime": 3917600}, "additional": {"logType": "info", "children": [], "durationId": "d8873ccb-3cd5-4598-9aec-25adefe50dbd"}}, {"head": {"id": "e416f63e-dcb3-497b-99c3-fe560260b641", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278899499400, "endTime": 32278904678500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0b5ab426-5e7a-48dd-8b3c-edfc84bdbada", "logId": "95735c58-cfcd-44cf-b7a6-2e5967f80826"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b5ab426-5e7a-48dd-8b3c-edfc84bdbada", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278894583500}, "additional": {"logType": "detail", "children": [], "durationId": "e416f63e-dcb3-497b-99c3-fe560260b641"}}, {"head": {"id": "df25685d-b8e6-4e2c-a5cd-d0ec41c9ba47", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278896259900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f1a559a-41f1-4144-9eca-3d174e116249", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278896447800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f896f0-6e51-4e55-9669-a9853844abb3", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278899512300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bbec4e4-b929-40fb-9c29-d1db44a81075", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278904004500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e811045c-4752-4ca3-a877-b614d1739d71", "name": "entry : default@MakePackInfo cost memory 0.138214111328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278904166600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95735c58-cfcd-44cf-b7a6-2e5967f80826", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278899499400, "endTime": 32278904678500}, "additional": {"logType": "info", "children": [], "durationId": "e416f63e-dcb3-497b-99c3-fe560260b641"}}, {"head": {"id": "cebcbabc-8039-45ec-8bea-5dab8577e5c3", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278909953400, "endTime": 32278913833200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "690d9255-f262-4aa9-aa88-50d04b473a4b", "logId": "1f28fe11-4761-4218-a41e-b94a72215bb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "690d9255-f262-4aa9-aa88-50d04b473a4b", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278907850500}, "additional": {"logType": "detail", "children": [], "durationId": "cebcbabc-8039-45ec-8bea-5dab8577e5c3"}}, {"head": {"id": "29d2097b-5f06-4a32-95bd-679a5c8cae40", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278908302700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381b4585-f74b-4654-8b84-7017c1a32c2b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278908427000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8693a6df-c449-4eff-b819-0526795d3bdf", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278909964500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8210ddf4-376b-4e1a-b0b6-2b62f5dbe34f", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278910110400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db71cb9b-ee8e-47fb-aeba-aff14b7d6359", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278910837800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af2bbc9b-335b-4b3a-aeac-104997f5f615", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278912739200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cef988-4da0-4055-8213-ed2977bd12f5", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278913020400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "177990b2-7a84-43e8-985a-0e7c9497d2f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278913146700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cf0cf74-22fd-415c-8c3b-af200738f619", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278913269300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed28a8d-004f-4386-8204-2c6b058987f4", "name": "entry : default@SyscapTransform cost memory 0.15027618408203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278913505400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c82db7-de5f-4331-87c4-db9bfd3f47cc", "name": "runTaskFromQueue task cost before running: 498 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278913593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f28fe11-4761-4218-a41e-b94a72215bb0", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278909953400, "endTime": 32278913833200, "totalTime": 3620300}, "additional": {"logType": "info", "children": [], "durationId": "cebcbabc-8039-45ec-8bea-5dab8577e5c3"}}, {"head": {"id": "c69ac6db-0625-4089-ba7b-1b2fc69d2bc0", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278921062000, "endTime": 32278922322100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "13d2cad6-a6ac-4205-9695-798c786c3c6f", "logId": "be94252b-8d51-4854-9cf3-6d23c0873d81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13d2cad6-a6ac-4205-9695-798c786c3c6f", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278919015700}, "additional": {"logType": "detail", "children": [], "durationId": "c69ac6db-0625-4089-ba7b-1b2fc69d2bc0"}}, {"head": {"id": "b9019214-ca0d-4d18-879e-7b955f2d29e6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278919557500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed83bd8f-37df-4d00-a89a-d8b32da0a69f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278919832900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4399e3c0-f5ca-43f5-bfda-bfc344e1819c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278921073200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3746448a-aac6-46d4-8789-1fcde1ad8ae3", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278922089000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e256617a-e168-449e-b68a-6a87b9cdd453", "name": "entry : default@ProcessProfile cost memory 0.05944061279296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278922249100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be94252b-8d51-4854-9cf3-6d23c0873d81", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278921062000, "endTime": 32278922322100}, "additional": {"logType": "info", "children": [], "durationId": "c69ac6db-0625-4089-ba7b-1b2fc69d2bc0"}}, {"head": {"id": "a3643214-aeed-4774-ac36-e155b58a63a2", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278927313000, "endTime": 32278932716400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b19bc69f-24be-4a49-a64a-bb2748764af4", "logId": "c4655fd6-b121-4c86-bfc3-a3c55e33c5b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b19bc69f-24be-4a49-a64a-bb2748764af4", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278924957000}, "additional": {"logType": "detail", "children": [], "durationId": "a3643214-aeed-4774-ac36-e155b58a63a2"}}, {"head": {"id": "55e2c4ad-c3bc-4ced-a234-04a3f492b4c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278925352900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf21e18c-037d-4538-a29c-3a9da915b8e0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278925450700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a30a9c4-5190-4e26-abb5-eedf0dc07bb6", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278927326000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7539fa1-65d9-4f40-a902-3a0fe28a5874", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278932362000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a8aba21-5a90-40bb-a577-82cdb92b84f1", "name": "entry : default@ProcessRouterMap cost memory 0.20121002197265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278932602400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4655fd6-b121-4c86-bfc3-a3c55e33c5b3", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278927313000, "endTime": 32278932716400}, "additional": {"logType": "info", "children": [], "durationId": "a3643214-aeed-4774-ac36-e155b58a63a2"}}, {"head": {"id": "148075dc-bfb3-4169-a267-af190d4714c9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278938322500, "endTime": 32278939909600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "60f626b7-3fbd-4482-92d3-f280e3cd497c", "logId": "c2f5311d-9ed7-46f1-8d53-8c7e2d33f083"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60f626b7-3fbd-4482-92d3-f280e3cd497c", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278936452900}, "additional": {"logType": "detail", "children": [], "durationId": "148075dc-bfb3-4169-a267-af190d4714c9"}}, {"head": {"id": "84c94d81-8050-4fdb-927a-f03bdd6bf7bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278936955000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ef476b9-b905-4300-8a1d-fc809c23960c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278937056600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b828981f-5017-4c4b-afd0-4c950f99ee03", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278938334600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76f7cd6e-a829-4005-b97a-e2a703639b9c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278938579100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ef0da43-7adc-4a38-a77e-1cac17c3fcdb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278938692800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49500198-8729-4353-887f-f0dbf059dc34", "name": "entry : default@BuildNativeWithNinja cost memory 0.0567169189453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278939696700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d255ab-5812-45dc-a685-15e238e6bc56", "name": "runTaskFromQueue task cost before running: 524 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278939840100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2f5311d-9ed7-46f1-8d53-8c7e2d33f083", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278938322500, "endTime": 32278939909600, "totalTime": 1482000}, "additional": {"logType": "info", "children": [], "durationId": "148075dc-bfb3-4169-a267-af190d4714c9"}}, {"head": {"id": "7095f1e0-788f-4ad4-9096-07f1274ac604", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278945737700, "endTime": 32278954711500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "247fd48f-8189-4bc8-885b-ac7ed4eb3b24", "logId": "fbe5c498-f7d9-4dcb-9332-38177377469c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "247fd48f-8189-4bc8-885b-ac7ed4eb3b24", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278942907000}, "additional": {"logType": "detail", "children": [], "durationId": "7095f1e0-788f-4ad4-9096-07f1274ac604"}}, {"head": {"id": "344b5c6e-04f7-4ce3-864c-38f9cc93e185", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278943500200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c01c54-e3b3-492a-87d9-628db6281d1d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278943659000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c17b691-c8a0-40a0-911c-e6a99ea36f03", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278944599300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f306c804-7e8a-42be-a8ac-461a2936dede", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278947104000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efe88593-eefd-4eee-8949-29d45cd1230e", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278950790200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e19852-81b6-4462-aeb2-f06a8563021a", "name": "entry : default@ProcessResource cost memory 0.1693115234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278950920500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbe5c498-f7d9-4dcb-9332-38177377469c", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278945737700, "endTime": 32278954711500}, "additional": {"logType": "info", "children": [], "durationId": "7095f1e0-788f-4ad4-9096-07f1274ac604"}}, {"head": {"id": "8f852a50-2d73-46bb-96ce-b9f8a4881526", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278967037700, "endTime": 32278991516100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "53dbc726-ce9f-4b10-931a-bbadbb99c445", "logId": "5db3b39f-55a4-4aa3-b17d-f316983de113"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53dbc726-ce9f-4b10-931a-bbadbb99c445", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278959068800}, "additional": {"logType": "detail", "children": [], "durationId": "8f852a50-2d73-46bb-96ce-b9f8a4881526"}}, {"head": {"id": "a0dfa790-1ddc-4f6a-b2cf-65dbb8fe72a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278959781400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee6fa287-7012-46a7-b9d1-1beb2b48e15a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278959890400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba3fff51-8f9f-4d01-b680-354a900f5325", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278967054700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7a1d42-4c52-433d-9b19-00e29a199c15", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278991326700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f1324b9-b775-4bde-a838-41bb6494908c", "name": "entry : default@GenerateLoaderJson cost memory 0.7670440673828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278991451100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db3b39f-55a4-4aa3-b17d-f316983de113", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278967037700, "endTime": 32278991516100}, "additional": {"logType": "info", "children": [], "durationId": "8f852a50-2d73-46bb-96ce-b9f8a4881526"}}, {"head": {"id": "bb36ce0a-cdbb-42fe-a403-e548480cd129", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279001150000, "endTime": 32279004423600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "496e69ef-ac1b-4ec6-89b4-110ce086422d", "logId": "c90b0af5-67e5-47d5-b02d-16e0282dd24c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "496e69ef-ac1b-4ec6-89b4-110ce086422d", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278999666600}, "additional": {"logType": "detail", "children": [], "durationId": "bb36ce0a-cdbb-42fe-a403-e548480cd129"}}, {"head": {"id": "48c6e476-595b-44d3-93bc-d902005712b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279000241600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "819d0982-7e5c-4078-ae98-c7b1650c7832", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279000404300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ebfb6c5-5884-4808-82fc-9ce83a5433a6", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279001164900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc67aaa6-0f25-46c5-866b-23297e6ff500", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279003172400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "033d3abf-def1-4044-a249-0bafc2e2c9b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279003360800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5808d3c1-c708-44dc-953e-5d02cb1147c5", "name": "entry : default@ProcessLibs cost memory 0.1252288818359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279004147400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5107a2d-1703-4b86-8582-67eb6d6f408e", "name": "runTaskFromQueue task cost before running: 588 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279004318700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c90b0af5-67e5-47d5-b02d-16e0282dd24c", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279001150000, "endTime": 32279004423600, "totalTime": 3101800}, "additional": {"logType": "info", "children": [], "durationId": "bb36ce0a-cdbb-42fe-a403-e548480cd129"}}, {"head": {"id": "829f40cc-26ad-470e-9a47-29536fd21145", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279011921800, "endTime": 32279037464700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "80284264-5927-41c3-8bf8-de9a48dae16d", "logId": "c1ce5597-8116-4b17-b68a-2081c06ab877"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80284264-5927-41c3-8bf8-de9a48dae16d", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279007325200}, "additional": {"logType": "detail", "children": [], "durationId": "829f40cc-26ad-470e-9a47-29536fd21145"}}, {"head": {"id": "c404e96b-8046-4034-add4-1c69682d9678", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279007689600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "555dc04c-c0e3-457f-9717-ab7678caf230", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279007787800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aca18f71-5bc3-4aaf-b469-615e23327737", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279008559000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11d1ffc-2e6e-4774-9bef-31d4a9075570", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279011950600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "696a9d48-3b88-46ca-9e25-72b899cdc651", "name": "Incremental task entry:default@CompileResource pre-execution cost: 24 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279037234600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd34b1a-cdb6-4f3f-b847-0d4812b67f64", "name": "entry : default@CompileResource cost memory 1.3920822143554688", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279037374700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1ce5597-8116-4b17-b68a-2081c06ab877", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279011921800, "endTime": 32279037464700}, "additional": {"logType": "info", "children": [], "durationId": "829f40cc-26ad-470e-9a47-29536fd21145"}}, {"head": {"id": "e28dceee-d7db-4da7-923c-ef1280f651ab", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279044857000, "endTime": 32279046360200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e53f4c44-fec0-4334-b971-e2df3ca44335", "logId": "bc0c9169-1c18-445c-b6df-192227f48f22"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e53f4c44-fec0-4334-b971-e2df3ca44335", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279040952900}, "additional": {"logType": "detail", "children": [], "durationId": "e28dceee-d7db-4da7-923c-ef1280f651ab"}}, {"head": {"id": "b01ae31a-96d1-4957-8d06-3bff54c56b4f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279041357000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b39835-9514-4d93-a63c-a5ff4a984590", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279041456500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8626035c-67e0-4652-82d6-f84cae1caf8a", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279044868800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "431ae438-8a25-44ae-9375-e60342736c62", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279045101400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "408801f6-e675-4b9e-9313-390a3025eb07", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279046184500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c89873a-63c4-420b-b925-ff527466e8d5", "name": "entry : default@DoNativeStrip cost memory 0.07317352294921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279046286200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0c9169-1c18-445c-b6df-192227f48f22", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279044857000, "endTime": 32279046360200}, "additional": {"logType": "info", "children": [], "durationId": "e28dceee-d7db-4da7-923c-ef1280f651ab"}}, {"head": {"id": "a3f8b983-78a9-4b86-9f42-67ab42248fa6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279053518700, "endTime": 32281092350100}, "additional": {"children": ["64ccb4d5-65f0-4e02-a3ee-749596ac4693", "2fb023c1-6ffe-47b7-b13d-1164afba60d8"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "b67f8be4-b5a1-444c-95fa-428d052e4983", "logId": "9f6dcc80-8fa2-4b5b-8af4-18ff31965722"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b67f8be4-b5a1-444c-95fa-428d052e4983", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279048944200}, "additional": {"logType": "detail", "children": [], "durationId": "a3f8b983-78a9-4b86-9f42-67ab42248fa6"}}, {"head": {"id": "605c6ab5-69c5-4af3-9b0f-75fab8c2f1f9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279049788600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a60d335-1704-4d37-ad7e-04096f52b07a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279049916300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62f7fff-c5b8-4d0b-8a89-7ed6af1e51a4", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279053531000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ca7b44-7601-459d-a9e9-3c248b8ae145", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279067613900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81754eb3-f5d9-46c0-91cd-49b41929d373", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279067889100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "183a92b6-dd9d-4fce-bff1-331fa1b9557a", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279084703600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc8a56c-ae93-4be4-9fe6-0f5b38b25b64", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279085255200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1fcf495-c14b-4d0c-b336-11d22b02f320", "name": "default@CompileArkTS work[77] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279086394700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32279197427800, "endTime": 32281085576900}, "additional": {"children": ["70bbab17-67be-4935-b16b-7316a942b682", "8db8c7c0-04e3-49f6-9f3e-5f6482ee6dd7", "beb32d04-13c2-41b7-abe4-51cdb5a5fa96", "d6e97b20-5ea6-440e-8768-a0263f88f45f", "1bf47326-4dd0-43ba-96aa-90aa2b1f84cc", "8b1e7c9a-c0a4-410d-aff3-c3cf6a2cc0cf"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a3f8b983-78a9-4b86-9f42-67ab42248fa6", "logId": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b4edd22-d466-4ee3-8ca7-a71ffb396b11", "name": "default@CompileArkTS work[77] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279087196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5649850f-dd8d-4eca-9eb5-3726e84d0dcd", "name": "default@CompileArkTS work[77] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279087309300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed15a98-055e-47ad-a18d-6eb027e9acd9", "name": "CopyResources startTime: 32279087367400", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279087369200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a51fa22-2281-4c1a-9877-2e46bc5c34db", "name": "default@CompileArkTS work[78] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279087419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb023c1-6ffe-47b7-b13d-1164afba60d8", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32280642930600, "endTime": 32280668985400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "a3f8b983-78a9-4b86-9f42-67ab42248fa6", "logId": "35fd7757-ba36-4475-8d62-40c4aa519371"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "57339234-d94f-4033-b9fb-790cc58aed95", "name": "default@CompileArkTS work[78] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279088268100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba407ae4-e8f2-414d-ab70-d4a1302ed258", "name": "default@CompileArkTS work[78] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279088359500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d89e7d-364b-408a-b85d-2cfe078bac55", "name": "entry : default@CompileArkTS cost memory -4.825050354003906", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279088513700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd686dff-d765-4b0e-b6e7-6605e2854856", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279094411700, "endTime": 32279097379700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "43bac570-caec-4b5a-a2ea-ee438b6e80c8", "logId": "3efd6c2e-8ca9-4a97-8f43-dfd3dff47bce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43bac570-caec-4b5a-a2ea-ee438b6e80c8", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279090532800}, "additional": {"logType": "detail", "children": [], "durationId": "bd686dff-d765-4b0e-b6e7-6605e2854856"}}, {"head": {"id": "19274f84-56a7-4273-822e-f38695f2bc43", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279090889800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "314ead6c-f313-4224-b90e-675c9a69656c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279090986600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ceb9744-46a3-4f24-a29a-4a7a9f46270b", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279094424300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "466fc10b-aa1c-4275-a05b-1ee45ad2b8e6", "name": "entry : default@BuildJS cost memory 0.12694549560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279097185100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d585ada3-487c-48ef-8263-47eb43f5d0fa", "name": "runTaskFromQueue task cost before running: 681 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279097318600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3efd6c2e-8ca9-4a97-8f43-dfd3dff47bce", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279094411700, "endTime": 32279097379700, "totalTime": 2885600}, "additional": {"logType": "info", "children": [], "durationId": "bd686dff-d765-4b0e-b6e7-6605e2854856"}}, {"head": {"id": "1bf54e84-b698-463d-8a8b-a01e3a2d292e", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279102701800, "endTime": 32279104361900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d79d9699-1677-42eb-99a6-438fbb17187c", "logId": "73ced430-67e9-4e84-82c0-8c99a0e9cf58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d79d9699-1677-42eb-99a6-438fbb17187c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279099945000}, "additional": {"logType": "detail", "children": [], "durationId": "1bf54e84-b698-463d-8a8b-a01e3a2d292e"}}, {"head": {"id": "c2554e26-2e7b-43b7-8add-7d3ac4e4ad62", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279100474100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51aa55b7-41cc-44fc-9a68-eda09978fbf0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279100583800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47cb5f32-e269-461d-a3c9-179a23f05887", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279102718200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c55fdc4a-5f2d-4776-ad28-29361ba2b2aa", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279103100800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d40ee21-eaa3-42d1-b8e4-0e82cf71e06d", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279104165300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "744b2bc6-796f-4578-b119-953d03648cbf", "name": "entry : default@CacheNativeLibs cost memory 0.08661651611328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279104292600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ced430-67e9-4e84-82c0-8c99a0e9cf58", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279102701800, "endTime": 32279104361900}, "additional": {"logType": "info", "children": [], "durationId": "1bf54e84-b698-463d-8a8b-a01e3a2d292e"}}, {"head": {"id": "39c3ba8b-f6c7-47a6-a3f8-93946f6801d2", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279197012000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79142fbf-06e9-4a76-812e-13a580d15d73", "name": "default@CompileArkTS work[77] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279197296700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e474449-2de5-445b-a9b0-a79e31090454", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279197375900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf0f8a64-ffe2-43bd-90e4-2330fdb261f6", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279197424700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66f5b3d3-c9df-4c3e-98ee-d25b5e15ce7a", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279197770100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "175cb882-6f59-46ac-b023-4cb0531ef4c1", "name": "default@CompileArkTS work[78] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279200947700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11557439-9b3f-4ef1-bea6-6cfc998add15", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280669678100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039e5145-2f4e-41b4-98a8-566ee07584d2", "name": "CopyResources is end, endTime: 32280669961900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280669969000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37340dec-23d9-4992-aaa6-468ac6eb9622", "name": "default@CompileArkTS work[78] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280670075700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35fd7757-ba36-4475-8d62-40c4aa519371", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32280642930600, "endTime": 32280668985400}, "additional": {"logType": "info", "children": [], "durationId": "2fb023c1-6ffe-47b7-b13d-1164afba60d8", "parent": "9f6dcc80-8fa2-4b5b-8af4-18ff31965722"}}, {"head": {"id": "f7f7269a-9637-4d8e-9d0e-14bdbb858e7a", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281085848800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70bbab17-67be-4935-b16b-7316a942b682", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32279197504700, "endTime": 32279202811000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "logId": "791ede4b-49f6-4f3d-a933-bfb96d6dd993"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "791ede4b-49f6-4f3d-a933-bfb96d6dd993", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279197504700, "endTime": 32279202811000}, "additional": {"logType": "info", "children": [], "durationId": "70bbab17-67be-4935-b16b-7316a942b682", "parent": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}}, {"head": {"id": "8db8c7c0-04e3-49f6-9f3e-5f6482ee6dd7", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32279202828500, "endTime": 32279202936500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "logId": "9b0b3461-5a97-4e52-8856-e49d23666905"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b0b3461-5a97-4e52-8856-e49d23666905", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279202828500, "endTime": 32279202936500}, "additional": {"logType": "info", "children": [], "durationId": "8db8c7c0-04e3-49f6-9f3e-5f6482ee6dd7", "parent": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}}, {"head": {"id": "beb32d04-13c2-41b7-abe4-51cdb5a5fa96", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32279202948500, "endTime": 32279202987500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "logId": "b9120cf2-9558-411b-9f4b-429008389078"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9120cf2-9558-411b-9f4b-429008389078", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279202948500, "endTime": 32279202987500}, "additional": {"logType": "info", "children": [], "durationId": "beb32d04-13c2-41b7-abe4-51cdb5a5fa96", "parent": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}}, {"head": {"id": "d6e97b20-5ea6-440e-8768-a0263f88f45f", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32279203002000, "endTime": 32280993162200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "logId": "d70573d0-4ee7-40a3-a5ec-57e54ef34fc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d70573d0-4ee7-40a3-a5ec-57e54ef34fc2", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279203002000, "endTime": 32280993162200}, "additional": {"logType": "info", "children": [], "durationId": "d6e97b20-5ea6-440e-8768-a0263f88f45f", "parent": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}}, {"head": {"id": "1bf47326-4dd0-43ba-96aa-90aa2b1f84cc", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32280993185200, "endTime": 32281002291900}, "additional": {"children": ["e20866c8-ea85-4f08-8561-801a439b6b43", "f00c0206-f02c-47b3-8583-c69eb1188dcb", "e2c0c02f-596b-4518-a3b8-cda004d99c21"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "logId": "4af71dc7-62d1-4c11-9f95-bc7e8a95daeb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4af71dc7-62d1-4c11-9f95-bc7e8a95daeb", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280993185200, "endTime": 32281002291900}, "additional": {"logType": "info", "children": ["8ac1e4fc-9f24-4885-aae1-1289979fdfa8", "c92333ca-e5ba-49c1-9a6f-6b70e984c39e", "3489a531-8d49-4c73-917e-e07a4435ad58"], "durationId": "1bf47326-4dd0-43ba-96aa-90aa2b1f84cc", "parent": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}}, {"head": {"id": "e20866c8-ea85-4f08-8561-801a439b6b43", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32280993211300, "endTime": 32280993217700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1bf47326-4dd0-43ba-96aa-90aa2b1f84cc", "logId": "8ac1e4fc-9f24-4885-aae1-1289979fdfa8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ac1e4fc-9f24-4885-aae1-1289979fdfa8", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280993211300, "endTime": 32280993217700}, "additional": {"logType": "info", "children": [], "durationId": "e20866c8-ea85-4f08-8561-801a439b6b43", "parent": "4af71dc7-62d1-4c11-9f95-bc7e8a95daeb"}}, {"head": {"id": "f00c0206-f02c-47b3-8583-c69eb1188dcb", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32280993222200, "endTime": 32280995518700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1bf47326-4dd0-43ba-96aa-90aa2b1f84cc", "logId": "c92333ca-e5ba-49c1-9a6f-6b70e984c39e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c92333ca-e5ba-49c1-9a6f-6b70e984c39e", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280993222200, "endTime": 32280995518700}, "additional": {"logType": "info", "children": [], "durationId": "f00c0206-f02c-47b3-8583-c69eb1188dcb", "parent": "4af71dc7-62d1-4c11-9f95-bc7e8a95daeb"}}, {"head": {"id": "e2c0c02f-596b-4518-a3b8-cda004d99c21", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32280995523000, "endTime": 32281002278100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "1bf47326-4dd0-43ba-96aa-90aa2b1f84cc", "logId": "3489a531-8d49-4c73-917e-e07a4435ad58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3489a531-8d49-4c73-917e-e07a4435ad58", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32280995523000, "endTime": 32281002278100}, "additional": {"logType": "info", "children": [], "durationId": "e2c0c02f-596b-4518-a3b8-cda004d99c21", "parent": "4af71dc7-62d1-4c11-9f95-bc7e8a95daeb"}}, {"head": {"id": "8b1e7c9a-c0a4-410d-aff3-c3cf6a2cc0cf", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32281002309300, "endTime": 32281085446100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "logId": "21b6c3ff-1304-4e90-8e81-62398029eec0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "21b6c3ff-1304-4e90-8e81-62398029eec0", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281002309300, "endTime": 32281085446100}, "additional": {"logType": "info", "children": [], "durationId": "8b1e7c9a-c0a4-410d-aff3-c3cf6a2cc0cf", "parent": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30"}}, {"head": {"id": "6836becf-1fba-44fd-ae08-93a38a304594", "name": "default@CompileArkTS work[77] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281092175800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa54fea-f47d-44ee-9c8e-6619aeeadf30", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32279197427800, "endTime": 32281085576900}, "additional": {"logType": "info", "children": ["791ede4b-49f6-4f3d-a933-bfb96d6dd993", "9b0b3461-5a97-4e52-8856-e49d23666905", "b9120cf2-9558-411b-9f4b-429008389078", "d70573d0-4ee7-40a3-a5ec-57e54ef34fc2", "4af71dc7-62d1-4c11-9f95-bc7e8a95daeb", "21b6c3ff-1304-4e90-8e81-62398029eec0"], "durationId": "64ccb4d5-65f0-4e02-a3ee-749596ac4693", "parent": "9f6dcc80-8fa2-4b5b-8af4-18ff31965722"}}, {"head": {"id": "9f6dcc80-8fa2-4b5b-8af4-18ff31965722", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32279053518700, "endTime": 32281092350100, "totalTime": 1923210900}, "additional": {"logType": "info", "children": ["4fa54fea-f47d-44ee-9c8e-6619aeeadf30", "35fd7757-ba36-4475-8d62-40c4aa519371"], "durationId": "a3f8b983-78a9-4b86-9f42-67ab42248fa6"}}, {"head": {"id": "f9326c8b-435d-48de-b06e-169f33f19609", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281097215500, "endTime": 32281098704400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "2cc68cd0-fd1a-47d4-8f2f-c1d16414969d", "logId": "6abadb6e-19d1-4435-9bd2-5ef65ef1c2a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cc68cd0-fd1a-47d4-8f2f-c1d16414969d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281095910200}, "additional": {"logType": "detail", "children": [], "durationId": "f9326c8b-435d-48de-b06e-169f33f19609"}}, {"head": {"id": "df17982a-305b-4003-a875-faf30330171f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281096316800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d5d510a-bc70-4299-b38f-22a3cfd33aa2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281096413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdb3dd1f-6e9c-4d52-9f60-34b7e40efab1", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281097223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e11e040-d5b6-4920-b001-5bc5016d3b7a", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281097419900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a805c39-8d36-4382-9253-4c1727415e1d", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281098478700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13c37edc-cab9-403b-8875-799b69932a6a", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07498931884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281098630100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6abadb6e-19d1-4435-9bd2-5ef65ef1c2a8", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281097215500, "endTime": 32281098704400}, "additional": {"logType": "info", "children": [], "durationId": "f9326c8b-435d-48de-b06e-169f33f19609"}}, {"head": {"id": "9185399f-5b2e-434a-b2fa-12862a948861", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281106957500, "endTime": 32281689747300}, "additional": {"children": ["d092175f-91ee-4364-8c2a-f650dd5e1d5b", "0ae336bc-de5c-44e4-8c21-3b0b3210e12a", "5171120a-eacd-422e-9a5f-743c1152a733"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "cb20fb36-b2aa-4713-a511-29da97d3a41b", "logId": "9d9db9a8-8b1a-4aef-a0e9-d5b5d448e51d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb20fb36-b2aa-4713-a511-29da97d3a41b", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281100611800}, "additional": {"logType": "detail", "children": [], "durationId": "9185399f-5b2e-434a-b2fa-12862a948861"}}, {"head": {"id": "0ad1e729-2277-430a-96b4-deffbe7187e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281100986400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6154efc8-6500-4b88-b6bd-c645d11526e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281101086700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90b7339-438b-46cc-81cc-a31f86d4c5ad", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281106967300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f931a93-d13e-4ea2-90a4-91b1f11f5018", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281119297600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0022fe54-1673-4634-a918-02bd535a578b", "name": "Incremental task entry:default@PackageHap pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281119580100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24efb989-ce46-44e7-8984-4f10b9ba08b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281119852000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "654b4b3f-42fb-4b0b-93a4-c2ac959d1046", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281119977000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d092175f-91ee-4364-8c2a-f650dd5e1d5b", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281120925200, "endTime": 32281122395400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9185399f-5b2e-434a-b2fa-12862a948861", "logId": "b0381c6f-aae8-4dca-a9b2-83945643283b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c2174ea-db02-444e-98b3-fcd5333a617f", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281122190300}, "additional": {"logType": "debug", "children": [], "durationId": "9185399f-5b2e-434a-b2fa-12862a948861"}}, {"head": {"id": "b0381c6f-aae8-4dca-a9b2-83945643283b", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281120925200, "endTime": 32281122395400}, "additional": {"logType": "info", "children": [], "durationId": "d092175f-91ee-4364-8c2a-f650dd5e1d5b", "parent": "9d9db9a8-8b1a-4aef-a0e9-d5b5d448e51d"}}, {"head": {"id": "0ae336bc-de5c-44e4-8c21-3b0b3210e12a", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281123302000, "endTime": 32281125392400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9185399f-5b2e-434a-b2fa-12862a948861", "logId": "f9600825-879c-4601-8729-bfc73b4153a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "465da6fd-5024-4991-a8a0-9159af4ad78f", "name": "default@PackageHap work[79] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281124264100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5171120a-eacd-422e-9a5f-743c1152a733", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32281125219700, "endTime": 32281689270300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "9185399f-5b2e-434a-b2fa-12862a948861", "logId": "7511a407-de6e-4a57-b02b-b24f74a167da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "325277f7-399e-4f33-9839-93157f02a2e4", "name": "default@PackageHap work[79] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281124942800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0cc9a02-2d12-475e-ae82-40c201315d8e", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281125018500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa36ddf7-fb7f-43ef-b2df-905358ed45a1", "name": "default@PackageHap work[79] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281125161900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a7ec1f6-7335-4a8e-90f4-a6374ecf1a6b", "name": "default@PackageHap work[79] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281125270600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9600825-879c-4601-8729-bfc73b4153a2", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281123302000, "endTime": 32281125392400}, "additional": {"logType": "info", "children": [], "durationId": "0ae336bc-de5c-44e4-8c21-3b0b3210e12a", "parent": "9d9db9a8-8b1a-4aef-a0e9-d5b5d448e51d"}}, {"head": {"id": "0ac8c516-5388-4818-a8aa-c756b3836af1", "name": "entry : default@PackageHap cost memory 1.2945404052734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281130600600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e59861-6ba8-44a3-8c8d-6960df80dc7d", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281196816200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd59c8f3-b4c2-42ca-9a89-75ba6ba4151c", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281243359600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51bf5f66-1296-469f-8603-7f8039c3a294", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281689379500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3d003a-5532-46f1-beed-2f8460afe08a", "name": "default@PackageHap work[79] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281689591900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7511a407-de6e-4a57-b02b-b24f74a167da", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32281125219700, "endTime": 32281689270300}, "additional": {"logType": "info", "children": [], "durationId": "5171120a-eacd-422e-9a5f-743c1152a733", "parent": "9d9db9a8-8b1a-4aef-a0e9-d5b5d448e51d"}}, {"head": {"id": "0f68287c-0c9a-4c88-90b8-b1d1f0d6820f", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281689684500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d9db9a8-8b1a-4aef-a0e9-d5b5d448e51d", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281106957500, "endTime": 32281689747300, "totalTime": 582315200}, "additional": {"logType": "info", "children": ["b0381c6f-aae8-4dca-a9b2-83945643283b", "f9600825-879c-4601-8729-bfc73b4153a2", "7511a407-de6e-4a57-b02b-b24f74a167da"], "durationId": "9185399f-5b2e-434a-b2fa-12862a948861"}}, {"head": {"id": "6f6e9cf5-b6dd-4da0-b90e-4da4880eac70", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281696099200, "endTime": 32281697958000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "5043c295-a329-46f9-8bb5-b72b5796c60c", "logId": "bbfde6ec-e8eb-4d53-9069-427897fa10b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5043c295-a329-46f9-8bb5-b72b5796c60c", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281693179200}, "additional": {"logType": "detail", "children": [], "durationId": "6f6e9cf5-b6dd-4da0-b90e-4da4880eac70"}}, {"head": {"id": "aeb521ac-7461-44b5-b055-f4f6f2c11ab4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281693554600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fe55842-ec05-4679-9ec0-239de753a872", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281693787000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef71099c-a692-4195-acd0-ed0d8376e315", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281696146000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "002090e7-e9c9-4ba8-95b3-c1a8d675daa1", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281696483900}, "additional": {"logType": "warn", "children": [], "durationId": "6f6e9cf5-b6dd-4da0-b90e-4da4880eac70"}}, {"head": {"id": "905be080-ee5a-46cb-9683-c1ef7ad00b5a", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281697086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e81934b-2890-4cf5-ad0d-fcbda0fecd88", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281697195400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9caa951b-0fd7-4429-8549-f4f8bd505b60", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281697275400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e02a819-67fc-47b9-bd16-0c04801dd24c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281697325700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44316272-7bed-4b27-b974-eb5a5f14a92b", "name": "entry : default@SignHap cost memory 0.11902618408203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281697521300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9ce56d-195d-4854-a764-ae3038097c8c", "name": "runTaskFromQueue task cost before running: 3 s 282 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281697786200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbfde6ec-e8eb-4d53-9069-427897fa10b7", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281696099200, "endTime": 32281697958000, "totalTime": 1483100}, "additional": {"logType": "info", "children": [], "durationId": "6f6e9cf5-b6dd-4da0-b90e-4da4880eac70"}}, {"head": {"id": "7dae6ed7-98db-4f71-9ff6-6fdca625db59", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281701707600, "endTime": 32281707016200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "da2d552d-f8bb-46af-b1e2-0a3a2ec76e6e", "logId": "d3d7e7c6-a871-498a-a27a-19a81c3d690c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da2d552d-f8bb-46af-b1e2-0a3a2ec76e6e", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281700443400}, "additional": {"logType": "detail", "children": [], "durationId": "7dae6ed7-98db-4f71-9ff6-6fdca625db59"}}, {"head": {"id": "947bbb68-4720-4957-9fc8-93e217fd2d70", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281700905700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86b06b46-22fa-43f6-aeb5-301222d84d4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281701009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f443f4a2-7767-4a4e-91f8-e8e087bc2a8d", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281701716100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1c2c5d5-75b0-4f5f-9dbc-fc114d6db69c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281706635100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e7bc887-5297-4978-9f4d-f926907c9203", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281706774000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc48d2b4-8896-4d53-8330-09308d50d6b0", "name": "entry : default@CollectDebugSymbol cost memory 0.23946380615234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281706877800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "783246db-6042-4703-83ab-d9380115863e", "name": "runTaskFromQueue task cost before running: 3 s 291 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281706957600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d7e7c6-a871-498a-a27a-19a81c3d690c", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281701707600, "endTime": 32281707016200, "totalTime": 5228700}, "additional": {"logType": "info", "children": [], "durationId": "7dae6ed7-98db-4f71-9ff6-6fdca625db59"}}, {"head": {"id": "06db9028-acaf-4f92-8e2d-c60584d44495", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281709039700, "endTime": 32281709362600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "b14f74ab-96b1-4d5e-9d38-a02cbe04ab9a", "logId": "59669200-2473-42a0-89f9-bfef3708b0ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b14f74ab-96b1-4d5e-9d38-a02cbe04ab9a", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281708995900}, "additional": {"logType": "detail", "children": [], "durationId": "06db9028-acaf-4f92-8e2d-c60584d44495"}}, {"head": {"id": "062e50b0-c4ca-46f1-aff1-fd2fba2fd2f7", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281709046600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29343143-5b28-4bdf-9c46-9b2882185d97", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281709169200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142b2929-433e-441d-b92e-9eb124d1e642", "name": "runTaskFromQueue task cost before running: 3 s 293 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281709245800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59669200-2473-42a0-89f9-bfef3708b0ce", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281709039700, "endTime": 32281709362600, "totalTime": 184400}, "additional": {"logType": "info", "children": [], "durationId": "06db9028-acaf-4f92-8e2d-c60584d44495"}}, {"head": {"id": "784a4d95-77a8-4fea-ad41-5d238e3c1cad", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281718611800, "endTime": 32281718635700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b419f00a-5d7d-4db9-824e-e10044eb8fe8", "logId": "70280523-7b1d-4121-a0ea-d4e9cbf6f6cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70280523-7b1d-4121-a0ea-d4e9cbf6f6cc", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281718611800, "endTime": 32281718635700}, "additional": {"logType": "info", "children": [], "durationId": "784a4d95-77a8-4fea-ad41-5d238e3c1cad"}}, {"head": {"id": "867daf2f-209c-484b-9119-477172399413", "name": "BUILD SUCCESSFUL in 3 s 303 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281718675100}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "37da73a0-001a-45a4-b86a-592014ff4647", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32278416513200, "endTime": 32281718936100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 27}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "16b36a72-d057-45b7-8034-f427e4dd722d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281718971200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff87724a-1cad-4d07-8f08-b04b4d756e56", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281719034000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1efd2f3-06d4-478f-a749-f81166c01d34", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281719085700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684b21e1-0134-4ef1-87ec-391d05e6fda1", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281719189800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df871c7b-87d0-4f76-b5ba-7573065b842d", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281719406600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46dd4941-63f3-4121-990e-b5ee4973a061", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281719733500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d230d323-d618-4f66-a75c-cf6a3a28b418", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281720523800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30865952-5842-4c2b-a1d1-19f816abd14a", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281720804500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cd156cb-be02-4765-bd34-7d11062b4817", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281720876900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e772d6aa-312e-42ce-bd89-f083cfe14818", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281720943000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "576cdafd-9c48-403b-92af-fb2af8c8a4bb", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281721303600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d9606a-04cc-4cc8-8b9e-5437bd28f2aa", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281722179100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a4d0eec-e267-444f-8bd8-6e57d730e85d", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281722466700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48762ab2-aee6-46a0-b7b0-1e9ba85863e3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281722551900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8028b7c-afa1-4c22-bb0e-03b0da28e5fe", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281722603500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50ea166f-2e1a-4c41-9f7d-37fbcb9dc427", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281723033900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b26874e-5c30-439c-8376-b4291cedc450", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281723098200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ca488cb-3161-489f-89d3-91f8c9ca665d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281723698600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349cb9e1-0338-49af-a48c-c01c4912bb90", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281723956900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb08b5a-4de1-4ea1-858f-fb3e9a6cb591", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281724156400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be29f146-6021-4595-9d0f-642f3d3ea7a2", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281724419200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e771e7-7999-421f-bbd3-38db183ff89a", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281724488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6fa7a47-b8dd-4989-8df5-3af30394a9da", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281724540000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a93c432-3d11-4b20-8052-feb5c3236fc7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281726613400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cdf71f4-502b-4c9e-b691-16ff71491075", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281727038300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e56785f-96d3-418b-b824-f08eb6d02089", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281728048900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb148347-11bf-491c-ad06-9477311d9439", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281728261200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "648ee2fe-cd3d-4cf2-9d79-d9a13d5e80e9", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281728528200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6554542d-f31c-462f-918e-25193984afd3", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281729300300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b1e244d-e462-4f5c-8ab8-aa7060e39c5e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281729394700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5bae5de-eade-4858-b32b-c05ace5dc864", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281729598000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6931b10f-3138-42a5-81fa-dbe236d72313", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281729941400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9c1f6d3-0eb0-426d-bde1-8e64e3f83102", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281730553700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4417fba7-de37-47b6-a14f-ea6fbfbea505", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281732165900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3099c9f4-f42f-43e7-aae4-3f6585608d0d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281732838400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c94e138f-2923-4c71-a08e-614962033803", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281733874000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50835183-9399-42fc-a04d-b4cdc24ae68c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281734098100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6eceb09-ed51-4836-85b7-ed748141c634", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281734395100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c77e0a6f-b855-4cea-9887-7006848da137", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281734937400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3afa69-889e-4e3d-818f-14e95d6a1868", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281735194200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e4c8ce-2637-4f7b-bd62-d9e3437f518a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281735272300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a78a2541-a907-420f-bbda-3d19e3f47a40", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281735321600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01f6ffbd-4fac-4f15-a1a1-5f331ab7b9fe", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281736213400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a49c094-c751-4a59-ac72-f5af43d64261", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281736474900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa11c840-f83d-400a-8ce0-3fbbd3baf2a2", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281736729500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e90981e8-9125-48b1-84f5-f6531bbfbfe7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281745333000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71731322-a95e-41c7-a78c-4726624f685e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281745572400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fdab2b0-5194-4660-b230-581c64991c8e", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281745962300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cacb1df-3baa-4f2f-9a6c-d7e731ae5b25", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281746059400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c1c556b-fe6f-4ba3-916b-0cf75dfb1b3a", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281746446900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd551aff-9e72-4d06-854c-a1ed3eff26ae", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281747346800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c395cc02-a595-4692-a836-93a0c7086eb2", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281747885300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dbbae44-c872-4168-87de-9f763be08adc", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281749051900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd83cedc-3fe0-4a02-8f1b-1e95eb92aeb5", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281749482200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50cee5fd-0216-4879-8ac6-ea067ce106ed", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281749750700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2355d988-2efd-424a-baf2-a3cc3def0a01", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281749889500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3f92a68-f09a-47f4-9d22-d2fffbf5babc", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281750185800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f19c36-fa2a-4f6e-a898-365f4b3de543", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281752420500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16c35706-16b9-4a5a-80fd-0be4642c7474", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281752869000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09d1dbb6-3d06-4387-9936-6f9074abb43b", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281753149200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ba5fe6a-74ed-4b0e-b768-9f471b8ed82c", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32281753469600}, "additional": {"logType": "debug", "children": []}}], "workLog": []}