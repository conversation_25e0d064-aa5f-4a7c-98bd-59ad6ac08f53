/*
 * Copyright (c) 2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef RESOURCE_TABLE_H
#define RESOURCE_TABLE_H

#include<stdint.h>

namespace OHOS {
const int32_t STRING_ENTRYABILITY_DESC = 0x01000002;
const int32_t STRING_ENTRYABILITY_LABEL = 0x01000003;
const int32_t STRING_APP_NAME = 0x01000000;
const int32_t STRING_MODULE_DESC = 0x01000004;
const int32_t COLOR_START_WINDOW_BACKGROUND = 0x01000005;
const int32_t FLOAT_PAGE_TEXT_FONT_SIZE = 0x01000006;
const int32_t MEDIA_ANIMAL1 = 0x0100000b;
const int32_t MEDIA_ANIMAL2 = 0x0100000c;
const int32_t MEDIA_ANIMAL3 = 0x0100000d;
const int32_t MEDIA_ANIMAL4 = 0x0100000e;
const int32_t MEDIA_ANIMAL5 = 0x0100000f;
const int32_t MEDIA_ANIMAL6 = 0x01000010;
const int32_t MEDIA_ANIMAL7 = 0x01000011;
const int32_t MEDIA_ANIMAL8 = 0x01000012;
const int32_t MEDIA_ANIMAL9 = 0x01000013;
const int32_t MEDIA_APP_ICON = 0x01000001;
const int32_t MEDIA_DOC = 0x01000023;
const int32_t MEDIA_GAMEDOWN = 0x01000029;
const int32_t MEDIA_GAMEFAIL = 0x01000027;
const int32_t MEDIA_GOAL = 0x01000028;
const int32_t MEDIA_GOAL2 = 0x0100002a;
const int32_t MEDIA_ICON = 0x01000007;
const int32_t MEDIA_MENUE = 0x01000021;
const int32_t MEDIA_MONEKY = 0x01000026;
const int32_t MEDIA_MUSIC = 0x01000022;
const int32_t MEDIA_NAV_ARROW = 0x01000024;
const int32_t MEDIA_NIU = 0x01000015;
const int32_t MEDIA_NUMBER1 = 0x01000016;
const int32_t MEDIA_NUMBER2 = 0x01000017;
const int32_t MEDIA_NUMBER3 = 0x01000018;
const int32_t MEDIA_NUMBER4 = 0x01000019;
const int32_t MEDIA_NUMBER5 = 0x0100001a;
const int32_t MEDIA_NUMBER6 = 0x0100001b;
const int32_t MEDIA_NUMBER7 = 0x0100001c;
const int32_t MEDIA_NUMBER8 = 0x0100001d;
const int32_t MEDIA_NUMBER9 = 0x0100001e;
const int32_t MEDIA_PERSONLIZE = 0x01000025;
const int32_t MEDIA_QIE = 0x0100001f;
const int32_t MEDIA_RESTART = 0x01000014;
const int32_t MEDIA_SHUDU = 0x0100002b;
const int32_t MEDIA_SS = 0x01000020;
const int32_t MEDIA_STARTICON = 0x01000008;
const int32_t PROFILE_MAIN_PAGES = 0x01000009;
const int32_t PROFILE_ROUTER_MAP = 0x0100000a;
}
#endif