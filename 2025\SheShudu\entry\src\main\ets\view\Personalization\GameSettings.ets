import CommonConstants from "../../common/CommonConstants";
import Logger from "../../util/Logger";
import { preferences } from "@kit.ArkData";
import { BusinessError } from "@kit.BasicServicesKit";
import { CommonDialog } from "../../util/CommonDialog";

@Builder
export function GameSettingsBuilder(name: string, param: Object) {
  GameSettings()
}

@Component
export struct GameSettings {
  pageInfos: NavPathStack = new NavPathStack();
  scroller: Scroller = new Scroller();
  private ctx: UIContext = this.getUIContext();
  private diag: CommonDialog = new CommonDialog();
  private dataPreferences: preferences.Preferences | null = null;
  
  @State difficulty: string = 'easy';
  @State musicOpen: boolean = false;
  @State isLoading: boolean = true;
  //操作控制
  @State operStatus:number=0;//0-点击模式，1-拖动模式

  build() {
    NavDestination() {
      Column() {
        if (this.isLoading) {
          LoadingProgress()
            .color('#EE7CDF')
            .width(50)
            .height(50)
        } else {
          Scroll(this.scroller) {
            Column({ space: 20 }) {
              // 难度设置
              Column({ space: 10 }) {
                Text('难度设置')
                  .fontSize(18)
                  .fontWeight(FontWeight.Medium)
                  .width('90%')
                
                Row() {
                  Text('简单')
                    .fontSize(16)
                  Radio({ value: 'easy', group: 'difficultyGroup' })
                    .checked(this.difficulty === 'easy')
                    .onChange((isChecked: boolean) => {
                      if (isChecked) {
                        this.difficulty = 'easy';
                        this.saveSettings();
                      }
                    })
                }
                .width('90%')
                .justifyContent(FlexAlign.SpaceBetween)
                .padding(10)
                .backgroundColor('#FFFFFF')
                .borderRadius(12)
                
                Row() {
                  Text('中等')
                    .fontSize(16)
                  Radio({ value: 'medium', group: 'difficultyGroup' })
                    .checked(this.difficulty === 'medium')
                    .onChange((isChecked: boolean) => {
                      if (isChecked) {
                        this.difficulty = 'medium';
                        this.saveSettings();
                      }
                    })
                }
                .width('90%')
                .justifyContent(FlexAlign.SpaceBetween)
                .padding(10)
                .backgroundColor('#FFFFFF')
                .borderRadius(12)
                
                Row() {
                  Text('困难')
                    .fontSize(16)
                  Radio({ value: 'hard', group: 'difficultyGroup' })
                    .checked(this.difficulty === 'hard')
                    .onChange((isChecked: boolean) => {
                      if (isChecked) {
                        this.difficulty = 'hard';
                        this.saveSettings();
                      }
                    })
                }
                .width('90%')
                .justifyContent(FlexAlign.SpaceBetween)
                .padding(10)
                .backgroundColor('#FFFFFF')
                .borderRadius(12)
              }
              .width('100%')
              .alignItems(HorizontalAlign.Center)

              // 操作模式设置
              Column({ space: 10 }) {
                Text('操作模式设置')
                  .fontSize(18)
                  .fontWeight(FontWeight.Medium)
                  .width('90%')

                Row() {
                  Text('拖动模式')
                    .fontSize(16)
                  Radio({ value: 'pull', group: 'operGroup' })
                    .checked(this.operStatus === 1)
                    .onChange((isChecked: boolean) => {
                      if (isChecked) {
                        this.operStatus = 1;
                        this.saveSettings();
                      }
                    })
                }
                .width('90%')
                .justifyContent(FlexAlign.SpaceBetween)
                .padding(10)
                .backgroundColor('#FFFFFF')
                .borderRadius(12)

                Row() {
                  Text('点击模式')
                    .fontSize(16)
                  Radio({ value: 'click', group: 'operGroup' })
                    .checked(this.operStatus === 0)
                    .onChange((isChecked: boolean) => {
                      if (isChecked) {
                        this.operStatus = 0;
                        this.saveSettings();
                      }
                    })
                }
                .width('90%')
                .justifyContent(FlexAlign.SpaceBetween)
                .padding(10)
                .backgroundColor('#FFFFFF')
                .borderRadius(12)

              }
              .width('100%')
              .alignItems(HorizontalAlign.Center)
              
              // 音效设置
              Column({ space: 10 }) {
                Text('音效设置')
                  .fontSize(18)
                  .fontWeight(FontWeight.Medium)
                  .width('90%')
                
                Row() {
                  Text('游戏音效')
                    .fontSize(16)
                  Toggle({ type: ToggleType.Switch, isOn: $$this.musicOpen })
                    .onChange((isOn: boolean) => {
                      this.musicOpen = isOn;
                      this.saveSettings();
                    })
                }
                .width('90%')
                .justifyContent(FlexAlign.SpaceBetween)
                .padding(10)
                .backgroundColor('#FFFFFF')
                .borderRadius(12)
              }
              .width('100%')
              .alignItems(HorizontalAlign.Center)
              
              // 重置按钮
              Button('恢复默认设置')
                .width('90%')
                .height(50)
                .backgroundColor('#EE7CDF')
                .borderRadius(24)
                .margin({ top: 20 })
                .onClick(() => {
                  this.resetSettings();
                })
            }
            .width(CommonConstants.FULL_PARENT)
            .padding({ top: 20, bottom: 20 })
            .justifyContent(FlexAlign.Start)
            .alignItems(HorizontalAlign.Center)
          }
          .scrollable(ScrollDirection.Vertical)
          .scrollBar(BarState.Off)
          .edgeEffect(EdgeEffect.Spring)
        }
      }
      .height(CommonConstants.FULL_PARENT)
      .width(CommonConstants.FULL_PARENT)
      .backgroundColor('#F5F5F5')
      
    }.title('游戏设置')
    .onShown(
      ()=>{
        console.log("出现了");
        if(this.dataPreferences){
          this.difficulty=this.dataPreferences.getSync('difficulty','easy') as 'easy' | 'medium' | 'hard'
          this.musicOpen=this.dataPreferences.getSync('musicOpen',true) as boolean;
          this.operStatus=this.dataPreferences.getSync('operStatus',0) as number;
          console.log(this.musicOpen+"-----"+this.difficulty);
        }

      }
    )
   .onReady((context: NavDestinationContext) => {
      this.pageInfos = context.pathStack;
      Logger.info("GameSettings page ready");
    })
  }
  
  aboutToAppear() {
    // 获取偏好设置
    let options: preferences.Options = { name: 'myStore' };
    this.dataPreferences  =preferences.getPreferencesSync(getContext(this), options);
    this.isLoading = false;
  }
  
  saveSettings() {
    if (this.dataPreferences) {
      // 保存难度设置
      this.dataPreferences.putSync('difficulty', this.difficulty);
      
      // 保存音效设置
      this.dataPreferences.putSync('musicOpen', this.musicOpen);


      // 保存操作模式设置
      this.dataPreferences.putSync('operStatus', this.operStatus);


      // 刷新设置
      this.dataPreferences.flushSync();
    }
  }
  
  resetSettings() {
    this.difficulty = 'easy';
    this.musicOpen = true;
    this.operStatus=0;
    this.saveSettings();
    //this.diag.openMessageDiag(this.ctx, "已恢复默认设置", () => {});
  }
}