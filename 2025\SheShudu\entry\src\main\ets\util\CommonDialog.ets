import { PromptActionClass } from './PromptActionClass';
import { ComponentContent, UIContext } from '@kit.ArkUI';


let prmtAc = new PromptActionClass();


class deleteParams {
  confirm:()=>void=()=>{}
  message:string=""
  messageTitle?:string
  constructor(message: string, confirm: () => void,messageTitl?:string) {
    if(confirm){
      this.confirm=confirm;
    }
    this.message=message
    this.messageTitle=messageTitl
  }
}

@Builder
function errorDiag(parmas:deleteParams) {
  Column({space:10}) {
    Text(parmas.message)
      .fontSize(30)
      .fontWeight(FontWeight.Medium)
      .fontColor(Color.White)
      .margin(10)
    Button("重新开始")
      .onClick(
        ()=>{
          parmas.confirm()
          prmtAc.closeDialog()
        }
      )
      .opacity(0.8)
      .fontColor(Color.Black)
      .backgroundColor(Color.White)
      .width('80%')
  }
  .justifyContent(FlexAlign.Center)
  .alignItems(HorizontalAlign.Center)
  .width('60%')
  .backgroundColor(Color.Orange)
  .borderRadius(20)
  .opacity(0.9)

}



@Builder
function successDiag(parmas:deleteParams) {
  Column({space:10}) {
    Text(parmas.message)
      .fontSize(30)
      .fontWeight(FontWeight.Medium)
      .fontColor(Color.White)
      .margin(10)
    Button("继续挑战下一题")
      .onClick(
        ()=>{
          parmas.confirm()
          prmtAc.closeDialog()
        }
      )
      .opacity(0.8)
      .fontColor(Color.White)
      .width('80%')
  }
  .justifyContent(FlexAlign.Center)
  .alignItems(HorizontalAlign.Center)
  .width('60%')
  .backgroundColor(Color.Orange)
  .borderRadius(20)
  .opacity(0.9)

}


export class CommonDialog{
  openMessageDiag(UIContext: UIContext, message: string, confirm: () => void) {
    let contentNode: ComponentContent<Object> = new ComponentContent(UIContext, wrapBuilder(successDiag),new deleteParams(message,confirm));
    prmtAc.setContext(UIContext);
    prmtAc.setContentNode(contentNode);
    prmtAc.setOptions({ alignment: DialogAlignment.Center, offset: { dx: 0, dy: 0 },autoCancel:true});
    prmtAc.openDialog()
  }

  openErrorDialog(context: UIContext,message:string,confirm:()=>void,x:number,y:number){
    let contentNode: ComponentContent<Object> = new ComponentContent(context, wrapBuilder(errorDiag),new deleteParams(message,confirm));
    prmtAc.setContext(context);
    prmtAc.setContentNode(contentNode);
    prmtAc.setOptions({ alignment: DialogAlignment.Center, offset: { dx: 0, dy: 0 },autoCancel:false});
    prmtAc.openDialog()
  }

}