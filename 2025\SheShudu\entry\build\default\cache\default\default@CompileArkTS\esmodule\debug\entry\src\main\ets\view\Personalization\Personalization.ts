if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Personalization_Params {
    pageInfos?: NavPathStack;
    scroller?: Scroller;
    diag?: CommonDialog;
    ctx?: UIContext;
    dataPreferences?: preferences.Preferences | null;
    bacColor?: string;
}
import CommonConstants from "@bundle:com.atomicservice.6917576098804821135/entry/ets/common/CommonConstants";
import { CommonDialog } from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/CommonDialog";
import Logger from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/Logger";
import preferences from "@ohos:data.preferences";
import type { BusinessError } from "@ohos:base";
export function PersonalizationBuilder(name: string, param: Object, parent = null) {
    {
        (parent ? parent : this).observeComponentCreation2((elmtId, isInitialRender) => {
            if (isInitialRender) {
                let componentCall = new Personalization(parent ? parent : this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/Personalization/Personalization.ets", line: 9, col: 3 });
                ViewPU.create(componentCall);
                let paramsLambda = () => {
                    return {};
                };
                componentCall.paramsGenerator_ = paramsLambda;
            }
            else {
                (parent ? parent : this).updateStateVarsOfChildByElmtId(elmtId, {});
            }
        }, { name: "Personalization" });
    }
}
export class Personalization extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.pageInfos = new NavPathStack();
        this.scroller = new Scroller();
        this.diag = new CommonDialog();
        this.ctx = this.getUIContext();
        this.dataPreferences = null;
        this.__bacColor = new ObservedPropertySimplePU('#EEEEE1', this, "bacColor");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Personalization_Params) {
        if (params.pageInfos !== undefined) {
            this.pageInfos = params.pageInfos;
        }
        if (params.scroller !== undefined) {
            this.scroller = params.scroller;
        }
        if (params.diag !== undefined) {
            this.diag = params.diag;
        }
        if (params.ctx !== undefined) {
            this.ctx = params.ctx;
        }
        if (params.dataPreferences !== undefined) {
            this.dataPreferences = params.dataPreferences;
        }
        if (params.bacColor !== undefined) {
            this.bacColor = params.bacColor;
        }
    }
    updateStateVars(params: Personalization_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__bacColor.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__bacColor.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private pageInfos: NavPathStack;
    private scroller: Scroller;
    private diag: CommonDialog;
    private ctx: UIContext;
    private dataPreferences: preferences.Preferences | null;
    private __bacColor: ObservedPropertySimplePU<string>;
    get bacColor() {
        return this.__bacColor.get();
    }
    set bacColor(newValue: string) {
        this.__bacColor.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            NavDestination.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.height(CommonConstants.FULL_PARENT);
                    Column.width(CommonConstants.FULL_PARENT);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Scroll.create(this.scroller);
                    Scroll.scrollable(ScrollDirection.Vertical);
                    Scroll.scrollBar(BarState.Off);
                    Scroll.edgeEffect(EdgeEffect.Spring);
                }, Scroll);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 10 });
                    Column.width(CommonConstants.FULL_PARENT);
                    Column.justifyContent(FlexAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.width('90%');
                    Row.height(50);
                    Row.backgroundColor('#FFFFFF');
                    Row.padding(15);
                    Row.justifyContent(FlexAlign.SpaceBetween);
                    Row.borderRadius(24);
                    Row.onClick(() => {
                        // 跳转到玩法介绍页面
                        this.pageInfos.pushPathByName('gameRules', null);
                    });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 10 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777251, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(20);
                    Image.height(20);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('玩法介绍');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777252, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(16);
                    Image.height(16);
                }, Image);
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.width('90%');
                    Row.height(50);
                    Row.backgroundColor('#FFFFFF');
                    Row.padding(15);
                    Row.justifyContent(FlexAlign.SpaceBetween);
                    Row.borderRadius(24);
                    Row.onClick(() => {
                        // 跳转到游戏设置页面
                        this.pageInfos.pushPathByName('gameSettings', null);
                    });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 10 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777259, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(20);
                    Image.height(20);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('游戏设置');
                    Text.fontSize(16);
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777252, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(16);
                    Image.height(16);
                }, Image);
                Row.pop();
                Column.pop();
                Scroll.pop();
                Column.pop();
            }, { moduleName: "entry", pagePath: "entry/src/main/ets/view/Personalization/Personalization" });
            NavDestination.title('个人中心');
            NavDestination.linearGradient({
                angle: 180,
                colors: [['#FFF6F6', 0.0], ['#FFF0F0', 0.5], ['#FFFAFA', 1.0]]
            });
            NavDestination.onReady((context: NavDestinationContext) => {
                this.pageInfos = context.pathStack;
                Logger.info("current page config info is " + JSON.stringify(context.getConfigInRouteMap()));
            });
        }, NavDestination);
        NavDestination.pop();
    }
    aboutToAppear() {
        preferences.getPreferences(getContext(this), 'myStore', (err: BusinessError, val: preferences.Preferences) => {
            if (err) {
                console.error("Failed to get preferences. code =" + err.code + ", message =" + err.message);
                return;
            }
            this.dataPreferences = val;
            console.info("Succeeded in getting preferences.");
        });
    }
    rerender() {
        this.updateDirtyElements();
    }
}
(function () {
    if (typeof NavigationBuilderRegister === "function") {
        NavigationBuilderRegister("personalization", wrapBuilder(PersonalizationBuilder));
    }
})();
