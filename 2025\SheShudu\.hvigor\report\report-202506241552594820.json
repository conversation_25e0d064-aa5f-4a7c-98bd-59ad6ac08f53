{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "6145f0af-e678-4b91-8587-4cece682c243", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 25520709030600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e279763-4b5f-4d56-9525-9bda763f8112", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 25520712719800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d06daa3-a4f3-4a90-81b6-4bd5c6a80d01", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 25520713054500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0924dcbf-a27c-4fae-8689-1632356f6d3e", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 25520718672200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0908896-7e39-454d-8862-7f9788a1c445", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611375802100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "342da67a-279a-4971-b249-e4b60dcf0c30", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611381388100, "endTime": 26611611994700}, "additional": {"children": ["4bd239cf-3feb-46b1-a19c-c78739df7127", "412fe17a-06df-4ab4-874e-74abdae54fd6", "3836675a-c3de-4e8f-86e2-1add72a4b493", "2119442e-dc7d-4411-beef-95d660269988", "810d0e78-cf49-4cfe-8949-a64980cac496", "2ea7d4f3-a494-4aaf-9dfc-912963528d66", "a4bd59f2-d21e-4c33-a875-b10196a2b611"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bd239cf-3feb-46b1-a19c-c78739df7127", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611381390100, "endTime": 26611395571900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "33bc49c0-84b7-4a99-bd10-c93767255115"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "412fe17a-06df-4ab4-874e-74abdae54fd6", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611395596100, "endTime": 26611610736500}, "additional": {"children": ["1d03f94a-8e36-435e-9bb7-51fca4a482a8", "72fe161b-886a-4ccf-ba76-afc8fe5e58c4", "5476b7d3-14d7-4a60-a720-07ff7f632667", "a3f45ace-3626-4042-b186-1b85096f78f8", "06dabdb0-ffe1-4b6d-9a9b-067e543c16b8", "c1c672b1-1b01-415f-806f-ee5320f849ac", "d245cf6d-41ae-4072-8fc5-e1d3fdd1022b", "de1ea265-d2ef-4cba-946a-a82999ccbf5e", "433f91f8-49c5-4c3f-9e35-04f87e122f67"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "d5223666-06b9-4bff-93c3-2490abbc25f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3836675a-c3de-4e8f-86e2-1add72a4b493", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611610764100, "endTime": 26611611974000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "d6332318-38e0-4cda-80ba-855a0d494eef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2119442e-dc7d-4411-beef-95d660269988", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611611980000, "endTime": 26611611986800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "c8a9b239-2fee-4f82-9403-bb1d348b0c5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "810d0e78-cf49-4cfe-8949-a64980cac496", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611385424300, "endTime": 26611385463100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "1ef3d942-c639-44d3-9be4-eccd804fa510"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ef3d942-c639-44d3-9be4-eccd804fa510", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611385424300, "endTime": 26611385463100}, "additional": {"logType": "info", "children": [], "durationId": "810d0e78-cf49-4cfe-8949-a64980cac496", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "2ea7d4f3-a494-4aaf-9dfc-912963528d66", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611390883600, "endTime": 26611390903900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "7c03c107-3fe2-47d9-8e8f-9133dee63564"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c03c107-3fe2-47d9-8e8f-9133dee63564", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611390883600, "endTime": 26611390903900}, "additional": {"logType": "info", "children": [], "durationId": "2ea7d4f3-a494-4aaf-9dfc-912963528d66", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "d47cbe8a-50f8-44d9-9101-332b86a50063", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611390958200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2ad9f7a-32d1-470d-8c46-699269f33693", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611395432800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33bc49c0-84b7-4a99-bd10-c93767255115", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611381390100, "endTime": 26611395571900}, "additional": {"logType": "info", "children": [], "durationId": "4bd239cf-3feb-46b1-a19c-c78739df7127", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "1d03f94a-8e36-435e-9bb7-51fca4a482a8", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611401896800, "endTime": 26611401908700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "7eebd769-72a3-4346-982c-b73f5914eeac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "72fe161b-886a-4ccf-ba76-afc8fe5e58c4", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611401928400, "endTime": 26611409327800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "ac99d5a6-315d-41a2-96b4-b5840d68dad0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5476b7d3-14d7-4a60-a720-07ff7f632667", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611409352300, "endTime": 26611491381000}, "additional": {"children": ["ed502731-cea9-4033-9313-2b87f031d837", "d719812f-ec5d-4edd-a68f-d16cca5d3932", "34cfe71b-d6b0-4b06-a72a-5dcab3f41051"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "3f77e3d0-38cc-4539-899a-85dcb8763b41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3f45ace-3626-4042-b186-1b85096f78f8", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611491398900, "endTime": 26611512272300}, "additional": {"children": ["8f6210eb-3b03-41c8-ab68-110e063454c3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "78229555-d2a9-48e1-ac47-2fba10457a62"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06dabdb0-ffe1-4b6d-9a9b-067e543c16b8", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611512342000, "endTime": 26611592453200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "f03a218e-8d7c-4658-950d-9c0824deb276"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1c672b1-1b01-415f-806f-ee5320f849ac", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611593625000, "endTime": 26611602503200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "af84051a-4ab3-42f9-9e57-0190887425d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d245cf6d-41ae-4072-8fc5-e1d3fdd1022b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611602530900, "endTime": 26611610582100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "c8b11766-e847-4969-909a-15f5f4bf2392"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de1ea265-d2ef-4cba-946a-a82999ccbf5e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611610602000, "endTime": 26611610722700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "78e4c997-7b91-4942-ac8f-bdd7b565497a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7eebd769-72a3-4346-982c-b73f5914eeac", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611401896800, "endTime": 26611401908700}, "additional": {"logType": "info", "children": [], "durationId": "1d03f94a-8e36-435e-9bb7-51fca4a482a8", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "ac99d5a6-315d-41a2-96b4-b5840d68dad0", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611401928400, "endTime": 26611409327800}, "additional": {"logType": "info", "children": [], "durationId": "72fe161b-886a-4ccf-ba76-afc8fe5e58c4", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "ed502731-cea9-4033-9313-2b87f031d837", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611410148900, "endTime": 26611410172300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5476b7d3-14d7-4a60-a720-07ff7f632667", "logId": "80d6537c-dcac-45c9-bc73-889faccbd085"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80d6537c-dcac-45c9-bc73-889faccbd085", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611410148900, "endTime": 26611410172300}, "additional": {"logType": "info", "children": [], "durationId": "ed502731-cea9-4033-9313-2b87f031d837", "parent": "3f77e3d0-38cc-4539-899a-85dcb8763b41"}}, {"head": {"id": "d719812f-ec5d-4edd-a68f-d16cca5d3932", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611412445700, "endTime": 26611490259200}, "additional": {"children": ["8acc4c79-05d6-49b6-804a-e02a91d20b9e", "28fab82b-c424-425d-b93d-7f285f35e7bd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5476b7d3-14d7-4a60-a720-07ff7f632667", "logId": "acb3ed84-9b52-48b7-8ba4-dfad401d8532"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8acc4c79-05d6-49b6-804a-e02a91d20b9e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611412449100, "endTime": 26611420035600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d719812f-ec5d-4edd-a68f-d16cca5d3932", "logId": "02fbfbcd-9a0a-4f9e-bac7-b8c2470bf09c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28fab82b-c424-425d-b93d-7f285f35e7bd", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611420052700, "endTime": 26611490236300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d719812f-ec5d-4edd-a68f-d16cca5d3932", "logId": "e495c446-c92c-4ee8-b0cb-181c887cc277"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0dc98d93-4196-49dc-b569-6dbcd4d08bb5", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611412462200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5940d343-890c-4d00-87d4-957717a888e3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611419900000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02fbfbcd-9a0a-4f9e-bac7-b8c2470bf09c", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611412449100, "endTime": 26611420035600}, "additional": {"logType": "info", "children": [], "durationId": "8acc4c79-05d6-49b6-804a-e02a91d20b9e", "parent": "acb3ed84-9b52-48b7-8ba4-dfad401d8532"}}, {"head": {"id": "1e28a703-324b-4ce0-a938-f3a19fb2e2d3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611420065600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2340c58b-621e-4bdf-8b08-337e808040ff", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611426559300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e16edf09-a59d-4c55-9735-59dad5ec948f", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611426691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fbf29a-743f-4d0a-85f0-b3ff2f1e0583", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611426834100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee2c1739-0bea-4383-a601-ffc729294e8d", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611426928500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98bfa575-dbc6-470b-bd27-20f82440898f", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611429316200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9efd765-a311-41b4-ae67-d90d7fc09ea2", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611433752300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b0947b-536b-417f-a888-41b18e977292", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611442364800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e83760ba-ea98-4e1c-8bbb-5baa9a1cf143", "name": "Sdk init in 35 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611469505300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6db9fed2-8be8-4715-a628-20118d7c83ca", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611469631000}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 52}, "markType": "other"}}, {"head": {"id": "de5b99fc-61ee-444d-9efb-b30568358799", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611469645200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 52}, "markType": "other"}}, {"head": {"id": "9cfb4afe-5a33-4f1b-b17e-77fa053d9cff", "name": "Project task initialization takes 19 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611488968200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7922c73-62b4-47da-9c72-994b2a83bea8", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611489098200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebab0b4e-5aa1-45f2-b4f4-60d0e12c72ca", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611489230500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c42ee80-8bb0-47b1-86bb-65404877d379", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611489558600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e495c446-c92c-4ee8-b0cb-181c887cc277", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611420052700, "endTime": 26611490236300}, "additional": {"logType": "info", "children": [], "durationId": "28fab82b-c424-425d-b93d-7f285f35e7bd", "parent": "acb3ed84-9b52-48b7-8ba4-dfad401d8532"}}, {"head": {"id": "acb3ed84-9b52-48b7-8ba4-dfad401d8532", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611412445700, "endTime": 26611490259200}, "additional": {"logType": "info", "children": ["02fbfbcd-9a0a-4f9e-bac7-b8c2470bf09c", "e495c446-c92c-4ee8-b0cb-181c887cc277"], "durationId": "d719812f-ec5d-4edd-a68f-d16cca5d3932", "parent": "3f77e3d0-38cc-4539-899a-85dcb8763b41"}}, {"head": {"id": "34cfe71b-d6b0-4b06-a72a-5dcab3f41051", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611491341000, "endTime": 26611491359700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5476b7d3-14d7-4a60-a720-07ff7f632667", "logId": "424a8197-54e2-43c9-9882-6261492c064d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "424a8197-54e2-43c9-9882-6261492c064d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611491341000, "endTime": 26611491359700}, "additional": {"logType": "info", "children": [], "durationId": "34cfe71b-d6b0-4b06-a72a-5dcab3f41051", "parent": "3f77e3d0-38cc-4539-899a-85dcb8763b41"}}, {"head": {"id": "3f77e3d0-38cc-4539-899a-85dcb8763b41", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611409352300, "endTime": 26611491381000}, "additional": {"logType": "info", "children": ["80d6537c-dcac-45c9-bc73-889faccbd085", "acb3ed84-9b52-48b7-8ba4-dfad401d8532", "424a8197-54e2-43c9-9882-6261492c064d"], "durationId": "5476b7d3-14d7-4a60-a720-07ff7f632667", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "8f6210eb-3b03-41c8-ab68-110e063454c3", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611492015300, "endTime": 26611512257300}, "additional": {"children": ["fd7ad6b1-9ffd-4f37-a296-49c968d99de4", "f61c8ae7-0286-40f5-bc8e-0bc52e8baead", "cd43b4a1-38cf-4840-ad40-8b9dc364c34a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a3f45ace-3626-4042-b186-1b85096f78f8", "logId": "6bba224d-e157-489b-8bd9-eb4289b31c34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fd7ad6b1-9ffd-4f37-a296-49c968d99de4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611495146100, "endTime": 26611495165000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6210eb-3b03-41c8-ab68-110e063454c3", "logId": "1ceb8818-d40d-4cd0-947d-16382e64c699"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ceb8818-d40d-4cd0-947d-16382e64c699", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611495146100, "endTime": 26611495165000}, "additional": {"logType": "info", "children": [], "durationId": "fd7ad6b1-9ffd-4f37-a296-49c968d99de4", "parent": "6bba224d-e157-489b-8bd9-eb4289b31c34"}}, {"head": {"id": "f61c8ae7-0286-40f5-bc8e-0bc52e8baead", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611496804800, "endTime": 26611510401300}, "additional": {"children": ["4b889642-b75b-4f67-b9c4-88e6b94d1f89", "fe6019ee-baf5-4a1a-8f37-bd6cdb8f5c50"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6210eb-3b03-41c8-ab68-110e063454c3", "logId": "b867908d-1c41-46ad-8645-e5df27585310"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b889642-b75b-4f67-b9c4-88e6b94d1f89", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611496805800, "endTime": 26611500976300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f61c8ae7-0286-40f5-bc8e-0bc52e8baead", "logId": "a97cd386-e4c3-466f-80ed-5781a8f24be9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe6019ee-baf5-4a1a-8f37-bd6cdb8f5c50", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611501020400, "endTime": 26611510387000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f61c8ae7-0286-40f5-bc8e-0bc52e8baead", "logId": "899beedc-4620-4877-a5ec-09bbbb609882"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36095900-828f-40cd-a139-92fe21336a13", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611496811200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5bee72a-3e7b-467c-aa0b-2abd1c0c66ed", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611500691200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97cd386-e4c3-466f-80ed-5781a8f24be9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611496805800, "endTime": 26611500976300}, "additional": {"logType": "info", "children": [], "durationId": "4b889642-b75b-4f67-b9c4-88e6b94d1f89", "parent": "b867908d-1c41-46ad-8645-e5df27585310"}}, {"head": {"id": "9a0c65c5-f565-4cb7-bd30-87e42dcdc18a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611501036600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316fa8b1-9933-4eb6-9563-14f42267bd51", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611506830300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e18b8782-1eef-4be1-8b57-6af09ece8223", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611506998200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5dbdef4-5be8-4a65-b308-fdd3fcb77a84", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611507203200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5024e32-0e28-4048-8420-54cbd09e8d87", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611507345300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10f0ad0f-e8c0-4f8f-8f34-8d75daa166f0", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611507408000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd9740cb-60a0-4a4e-8070-73fe0f239fe7", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611507459400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a96aa6b1-e7fa-47dc-80fd-239e125406a3", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611507518800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b758edd-4449-4f44-86bc-f8e14d960336", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611510030100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2837616-7002-445c-9742-f722b0b592fa", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611510144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489a1b21-937d-4797-a1be-009f34e5888f", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611510208000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "911c8fde-6ef5-46a5-b186-2455b283762b", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611510261500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899beedc-4620-4877-a5ec-09bbbb609882", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611501020400, "endTime": 26611510387000}, "additional": {"logType": "info", "children": [], "durationId": "fe6019ee-baf5-4a1a-8f37-bd6cdb8f5c50", "parent": "b867908d-1c41-46ad-8645-e5df27585310"}}, {"head": {"id": "b867908d-1c41-46ad-8645-e5df27585310", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611496804800, "endTime": 26611510401300}, "additional": {"logType": "info", "children": ["a97cd386-e4c3-466f-80ed-5781a8f24be9", "899beedc-4620-4877-a5ec-09bbbb609882"], "durationId": "f61c8ae7-0286-40f5-bc8e-0bc52e8baead", "parent": "6bba224d-e157-489b-8bd9-eb4289b31c34"}}, {"head": {"id": "cd43b4a1-38cf-4840-ad40-8b9dc364c34a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611512206700, "endTime": 26611512227500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f6210eb-3b03-41c8-ab68-110e063454c3", "logId": "ea15b20c-b6ea-4542-8167-7a80c7a0658b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea15b20c-b6ea-4542-8167-7a80c7a0658b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611512206700, "endTime": 26611512227500}, "additional": {"logType": "info", "children": [], "durationId": "cd43b4a1-38cf-4840-ad40-8b9dc364c34a", "parent": "6bba224d-e157-489b-8bd9-eb4289b31c34"}}, {"head": {"id": "6bba224d-e157-489b-8bd9-eb4289b31c34", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611492015300, "endTime": 26611512257300}, "additional": {"logType": "info", "children": ["1ceb8818-d40d-4cd0-947d-16382e64c699", "b867908d-1c41-46ad-8645-e5df27585310", "ea15b20c-b6ea-4542-8167-7a80c7a0658b"], "durationId": "8f6210eb-3b03-41c8-ab68-110e063454c3", "parent": "78229555-d2a9-48e1-ac47-2fba10457a62"}}, {"head": {"id": "78229555-d2a9-48e1-ac47-2fba10457a62", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611491398900, "endTime": 26611512272300}, "additional": {"logType": "info", "children": ["6bba224d-e157-489b-8bd9-eb4289b31c34"], "durationId": "a3f45ace-3626-4042-b186-1b85096f78f8", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "5a705ef6-8240-4006-9291-8fb862498e37", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611536058300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d8f0be-0c04-4ee6-b2ad-8ebc48a14f72", "name": "hvigorfile, resolve hvigorfile dependencies in 80 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611592321400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f03a218e-8d7c-4658-950d-9c0824deb276", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611512342000, "endTime": 26611592453200}, "additional": {"logType": "info", "children": [], "durationId": "06dabdb0-ffe1-4b6d-9a9b-067e543c16b8", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "433f91f8-49c5-4c3f-9e35-04f87e122f67", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611593199100, "endTime": 26611593556100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "412fe17a-06df-4ab4-874e-74abdae54fd6", "logId": "7d0bacf9-aa05-4ca3-bf53-6c309041d786"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b3892f5-1fe2-4971-826d-30e5546662b9", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611593230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0bacf9-aa05-4ca3-bf53-6c309041d786", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611593199100, "endTime": 26611593556100}, "additional": {"logType": "info", "children": [], "durationId": "433f91f8-49c5-4c3f-9e35-04f87e122f67", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "2dcffd57-9df4-45ba-b560-37e8ed070ff0", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611594847400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56884829-ea98-4aaa-af3e-626675bbed78", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611601634500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af84051a-4ab3-42f9-9e57-0190887425d3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611593625000, "endTime": 26611602503200}, "additional": {"logType": "info", "children": [], "durationId": "c1c672b1-1b01-415f-806f-ee5320f849ac", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "57f73878-442b-45a4-b5e7-f7243bd8eb9d", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611606167700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0ad0ff-d789-459d-9872-6b99a0bcbc60", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611606361500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5389f3f7-8c89-45fb-a979-187e2b50bd87", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611608126500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b60b05-3e5b-41e6-aa5b-ff2045c75312", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611608218500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8b11766-e847-4969-909a-15f5f4bf2392", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611602530900, "endTime": 26611610582100}, "additional": {"logType": "info", "children": [], "durationId": "d245cf6d-41ae-4072-8fc5-e1d3fdd1022b", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "3a0ca358-cc10-42f3-a9d9-174e3a3f9a97", "name": "Configuration phase cost:209 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611610625000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78e4c997-7b91-4942-ac8f-bdd7b565497a", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611610602000, "endTime": 26611610722700}, "additional": {"logType": "info", "children": [], "durationId": "de1ea265-d2ef-4cba-946a-a82999ccbf5e", "parent": "d5223666-06b9-4bff-93c3-2490abbc25f3"}}, {"head": {"id": "d5223666-06b9-4bff-93c3-2490abbc25f3", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611395596100, "endTime": 26611610736500}, "additional": {"logType": "info", "children": ["7eebd769-72a3-4346-982c-b73f5914eeac", "ac99d5a6-315d-41a2-96b4-b5840d68dad0", "3f77e3d0-38cc-4539-899a-85dcb8763b41", "78229555-d2a9-48e1-ac47-2fba10457a62", "f03a218e-8d7c-4658-950d-9c0824deb276", "af84051a-4ab3-42f9-9e57-0190887425d3", "c8b11766-e847-4969-909a-15f5f4bf2392", "78e4c997-7b91-4942-ac8f-bdd7b565497a", "7d0bacf9-aa05-4ca3-bf53-6c309041d786"], "durationId": "412fe17a-06df-4ab4-874e-74abdae54fd6", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "a4bd59f2-d21e-4c33-a875-b10196a2b611", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611611941500, "endTime": 26611611960200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "342da67a-279a-4971-b249-e4b60dcf0c30", "logId": "40cee650-6dcd-41ca-82b8-cc931da288ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40cee650-6dcd-41ca-82b8-cc931da288ad", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611611941500, "endTime": 26611611960200}, "additional": {"logType": "info", "children": [], "durationId": "a4bd59f2-d21e-4c33-a875-b10196a2b611", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "d6332318-38e0-4cda-80ba-855a0d494eef", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611610764100, "endTime": 26611611974000}, "additional": {"logType": "info", "children": [], "durationId": "3836675a-c3de-4e8f-86e2-1add72a4b493", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "c8a9b239-2fee-4f82-9403-bb1d348b0c5e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611611980000, "endTime": 26611611986800}, "additional": {"logType": "info", "children": [], "durationId": "2119442e-dc7d-4411-beef-95d660269988", "parent": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6"}}, {"head": {"id": "aff9875a-6cc8-4d0a-ac0b-8e9072b06bd6", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611381388100, "endTime": 26611611994700}, "additional": {"logType": "info", "children": ["33bc49c0-84b7-4a99-bd10-c93767255115", "d5223666-06b9-4bff-93c3-2490abbc25f3", "d6332318-38e0-4cda-80ba-855a0d494eef", "c8a9b239-2fee-4f82-9403-bb1d348b0c5e", "1ef3d942-c639-44d3-9be4-eccd804fa510", "7c03c107-3fe2-47d9-8e8f-9133dee63564", "40cee650-6dcd-41ca-82b8-cc931da288ad"], "durationId": "342da67a-279a-4971-b249-e4b60dcf0c30"}}, {"head": {"id": "b9c25c5f-7d53-4ecb-9ac0-36f18a4b213c", "name": "Configuration task cost before running: 234 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611612147800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b32c7b39-540f-4db7-84f8-c2cf737fe799", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611616071300, "endTime": 26611623738700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "62c87c2f-b071-45f2-a094-0a2842d24ced", "logId": "1f295c6f-70fb-484f-be4f-378ad87610e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62c87c2f-b071-45f2-a094-0a2842d24ced", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611613432000}, "additional": {"logType": "detail", "children": [], "durationId": "b32c7b39-540f-4db7-84f8-c2cf737fe799"}}, {"head": {"id": "2e967d36-2059-4aa6-b85a-76374e20c8c8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611613795100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4479b8-e635-4017-926c-c5898e1a6424", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611613893300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab49ccc3-b180-4413-8cec-f8a62e9668fd", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611616098400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80f9389b-a4ce-4880-8ef8-011772e1ab13", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611623391200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef78d90e-f555-4329-ab4e-7c2c07fb7b29", "name": "entry : default@PreBuild cost memory 0.309478759765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611623645500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f295c6f-70fb-484f-be4f-378ad87610e7", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611616071300, "endTime": 26611623738700}, "additional": {"logType": "info", "children": [], "durationId": "b32c7b39-540f-4db7-84f8-c2cf737fe799"}}, {"head": {"id": "ee309a35-384f-4526-bfab-44f952f70c1e", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611629061100, "endTime": 26611630744700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0e2682d4-0e94-4a28-b525-d7ddcff10ffc", "logId": "43afc27f-36f3-41d7-bf1e-d4e40a74a72f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e2682d4-0e94-4a28-b525-d7ddcff10ffc", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611627973300}, "additional": {"logType": "detail", "children": [], "durationId": "ee309a35-384f-4526-bfab-44f952f70c1e"}}, {"head": {"id": "7feb3f12-0f5d-4a98-acb2-b672c1348b85", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611628315700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "704cced4-788d-416e-8d6e-5816cfc02a60", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611628421500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5bdc81-26d4-44a2-8ba5-9334f678d48f", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611629071400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66d327d8-9433-4771-bcf5-878b36513091", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611629736300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4798e6d-3189-475c-a178-86a01651a0ad", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611630561400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e04977a-e3b5-4353-a264-1b5e3d6ca17c", "name": "entry : default@GenerateMetadata cost memory 0.095733642578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611630670200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43afc27f-36f3-41d7-bf1e-d4e40a74a72f", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611629061100, "endTime": 26611630744700}, "additional": {"logType": "info", "children": [], "durationId": "ee309a35-384f-4526-bfab-44f952f70c1e"}}, {"head": {"id": "533f5115-af40-4b0c-a334-f76bc1b1cc01", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633418100, "endTime": 26611634061900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9f38434f-68ee-42f4-8ea4-4b70f9c52876", "logId": "d5cee13d-1670-4517-8ba8-0dbd92676295"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f38434f-68ee-42f4-8ea4-4b70f9c52876", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611632541500}, "additional": {"logType": "detail", "children": [], "durationId": "533f5115-af40-4b0c-a334-f76bc1b1cc01"}}, {"head": {"id": "f0635a50-3b10-440b-ac38-a145ca0ede8a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633059500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bab9634a-3628-4e55-a51b-23e666b82db0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633181000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2fb5a4-01eb-426e-9cd1-c7a1fc218ad3", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633432700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b23f55-3229-439f-872a-3aa0a54f5d1b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633690200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d4a184-26dc-4421-8971-9f28904fc2cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633768300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0072cb5a-9262-494b-bce1-66455acc42fa", "name": "entry : default@ConfigureCmake cost memory 0.0370635986328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633890100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7e7b125-f242-49ff-858c-eec56dca7ee7", "name": "runTaskFromQueue task cost before running: 256 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633997200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5cee13d-1670-4517-8ba8-0dbd92676295", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611633418100, "endTime": 26611634061900, "totalTime": 556800}, "additional": {"logType": "info", "children": [], "durationId": "533f5115-af40-4b0c-a334-f76bc1b1cc01"}}, {"head": {"id": "ae717682-70f6-45f8-9409-d34f753abb37", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611637646700, "endTime": 26611640268200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "05307fff-5247-4758-a187-8511a3d4b140", "logId": "49ad1236-a08b-457b-b47c-8d227b27b367"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05307fff-5247-4758-a187-8511a3d4b140", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611636361600}, "additional": {"logType": "detail", "children": [], "durationId": "ae717682-70f6-45f8-9409-d34f753abb37"}}, {"head": {"id": "8b154f8a-16bb-4a00-b20d-e1a6a9919919", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611636839600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22f63386-b657-43ae-94bb-17b51093f7c5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611636961300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fbac6fa-85ed-4160-b102-1f8755cdca53", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611637659200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c273a0fb-47b8-4351-a1e0-713500c<PERSON>ce", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611639956500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9762cf29-3d16-4a69-9517-c9e270368ebe", "name": "entry : default@MergeProfile cost memory 0.10739898681640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611640164500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49ad1236-a08b-457b-b47c-8d227b27b367", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611637646700, "endTime": 26611640268200}, "additional": {"logType": "info", "children": [], "durationId": "ae717682-70f6-45f8-9409-d34f753abb37"}}, {"head": {"id": "075db381-13cb-484d-a30e-a39e81b2b8ac", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611643528200, "endTime": 26611646039200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f7c000f5-f769-4af1-a412-205951e3cb38", "logId": "ccb0bae8-2713-493e-8d7a-44cfccbbc371"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7c000f5-f769-4af1-a412-205951e3cb38", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611642263300}, "additional": {"logType": "detail", "children": [], "durationId": "075db381-13cb-484d-a30e-a39e81b2b8ac"}}, {"head": {"id": "d7516550-614b-473f-b9f7-2e25bb6d124d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611642647700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee482d7-e472-45b1-8850-63b57839e804", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611642757400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "004ea4a8-86a7-453d-8397-d3e2f1ec8674", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611643542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f0791e9-4e96-4e7c-8c5f-4ebcb75ce054", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611644475800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74ccfe3f-4c6a-4cf8-bb81-e69da048c066", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611645645300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb471502-4f68-40b0-9f89-4a042fac2749", "name": "entry : default@CreateBuildProfile cost memory 0.10503387451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611645867100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb0bae8-2713-493e-8d7a-44cfccbbc371", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611643528200, "endTime": 26611646039200}, "additional": {"logType": "info", "children": [], "durationId": "075db381-13cb-484d-a30e-a39e81b2b8ac"}}, {"head": {"id": "e8cce41c-805b-4cdd-b037-63f92b5d0222", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650216300, "endTime": 26611650891300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "9d886590-712a-4213-a2d9-98fee2173440", "logId": "79ac126c-56fb-4c1f-9b80-7d85cc3a2870"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d886590-712a-4213-a2d9-98fee2173440", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611648073000}, "additional": {"logType": "detail", "children": [], "durationId": "e8cce41c-805b-4cdd-b037-63f92b5d0222"}}, {"head": {"id": "01f23fad-a31c-4839-9786-0958db18fc91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611648449800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39cd3078-5c2c-42dd-a6a9-7af7f3dd26c3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611648552800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5995adca-b6ce-407e-a3b3-579215d7557e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650254700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38103486-00f6-4acc-bb30-06075a0c314d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650428600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78bdf3bc-8964-4662-ba1e-7bcb798be8f8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650496000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf142581-9e47-452c-8a57-37d433e5cdc7", "name": "entry : default@PreCheckSyscap cost memory 0.03728485107421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650587700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d8b4a5-b090-458b-a340-560062d7aa95", "name": "runTaskFromQueue task cost before running: 273 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650733400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ac126c-56fb-4c1f-9b80-7d85cc3a2870", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611650216300, "endTime": 26611650891300, "totalTime": 516600}, "additional": {"logType": "info", "children": [], "durationId": "e8cce41c-805b-4cdd-b037-63f92b5d0222"}}, {"head": {"id": "789e0e1d-3f81-427b-8829-dbbf433c2144", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611659674600, "endTime": 26611660450100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "95b9ec1b-2153-42d6-9399-c52f231bbfb3", "logId": "89c7c310-578c-48ae-b229-fa8a04be67ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95b9ec1b-2153-42d6-9399-c52f231bbfb3", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611655068200}, "additional": {"logType": "detail", "children": [], "durationId": "789e0e1d-3f81-427b-8829-dbbf433c2144"}}, {"head": {"id": "cce59159-1cc0-499e-8158-975a03629213", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611655630000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "206b2349-d155-4f5c-824d-da86a2c50cdd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611655747100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0129e0b-60ef-45a3-82bb-bd339caf59bb", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611659691000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b64f6d-4159-4928-bb8d-c46d8372a77c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611660023200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f892c6-d208-4bd3-ba5e-54cb9541fb97", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03948211669921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611660244300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a2e37f-c813-4a33-b9e7-df0a28211130", "name": "runTaskFromQueue task cost before running: 282 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611660381900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89c7c310-578c-48ae-b229-fa8a04be67ec", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611659674600, "endTime": 26611660450100, "totalTime": 683900}, "additional": {"logType": "info", "children": [], "durationId": "789e0e1d-3f81-427b-8829-dbbf433c2144"}}, {"head": {"id": "465e396e-7dcb-4fdb-a16f-05d22581a4b9", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611664056400, "endTime": 26611667886400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "75b625d2-e649-4a70-91d2-21f3fbb572ac", "logId": "5bd9b7e9-4aa2-4e2b-ae77-9fd8db5a8576"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "75b625d2-e649-4a70-91d2-21f3fbb572ac", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611662007900}, "additional": {"logType": "detail", "children": [], "durationId": "465e396e-7dcb-4fdb-a16f-05d22581a4b9"}}, {"head": {"id": "55a90556-def5-4532-8c83-9833926a3e76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611662342600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1951118-330b-4ed1-a3ae-b3be0e54c673", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611662437800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b67d2bbb-231b-4b06-bde7-ecc2965780da", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611664070100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5ec339-4631-45cb-919f-e411b3928dc4", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611666133300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcace69d-3520-46ae-9879-2d98b6527fc4", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611666909500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f148fc1-2c24-4298-a62d-d74f0ee3735f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611667141300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "183ee48d-ed0b-432c-99f2-0830268b30be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611667249400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28c802ac-11e1-4979-9bb5-202ec2cb848e", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1198577880859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611667570700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "debc7c1b-1abe-4590-ba61-b7cd112d88f7", "name": "runTaskFromQueue task cost before running: 290 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611667770400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bd9b7e9-4aa2-4e2b-ae77-9fd8db5a8576", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611664056400, "endTime": 26611667886400, "totalTime": 3686800}, "additional": {"logType": "info", "children": [], "durationId": "465e396e-7dcb-4fdb-a16f-05d22581a4b9"}}, {"head": {"id": "6591721a-1e22-4a94-a87d-0488de87caea", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611671627800, "endTime": 26611672066200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6c83b12b-ebd7-4d1d-ac05-12eec1b22180", "logId": "d8418eae-f0b0-460b-9c7f-368d2dd52203"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6c83b12b-ebd7-4d1d-ac05-12eec1b22180", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611670340300}, "additional": {"logType": "detail", "children": [], "durationId": "6591721a-1e22-4a94-a87d-0488de87caea"}}, {"head": {"id": "1b673089-c203-4269-93e7-81e39fc97e84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611670737900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e517242a-c21c-4758-9e74-a31b2a220520", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611670861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aabbccdd-2128-4992-b63c-35c7820b4e58", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611671641200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f603d15-f639-4bfb-99da-42ff2b86013b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611671754900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffb2d6c8-6d8a-4bfd-89be-d5949a7f3d1e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611671841100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54226c49-9ad5-4ccd-87e0-28cc712e4f62", "name": "entry : default@BuildNativeWithCmake cost memory 0.038116455078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611671931100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2649b738-094b-4118-8ba0-fce09044d00e", "name": "runTaskFromQueue task cost before running: 294 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611672012700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8418eae-f0b0-460b-9c7f-368d2dd52203", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611671627800, "endTime": 26611672066200, "totalTime": 366700}, "additional": {"logType": "info", "children": [], "durationId": "6591721a-1e22-4a94-a87d-0488de87caea"}}, {"head": {"id": "615aee96-617d-45bc-9920-0433fcde189d", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611675750100, "endTime": 26611678708500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0071b7b6-1e84-4d24-8f3b-01e8d7d71186", "logId": "4a846dde-bcc7-409d-bcfe-d48f4b38f1d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0071b7b6-1e84-4d24-8f3b-01e8d7d71186", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611674644300}, "additional": {"logType": "detail", "children": [], "durationId": "615aee96-617d-45bc-9920-0433fcde189d"}}, {"head": {"id": "fdb13480-008c-4bab-a7a0-b4fd373eae6d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611675045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4599e8c8-8164-4ed9-b70e-70c596c57c01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611675147200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "698706d2-1d56-4287-86f5-2a9c4c1236f0", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611675760500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a2fa4c-044f-4192-95b1-f948b2d97260", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611678488600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5ab049-0e0b-4e4f-91b0-d4be98b9c42b", "name": "entry : default@MakePackInfo cost memory 0.140289306640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611678638400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a846dde-bcc7-409d-bcfe-d48f4b38f1d1", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611675750100, "endTime": 26611678708500}, "additional": {"logType": "info", "children": [], "durationId": "615aee96-617d-45bc-9920-0433fcde189d"}}, {"head": {"id": "1702bfea-6283-4dfb-9290-036e93f1e270", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611683540900, "endTime": 26611686423100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "59ea0633-86a4-425b-a723-575bd18d0af8", "logId": "320cab21-9d8c-41f0-84c4-2588002b5825"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59ea0633-86a4-425b-a723-575bd18d0af8", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611680684700}, "additional": {"logType": "detail", "children": [], "durationId": "1702bfea-6283-4dfb-9290-036e93f1e270"}}, {"head": {"id": "445e1c86-3b7d-46f9-b216-ebfa63a498f1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611681309500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "274ac460-de4d-4c6b-a20a-bc7e7df74517", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611681423600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab2a993-b289-4f22-a53b-687c37ce8112", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611683563100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "900b3895-880c-451d-8093-b51a26209111", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611683745500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02a4fbcf-4bfc-4f6f-a8ad-735673c70435", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611684516900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41cac4d4-2405-441f-bc75-37ac43f26a28", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611685925600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74e7adc8-0226-4353-ab00-e2f220dceb4b", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611686043200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a9e164-d1ea-47dd-bee3-b6596d4e5279", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611686133800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbff4f7c-8482-4d18-ae2e-69e39430921d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611686192300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "138f6748-65c3-48cc-9dda-c58c9dd17943", "name": "entry : default@SyscapTransform cost memory 0.15540313720703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611686283800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f15ab5-b2c6-44e6-a1eb-3eb1a0e1b419", "name": "runTaskFromQueue task cost before running: 308 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611686368300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "320cab21-9d8c-41f0-84c4-2588002b5825", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611683540900, "endTime": 26611686423100, "totalTime": 2813900}, "additional": {"logType": "info", "children": [], "durationId": "1702bfea-6283-4dfb-9290-036e93f1e270"}}, {"head": {"id": "453233cd-e8c8-4944-85ca-47bd186309cc", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611689991000, "endTime": 26611691237100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "b41bc3ce-8e91-4ed3-937b-f75499a3bf25", "logId": "45016d6e-4a79-4326-9b09-6066765827ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b41bc3ce-8e91-4ed3-937b-f75499a3bf25", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611688175300}, "additional": {"logType": "detail", "children": [], "durationId": "453233cd-e8c8-4944-85ca-47bd186309cc"}}, {"head": {"id": "30df4685-29d1-409a-a95d-df9c050b3124", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611688547500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cb713a3-dee2-48be-8cff-a2f5ecdcbccd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611688654500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73a26a86-7edf-41b7-bce7-23cdc26b4cc8", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611690007000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd2b377d-8579-4b0c-aadc-b523080cd2a5", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611690990600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fa9eed1-e15f-4472-bf90-d7db0944a58d", "name": "entry : default@ProcessProfile cost memory 0.06082916259765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611691149200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45016d6e-4a79-4326-9b09-6066765827ed", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611689991000, "endTime": 26611691237100}, "additional": {"logType": "info", "children": [], "durationId": "453233cd-e8c8-4944-85ca-47bd186309cc"}}, {"head": {"id": "97ada962-c7ef-4ce5-b7a7-544dc9d2bf0b", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611695169200, "endTime": 26611702369200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "48f83330-7b5f-416b-85cc-6671741537e4", "logId": "307f2960-d106-4716-aaa6-18ce6ad39444"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "48f83330-7b5f-416b-85cc-6671741537e4", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611693042300}, "additional": {"logType": "detail", "children": [], "durationId": "97ada962-c7ef-4ce5-b7a7-544dc9d2bf0b"}}, {"head": {"id": "7c1a3364-143b-41dc-944e-f127814de32e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611693441400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6380ac39-23ef-4a2d-8b32-6e1812186d74", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611693554000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14e07081-f474-464b-b221-bc26fbc13c03", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611695182100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ab18e9-459a-44a7-b68e-03be70d53f85", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611702146400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc81580b-cca4-46f1-aea2-d26482d7705c", "name": "entry : default@ProcessRouterMap cost memory 0.2037506103515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611702296300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307f2960-d106-4716-aaa6-18ce6ad39444", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611695169200, "endTime": 26611702369200}, "additional": {"logType": "info", "children": [], "durationId": "97ada962-c7ef-4ce5-b7a7-544dc9d2bf0b"}}, {"head": {"id": "a2e03d60-98d1-4e17-9e7f-2ed70d950e81", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611707543700, "endTime": 26611708737500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "13b8838f-27d2-419b-9b4c-cb9be6ad83b1", "logId": "553caee7-fd38-4a29-9969-05bd87204741"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13b8838f-27d2-419b-9b4c-cb9be6ad83b1", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611705074400}, "additional": {"logType": "detail", "children": [], "durationId": "a2e03d60-98d1-4e17-9e7f-2ed70d950e81"}}, {"head": {"id": "69541726-b8bb-46d7-afad-b2a3bed13480", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611705599800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30ca747-e2c7-4b69-8ed5-5cb8e73b2613", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611706011000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ca7b08b-5a2d-4e8b-9a69-06f28dfbef42", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611707560700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e2435d-48f1-40e5-b0c0-6a8c24428ecf", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611707720700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4320c703-11c3-420f-86b1-683834a52872", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611707865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52fdf5b4-5b8d-411b-b843-8a9f4387d671", "name": "entry : default@BuildNativeWithNinja cost memory 0.057708740234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611708550900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66048101-70ed-4c98-a9cc-e27960de84d0", "name": "runTaskFromQueue task cost before running: 331 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611708670100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "553caee7-fd38-4a29-9969-05bd87204741", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611707543700, "endTime": 26611708737500, "totalTime": 1103900}, "additional": {"logType": "info", "children": [], "durationId": "a2e03d60-98d1-4e17-9e7f-2ed70d950e81"}}, {"head": {"id": "6912fa56-85f6-4b32-9129-9f59270dc10d", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611714658300, "endTime": 26611722022800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6e4c9231-9f7a-474b-b2e9-a8ef45e047a9", "logId": "f1fb2cbe-be88-4152-a8e2-aa96cfa3db80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e4c9231-9f7a-474b-b2e9-a8ef45e047a9", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611710861200}, "additional": {"logType": "detail", "children": [], "durationId": "6912fa56-85f6-4b32-9129-9f59270dc10d"}}, {"head": {"id": "6482c42a-5fb2-4a80-9c29-f71777ff0b50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611711234400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054086aa-1f6c-4996-b1e3-cae1bf88163d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611711573700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a0499a-0725-421e-85bd-0ccc6ba42c61", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611713064800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45ce03c5-7f27-430d-a609-a2a154966833", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611717056900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32169a6f-a29e-453a-80e5-31577a303765", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611719368800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb55109-d2f1-4879-aeb8-d7d45e7f4f34", "name": "entry : default@ProcessResource cost memory 0.30698394775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611719518700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1fb2cbe-be88-4152-a8e2-aa96cfa3db80", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611714658300, "endTime": 26611722022800}, "additional": {"logType": "info", "children": [], "durationId": "6912fa56-85f6-4b32-9129-9f59270dc10d"}}, {"head": {"id": "72319eec-e92d-4f27-9f80-e9f223295ed0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611731419900, "endTime": 26611752658500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "86aab1dd-9374-4e5d-ac6d-70bfc702db53", "logId": "4b3a3054-6cc8-415e-abb2-4c18dead526b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86aab1dd-9374-4e5d-ac6d-70bfc702db53", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611726866600}, "additional": {"logType": "detail", "children": [], "durationId": "72319eec-e92d-4f27-9f80-e9f223295ed0"}}, {"head": {"id": "9a41873e-7361-4a78-854b-a9ccda250a8b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611727243000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea59856-e022-4376-a54c-21f58df1572e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611727454400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4e23e75-dc75-4711-b502-cd394724597c", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611731436000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b51b7472-dd4e-4c56-b51f-248e72ba2e4c", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611752268300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0561de-3a84-4d53-9967-2d6cb3777ab8", "name": "entry : default@GenerateLoaderJson cost memory 0.7073135375976562", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611752487500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b3a3054-6cc8-415e-abb2-4c18dead526b", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611731419900, "endTime": 26611752658500}, "additional": {"logType": "info", "children": [], "durationId": "72319eec-e92d-4f27-9f80-e9f223295ed0"}}, {"head": {"id": "76ab17f3-4dd4-4c28-9a56-1a31790bab08", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611764817400, "endTime": 26611772163700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1c343974-483c-40bc-a1ce-92d6018e4633", "logId": "f9622132-3131-40b2-9c7a-59dd3bcc0aa4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c343974-483c-40bc-a1ce-92d6018e4633", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611762456800}, "additional": {"logType": "detail", "children": [], "durationId": "76ab17f3-4dd4-4c28-9a56-1a31790bab08"}}, {"head": {"id": "14d84e36-0702-4195-8651-ffce260f5c27", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611762988900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17698975-c37c-452c-876a-4513567ec470", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611763116000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00a5cafa-ce54-45cd-8a49-66fb3f991712", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611764845300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2af44826-7ca5-4db6-b76c-a5a3cb5d001b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611770289300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b77aa97-d194-4de3-95c5-72c743a5b4a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611770425500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb76fec3-c415-46c0-af7c-dbc421c0c197", "name": "entry : default@ProcessLibs cost memory 0.1267547607421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611771702900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7910448-0770-4b62-88fc-0b8c2c75b87b", "name": "runTaskFromQueue task cost before running: 394 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611772000200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9622132-3131-40b2-9c7a-59dd3bcc0aa4", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611764817400, "endTime": 26611772163700, "totalTime": 7046600}, "additional": {"logType": "info", "children": [], "durationId": "76ab17f3-4dd4-4c28-9a56-1a31790bab08"}}, {"head": {"id": "409c174d-a658-45ae-9d66-e7d20cc22d95", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611785135600, "endTime": 26612077601600}, "additional": {"children": ["8f158dee-563c-41f2-8e5d-bc7db372669b", "97ad0731-81f1-4885-90dd-8c50779d31f5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources' has been changed."], "detailId": "ae180d1c-38d4-42ae-bc5c-23e002cba745", "logId": "4bbf6391-8a6e-42c2-a461-11bdb1c0a682"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ae180d1c-38d4-42ae-bc5c-23e002cba745", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611779115100}, "additional": {"logType": "detail", "children": [], "durationId": "409c174d-a658-45ae-9d66-e7d20cc22d95"}}, {"head": {"id": "079add00-e85c-4feb-85d9-0c42508194ce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611779714200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a656115-8675-4533-b035-42b68dcb170d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611779904100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6abc7e63-4e27-4b25-a96b-92f2f4173580", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611780938500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48af40e6-fdb5-49f9-af1b-c455a80f5fa5", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611785167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c37f6c3-87dd-4cec-8586-c29816589e8f", "name": "entry:default@CompileResource is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611801875000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cdb8063-1c20-484d-87da-f29b9e84bb12", "name": "Incremental task entry:default@CompileResource pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611802042000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f158dee-563c-41f2-8e5d-bc7db372669b", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611803058500, "endTime": 26611854682200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "409c174d-a658-45ae-9d66-e7d20cc22d95", "logId": "f46e4c07-162e-4eb2-9b49-149f1e6b2f81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f46e4c07-162e-4eb2-9b49-149f1e6b2f81", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611803058500, "endTime": 26611854682200}, "additional": {"logType": "info", "children": [], "durationId": "8f158dee-563c-41f2-8e5d-bc7db372669b", "parent": "4bbf6391-8a6e-42c2-a461-11bdb1c0a682"}}, {"head": {"id": "e17e4191-5a0e-44e7-824c-381731841a58", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-l',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611855295200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ad0731-81f1-4885-90dd-8c50779d31f5", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611856760400, "endTime": 26612072319400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "409c174d-a658-45ae-9d66-e7d20cc22d95", "logId": "280ffca1-5ed9-4d64-a34e-d8f4ed22b32a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "708bb680-ac9d-4f38-8a8c-bc8da9d8a240", "name": "current process  memoryUsage: {\n  rss: 542711808,\n  heapTotal: 147333120,\n  heapUsed: 121670752,\n  external: 3132850,\n  arrayBuffers: 60387\n} os memoryUsage :21.78713607788086", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611858280900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20879fd9-d814-4808-8c87-a8ec321ad165", "name": "06-24 15:53:00.102  4260 25360 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611999130700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5072bed6-b165-4f78-87e7-5f0c5b5c076f", "name": "06-24 15:53:00.102  4260 25360 E C01400/ImageTranscoderUtils: ImageFilter IN D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources\\base\\media\\app_icon.png\r\n06-24 15:53:00.103  4260 25360 E C01400/ImageTranscoder: TranscodeSLR Only zoom-out is supported.\r\nWarning: ScaleImage failed, error message: INVALID_PARAMETERS, file path = D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources\\base\\media\\app_icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612001389300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da60e7f8-b6c1-4e7b-be9b-b532bf9a842c", "name": "06-24 15:53:00.107  4260 25360 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612003062000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a711e490-c8cf-4594-ad7a-7b11d43f3e62", "name": "06-24 15:53:00.107  4260 25360 E C01400/ImageTranscoderUtils: ImageFilter IN D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\media\\icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612003288600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc07e6a2-8ae4-4c1d-b8f3-cb9d2107f035", "name": "06-24 15:53:00.107  4260 25360 E C01400/ImageTranscoder: TranscodeSLR Only zoom-out is supported.\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612003502000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c266989-2411-4bf6-8bb5-5aa11286a916", "name": "Warning: ScaleImage failed, error message: INVALID_PARAMETERS, file path = D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources\\base\\media\\icon.png\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612003653500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6e44f7-478f-4a85-b33a-2a554a67b1d1", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612006454400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac81c970-a5fd-4f2e-8d35-e7bb37e51501", "name": "astcenc customized so is not be opened when dlclose!\r\n", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612069069400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "280ffca1-5ed9-4d64-a34e-d8f4ed22b32a", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611856760400, "endTime": 26612072319400}, "additional": {"logType": "info", "children": [], "durationId": "97ad0731-81f1-4885-90dd-8c50779d31f5", "parent": "4bbf6391-8a6e-42c2-a461-11bdb1c0a682"}}, {"head": {"id": "e2d018bb-9d31-4efd-8bc1-e187b834a947", "name": "entry : default@CompileResource cost memory -13.655632019042969", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612077327200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4890b2fd-abda-4e10-9bdf-903a611c9fff", "name": "runTaskFromQueue task cost before running: 700 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612077520900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bbf6391-8a6e-42c2-a461-11bdb1c0a682", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611785135600, "endTime": 26612077601600, "totalTime": 292333100}, "additional": {"logType": "info", "children": ["f46e4c07-162e-4eb2-9b49-149f1e6b2f81", "280ffca1-5ed9-4d64-a34e-d8f4ed22b32a"], "durationId": "409c174d-a658-45ae-9d66-e7d20cc22d95"}}, {"head": {"id": "1952a4b2-6168-4ed4-9787-2c953e1ca2ef", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612084214700, "endTime": 26612086014400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3d13ee54-5e90-4aa4-baa6-21c155ecbb66", "logId": "b237d964-fd87-4f3f-a1c8-2a697e61c72b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d13ee54-5e90-4aa4-baa6-21c155ecbb66", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612080616000}, "additional": {"logType": "detail", "children": [], "durationId": "1952a4b2-6168-4ed4-9787-2c953e1ca2ef"}}, {"head": {"id": "04857379-ac18-4039-8063-414d949ef2fb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612081061900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88f19795-ba93-4e67-88b7-5397e3d9b49e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612081186000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db51ea0d-fcc9-4c73-b205-feaf6ad47851", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612084234400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e888653b-2d73-4fde-b406-814d0b259702", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612084625000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18408b66-caed-407d-a1d0-689d13bf6979", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612085764300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0575d6-49f3-45cb-a63e-1826b62b08ee", "name": "entry : default@DoNativeStrip cost memory 0.07859039306640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612085931700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b237d964-fd87-4f3f-a1c8-2a697e61c72b", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612084214700, "endTime": 26612086014400}, "additional": {"logType": "info", "children": [], "durationId": "1952a4b2-6168-4ed4-9787-2c953e1ca2ef"}}, {"head": {"id": "c33b52af-1705-40ee-a795-058f215ee8be", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612102921000, "endTime": 26614394183500}, "additional": {"children": ["f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "ef15231d-19ae-4a96-b0b9-47a4b7aa51a7"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed."], "detailId": "a826c106-1d0a-4f28-a680-2a9ad151c7e2", "logId": "587fa5f5-ad8e-4809-ad09-4246b435eaa1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a826c106-1d0a-4f28-a680-2a9ad151c7e2", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612098159300}, "additional": {"logType": "detail", "children": [], "durationId": "c33b52af-1705-40ee-a795-058f215ee8be"}}, {"head": {"id": "6c978bae-3e80-45b9-8986-bf1e7b94c845", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612098560600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ac5f357-b814-49e1-9f38-032cc13708e9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612098693100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d304cdd5-c950-49f6-9bca-0c15e5158e03", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612102950000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90ce0831-8a34-42fb-8dc2-578376599fa8", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612112591400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1513f2dc-3022-465d-8c31-5034961824f5", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612112749800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2cc1320-f6a6-4c81-85f1-d3038da5e3a6", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612137449000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a591fb94-f211-459e-8ba6-3b0916eb27d0", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612137921500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "695d1538-9ae9-4bcf-8ce9-a51e28cfc0b2", "name": "default@CompileArkTS work[18] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612139118200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26612366868400, "endTime": 26613730995600}, "additional": {"children": ["65a887f9-7950-4342-a6e8-d38bcfe57f64", "7ba31c8f-ed7a-41c0-b0ea-4b736f62b2f0", "d0787407-554f-4681-9af8-9f36b65a7db1", "245c9058-67da-4928-a2f1-28df43a8f54f", "4e849526-ae35-4f67-bfea-633d6afd3715", "f67eda0c-3cbb-4586-a9b1-21aa89d0d365"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c33b52af-1705-40ee-a795-058f215ee8be", "logId": "0a556429-5c39-441f-b8e9-c980c248af08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8ca0243-0f77-40e4-8020-c09b49f08643", "name": "default@CompileArkTS work[18] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612140602500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b586ea20-e03d-45c2-b62f-a0471f452c3d", "name": "default@CompileArkTS work[18] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612140737300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fc3a744-9a3b-4adc-8058-be6a7dcad100", "name": "CopyResources startTime: 26612140813200", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612140817000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5135b2e2-b580-4f00-bc00-b665b98184c3", "name": "default@CompileArkTS work[19] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612140913600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef15231d-19ae-4a96-b0b9-47a4b7aa51a7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26614379682400, "endTime": 26614393669300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c33b52af-1705-40ee-a795-058f215ee8be", "logId": "3acebd68-f172-4425-9b9a-d801212f2708"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0cd2266-1dbf-4a61-915b-d4b0130c9fe9", "name": "default@CompileArkTS work[19] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612142791400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6571ea1e-28ca-4d67-bfc5-6e611a79c343", "name": "default@CompileArkTS work[19] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612142967900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0e2c922-9022-4e70-b635-687abe964882", "name": "entry : default@CompileArkTS cost memory 1.5438461303710938", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612143124000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa917fd-5698-4676-9bfd-d15c05674580", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612151268000, "endTime": 26612154891100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "5a88c102-8c14-4ef5-b737-3d339224f267", "logId": "ae40b058-09a9-41cb-8bc5-37471f77c077"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a88c102-8c14-4ef5-b737-3d339224f267", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612144743800}, "additional": {"logType": "detail", "children": [], "durationId": "8fa917fd-5698-4676-9bfd-d15c05674580"}}, {"head": {"id": "ca87b006-ea8e-4657-baef-b8fabc415dd2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612145326900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4d6418-afe8-4ea4-8406-8769e2e7d32b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612145455200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e53a056a-9f78-4ff8-b797-362a8fbf335d", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612151290600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60b32449-b189-4870-8fae-94785052ed75", "name": "entry : default@BuildJS cost memory 0.13067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612154539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4954c3d-1f2d-432a-9d90-d9329d2b0b1d", "name": "runTaskFromQueue task cost before running: 777 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612154815200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae40b058-09a9-41cb-8bc5-37471f77c077", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612151268000, "endTime": 26612154891100, "totalTime": 3519500}, "additional": {"logType": "info", "children": [], "durationId": "8fa917fd-5698-4676-9bfd-d15c05674580"}}, {"head": {"id": "bd2c5ded-2fa5-4050-b04e-2ffe643e42b7", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612164396300, "endTime": 26612173850600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "14af7046-c82a-4132-84e6-bd63251b9e51", "logId": "4ddfa84a-0f8f-4055-bbc6-364f366c5977"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14af7046-c82a-4132-84e6-bd63251b9e51", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612157676000}, "additional": {"logType": "detail", "children": [], "durationId": "bd2c5ded-2fa5-4050-b04e-2ffe643e42b7"}}, {"head": {"id": "eb0fba21-ea37-42f5-b247-b324475e3101", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612158267300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cab21de9-901d-4140-b858-023b76f42da8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612158430700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36ab8c71-d33a-40cc-bc88-e8fbb4eaeb20", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612164417900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0e0310-6e31-495e-ae0c-fef6999f1a43", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612166389700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0386084-17d9-49cf-8c15-bafe73659c30", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612173356300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9980911-acb6-47aa-b45c-38edda16fc02", "name": "entry : default@CacheNativeLibs cost memory 0.093292236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612173526600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ddfa84a-0f8f-4055-bbc6-364f366c5977", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612164396300, "endTime": 26612173850600}, "additional": {"logType": "info", "children": [], "durationId": "bd2c5ded-2fa5-4050-b04e-2ffe643e42b7"}}, {"head": {"id": "df0bea46-ccdf-4824-807a-f24a5b1d19d9", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612363386800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c4412e-a599-4c74-b510-71c5c84e15d2", "name": "default@CompileArkTS work[18] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612364036800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "989c9fc9-d421-4162-bdf9-aa65cee01c3f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613024597200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bec298-b9d2-4e99-92bf-fdf2775ea136", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613028573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a3ff591-778b-4e51-9236-e1e23ed528a4", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613029013400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b90368ef-384e-41bd-b4d6-7352f8963cf0", "name": "default@CompileArkTS work[19] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613030909300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1574412a-890e-460b-850a-43879696ae8e", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613731252300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a887f9-7950-4342-a6e8-d38bcfe57f64", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26612367164000, "endTime": 26612381137300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "logId": "6421b143-724e-419c-968f-5ce860d5f010"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6421b143-724e-419c-968f-5ce860d5f010", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612367164000, "endTime": 26612381137300}, "additional": {"logType": "info", "children": [], "durationId": "65a887f9-7950-4342-a6e8-d38bcfe57f64", "parent": "0a556429-5c39-441f-b8e9-c980c248af08"}}, {"head": {"id": "7ba31c8f-ed7a-41c0-b0ea-4b736f62b2f0", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26612381155100, "endTime": 26612381256600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "logId": "c12e64da-86dc-4747-8b1c-ba5fc5a4193e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c12e64da-86dc-4747-8b1c-ba5fc5a4193e", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612381155100, "endTime": 26612381256600}, "additional": {"logType": "info", "children": [], "durationId": "7ba31c8f-ed7a-41c0-b0ea-4b736f62b2f0", "parent": "0a556429-5c39-441f-b8e9-c980c248af08"}}, {"head": {"id": "d0787407-554f-4681-9af8-9f36b65a7db1", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26612381264600, "endTime": 26612381837800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "logId": "82f5aa80-7ac5-46b6-9f6b-6aaf42642acb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82f5aa80-7ac5-46b6-9f6b-6aaf42642acb", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612381264600, "endTime": 26612381837800}, "additional": {"logType": "info", "children": [], "durationId": "d0787407-554f-4681-9af8-9f36b65a7db1", "parent": "0a556429-5c39-441f-b8e9-c980c248af08"}}, {"head": {"id": "245c9058-67da-4928-a2f1-28df43a8f54f", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26612381867400, "endTime": 26613646407000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "logId": "59ef8ca6-d7f2-456f-b1d2-2e8e8e62d455"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59ef8ca6-d7f2-456f-b1d2-2e8e8e62d455", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612381867400, "endTime": 26613646407000}, "additional": {"logType": "info", "children": [], "durationId": "245c9058-67da-4928-a2f1-28df43a8f54f", "parent": "0a556429-5c39-441f-b8e9-c980c248af08"}}, {"head": {"id": "4e849526-ae35-4f67-bfea-633d6afd3715", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26613646436600, "endTime": 26613655116900}, "additional": {"children": ["e8c7cc05-7139-4c07-9e64-5646cfb8b386", "0a70907d-7209-4e3b-a6ba-de7b367f7889", "854a3c8f-0922-4d80-a449-b2ec5deb1d62"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "logId": "214a8a00-d70b-48fc-8896-d4dbd9f14d99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "214a8a00-d70b-48fc-8896-d4dbd9f14d99", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613646436600, "endTime": 26613655116900}, "additional": {"logType": "info", "children": ["44fb9a2f-df80-414f-bfd8-f3152e76a6d7", "fe97b1f2-8fdb-44a9-80f3-70b97c3366bf", "a07bf0ce-941d-4ea7-954e-a186050533c6"], "durationId": "4e849526-ae35-4f67-bfea-633d6afd3715", "parent": "0a556429-5c39-441f-b8e9-c980c248af08"}}, {"head": {"id": "e8c7cc05-7139-4c07-9e64-5646cfb8b386", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26613646465100, "endTime": 26613646469100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e849526-ae35-4f67-bfea-633d6afd3715", "logId": "44fb9a2f-df80-414f-bfd8-f3152e76a6d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44fb9a2f-df80-414f-bfd8-f3152e76a6d7", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613646465100, "endTime": 26613646469100}, "additional": {"logType": "info", "children": [], "durationId": "e8c7cc05-7139-4c07-9e64-5646cfb8b386", "parent": "214a8a00-d70b-48fc-8896-d4dbd9f14d99"}}, {"head": {"id": "0a70907d-7209-4e3b-a6ba-de7b367f7889", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26613646472000, "endTime": 26613651894700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e849526-ae35-4f67-bfea-633d6afd3715", "logId": "fe97b1f2-8fdb-44a9-80f3-70b97c3366bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe97b1f2-8fdb-44a9-80f3-70b97c3366bf", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613646472000, "endTime": 26613651894700}, "additional": {"logType": "info", "children": [], "durationId": "0a70907d-7209-4e3b-a6ba-de7b367f7889", "parent": "214a8a00-d70b-48fc-8896-d4dbd9f14d99"}}, {"head": {"id": "854a3c8f-0922-4d80-a449-b2ec5deb1d62", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26613651900000, "endTime": 26613655104400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "4e849526-ae35-4f67-bfea-633d6afd3715", "logId": "a07bf0ce-941d-4ea7-954e-a186050533c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a07bf0ce-941d-4ea7-954e-a186050533c6", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613651900000, "endTime": 26613655104400}, "additional": {"logType": "info", "children": [], "durationId": "854a3c8f-0922-4d80-a449-b2ec5deb1d62", "parent": "214a8a00-d70b-48fc-8896-d4dbd9f14d99"}}, {"head": {"id": "f67eda0c-3cbb-4586-a9b1-21aa89d0d365", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26613655129100, "endTime": 26613730827300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "logId": "291cfdeb-d93d-45ab-84ae-c3c41f97e4c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "291cfdeb-d93d-45ab-84ae-c3c41f97e4c3", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613655129100, "endTime": 26613730827300}, "additional": {"logType": "info", "children": [], "durationId": "f67eda0c-3cbb-4586-a9b1-21aa89d0d365", "parent": "0a556429-5c39-441f-b8e9-c980c248af08"}}, {"head": {"id": "040ebc4c-6c15-4572-833b-bd5691ec5fc8", "name": "default@CompileArkTS work[18] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613738817900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a556429-5c39-441f-b8e9-c980c248af08", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 26612366868400, "endTime": 26613730995600}, "additional": {"logType": "info", "children": ["6421b143-724e-419c-968f-5ce860d5f010", "c12e64da-86dc-4747-8b1c-ba5fc5a4193e", "82f5aa80-7ac5-46b6-9f6b-6aaf42642acb", "59ef8ca6-d7f2-456f-b1d2-2e8e8e62d455", "214a8a00-d70b-48fc-8896-d4dbd9f14d99", "291cfdeb-d93d-45ab-84ae-c3c41f97e4c3"], "durationId": "f84cf2e1-570f-4673-ad8d-f76f0dca9f46", "parent": "587fa5f5-ad8e-4809-ad09-4246b435eaa1"}}, {"head": {"id": "7ec0433f-6107-43eb-ad25-f41346841b59", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26613959330100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "276e911a-c571-451d-9f31-8b0a7a141889", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614393846500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4f331d-2b6c-48d5-afa8-5b20c509cba1", "name": "CopyResources is end, endTime: 26614393987500", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614393994600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0071ff00-7059-475f-b9e0-376f09f949eb", "name": "default@CompileArkTS work[19] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614394073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3acebd68-f172-4425-9b9a-d801212f2708", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26614379682400, "endTime": 26614393669300}, "additional": {"logType": "info", "children": [], "durationId": "ef15231d-19ae-4a96-b0b9-47a4b7aa51a7", "parent": "587fa5f5-ad8e-4809-ad09-4246b435eaa1"}}, {"head": {"id": "587fa5f5-ad8e-4809-ad09-4246b435eaa1", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26612102921000, "endTime": 26614394183500, "totalTime": 1418393600}, "additional": {"logType": "info", "children": ["0a556429-5c39-441f-b8e9-c980c248af08", "3acebd68-f172-4425-9b9a-d801212f2708"], "durationId": "c33b52af-1705-40ee-a795-058f215ee8be"}}, {"head": {"id": "5aa228ea-1507-4606-8e74-6a3716e541a0", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614402377400, "endTime": 26614404725400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "01de91b0-b02d-4386-b2d2-93a57064661d", "logId": "a15afad4-e1cf-429f-8104-f0befa37b9ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01de91b0-b02d-4386-b2d2-93a57064661d", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614400589900}, "additional": {"logType": "detail", "children": [], "durationId": "5aa228ea-1507-4606-8e74-6a3716e541a0"}}, {"head": {"id": "9f86447b-d771-4cd9-99cf-e2efe78fc800", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614401199500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c655735d-9a80-4adf-b75a-4a8e91f12f6b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614401343700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b744a9f-03dc-40cd-8b27-780c74375a98", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614402391400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85086c72-a252-4d63-98fa-fef9abb7e616", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614402605500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b7f1206-220d-49ac-ae89-1431f72b4d5d", "name": "entry:default@GeneratePkgModuleJson is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614402933700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9286df6d-dade-4b68-acbf-decc8618f488", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614403018900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a6c3ff1-990d-499b-8009-6551d3c8cbb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614403092200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b833a43-cf88-46b5-9bd9-93eaf79d80cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614403145700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf5aa31a-0738-4d3d-a59f-c0701812c8bc", "name": "entry : default@GeneratePkgModuleJson cost memory 0.12127685546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614404511000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3117ff9-ab53-4fdf-ad8a-e5247d7f4e03", "name": "runTaskFromQueue task cost before running: 3 s 27 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614404654000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a15afad4-e1cf-429f-8104-f0befa37b9ac", "name": "Finished :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614402377400, "endTime": 26614404725400, "totalTime": 2246000}, "additional": {"logType": "info", "children": [], "durationId": "5aa228ea-1507-4606-8e74-6a3716e541a0"}}, {"head": {"id": "0304088e-8a42-4f25-bd9e-25e15e3bba27", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614413992700, "endTime": 26615137456400}, "additional": {"children": ["4e5f5ce0-e594-4afa-a4c6-6b8be8c4f7eb", "4d6097d3-1b67-45c1-829b-d2dc9f2b1796", "fc11d173-8006-42e8-b3e0-a518022e6086"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed."], "detailId": "f50e94b6-57cc-4d1e-8240-1d795bb6c154", "logId": "20287454-ec6e-4b60-a867-e2c965908f5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f50e94b6-57cc-4d1e-8240-1d795bb6c154", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614407297900}, "additional": {"logType": "detail", "children": [], "durationId": "0304088e-8a42-4f25-bd9e-25e15e3bba27"}}, {"head": {"id": "14db3d09-dd79-416e-a21f-f124899ea88b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614407639000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57343347-3c38-4de6-b019-06a7c78e41cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614407745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6eee4c6-96b7-49a6-a5b0-c0c9c0812ef6", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614414008400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8bff6c4-6c74-4a0b-aea6-6e7ece6fac32", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614418381300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "526721a3-489e-4246-bb18-5fe9cf69d9a3", "name": "Incremental task entry:default@PackageHap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614418522100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e1fee71-9dd2-49fc-93b6-c9806a5995c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614418615400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dc3dabd-249e-48ed-b074-2f7e0571f19a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614418670700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e5f5ce0-e594-4afa-a4c6-6b8be8c4f7eb", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614419288500, "endTime": 26614420399800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0304088e-8a42-4f25-bd9e-25e15e3bba27", "logId": "8efbb22a-d5ec-4d4d-8b66-881d76981515"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcda6c49-c7c8-47ee-84ad-1015b605a7bd", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614420246700}, "additional": {"logType": "debug", "children": [], "durationId": "0304088e-8a42-4f25-bd9e-25e15e3bba27"}}, {"head": {"id": "8efbb22a-d5ec-4d4d-8b66-881d76981515", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614419288500, "endTime": 26614420399800}, "additional": {"logType": "info", "children": [], "durationId": "4e5f5ce0-e594-4afa-a4c6-6b8be8c4f7eb", "parent": "20287454-ec6e-4b60-a867-e2c965908f5e"}}, {"head": {"id": "4d6097d3-1b67-45c1-829b-d2dc9f2b1796", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614420937500, "endTime": 26614422430100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0304088e-8a42-4f25-bd9e-25e15e3bba27", "logId": "50762c96-baf5-4ddd-96dd-b50d76cafc4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3747eda4-c016-4651-8db4-4b9842be4dbf", "name": "default@PackageHap work[20] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614421542000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc11d173-8006-42e8-b3e0-a518022e6086", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26614644065500, "endTime": 26615137018400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0304088e-8a42-4f25-bd9e-25e15e3bba27", "logId": "97ca0580-4b03-4922-bd0c-0482d7ae216d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50372813-827d-45d8-bb7c-6aa0476dc870", "name": "default@PackageHap work[20] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614422198200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "119702a2-ca1f-4621-a46e-f4380f7544d4", "name": "default@PackageHap work[20] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614422339200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50762c96-baf5-4ddd-96dd-b50d76cafc4e", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614420937500, "endTime": 26614422430100}, "additional": {"logType": "info", "children": [], "durationId": "4d6097d3-1b67-45c1-829b-d2dc9f2b1796", "parent": "20287454-ec6e-4b60-a867-e2c965908f5e"}}, {"head": {"id": "3f07ca14-e738-4c80-9e44-81c6959e053d", "name": "entry : default@PackageHap cost memory 0.761810302734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614426852700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f60171c-9b7b-48de-ae21-1b3f9c8bb334", "name": "default@PackageHap work[20] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614501504600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c0e7f4-7804-4d33-b614-a426e1a87b00", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614532881100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111b4557-8c6e-455e-ab78-1edd825d337c", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614533046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b70c7be-7abf-4079-a7de-2014ac88e72e", "name": "A work dispatched to worker[3] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614533107800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cd615ed-e0f8-4db7-afca-fcf09c6c29ef", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614533165600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117de180-84f8-42ec-b616-8311c4800159", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614533218900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d26fe445-b4b9-4e8f-92e0-2e64257e595e", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614533271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3783115-372d-4bb5-abdd-dda23df3c7b7", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615137118600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e8fbf2-55f8-444d-b9a3-49f384902c2b", "name": "default@PackageHap work[20] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615137278300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ca0580-4b03-4922-bd0c-0482d7ae216d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 26614644065500, "endTime": 26615137018400}, "additional": {"logType": "info", "children": [], "durationId": "fc11d173-8006-42e8-b3e0-a518022e6086", "parent": "20287454-ec6e-4b60-a867-e2c965908f5e"}}, {"head": {"id": "20287454-ec6e-4b60-a867-e2c965908f5e", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26614413992700, "endTime": 26615137456400, "totalTime": 505943500}, "additional": {"logType": "info", "children": ["8efbb22a-d5ec-4d4d-8b66-881d76981515", "50762c96-baf5-4ddd-96dd-b50d76cafc4e", "97ca0580-4b03-4922-bd0c-0482d7ae216d"], "durationId": "0304088e-8a42-4f25-bd9e-25e15e3bba27"}}, {"head": {"id": "a127ea78-3402-4950-87c9-315c1bb98353", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615143819700, "endTime": 26615145300500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "6f5e9c61-3049-45e9-81fc-a825a9e0e5fa", "logId": "b1df6967-4df6-4aab-ab47-699074c171f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f5e9c61-3049-45e9-81fc-a825a9e0e5fa", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615141580000}, "additional": {"logType": "detail", "children": [], "durationId": "a127ea78-3402-4950-87c9-315c1bb98353"}}, {"head": {"id": "067e560c-e30e-4f55-8e80-b646d272ba22", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615141929300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b74266c-bf88-49be-912a-68c079753cdc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615142026900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c6812cf-92d0-421d-ad44-4369d300bdd4", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615143829700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff540bb-c345-44e4-aeda-1618a97ae0ed", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615144076300}, "additional": {"logType": "warn", "children": [], "durationId": "a127ea78-3402-4950-87c9-315c1bb98353"}}, {"head": {"id": "04fe6a97-3b5d-4f3c-8f06-1b1302301ecd", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615144565400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "583947ce-5151-454e-b654-162792375053", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615144659800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12dc548b-1417-4e84-98ad-d79550c23051", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615144742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa1a1d92-e536-4a71-aca0-adde05db0f57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615144835000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f253bd48-f350-4635-ac99-c9eacc744803", "name": "entry : default@SignHap cost memory 0.1189727783203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615145124200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7380cfa-0763-43a5-a794-2b110445f1d9", "name": "runTaskFromQueue task cost before running: 3 s 767 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615145227200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1df6967-4df6-4aab-ab47-699074c171f7", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615143819700, "endTime": 26615145300500, "totalTime": 1385900}, "additional": {"logType": "info", "children": [], "durationId": "a127ea78-3402-4950-87c9-315c1bb98353"}}, {"head": {"id": "2ffd4674-aa61-4288-9a2f-b765b6f92ddb", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615148412100, "endTime": 26615153732800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7902e737-5521-45da-abfb-237039947068", "logId": "060ff3b1-452f-4689-ad39-5570e5841aed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7902e737-5521-45da-abfb-237039947068", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615147179000}, "additional": {"logType": "detail", "children": [], "durationId": "2ffd4674-aa61-4288-9a2f-b765b6f92ddb"}}, {"head": {"id": "5286c6cc-28c4-438a-a83f-835c66e67ac1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615147601300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b091705d-09b5-4849-bf66-3611ccbf6f14", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615147712000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada413a9-b8c7-4701-b9e6-16f963ea372c", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615148422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a65d3d5-d367-473b-ae50-7693881cd6c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615153371600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bbf9be6-4c3a-4997-8924-a18acb1e0c56", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615153487300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1aed341-17ec-4c10-9f14-6e95fb72313c", "name": "entry : default@CollectDebugSymbol cost memory 0.24114990234375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615153583400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f60812e-6bb6-493b-90d6-80d96da6104c", "name": "runTaskFromQueue task cost before running: 3 s 776 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615153672900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060ff3b1-452f-4689-ad39-5570e5841aed", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615148412100, "endTime": 26615153732800, "totalTime": 5237200}, "additional": {"logType": "info", "children": [], "durationId": "2ffd4674-aa61-4288-9a2f-b765b6f92ddb"}}, {"head": {"id": "026c3d9a-d4e1-47a0-ac95-671a6dee7f83", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615155359000, "endTime": 26615155783800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "aa68e59a-654e-449a-9a8d-19d63a5414ef", "logId": "d8bf4571-4986-4e0f-87f2-85084f814c52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa68e59a-654e-449a-9a8d-19d63a5414ef", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615155297400}, "additional": {"logType": "detail", "children": [], "durationId": "026c3d9a-d4e1-47a0-ac95-671a6dee7f83"}}, {"head": {"id": "516ad2e1-70be-4c95-a428-78677dd93fee", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615155367800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "700f787e-6145-473b-b9cc-75881105fc1c", "name": "entry : assembleHap cost memory 0.0117340087890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615155508500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10a45f0b-207d-45aa-ad32-f939a91229e5", "name": "runTaskFromQueue task cost before running: 3 s 778 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615155599900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8bf4571-4986-4e0f-87f2-85084f814c52", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615155359000, "endTime": 26615155783800, "totalTime": 217600}, "additional": {"logType": "info", "children": [], "durationId": "026c3d9a-d4e1-47a0-ac95-671a6dee7f83"}}, {"head": {"id": "c99dc936-9717-4413-a838-533b6c82751b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615163628000, "endTime": 26615163653000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "44ebe6af-c6ba-41da-9c5d-90d50fe8d5ad", "logId": "1bc36dc5-c0f0-4223-bc7d-c5240d395392"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1bc36dc5-c0f0-4223-bc7d-c5240d395392", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615163628000, "endTime": 26615163653000}, "additional": {"logType": "info", "children": [], "durationId": "c99dc936-9717-4413-a838-533b6c82751b"}}, {"head": {"id": "d9d7bd1c-922e-4633-b8fb-a16b8529ded8", "name": "BUILD SUCCESSFUL in 3 s 786 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615163697800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "f38e0334-b9d2-4f61-9951-a9605dde8c68", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26611378482200, "endTime": 26615163963400}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 15, "minute": 53}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "e550ce92-2f19-468a-80c7-22d3a30049af", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615163993700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "928ba4b8-9ce1-4928-8241-091b68a3697c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615164062700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f3b434-ba19-4f7a-b5a8-34c530244ef6", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615164121200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c5113be-8c07-46f6-854e-cb2ae9803eb5", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615164175100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b726f7-b390-4f0f-9916-d58f0a83a28a", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615164237900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87c8126a-1a78-4d00-a7c9-e6a84e3eec1b", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615164543300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7c43f66-4974-4659-9583-506b58aee92b", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615165178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4f80ff9-551d-475c-b49e-aa629c30823e", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615165722900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcca9b38-b53e-45a6-8432-2621ebc555c4", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615166002100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6221e3b2-2bf0-424c-9e90-4f2864054cf2", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615166110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13f5174b-1c97-4abe-9b1f-1423a725c9a7", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615166545000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0530c128-e2d8-4438-8765-15db6f12eea3", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615167331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f5c9aef-5f36-4b50-948b-f32c82aa1c65", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615167575700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c42bc185-e947-4a58-9ff9-081d0e3a44bd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615167653400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d3cd3a1-5932-42e7-8719-d96d906a2010", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615167710000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "820d81de-02ee-4e3f-8247-4c6ed252d5eb", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615167761800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5be9f7c-613e-44d7-8ce5-69ad087fec0d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615168127200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "035bae2b-690d-44dc-a6dc-3018c200c214", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615168499100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af6034d-fa08-4052-b092-4008e9f7d792", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615168713900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1acc4e27-175a-4137-8b68-e6eae19bb9a9", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615168898100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90b886cb-1de3-4de9-a3f6-fbe22d3f0cef", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615169140300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a0966bd-08fb-49ff-b7c3-9a78ec030827", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615170070400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d83cbf04-28a0-4450-9a61-483bdb9aa0f0", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\AppScope\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615170159500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed3775b-1550-4fb6-a74a-3a4999b9fb91", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615171082700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3262030-68dd-4a91-ac6f-6464dbffda06", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\router_map\\default\\router_map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615171284400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f800471-1640-408e-83be-fc8f8a0b3a45", "name": "Update task entry:default@CompileResource input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615171463200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e25f2c9-6777-4732-91c6-e1aefd435152", "name": "Update task entry:default@CompileResource output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615171684700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f924a5-dd2d-4327-bb39-38eae390a781", "name": "Update task entry:default@CompileResource output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615178766100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce0c2a8-4e16-4e27-a41b-d933be746e43", "name": "Update task entry:default@CompileResource output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615179020100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc03b36d-9df4-413a-946b-37f912dca203", "name": "Incremental task entry:default@CompileResource post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615179455500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47aa6d94-e9c4-4700-8b5e-22c9b79024ec", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615179538100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16e1bd69-9c80-4ec2-b05d-5096be4f702b", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615181209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29ac826b-0c18-473c-a2f5-a2cd4b9f48ef", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615181595700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d286ac2-3b7b-402a-974b-7253d11f3b22", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615182851000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac39c6f0-c36a-4cd8-ade4-c8ba46f231da", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615183158600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc673d2-a908-4238-aadf-24c4c708301f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615183705900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534fd0d9-b1e0-46d6-806f-ed9fee5c8236", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615184449900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ac25ab8-3233-41c6-92b6-342251a71b3e", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615187258100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6cbc0dc-9858-48c3-af47-e964ff089ed8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615187500900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2ec6936-e340-492b-8422-0f994ed9d282", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615187701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "682c58db-acee-4f39-8174-f3b203a9fa50", "name": "Incremental task entry:default@CompileArkTS post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615188202400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80e0dd29-c523-49df-9df6-36dc16f1cd68", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615189130800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beada94a-70c0-4df9-97ce-25ade31885be", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615189896900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7893b4dd-2fac-451c-9232-6c2ce41d7d0e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615190707700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "442aefec-0f05-4082-b47f-161e3ba3a292", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615190916100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be6735fd-b3e6-4d24-bc32-abfb7cf2d6a7", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615191092600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aae04e3-fea3-4872-afef-cf7cfa22433b", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615191553900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c41ad29-f6f2-4319-9e57-4b5a2bfcc336", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615191772300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84ca6728-7d94-4ebc-98bb-6ec4dc78b2d5", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615191874500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbdb1437-9a68-4436-9352-b3a9f566b36e", "name": "Update task entry:default@GeneratePkgModuleJson input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615191940500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c2d23dc-aa77-407d-92e9-8f3d26eca66e", "name": "Update task entry:default@GeneratePkgModuleJson output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\package\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615192000600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55eaab3c-e1be-4097-8232-72ba8d7f2172", "name": "Incremental task entry:default@GeneratePkgModuleJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615192232300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "191f7b36-847f-4ac0-950a-6dadf962a699", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615193099800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474feb80-b983-4b89-b5f2-8e1f83edbefb", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615193377900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "005f3438-26eb-4371-a4b7-5e3a296c33b4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615193457200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1b87561-9b7c-4d4d-82d5-cf977aa04ca7", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615200581300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d063c3b-d0c1-48ff-8fdb-608e40d09a45", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615200845800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b1f9c76-a9c7-49be-aeb5-25abc6859a42", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615201040900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68942b9d-7bf7-4ff9-805a-051aebdb5813", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615201572400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ed998ed-d594-4fcb-97d8-2e68d14c316f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615201782300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4b57150-0d33-4f26-96f5-bf1adfcbeb52", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615202402400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86ef7102-2d8e-4cd6-ad66-4d94f4323f5e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615202615700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb552603-4f7b-4b6b-9f99-e1d704439a81", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615202844000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a956a819-247b-4845-b63a-ea1edbe3c0d2", "name": "Incremental task entry:default@PackageHap post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615203107500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "158c8ab3-128e-4f49-9db8-69ef4806bd29", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615203268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab60c40-2593-44a1-b6c7-faa84019a6e8", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615203366600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "857fa0ed-9c83-4a45-bbdb-e1e6a83fd2e8", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615203555400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b9fbc9c-0751-4cf6-9962-1584e425b4eb", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615205757100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029b8d2d-082b-4f28-8d56-8e810701b9ca", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615206044600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "925851fb-85e7-42a0-845f-19f5dab138d3", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615206519700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8708dd7d-f08e-4e94-a684-3396cb3bfc0b", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 26615206822300}, "additional": {"logType": "debug", "children": []}}], "workLog": []}