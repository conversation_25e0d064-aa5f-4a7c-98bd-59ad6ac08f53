{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 649700, "PreCheckSyscap": 1990400, "GeneratePkgContextInfo": 9184400, "ProcessIntegratedHsp": 8644300, "BuildNativeWithCmake": 2628000, "SyscapTransform": 14117600, "BuildNativeWithNinja": 4129000, "ProcessLibs": 5004800, "BuildJS": 2467100, "CompileArkTS": 3398482700, "PackageHap": 1203013900, "SignHap": 2503900, "CollectDebugSymbol": 7638600, "assembleHap": 305400}}, "TOTAL_TIME": 5671163400, "BUILD_ID": "202506241713203860"}}