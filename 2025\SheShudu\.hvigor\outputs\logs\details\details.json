{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 1450400, "PreCheckSyscap": 546000, "GeneratePkgContextInfo": 1013400, "ProcessIntegratedHsp": 2800800, "BuildNativeWithCmake": 570000, "SyscapTransform": 2569800, "BuildNativeWithNinja": 1767000, "ProcessLibs": 3882800, "BuildJS": 3421000, "CompileArkTS": 2142000400, "PackageHap": 540704200, "SignHap": 3816900, "CollectDebugSymbol": 6790500, "assembleHap": 499200}}, "TOTAL_TIME": 3500116900, "BUILD_ID": "202506241615468710"}}