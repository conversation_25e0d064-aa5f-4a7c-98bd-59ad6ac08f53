{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 426100, "PreCheckSyscap": 355200, "GeneratePkgContextInfo": 653800, "ProcessIntegratedHsp": 2477600, "BuildNativeWithCmake": 372600, "SyscapTransform": 2333700, "BuildNativeWithNinja": 1362600, "ProcessLibs": 3355900, "BuildJS": 2527800, "SignHap": 7920400, "CollectDebugSymbol": 7675500, "assembleHap": 369000}}, "TOTAL_TIME": 825732100, "BUILD_ID": "202506241708364480"}}