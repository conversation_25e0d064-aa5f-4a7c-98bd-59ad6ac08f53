{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 603600, "PreCheckSyscap": 1539400, "GeneratePkgContextInfo": 740500, "ProcessIntegratedHsp": 2894200, "BuildNativeWithCmake": 510300, "SyscapTransform": 4029800, "BuildNativeWithNinja": 1649700, "ProcessLibs": 3688300, "BuildJS": 2644400, "CompileArkTS": 2322073200, "PackageHap": 561726800, "SignHap": 1797700, "CollectDebugSymbol": 4354000, "assembleHap": 298000}}, "TOTAL_TIME": 3510342800, "BUILD_ID": "202506241734436150"}}