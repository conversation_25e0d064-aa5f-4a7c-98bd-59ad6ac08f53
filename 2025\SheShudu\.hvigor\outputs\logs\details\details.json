{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 2033700, "PreCheckSyscap": 736700, "GeneratePkgContextInfo": 647300, "ProcessIntegratedHsp": 5377500, "BuildNativeWithCmake": 615200, "SyscapTransform": 3679600, "BuildNativeWithNinja": 2152300, "ProcessLibs": 4142000, "BuildJS": 3423500, "SignHap": 2114200, "CollectDebugSymbol": 7232100, "assembleHap": 1512900}}, "TOTAL_TIME": 917268800, "BUILD_ID": "202506241657400560"}}