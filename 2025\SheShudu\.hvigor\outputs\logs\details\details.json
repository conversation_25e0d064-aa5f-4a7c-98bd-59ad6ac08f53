{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 582300, "PreCheckSyscap": 418300, "GeneratePkgContextInfo": 776400, "ProcessIntegratedHsp": 9064100, "BuildNativeWithCmake": 432900, "SyscapTransform": 3730200, "BuildNativeWithNinja": 4223100, "ProcessLibs": 5246400, "BuildJS": 4099300, "CompileArkTS": 1898143600, "PackageHap": 542117600, "SignHap": 2484300, "CollectDebugSymbol": 9698900, "assembleHap": 558300}}, "TOTAL_TIME": 3534998100, "BUILD_ID": "202506241728217690"}}