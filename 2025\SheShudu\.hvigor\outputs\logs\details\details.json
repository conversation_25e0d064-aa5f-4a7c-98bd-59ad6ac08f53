{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": false}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"ConfigureCmake": 473200, "PreCheckSyscap": 458200, "GeneratePkgContextInfo": 687400, "ProcessIntegratedHsp": 5254800, "BuildNativeWithCmake": 677300, "SyscapTransform": 3636900, "BuildNativeWithNinja": 1607100, "ProcessLibs": 4180700, "BuildJS": 4553800, "CompileArkTS": 2056186100, "PackageHap": 543789600, "SignHap": 1750800, "CollectDebugSymbol": 4458800, "assembleHap": 324100}}, "TOTAL_TIME": 3211444800, "BUILD_ID": "202506241803155520"}}