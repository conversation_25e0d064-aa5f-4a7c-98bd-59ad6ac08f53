{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "5637fd91-e934-45d4-af88-4053f4284473", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938256517400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "528d569e-8f7e-4bfd-a4e5-1412dceb05fd", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938311696000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ec3ca0f-ef40-4b16-9e87-a8626e39bc8a", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 30938312577800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac44c1bf-e3b3-42b4-a270-527dd29c2680", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036195550100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036201698100, "endTime": 31036501997700}, "additional": {"children": ["ac8772c1-be17-4e4e-8d00-dae1b0606dc3", "13355e56-0106-484f-9a31-2e46423ba5fd", "d6069490-9f87-4cec-8d59-ca0861ee8403", "4cf88486-f71a-4980-8aec-a86673234f67", "bdfec0d9-17ad-4cbe-b49b-a5b10473b048", "ff54a444-f0a2-480c-b543-19c47de22f1b", "c3d73ef6-abae-4990-9a83-72e70836a89b"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "1564828f-305d-4b00-aaf7-180fce4dc55e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac8772c1-be17-4e4e-8d00-dae1b0606dc3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036201701100, "endTime": 31036216323700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "ec9ca0b5-0959-4d3b-a6ce-1fbae51e2ab6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13355e56-0106-484f-9a31-2e46423ba5fd", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036216344700, "endTime": 31036500975000}, "additional": {"children": ["c1344830-c435-4f47-a033-23e58208dd45", "b94b7f5c-6117-4a3b-af56-e72673f55b58", "5a6cde9e-d0c9-4fe5-a107-d44932bb4f00", "216f292f-9dce-446f-82c7-8c11c3f73be0", "0e511b09-b8cd-4ce1-9169-356a66a4b756", "567f33b6-96fa-4547-a8e6-4b87b1942bde", "eef9456c-d14e-4650-94b0-5968a783f7f6", "ca974ced-9513-4876-af12-12367d149d9c", "0f506862-0355-466b-af6d-98883a6ee6a4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6069490-9f87-4cec-8d59-ca0861ee8403", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036500993400, "endTime": 31036501989000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "c5f85f6b-46ca-498a-9ca4-d59be1be0719"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cf88486-f71a-4980-8aec-a86673234f67", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036501993200, "endTime": 31036501994000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "7465f5ea-967b-4ce8-b622-35c9e5f1bd1f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdfec0d9-17ad-4cbe-b49b-a5b10473b048", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036206746800, "endTime": 31036206796500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "2be7c6c8-d37a-4709-be63-7fc632a9052a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2be7c6c8-d37a-4709-be63-7fc632a9052a", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036206746800, "endTime": 31036206796500}, "additional": {"logType": "info", "children": [], "durationId": "bdfec0d9-17ad-4cbe-b49b-a5b10473b048", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "ff54a444-f0a2-480c-b543-19c47de22f1b", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036211989600, "endTime": 31036212008400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "b3876335-93d2-4dc0-b0d9-f9e5e94423cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3876335-93d2-4dc0-b0d9-f9e5e94423cf", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036211989600, "endTime": 31036212008400}, "additional": {"logType": "info", "children": [], "durationId": "ff54a444-f0a2-480c-b543-19c47de22f1b", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "7866c54d-295e-49e8-8c51-cb53c89bacaf", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036212048200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46c16125-0f44-4be3-8b3c-925a7392d710", "name": "Cache service initialization finished in 4 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036216179800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec9ca0b5-0959-4d3b-a6ce-1fbae51e2ab6", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036201701100, "endTime": 31036216323700}, "additional": {"logType": "info", "children": [], "durationId": "ac8772c1-be17-4e4e-8d00-dae1b0606dc3", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "c1344830-c435-4f47-a033-23e58208dd45", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036223071700, "endTime": 31036223084600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "a811d208-1be5-4a92-b29f-32ca092ea498"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b94b7f5c-6117-4a3b-af56-e72673f55b58", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036223107700, "endTime": 31036228862900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "ddb93fcf-0edf-4339-9d4b-eaed7349297d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a6cde9e-d0c9-4fe5-a107-d44932bb4f00", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036228878300, "endTime": 31036321325100}, "additional": {"children": ["67e150e3-ab19-40e2-be1c-eff6f794022f", "1ce29021-a93b-4eb3-bb28-9d11d903b6da", "45a1783b-ece2-4d6a-8a42-9a7acea7ca4a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "edc068e2-b9f8-4bcc-8930-54a01ab0e5e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "216f292f-9dce-446f-82c7-8c11c3f73be0", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036321341500, "endTime": 31036353520100}, "additional": {"children": ["b897c7cf-b398-42ff-897d-1cc941b215d5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "0169ced6-e10f-4ee9-b351-e411b3b6d279"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e511b09-b8cd-4ce1-9169-356a66a4b756", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036353528100, "endTime": 31036469591600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "94c1ba43-efcf-4c46-9b9f-d5ab95bfec6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "567f33b6-96fa-4547-a8e6-4b87b1942bde", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036470562400, "endTime": 31036491471600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "3546b287-5af8-4b4e-a6e4-7bae57f31fc3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eef9456c-d14e-4650-94b0-5968a783f7f6", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036491490700, "endTime": 31036500845800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "f704b25b-3b7f-4b71-8cb6-86b980fb47e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca974ced-9513-4876-af12-12367d149d9c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036500861800, "endTime": 31036500956600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "58d07a7a-64d0-488e-a18d-51db9ff413de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a811d208-1be5-4a92-b29f-32ca092ea498", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036223071700, "endTime": 31036223084600}, "additional": {"logType": "info", "children": [], "durationId": "c1344830-c435-4f47-a033-23e58208dd45", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "ddb93fcf-0edf-4339-9d4b-eaed7349297d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036223107700, "endTime": 31036228862900}, "additional": {"logType": "info", "children": [], "durationId": "b94b7f5c-6117-4a3b-af56-e72673f55b58", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "67e150e3-ab19-40e2-be1c-eff6f794022f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036229471600, "endTime": 31036229489200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a6cde9e-d0c9-4fe5-a107-d44932bb4f00", "logId": "b1d2ddfc-8a68-41d9-b4ee-09bb31c1572b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1d2ddfc-8a68-41d9-b4ee-09bb31c1572b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036229471600, "endTime": 31036229489200}, "additional": {"logType": "info", "children": [], "durationId": "67e150e3-ab19-40e2-be1c-eff6f794022f", "parent": "edc068e2-b9f8-4bcc-8930-54a01ab0e5e1"}}, {"head": {"id": "1ce29021-a93b-4eb3-bb28-9d11d903b6da", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036232006500, "endTime": 31036320312300}, "additional": {"children": ["76df739a-8204-4fbb-be9f-7bf8ae3c8d27", "c8bb196b-25f4-4c10-afa5-13de20913f8d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a6cde9e-d0c9-4fe5-a107-d44932bb4f00", "logId": "9c7a7f87-12a7-4439-a8bd-67be399a8c8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "76df739a-8204-4fbb-be9f-7bf8ae3c8d27", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036232030800, "endTime": 31036239827900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ce29021-a93b-4eb3-bb28-9d11d903b6da", "logId": "a8a90068-1c9c-4697-818b-7620909cf253"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8bb196b-25f4-4c10-afa5-13de20913f8d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036239846700, "endTime": 31036320267400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ce29021-a93b-4eb3-bb28-9d11d903b6da", "logId": "5ff793ee-8340-4671-8084-32d4d52bc733"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "230b4fb5-e481-4330-9985-b33d5ca3fbd0", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036232038600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a22a5b4-f0a8-4cc1-8770-970dcd45ef6d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036239679800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8a90068-1c9c-4697-818b-7620909cf253", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036232030800, "endTime": 31036239827900}, "additional": {"logType": "info", "children": [], "durationId": "76df739a-8204-4fbb-be9f-7bf8ae3c8d27", "parent": "9c7a7f87-12a7-4439-a8bd-67be399a8c8e"}}, {"head": {"id": "9a330776-e4fb-43b6-b840-528f35125056", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036239857600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "764b93f0-9cc0-4b9d-8448-e9ff726dd74c", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036247472800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5aee250-5a97-4cff-9156-3e71c36a8dda", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036247642800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "599c3061-b206-46bf-90ea-283ffda377bc", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036247953000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852b8bce-02c9-45c3-8f5a-dc36dd4136f6", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036248126200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcf08c8a-252c-40fc-b7cb-719fd2a9f369", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036250214500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9ea63c5-1454-4b1e-b1aa-047043617e01", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036255896400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e352da10-fd4e-4a0b-b42d-7c97d01f4693", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036264852700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b9e660-561f-4178-90d1-1b92290eeff6", "name": "Sdk init in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036294323700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0410573f-5b93-49c9-8235-1e504204d9b8", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036294465300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 6}, "markType": "other"}}, {"head": {"id": "1afd028d-b68a-44bf-a7c4-bc5025cc7b21", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036294480200}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 6}, "markType": "other"}}, {"head": {"id": "9a4b4d91-ab57-4a23-917b-785936e762d3", "name": "Project task initialization takes 24 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036319532900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0f6aaa-41b9-4829-9903-90508f020c1a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036319913400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c54fb45c-e924-4d9e-be10-2333dcd95079", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036320052000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dcdb178-691e-4441-87eb-daa0691aa501", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036320163800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ff793ee-8340-4671-8084-32d4d52bc733", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036239846700, "endTime": 31036320267400}, "additional": {"logType": "info", "children": [], "durationId": "c8bb196b-25f4-4c10-afa5-13de20913f8d", "parent": "9c7a7f87-12a7-4439-a8bd-67be399a8c8e"}}, {"head": {"id": "9c7a7f87-12a7-4439-a8bd-67be399a8c8e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036232006500, "endTime": 31036320312300}, "additional": {"logType": "info", "children": ["a8a90068-1c9c-4697-818b-7620909cf253", "5ff793ee-8340-4671-8084-32d4d52bc733"], "durationId": "1ce29021-a93b-4eb3-bb28-9d11d903b6da", "parent": "edc068e2-b9f8-4bcc-8930-54a01ab0e5e1"}}, {"head": {"id": "45a1783b-ece2-4d6a-8a42-9a7acea7ca4a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036321282300, "endTime": 31036321306500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5a6cde9e-d0c9-4fe5-a107-d44932bb4f00", "logId": "a7ce0518-950c-4f62-bfb7-d32779d7243d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a7ce0518-950c-4f62-bfb7-d32779d7243d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036321282300, "endTime": 31036321306500}, "additional": {"logType": "info", "children": [], "durationId": "45a1783b-ece2-4d6a-8a42-9a7acea7ca4a", "parent": "edc068e2-b9f8-4bcc-8930-54a01ab0e5e1"}}, {"head": {"id": "edc068e2-b9f8-4bcc-8930-54a01ab0e5e1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036228878300, "endTime": 31036321325100}, "additional": {"logType": "info", "children": ["b1d2ddfc-8a68-41d9-b4ee-09bb31c1572b", "9c7a7f87-12a7-4439-a8bd-67be399a8c8e", "a7ce0518-950c-4f62-bfb7-d32779d7243d"], "durationId": "5a6cde9e-d0c9-4fe5-a107-d44932bb4f00", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "b897c7cf-b398-42ff-897d-1cc941b215d5", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036323085700, "endTime": 31036353507500}, "additional": {"children": ["9fa81922-b105-45c5-a188-fc6cb49c5219", "ac0e1f49-c704-494d-b39b-e6c28abcd760", "6a6c2050-faa9-44e4-b8a0-b57e3a5af362"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "216f292f-9dce-446f-82c7-8c11c3f73be0", "logId": "f6e8b6f7-ff47-429e-aa65-a24cde132c18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fa81922-b105-45c5-a188-fc6cb49c5219", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036326329000, "endTime": 31036326345700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b897c7cf-b398-42ff-897d-1cc941b215d5", "logId": "f1466891-8535-4e0d-9635-6f32e5bb1bb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1466891-8535-4e0d-9635-6f32e5bb1bb3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036326329000, "endTime": 31036326345700}, "additional": {"logType": "info", "children": [], "durationId": "9fa81922-b105-45c5-a188-fc6cb49c5219", "parent": "f6e8b6f7-ff47-429e-aa65-a24cde132c18"}}, {"head": {"id": "ac0e1f49-c704-494d-b39b-e6c28abcd760", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036327963100, "endTime": 31036351369000}, "additional": {"children": ["f88de53d-d746-49bd-8589-480b6dc65ac3", "8d252aee-933e-4e0c-91fd-45b1b3140fcc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b897c7cf-b398-42ff-897d-1cc941b215d5", "logId": "66bd5c8c-3534-407a-a1a1-2f96548e6ba7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f88de53d-d746-49bd-8589-480b6dc65ac3", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036327964600, "endTime": 31036333823800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac0e1f49-c704-494d-b39b-e6c28abcd760", "logId": "e4a9fad0-60a8-44ed-9d8b-022002e177dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d252aee-933e-4e0c-91fd-45b1b3140fcc", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036333843800, "endTime": 31036351163600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac0e1f49-c704-494d-b39b-e6c28abcd760", "logId": "651d4d7f-e36d-4e0f-9a74-8c772c4cf791"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba4e6dce-79c3-4ff3-a3e1-c52651221dc2", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036327967800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88eac1b5-c2bc-48fa-9224-adedb3931c15", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036333676700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4a9fad0-60a8-44ed-9d8b-022002e177dc", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036327964600, "endTime": 31036333823800}, "additional": {"logType": "info", "children": [], "durationId": "f88de53d-d746-49bd-8589-480b6dc65ac3", "parent": "66bd5c8c-3534-407a-a1a1-2f96548e6ba7"}}, {"head": {"id": "21015610-d4c6-4a67-acf0-e7310013a61c", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036333857600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b279a390-cbd2-471f-b1c9-509eec34d3a4", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345112600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b06bc03-64ab-4b22-bd07-e5d7972f7305", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345243000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d49269b4-884d-49b8-9381-4fae486f0383", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345459700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b59273a6-ccd3-4554-9147-a6d31bbc4ccb", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345592700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f82e37-3b87-4437-98aa-8b8866697fad", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345724900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "504622dc-da11-41dd-96d0-83a929d2b681", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345796100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05f6242a-c4db-47f1-9416-9f0dcf4a719f", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036345861700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0111d0f-c328-4a5e-bbf6-3f32a2837662", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036350797300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5fbd080-cd8e-47a2-a774-2ac597867b34", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036350969700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11607f45-e216-4892-a9dc-8996e2647bdb", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036351027800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5150f33-fb6e-4300-ac31-de649d87e44c", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036351073800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "651d4d7f-e36d-4e0f-9a74-8c772c4cf791", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036333843800, "endTime": 31036351163600}, "additional": {"logType": "info", "children": [], "durationId": "8d252aee-933e-4e0c-91fd-45b1b3140fcc", "parent": "66bd5c8c-3534-407a-a1a1-2f96548e6ba7"}}, {"head": {"id": "66bd5c8c-3534-407a-a1a1-2f96548e6ba7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036327963100, "endTime": 31036351369000}, "additional": {"logType": "info", "children": ["e4a9fad0-60a8-44ed-9d8b-022002e177dc", "651d4d7f-e36d-4e0f-9a74-8c772c4cf791"], "durationId": "ac0e1f49-c704-494d-b39b-e6c28abcd760", "parent": "f6e8b6f7-ff47-429e-aa65-a24cde132c18"}}, {"head": {"id": "6a6c2050-faa9-44e4-b8a0-b57e3a5af362", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036353468200, "endTime": 31036353490100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b897c7cf-b398-42ff-897d-1cc941b215d5", "logId": "5dd07f49-5c4d-4ef0-86d9-892278acc855"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5dd07f49-5c4d-4ef0-86d9-892278acc855", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036353468200, "endTime": 31036353490100}, "additional": {"logType": "info", "children": [], "durationId": "6a6c2050-faa9-44e4-b8a0-b57e3a5af362", "parent": "f6e8b6f7-ff47-429e-aa65-a24cde132c18"}}, {"head": {"id": "f6e8b6f7-ff47-429e-aa65-a24cde132c18", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036323085700, "endTime": 31036353507500}, "additional": {"logType": "info", "children": ["f1466891-8535-4e0d-9635-6f32e5bb1bb3", "66bd5c8c-3534-407a-a1a1-2f96548e6ba7", "5dd07f49-5c4d-4ef0-86d9-892278acc855"], "durationId": "b897c7cf-b398-42ff-897d-1cc941b215d5", "parent": "0169ced6-e10f-4ee9-b351-e411b3b6d279"}}, {"head": {"id": "0169ced6-e10f-4ee9-b351-e411b3b6d279", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036321341500, "endTime": 31036353520100}, "additional": {"logType": "info", "children": ["f6e8b6f7-ff47-429e-aa65-a24cde132c18"], "durationId": "216f292f-9dce-446f-82c7-8c11c3f73be0", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "6f318021-9378-4fc0-ac9a-68e5f39537c1", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036385914100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ff25bc9-97c8-413b-9e92-bf6f8dd82b74", "name": "hvigorfile, resolve hvigorfile dependencies in 116 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036469469600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94c1ba43-efcf-4c46-9b9f-d5ab95bfec6a", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036353528100, "endTime": 31036469591600}, "additional": {"logType": "info", "children": [], "durationId": "0e511b09-b8cd-4ce1-9169-356a66a4b756", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "0f506862-0355-466b-af6d-98883a6ee6a4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036470341600, "endTime": 31036470547900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "13355e56-0106-484f-9a31-2e46423ba5fd", "logId": "ba2bf762-70a5-48a8-94da-5d70a844a733"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c111e3f9-1557-4fec-bb17-e3baf2be4bed", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036470370600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba2bf762-70a5-48a8-94da-5d70a844a733", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036470341600, "endTime": 31036470547900}, "additional": {"logType": "info", "children": [], "durationId": "0f506862-0355-466b-af6d-98883a6ee6a4", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "e54ec243-3174-4629-ae43-cff079c65a85", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036473234200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1930817f-bb42-4e5e-87f0-90c57f8a964a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036490869100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3546b287-5af8-4b4e-a6e4-7bae57f31fc3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036470562400, "endTime": 31036491471600}, "additional": {"logType": "info", "children": [], "durationId": "567f33b6-96fa-4547-a8e6-4b87b1942bde", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "384b0d55-d07b-42f5-9b39-c0ff8062fb19", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036496703000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cddcb34e-5a2b-480a-82ff-0b1270c4bab4", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036496811200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db0e61b-8b85-4fc9-a3a4-72498776068f", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036498484000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9136cf3-fcb8-4820-a918-38c706f5f89b", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036498562500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f704b25b-3b7f-4b71-8cb6-86b980fb47e3", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036491490700, "endTime": 31036500845800}, "additional": {"logType": "info", "children": [], "durationId": "eef9456c-d14e-4650-94b0-5968a783f7f6", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "a5902f8f-e1bd-4113-96f4-07d865fb7c52", "name": "Configuration phase cost:278 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036500880900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58d07a7a-64d0-488e-a18d-51db9ff413de", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036500861800, "endTime": 31036500956600}, "additional": {"logType": "info", "children": [], "durationId": "ca974ced-9513-4876-af12-12367d149d9c", "parent": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2"}}, {"head": {"id": "3b4a2b66-973d-407e-b27b-7c2aded6e2e2", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036216344700, "endTime": 31036500975000}, "additional": {"logType": "info", "children": ["a811d208-1be5-4a92-b29f-32ca092ea498", "ddb93fcf-0edf-4339-9d4b-eaed7349297d", "edc068e2-b9f8-4bcc-8930-54a01ab0e5e1", "0169ced6-e10f-4ee9-b351-e411b3b6d279", "94c1ba43-efcf-4c46-9b9f-d5ab95bfec6a", "3546b287-5af8-4b4e-a6e4-7bae57f31fc3", "f704b25b-3b7f-4b71-8cb6-86b980fb47e3", "58d07a7a-64d0-488e-a18d-51db9ff413de", "ba2bf762-70a5-48a8-94da-5d70a844a733"], "durationId": "13355e56-0106-484f-9a31-2e46423ba5fd", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "c3d73ef6-abae-4990-9a83-72e70836a89b", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036501969200, "endTime": 31036501981400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "411d2290-bf3d-43ed-b517-cd870cddcc3c", "logId": "00212707-5c4c-40ea-beb6-6991e425e58e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00212707-5c4c-40ea-beb6-6991e425e58e", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036501969200, "endTime": 31036501981400}, "additional": {"logType": "info", "children": [], "durationId": "c3d73ef6-abae-4990-9a83-72e70836a89b", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "c5f85f6b-46ca-498a-9ca4-d59be1be0719", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036500993400, "endTime": 31036501989000}, "additional": {"logType": "info", "children": [], "durationId": "d6069490-9f87-4cec-8d59-ca0861ee8403", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "7465f5ea-967b-4ce8-b622-35c9e5f1bd1f", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036501993200, "endTime": 31036501994000}, "additional": {"logType": "info", "children": [], "durationId": "4cf88486-f71a-4980-8aec-a86673234f67", "parent": "1564828f-305d-4b00-aaf7-180fce4dc55e"}}, {"head": {"id": "1564828f-305d-4b00-aaf7-180fce4dc55e", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036201698100, "endTime": 31036501997700}, "additional": {"logType": "info", "children": ["ec9ca0b5-0959-4d3b-a6ce-1fbae51e2ab6", "3b4a2b66-973d-407e-b27b-7c2aded6e2e2", "c5f85f6b-46ca-498a-9ca4-d59be1be0719", "7465f5ea-967b-4ce8-b622-35c9e5f1bd1f", "2be7c6c8-d37a-4709-be63-7fc632a9052a", "b3876335-93d2-4dc0-b0d9-f9e5e94423cf", "00212707-5c4c-40ea-beb6-6991e425e58e"], "durationId": "411d2290-bf3d-43ed-b517-cd870cddcc3c"}}, {"head": {"id": "962b2cea-80ec-45ba-9fb8-e855fc8a159d", "name": "Configuration task cost before running: 304 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036502215600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98abe9aa-ff21-45c3-8fa7-1d97887c416a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036507164100, "endTime": 31036512727100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "98acd606-c5b7-4779-ab37-bcae620e6034", "logId": "c8e8e228-4c60-43a4-90ca-032f47b394be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98acd606-c5b7-4779-ab37-bcae620e6034", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036503756300}, "additional": {"logType": "detail", "children": [], "durationId": "98abe9aa-ff21-45c3-8fa7-1d97887c416a"}}, {"head": {"id": "090f6fb1-9897-4f1d-9c5a-37b614d00bb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036504200300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0466a665-a0c8-437c-a981-4d111b669aaa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036504818800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca26f767-bc6f-4782-b539-f2bbbfd82a88", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036507175200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f905539-7590-47eb-986f-98572be840b3", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036512497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393fa34f-cb5e-4c59-82d9-25db45ccbcc0", "name": "entry : default@PreBuild cost memory 0.31439208984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********5400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e8e228-4c60-43a4-90ca-032f47b394be", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036507164100, "endTime": 31036512727100}, "additional": {"logType": "info", "children": [], "durationId": "98abe9aa-ff21-45c3-8fa7-1d97887c416a"}}, {"head": {"id": "f89eff5d-89ae-4dc2-a7e3-c9d7c6a599a2", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036516550700, "endTime": 31036518226500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "da282e53-67c1-4260-9a94-dc3af09f5976", "logId": "78c0cdb4-963e-4e52-8555-69d9c2faf148"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da282e53-67c1-4260-9a94-dc3af09f5976", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036515503000}, "additional": {"logType": "detail", "children": [], "durationId": "f89eff5d-89ae-4dc2-a7e3-c9d7c6a599a2"}}, {"head": {"id": "c1df4ce3-8499-49a9-a4c7-ff3616519d96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036515819900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0038eaaf-f199-4410-ba7b-4b8d830c2ea5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036515904000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5e7cba5-99e1-4acc-836e-8f7dea3c8b22", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036516558800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf1c35df-e897-4e24-a945-715f639e72c3", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036517174500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c36e1d-9477-4e13-96a4-5124fbf4f657", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036518067400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "752502a9-3954-45d3-8283-5ba26e9bed6b", "name": "entry : default@GenerateMetadata cost memory 0.09481048583984375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036518160500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c0cdb4-963e-4e52-8555-69d9c2faf148", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036516550700, "endTime": 31036518226500}, "additional": {"logType": "info", "children": [], "durationId": "f89eff5d-89ae-4dc2-a7e3-c9d7c6a599a2"}}, {"head": {"id": "8cf4a62f-4c19-4f0b-9eab-43490499a9e5", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520429300, "endTime": 31036520994400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f0f5a4b3-0750-4625-ab02-06ba0aba99c4", "logId": "ffea0e03-4b7e-40a8-a81d-84c6d39e3e96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0f5a4b3-0750-4625-ab02-06ba0aba99c4", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036519802500}, "additional": {"logType": "detail", "children": [], "durationId": "8cf4a62f-4c19-4f0b-9eab-43490499a9e5"}}, {"head": {"id": "b66f99c8-b258-4b34-9f4f-3185c6876ef9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520189700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f9eac0-1d9e-4d58-acab-506f95706089", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520288000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "898faa88-2d74-425f-985f-dbd0a5c1e63a", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520456700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "583e311b-99af-49ad-983d-eae29e3cae7f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520545400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58b0d223-5b32-4378-bce4-de25e8dba97c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520594600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0541fe34-93ef-4e1d-8962-b5d50a1d4bf3", "name": "entry : default@ConfigureCmake cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520830000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fca013ab-1de5-4263-bdb8-27569f76ce2e", "name": "runTaskFromQueue task cost before running: 323 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520924400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffea0e03-4b7e-40a8-a81d-84c6d39e3e96", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036520429300, "endTime": 31036520994400, "totalTime": 473600}, "additional": {"logType": "info", "children": [], "durationId": "8cf4a62f-4c19-4f0b-9eab-43490499a9e5"}}, {"head": {"id": "52b4faee-2c3f-4647-9d8d-8f9faebd79fa", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036524351100, "endTime": 31036525827100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3284faf7-84a9-42c8-873c-50f3e7af997b", "logId": "4144df56-6fe0-4136-9d39-90ebdab3f2b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3284faf7-84a9-42c8-873c-50f3e7af997b", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036523348600}, "additional": {"logType": "detail", "children": [], "durationId": "52b4faee-2c3f-4647-9d8d-8f9faebd79fa"}}, {"head": {"id": "1c9cfbdd-6968-4573-bc54-62675984458a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036523672600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32d56501-23ae-413d-aac5-a30a1580eb6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036523766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e073082-bfd3-4699-ba17-38bec540d2ed", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036524358900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "339dc117-6748-4396-937a-38a97125f559", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036525672500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2adb0c71-8151-4468-9229-6580586307f8", "name": "entry : default@MergeProfile cost memory 0.10630035400390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036525765500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4144df56-6fe0-4136-9d39-90ebdab3f2b4", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036524351100, "endTime": 31036525827100}, "additional": {"logType": "info", "children": [], "durationId": "52b4faee-2c3f-4647-9d8d-8f9faebd79fa"}}, {"head": {"id": "5aa7407d-52b6-4653-8379-53f57ab0a606", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036528157800, "endTime": 31036529928500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7c18472c-12fd-4c04-aaee-6040335a2592", "logId": "9db840ec-cebd-4a96-a924-52564e1ce3ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c18472c-12fd-4c04-aaee-6040335a2592", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036527176800}, "additional": {"logType": "detail", "children": [], "durationId": "5aa7407d-52b6-4653-8379-53f57ab0a606"}}, {"head": {"id": "de00d872-cb3c-422d-8f49-e4a54e7c93d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036527486100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c44bfa29-ef8c-4df4-acd7-e92e2e5fd0e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036527568900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f542ad3-69e3-4604-92a3-579f9c07dfd3", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036528168000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0a4eb0-cbcf-4e54-a4b8-9284b6b90a82", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036528831600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d68dd3fe-15d0-4ab2-96e1-dbe9d3babacf", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036529763800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70b23e09-06e3-4834-bd35-682f71172f83", "name": "entry : default@CreateBuildProfile cost memory 0.10373687744140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036529862300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9db840ec-cebd-4a96-a924-52564e1ce3ae", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036528157800, "endTime": 31036529928500}, "additional": {"logType": "info", "children": [], "durationId": "5aa7407d-52b6-4653-8379-53f57ab0a606"}}, {"head": {"id": "15526b9a-cd5f-4b14-90e6-7fe3b68d8f3b", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036533934600, "endTime": 31036534823300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b7b90466-6dd9-4e9c-b28d-b5b0b68fa2ed", "logId": "60466ec0-cc09-4b3e-a758-b1b58076e0d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7b90466-6dd9-4e9c-b28d-b5b0b68fa2ed", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036531831800}, "additional": {"logType": "detail", "children": [], "durationId": "15526b9a-cd5f-4b14-90e6-7fe3b68d8f3b"}}, {"head": {"id": "0f76d660-99fd-4b51-81a1-8abd8eb8e94d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036532509200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106aea94-74ca-4136-918a-e180ef7db565", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036532652400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11ad6ad7-a070-444e-9ab2-0079ad339be7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036533953600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c34478-8597-44dc-be12-4688adce126b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036534254000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070bc2e7-441e-47dc-b2ee-d4344274a1c7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036534370400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94505320-9da0-4031-869c-d225a7985da2", "name": "entry : default@PreCheckSyscap cost memory 0.03629302978515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036534564800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "617e0b8b-21cd-4498-b057-ae8fc98b5af1", "name": "runTaskFromQueue task cost before running: 337 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036534751300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60466ec0-cc09-4b3e-a758-b1b58076e0d9", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036533934600, "endTime": 31036534823300, "totalTime": 880200}, "additional": {"logType": "info", "children": [], "durationId": "15526b9a-cd5f-4b14-90e6-7fe3b68d8f3b"}}, {"head": {"id": "9ad5ed15-d821-4b6b-9fd7-b3254a388730", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036543298600, "endTime": 31036544009100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b1f29d5e-8d4e-4ec5-b8cb-40d6fa1ab65d", "logId": "e9a6be25-55fe-4cf8-870e-5638f261e15b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1f29d5e-8d4e-4ec5-b8cb-40d6fa1ab65d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036537000200}, "additional": {"logType": "detail", "children": [], "durationId": "9ad5ed15-d821-4b6b-9fd7-b3254a388730"}}, {"head": {"id": "9d380b03-5faa-49ef-bfa8-232965cdc613", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036537377600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad823dcc-ad53-4aca-9acd-65cfdef44d23", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036537480600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61aa0e6d-0680-4307-996c-872c43a34545", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036543310000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b970fe-58ca-45c0-926a-685ac84dc35a", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036543533200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d740bd8-807b-477b-b2c9-78c9e8f8eaa3", "name": "entry : default@GeneratePkgContextInfo cost memory 0.038787841796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036543847100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e975fe02-ec9e-47d8-80ba-b32a58e95a24", "name": "runTaskFromQueue task cost before running: 346 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036543950200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a6be25-55fe-4cf8-870e-5638f261e15b", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036543298600, "endTime": 31036544009100, "totalTime": 633900}, "additional": {"logType": "info", "children": [], "durationId": "9ad5ed15-d821-4b6b-9fd7-b3254a388730"}}, {"head": {"id": "ae7d145f-706b-4dd0-a2a0-fda32cb73960", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036548296100, "endTime": 31036550958100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "d8599914-068e-442d-af08-4ae57062ca6f", "logId": "4a584c9b-1795-4193-8535-8c5259a99b45"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8599914-068e-442d-af08-4ae57062ca6f", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036545957300}, "additional": {"logType": "detail", "children": [], "durationId": "ae7d145f-706b-4dd0-a2a0-fda32cb73960"}}, {"head": {"id": "b7e8354b-3df8-48f1-8add-368fb72c2c08", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036546325800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df778606-9809-4f62-8119-84e141b2e050", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036546428800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d62a0ee3-f1d0-4e7f-973f-c96cca3c8167", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036548308000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d11a27e5-38d4-484b-b757-47611d143ef9", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036550172000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8660a9c-3e9b-4ec0-a666-97d263e64130", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036550335700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "caa447aa-b5e2-40b0-8f0c-d093f9a4b69c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036550433500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c79163c0-6f4a-4b49-94ea-792c61d7395b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036550486800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f0ce9f-b01e-4b63-9147-697adcc8f2de", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11804962158203125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036550565300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f5e9e8-189c-49bf-b629-724ec8600c35", "name": "runTaskFromQueue task cost before running: 353 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036550748700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a584c9b-1795-4193-8535-8c5259a99b45", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036548296100, "endTime": 31036550958100, "totalTime": 2427100}, "additional": {"logType": "info", "children": [], "durationId": "ae7d145f-706b-4dd0-a2a0-fda32cb73960"}}, {"head": {"id": "ef23b22b-2089-4f9c-bef0-44ac05400dac", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036556954000, "endTime": 31036558311000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e6ab3130-8b26-4577-9c46-62498be8e954", "logId": "b2643bfa-e550-47e3-abae-e10e5b2b9374"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6ab3130-8b26-4577-9c46-62498be8e954", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036553863400}, "additional": {"logType": "detail", "children": [], "durationId": "ef23b22b-2089-4f9c-bef0-44ac05400dac"}}, {"head": {"id": "6abbe06a-20e4-46ef-820a-f374f40ab03b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036554416900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9e69c03-cdb4-4b5d-aaae-687bc3acfd39", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036554696100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5bf27b5-d4c4-4181-b01c-d5cd721e14e8", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036556973400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb323d2b-54ce-4c3d-bdb3-df8112828781", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036557232800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecf24921-8507-49bb-8974-d4de0628c419", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036557821300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fedc3a26-ce93-4c31-8f65-3c23c9aa097b", "name": "entry : default@BuildNativeWithCmake cost memory 0.0371246337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036558025700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ca7f1c-0311-4d0c-8bef-b8702298aa2a", "name": "runTaskFromQueue task cost before running: 360 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036558220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2643bfa-e550-47e3-abae-e10e5b2b9374", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036556954000, "endTime": 31036558311000, "totalTime": 1154000}, "additional": {"logType": "info", "children": [], "durationId": "ef23b22b-2089-4f9c-bef0-44ac05400dac"}}, {"head": {"id": "382fd0e7-51b8-4bd1-a83a-c4ba9a7552b2", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036561157000, "endTime": 31036563968900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "02b1b277-9a5f-4353-bdca-c205df599da0", "logId": "9508a3af-33fb-4aa3-8ae5-fefb0c75b256"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02b1b277-9a5f-4353-bdca-c205df599da0", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036559968600}, "additional": {"logType": "detail", "children": [], "durationId": "382fd0e7-51b8-4bd1-a83a-c4ba9a7552b2"}}, {"head": {"id": "4aa5277c-01d8-4f78-94ea-9c2ba2c06174", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036560341000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d0b38c4-920b-424c-99b5-36762e8ae738", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036560437100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "420e2eed-23d5-4c03-9bcf-956a24ec4386", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036561165900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "620b1c5b-a9a8-4817-af64-a01dc0741b75", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036563800300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b267b6e8-0013-486b-9f37-b3679087d194", "name": "entry : default@MakePackInfo cost memory 0.139892578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036563904800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9508a3af-33fb-4aa3-8ae5-fefb0c75b256", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036561157000, "endTime": 31036563968900}, "additional": {"logType": "info", "children": [], "durationId": "382fd0e7-51b8-4bd1-a83a-c4ba9a7552b2"}}, {"head": {"id": "a90dc36b-14ac-445c-9dba-4c34f40fc819", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036568344900, "endTime": 31036571565500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "8baf0279-ac9c-48da-9ec9-443171d9f889", "logId": "12299b59-74d5-42d7-beb2-4fa527fe1f6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8baf0279-ac9c-48da-9ec9-443171d9f889", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036566536000}, "additional": {"logType": "detail", "children": [], "durationId": "a90dc36b-14ac-445c-9dba-4c34f40fc819"}}, {"head": {"id": "f69ef4a0-8f70-4daf-a150-f3fbbc2e0eb0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036566998300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de99683b-e67b-40e7-baa1-487471e8d6c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036567096900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dbf7546-6985-4da0-953e-21166b2f8892", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036568356700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15af47b0-f018-4cb1-89ca-b65dfb5b8aae", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036568498000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c03c8ccd-d641-4633-8ac7-08736923d5b1", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036569272800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55983a88-cabe-4cc4-8a71-9fd0a304ec08", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036570709000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "602c66f6-f5ab-4011-852e-8f4a94984d48", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036570812500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a43d452-b6ee-45d4-971f-525b3589cdd1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036571051800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44ac5d8a-786d-409e-ac72-bdd4c48cefab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036571262100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e5d88c9-92ae-4d33-8cf5-112fc7bc6e51", "name": "entry : default@SyscapTransform cost memory 0.1529541015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036571410100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b7ba7d7-3f4e-4fa8-9f3b-1f3e7319416c", "name": "runTaskFromQueue task cost before running: 374 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036571502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12299b59-74d5-42d7-beb2-4fa527fe1f6b", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036568344900, "endTime": 31036571565500, "totalTime": 3134100}, "additional": {"logType": "info", "children": [], "durationId": "a90dc36b-14ac-445c-9dba-4c34f40fc819"}}, {"head": {"id": "768acdd0-7b99-4a45-83b6-9a7069217c94", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036574994400, "endTime": 31036575889500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "bbef9de0-936c-4d4b-9ae3-d7011761edd0", "logId": "b54c31fd-41c2-4447-a84d-4791485c86cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bbef9de0-936c-4d4b-9ae3-d7011761edd0", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036573542500}, "additional": {"logType": "detail", "children": [], "durationId": "768acdd0-7b99-4a45-83b6-9a7069217c94"}}, {"head": {"id": "96e4e64b-a53e-48ab-8275-5f81a5db9d73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036573867000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48672e83-156b-4536-999b-dc7f8d9d93c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036573959900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbc93bf7-b353-4fcc-9629-f7599462e1ad", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036575002900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93008994-cf78-40f4-a6d5-632348ca0c3a", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036575742400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "607ada45-8c07-449d-9a9a-b65585ceb313", "name": "entry : default@ProcessProfile cost memory 0.05973052978515625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036575827500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b54c31fd-41c2-4447-a84d-4791485c86cf", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036574994400, "endTime": 31036575889500}, "additional": {"logType": "info", "children": [], "durationId": "768acdd0-7b99-4a45-83b6-9a7069217c94"}}, {"head": {"id": "c400350c-68d9-411a-8922-f15380a391f7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036579354500, "endTime": 31036584071900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "552922eb-bef7-4f3c-a35b-47ffccf63807", "logId": "556e5f13-b88b-42ac-ac25-ddd7138666cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "552922eb-bef7-4f3c-a35b-47ffccf63807", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036577234800}, "additional": {"logType": "detail", "children": [], "durationId": "c400350c-68d9-411a-8922-f15380a391f7"}}, {"head": {"id": "cfa8f7f1-2623-4f71-bdbe-0aaa33e80a33", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036577557000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fb0d605-c2ce-4214-a9b4-d10260eb9d7e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036577644600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d2bda7-1e23-43d1-a771-5ab7604001a2", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036579364300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a446450a-6cc7-4f10-ac68-0362bdd7dc61", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036583828800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7154b19-de02-49fc-a064-2b218c93ce21", "name": "entry : default@ProcessRouterMap cost memory 0.24990081787109375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036584001200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "556e5f13-b88b-42ac-ac25-ddd7138666cc", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036579354500, "endTime": 31036584071900}, "additional": {"logType": "info", "children": [], "durationId": "c400350c-68d9-411a-8922-f15380a391f7"}}, {"head": {"id": "07443cdb-f208-41f6-a566-9013c3267145", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036589412000, "endTime": 31036590986000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0000ad52-93b6-4caa-824a-7b4fb922e3f2", "logId": "d65da105-5653-4d32-bda1-ce5a29cf9ecf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0000ad52-93b6-4caa-824a-7b4fb922e3f2", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036586890300}, "additional": {"logType": "detail", "children": [], "durationId": "07443cdb-f208-41f6-a566-9013c3267145"}}, {"head": {"id": "52960de0-bdce-47ee-b5b0-ee1cf1f2f2ed", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036587233800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1377d62c-f3a9-42d3-a1c3-33a577c265cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036587337300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "397321fb-4bee-43bd-a7bd-c2f937606628", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036589494800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37f5e39b-260a-4f22-914c-0e376dfb4a00", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036589811200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5797c67-08c2-4a36-9cfc-2de026af6132", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036589922900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216f3a54-5e58-4b89-87c6-0cca2458a93f", "name": "entry : default@BuildNativeWithNinja cost memory 0.05664825439453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036590593300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d33173f-5cab-4f16-a09a-2047f80a144a", "name": "runTaskFromQueue task cost before running: 393 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036590896400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d65da105-5653-4d32-bda1-ce5a29cf9ecf", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036589412000, "endTime": 31036590986000, "totalTime": 1480000}, "additional": {"logType": "info", "children": [], "durationId": "07443cdb-f208-41f6-a566-9013c3267145"}}, {"head": {"id": "d663a39f-24b6-47b2-8a60-6f2c52623237", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036597928300, "endTime": 31036603616200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "cf986916-4c19-4798-b990-800975349b24", "logId": "9624737e-13eb-42a0-9080-4e82c0efa3c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf986916-4c19-4798-b990-800975349b24", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036593830100}, "additional": {"logType": "detail", "children": [], "durationId": "d663a39f-24b6-47b2-8a60-6f2c52623237"}}, {"head": {"id": "0b38d49c-8de3-4a6e-a242-27620eb8b2a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036594401400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b629307c-9621-4db1-8ac6-57e0930b8172", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036594540200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9533c583-fa86-486b-a443-53336adfea15", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036596060800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c46fd3e-dccb-4cca-89e6-a887a6815c54", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036600274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2007fe0b-12ae-48b8-a05d-5e11f06871fc", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036601967200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d41b9ed-b3d0-473a-95da-6c6611b7e7e6", "name": "entry : default@ProcessResource cost memory 0.16840362548828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036602086800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9624737e-13eb-42a0-9080-4e82c0efa3c6", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036597928300, "endTime": 31036603616200}, "additional": {"logType": "info", "children": [], "durationId": "d663a39f-24b6-47b2-8a60-6f2c52623237"}}, {"head": {"id": "5113a4f0-0982-432e-83f2-9fcf4d1efb97", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036612530500, "endTime": 31036627942800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "41dd03b6-625b-4455-8147-8913d574fc4f", "logId": "3458c008-37f4-4e0f-b623-33562981baa5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41dd03b6-625b-4455-8147-8913d574fc4f", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036609156600}, "additional": {"logType": "detail", "children": [], "durationId": "5113a4f0-0982-432e-83f2-9fcf4d1efb97"}}, {"head": {"id": "e4b7b4f4-38e6-46f0-8ddb-e16cc04c2953", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036609487100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "917c2524-aebc-4f7c-bac6-4eebb1668adb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036609574500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2334cffb-4bd4-4e64-bb4e-63151f949da9", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036612542500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d09a5d2a-f8dd-4b5c-8d92-857b9c7863e5", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036627712600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b7f3a52-1452-4917-b376-470bb9b0dd34", "name": "entry : default@GenerateLoaderJson cost memory 0.762054443359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036627854600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3458c008-37f4-4e0f-b623-33562981baa5", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036612530500, "endTime": 31036627942800}, "additional": {"logType": "info", "children": [], "durationId": "5113a4f0-0982-432e-83f2-9fcf4d1efb97"}}, {"head": {"id": "e5a2cf83-a29e-47c1-bd6b-76b1a081ca3a", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036636820100, "endTime": 31036641735200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "de889783-edaf-492d-a0be-c492eb9b994b", "logId": "ef8c6da6-5e21-409b-9fee-3ac3a8ac62b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de889783-edaf-492d-a0be-c492eb9b994b", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036635484900}, "additional": {"logType": "detail", "children": [], "durationId": "e5a2cf83-a29e-47c1-bd6b-76b1a081ca3a"}}, {"head": {"id": "8e2851fd-f00b-4ba5-920f-246a26603034", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036635919500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62d6d62d-c138-492d-a271-6fa021c46236", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036636044600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e096b29f-1a6d-4718-82e7-6058d7865994", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036636832800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e32c01c1-45d3-4477-bd9c-ec024a59b1e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036640098600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e2984c-df8b-4db1-a2c2-c0961207779f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036640322100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ea4705-624c-4e4b-b748-c63453841080", "name": "entry : default@ProcessLibs cost memory 0.1252288818359375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036641179000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf01ea28-9906-4952-af72-d67b0986c073", "name": "runTaskFromQueue task cost before running: 443 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036641436100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8c6da6-5e21-409b-9fee-3ac3a8ac62b6", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036636820100, "endTime": 31036641735200, "totalTime": 4459400}, "additional": {"logType": "info", "children": [], "durationId": "e5a2cf83-a29e-47c1-bd6b-76b1a081ca3a"}}, {"head": {"id": "7afc3144-c318-4096-b737-cecd5f1a0e08", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036649180500, "endTime": 31036673501200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "771d5786-349f-4fca-a860-9beb11517582", "logId": "178f2626-be20-4863-9eab-2609bdc92c3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "771d5786-349f-4fca-a860-9beb11517582", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036645217300}, "additional": {"logType": "detail", "children": [], "durationId": "7afc3144-c318-4096-b737-cecd5f1a0e08"}}, {"head": {"id": "5a33db8e-1043-423a-b13f-c7b3aa33d9a7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036645548700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a92c375-ff40-43ed-a00e-a6789afb3dd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036645859100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72800c97-fdca-4af8-9dc6-535610931120", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036646732900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0c1e735-a9a3-49d1-a92d-602678947831", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036649204200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32360973-cd44-4102-aa97-7677f6594395", "name": "Incremental task entry:default@CompileResource pre-execution cost: 21 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036671476700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d19c7eba-329b-4ef9-9d1b-097283fd0806", "name": "entry : default@CompileResource cost memory 1.592987060546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036672065300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "178f2626-be20-4863-9eab-2609bdc92c3f", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036649180500, "endTime": 31036673501200}, "additional": {"logType": "info", "children": [], "durationId": "7afc3144-c318-4096-b737-cecd5f1a0e08"}}, {"head": {"id": "89105160-13cf-4ced-bf7e-11322c6a0139", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036680928700, "endTime": 31036682519000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cfd0cdc7-e36a-46dc-8954-054352e2852c", "logId": "cd74d4e2-0774-4ecc-8310-5fb896b063b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfd0cdc7-e36a-46dc-8954-054352e2852c", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036677855600}, "additional": {"logType": "detail", "children": [], "durationId": "89105160-13cf-4ced-bf7e-11322c6a0139"}}, {"head": {"id": "7652d0e7-8d18-4534-95b0-da966bdd8b24", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036678279000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a13a4ed5-7073-436b-ac38-e3aa27b97c99", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036678384400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7529c37c-ac48-4aa0-9575-86a67ace7612", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036680939800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbc2c0a-863e-4206-a619-7f4b66ad9a71", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036681207100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ac88f5-05d1-4765-951b-e8cd4be82812", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036682178900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "632e6ad2-04f5-47f0-abbd-d503c1616529", "name": "entry : default@DoNativeStrip cost memory 0.07657623291015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036682343600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd74d4e2-0774-4ecc-8310-5fb896b063b1", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036680928700, "endTime": 31036682519000}, "additional": {"logType": "info", "children": [], "durationId": "89105160-13cf-4ced-bf7e-11322c6a0139"}}, {"head": {"id": "c5f5be51-089d-4526-b13f-992e964ab5a1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036699780100, "endTime": 31038350511000}, "additional": {"children": ["ab919396-4a9b-46c4-a386-0eb3ce03e591", "722a6460-b050-4a71-8fc0-41e0a5b5a3a2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "4a453b25-a393-402c-9542-d4d6e926954f", "logId": "6da2869c-5e13-42d1-9e51-40f376c6d0db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a453b25-a393-402c-9542-d4d6e926954f", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036684678500}, "additional": {"logType": "detail", "children": [], "durationId": "c5f5be51-089d-4526-b13f-992e964ab5a1"}}, {"head": {"id": "04f8174a-3699-493e-9f23-1bb5ac1663cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036685056900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f5d0929-a158-4f00-87b8-83e24be680a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036685226500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00b0b968-5baa-44cc-9e88-0e3aa57ff4cb", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036699793000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee1db4cb-9e2e-4882-b6ff-5407527270b8", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036739475700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38377dc1-d932-432d-9319-dedda02e06f6", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 32 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036740085200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4af1ad01-a9b4-4d80-a829-2dbb79d63320", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036763928300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61bc67b8-87a6-4e5f-af4c-7601c91ce60d", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036764986800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5b3f0b4-7f60-4e93-87d5-40fe676b0992", "name": "default@CompileArkTS work[57] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036767783900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31036875049800, "endTime": 31038340852500}, "additional": {"children": ["3588abf0-a989-4b6c-9734-bf707fc974a7", "f611b84f-5ff2-4fc2-8805-51f144463e91", "0be6c664-fb84-4933-b006-b56040a76bc6", "f8c88fc6-6634-412d-96ea-c744b2903d3f", "b52fca02-2927-4f0d-b4ec-ca20ad757e83", "6c88c1e6-0bee-407f-b9c1-4c761a070e7c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c5f5be51-089d-4526-b13f-992e964ab5a1", "logId": "42584880-1285-4dae-b5ce-7d15afad9fc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a87ce6e2-6615-42d0-80c1-dc607efec449", "name": "default@CompileArkTS work[57] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036769003600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db1c1bfd-fbe6-49d4-b511-66e68d62e3b6", "name": "default@CompileArkTS work[57] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036769427900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3bb5c80-ddb7-4f23-ab6d-cc89caa548b0", "name": "CopyResources startTime: 31036769576900", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036769581400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a1ce495-8b7f-4572-aa4e-0318e94d1317", "name": "default@CompileArkTS work[58] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036769696400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "722a6460-b050-4a71-8fc0-41e0a5b5a3a2", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31038231280300, "endTime": 31038266693100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c5f5be51-089d-4526-b13f-992e964ab5a1", "logId": "6a97386c-dab9-440f-825f-16ba4e378921"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3fd00d7-6dfe-4206-9b06-9db0831d7d5a", "name": "default@CompileArkTS work[58] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036771274600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "963868c9-74eb-49c3-a192-19b692ba3f41", "name": "default@CompileArkTS work[58] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036771464700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75f1e93a-c7ac-4917-815f-599cf6cb851f", "name": "entry : default@CompileArkTS cost memory 1.5854339599609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036771812100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425e3c95-8174-424e-a125-c16546305dd4", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036779540700, "endTime": 31036783989400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2b54e0d4-b653-4d08-9fe0-7473702e4ae9", "logId": "c1bb41ad-8230-4aaa-9d1d-9acb856eaf97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b54e0d4-b653-4d08-9fe0-7473702e4ae9", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036774881200}, "additional": {"logType": "detail", "children": [], "durationId": "425e3c95-8174-424e-a125-c16546305dd4"}}, {"head": {"id": "7c893d19-b1b2-44e0-a56e-3b3beeabeb91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036775557900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef6c95e-320d-4304-8b7b-00194fd2bd05", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036775673200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fda78f82-a33e-40b6-a178-3988b6909b43", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036779552300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b6a1b70-8344-4f0b-8861-66b9577da731", "name": "entry : default@BuildJS cost memory 0.12787628173828125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036783720500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df8af9b-218f-461b-b02e-1b3374320d33", "name": "runTaskFromQueue task cost before running: 586 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036783905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1bb41ad-8230-4aaa-9d1d-9acb856eaf97", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036779540700, "endTime": 31036783989400, "totalTime": 4320900}, "additional": {"logType": "info", "children": [], "durationId": "425e3c95-8174-424e-a125-c16546305dd4"}}, {"head": {"id": "f1abfd4d-cb4e-4c21-bac5-77e8b3f0052b", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036790035100, "endTime": 31036791837900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "55abea78-0590-490d-8326-68eaa3d76f31", "logId": "f44d62eb-5a91-4247-b9dc-1d6fe7598a92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "55abea78-0590-490d-8326-68eaa3d76f31", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036786021800}, "additional": {"logType": "detail", "children": [], "durationId": "f1abfd4d-cb4e-4c21-bac5-77e8b3f0052b"}}, {"head": {"id": "e0edb841-23af-4e34-80c2-c27e5bcd9dc5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036786490100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c9334d-54b8-4367-86cf-e8c968c70614", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036786596000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628224f8-6766-4ade-8d82-ec02a4fe73b2", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036790046800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd73093e-4aaf-4584-862d-0b93d028f4ff", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036790549900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3302755f-2296-4631-8765-20bb5edeb5b1", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036791666900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d094200-8e96-4855-9e74-b31aa3cbe4a4", "name": "entry : default@CacheNativeLibs cost memory 0.09064483642578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036791767300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f44d62eb-5a91-4247-b9dc-1d6fe7598a92", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036790035100, "endTime": 31036791837900}, "additional": {"logType": "info", "children": [], "durationId": "f1abfd4d-cb4e-4c21-bac5-77e8b3f0052b"}}, {"head": {"id": "ad07dbce-8f6b-45c8-ba49-7b59270ee356", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036874290600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6d053f3-5af5-46d0-b87f-921b6239c431", "name": "default@CompileArkTS work[57] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036874825700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c61d65b7-b97d-4466-9fcf-eac7d222236a", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036874940700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "019d9f14-bce4-494e-834f-bc045d3ae080", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036874994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8729830-9a3c-42de-bf24-12bfed9cc1ce", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036875274700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c18bf021-d254-4622-9c31-4d14684f447e", "name": "default@CompileArkTS work[58] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036876993800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8851c6ea-2e50-4323-b521-c51ebd471054", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038270049400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30eeb2d1-84dd-4865-9c33-1f15b2e5ff06", "name": "CopyResources is end, endTime: 31038270491700", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038270512500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "190950d4-9dc6-4be8-8792-abc4203de109", "name": "default@CompileArkTS work[58] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038272777100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a97386c-dab9-440f-825f-16ba4e378921", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 31038231280300, "endTime": 31038266693100}, "additional": {"logType": "info", "children": [], "durationId": "722a6460-b050-4a71-8fc0-41e0a5b5a3a2", "parent": "6da2869c-5e13-42d1-9e51-40f376c6d0db"}}, {"head": {"id": "5c00b55b-6a03-4d76-bb49-f4c3ba8dc424", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038342288000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3588abf0-a989-4b6c-9734-bf707fc974a7", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31036875159800, "endTime": 31036878332500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "logId": "de6181da-4a6b-4e02-a5f9-0d706e86a659"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de6181da-4a6b-4e02-a5f9-0d706e86a659", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036875159800, "endTime": 31036878332500}, "additional": {"logType": "info", "children": [], "durationId": "3588abf0-a989-4b6c-9734-bf707fc974a7", "parent": "42584880-1285-4dae-b5ce-7d15afad9fc7"}}, {"head": {"id": "f611b84f-5ff2-4fc2-8805-51f144463e91", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31036878347600, "endTime": 31036878464600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "logId": "ca25aba5-726a-4272-85af-96ffcc94674b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca25aba5-726a-4272-85af-96ffcc94674b", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036878347600, "endTime": 31036878464600}, "additional": {"logType": "info", "children": [], "durationId": "f611b84f-5ff2-4fc2-8805-51f144463e91", "parent": "42584880-1285-4dae-b5ce-7d15afad9fc7"}}, {"head": {"id": "0be6c664-fb84-4933-b006-b56040a76bc6", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31036878475600, "endTime": 31036878507700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "logId": "23b24714-3318-429a-914f-02f7d92cdb46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23b24714-3318-429a-914f-02f7d92cdb46", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036878475600, "endTime": 31036878507700}, "additional": {"logType": "info", "children": [], "durationId": "0be6c664-fb84-4933-b006-b56040a76bc6", "parent": "42584880-1285-4dae-b5ce-7d15afad9fc7"}}, {"head": {"id": "f8c88fc6-6634-412d-96ea-c744b2903d3f", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31036878524900, "endTime": 31038262418100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "logId": "1c3a353b-f4a2-41e4-af39-e0e337279a82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c3a353b-f4a2-41e4-af39-e0e337279a82", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036878524900, "endTime": 31038262418100}, "additional": {"logType": "info", "children": [], "durationId": "f8c88fc6-6634-412d-96ea-c744b2903d3f", "parent": "42584880-1285-4dae-b5ce-7d15afad9fc7"}}, {"head": {"id": "b52fca02-2927-4f0d-b4ec-ca20ad757e83", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038262436300, "endTime": 31038268894800}, "additional": {"children": ["ad98ebae-e5fb-4c77-81dd-30e58baf6ee6", "c7d737be-70e9-4d09-8cdb-086dd8d84931", "b21fee17-039c-4d5d-b23c-c3f1d7c3ed7a"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "logId": "889f39ad-3c65-4206-b724-47917d90e3cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "889f39ad-3c65-4206-b724-47917d90e3cf", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038262436300, "endTime": 31038268894800}, "additional": {"logType": "info", "children": ["d6f985ac-f41e-459d-8e2d-73c82cabc6bd", "c9dafa98-978e-467c-85a2-251df603e8c2", "575612d4-0fc2-4505-b306-39aee1450347"], "durationId": "b52fca02-2927-4f0d-b4ec-ca20ad757e83", "parent": "42584880-1285-4dae-b5ce-7d15afad9fc7"}}, {"head": {"id": "ad98ebae-e5fb-4c77-81dd-30e58baf6ee6", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038262449700, "endTime": 31038262455100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b52fca02-2927-4f0d-b4ec-ca20ad757e83", "logId": "d6f985ac-f41e-459d-8e2d-73c82cabc6bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d6f985ac-f41e-459d-8e2d-73c82cabc6bd", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038262449700, "endTime": 31038262455100}, "additional": {"logType": "info", "children": [], "durationId": "ad98ebae-e5fb-4c77-81dd-30e58baf6ee6", "parent": "889f39ad-3c65-4206-b724-47917d90e3cf"}}, {"head": {"id": "c7d737be-70e9-4d09-8cdb-086dd8d84931", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038262458000, "endTime": 31038264791300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b52fca02-2927-4f0d-b4ec-ca20ad757e83", "logId": "c9dafa98-978e-467c-85a2-251df603e8c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9dafa98-978e-467c-85a2-251df603e8c2", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038262458000, "endTime": 31038264791300}, "additional": {"logType": "info", "children": [], "durationId": "c7d737be-70e9-4d09-8cdb-086dd8d84931", "parent": "889f39ad-3c65-4206-b724-47917d90e3cf"}}, {"head": {"id": "b21fee17-039c-4d5d-b23c-c3f1d7c3ed7a", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038264795000, "endTime": 31038268882000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b52fca02-2927-4f0d-b4ec-ca20ad757e83", "logId": "575612d4-0fc2-4505-b306-39aee1450347"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "575612d4-0fc2-4505-b306-39aee1450347", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038264795000, "endTime": 31038268882000}, "additional": {"logType": "info", "children": [], "durationId": "b21fee17-039c-4d5d-b23c-c3f1d7c3ed7a", "parent": "889f39ad-3c65-4206-b724-47917d90e3cf"}}, {"head": {"id": "6c88c1e6-0bee-407f-b9c1-4c761a070e7c", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038268908600, "endTime": 31038340685800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "logId": "97a438bd-3f70-4167-a9ea-a429c66341fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97a438bd-3f70-4167-a9ea-a429c66341fb", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038268908600, "endTime": 31038340685800}, "additional": {"logType": "info", "children": [], "durationId": "6c88c1e6-0bee-407f-b9c1-4c761a070e7c", "parent": "42584880-1285-4dae-b5ce-7d15afad9fc7"}}, {"head": {"id": "87f2e24c-78f0-4448-a3a7-0ace21d639ef", "name": "default@CompileArkTS work[57] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038350351400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42584880-1285-4dae-b5ce-7d15afad9fc7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31036875049800, "endTime": 31038340852500}, "additional": {"logType": "info", "children": ["de6181da-4a6b-4e02-a5f9-0d706e86a659", "ca25aba5-726a-4272-85af-96ffcc94674b", "23b24714-3318-429a-914f-02f7d92cdb46", "1c3a353b-f4a2-41e4-af39-e0e337279a82", "889f39ad-3c65-4206-b724-47917d90e3cf", "97a438bd-3f70-4167-a9ea-a429c66341fb"], "durationId": "ab919396-4a9b-46c4-a386-0eb3ce03e591", "parent": "6da2869c-5e13-42d1-9e51-40f376c6d0db"}}, {"head": {"id": "6da2869c-5e13-42d1-9e51-40f376c6d0db", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036699780100, "endTime": 31038350511000, "totalTime": 1538140600}, "additional": {"logType": "info", "children": ["42584880-1285-4dae-b5ce-7d15afad9fc7", "6a97386c-dab9-440f-825f-16ba4e378921"], "durationId": "c5f5be51-089d-4526-b13f-992e964ab5a1"}}, {"head": {"id": "e0100fbc-865e-4ac8-be91-5898f99dceed", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038355838600, "endTime": 31038357265500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "e18a10f1-acd0-41eb-8a89-21c70818f447", "logId": "c5de16f3-5d1b-455a-a0f1-835ec8a0126a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e18a10f1-acd0-41eb-8a89-21c70818f447", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038353911700}, "additional": {"logType": "detail", "children": [], "durationId": "e0100fbc-865e-4ac8-be91-5898f99dceed"}}, {"head": {"id": "c35e3dee-7c53-4881-945d-d998f348134e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038354301900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96168da1-d011-4ec1-9a10-255ac808eb5e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038354432400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ba13b7-6965-46e8-930d-0ca1679b6517", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038355848500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42ce73e0-10bd-4d89-8608-4297d0b7c08c", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038356086900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fa05590-7a84-44c7-bf83-8c3901a24030", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038357032200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f762da-94d6-455f-a1e8-b816d70203ea", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07466888427734375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038357180900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5de16f3-5d1b-455a-a0f1-835ec8a0126a", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038355838600, "endTime": 31038357265500}, "additional": {"logType": "info", "children": [], "durationId": "e0100fbc-865e-4ac8-be91-5898f99dceed"}}, {"head": {"id": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038366859200, "endTime": 31038886678400}, "additional": {"children": ["bdb6a0a6-abab-4628-afe3-3e93521d034e", "687664d6-1626-4ab2-917e-5d1e46798a44", "c2bd9b19-f150-41c2-9ef2-deafa7ec6062"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "f3da132c-0b2a-4b02-9901-1df71f47ccd5", "logId": "04fe5fde-94e0-40de-9b2d-d2778ef3737c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f3da132c-0b2a-4b02-9901-1df71f47ccd5", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038359639200}, "additional": {"logType": "detail", "children": [], "durationId": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1"}}, {"head": {"id": "50824e6e-8609-48d9-bc01-05920bfdd9c7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038360035300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c12437-c82a-46d2-882f-0fe17d01d968", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038360156200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4212f6a0-9772-4f71-b830-bc9fab39104f", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038366871300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780b7cef-c11d-460e-999a-f77198468bb7", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038378291900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8091b910-7dc7-4eb2-8c0b-19cabf6a4502", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038378434000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efb769f1-23b0-4b43-adbf-7a43dd0c99ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038378521300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8836e8ae-22c9-4948-ac42-018532a100b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038378575900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdb6a0a6-abab-4628-afe3-3e93521d034e", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038379454700, "endTime": 31038380697100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1", "logId": "418271f6-fd65-463f-a18e-0765fbc03991"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35e91bc2-e4c3-4b6e-a6e1-fcbd892cccc4", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038380558600}, "additional": {"logType": "debug", "children": [], "durationId": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1"}}, {"head": {"id": "418271f6-fd65-463f-a18e-0765fbc03991", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038379454700, "endTime": 31038380697100}, "additional": {"logType": "info", "children": [], "durationId": "bdb6a0a6-abab-4628-afe3-3e93521d034e", "parent": "04fe5fde-94e0-40de-9b2d-d2778ef3737c"}}, {"head": {"id": "687664d6-1626-4ab2-917e-5d1e46798a44", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038381286700, "endTime": 31038382988500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1", "logId": "239183de-31f3-4300-8241-6f063864bf25"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b499c017-1598-4836-b395-afcbc343a764", "name": "default@PackageHap work[59] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038381978500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2bd9b19-f150-41c2-9ef2-deafa7ec6062", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038456053700, "endTime": 31038886265200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1", "logId": "2967d975-9841-4963-83b7-a7948ca3cb92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9e00d6c-f200-40a4-bbec-989c7bef99cb", "name": "default@PackageHap work[59] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038382717700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ff62ec-947e-4139-a340-b8c891f447fb", "name": "default@PackageHap work[59] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038382815600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239183de-31f3-4300-8241-6f063864bf25", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038381286700, "endTime": 31038382988500}, "additional": {"logType": "info", "children": [], "durationId": "687664d6-1626-4ab2-917e-5d1e46798a44", "parent": "04fe5fde-94e0-40de-9b2d-d2778ef3737c"}}, {"head": {"id": "6329c564-2c8c-4a05-b91c-fd207ebf3264", "name": "entry : default@PackageHap cost memory 1.3046417236328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038386745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c3cc28e-e5b4-4978-8f4d-8203f3f7e2b1", "name": "default@PackageHap work[59] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038455943600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c11c562-5eb9-4c30-b111-be6dbd85e2b6", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038486247800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "038fab3a-07ac-4fe4-883e-7c1bdcbb21cf", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038486395900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb8bbd6a-9116-43dd-93d2-4e6975577131", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038486459700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5883757e-e8fe-41e5-8dd3-08ea507d8cb0", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038486514400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e595c7d5-ddad-421d-8473-bc5c69bcd475", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038486563100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4cd80a7-0b9d-4115-b418-4a7bd93d288a", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038486676600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09a06621-4ec7-4fce-b9d5-9438867ee02c", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038886353700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc860ff-3573-4de1-8424-c1b74a35eb9f", "name": "default@PackageHap work[59] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038886545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2967d975-9841-4963-83b7-a7948ca3cb92", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 31038456053700, "endTime": 31038886265200}, "additional": {"logType": "info", "children": [], "durationId": "c2bd9b19-f150-41c2-9ef2-deafa7ec6062", "parent": "04fe5fde-94e0-40de-9b2d-d2778ef3737c"}}, {"head": {"id": "04fe5fde-94e0-40de-9b2d-d2778ef3737c", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038366859200, "endTime": 31038886678400, "totalTime": 450224500}, "additional": {"logType": "info", "children": ["418271f6-fd65-463f-a18e-0765fbc03991", "239183de-31f3-4300-8241-6f063864bf25", "2967d975-9841-4963-83b7-a7948ca3cb92"], "durationId": "5b64a1f5-e36b-434e-a888-e6d0faf4e7d1"}}, {"head": {"id": "0f33c94e-b720-4530-bc30-a75af18b6972", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038894002200, "endTime": 31038896397300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "c5d5e313-72cd-4732-a9f4-7fd9e19f1e23", "logId": "88d07b0f-00ea-4de8-931a-b6fffbe4b8c3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5d5e313-72cd-4732-a9f4-7fd9e19f1e23", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038890789800}, "additional": {"logType": "detail", "children": [], "durationId": "0f33c94e-b720-4530-bc30-a75af18b6972"}}, {"head": {"id": "b97110bc-b968-4ca9-b3d4-b66ae64be438", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038891157800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b1839b3-c5cf-41b9-9ac6-caf430deb60b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038891257000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4820e9c0-6b98-45f6-ada3-235552f13fc2", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038894013300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6958f0ff-d488-4ced-b2ed-456296085c7f", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038894487200}, "additional": {"logType": "warn", "children": [], "durationId": "0f33c94e-b720-4530-bc30-a75af18b6972"}}, {"head": {"id": "c02800e4-d5d2-453f-811e-ea1e0cd63433", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038895553800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d106f8cd-dfca-415a-ac9e-11e6eda6bce8", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038895855500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e64fd19-635b-479b-acd5-8492087df94d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038895955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca79326f-6f9b-4c6d-a7b3-5d794429aff2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038896019600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bea786-267d-4bb1-b94d-ba96bd54ae5f", "name": "entry : default@SignHap cost memory 0.12337493896484375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038896255000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e48125-0653-4862-b2c4-735bb12670a2", "name": "runTaskFromQueue task cost before running: 2 s 698 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038896342500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d07b0f-00ea-4de8-931a-b6fffbe4b8c3", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038894002200, "endTime": 31038896397300, "totalTime": 2321100}, "additional": {"logType": "info", "children": [], "durationId": "0f33c94e-b720-4530-bc30-a75af18b6972"}}, {"head": {"id": "9ded7986-feec-4918-bc64-1c1e4ee8d864", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038899883500, "endTime": 31038905460300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4f375767-c0f9-4b17-b600-5cccdaa93abf", "logId": "2b75d07e-e70a-4c3a-baad-f53365f3eacb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f375767-c0f9-4b17-b600-5cccdaa93abf", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038898328800}, "additional": {"logType": "detail", "children": [], "durationId": "9ded7986-feec-4918-bc64-1c1e4ee8d864"}}, {"head": {"id": "efd25bf8-2943-4601-a020-3a47d139de51", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038898936300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb2fbb42-24f7-4de4-ac41-859e01ebcba8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038899048600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "919046c0-f373-4dcb-ab52-9c6d00206250", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038899892600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "982e9d66-5385-4ec6-a5f1-adebaca38567", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038905099700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4694fd9d-8cfe-44ad-b408-49afa0a7811e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038905237800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f42bca-4c94-4857-ac37-5572e42ec258", "name": "entry : default@CollectDebugSymbol cost memory 0.23992919921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038905319700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "468b2911-8099-4b04-9be7-58d1d0bb191c", "name": "runTaskFromQueue task cost before running: 2 s 707 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038905401300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b75d07e-e70a-4c3a-baad-f53365f3eacb", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038899883500, "endTime": 31038905460300, "totalTime": 5492800}, "additional": {"logType": "info", "children": [], "durationId": "9ded7986-feec-4918-bc64-1c1e4ee8d864"}}, {"head": {"id": "d76464bd-f4f7-4b0e-bcc9-6aafa1067df4", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038907598000, "endTime": 31038907932400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "e3036aac-718d-4f52-be71-7f45404a3130", "logId": "b8ee416b-2e34-43ee-933e-b6e8f9c49d1e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3036aac-718d-4f52-be71-7f45404a3130", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038907547500}, "additional": {"logType": "detail", "children": [], "durationId": "d76464bd-f4f7-4b0e-bcc9-6aafa1067df4"}}, {"head": {"id": "70af3c0f-bffa-45ce-8275-120615b42118", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038907633400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81b0979-115f-49a6-bc05-746bb7ce3cc4", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038907784900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55e6405e-64ad-47c4-89e4-281c25a1a1f0", "name": "runTaskFromQueue task cost before running: 2 s 710 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038907872700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8ee416b-2e34-43ee-933e-b6e8f9c49d1e", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038907598000, "endTime": 31038907932400, "totalTime": 251500}, "additional": {"logType": "info", "children": [], "durationId": "d76464bd-f4f7-4b0e-bcc9-6aafa1067df4"}}, {"head": {"id": "c26ec5d6-bf26-4017-a48c-d66c2805b605", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038915495100, "endTime": 31038915514800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4879aace-aa63-41e1-8abb-ea6635121168", "logId": "0bd98f8a-3af0-4735-af0b-378684d15385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0bd98f8a-3af0-4735-af0b-378684d15385", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038915495100, "endTime": 31038915514800}, "additional": {"logType": "info", "children": [], "durationId": "c26ec5d6-bf26-4017-a48c-d66c2805b605"}}, {"head": {"id": "bafdcb11-985d-4486-908a-17520c8bef81", "name": "BUILD SUCCESSFUL in 2 s 718 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038915554400}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "dd8e5fde-5be4-4a0b-aeca-bc611c15d6c2", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31036198476000, "endTime": 31038915808100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 6}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "7fc53b7d-50a5-4d44-9cf1-432135c25d5e", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038915838600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07f312f9-72cf-4751-81df-784f59c0ceec", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038915904900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "200488f0-0d72-493f-a964-6d9a466b6019", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038915957700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e076b338-515b-447b-ab4b-6855a6c4097c", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038916019600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e69e8900-90c6-4880-af48-827a76ec8390", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038916082100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f8c1ac-33c2-4a7c-bb21-67674c12b90a", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038916349700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acc22244-2ea0-4d90-955e-989ee780d6f6", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038917136200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "896cc8bf-0c1e-42fc-8d15-53a4f3017152", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038917405600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06aee359-c4d6-4d92-bdcc-e7eac3cdfbeb", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038917480000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1b06735-8163-47a1-af8c-8d0af93c97b6", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038917545800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "351f7e66-d654-45da-acc1-c87f16fc1f30", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038917822600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354e052b-a787-41af-80ca-32b918982a4c", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038918776200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82aee543-192b-4ddc-a920-c7a33890afb2", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038919015000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcb817c2-4511-481e-9f94-18635e5443e8", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038919086300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40c27103-ad33-4f2d-89c3-e6f738a39dac", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038919192800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6826d634-1b56-4b97-a65f-e0b862aa09be", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038919298600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0f5c4b4-b013-4f57-973d-a21258ae5b5d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038919355400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fc2653e-6fa7-41c3-b62d-fb72a70f5017", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038920521800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c44bb1bb-0dfa-486f-b876-e51f57f9c084", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038921579300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb920c4e-fcb3-466f-b17f-e3a6849ce070", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038921994400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad7c187-e8aa-41eb-9423-5bd6648f4ee3", "name": "Incremental task entry:default@ProcessLibs post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038922390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b59d198-4476-4a00-a23a-78a26f5cfcbf", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038922579100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "103a49bb-7995-42c5-882e-94d03a4f6a0a", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038922733900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b07fde3b-7e5c-4d61-be24-23eb2163fb12", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038925310300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "637432a5-9b6c-4a6c-95da-716171195609", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038925758300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7446677-db6a-4667-91b5-b5c85228dc63", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038926410500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08497557-a8bd-4bdd-b4bf-b6645a4be6b7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038926578800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97fb42e-4946-492c-a8c8-73404b7a3bb7", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038926821600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87174335-c3ee-4adc-9898-4470662bb405", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038927288400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e50fcc8e-56d8-49cd-a94c-e3e6666aaa7f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038927362900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cba2776-e08c-4dcd-9e7d-810569ec751d", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038927524500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c4e3489-dfa1-4c98-8b20-41747e2fdbd4", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038927797500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00316d03-5c47-468a-9651-c66f8176b46c", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038928330900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "383ce9c1-5e37-409f-8198-92d6f0f9fd00", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038929355200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb36615a-9eb8-463e-9731-c7be74b3a81d", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038929746600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "830fd48d-f6b6-47ca-bf01-8a04dda06987", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038930387400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f73dbcbd-f185-4994-89e2-c45516f5f9a3", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038930553600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a56913-7341-4260-8e0a-6811bb92902c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038930725600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c2be8d-0d38-4d31-b8b7-36e6a73b4f0a", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038931144600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab15e012-63b6-4ace-a4c4-c8f22d63fcc9", "name": "Incremental task entry:default@BuildJS post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038931350200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4a8a6e-7f2e-4966-878b-d885042ddab5", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038931416200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5941fab-9439-4c4f-b122-aaaa5c990b80", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038931464400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d18e8f49-2c5a-4406-823a-a5e5d72e72f1", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038932353300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9b6e86-2109-4a1c-99ab-9e6460aea5f5", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038932616100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd1316eb-19e9-44ba-a883-5aa235bf5e21", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038932805500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4038a99-953e-40b2-8a06-87846ac4465f", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038938926400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fde1ae29-b6cf-4262-b215-ef9af8407791", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038939245100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b996d4eb-51b6-46a2-bd33-a62a93142370", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038939507300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f6fb4bd-400b-4921-ac9b-b645c6dd10ff", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038939590300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b56aa74-a335-4adf-b188-8c8f6dc0375c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038939790300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59effd70-ead2-44cb-bc79-580b974a6f5c", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038940408200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe27d92-17f8-46ba-b54a-d80dc5806f97", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038940640400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c04c32-29a8-41f2-b27a-8b91a385a874", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038940925200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58059e7b-31b4-4719-97de-b59f295f352c", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038941183100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7443c74d-9444-4af2-a2c2-a09ad6106e9e", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038941341200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7227332-0f8f-4155-b143-51be6fb33f29", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038941413900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "728c3b0d-f410-4277-948e-b5b1235db953", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038941598400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1bff285-8a37-4eab-b7a2-11ab62318f34", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038943748200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "721143e4-4619-4b2b-a8c4-3fe745a93643", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038944000400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5f95fb2-ec2a-472e-989c-9295bbf8be21", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038944311400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "944f4dd9-a4ec-472b-a2ad-124d0036ce3a", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 31038944527500}, "additional": {"logType": "debug", "children": []}}], "workLog": []}