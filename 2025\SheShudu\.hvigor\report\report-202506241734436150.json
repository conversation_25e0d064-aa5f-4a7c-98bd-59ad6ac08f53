{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "b7f36ee8-c39e-48d5-8be8-f318b9d98a7b", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32674686207900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff34c5f-10ac-4ed4-9c73-025e61d76fb3", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32674696695000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea6f665d-64c2-4848-b519-3903478dbad6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32674697116100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bb9336a-6423-48d7-9653-c7fc448c8c08", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32674697505600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08e10448-05f2-45bc-bfa0-5306de7e4652", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715508298600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715515535300, "endTime": 32715907739300}, "additional": {"children": ["51059f4c-b76c-4766-a3d8-a73b45afe6cb", "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "031162d6-caa2-4295-bdb3-106b23c7478c", "85cdcfb6-9e2f-43bb-a1c8-151ca1472b71", "b71994ae-41ae-412e-b266-456945d3dedb", "598400bc-8652-463e-881b-390071a1017a", "dabc66fb-8f9c-4151-9fb9-0dd952679120"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51059f4c-b76c-4766-a3d8-a73b45afe6cb", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715515537600, "endTime": 32715531904500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "d3b5339d-90e6-4727-908f-bbd69c2cfb44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715531920800, "endTime": 32715906425600}, "additional": {"children": ["b0f91d49-9a38-4f87-bb48-e2342dab1f55", "047cc3c7-819d-4968-a41e-9c93843b26a1", "2537a6f4-4603-4a68-a772-1783bc7adacf", "7fd1cf49-7ec7-4a19-b754-c4043226ce99", "f006a443-1896-45dc-8ee8-5a336cdc3fb1", "93a3fbfc-722b-4f17-850b-16802dbb08b4", "3243a9de-d8ae-45fa-9af1-5f11dc0cd061", "7b12d4f0-dd95-4769-b41f-a140216e032c", "1fe9e5e1-9ee6-4ea2-b056-4102d4d5c2e2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "cd54010d-fb53-489b-bc8d-106abd720d4d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "031162d6-caa2-4295-bdb3-106b23c7478c", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715906453100, "endTime": 32715907729900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "d2de78b7-a26a-4d5d-a2b2-aab25ae90bee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85cdcfb6-9e2f-43bb-a1c8-151ca1472b71", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715907734800, "endTime": 32715907736000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "19d4b287-e065-4228-9a12-df4d400998a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b71994ae-41ae-412e-b266-456945d3dedb", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715519646600, "endTime": 32715519686300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "fc0e1099-027b-46b7-824c-3c0ab019bcac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc0e1099-027b-46b7-824c-3c0ab019bcac", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715519646600, "endTime": 32715519686300}, "additional": {"logType": "info", "children": [], "durationId": "b71994ae-41ae-412e-b266-456945d3dedb", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "598400bc-8652-463e-881b-390071a1017a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715525135600, "endTime": 32715525155500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "50ba2369-e8dc-49cf-af64-a88eabc4f045"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50ba2369-e8dc-49cf-af64-a88eabc4f045", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715525135600, "endTime": 32715525155500}, "additional": {"logType": "info", "children": [], "durationId": "598400bc-8652-463e-881b-390071a1017a", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "f1ee268c-feef-486c-95ce-78b2753e3651", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715525198900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20089995-6648-4b65-967e-ac0f2fd834f4", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715531773400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b5339d-90e6-4727-908f-bbd69c2cfb44", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715515537600, "endTime": 32715531904500}, "additional": {"logType": "info", "children": [], "durationId": "51059f4c-b76c-4766-a3d8-a73b45afe6cb", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "b0f91d49-9a38-4f87-bb48-e2342dab1f55", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715537774400, "endTime": 32715537782200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "a6b9dafb-b7de-4399-b924-9d9d853d1ec4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "047cc3c7-819d-4968-a41e-9c93843b26a1", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715537800000, "endTime": 32715544616600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "0ad29010-83c2-4268-86c1-61ad588a9d43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2537a6f4-4603-4a68-a772-1783bc7adacf", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715544644000, "endTime": 32715694392200}, "additional": {"children": ["0b621bd4-a400-44bf-b121-187587756996", "7ac971ac-d324-4813-9140-de8752eca377", "1ee84271-9813-4f08-b4ba-558e2baead6a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "a7f50354-20ab-48a6-8973-9702d8971c91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fd1cf49-7ec7-4a19-b754-c4043226ce99", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715694406900, "endTime": 32715731688500}, "additional": {"children": ["718e9b9e-ea0f-468f-93ee-7670203ced1e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "4d57eb12-c6e8-4307-9c98-8f2eb795139b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f006a443-1896-45dc-8ee8-5a336cdc3fb1", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715731698800, "endTime": 32715866301500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "25ae6db3-7cfd-4af6-8265-37a31dc79098"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93a3fbfc-722b-4f17-850b-16802dbb08b4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715867890300, "endTime": 32715895453800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "64a052fa-1894-4c8f-9977-2c1c863623f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3243a9de-d8ae-45fa-9af1-5f11dc0cd061", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715895477000, "endTime": 32715906198200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "2587e72b-9828-4151-8f22-ae46ce9a154c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b12d4f0-dd95-4769-b41f-a140216e032c", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715906224700, "endTime": 32715906413000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "071c6942-fd24-42d5-806b-68a4b7f8b455"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6b9dafb-b7de-4399-b924-9d9d853d1ec4", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715537774400, "endTime": 32715537782200}, "additional": {"logType": "info", "children": [], "durationId": "b0f91d49-9a38-4f87-bb48-e2342dab1f55", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "0ad29010-83c2-4268-86c1-61ad588a9d43", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715537800000, "endTime": 32715544616600}, "additional": {"logType": "info", "children": [], "durationId": "047cc3c7-819d-4968-a41e-9c93843b26a1", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "0b621bd4-a400-44bf-b121-187587756996", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715545743400, "endTime": 32715545766300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2537a6f4-4603-4a68-a772-1783bc7adacf", "logId": "8252c0a5-e4b0-44ce-b87a-19b184a5d89f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8252c0a5-e4b0-44ce-b87a-19b184a5d89f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715545743400, "endTime": 32715545766300}, "additional": {"logType": "info", "children": [], "durationId": "0b621bd4-a400-44bf-b121-187587756996", "parent": "a7f50354-20ab-48a6-8973-9702d8971c91"}}, {"head": {"id": "7ac971ac-d324-4813-9140-de8752eca377", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715548491500, "endTime": 32715693394600}, "additional": {"children": ["f8df5f4e-7dff-4645-b7d1-49807d0bcb5c", "cedae98f-c353-4d54-b3e7-920b0086fcee"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2537a6f4-4603-4a68-a772-1783bc7adacf", "logId": "c7c218a2-c3f0-4696-b4a7-c55950608db7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8df5f4e-7dff-4645-b7d1-49807d0bcb5c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715548494400, "endTime": 32715557874400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ac971ac-d324-4813-9140-de8752eca377", "logId": "8d2d9c5f-b985-4d23-b974-065f58e1e3ea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cedae98f-c353-4d54-b3e7-920b0086fcee", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715557893800, "endTime": 32715693380600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7ac971ac-d324-4813-9140-de8752eca377", "logId": "80d09286-8bf1-4d8f-a307-f24407dc0547"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2065d5cc-f660-4537-a45f-e53cf8787284", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715548501400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85db69db-16e3-47a5-bea3-1865b02c51e6", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715557587600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d2d9c5f-b985-4d23-b974-065f58e1e3ea", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715548494400, "endTime": 32715557874400}, "additional": {"logType": "info", "children": [], "durationId": "f8df5f4e-7dff-4645-b7d1-49807d0bcb5c", "parent": "c7c218a2-c3f0-4696-b4a7-c55950608db7"}}, {"head": {"id": "7a97b574-ef4f-41e4-8601-06df98932af7", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715557906000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "794d875a-0b21-4dee-8af5-619aff37b96a", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715564895500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa539e6-f985-40f6-acc4-93f36c307a4a", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715565027300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd07daa6-b8cf-4985-a6e7-dd666f7909cf", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715565168800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "460f547d-d722-4160-87a4-40f1adbc554b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715565276400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751f0477-797a-4e14-b37f-dc82efdd4cbc", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715568403200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84a92404-bb60-445b-8741-36c7d82d3a0c", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715576460000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a45e45-a915-40ce-8aeb-fc0299a45973", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715590825300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2567f0aa-a463-4971-8b44-e33c6477e8e4", "name": "Sdk init in 45 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715621940500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5b31ee0-d80c-4885-9b70-0f0f6d1f1d46", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715633221300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 34}, "markType": "other"}}, {"head": {"id": "9432a297-c7c3-4dd9-aa5c-85c5aca42036", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715633252600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 34}, "markType": "other"}}, {"head": {"id": "76610b6d-c209-4dff-bfde-e17e5ba1f313", "name": "Project task initialization takes 54 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715693090000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca47f0aa-1c0e-4bc3-b48e-88b1369c4b25", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715693214400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3f421a6-8879-4f05-8bcd-06490a3e408b", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715693276300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f251419c-11e9-4a03-985d-16da6d4a9378", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715693329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d09286-8bf1-4d8f-a307-f24407dc0547", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715557893800, "endTime": 32715693380600}, "additional": {"logType": "info", "children": [], "durationId": "cedae98f-c353-4d54-b3e7-920b0086fcee", "parent": "c7c218a2-c3f0-4696-b4a7-c55950608db7"}}, {"head": {"id": "c7c218a2-c3f0-4696-b4a7-c55950608db7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715548491500, "endTime": 32715693394600}, "additional": {"logType": "info", "children": ["8d2d9c5f-b985-4d23-b974-065f58e1e3ea", "80d09286-8bf1-4d8f-a307-f24407dc0547"], "durationId": "7ac971ac-d324-4813-9140-de8752eca377", "parent": "a7f50354-20ab-48a6-8973-9702d8971c91"}}, {"head": {"id": "1ee84271-9813-4f08-b4ba-558e2baead6a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715694317300, "endTime": 32715694373600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2537a6f4-4603-4a68-a772-1783bc7adacf", "logId": "2c186e44-fe58-4f05-a3c4-34567d5f00fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c186e44-fe58-4f05-a3c4-34567d5f00fd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715694317300, "endTime": 32715694373600}, "additional": {"logType": "info", "children": [], "durationId": "1ee84271-9813-4f08-b4ba-558e2baead6a", "parent": "a7f50354-20ab-48a6-8973-9702d8971c91"}}, {"head": {"id": "a7f50354-20ab-48a6-8973-9702d8971c91", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715544644000, "endTime": 32715694392200}, "additional": {"logType": "info", "children": ["8252c0a5-e4b0-44ce-b87a-19b184a5d89f", "c7c218a2-c3f0-4696-b4a7-c55950608db7", "2c186e44-fe58-4f05-a3c4-34567d5f00fd"], "durationId": "2537a6f4-4603-4a68-a772-1783bc7adacf", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "718e9b9e-ea0f-468f-93ee-7670203ced1e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715695752100, "endTime": 32715731674700}, "additional": {"children": ["36268e0a-53c1-4f6e-99a7-de2ab2c76923", "9ecc8d0c-ee48-402a-9b7a-ff6d2471cbc3", "4bbe8b81-136a-4cb9-bb69-bd46313ef3a2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7fd1cf49-7ec7-4a19-b754-c4043226ce99", "logId": "b8083c9f-34a1-42a8-835f-190ef794aa1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36268e0a-53c1-4f6e-99a7-de2ab2c76923", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715703681300, "endTime": 32715703701600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "718e9b9e-ea0f-468f-93ee-7670203ced1e", "logId": "9944130d-28ed-44a9-a8c6-f2c3ad12ba08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9944130d-28ed-44a9-a8c6-f2c3ad12ba08", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715703681300, "endTime": 32715703701600}, "additional": {"logType": "info", "children": [], "durationId": "36268e0a-53c1-4f6e-99a7-de2ab2c76923", "parent": "b8083c9f-34a1-42a8-835f-190ef794aa1a"}}, {"head": {"id": "9ecc8d0c-ee48-402a-9b7a-ff6d2471cbc3", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715708277600, "endTime": 32715730047300}, "additional": {"children": ["dd4bc023-37f9-4819-995e-7fc1b68d30ac", "796dfbe0-dde9-40ce-9af9-1daaefc2408e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "718e9b9e-ea0f-468f-93ee-7670203ced1e", "logId": "54f882c8-cf49-4760-ae7c-c9f6e6b53c18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd4bc023-37f9-4819-995e-7fc1b68d30ac", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715708280000, "endTime": 32715711691600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ecc8d0c-ee48-402a-9b7a-ff6d2471cbc3", "logId": "490e4877-d095-4d4d-8c6e-42a14b34a93e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "796dfbe0-dde9-40ce-9af9-1daaefc2408e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715711715400, "endTime": 32715730017900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9ecc8d0c-ee48-402a-9b7a-ff6d2471cbc3", "logId": "bacaf867-7333-4254-9333-187de5371042"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29b3f246-b0ff-48f5-890c-900737699817", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715708286700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d9668c6-6264-4647-af51-42ffa9c912b8", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715711487300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490e4877-d095-4d4d-8c6e-42a14b34a93e", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715708280000, "endTime": 32715711691600}, "additional": {"logType": "info", "children": [], "durationId": "dd4bc023-37f9-4819-995e-7fc1b68d30ac", "parent": "54f882c8-cf49-4760-ae7c-c9f6e6b53c18"}}, {"head": {"id": "fb4e35e2-4612-4976-99d0-bbe69384813b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715711726900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d713a75-14c7-40f1-80dc-99fb861bf316", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715723452400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279364ed-bbb9-44a9-b790-514777c21c17", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715723663500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2573bc5e-5a6e-4580-bd68-c0767fb4e2cc", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715723887500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27fdb63e-60d0-4470-9615-399513ee59d0", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715724026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67145387-e083-4b6d-8ca8-7e37f30aff03", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715724090200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d444fe-9a9a-4dd3-a487-e3266e52ce37", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715724221000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7927ac96-ba3f-4f7f-947f-fd99fdd24719", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715724285800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8aeb5e2c-07c8-47d9-a5e2-4627471f2aff", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715729245300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "addaa39e-0184-402a-ad10-ffd2fe54f21a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715729757000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ad8114e-77e4-487c-a285-7b32d54b1fb9", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715729901100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f949bdc-2fdc-4870-beda-63cff51c9e5d", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715729961000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacaf867-7333-4254-9333-187de5371042", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715711715400, "endTime": 32715730017900}, "additional": {"logType": "info", "children": [], "durationId": "796dfbe0-dde9-40ce-9af9-1daaefc2408e", "parent": "54f882c8-cf49-4760-ae7c-c9f6e6b53c18"}}, {"head": {"id": "54f882c8-cf49-4760-ae7c-c9f6e6b53c18", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715708277600, "endTime": 32715730047300}, "additional": {"logType": "info", "children": ["490e4877-d095-4d4d-8c6e-42a14b34a93e", "bacaf867-7333-4254-9333-187de5371042"], "durationId": "9ecc8d0c-ee48-402a-9b7a-ff6d2471cbc3", "parent": "b8083c9f-34a1-42a8-835f-190ef794aa1a"}}, {"head": {"id": "4bbe8b81-136a-4cb9-bb69-bd46313ef3a2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715731615800, "endTime": 32715731655300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "718e9b9e-ea0f-468f-93ee-7670203ced1e", "logId": "53027236-0a9e-4f69-a5f7-d05dcbf7c961"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53027236-0a9e-4f69-a5f7-d05dcbf7c961", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715731615800, "endTime": 32715731655300}, "additional": {"logType": "info", "children": [], "durationId": "4bbe8b81-136a-4cb9-bb69-bd46313ef3a2", "parent": "b8083c9f-34a1-42a8-835f-190ef794aa1a"}}, {"head": {"id": "b8083c9f-34a1-42a8-835f-190ef794aa1a", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715695752100, "endTime": 32715731674700}, "additional": {"logType": "info", "children": ["9944130d-28ed-44a9-a8c6-f2c3ad12ba08", "54f882c8-cf49-4760-ae7c-c9f6e6b53c18", "53027236-0a9e-4f69-a5f7-d05dcbf7c961"], "durationId": "718e9b9e-ea0f-468f-93ee-7670203ced1e", "parent": "4d57eb12-c6e8-4307-9c98-8f2eb795139b"}}, {"head": {"id": "4d57eb12-c6e8-4307-9c98-8f2eb795139b", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715694406900, "endTime": 32715731688500}, "additional": {"logType": "info", "children": ["b8083c9f-34a1-42a8-835f-190ef794aa1a"], "durationId": "7fd1cf49-7ec7-4a19-b754-c4043226ce99", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "4c57e713-dca5-417e-910e-6056dbc53e4d", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715761701600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598ad5c9-6f7e-403b-b87e-c04483aa847b", "name": "hvigorfile, resolve hvigorfile dependencies in 135 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715866147600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25ae6db3-7cfd-4af6-8265-37a31dc79098", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715731698800, "endTime": 32715866301500}, "additional": {"logType": "info", "children": [], "durationId": "f006a443-1896-45dc-8ee8-5a336cdc3fb1", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "1fe9e5e1-9ee6-4ea2-b056-4102d4d5c2e2", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715867427100, "endTime": 32715867862100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "logId": "d4d946ce-a65f-410b-b7f6-25c8c486e3c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bd5e05e-0852-4191-947c-50d253eb92c3", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715867464600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4d946ce-a65f-410b-b7f6-25c8c486e3c9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715867427100, "endTime": 32715867862100}, "additional": {"logType": "info", "children": [], "durationId": "1fe9e5e1-9ee6-4ea2-b056-4102d4d5c2e2", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "e3e04430-c42f-468f-b73d-0afa11152371", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715869636900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ade745fa-34bd-4ca3-8a4a-3c243e1c33ba", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715894500100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a052fa-1894-4c8f-9977-2c1c863623f3", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715867890300, "endTime": 32715895453800}, "additional": {"logType": "info", "children": [], "durationId": "93a3fbfc-722b-4f17-850b-16802dbb08b4", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "3117de04-ad8b-47fe-a5b9-8af4f581edc0", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715899908500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49df1a7a-7ed7-491d-b4d6-f64ee868704a", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715900058900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91b1886b-5900-439f-9dd8-1bb0623e4ae5", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715903431000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1fd6bc-c9c3-43c3-b1d4-e00efab3fdc8", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715903563600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2587e72b-9828-4151-8f22-ae46ce9a154c", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715895477000, "endTime": 32715906198200}, "additional": {"logType": "info", "children": [], "durationId": "3243a9de-d8ae-45fa-9af1-5f11dc0cd061", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "b6fb411b-2f39-4344-b453-868d4e410543", "name": "Configuration phase cost:369 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715906249700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "071c6942-fd24-42d5-806b-68a4b7f8b455", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715906224700, "endTime": 32715906413000}, "additional": {"logType": "info", "children": [], "durationId": "7b12d4f0-dd95-4769-b41f-a140216e032c", "parent": "cd54010d-fb53-489b-bc8d-106abd720d4d"}}, {"head": {"id": "cd54010d-fb53-489b-bc8d-106abd720d4d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715531920800, "endTime": 32715906425600}, "additional": {"logType": "info", "children": ["a6b9dafb-b7de-4399-b924-9d9d853d1ec4", "0ad29010-83c2-4268-86c1-61ad588a9d43", "a7f50354-20ab-48a6-8973-9702d8971c91", "4d57eb12-c6e8-4307-9c98-8f2eb795139b", "25ae6db3-7cfd-4af6-8265-37a31dc79098", "64a052fa-1894-4c8f-9977-2c1c863623f3", "2587e72b-9828-4151-8f22-ae46ce9a154c", "071c6942-fd24-42d5-806b-68a4b7f8b455", "d4d946ce-a65f-410b-b7f6-25c8c486e3c9"], "durationId": "2ba256d6-3bba-4a3e-bb06-e82017c7e848", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "dabc66fb-8f9c-4151-9fb9-0dd952679120", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715907693500, "endTime": 32715907716100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54", "logId": "17fe58b2-98aa-4114-9d09-785522d8e099"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "17fe58b2-98aa-4114-9d09-785522d8e099", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715907693500, "endTime": 32715907716100}, "additional": {"logType": "info", "children": [], "durationId": "dabc66fb-8f9c-4151-9fb9-0dd952679120", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "d2de78b7-a26a-4d5d-a2b2-aab25ae90bee", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715906453100, "endTime": 32715907729900}, "additional": {"logType": "info", "children": [], "durationId": "031162d6-caa2-4295-bdb3-106b23c7478c", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "19d4b287-e065-4228-9a12-df4d400998a6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715907734800, "endTime": 32715907736000}, "additional": {"logType": "info", "children": [], "durationId": "85cdcfb6-9e2f-43bb-a1c8-151ca1472b71", "parent": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c"}}, {"head": {"id": "741e46fe-0895-4a62-9a3d-1f5b61cd7f1c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715515535300, "endTime": 32715907739300}, "additional": {"logType": "info", "children": ["d3b5339d-90e6-4727-908f-bbd69c2cfb44", "cd54010d-fb53-489b-bc8d-106abd720d4d", "d2de78b7-a26a-4d5d-a2b2-aab25ae90bee", "19d4b287-e065-4228-9a12-df4d400998a6", "fc0e1099-027b-46b7-824c-3c0ab019bcac", "50ba2369-e8dc-49cf-af64-a88eabc4f045", "17fe58b2-98aa-4114-9d09-785522d8e099"], "durationId": "cf2a4dca-069b-42a0-b17e-9c062ebe3d54"}}, {"head": {"id": "06274515-dd44-44cb-ba9e-9a7fe5b88e86", "name": "Configuration task cost before running: 397 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715907951900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c7d053-b594-4f23-88d6-3f096804ba62", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715912703800, "endTime": 32715920876500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "39d7e7d2-bf16-4243-911c-0c576cd6c437", "logId": "db018aaf-6b46-42d6-b025-e5db50b3f301"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "39d7e7d2-bf16-4243-911c-0c576cd6c437", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715909345400}, "additional": {"logType": "detail", "children": [], "durationId": "55c7d053-b594-4f23-88d6-3f096804ba62"}}, {"head": {"id": "6e8a0112-c58b-49d6-8e44-d5fcbc60cc72", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715909726500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9d3e4ab-5a5d-485e-813e-67320347ca25", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715909866300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63a7ead7-c4e5-45d1-83cb-90fb0bf6f4f3", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715912717700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8ee7ba-ab74-41aa-9599-1e46352af549", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715920621100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cada424-3937-458d-8b83-c4143f806661", "name": "entry : default@PreBuild cost memory 0.312713623046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715920798300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db018aaf-6b46-42d6-b025-e5db50b3f301", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715912703800, "endTime": 32715920876500}, "additional": {"logType": "info", "children": [], "durationId": "55c7d053-b594-4f23-88d6-3f096804ba62"}}, {"head": {"id": "01823840-7d4e-4dde-8b9b-113de1f83e97", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715926465200, "endTime": 32715928922700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "43420af2-ab5d-47aa-a4c6-7f427e041e34", "logId": "4bf94648-d16e-4d6a-9c5f-3a3baef870fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43420af2-ab5d-47aa-a4c6-7f427e041e34", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715924977400}, "additional": {"logType": "detail", "children": [], "durationId": "01823840-7d4e-4dde-8b9b-113de1f83e97"}}, {"head": {"id": "4fb9d8e3-293f-449d-ab33-0258be73ba29", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715925465400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93833049-9de4-413f-b06a-7e975f889f20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715925593400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b34b659-ce4e-421a-aea1-134374160b07", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715926478000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b27c0e5c-11c2-4a9f-9504-e73c0a30f09d", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715927513600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f70792-15b3-4337-ab3c-6f7d9980a2bc", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715928577200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06a5b05d-a9e7-4958-8229-320855c3e1e1", "name": "entry : default@GenerateMetadata cost memory 0.092498779296875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715928824100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bf94648-d16e-4d6a-9c5f-3a3baef870fc", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715926465200, "endTime": 32715928922700}, "additional": {"logType": "info", "children": [], "durationId": "01823840-7d4e-4dde-8b9b-113de1f83e97"}}, {"head": {"id": "388bc3a8-9953-4f09-b549-e439ff845908", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931360700, "endTime": 32715931955700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c3448f1c-1082-490f-80f0-b64b6a5f51d2", "logId": "c0ad76c6-3425-4f5f-bb4b-3947f20a1d85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3448f1c-1082-490f-80f0-b64b6a5f51d2", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715930462700}, "additional": {"logType": "detail", "children": [], "durationId": "388bc3a8-9953-4f09-b549-e439ff845908"}}, {"head": {"id": "94913d21-a1a7-49fb-b218-909ad127ecfc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715930884100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d1e09f-5151-4c59-95d5-77ec52507ce6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931007300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70e99cde-3410-433b-9501-82a8d30bfc17", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931374900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0342dee-0bef-4c8d-a13d-c21ee8df49f5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931511200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "905730fc-dbd3-4fff-b59c-43052a57a238", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931569600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59888761-932d-403c-bf1a-9e23fef5fabc", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931751400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab7d3da9-a7ce-42da-a6f2-d375b348e489", "name": "runTaskFromQueue task cost before running: 421 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931866300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0ad76c6-3425-4f5f-bb4b-3947f20a1d85", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715931360700, "endTime": 32715931955700, "totalTime": 468400}, "additional": {"logType": "info", "children": [], "durationId": "388bc3a8-9953-4f09-b549-e439ff845908"}}, {"head": {"id": "80752c87-6b00-4461-ac36-e0bc0b642455", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715935369300, "endTime": 32715938191000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "97acc011-52c0-4c96-95ec-057ea93145fe", "logId": "bd070b4e-d455-4027-a011-80f6401ab971"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97acc011-52c0-4c96-95ec-057ea93145fe", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715933862400}, "additional": {"logType": "detail", "children": [], "durationId": "80752c87-6b00-4461-ac36-e0bc0b642455"}}, {"head": {"id": "bd1af7ba-9082-4cf7-b796-e141a6fd562c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715934357900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb893c91-c43d-441e-8a51-24d124029caf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715934473200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80ad8571-2102-4ebb-98d6-9e09af2653f8", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715935380000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6426499-b2ca-43fe-ae59-16a983388ab0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715937978700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05ba25cd-15a0-46cc-9cf6-747d0b3b3693", "name": "entry : default@MergeProfile cost memory 0.1051025390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715938118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd070b4e-d455-4027-a011-80f6401ab971", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715935369300, "endTime": 32715938191000}, "additional": {"logType": "info", "children": [], "durationId": "80752c87-6b00-4461-ac36-e0bc0b642455"}}, {"head": {"id": "a1e40813-166b-4ef4-afc2-2f88bfe937b2", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715942367100, "endTime": 32715945038900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "99a8881f-8333-4615-b0a5-27f368e98f6e", "logId": "d6fd4e2c-89cc-4b02-8f6e-1f0013992e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99a8881f-8333-4615-b0a5-27f368e98f6e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715939894900}, "additional": {"logType": "detail", "children": [], "durationId": "a1e40813-166b-4ef4-afc2-2f88bfe937b2"}}, {"head": {"id": "70e03f96-3520-4a3c-a223-ed4ab1e33d07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715940305800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "352011b6-8793-4d5d-b750-5dc1b741a508", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715940428900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a2a877c-0d00-400f-923d-f079bb75a34a", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715942383300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e4ebc6-02c0-41dd-ba6d-8a506dc7da59", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715943502100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d4ee9f0-2ea5-4652-94d3-a332f8ece641", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715944795400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87843efa-ec23-453f-90ba-6ce3d2165997", "name": "entry : default@CreateBuildProfile cost memory 0.1016845703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715944955600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6fd4e2c-89cc-4b02-8f6e-1f0013992e35", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715942367100, "endTime": 32715945038900}, "additional": {"logType": "info", "children": [], "durationId": "a1e40813-166b-4ef4-afc2-2f88bfe937b2"}}, {"head": {"id": "1aa9603b-b5be-4399-86a2-7ad88bc68857", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715950605100, "endTime": 32715952126100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "861f99cc-aa8d-4160-ad84-98f6a2f87202", "logId": "657cd5f6-138f-420e-a9e0-4ad971556cdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "861f99cc-aa8d-4160-ad84-98f6a2f87202", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715948824100}, "additional": {"logType": "detail", "children": [], "durationId": "1aa9603b-b5be-4399-86a2-7ad88bc68857"}}, {"head": {"id": "23eb96f5-dbd8-4502-837b-3687e0e006e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715949230700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b30dc2-0b69-459a-ac77-60c8085c3b97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715949345000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49b583de-e125-4842-b422-5fef6ac3949b", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715951177900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07208b53-166b-4351-8a39-818825aa0ff1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715951705600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5508b431-724e-475e-a467-069318926b3d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715951839500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71655fe1-f659-4989-9bfa-3fdac5616a10", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715951948000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3732ead3-ade3-49fe-bff2-ead61cdcee9a", "name": "runTaskFromQueue task cost before running: 441 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715952057300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "657cd5f6-138f-420e-a9e0-4ad971556cdb", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715950605100, "endTime": 32715952126100, "totalTime": 1426900}, "additional": {"logType": "info", "children": [], "durationId": "1aa9603b-b5be-4399-86a2-7ad88bc68857"}}, {"head": {"id": "fb515e62-58e4-4172-96cb-2084aa03961c", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715958312700, "endTime": 32715959047100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fa028ed5-9510-4d62-86e7-d86f934de149", "logId": "69551c46-0281-46e9-bac1-4b2cdbb8fb53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa028ed5-9510-4d62-86e7-d86f934de149", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715954086600}, "additional": {"logType": "detail", "children": [], "durationId": "fb515e62-58e4-4172-96cb-2084aa03961c"}}, {"head": {"id": "78202158-642e-4e5f-a4e5-786d2daf9801", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715954526200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada4a354-7f9b-4b6b-b787-29b7e0b8f59e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715954708700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18d069ef-83c6-48f8-b6bb-a04cd4d9ab87", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715958324700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b4d4f2-04ed-4a54-9e86-4eff5307b971", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715958548200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f436629c-0ef9-43ff-b3d9-2804f453925a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0382843017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715958860100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa9d016d-cef4-4885-8113-ada201fa0828", "name": "runTaskFromQueue task cost before running: 448 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715958981800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69551c46-0281-46e9-bac1-4b2cdbb8fb53", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715958312700, "endTime": 32715959047100, "totalTime": 646300}, "additional": {"logType": "info", "children": [], "durationId": "fb515e62-58e4-4172-96cb-2084aa03961c"}}, {"head": {"id": "04576eab-e3d5-40ab-85da-15b31044895e", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715963580600, "endTime": 32715965910500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "810c5d45-ba34-4b49-8d00-a48d8098b3d6", "logId": "6fa7027f-49ba-4ebd-8d9d-e9ea7c20c16b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "810c5d45-ba34-4b49-8d00-a48d8098b3d6", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715961080800}, "additional": {"logType": "detail", "children": [], "durationId": "04576eab-e3d5-40ab-85da-15b31044895e"}}, {"head": {"id": "3f8b58f3-5eb5-411b-a57a-e9c8d3a9d91b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715961456200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db391ceb-7997-4bcd-89c5-d57e64d5ce18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715961561200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62cb5eb2-2670-48d3-ad56-9f13c2ee91c2", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715963595100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "586608e5-4547-42b8-92b4-c0ac0ddfdc15", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715965270300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9000f3-e7da-4c28-84a9-90f05b50b205", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715965418600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "907039ff-e128-4747-8587-199d5decd66a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715965517700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d0b1883-a0f0-425a-b1d7-feae68265e69", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715965571900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d2ebabd-c8c3-4d3e-8ceb-cb8c1d60d9ac", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11786651611328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715965721000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dcda5cf-0b51-4957-a297-056567e7c56d", "name": "runTaskFromQueue task cost before running: 455 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715965826300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa7027f-49ba-4ebd-8d9d-e9ea7c20c16b", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715963580600, "endTime": 32715965910500, "totalTime": 2227400}, "additional": {"logType": "info", "children": [], "durationId": "04576eab-e3d5-40ab-85da-15b31044895e"}}, {"head": {"id": "4f6bf33a-ae2f-44e9-b3fa-5a9cf24d4064", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970304000, "endTime": 32715970807400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d765abc0-691c-41e7-9528-030046c4df77", "logId": "2d2697af-02fc-4b83-bfe6-2cb19def0e5a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d765abc0-691c-41e7-9528-030046c4df77", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715968860400}, "additional": {"logType": "detail", "children": [], "durationId": "4f6bf33a-ae2f-44e9-b3fa-5a9cf24d4064"}}, {"head": {"id": "cb964edd-ec4e-47ec-981e-f8e7dc64d4c0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715969438100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1340a62-4ddf-4410-8cd5-cccd9c6abf97", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715969558200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12181d79-ed71-4952-81b2-987c6c13854b", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970318200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3508e53f-2b1b-448a-a773-b13c41d3f23a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970455100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a6c9c5-93f9-44bd-834f-0ac41360a4cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970517900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36479a98-8c05-42dc-96b9-6764ca8d3aa0", "name": "entry : default@BuildNativeWithCmake cost memory 0.037078857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970597800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb96ac60-f33d-489c-938e-a29738f06cc8", "name": "runTaskFromQueue task cost before running: 460 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970721700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2697af-02fc-4b83-bfe6-2cb19def0e5a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715970304000, "endTime": 32715970807400, "totalTime": 395900}, "additional": {"logType": "info", "children": [], "durationId": "4f6bf33a-ae2f-44e9-b3fa-5a9cf24d4064"}}, {"head": {"id": "58612e89-761d-4fc9-8230-455366e41d9a", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715974158300, "endTime": 32715977316000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e79aacd8-f307-46a7-96ac-2fca013407ef", "logId": "74a7676b-04ca-4ea4-aef0-ac1257f16756"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e79aacd8-f307-46a7-96ac-2fca013407ef", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715972844100}, "additional": {"logType": "detail", "children": [], "durationId": "58612e89-761d-4fc9-8230-455366e41d9a"}}, {"head": {"id": "632e068c-8ba0-40e7-8020-8eed6de36196", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715973302500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e29a22b-3333-4e9b-8dd4-aa332e4f385e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715973414300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5919a485-0660-4a1a-a0c0-e7bae6a5e86c", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715974171900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6c71c2c-e449-4ac0-b8b5-d1da32366f6a", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715977035700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8aab9ec-9c73-4eaa-86e2-970e803f3e37", "name": "entry : default@MakePackInfo cost memory 0.1386871337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715977227600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74a7676b-04ca-4ea4-aef0-ac1257f16756", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715974158300, "endTime": 32715977316000}, "additional": {"logType": "info", "children": [], "durationId": "58612e89-761d-4fc9-8230-455366e41d9a"}}, {"head": {"id": "914f685e-ce87-46d6-86fe-6363b194ca67", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715983652600, "endTime": 32715987663600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "f34bfced-5103-424a-8259-95a0d807b4a2", "logId": "a127deb2-3e5e-441e-b14a-62b3b71c1860"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f34bfced-5103-424a-8259-95a0d807b4a2", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715980473900}, "additional": {"logType": "detail", "children": [], "durationId": "914f685e-ce87-46d6-86fe-6363b194ca67"}}, {"head": {"id": "b0f673e3-424d-4ba2-9783-28c3c25afd78", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715981266400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07cee233-f2cc-481e-b474-42a7c5f1c598", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715981523200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13a16212-00a9-417d-ae59-dc8808bf3b98", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715983667900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4e894e9-fc75-49f6-aac9-d8089cfd5e5b", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715983906600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac70ed0d-98b6-44c4-be04-d49662e08b17", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715984758300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d7bf9b5-d99a-4d8c-893f-3a0e28cbb155", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715986580800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46bf4045-1d84-4deb-808a-22ccd5a86fc8", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715987240200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8e614a5-1035-4a89-9104-11345e42dab5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715987380900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a43d67d-5dcd-4280-bbf1-1b101472bcdf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715987443800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490e522c-8438-4933-b3e2-06fc6edeb1bc", "name": "entry : default@SyscapTransform cost memory 0.15102386474609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715987527100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5198924c-41ae-4341-971e-57bcf4d3f8c0", "name": "runTaskFromQueue task cost before running: 477 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715987602400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a127deb2-3e5e-441e-b14a-62b3b71c1860", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715983652600, "endTime": 32715987663600, "totalTime": 3936000}, "additional": {"logType": "info", "children": [], "durationId": "914f685e-ce87-46d6-86fe-6363b194ca67"}}, {"head": {"id": "55bac8dd-79f6-42ce-9034-b4ecdd1437fc", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715991895200, "endTime": 32715992947600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "8bca10f3-f90d-48d7-a470-9ce0720ca656", "logId": "0cb07316-2994-41c2-b69d-e026d04dacc5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8bca10f3-f90d-48d7-a470-9ce0720ca656", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715989688700}, "additional": {"logType": "detail", "children": [], "durationId": "55bac8dd-79f6-42ce-9034-b4ecdd1437fc"}}, {"head": {"id": "1fdf4200-7c66-4a22-82d2-070c3a567774", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715990271800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87d25b0-5b40-4f5f-92a5-813c6742252d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715990390900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14610ec0-4610-4472-a74b-e5bc101a28dd", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715991907100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0365c913-d62b-41e2-8bb6-49a724108497", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715992769900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2df7dddb-3ada-49ea-9680-0cbe2da7c339", "name": "entry : default@ProcessProfile cost memory 0.05956268310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715992881100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cb07316-2994-41c2-b69d-e026d04dacc5", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715991895200, "endTime": 32715992947600}, "additional": {"logType": "info", "children": [], "durationId": "55bac8dd-79f6-42ce-9034-b4ecdd1437fc"}}, {"head": {"id": "d4224519-2048-43b0-9584-be6f80591b54", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715997458600, "endTime": 32716005713400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ab776975-88a8-4b86-b658-bf1e2e501aba", "logId": "8963fbac-0476-464e-b932-9f43e9c3f88c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab776975-88a8-4b86-b658-bf1e2e501aba", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715995336500}, "additional": {"logType": "detail", "children": [], "durationId": "d4224519-2048-43b0-9584-be6f80591b54"}}, {"head": {"id": "bff6b846-bd7c-4f36-b20c-b1bba8623635", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715995715900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ce573b-3e5b-4a25-a8fe-779f8e13be80", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715995822900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2240d1f-742d-406b-b71f-bf5fcf4d1404", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715997471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218c8710-ffb8-4afa-b983-e8a718198d02", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716005102100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbc701d-12df-47d7-854a-b80347610b3b", "name": "entry : default@ProcessRouterMap cost memory -5.297737121582031", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716005622900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8963fbac-0476-464e-b932-9f43e9c3f88c", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715997458600, "endTime": 32716005713400}, "additional": {"logType": "info", "children": [], "durationId": "d4224519-2048-43b0-9584-be6f80591b54"}}, {"head": {"id": "1b1f5971-5f5b-4fec-b693-48aa9d6d7d85", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716010561600, "endTime": 32716011666600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9fe0844f-428a-4f8f-93ce-826986b6423e", "logId": "7859ab49-3af4-48a0-abdc-e62f5686231f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fe0844f-428a-4f8f-93ce-826986b6423e", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716008411600}, "additional": {"logType": "detail", "children": [], "durationId": "1b1f5971-5f5b-4fec-b693-48aa9d6d7d85"}}, {"head": {"id": "86544a5e-2465-4327-8f85-a1259a586706", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716009504700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa237518-c1b0-454d-9cf1-dbba700ee961", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716009679900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378b7f66-cb5f-47c5-8c4c-6555568af06e", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716010572800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c65ab1dc-84c5-41e0-bd6f-fba85fd149dd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716010721200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f503962-a57a-4ee2-b738-9826d0fb1477", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716010778500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb4dc60-baff-4bf5-8e95-4d9fa05a416f", "name": "entry : default@BuildNativeWithNinja cost memory 0.056884765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716011481100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a042ae0d-3512-4ab8-8dac-92aad53e2aca", "name": "runTaskFromQueue task cost before running: 501 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716011600800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7859ab49-3af4-48a0-abdc-e62f5686231f", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716010561600, "endTime": 32716011666600, "totalTime": 1018800}, "additional": {"logType": "info", "children": [], "durationId": "1b1f5971-5f5b-4fec-b693-48aa9d6d7d85"}}, {"head": {"id": "f3419c92-9676-4965-82cb-96849426bf00", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716016104900, "endTime": 32716021328500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c25bd2b7-0139-4bf5-95e2-5537ae12e68e", "logId": "a2225baf-a0db-47b5-9ee5-39442195695d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c25bd2b7-0139-4bf5-95e2-5537ae12e68e", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716013917200}, "additional": {"logType": "detail", "children": [], "durationId": "f3419c92-9676-4965-82cb-96849426bf00"}}, {"head": {"id": "ac1e8e66-8645-4275-baab-b10b183a38ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716014353500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e655348a-ce19-430f-b848-34cdbb330513", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716014454300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac7d1e6d-75c3-4892-8427-a05398dd0a51", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716015191700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10094d03-e02a-4b8f-9ae9-9bb72971d31e", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716017471500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8bc1385-c96a-4f89-8203-3ad38e6aef90", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716019404000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "508034d7-4f65-42bf-97b0-dfedbb51d605", "name": "entry : default@ProcessResource cost memory 0.1692047119140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716019576700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2225baf-a0db-47b5-9ee5-39442195695d", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716016104900, "endTime": 32716021328500}, "additional": {"logType": "info", "children": [], "durationId": "f3419c92-9676-4965-82cb-96849426bf00"}}, {"head": {"id": "623e1939-f7a7-4c88-b117-6ebd29e0ad08", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716029213500, "endTime": 32716047338900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f88c2356-009f-4546-bb74-c951e131e2ec", "logId": "9257685f-bfb8-422f-b4e3-d1a6cd9f5161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f88c2356-009f-4546-bb74-c951e131e2ec", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716025366800}, "additional": {"logType": "detail", "children": [], "durationId": "623e1939-f7a7-4c88-b117-6ebd29e0ad08"}}, {"head": {"id": "810a41ab-7934-45ed-89fc-27ae838405d6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716025915500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ded1170b-82b6-4098-b076-33788128d8c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716026027200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b26ccae8-2cdc-44f7-b3d0-46a875f6531f", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716029226800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49c23d7e-e127-4eac-86f5-355e806c77f9", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716047135000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca309a67-0bce-49e6-b29a-223aeb6d8576", "name": "entry : default@GenerateLoaderJson cost memory 0.7632598876953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716047270200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9257685f-bfb8-422f-b4e3-d1a6cd9f5161", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716029213500, "endTime": 32716047338900}, "additional": {"logType": "info", "children": [], "durationId": "623e1939-f7a7-4c88-b117-6ebd29e0ad08"}}, {"head": {"id": "adc36a25-060d-4839-94be-ef839ac93617", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716054233400, "endTime": 32716057352900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "e5585e96-4db4-4ee4-94d8-50610f4d9f15", "logId": "64dca8a5-f07b-485c-a516-c7bf5011eb1d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5585e96-4db4-4ee4-94d8-50610f4d9f15", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716053187300}, "additional": {"logType": "detail", "children": [], "durationId": "adc36a25-060d-4839-94be-ef839ac93617"}}, {"head": {"id": "7d2c64a0-6b53-4fcb-bee1-0bdaca9c785e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716053513800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23d83d12-de77-4634-82dd-aa3d0b0b0826", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716053644900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "152798b1-fda9-4997-be54-384498ad0cf1", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716054244600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a21f707-281d-4629-8059-6584b8aa6328", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716056265800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ca9f402-cf8d-4178-9719-7dd2675dc963", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716056373900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a60077cb-07dc-4045-ace0-4ed77f4d845c", "name": "entry : default@ProcessLibs cost memory 0.12558746337890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716057167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c863a54b-6da1-4768-9dcb-fdab11a0d0dc", "name": "runTaskFromQueue task cost before running: 546 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716057288800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64dca8a5-f07b-485c-a516-c7bf5011eb1d", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716054233400, "endTime": 32716057352900, "totalTime": 3033300}, "additional": {"logType": "info", "children": [], "durationId": "adc36a25-060d-4839-94be-ef839ac93617"}}, {"head": {"id": "1f77a2e4-9e98-414f-8a49-78d0b51ff785", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716063046200, "endTime": 32716084097700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "344d080c-d78f-4aba-a283-40e0288e96b9", "logId": "a0ee2f98-8891-4060-ab3c-da32edfc4976"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "344d080c-d78f-4aba-a283-40e0288e96b9", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716059368500}, "additional": {"logType": "detail", "children": [], "durationId": "1f77a2e4-9e98-414f-8a49-78d0b51ff785"}}, {"head": {"id": "06d1624e-e113-44ce-a1ab-6a5d33f76fef", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716059709500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91f30379-a173-42dc-b27b-565c6c9d334b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716059805600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91f13cb-fce1-4c85-ad3b-3de0cdf4b670", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716060536100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677d8fe7-522b-43a9-b977-9b6492f9078f", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716063070300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a62fb4ad-3ed6-43e9-97cd-ca87a2f20fde", "name": "Incremental task entry:default@CompileResource pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716083765400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23c3b239-aafe-46ce-94cd-1350dfd5de63", "name": "entry : default@CompileResource cost memory 1.4109878540039062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716083981600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ee2f98-8891-4060-ab3c-da32edfc4976", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716063046200, "endTime": 32716084097700}, "additional": {"logType": "info", "children": [], "durationId": "1f77a2e4-9e98-414f-8a49-78d0b51ff785"}}, {"head": {"id": "048b6a27-5b0c-41c8-94cf-e12cf5e00cc9", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716090589100, "endTime": 32716091992700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "326451e0-c7d8-4968-8602-e28f8c3b389c", "logId": "aa3c9193-29b6-41e4-a0b4-b1861afd3b3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "326451e0-c7d8-4968-8602-e28f8c3b389c", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716087308400}, "additional": {"logType": "detail", "children": [], "durationId": "048b6a27-5b0c-41c8-94cf-e12cf5e00cc9"}}, {"head": {"id": "038edec3-21a2-4b6e-8b3c-af54aaaf0fee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716088205500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ac5b43-d375-4896-ae21-b8f656f8d4a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716088329100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "affe5b1a-986d-4d69-8a75-c001f8a68152", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716090598900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5e0d14-5d67-492c-a328-252ad02010a6", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716090932400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ac3f18d-388a-465a-b621-7678e7fb0f1d", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716091836700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6740835-f0dd-41d3-b142-7db307cd9296", "name": "entry : default@DoNativeStrip cost memory 0.074432373046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716091928400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa3c9193-29b6-41e4-a0b4-b1861afd3b3d", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716090589100, "endTime": 32716091992700}, "additional": {"logType": "info", "children": [], "durationId": "048b6a27-5b0c-41c8-94cf-e12cf5e00cc9"}}, {"head": {"id": "86dbf900-ea39-4093-9ce7-9f251be10aac", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716097984900, "endTime": 32718417400800}, "additional": {"children": ["d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "d94895dd-7da2-4530-aded-fc8fe70b1a6b"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "abb62c07-f7aa-4f94-b0f4-9a0ab7aabeb5", "logId": "b58627b4-8ff6-4833-b091-108ca4baf9f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abb62c07-f7aa-4f94-b0f4-9a0ab7aabeb5", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716093579300}, "additional": {"logType": "detail", "children": [], "durationId": "86dbf900-ea39-4093-9ce7-9f251be10aac"}}, {"head": {"id": "2c811104-b9dd-45ed-ab96-76b2c555f397", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716093967100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf78727-b97f-47ab-abab-e2f86cd5097f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716094064800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a045335-41f0-46e1-b9f0-6d40e70c920d", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716097997600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663686ac-3ac0-4bbf-a111-1bb67fac0d87", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716113813200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9d53b0e-08a6-439f-b908-62da6cb28668", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": **********4300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "911fa40b-324a-47c5-a033-07f9e15c3056", "name": "default@CompileArkTS work[88] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716114984900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32716335068200, "endTime": 32718409192800}, "additional": {"children": ["23f6cab8-913d-4176-b92f-ec3198edad50", "863f7066-7eb8-4b3f-a4b9-e9813c08aaf4", "c5082ec9-06bb-47b2-9f89-2bf9946e4cbc", "96e12a69-3d1c-42e1-88ae-18e05b8c892f", "c3a85484-1717-4405-a778-dd0c8272ec9f", "79b3f54b-771e-433c-b0f0-fa55be505f00"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "86dbf900-ea39-4093-9ce7-9f251be10aac", "logId": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cc06eed-a459-452a-bc6e-f20292950110", "name": "default@CompileArkTS work[88] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716115761900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f53b8b1-7507-4f5a-aaf1-c2251295c3c0", "name": "default@CompileArkTS work[88] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716115860500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c704ffaa-74cb-4c7d-a086-26c2d4dabd66", "name": "CopyResources startTime: 32716115917600", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716115919300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304950fd-e25e-4a47-ae38-65eea2e41ac8", "name": "default@CompileArkTS work[89] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716115974600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d94895dd-7da2-4530-aded-fc8fe70b1a6b", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32717794883500, "endTime": 32717813524200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "86dbf900-ea39-4093-9ce7-9f251be10aac", "logId": "46778e2c-aea9-47b7-8b01-772824031b77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d68949dd-1d64-462f-9d90-b97f7913323a", "name": "default@CompileArkTS work[89] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716116536400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9b4f61e-db46-4272-b35c-a350c2dcc0f6", "name": "default@CompileArkTS work[89] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716116645400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d21ad0-7496-4a26-b5a0-e4935ee87ac1", "name": "entry : default@CompileArkTS cost memory 1.1825637817382812", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716116804100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bbb81fd-3888-457e-bce9-3d7018699a2a", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716124370400, "endTime": 32716127010300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "2323b134-95d7-444d-b2aa-dc0811c5a2d6", "logId": "cf93cf25-3cf4-45d5-acab-972616b759e7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2323b134-95d7-444d-b2aa-dc0811c5a2d6", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716120453800}, "additional": {"logType": "detail", "children": [], "durationId": "5bbb81fd-3888-457e-bce9-3d7018699a2a"}}, {"head": {"id": "e4020ccb-76fd-4c64-a3bd-4c50082aed42", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716120999100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26cfcaa2-6930-44ff-b3e5-4f4960e423e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716121111000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "515cbd35-bbd9-4e21-8905-fef3719ca501", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716124379900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f08526d-95d9-4255-b364-859213a25ecc", "name": "entry : default@BuildJS cost memory 0.1268310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716126821400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be83e280-5d3e-4ada-bedf-c444ab47c4c6", "name": "runTaskFromQueue task cost before running: 616 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716126950900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf93cf25-3cf4-45d5-acab-972616b759e7", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716124370400, "endTime": 32716127010300, "totalTime": 2560600}, "additional": {"logType": "info", "children": [], "durationId": "5bbb81fd-3888-457e-bce9-3d7018699a2a"}}, {"head": {"id": "8a5964d7-2384-4274-bd38-4df5ab1e9491", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716132433500, "endTime": 32716135148500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9724fed6-98f9-45df-b5be-3741dbfe504d", "logId": "f48d9285-3b8b-4a56-b493-1c1bd7712937"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9724fed6-98f9-45df-b5be-3741dbfe504d", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716128964900}, "additional": {"logType": "detail", "children": [], "durationId": "8a5964d7-2384-4274-bd38-4df5ab1e9491"}}, {"head": {"id": "3e42b126-4f4e-4032-aaab-68c20f24661d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716129378100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "479edea4-c5ca-45fa-9768-a8bdbfa4874d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716129478900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55722fd4-5f31-4cf8-aff1-1c2c4663bd7b", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716132445500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4285657-0005-4f0b-b56f-90c578bb73df", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716133325500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82c05d87-ecca-4561-8fc1-6a5a9a92c40c", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716134955000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e18d9144-d6d2-49c3-be91-98f0eae6f188", "name": "entry : default@CacheNativeLibs cost memory 0.0886077880859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716135077400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f48d9285-3b8b-4a56-b493-1c1bd7712937", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716132433500, "endTime": 32716135148500}, "additional": {"logType": "info", "children": [], "durationId": "8a5964d7-2384-4274-bd38-4df5ab1e9491"}}, {"head": {"id": "d6d070bc-529a-4168-97a1-cccf97ab6527", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716334558100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f056d293-7ed4-44a2-80f1-b21b8865c281", "name": "default@CompileArkTS work[88] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716334886100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b3b1409-858d-4d13-957a-2c1e3c103bd8", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716334974400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c45c212-ac53-4013-9af7-655e2c2de40d", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716335047200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d83c9a1f-5bc2-4aac-991b-4b57d5a3a7a3", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716335345800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c23f3709-ed33-4bb6-8cc0-dc109ea7cef0", "name": "default@CompileArkTS work[89] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716336746100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14b0768d-97e1-450a-a0af-7ef56d1a3ec0", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32717814448200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e79f824-2802-4803-bf21-f00cc85ebd6e", "name": "CopyResources is end, endTime: 32717815024000", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32717815031200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7758657-a63e-4deb-ae47-395828584302", "name": "default@CompileArkTS work[89] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32717815336000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46778e2c-aea9-47b7-8b01-772824031b77", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 32717794883500, "endTime": 32717813524200}, "additional": {"logType": "info", "children": [], "durationId": "d94895dd-7da2-4530-aded-fc8fe70b1a6b", "parent": "b58627b4-8ff6-4833-b091-108ca4baf9f9"}}, {"head": {"id": "be715e71-c816-4b87-8c7b-0bd6507fedaf", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718039764000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d7b6d6b-8e6e-4f4b-83eb-5f1ba6db62d6", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718409587000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23f6cab8-913d-4176-b92f-ec3198edad50", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32716335246300, "endTime": 32716339383700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "logId": "13a7a035-ef2c-40ee-975c-47a05fe927ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13a7a035-ef2c-40ee-975c-47a05fe927ab", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716335246300, "endTime": 32716339383700}, "additional": {"logType": "info", "children": [], "durationId": "23f6cab8-913d-4176-b92f-ec3198edad50", "parent": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}}, {"head": {"id": "863f7066-7eb8-4b3f-a4b9-e9813c08aaf4", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32716339400900, "endTime": 32716339551100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "logId": "8dce8018-87f3-4ebb-b5dd-03e7d131eb49"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dce8018-87f3-4ebb-b5dd-03e7d131eb49", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716339400900, "endTime": 32716339551100}, "additional": {"logType": "info", "children": [], "durationId": "863f7066-7eb8-4b3f-a4b9-e9813c08aaf4", "parent": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}}, {"head": {"id": "c5082ec9-06bb-47b2-9f89-2bf9946e4cbc", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32716339564300, "endTime": 32716339595100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "logId": "b27b5414-a8e2-4c3a-a901-ba2e8959d75f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b27b5414-a8e2-4c3a-a901-ba2e8959d75f", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716339564300, "endTime": 32716339595100}, "additional": {"logType": "info", "children": [], "durationId": "c5082ec9-06bb-47b2-9f89-2bf9946e4cbc", "parent": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}}, {"head": {"id": "96e12a69-3d1c-42e1-88ae-18e05b8c892f", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32716339614000, "endTime": 32718303587900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "logId": "7b5b6c71-5d6d-4676-af17-a60689f58d4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b5b6c71-5d6d-4676-af17-a60689f58d4c", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716339614000, "endTime": 32718303587900}, "additional": {"logType": "info", "children": [], "durationId": "96e12a69-3d1c-42e1-88ae-18e05b8c892f", "parent": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}}, {"head": {"id": "c3a85484-1717-4405-a778-dd0c8272ec9f", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718303612400, "endTime": 32718312520900}, "additional": {"children": ["492818b4-e931-48ad-b478-18da1dbc6e2a", "628a4dee-bbcc-470e-a377-479cd1eef2b8", "c27881eb-24b7-4bfb-a5ad-ff701720bf90"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "logId": "820550ab-ef98-4f07-864c-0db05b6fd77b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "820550ab-ef98-4f07-864c-0db05b6fd77b", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718303612400, "endTime": 32718312520900}, "additional": {"logType": "info", "children": ["bc17783d-be2a-47ed-b85d-903aa4be2cbb", "5f7fb286-d0d5-4eec-b1c6-38c8c7155ace", "73fc29d1-9efe-4bb6-8b3d-b348ab2a756c"], "durationId": "c3a85484-1717-4405-a778-dd0c8272ec9f", "parent": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}}, {"head": {"id": "492818b4-e931-48ad-b478-18da1dbc6e2a", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718303633700, "endTime": 32718303641500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3a85484-1717-4405-a778-dd0c8272ec9f", "logId": "bc17783d-be2a-47ed-b85d-903aa4be2cbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bc17783d-be2a-47ed-b85d-903aa4be2cbb", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718303633700, "endTime": 32718303641500}, "additional": {"logType": "info", "children": [], "durationId": "492818b4-e931-48ad-b478-18da1dbc6e2a", "parent": "820550ab-ef98-4f07-864c-0db05b6fd77b"}}, {"head": {"id": "628a4dee-bbcc-470e-a377-479cd1eef2b8", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718303645000, "endTime": 32718309383600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3a85484-1717-4405-a778-dd0c8272ec9f", "logId": "5f7fb286-d0d5-4eec-b1c6-38c8c7155ace"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5f7fb286-d0d5-4eec-b1c6-38c8c7155ace", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718303645000, "endTime": 32718309383600}, "additional": {"logType": "info", "children": [], "durationId": "628a4dee-bbcc-470e-a377-479cd1eef2b8", "parent": "820550ab-ef98-4f07-864c-0db05b6fd77b"}}, {"head": {"id": "c27881eb-24b7-4bfb-a5ad-ff701720bf90", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718309387600, "endTime": 32718312509400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "c3a85484-1717-4405-a778-dd0c8272ec9f", "logId": "73fc29d1-9efe-4bb6-8b3d-b348ab2a756c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73fc29d1-9efe-4bb6-8b3d-b348ab2a756c", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718309387600, "endTime": 32718312509400}, "additional": {"logType": "info", "children": [], "durationId": "c27881eb-24b7-4bfb-a5ad-ff701720bf90", "parent": "820550ab-ef98-4f07-864c-0db05b6fd77b"}}, {"head": {"id": "79b3f54b-771e-433c-b0f0-fa55be505f00", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718312533900, "endTime": 32718408847100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "logId": "03d375cf-f3bb-411a-9d50-c1473caf8766"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03d375cf-f3bb-411a-9d50-c1473caf8766", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718312533900, "endTime": 32718408847100}, "additional": {"logType": "info", "children": [], "durationId": "79b3f54b-771e-433c-b0f0-fa55be505f00", "parent": "8f2bf105-06a0-4012-9b54-b468bc7f57cd"}}, {"head": {"id": "456b5d48-2b29-47d3-b117-c0f48e5b06d6", "name": "default@CompileArkTS work[88] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718417021400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f2bf105-06a0-4012-9b54-b468bc7f57cd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32716335068200, "endTime": 32718409192800}, "additional": {"logType": "info", "children": ["13a7a035-ef2c-40ee-975c-47a05fe927ab", "8dce8018-87f3-4ebb-b5dd-03e7d131eb49", "b27b5414-a8e2-4c3a-a901-ba2e8959d75f", "7b5b6c71-5d6d-4676-af17-a60689f58d4c", "820550ab-ef98-4f07-864c-0db05b6fd77b", "03d375cf-f3bb-411a-9d50-c1473caf8766"], "durationId": "d188d153-bacd-4bf0-bfc0-b0f1af1d3a10", "parent": "b58627b4-8ff6-4833-b091-108ca4baf9f9"}}, {"head": {"id": "bcaba65e-b54b-4aba-866a-66ec615ca513", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718417289200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58627b4-8ff6-4833-b091-108ca4baf9f9", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32716097984900, "endTime": 32718417400800, "totalTime": 2093014000}, "additional": {"logType": "info", "children": ["8f2bf105-06a0-4012-9b54-b468bc7f57cd", "46778e2c-aea9-47b7-8b01-772824031b77"], "durationId": "86dbf900-ea39-4093-9ce7-9f251be10aac"}}, {"head": {"id": "2a61001b-6ff7-4436-a821-b9f546519a53", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718423744100, "endTime": 32718425096400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "835a5d96-d6ef-43d3-b88a-46e33c450f17", "logId": "bf6fdc53-66e1-4c03-9038-e36b511c198c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "835a5d96-d6ef-43d3-b88a-46e33c450f17", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718422034700}, "additional": {"logType": "detail", "children": [], "durationId": "2a61001b-6ff7-4436-a821-b9f546519a53"}}, {"head": {"id": "e9404715-1a52-4f54-afdb-0154369ee864", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718422457700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8f9454-45e3-4143-a320-7e1a3600b39a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718422569400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ef6cc21-2133-424b-83ae-1ba74baf212c", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718423754700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e12b8d8-28b5-4662-8bc9-2358dcb17f57", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718424043600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24591d41-3f37-4545-afda-f0a8b9d542d6", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718424903900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef7c1895-0927-44aa-a302-f778aaa6d143", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07288360595703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718425025800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf6fdc53-66e1-4c03-9038-e36b511c198c", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718423744100, "endTime": 32718425096400}, "additional": {"logType": "info", "children": [], "durationId": "2a61001b-6ff7-4436-a821-b9f546519a53"}}, {"head": {"id": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718436779700, "endTime": 32718997156200}, "additional": {"children": ["ac423cba-815f-45f4-80ab-e4c32932953d", "7a7f8dfb-f450-4f23-bbdb-37591ba940c9", "bb58f59f-1f75-48ba-8e3d-1f8894651728"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "414539e2-ffd7-47a3-b4f8-21745f94a818", "logId": "e2faa4b2-7360-4995-9cc4-b61c484b3c92"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "414539e2-ffd7-47a3-b4f8-21745f94a818", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718428171900}, "additional": {"logType": "detail", "children": [], "durationId": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3"}}, {"head": {"id": "750cf946-914a-4eb8-a440-fc46ea50d84c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718428519000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba4046ad-0ea7-49d2-a2ba-99847a405039", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718428669900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82ea90d4-50a7-4ba5-a800-263cd9faeb26", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718436790400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f6a45b-50fb-49b3-ad7e-289265fcf2c8", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718455905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f131a52-359d-4880-9cdb-71262a1c91d5", "name": "Incremental task entry:default@PackageHap pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718456042200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61d613a-19e2-430b-8371-0dff8e6547a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718456134000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "071a3e13-4f65-4225-b5ef-199f4f65fc37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718456187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac423cba-815f-45f4-80ab-e4c32932953d", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718457073400, "endTime": 32718459158900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3", "logId": "29e4d298-2796-43ad-ab60-4b6b490326ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d13c08f-b32d-4bea-bbfd-aa5278f54344", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718458956400}, "additional": {"logType": "debug", "children": [], "durationId": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3"}}, {"head": {"id": "29e4d298-2796-43ad-ab60-4b6b490326ff", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718457073400, "endTime": 32718459158900}, "additional": {"logType": "info", "children": [], "durationId": "ac423cba-815f-45f4-80ab-e4c32932953d", "parent": "e2faa4b2-7360-4995-9cc4-b61c484b3c92"}}, {"head": {"id": "7a7f8dfb-f450-4f23-bbdb-37591ba940c9", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718459853400, "endTime": 32718462020900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3", "logId": "b911bb9d-d43f-46ad-beb1-a08a792996ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0798e6a-17cf-4ac5-b0e7-4d96c1b0e463", "name": "default@PackageHap work[90] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718460822700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb58f59f-1f75-48ba-8e3d-1f8894651728", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718461929100, "endTime": 32718996761800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3", "logId": "55f88f63-08d0-4f26-a4f2-67c2b5ee01ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e44e2325-c705-4478-aec2-b6919f4a0661", "name": "default@PackageHap work[90] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718461582100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41f3050-0830-4061-8d04-02823682a7e0", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718461743200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29d8fa50-94ec-4406-acef-d93e6e7811d0", "name": "default@PackageHap work[90] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718461851900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85849fb4-1e8f-4c1d-ba3d-c6cb2c5d8779", "name": "default@PackageHap work[90] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718461943800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b911bb9d-d43f-46ad-beb1-a08a792996ae", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718459853400, "endTime": 32718462020900}, "additional": {"logType": "info", "children": [], "durationId": "7a7f8dfb-f450-4f23-bbdb-37591ba940c9", "parent": "e2faa4b2-7360-4995-9cc4-b61c484b3c92"}}, {"head": {"id": "c1ea5db6-988f-43cd-b4f3-0f39b01c05ea", "name": "entry : default@PackageHap cost memory 1.276519775390625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718472250400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82eb1af-523b-4687-aaad-8e2bb5235e98", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718996836600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "399702da-b214-46f6-9807-edb01ca786c6", "name": "default@PackageHap work[90] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718997003100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f88f63-08d0-4f26-a4f2-67c2b5ee01ab", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 32718461929100, "endTime": 32718996761800}, "additional": {"logType": "info", "children": [], "durationId": "bb58f59f-1f75-48ba-8e3d-1f8894651728", "parent": "e2faa4b2-7360-4995-9cc4-b61c484b3c92"}}, {"head": {"id": "81d4c362-616f-4a8e-b650-954c160c963e", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718997085300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2faa4b2-7360-4995-9cc4-b61c484b3c92", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32718436779700, "endTime": 32718997156200, "totalTime": 559984600}, "additional": {"logType": "info", "children": ["29e4d298-2796-43ad-ab60-4b6b490326ff", "b911bb9d-d43f-46ad-beb1-a08a792996ae", "55f88f63-08d0-4f26-a4f2-67c2b5ee01ab"], "durationId": "5e498a61-f30c-4ab2-b5c3-afd0d6bd38d3"}}, {"head": {"id": "e05debac-4ac5-4b73-a42a-82c672d5a7bd", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719003369000, "endTime": 32719005025000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "60fcb510-791a-4cce-9e99-f9de0df9e5d8", "logId": "3aee0e53-df01-45b1-bb66-8d5c53dbd6c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60fcb510-791a-4cce-9e99-f9de0df9e5d8", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719000012200}, "additional": {"logType": "detail", "children": [], "durationId": "e05debac-4ac5-4b73-a42a-82c672d5a7bd"}}, {"head": {"id": "0879cf69-88c5-4b1e-bdf5-4ccfe683a210", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719000854000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fa36405-95ec-48a9-a0ce-e77eaf760c52", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719000980300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58fef91-89a4-4add-8a26-faec9a0a877a", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719003378800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c5fa1bd-51c5-40dc-a3aa-41c734cde90a", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719003719500}, "additional": {"logType": "warn", "children": [], "durationId": "e05debac-4ac5-4b73-a42a-82c672d5a7bd"}}, {"head": {"id": "e487bac1-a237-4682-8562-9650ad03757a", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719004209500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75ac0ff-8c91-4c83-8cd8-357634dd821d", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719004292500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b623e5c-365c-4abf-9fa0-92302dbfcabe", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719004367800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e7f8be7-b91f-43ba-ae52-be8630641b3f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719004418600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "723bc10a-f2e5-4ffd-a2b5-a205328e8edf", "name": "entry : default@SignHap cost memory 0.11479949951171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719004851600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ea92e03-92ac-4677-9e07-ee6545225847", "name": "runTaskFromQueue task cost before running: 3 s 494 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719004958500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aee0e53-df01-45b1-bb66-8d5c53dbd6c4", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719003369000, "endTime": 32719005025000, "totalTime": 1570400}, "additional": {"logType": "info", "children": [], "durationId": "e05debac-4ac5-4b73-a42a-82c672d5a7bd"}}, {"head": {"id": "677a337d-474c-4756-9769-7544b3b569e4", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719007823900, "endTime": 32719012173700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "cc70708c-6438-4908-acef-b65e0e84d7d2", "logId": "8f129c73-8180-4fd6-8ba9-170d13a03276"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc70708c-6438-4908-acef-b65e0e84d7d2", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719006688700}, "additional": {"logType": "detail", "children": [], "durationId": "677a337d-474c-4756-9769-7544b3b569e4"}}, {"head": {"id": "0d000160-66c2-4718-a41e-1123dad5e4b0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719007030200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe72c8fe-968d-44db-9859-fb423bb701e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719007120600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1bedf0b-5ace-488c-b84f-9ce65ea6afed", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719007831100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "640529b4-16f1-4a17-ad73-aa3d5c60eecd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719011863400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55f52534-806f-4151-8774-bf89c5ffc98d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719011952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05a96e95-623a-4e72-83a1-b6d94ad3490e", "name": "entry : default@CollectDebugSymbol cost memory 0.2395782470703125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719012040200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d35663a5-dab3-4b9c-b283-2e06aac4d101", "name": "runTaskFromQueue task cost before running: 3 s 501 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719012119100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f129c73-8180-4fd6-8ba9-170d13a03276", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719007823900, "endTime": 32719012173700, "totalTime": 4273500}, "additional": {"logType": "info", "children": [], "durationId": "677a337d-474c-4756-9769-7544b3b569e4"}}, {"head": {"id": "5d1931ae-a7dd-474f-ba72-08f6fde5ef4c", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719013718100, "endTime": 32719013994200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c070a341-8ce8-4ced-9335-da99d03f64d4", "logId": "104e6f1b-4561-495c-b096-625dda3dfee6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c070a341-8ce8-4ced-9335-da99d03f64d4", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719013679900}, "additional": {"logType": "detail", "children": [], "durationId": "5d1931ae-a7dd-474f-ba72-08f6fde5ef4c"}}, {"head": {"id": "584a4bae-d6b1-4d33-b933-37024b17424c", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719013723000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ae541ea-7ae0-4c89-b015-282815157eb1", "name": "entry : assembleHap cost memory 0.0113067626953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719013865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5398731-93d5-4903-b43a-328e1b44e191", "name": "runTaskFromQueue task cost before running: 3 s 503 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719013941100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "104e6f1b-4561-495c-b096-625dda3dfee6", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719013718100, "endTime": 32719013994200, "totalTime": 206100}, "additional": {"logType": "info", "children": [], "durationId": "5d1931ae-a7dd-474f-ba72-08f6fde5ef4c"}}, {"head": {"id": "71973a72-7993-459a-b1ee-7cc3f28cb889", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719021723000, "endTime": 32719021742800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "861a83d8-b500-4e9b-bf6b-967f0e20bf43", "logId": "271fe060-2fc9-4b4f-a154-07afb52e8a99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "271fe060-2fc9-4b4f-a154-07afb52e8a99", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719021723000, "endTime": 32719021742800}, "additional": {"logType": "info", "children": [], "durationId": "71973a72-7993-459a-b1ee-7cc3f28cb889"}}, {"head": {"id": "3299347c-f6fb-4abe-8025-fb6ac8732544", "name": "BUILD SUCCESSFUL in 3 s 511 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719021837800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "30d7550d-eb0d-4410-9e33-931c089f9fa2", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32715511415300, "endTime": 32719022080100}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 34}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "e12266f4-4f54-4510-898e-e09d09478d36", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719022109000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c240d62e-380b-4e82-96f9-8254a9227bae", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719022203200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd31d0c3-3f57-4958-8d06-db59220dd2df", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719022294700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10dcd762-498e-43e4-bcd4-9005c9cbbe59", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719022352000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2626ffc9-c9fb-4796-8025-900c8d956a4f", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719022460400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffe3bb06-6b7d-46d0-acd6-13802a9cc576", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719022891200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea72609d-47b9-4ff4-b819-9d04760ed852", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719023457900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fdd2d2c-f09f-4711-9cfc-7de7bd31d6d6", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719023674700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6687c7d-1cfd-4708-8220-89d570c10ed3", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719023744000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dce5b0d4-35eb-4954-9833-d796c3c4ff18", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719023803100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "339959e8-3248-41b0-a425-11c1282d46a5", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719024029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77dceeab-1507-4988-9590-c3807303c3c6", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719024950800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3406b9e2-49c1-4528-8d1f-a87111dc3549", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025172500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "125e91c9-340a-4095-a949-b41846d35b59", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025241900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba4e90b-86d0-4538-b5c8-3bcdff6d3a1a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025295500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96d2fa84-9db4-489c-9d7e-bf73ae0f1e3a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025345900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bd62d70-b7d0-4a1a-81f5-abb6219e2655", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025396800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8c4d585-73a5-4114-9788-50261ccbd40d", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025689400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bf80fe4-4c83-4e29-95a1-75750ec32f40", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719025919800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d14bc94d-cf57-425c-9cef-3523a4bad9c9", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719026153100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feccfd87-5dcf-4db0-aa01-23d159f80258", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719026438400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcd7efea-c295-49d2-a52b-1f059c495516", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719026508500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51098ac9-a5b6-404a-8f67-0ba7cc92d38a", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719026560700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039176c5-d67e-456e-be60-a95814bc8bd9", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719028294300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fec04cfe-dfcb-4369-ac9e-8b296c91d407", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719028837100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e09e6d90-38e9-4452-b6c5-875233ba4a1f", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719029546500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24447d1e-d0fe-413e-9a4d-098deaf30cff", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719029729000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c23a1ed-643b-451e-a68d-cbceb612a7dc", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719029896600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfae5f1f-5a1a-4d76-9a98-e028ba73d836", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719030333900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "735acaa2-f93a-41d6-99f3-6fee48eb2fc4", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719034310900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1b781d6-2ae5-4e23-977b-db9f670b8b2c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719034581800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ac26b2b-e54e-40f9-b9e2-4341abe5a29c", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719036749600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1c14a55-f501-4782-a415-40cca4932d90", "name": "Incremental task entry:default@CompileArkTS post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719037445100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "149f6c28-8f91-419d-a1ea-4dce58166786", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719039026500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5855ba4d-69d6-4f52-b52a-2b58694b1dda", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719039626300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad3a085e-49cb-4321-9a6a-37a612543539", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719040360800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72233dc2-6b47-4aa3-8793-bda5f528c891", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719040569000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dfef289-e1b2-4764-a684-1d31c8a1c84c", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719040891100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4cc0f3a-8c4a-4f02-a8fa-1c1b062ceb12", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719041419400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfd3ffb5-5847-4e22-bb5e-04c621b4fff1", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719041756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73c9d21-957a-43b8-9142-6fdf2d71f179", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719041838700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbc0d442-5ba0-4b6a-b5be-b14500e8fc4f", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719041891600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e647e1f-441c-4bfc-a230-70dd85c3121c", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719042893400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fa76b51-b2b3-4ea3-9692-d519c5fc9771", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719043278500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "252a31e4-6ae1-4282-bb5c-ac9eced17964", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719043524100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e82c371-e52d-43f3-9f37-8b50ceed9c83", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719050341600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7128002a-36ae-4232-876f-f5390b127058", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719050595400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1247943d-8954-43e1-ace0-42ff7b7f0570", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719050893500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381af7c2-8f6c-4dd1-bc8f-3016dd6bddcb", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719050978600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf7f01c-187e-405e-8390-d25e1dbf3edf", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719051172700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f7d8c94-cc4c-458a-af0f-cc0195aa017e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719051784400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "609770e3-8f9f-4dbd-984e-876e3cf89f9d", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719051981000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7bbc0f-8d98-410d-98c3-815c86180f3a", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719052224600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba0fe9a0-3a07-457a-8ed1-38620b07137b", "name": "Incremental task entry:default@PackageHap post-execution cost:11 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719052473800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82267d1d-b2a7-4c2a-8b30-5323fed14739", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719052626700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60346a85-4cc2-4ff7-b863-f916ecb5d68e", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719052780700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "257328e0-829a-4de4-947e-046da29cdb13", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719053003700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef66c90-b3d2-48e0-93e8-6b8a01f25cbb", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719054936100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "742794e2-29e6-44e1-b73b-5351dde16050", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719055169400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e992e28-365b-4d50-b3ef-892e4bebae6d", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719055393500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c44a7a9b-343d-42a5-935f-baf7066d50e3", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 32719055588800}, "additional": {"logType": "debug", "children": []}}], "workLog": []}