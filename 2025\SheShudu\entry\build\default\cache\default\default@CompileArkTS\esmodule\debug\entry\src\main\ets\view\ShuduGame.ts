if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ShuduGame_Params {
    pageInfos?: NavPathStack;
    UIContext?;
    uiAbilityContext?;
    windowClass?: window.Window;
    naviIndicatorHeight?: number;
    statusBarHeight?: number;
    settings?: RenderingContextSettings;
    context?: CanvasRenderingContext2D;
    datas0?: Array<ChessPieces>;
    datas1?: Array<ChessPieces>;
    shud?: number[][];
    cellsize?;
    wf?: componentUtils.Offset;
    chooseOne?: ChessPieces | undefined;
    usedDatas?: Array<ChessPieces>;
    modeFlag?: number;
    initFlag?: number;
    lastX?: number;
    lastY?: number;
    short?: number;
    showInstructions?: boolean;
    iconStr?: ResourceStr;
    select?: boolean;
    mavplr?: MyAvPlayer;
    musicOpen?: boolean;
    gameSatus?: number;
    difficulty?: 'easy' | 'medium' | 'hard';
    dataPreferences?: preferences.Preferences | null;
}
import hilog from "@ohos:hilog";
import type window from "@ohos:window";
import type { ComponentUtils } from "@ohos:arkui.UIContext";
import type componentUtils from "@ohos:arkui.componentUtils";
import type common from "@ohos:app.ability.common";
import preferences from "@ohos:data.preferences";
import uniformTypeDescriptor from "@ohos:data.uniformTypeDescriptor";
import type unifiedDataChannel from "@ohos:data.unifiedDataChannel";
import image from "@ohos:multimedia.image";
import { ChessPieces } from "@bundle:com.atomicservice.6917576098804821135/entry/ets/model/GameModels";
import HashMap from "@ohos:util.HashMap";
import HashSet from "@ohos:util.HashSet";
import { CommonDialog } from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/CommonDialog";
import MyAvPlayer from "@bundle:com.atomicservice.6917576098804821135/entry/ets/util/MyAvPlayer";
export function ShuduGameBuilder(name: string, param: Object, parent = null) {
    {
        (parent ? parent : this).observeComponentCreation2((elmtId, isInitialRender) => {
            if (isInitialRender) {
                let componentCall = new ShuduGame(parent ? parent : this, {}, undefined, elmtId, () => { }, { page: "entry/src/main/ets/view/ShuduGame.ets", line: 16, col: 3 });
                ViewPU.create(componentCall);
                let paramsLambda = () => {
                    return {};
                };
                componentCall.paramsGenerator_ = paramsLambda;
            }
            else {
                (parent ? parent : this).updateStateVarsOfChildByElmtId(elmtId, {});
            }
        }, { name: "ShuduGame" });
    }
}
class Tmp {
    iconStr2: ResourceStr = { "id": 16777247, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" };
    set(val: Resource) {
        this.iconStr2 = val;
    }
}
export default class ShuduGame extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.pageInfos = new NavPathStack();
        this.UIContext = this.getUIContext();
        this.uiAbilityContext = this.UIContext.getHostContext() as common.UIAbilityContext;
        this.windowClass = AppStorage.get<window.Window>('windowClass')!;
        this.naviIndicatorHeight = AppStorage.get<number>('naviIndicatorHeight')!;
        this.statusBarHeight = AppStorage.get<number>('statusBarHeight')!;
        this.settings = new RenderingContextSettings(true);
        this.context = new CanvasRenderingContext2D(this.settings);
        this.datas0 = [];
        this.datas1 = [];
        this.shud = Array(9).fill(0).map(() => Array(9).fill(0));
        this.cellsize = 0;
        this.wf = { x: 0, y: 0 };
        this.chooseOne = undefined;
        this.__usedDatas = new ObservedPropertyObjectPU([], this, "usedDatas");
        this.__modeFlag = new ObservedPropertySimplePU(1, this, "modeFlag");
        this.initFlag = 1;
        this.__lastX = new ObservedPropertySimplePU(0, this, "lastX");
        this.__lastY = new ObservedPropertySimplePU(0, this, "lastY");
        this.__short = new ObservedPropertySimplePU(0, this, "short");
        this.__showInstructions = new ObservedPropertySimplePU(true, this, "showInstructions");
        this.iconStr = { "id": 16777248, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" };
        this.__select = new ObservedPropertySimplePU(true, this, "select");
        this.mavplr = new MyAvPlayer();
        this.musicOpen = false;
        this.__gameSatus = new ObservedPropertySimplePU(0, this, "gameSatus");
        this.difficulty = 'easy';
        this.dataPreferences = null;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ShuduGame_Params) {
        if (params.pageInfos !== undefined) {
            this.pageInfos = params.pageInfos;
        }
        if (params.UIContext !== undefined) {
            this.UIContext = params.UIContext;
        }
        if (params.uiAbilityContext !== undefined) {
            this.uiAbilityContext = params.uiAbilityContext;
        }
        if (params.windowClass !== undefined) {
            this.windowClass = params.windowClass;
        }
        if (params.naviIndicatorHeight !== undefined) {
            this.naviIndicatorHeight = params.naviIndicatorHeight;
        }
        if (params.statusBarHeight !== undefined) {
            this.statusBarHeight = params.statusBarHeight;
        }
        if (params.settings !== undefined) {
            this.settings = params.settings;
        }
        if (params.context !== undefined) {
            this.context = params.context;
        }
        if (params.datas0 !== undefined) {
            this.datas0 = params.datas0;
        }
        if (params.datas1 !== undefined) {
            this.datas1 = params.datas1;
        }
        if (params.shud !== undefined) {
            this.shud = params.shud;
        }
        if (params.cellsize !== undefined) {
            this.cellsize = params.cellsize;
        }
        if (params.wf !== undefined) {
            this.wf = params.wf;
        }
        if (params.chooseOne !== undefined) {
            this.chooseOne = params.chooseOne;
        }
        if (params.usedDatas !== undefined) {
            this.usedDatas = params.usedDatas;
        }
        if (params.modeFlag !== undefined) {
            this.modeFlag = params.modeFlag;
        }
        if (params.initFlag !== undefined) {
            this.initFlag = params.initFlag;
        }
        if (params.lastX !== undefined) {
            this.lastX = params.lastX;
        }
        if (params.lastY !== undefined) {
            this.lastY = params.lastY;
        }
        if (params.short !== undefined) {
            this.short = params.short;
        }
        if (params.showInstructions !== undefined) {
            this.showInstructions = params.showInstructions;
        }
        if (params.iconStr !== undefined) {
            this.iconStr = params.iconStr;
        }
        if (params.select !== undefined) {
            this.select = params.select;
        }
        if (params.mavplr !== undefined) {
            this.mavplr = params.mavplr;
        }
        if (params.musicOpen !== undefined) {
            this.musicOpen = params.musicOpen;
        }
        if (params.gameSatus !== undefined) {
            this.gameSatus = params.gameSatus;
        }
        if (params.difficulty !== undefined) {
            this.difficulty = params.difficulty;
        }
        if (params.dataPreferences !== undefined) {
            this.dataPreferences = params.dataPreferences;
        }
    }
    updateStateVars(params: ShuduGame_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__usedDatas.purgeDependencyOnElmtId(rmElmtId);
        this.__modeFlag.purgeDependencyOnElmtId(rmElmtId);
        this.__lastX.purgeDependencyOnElmtId(rmElmtId);
        this.__lastY.purgeDependencyOnElmtId(rmElmtId);
        this.__short.purgeDependencyOnElmtId(rmElmtId);
        this.__showInstructions.purgeDependencyOnElmtId(rmElmtId);
        this.__select.purgeDependencyOnElmtId(rmElmtId);
        this.__gameSatus.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__usedDatas.aboutToBeDeleted();
        this.__modeFlag.aboutToBeDeleted();
        this.__lastX.aboutToBeDeleted();
        this.__lastY.aboutToBeDeleted();
        this.__short.aboutToBeDeleted();
        this.__showInstructions.aboutToBeDeleted();
        this.__select.aboutToBeDeleted();
        this.__gameSatus.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private pageInfos: NavPathStack;
    private UIContext;
    private uiAbilityContext;
    private windowClass: window.Window;
    private naviIndicatorHeight: number;
    private statusBarHeight: number;
    private settings: RenderingContextSettings;
    private context: CanvasRenderingContext2D;
    private datas0: Array<ChessPieces>;
    private datas1: Array<ChessPieces>;
    private shud: number[][];
    private cellsize;
    //private mntStack:Stack<ChessPieces> = new Stack<ChessPieces>();
    private wf: componentUtils.Offset;
    private chooseOne: ChessPieces | undefined;
    private __usedDatas: ObservedPropertyObjectPU<Array<ChessPieces>>;
    get usedDatas() {
        return this.__usedDatas.get();
    }
    set usedDatas(newValue: Array<ChessPieces>) {
        this.__usedDatas.set(newValue);
    }
    private __modeFlag: ObservedPropertySimplePU<number>;
    get modeFlag() {
        return this.__modeFlag.get();
    }
    set modeFlag(newValue: number) {
        this.__modeFlag.set(newValue);
    }
    private initFlag: number;
    private __lastX: ObservedPropertySimplePU<number>;
    get lastX() {
        return this.__lastX.get();
    }
    set lastX(newValue: number) {
        this.__lastX.set(newValue);
    }
    private __lastY: ObservedPropertySimplePU<number>;
    get lastY() {
        return this.__lastY.get();
    }
    set lastY(newValue: number) {
        this.__lastY.set(newValue);
    }
    private __short: ObservedPropertySimplePU<number>;
    get short() {
        return this.__short.get();
    }
    set short(newValue: number) {
        this.__short.set(newValue);
    }
    private __showInstructions: ObservedPropertySimplePU<boolean>;
    get showInstructions() {
        return this.__showInstructions.get();
    }
    set showInstructions(newValue: boolean) {
        this.__showInstructions.set(newValue);
    }
    //菜单相关属性
    private iconStr: ResourceStr;
    private __select: ObservedPropertySimplePU<boolean>;
    get select() {
        return this.__select.get();
    }
    set select(newValue: boolean) {
        this.__select.set(newValue);
    }
    //音乐播放器控制类
    private mavplr: MyAvPlayer;
    private musicOpen: boolean;
    //动画控制
    private __gameSatus: ObservedPropertySimplePU<number>; //0-游戏初始化，1-游戏成功，2-游戏失败
    get gameSatus() {
        return this.__gameSatus.get();
    }
    set gameSatus(newValue: number) {
        this.__gameSatus.set(newValue);
    }
    //难度控制
    private difficulty: 'easy' | 'medium' | 'hard';
    //配置读取
    private dataPreferences: preferences.Preferences | null;
    SubMenu(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Menu.create();
        }, Menu);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            MenuItem.create({ startIcon: { "id": 16777243, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }, content: "数独经典模式" });
            MenuItem.onChange((selected) => {
                if (selected) {
                    this.initFlag = 1;
                    this.modeFlag = 1;
                    this.usedDatas = this.datas1;
                    this.initCnavas();
                }
            });
        }, MenuItem);
        MenuItem.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            MenuItem.create({ startIcon: { "id": 16777244, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }, content: "自在经典模式" });
            MenuItem.onChange((selected) => {
                if (selected) {
                    this.initFlag = 0;
                    this.modeFlag = 1;
                    this.usedDatas = this.datas1;
                    this.initCnavas();
                }
            });
        }, MenuItem);
        MenuItem.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            MenuItem.create({ startIcon: { "id": 16777234, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }, content: "趣味卡通模式" });
            MenuItem.onChange((selected) => {
                if (selected) {
                    this.initFlag = 1;
                    this.modeFlag = 0;
                    this.usedDatas = this.datas0;
                    this.initCnavas();
                }
            });
        }, MenuItem);
        MenuItem.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            MenuItem.create({ startIcon: { "id": 16777233, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }, content: "自在卡通模式" });
            MenuItem.onChange((selected) => {
                if (selected) {
                    this.initFlag = 0;
                    this.modeFlag = 0;
                    this.usedDatas = this.datas0;
                    this.initCnavas();
                }
            });
        }, MenuItem);
        MenuItem.pop();
        Menu.pop();
    }
    MyMenu(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Menu.create();
        }, Menu);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            MenuItem.create({ startIcon: { "id": 16777247, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }, content: "游戏模式选择", builder: this.SubMenu.bind(this) });
            MenuItem.onChange((selected) => {
                console.info("菜单选项1 select" + selected);
                let Str: Tmp = new Tmp();
                Str.set({ "id": 16777237, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
            });
        }, MenuItem);
        MenuItem.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            MenuItem.create({
                startIcon: { "id": 16777253, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" },
                content: "个人中心",
                endIcon: { "id": 16777252, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }
            });
            MenuItem.onChange((selected) => {
                if (selected) {
                    this.pageInfos.pushPathByName("personalization", null);
                }
            });
        }, MenuItem);
        MenuItem.pop();
        Menu.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            NavDestination.create(() => {
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Stack.create();
                    Stack.alignContent(Alignment.Center);
                }, Stack);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Scroll.create();
                }, Scroll);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 30 });
                    Column.justifyContent(FlexAlign.Start);
                    Column.alignItems(HorizontalAlign.Center);
                    Column.width('100%');
                    Column.backgroundColor(Color.White);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.width('90%');
                    Row.justifyContent(FlexAlign.SpaceBetween);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777249, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(30);
                    Image.height(30);
                    Image.onClick(() => { });
                    Image.bindMenu({ builder: this.MyMenu.bind(this) });
                    Image.draggable(false);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.onClick(() => {
                        this.initCnavas();
                    });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777236, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(30);
                    Image.height(30);
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.initFlag === 0) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('重新开始');
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('换一题');
                            }, Text);
                            Text.pop();
                        });
                    }
                }, If);
                If.pop();
                Column.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Canvas.create(this.context);
                    Canvas.id("canvas");
                    Canvas.width(this.short);
                    Canvas.height(this.short);
                    Canvas.backgroundColor('#000000');
                    Canvas.allowDrop([uniformTypeDescriptor.UniformDataType.IMAGE]);
                    Canvas.onDrop((dragEvent: DragEvent) => {
                        //this.context.saveLayer();
                        let cellsize = Math.floor(this.short / 9);
                        let records: Array<unifiedDataChannel.UnifiedRecord> = dragEvent.getData().getRecords();
                        let uri = (records[0] as unifiedDataChannel.Image).imageUri;
                        console.log("uri:" + uri + "id" + { "id": 16777230, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }.id);
                        this.resourceUriToPixelMap(cellsize).then((pixelMap: image.PixelMap | undefined) => {
                            if (pixelMap) {
                                //获取画布组件相对应用窗口的坐标
                                let componentUtils: ComponentUtils = this.UIContext.getComponentUtils();
                                let modePosition = componentUtils.getRectangleById("canvas");
                                this.wf = modePosition.windowOffset;
                                let tochX = dragEvent.getWindowX() - px2vp(this.wf.x);
                                let tochY = dragEvent.getWindowY() - px2vp(this.wf.y);
                                //计算画布坐标系坐标
                                tochX = tochX - tochX % (cellsize); //对x减调多余的
                                tochY = tochY - tochY % (cellsize);
                                //判断坐标内是否已有数字
                                let sdux = tochX / cellsize;
                                let sduy = tochY / cellsize;
                                console.log('下棋位置' + sdux + "---" + sduy);
                                if (this.shud[sdux][sduy] == 0) {
                                    //根据图片大小和网格线大小，计算坐标使图片渲染在网格中心，图片大小默认为网格大小-8
                                    tochX = tochX + (8 / 2);
                                    tochY = tochY + (8 / 2);
                                    console.log("坐标信息:", JSON.stringify(modePosition));
                                    this.lastX = tochX;
                                    this.lastY = tochY;
                                    this.shud[sdux][sduy] = this.chooseOne ? this.chooseOne.index : 0;
                                    console.log("下棋后的矩阵变化" + JSON.stringify(this.shud));
                                    this.context.drawImage(pixelMap, tochX, tochY);
                                    //检查是否破坏游戏规则
                                    this.checkGameResu();
                                    console.info('成功画画了');
                                }
                                else {
                                    //提示错误
                                    console.log('下棋位置重复');
                                }
                            }
                            else {
                                console.log("没有获取到图片资源");
                            }
                        });
                    });
                    Canvas.onReady(() => {
                        this.initCnavas();
                    });
                }, Canvas);
                Canvas.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 操作说明区域
                    Column.create({ space: 8 });
                    // 操作说明区域
                    Column.width('95%');
                    // 操作说明区域
                    Column.padding(12);
                    // 操作说明区域
                    Column.backgroundColor('#F8F9FA');
                    // 操作说明区域
                    Column.borderRadius(12);
                    // 操作说明区域
                    Column.border({ width: 1, color: '#E1E8ED' });
                    // 操作说明区域
                    Column.margin({ top: 10, bottom: 10 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 8 });
                    Row.justifyContent(FlexAlign.Center);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Image.create({ "id": 16777251, "type": 20000, params: [], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                    Image.width(20);
                    Image.height(20);
                    Image.fillColor('#3498DB');
                }, Image);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('游戏操作说明');
                    Text.fontSize(16);
                    Text.fontWeight(FontWeight.Medium);
                    Text.fontColor('#2C3E50');
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create({ space: 6 });
                    Column.width('90%');
                    Column.alignItems(HorizontalAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 8 });
                    Row.width('100%');
                    Row.justifyContent(FlexAlign.Start);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('1️⃣');
                    Text.fontSize(14);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('长按下方的数字或图标');
                    Text.fontSize(14);
                    Text.fontColor('#34495E');
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 8 });
                    Row.width('100%');
                    Row.justifyContent(FlexAlign.Start);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('2️⃣');
                    Text.fontSize(14);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('拖拽到棋盘中对应的位置');
                    Text.fontSize(14);
                    Text.fontColor('#34495E');
                }, Text);
                Text.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create({ space: 8 });
                    Row.width('100%');
                    Row.justifyContent(FlexAlign.Start);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('3️⃣');
                    Text.fontSize(14);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create('松开手指完成数字填入');
                    Text.fontSize(14);
                    Text.fontColor('#34495E');
                }, Text);
                Text.pop();
                Row.pop();
                Column.pop();
                // 操作说明区域
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.width('100%');
                    Row.justifyContent(FlexAlign.SpaceEvenly);
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    ForEach.create();
                    const forEachItemGenFunction = _item => {
                        const value = _item;
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Image.create({ "id": -1, "type": -1, params: [value.photo], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" });
                            Image.width(Math.floor(this.short / 9));
                            Image.height(Math.floor(this.short / 9));
                            Image.onDragStart(() => {
                                this.chooseOne = value;
                            });
                            Image.onPreDrag((value: PreDragStatus) => {
                                if (value === PreDragStatus.READY_TO_TRIGGER_DRAG_ACTION) {
                                    //音效播放
                                    this.mavplr.setUrl(this.uiAbilityContext, "select01.mp3");
                                }
                            });
                            Gesture.create(GesturePriority.Low);
                            LongPressGesture.create({ repeat: false });
                            LongPressGesture.onAction((event: GestureEvent) => {
                            });
                            LongPressGesture.onActionEnd((event: GestureEvent) => {
                            });
                            LongPressGesture.pop();
                            Gesture.pop();
                        }, Image);
                    };
                    this.forEachUpdateFunction(elmtId, this.usedDatas, forEachItemGenFunction, (value: ChessPieces, index: number) => {
                        console.log("什么鬼" + value.photo + value.photoid);
                        return index.toString() + JSON.stringify(value);
                    }, false, true);
                }, ForEach);
                ForEach.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Button.createWithLabel("撤销上一步");
                    Button.onClick(() => {
                        this.context.clearRect(this.lastX, this.lastY, this.cellsize - 8, this.cellsize - 8);
                        this.shud[Math.floor(this.lastX / this.cellsize)][Math.floor(this.lastY / this.cellsize)] = 0;
                    });
                }, Button);
                Button.pop();
                Column.pop();
                Scroll.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.gameSatus === 2 || this.gameSatus === 1) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Particle.create({
                                    particles: [
                                        {
                                            emitter: {
                                                particle: {
                                                    type: ParticleType.IMAGE,
                                                    config: {
                                                        src: { "id": -1, "type": -1, params: [this.gameSatus === 2 ? "app.media.gamedown" : "app.media.goal2"], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" },
                                                        size: [20, 20]
                                                    },
                                                    count: 500,
                                                    lifetime: 10000,
                                                    lifetimeRange: 100 //粒子生命周期取值范围，单位ms
                                                },
                                                emitRate: this.gameSatus === 2 ? 10 : 20,
                                                position: [0, 0],
                                                shape: ParticleEmitterShape.ELLIPSE //发射器形状
                                            },
                                            opacity: {
                                                range: [0.8, 1.0],
                                                updater: {
                                                    type: ParticleUpdater.CURVE,
                                                    config: [
                                                        {
                                                            from: 0.0,
                                                            to: 1.0,
                                                            startMillis: 0,
                                                            endMillis: 3000,
                                                            curve: Curve.EaseIn
                                                        },
                                                        {
                                                            from: 1.0,
                                                            to: 0.0,
                                                            startMillis: 5000,
                                                            endMillis: 10000,
                                                            curve: Curve.EaseIn
                                                        }
                                                    ]
                                                }
                                            },
                                            scale: {
                                                range: [1.0, 1.0],
                                                updater: {
                                                    type: ParticleUpdater.CURVE,
                                                    config: [
                                                        {
                                                            from: 1.0,
                                                            to: 1.5,
                                                            startMillis: 0,
                                                            endMillis: 3000,
                                                            curve: Curve.EaseIn
                                                        }
                                                    ]
                                                }
                                            },
                                            acceleration: {
                                                //加速度的配置，从大小和方向两个维度变化，speed表示加速度大小，angle表示加速度方向
                                                speed: {
                                                    range: [3, 9],
                                                    updater: {
                                                        type: ParticleUpdater.RANDOM,
                                                        config: [1, 20]
                                                    }
                                                },
                                                angle: {
                                                    range: [225, 315]
                                                }
                                            }
                                        }
                                    ]
                                });
                                Particle.width(300);
                                Particle.height(300);
                            }, Particle);
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Stack.pop();
            }, { moduleName: "entry", pagePath: "entry/src/main/ets/view/ShuduGame" });
            NavDestination.hideTitleBar(true);
            NavDestination.padding({ top: this.statusBarHeight, bottom: this.naviIndicatorHeight });
            NavDestination.onReady((context: NavDestinationContext) => {
                this.pageInfos = context.pathStack;
                hilog.info(0, "数海鹅独", "current page config info is " + JSON.stringify(context.getConfigInRouteMap()));
            });
            NavDestination.onShown(() => {
                hilog.info(0, "数海鹅独", "头部%{public}d,状态栏%{public}d", this.naviIndicatorHeight, this.statusBarHeight);
                this.windowClass.setWindowLayoutFullScreen(true);
                this.windowClass.setWindowSystemBarEnable([]);
                //获取窗口宽高
                try {
                    //Get window properties
                    let properties: window.WindowProperties = this.windowClass.getWindowProperties();
                    let rect = properties.windowRect;
                    hilog.info(0, "数海鹅独", "长度%{public}d,宽度%{public}d", px2vp(rect.height), px2vp(rect.width));
                    let min = px2vp(Math.min(...[rect.width, rect.height]));
                    this.short = min > 600 ? min * 0.8 : min;
                    this.short = min > 600 ? min * 0.8 : min;
                    this.cellsize = Math.floor(this.short / 9);
                    //rect.width: Window Width；rect.height: Window height
                    if (this.dataPreferences) {
                        this.difficulty = this.dataPreferences.getSync('difficulty', 'easy') as 'easy' | 'medium' | 'hard';
                        this.musicOpen = this.dataPreferences.getSync('musicOpen', false) as boolean;
                    }
                    if (this.musicOpen) {
                        //初始化音乐播放器
                        this.mavplr.init();
                    }
                }
                catch (exception) {
                    console.error('有些页面初始化异常: ' + JSON.stringify(exception));
                }
            });
            NavDestination.onHidden(() => {
                this.windowClass.setWindowLayoutFullScreen(false);
                this.windowClass.setWindowSystemBarEnable(['status', 'navigation']);
                this.mavplr.release();
            });
        }, NavDestination);
        NavDestination.pop();
    }
    aboutToAppear(): void {
        //初始化棋子图片
        this.initDatas();
        // 获取偏好设置
        let options: preferences.Options = { name: 'myStore' };
        this.dataPreferences = preferences.getPreferencesSync(getContext(this), options);
    }
    checkGameResu() {
        let len1 = this.shud.length;
        let len2 = this.shud[0].length;
        let remain = false;
        let jiuge: HashMap<string, HashMap<number, string>> = new HashMap();
        let lie: HashMap<number, HashMap<number, string>> = new HashMap();
        let repeat: HashSet<string> = new HashSet();
        for (let i = 0; i < len1; i++) {
            let hang: HashMap<number, string> = new HashMap();
            for (let j = 0; j < len2; j++) {
                if (this.shud[i][j] === 0) {
                    remain = true;
                }
                else {
                    if (hang.hasKey(this.shud[i][j])) {
                        repeat.add(i + "-" + j);
                        repeat.add(hang.get(this.shud[i][j]));
                    }
                    if (lie.get(j) === undefined) {
                        lie.set(j, new HashMap());
                    }
                    if (lie.get(j).hasKey(this.shud[i][j])) {
                        repeat.add(i + "-" + j);
                        repeat.add(lie.get(j).get(this.shud[i][j]));
                    }
                    let keyJ: string = Math.floor(i / 3) + "-" + Math.floor(j / 3);
                    if (jiuge.get(keyJ) === undefined) {
                        jiuge.set(keyJ, new HashMap());
                    }
                    if (jiuge.get(keyJ).hasKey(this.shud[i][j])) {
                        repeat.add(i + "-" + j);
                        repeat.add(jiuge.get(keyJ).get(this.shud[i][j]));
                    }
                    hang.set(this.shud[i][j], i + "-" + j);
                    lie.get(j).set(this.shud[i][j], i + "-" + j);
                    jiuge.get(keyJ).set(this.shud[i][j], i + "-" + j);
                }
            }
        }
        if (repeat.isEmpty() && remain) {
            //游戏继续
            console.log("游戏继续");
            //音效播放
            this.mavplr.setUrl(this.uiAbilityContext, "jump01.mp3");
        }
        else if (repeat.isEmpty() && !remain) {
            //游戏胜利
            this.gameSatus = 1;
            this.mavplr.setUrl(this.uiAbilityContext, "success.mp3");
            let confirm = () => {
                this.initCnavas();
                this.mavplr.stop();
            };
            new CommonDialog().openMessageDiag(this.UIContext, "太棒了,恭喜你完成挑战！", confirm);
            console.log("游戏胜利");
        }
        else if (!repeat.isEmpty()) {
            //游戏失败
            console.log("你输了哦");
            this.gameSatus = 2;
            this.mavplr.setUrl(this.uiAbilityContext, "fail.mp3");
            let confirm = () => {
                this.initCnavas();
                this.mavplr.stop();
            };
            new CommonDialog().openErrorDialog(this.UIContext, "游戏结束，继续加油！", confirm, px2vp(this.wf.x), px2vp(this.wf.y));
            repeat.forEach((value: string, key?: string): void => {
                if (value) {
                    let splits = value.split("-");
                    //获取冲突元素坐标
                    let dupx = Number(splits[0]);
                    let dupy = Number(splits[1]);
                    //获取冲突元素根对象
                    let duplicate: ChessPieces = this.usedDatas[this.shud[dupx][dupy] - 1]; //矩阵中存的下标是1开始的
                    let cellsize = Math.floor(this.short / 9);
                    this.context.beginPath();
                    this.context.strokeStyle = '#ff0000';
                    this.context.lineWidth = 4;
                    this.context.moveTo(cellsize * (dupx) + 4 + 2, cellsize * (dupy) + 4 + 2);
                    this.context.lineTo(cellsize * (dupx + 1) - 4 - 2, cellsize * (dupy + 1) - 4 - 2);
                    this.context.stroke();
                }
            });
        }
    }
    async resourceUriToPixelMap(cellsize: number): Promise<image.PixelMap | undefined> {
        try {
            const context = getContext(this);
            const resourceMgr = context.resourceManager;
            // 获取资源Buffer并生成PixelMap
            let filename = "animal1";
            if (this.chooseOne) {
                filename = this.chooseOne.photo.replaceAll("app.media.", "");
            }
            else {
                console.log("当前选中图片资源不存在");
            }
            let val = resourceMgr.getMediaByNameSync(filename);
            const imageSource = image.createImageSource(val.buffer);
            return await imageSource.createPixelMap({ desiredSize: { width: vp2px(cellsize - 8), height: vp2px(cellsize - 8) } });
        }
        catch (err) {
            console.error('Resource conversion failed:', err);
        }
        return undefined;
    }
    async resourceToPixelMap(media: ChessPieces, cellsize: number): Promise<image.PixelMap | undefined> {
        try {
            const context = getContext(this);
            const resourceMgr = context.resourceManager;
            // 获取资源Buffer并生成PixelMap
            let filename = media.photo.replaceAll("app.media.", "");
            let val = resourceMgr.getMediaByNameSync(filename);
            const imageSource = image.createImageSource(val.buffer);
            return await imageSource.createPixelMap({ desiredSize: { width: vp2px(cellsize - 8), height: vp2px(cellsize - 8) } });
        }
        catch (err) {
            console.error('Resource conversion failed:', err);
        }
        return undefined;
    }
    initDatas() {
        for (let i = 1; i <= 9; i++) {
            let animal = new ChessPieces(i, "app.media." + "animal" + i, { "id": -1, "type": -1, params: [`app.media.animal+${i}`], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }.id);
            let numer = new ChessPieces(i, "app.media." + "number" + i, { "id": -1, "type": -1, params: [`app.media.number+${i}`], "bundleName": "com.atomicservice.6917576098804821135", "moduleName": "entry" }.id);
            this.datas0[i - 1] = animal;
            this.datas1[i - 1] = numer;
        }
        if (this.modeFlag == 0) {
            this.usedDatas = this.datas0;
        }
        else {
            this.usedDatas = this.datas1;
        }
        console.log("数据源" + JSON.stringify(this.usedDatas));
    }
    initCnavas() {
        //清空画布+初始化矩阵+初始化游戏状态
        this.shud = Array(9).fill(0).map(() => Array(9).fill(0));
        this.gameSatus = 0;
        this.context.clearRect(0, 0, this.short, this.short);
        this.mavplr.stop();
        //可以在这里绘制内容。
        let kuan = this.cellsize;
        this.context.fillStyle = '#000000';
        for (let x = 0; x < 8; x++) {
            if ((x + 1) % 3 === 0) {
                this.context.beginPath();
                this.context.strokeStyle = '#ffff00';
                this.context.lineWidth = 4;
                this.context.moveTo(0, kuan * (1 + x));
                this.context.lineTo(kuan * 9, kuan * (1 + x));
                this.context.stroke();
            }
            else {
                this.context.beginPath();
                this.context.strokeStyle = '#EE7CDF';
                this.context.lineWidth = 2;
                this.context.moveTo(0, kuan * (1 + x));
                this.context.lineTo(kuan * 9, kuan * (1 + x));
                this.context.stroke();
            }
        }
        for (let x = 0; x < 8; x++) {
            if ((x + 1) % 3 === 0) {
                this.context.beginPath();
                this.context.strokeStyle = '#ffff00';
                this.context.lineWidth = 4;
                this.context.moveTo(kuan * (1 + x), 0);
                this.context.lineTo(kuan * (1 + x), kuan * 9);
                this.context.stroke();
            }
            else {
                this.context.beginPath();
                this.context.strokeStyle = '#EE7CDF';
                this.context.lineWidth = 2;
                this.context.moveTo(kuan * (1 + x), 0);
                this.context.lineTo(kuan * (1 + x), kuan * 9);
                this.context.stroke();
            }
        }
        if (this.initFlag == 1) {
            //先生成完整解法
            this.initSudus();
            //在完整解法上挖洞
            this.digHoles(this.difficulty);
            //棋子图片渲染棋盘
            for (let i = 0; i < 9; i++) {
                for (let j = 0; j < 9; j++) {
                    let index = this.shud[i][j];
                    if (index > 0) {
                        //棋盘的xy坐标系和数组的i j 索引是反的
                        this.initDrawingByMedia(this.usedDatas[index - 1], i, j);
                    }
                }
            }
            console.log("初始化棋盘棋子成功" + JSON.stringify(this.shud));
        }
    }
    initDrawingByMedia(arg0: ChessPieces, x: number, y: number) {
        this.resourceToPixelMap(arg0, this.cellsize).then((pixelMap: image.PixelMap | undefined) => {
            if (pixelMap) {
                let tochX = x * this.cellsize + (8 / 2);
                let tochY = y * this.cellsize + (8 / 2);
                this.context.drawImage(pixelMap, tochX, tochY);
            }
            else {
                console.log("没有获取到图片资源");
            }
        });
    }
    digHoles(difficulty: 'easy' | 'medium' | 'hard'): void {
        let holes = difficulty === 'easy' ? 36 : difficulty === 'medium' ? 55 : 64;
        while (holes > 0) {
            const row = Math.floor(Math.random() * 9);
            const col = Math.floor(Math.random() * 9);
            if (this.shud[row][col] !== 0) {
                const temp = this.shud[row][col];
                this.shud[row][col] = 0;
                holes--;
            }
        }
    }
    private initSudus(): void {
        // 随机填充第一行作为种子
        const firstRow = [1, 2, 3, 4, 5, 6, 7, 8, 9]
            .sort(() => Math.random() - 0.5);
        this.shud[0] = [...firstRow];
        const backtrack = (row: number, col: number): boolean => {
            if (row === 9)
                return true;
            if (col === 9)
                return backtrack(row + 1, 0);
            if (this.shud[row][col] !== 0)
                return backtrack(row, col + 1);
            // 随机尝试数字
            const nums = [1, 2, 3, 4, 5, 6, 7, 8, 9]
                .sort(() => Math.random() - 0.5);
            for (const num of nums) {
                if (this.isValid(this.shud, row, col, num)) {
                    this.shud[row][col] = num;
                    if (backtrack(row, col + 1))
                        return true;
                    this.shud[row][col] = 0; // 回溯
                }
            }
            return false;
        };
        backtrack(1, 0); // 从第二行开始填充
    }
    private isValid(board: number[][], row: number, col: number, num: number): boolean {
        // 检查行
        if (board[row].includes(num))
            return false;
        // 检查列
        if (board.some(r => r[col] === num))
            return false;
        // 检查3x3宫格
        const boxRow = Math.floor(row / 3) * 3;
        const boxCol = Math.floor(col / 3) * 3;
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                if (board[boxRow + i][boxCol + j] === num)
                    return false;
            }
        }
        return true;
    }
    rerender() {
        this.updateDirtyElements();
    }
}
(function () {
    if (typeof NavigationBuilderRegister === "function") {
        NavigationBuilderRegister("shuduGame", wrapBuilder(ShuduGameBuilder));
    }
})();
