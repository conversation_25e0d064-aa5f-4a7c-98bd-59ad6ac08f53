{"version": "2.0", "ppid": 19208, "events": [{"head": {"id": "62007fd2-6d5c-4065-b4f5-e739545e9a02", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33584305614800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf9c3723-ce40-401c-82fe-8aa1968fb091", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33584311252900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fb843a4-7656-46f0-af3b-f6d1aa5609ea", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33584311620000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e68f10-d7d3-4fe2-9dfa-7eefb64954b4", "name": "worker[3] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33584315579700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f56bb103-31d0-4341-b0fd-d0f12d552a79", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790826009500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec65098b-a0df-4b7e-b7ea-10312868f997", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790835015200, "endTime": 33791358329000}, "additional": {"children": ["739f907f-ca42-4877-8311-77576c24d9f3", "ddfc0729-52f8-4821-adc4-cc8010cc908b", "8e2fa8c7-1291-48a0-8be3-8eedc5ef0008", "ca85f717-fca8-4f42-97a6-2db90621f223", "adcaa6c6-0232-4223-b3f9-849ec0a4cc21", "80bc97c0-ce94-417f-9b6a-b1757766566f", "570ca238-1c29-4ebe-b5db-86159d301ddc"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "739f907f-ca42-4877-8311-77576c24d9f3", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790835017900, "endTime": 33790849771600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "979db04d-3b3b-4651-8a89-b9730fe3b106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790849793500, "endTime": 33791356380100}, "additional": {"children": ["574d9a82-a310-4dbb-964a-b42a72f8dfdf", "a12acf8d-5d8f-480f-a9c4-1814f454b39f", "3a5091ed-2255-4456-ab62-8efd2a3d0e6b", "2d504547-2a7b-438f-8a7b-aaac7ab3ee3c", "d7a864bd-9223-4af1-ba42-2511cf273b61", "5cc34bcc-f539-4ef9-b01c-a51c7f2f7f21", "d5c23f9b-e327-4f47-a8f2-2f1cbe65abda", "801b6497-3124-4b0c-ad3f-274cbd78fdc2", "1f9c1210-0ec7-4667-828b-6c947d1ea6e8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "949f8beb-e0de-42e9-a5ff-29894519c97a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e2fa8c7-1291-48a0-8be3-8eedc5ef0008", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791356406900, "endTime": 33791358315200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "94636fd4-8266-46cb-9aa7-e90a966319a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca85f717-fca8-4f42-97a6-2db90621f223", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791358320300, "endTime": 33791358322700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "1d691f81-a11d-4a52-821b-4ead35796c99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "adcaa6c6-0232-4223-b3f9-849ec0a4cc21", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790838577500, "endTime": 33790838709500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "7bc7fbe7-740d-4edb-ac14-e5b27d1a69b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bc7fbe7-740d-4edb-ac14-e5b27d1a69b6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790838577500, "endTime": 33790838709500}, "additional": {"logType": "info", "children": [], "durationId": "adcaa6c6-0232-4223-b3f9-849ec0a4cc21", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "80bc97c0-ce94-417f-9b6a-b1757766566f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790844680000, "endTime": 33790844703300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "16616770-9918-46eb-a64d-5a4b72070632"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16616770-9918-46eb-a64d-5a4b72070632", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790844680000, "endTime": 33790844703300}, "additional": {"logType": "info", "children": [], "durationId": "80bc97c0-ce94-417f-9b6a-b1757766566f", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "6d8c0661-1927-4a58-a870-226f2afa631e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790844855300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f3b36a-0b64-4ab3-9feb-4662ca63792f", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790849583500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "979db04d-3b3b-4651-8a89-b9730fe3b106", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790835017900, "endTime": 33790849771600}, "additional": {"logType": "info", "children": [], "durationId": "739f907f-ca42-4877-8311-77576c24d9f3", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "574d9a82-a310-4dbb-964a-b42a72f8dfdf", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790857293400, "endTime": 33790857301100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "a13a9114-6073-49d7-8745-6eed5465fa97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a12acf8d-5d8f-480f-a9c4-1814f454b39f", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790857320700, "endTime": 33790862310400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "f18a46c1-b6ec-44ec-bb12-e08a0b5579f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a5091ed-2255-4456-ab62-8efd2a3d0e6b", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790862332400, "endTime": 33791165063700}, "additional": {"children": ["7d431189-355c-4ab6-8cc1-c828d75d0652", "ee148ec2-3b42-40d2-89d2-90299a17efe0", "8f5da3c0-4f64-4671-8f12-6ebbd10ddd2d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "14903656-3f7c-4957-a3e2-c14d51d9d61b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d504547-2a7b-438f-8a7b-aaac7ab3ee3c", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791165077100, "endTime": 33791213085900}, "additional": {"children": ["f8858a5a-7e33-4f05-bef9-44e5788c4d0a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "2e43f4e1-b85a-4ba7-b609-f91f78fb3143"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7a864bd-9223-4af1-ba42-2511cf273b61", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791213093400, "endTime": 33791313595700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "81f479fa-6f1e-43cb-97c9-57cb08737b6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5cc34bcc-f539-4ef9-b01c-a51c7f2f7f21", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791316070500, "endTime": 33791342251900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "d0c87a20-aed4-4fa9-838b-d5cf112e84bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5c23f9b-e327-4f47-a8f2-2f1cbe65abda", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791342280700, "endTime": 33791355642300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "8ec111d4-728e-4dfc-8365-b5118da5e7fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "801b6497-3124-4b0c-ad3f-274cbd78fdc2", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791355675300, "endTime": 33791356361200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "a6e35326-b2f1-4f5c-bfa1-86c2a01626d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a13a9114-6073-49d7-8745-6eed5465fa97", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790857293400, "endTime": 33790857301100}, "additional": {"logType": "info", "children": [], "durationId": "574d9a82-a310-4dbb-964a-b42a72f8dfdf", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "f18a46c1-b6ec-44ec-bb12-e08a0b5579f9", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790857320700, "endTime": 33790862310400}, "additional": {"logType": "info", "children": [], "durationId": "a12acf8d-5d8f-480f-a9c4-1814f454b39f", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "7d431189-355c-4ab6-8cc1-c828d75d0652", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790863605600, "endTime": 33790863629500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a5091ed-2255-4456-ab62-8efd2a3d0e6b", "logId": "44ba9273-cb91-4281-9cf4-822d702e8635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44ba9273-cb91-4281-9cf4-822d702e8635", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790863605600, "endTime": 33790863629500}, "additional": {"logType": "info", "children": [], "durationId": "7d431189-355c-4ab6-8cc1-c828d75d0652", "parent": "14903656-3f7c-4957-a3e2-c14d51d9d61b"}}, {"head": {"id": "ee148ec2-3b42-40d2-89d2-90299a17efe0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790866131800, "endTime": 33791164376000}, "additional": {"children": ["4a64569d-fe2e-4fc3-9c16-ee21af03757b", "bd3b20f9-1ef8-4709-b385-a051d4a8d599"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a5091ed-2255-4456-ab62-8efd2a3d0e6b", "logId": "26610cc6-d2df-4f72-b453-9ee05eb58a9e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a64569d-fe2e-4fc3-9c16-ee21af03757b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790866134600, "endTime": 33790886393500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee148ec2-3b42-40d2-89d2-90299a17efe0", "logId": "7e968a27-44d7-4cf7-808c-892d4d93d876"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd3b20f9-1ef8-4709-b385-a051d4a8d599", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790886411600, "endTime": 33791164364700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ee148ec2-3b42-40d2-89d2-90299a17efe0", "logId": "d533320c-c1a3-4359-8351-6f6421c1a97b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cdc15233-0e53-4ae3-ad31-d9c0974028c9", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790866140800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74b7c675-b186-43c1-b0a3-f7e190331669", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790886249800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e968a27-44d7-4cf7-808c-892d4d93d876", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790866134600, "endTime": 33790886393500}, "additional": {"logType": "info", "children": [], "durationId": "4a64569d-fe2e-4fc3-9c16-ee21af03757b", "parent": "26610cc6-d2df-4f72-b453-9ee05eb58a9e"}}, {"head": {"id": "1dbcd4f6-bf34-4b83-9baa-7d1ae4b01b03", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790886426000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8267fd51-1cc0-49d7-b3e2-007affdb98e1", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791016470200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d4cf2e-ae1b-42f7-8038-f09d6ef23038", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791016883200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19ef24d4-b5d9-4795-9e1a-a14111ee2dea", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791018396200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a00ec8fe-8734-4f04-9b22-88c67a61cb48", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791018603300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b2f236-c439-48fb-817e-b9b5eb8c9042", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791028562100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a4f77f-48cb-4279-808a-a66e505445e5", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791065105300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ff43753-1263-475c-9b45-e3bd95f631b2", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791096761300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6844fc8-a2dd-4d33-9167-e7694f56bc60", "name": "Sdk init in 74 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791142795900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2d77ee3-87ab-4a30-9d26-b4abe5fc6658", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791142944600}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 52}, "markType": "other"}}, {"head": {"id": "ff2eb631-b63a-46fb-ad81-273425a7d90d", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791142995300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 52}, "markType": "other"}}, {"head": {"id": "9829aeaf-945a-4d32-acfd-aab83dd405c6", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791164096700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d869c66-b8f4-43c4-8062-c9d9a8b3865f", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791164217500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9595b720-245d-41b0-ad84-c807e1ef81f5", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791164273600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8d0231-9652-41d4-8153-a1d0b34f4261", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791164319500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d533320c-c1a3-4359-8351-6f6421c1a97b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790886411600, "endTime": 33791164364700}, "additional": {"logType": "info", "children": [], "durationId": "bd3b20f9-1ef8-4709-b385-a051d4a8d599", "parent": "26610cc6-d2df-4f72-b453-9ee05eb58a9e"}}, {"head": {"id": "26610cc6-d2df-4f72-b453-9ee05eb58a9e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790866131800, "endTime": 33791164376000}, "additional": {"logType": "info", "children": ["7e968a27-44d7-4cf7-808c-892d4d93d876", "d533320c-c1a3-4359-8351-6f6421c1a97b"], "durationId": "ee148ec2-3b42-40d2-89d2-90299a17efe0", "parent": "14903656-3f7c-4957-a3e2-c14d51d9d61b"}}, {"head": {"id": "8f5da3c0-4f64-4671-8f12-6ebbd10ddd2d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791165033700, "endTime": 33791165049600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a5091ed-2255-4456-ab62-8efd2a3d0e6b", "logId": "f89a489f-9e9c-42d6-ba37-eff70672d89e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f89a489f-9e9c-42d6-ba37-eff70672d89e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791165033700, "endTime": 33791165049600}, "additional": {"logType": "info", "children": [], "durationId": "8f5da3c0-4f64-4671-8f12-6ebbd10ddd2d", "parent": "14903656-3f7c-4957-a3e2-c14d51d9d61b"}}, {"head": {"id": "14903656-3f7c-4957-a3e2-c14d51d9d61b", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790862332400, "endTime": 33791165063700}, "additional": {"logType": "info", "children": ["44ba9273-cb91-4281-9cf4-822d702e8635", "26610cc6-d2df-4f72-b453-9ee05eb58a9e", "f89a489f-9e9c-42d6-ba37-eff70672d89e"], "durationId": "3a5091ed-2255-4456-ab62-8efd2a3d0e6b", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "f8858a5a-7e33-4f05-bef9-44e5788c4d0a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791165980000, "endTime": 33791213074500}, "additional": {"children": ["8462530e-2bfb-4820-98e1-0bb5abd275d4", "bdae1c75-989a-4a63-8127-50522d7d7997", "863cd334-bfe0-4f12-a2a9-b9c02913a27e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2d504547-2a7b-438f-8a7b-aaac7ab3ee3c", "logId": "747cdfb6-810e-40a8-b7a0-4851e1b8ac55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8462530e-2bfb-4820-98e1-0bb5abd275d4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791173808800, "endTime": 33791173834200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8858a5a-7e33-4f05-bef9-44e5788c4d0a", "logId": "0b21812c-bb1a-4a3c-a751-1e578c57a022"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b21812c-bb1a-4a3c-a751-1e578c57a022", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791173808800, "endTime": 33791173834200}, "additional": {"logType": "info", "children": [], "durationId": "8462530e-2bfb-4820-98e1-0bb5abd275d4", "parent": "747cdfb6-810e-40a8-b7a0-4851e1b8ac55"}}, {"head": {"id": "bdae1c75-989a-4a63-8127-50522d7d7997", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791177667200, "endTime": 33791211392800}, "additional": {"children": ["22c469d3-4bc1-4f4a-854b-b402e89660e0", "dd951af2-1f30-41f4-99a3-6db1b46198f0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8858a5a-7e33-4f05-bef9-44e5788c4d0a", "logId": "4d9f025f-b5af-4a08-ad94-0e2739f58fda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22c469d3-4bc1-4f4a-854b-b402e89660e0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791177669400, "endTime": 33791183881000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bdae1c75-989a-4a63-8127-50522d7d7997", "logId": "d98e2550-1e1c-402e-bea9-b53d461bdf13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd951af2-1f30-41f4-99a3-6db1b46198f0", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791183945400, "endTime": 33791211379300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bdae1c75-989a-4a63-8127-50522d7d7997", "logId": "3f6a06a5-1f8e-4ab3-a95d-4c78af23a3a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e7ad2e1-46f9-4097-859c-5fb18ce5fc2c", "name": "hvigorfile, resolving D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791177674000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c6f58bd-04d0-431b-8720-bfeb8bc68de5", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791181819700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98e2550-1e1c-402e-bea9-b53d461bdf13", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791177669400, "endTime": 33791183881000}, "additional": {"logType": "info", "children": [], "durationId": "22c469d3-4bc1-4f4a-854b-b402e89660e0", "parent": "4d9f025f-b5af-4a08-ad94-0e2739f58fda"}}, {"head": {"id": "e221c1f1-8b93-4548-b2ba-6e7870e8d5d5", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791183971700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d5ac7a2-4cb8-458c-8f29-d208d6c4e20f", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791199826100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c51a774-be6e-46f1-afd6-1e8d36509828", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791200220600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3baa4cc1-e15e-4ecd-ac1b-7e55e873a764", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791200904100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fcb865d-b53d-4729-ab1e-f1c6ad1915ee", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791201280600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b6d8fc-68f8-4f27-bf00-67914d295b30", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791201522400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3edebb42-bdbd-4c37-b489-6bcbc8a62608", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791201884800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80bb2fe-e303-4bb8-936b-8722b0f156ab", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": false\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791202262900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4730561b-cbb6-4e9c-bcca-58af267d872b", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791210859200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad35e3cb-9a3d-4e7e-8277-963404040a21", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791211026000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c829fb7d-86a7-498c-8c0c-983a24accaae", "name": "hvigorfile, no custom plugins were found in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791211095300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38cae579-d732-412c-bc20-e905956bfde9", "name": "hvigorfile, resolve finished D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791211317200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f6a06a5-1f8e-4ab3-a95d-4c78af23a3a9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791183945400, "endTime": 33791211379300}, "additional": {"logType": "info", "children": [], "durationId": "dd951af2-1f30-41f4-99a3-6db1b46198f0", "parent": "4d9f025f-b5af-4a08-ad94-0e2739f58fda"}}, {"head": {"id": "4d9f025f-b5af-4a08-ad94-0e2739f58fda", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791177667200, "endTime": 33791211392800}, "additional": {"logType": "info", "children": ["d98e2550-1e1c-402e-bea9-b53d461bdf13", "3f6a06a5-1f8e-4ab3-a95d-4c78af23a3a9"], "durationId": "bdae1c75-989a-4a63-8127-50522d7d7997", "parent": "747cdfb6-810e-40a8-b7a0-4851e1b8ac55"}}, {"head": {"id": "863cd334-bfe0-4f12-a2a9-b9c02913a27e", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791213042700, "endTime": 33791213060200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f8858a5a-7e33-4f05-bef9-44e5788c4d0a", "logId": "d96ae02a-b816-487e-8ba5-795446e3e161"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d96ae02a-b816-487e-8ba5-795446e3e161", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791213042700, "endTime": 33791213060200}, "additional": {"logType": "info", "children": [], "durationId": "863cd334-bfe0-4f12-a2a9-b9c02913a27e", "parent": "747cdfb6-810e-40a8-b7a0-4851e1b8ac55"}}, {"head": {"id": "747cdfb6-810e-40a8-b7a0-4851e1b8ac55", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791165980000, "endTime": 33791213074500}, "additional": {"logType": "info", "children": ["0b21812c-bb1a-4a3c-a751-1e578c57a022", "4d9f025f-b5af-4a08-ad94-0e2739f58fda", "d96ae02a-b816-487e-8ba5-795446e3e161"], "durationId": "f8858a5a-7e33-4f05-bef9-44e5788c4d0a", "parent": "2e43f4e1-b85a-4ba7-b609-f91f78fb3143"}}, {"head": {"id": "2e43f4e1-b85a-4ba7-b609-f91f78fb3143", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791165077100, "endTime": 33791213085900}, "additional": {"logType": "info", "children": ["747cdfb6-810e-40a8-b7a0-4851e1b8ac55"], "durationId": "2d504547-2a7b-438f-8a7b-aaac7ab3ee3c", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "c682cb0b-1d41-4847-b2fa-bf582504c856", "name": "watch files: [\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\hvigorfile.ts',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShu<PERSON>\\\\entry\\\\hvigorfile.ts',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor-ohos-plugin\\\\src\\\\plugin\\\\factory\\\\plugin-factory.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\log4js.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\node.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\debug\\\\src\\\\common.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\ms\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\rfdc\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\configuration.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\layouts.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\date-format\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\levels.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\clustering.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\LoggingEvent.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\flatted\\\\cjs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\adapters.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\console.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stdout.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\stderr.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\logLevelFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\categoryFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\noLogFilter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileWriteStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\graceful-fs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\polyfills.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\legacy-streams.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\graceful-fs\\\\clone.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy-sync\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\win32.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\mkdirs-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\rimraf.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\jsonfile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\jsonfile\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\json\\\\output-json-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move-sync\\\\move-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\move\\\\move.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\node_modules\\\\fs-extra\\\\lib\\\\output\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\now.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameFormatter.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\fileNameParser.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\moveAndMaybeCompressFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\RollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\streamroller\\\\lib\\\\DateRollingFileStream.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\dateFile.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\fileSync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\tcp.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\categories.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\connect-logger.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\log4js\\\\lib\\\\appenders\\\\recording.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\src\\\\common\\\\util\\\\local-file-writer.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\fs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\universalify\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\make-dir.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\mkdirs\\\\utils.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\path-exists\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\utimes.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\util\\\\stat.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\copy\\\\copy-sync.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\empty\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\remove\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\index.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\file.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\link.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-paths.js',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\tools\\\\hvigor\\\\hvigor\\\\node_modules\\\\fs-extra\\\\lib\\\\ensure\\\\symlink-type.js',\n  ... 1857 more items\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791245709300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47817959-d3a3-4ca6-afb5-b5fb3487e4ef", "name": "hvigorfile, resolve hvigorfile dependencies in 101 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791313254300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f479fa-6f1e-43cb-97c9-57cb08737b6b", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791213093400, "endTime": 33791313595700}, "additional": {"logType": "info", "children": [], "durationId": "d7a864bd-9223-4af1-ba42-2511cf273b61", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "1f9c1210-0ec7-4667-828b-6c947d1ea6e8", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791314809900, "endTime": 33791315991900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "logId": "290f7a78-c290-4627-a7c4-f182ae16776a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9af3cf7e-f0b5-47e6-a519-7f200295e6d5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791314833800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "290f7a78-c290-4627-a7c4-f182ae16776a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791314809900, "endTime": 33791315991900}, "additional": {"logType": "info", "children": [], "durationId": "1f9c1210-0ec7-4667-828b-6c947d1ea6e8", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "2871c1db-cd6c-4b3e-82b1-e33a62d6c44a", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791318013000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07aafaf5-3662-4a8e-a0a9-1d73b5c225c8", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791341442900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0c87a20-aed4-4fa9-838b-d5cf112e84bf", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791316070500, "endTime": 33791342251900}, "additional": {"logType": "info", "children": [], "durationId": "5cc34bcc-f539-4ef9-b01c-a51c7f2f7f21", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "4993bc71-2d27-460b-ba7c-22d1b7b0f26f", "name": "<PERSON><PERSON><PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791347596400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "486b93a7-5844-43a0-b67b-e56bebc84d49", "name": "<PERSON><PERSON><PERSON>'s total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791347765300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b9def6e-56e8-4f60-8cd3-012f0a2e3766", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791350819100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3149b456-3b60-4eb4-9d8f-bca9a3105572", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791350944600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec111d4-728e-4dfc-8365-b5118da5e7fe", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791342280700, "endTime": 33791355642300}, "additional": {"logType": "info", "children": [], "durationId": "d5c23f9b-e327-4f47-a8f2-2f1cbe65abda", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "0e3d8973-4437-46ac-8da6-b8d0d31fda4a", "name": "Configuration phase cost:499 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791355915900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e35326-b2f1-4f5c-bfa1-86c2a01626d0", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791355675300, "endTime": 33791356361200}, "additional": {"logType": "info", "children": [], "durationId": "801b6497-3124-4b0c-ad3f-274cbd78fdc2", "parent": "949f8beb-e0de-42e9-a5ff-29894519c97a"}}, {"head": {"id": "949f8beb-e0de-42e9-a5ff-29894519c97a", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790849793500, "endTime": 33791356380100}, "additional": {"logType": "info", "children": ["a13a9114-6073-49d7-8745-6eed5465fa97", "f18a46c1-b6ec-44ec-bb12-e08a0b5579f9", "14903656-3f7c-4957-a3e2-c14d51d9d61b", "2e43f4e1-b85a-4ba7-b609-f91f78fb3143", "81f479fa-6f1e-43cb-97c9-57cb08737b6b", "d0c87a20-aed4-4fa9-838b-d5cf112e84bf", "8ec111d4-728e-4dfc-8365-b5118da5e7fe", "a6e35326-b2f1-4f5c-bfa1-86c2a01626d0", "290f7a78-c290-4627-a7c4-f182ae16776a"], "durationId": "ddfc0729-52f8-4821-adc4-cc8010cc908b", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "570ca238-1c29-4ebe-b5db-86159d301ddc", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791358276900, "endTime": 33791358298300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec65098b-a0df-4b7e-b7ea-10312868f997", "logId": "9bef5f4b-efb7-493d-9b65-107094d8bb9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9bef5f4b-efb7-493d-9b65-107094d8bb9d", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791358276900, "endTime": 33791358298300}, "additional": {"logType": "info", "children": [], "durationId": "570ca238-1c29-4ebe-b5db-86159d301ddc", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "94636fd4-8266-46cb-9aa7-e90a966319a0", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791356406900, "endTime": 33791358315200}, "additional": {"logType": "info", "children": [], "durationId": "8e2fa8c7-1291-48a0-8be3-8eedc5ef0008", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "1d691f81-a11d-4a52-821b-4ead35796c99", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791358320300, "endTime": 33791358322700}, "additional": {"logType": "info", "children": [], "durationId": "ca85f717-fca8-4f42-97a6-2db90621f223", "parent": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c"}}, {"head": {"id": "f0d61c41-cd5a-487c-a3a8-d0ffb3842b0c", "name": "init", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790835015200, "endTime": 33791358329000}, "additional": {"logType": "info", "children": ["979db04d-3b3b-4651-8a89-b9730fe3b106", "949f8beb-e0de-42e9-a5ff-29894519c97a", "94636fd4-8266-46cb-9aa7-e90a966319a0", "1d691f81-a11d-4a52-821b-4ead35796c99", "7bc7fbe7-740d-4edb-ac14-e5b27d1a69b6", "16616770-9918-46eb-a64d-5a4b72070632", "9bef5f4b-efb7-493d-9b65-107094d8bb9d"], "durationId": "ec65098b-a0df-4b7e-b7ea-10312868f997"}}, {"head": {"id": "3b897198-e1d7-41d6-ab36-843573f311d0", "name": "Configuration task cost before running: 528 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791358698200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bddc52c2-f4ac-4fe1-9ff0-55724bae3ae0", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791368402200, "endTime": 33791389663700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "19d3c365-dbf4-4302-9d0a-efa4c7f3a698", "logId": "dd4a4814-8048-4703-ab61-be70068bd1f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19d3c365-dbf4-4302-9d0a-efa4c7f3a698", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791360511500}, "additional": {"logType": "detail", "children": [], "durationId": "bddc52c2-f4ac-4fe1-9ff0-55724bae3ae0"}}, {"head": {"id": "bf1bd1a1-f1c6-40d9-8cb2-599fd2f90629", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791361162300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c31521f-6605-4e71-90f0-ce85f9ab690f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791361291700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0204a35-dd61-4d2e-a41f-d572b6e180f2", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791368417700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6400436e-fce3-4769-bcdc-0a4b46f8b836", "name": "Incremental task entry:default@PreBuild pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791388955500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1801481-3e65-4b4d-b028-5c582160bc3b", "name": "entry : default@PreBuild cost memory 0.3111114501953125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791389097700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd4a4814-8048-4703-ab61-be70068bd1f3", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791368402200, "endTime": 33791389663700}, "additional": {"logType": "info", "children": [], "durationId": "bddc52c2-f4ac-4fe1-9ff0-55724bae3ae0"}}, {"head": {"id": "f793e7ec-4cc7-49f6-9389-181946b2a792", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791400669400, "endTime": 33791404160100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "27a83f02-1267-4a6a-be54-41d6cb0704d5", "logId": "00c01517-9af0-43ea-9fe1-72bf0be5c570"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27a83f02-1267-4a6a-be54-41d6cb0704d5", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791396640800}, "additional": {"logType": "detail", "children": [], "durationId": "f793e7ec-4cc7-49f6-9389-181946b2a792"}}, {"head": {"id": "eb7c3879-2366-44a6-b28e-a0051023908d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791397758300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e81a189c-861f-4735-a901-3fab14621022", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791398192800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2e1fa0-3b0b-4777-9742-4e8e60479395", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791400691300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3125a91-1408-46dd-9a60-c4886f65d2d6", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791402568300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d6b509f-10c6-4963-a844-648d48336f13", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791403907700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b716726-7e3f-445a-a018-79bcf57bed33", "name": "entry : default@GenerateMetadata cost memory 0.09394073486328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791404036000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c01517-9af0-43ea-9fe1-72bf0be5c570", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791400669400, "endTime": 33791404160100}, "additional": {"logType": "info", "children": [], "durationId": "f793e7ec-4cc7-49f6-9389-181946b2a792"}}, {"head": {"id": "3027c382-b8a7-4a35-ac5a-289398fd845a", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411251200, "endTime": 33791412420500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "78eae6ec-b4d2-4200-ac58-863c5afd3c9d", "logId": "a85f7427-15ca-4832-9fc6-3379ce10d966"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "78eae6ec-b4d2-4200-ac58-863c5afd3c9d", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791409044300}, "additional": {"logType": "detail", "children": [], "durationId": "3027c382-b8a7-4a35-ac5a-289398fd845a"}}, {"head": {"id": "9574bedb-750d-40bf-b1a9-015fd48e31e3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791410686600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a81b153a-a665-4882-a2c1-57d487b6e369", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791410926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14115682-c3b5-4969-b508-cd8df3be538b", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411262200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7cb2428-be9c-498d-bb61-bafe8933e954", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411381700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5a3d5f2-da3d-4d86-8345-9cec87911571", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411435300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac26445-495b-4fcf-b2f7-1acd3f3df362", "name": "entry : default@ConfigureCmake cost memory 0.0360260009765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411592800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da9df5ed-62dd-4067-ac6b-8b616cbe54e8", "name": "runTaskFromQueue task cost before running: 582 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411966500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a85f7427-15ca-4832-9fc6-3379ce10d966", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791411251200, "endTime": 33791412420500, "totalTime": 697500}, "additional": {"logType": "info", "children": [], "durationId": "3027c382-b8a7-4a35-ac5a-289398fd845a"}}, {"head": {"id": "fb15678e-91bd-4166-9246-296af93fef81", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791416698600, "endTime": 33791423217800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "a9a2a37a-9b2a-44be-8eda-84fa82357af1", "logId": "7d2e5f2e-c14f-47e4-83d6-608887216831"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9a2a37a-9b2a-44be-8eda-84fa82357af1", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791414736800}, "additional": {"logType": "detail", "children": [], "durationId": "fb15678e-91bd-4166-9246-296af93fef81"}}, {"head": {"id": "c6d6e30d-4e89-4110-b7b8-f6b637905048", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791415196000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30f8f60-105d-40ca-86a2-b959ac8ebee5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791415736000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34e2c291-418f-4458-9e74-6d349eaeb3b9", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791416711400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52087323-dcdf-4374-b333-76c0b17f3240", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791422163600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bf0be4c-2353-4fe6-93c7-cceafd9a9a47", "name": "entry : default@MergeProfile cost memory 0.10512542724609375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791423044400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2e5f2e-c14f-47e4-83d6-608887216831", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791416698600, "endTime": 33791423217800}, "additional": {"logType": "info", "children": [], "durationId": "fb15678e-91bd-4166-9246-296af93fef81"}}, {"head": {"id": "34cef30f-a4cc-4e6c-b227-ce3841e404da", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791427630400, "endTime": 33791436427200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dcf402c3-1463-446b-b6a6-019223d16ca6", "logId": "a2a6aec1-095b-4c1f-8d07-96261a211215"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcf402c3-1463-446b-b6a6-019223d16ca6", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791425974000}, "additional": {"logType": "detail", "children": [], "durationId": "34cef30f-a4cc-4e6c-b227-ce3841e404da"}}, {"head": {"id": "29306796-91c6-469a-8d99-7c9abb512f76", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791426480100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32b426a9-a935-4c46-be10-ea89a31c2562", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791426681000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f830bf-9e01-4326-84be-c1ccdee3a164", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791427644300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0742c214-0669-46a7-88bc-9224813e0093", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791429062900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77f889d8-ce8b-44ae-8733-eb350c2b5b54", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791433843000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07c78133-a8e3-4ccb-9568-f6bda5f7b7eb", "name": "entry : default@CreateBuildProfile cost memory 0.1024322509765625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791435003800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2a6aec1-095b-4c1f-8d07-96261a211215", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791427630400, "endTime": 33791436427200}, "additional": {"logType": "info", "children": [], "durationId": "34cef30f-a4cc-4e6c-b227-ce3841e404da"}}, {"head": {"id": "c93131e7-e5d1-4818-9f1c-325b3eb0a5eb", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791443220900, "endTime": 33791444950800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "41e28c71-14f9-40e2-8466-3589d2c44c84", "logId": "4586b92e-5cc8-470b-9e46-d2cee69c0700"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41e28c71-14f9-40e2-8466-3589d2c44c84", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791440760100}, "additional": {"logType": "detail", "children": [], "durationId": "c93131e7-e5d1-4818-9f1c-325b3eb0a5eb"}}, {"head": {"id": "198c7a57-d53a-4e57-aafc-0ddf6c964128", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791441192900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "542e501a-bd55-46b5-9dce-20db56bda798", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791441339600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b423a0-d40a-49e7-be2c-b376e7344845", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791443248600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da2245e8-ac16-4d91-b35a-e804863c3b2b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791444062100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85cd1d95-f7a3-4b60-86e5-7f4d3703066d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791444192300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adddf842-ef7d-4ad0-931f-b1aad230d1f0", "name": "entry : default@PreCheckSyscap cost memory 0.03624725341796875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791444585600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc7bc3e-4226-404b-9bdf-d3c35d8f9300", "name": "runTaskFromQueue task cost before running: 614 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791444811100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4586b92e-5cc8-470b-9e46-d2cee69c0700", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791443220900, "endTime": 33791444950800, "totalTime": 1557700}, "additional": {"logType": "info", "children": [], "durationId": "c93131e7-e5d1-4818-9f1c-325b3eb0a5eb"}}, {"head": {"id": "63145130-d47b-45c5-b185-7a77cbfc0fa0", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791470719300, "endTime": 33791472053700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fa184478-17f6-4de2-b761-e0b17a386a03", "logId": "ef8934bd-e04a-4e43-9aad-a99cdb0ffdd8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa184478-17f6-4de2-b761-e0b17a386a03", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791455060200}, "additional": {"logType": "detail", "children": [], "durationId": "63145130-d47b-45c5-b185-7a77cbfc0fa0"}}, {"head": {"id": "6a7f78e0-8165-4481-a4a3-37128dedd803", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791457042300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2d1c7ec-89d1-4528-9d6a-c1cbbc8d9f7c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791457189500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce995899-714f-4aa1-969e-908f492b5c32", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791470739300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55252d1a-9881-4ced-99b7-051c168dde80", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791471101800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80aad9fe-649d-42ef-aa2c-9fffca790886", "name": "entry : default@GeneratePkgContextInfo cost memory 0.03832244873046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791471648500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b13a6ac4-7497-442e-8a13-dbd6c101f904", "name": "runTaskFromQueue task cost before running: 642 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791471984200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8934bd-e04a-4e43-9aad-a99cdb0ffdd8", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791470719300, "endTime": 33791472053700, "totalTime": 1241900}, "additional": {"logType": "info", "children": [], "durationId": "63145130-d47b-45c5-b185-7a77cbfc0fa0"}}, {"head": {"id": "1f7da4e8-f42b-4e16-9c60-b812d9a4da04", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791481953400, "endTime": 33791492067300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist."], "detailId": "b2d11a43-8cb4-45e4-a65f-44ffd806e9a8", "logId": "e267b6dd-7569-4e0e-8b51-3c2b25d5e146"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2d11a43-8cb4-45e4-a65f-44ffd806e9a8", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791476019100}, "additional": {"logType": "detail", "children": [], "durationId": "1f7da4e8-f42b-4e16-9c60-b812d9a4da04"}}, {"head": {"id": "fa23f561-bf69-4e51-a04d-d03de3ef2923", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791476569100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78295a8d-dbde-423b-8b47-d44a2703d65b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791476742500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84e7ada9-f5b3-4ab4-91d5-98c4f92101c6", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791481968800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afccb203-56b3-4f47-8b28-f37390bb9543", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791490686000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf33ffb1-dee1-400f-9289-c8bec3cc6f08", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791490864100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d52349-c46d-42b5-96f8-ad10f4fd9ab1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791491102700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "484ab17c-fe37-4376-9bee-c7f51e284858", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791491562600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09b80a99-1791-4eac-a6c6-bd1f215caed9", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1178741455078125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791491737600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66778af1-cb31-4f18-9bb5-6653c1ad1e19", "name": "runTaskFromQueue task cost before running: 661 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791491854900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e267b6dd-7569-4e0e-8b51-3c2b25d5e146", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791481953400, "endTime": 33791492067300, "totalTime": 9880900}, "additional": {"logType": "info", "children": [], "durationId": "1f7da4e8-f42b-4e16-9c60-b812d9a4da04"}}, {"head": {"id": "1aa28fb6-9fac-483e-b54e-7280e9508f50", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791502315500, "endTime": 33791505237900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5c12e70f-c279-4b33-93db-8c823bdcc3cf", "logId": "29b9b552-5919-442b-aa68-fbbd27043db2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c12e70f-c279-4b33-93db-8c823bdcc3cf", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791496061600}, "additional": {"logType": "detail", "children": [], "durationId": "1aa28fb6-9fac-483e-b54e-7280e9508f50"}}, {"head": {"id": "3c4e0da8-3747-4634-939b-3d1c6f23ebcb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791497220100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b204a1db-fbcb-4104-a2a9-64b03d5db462", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791498360600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15083fcd-1c74-4ddc-b424-d9f9e06cfb1d", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791502329500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de22f2a7-7f8f-4cb1-9bb5-bd69f4a23149", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791503016100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d03334-7736-4629-90b1-c3a0cbf62379", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791503394500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e02f3e4-74d4-4777-9f33-191941be3d06", "name": "entry : default@BuildNativeWithCmake cost memory 0.0374908447265625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791504384300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46b92e80-5268-4c4c-9a37-1d3eec633def", "name": "runTaskFromQueue task cost before running: 674 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791504535700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29b9b552-5919-442b-aa68-fbbd27043db2", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791502315500, "endTime": 33791505237900, "totalTime": 2194200}, "additional": {"logType": "info", "children": [], "durationId": "1aa28fb6-9fac-483e-b54e-7280e9508f50"}}, {"head": {"id": "82b83426-67db-417a-8b57-1c3c60e46de6", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791511730800, "endTime": 33791520613600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "13889355-02e5-4b8b-9722-5b9b721c913e", "logId": "59c6be5a-c1fa-4130-9422-7c5fa1f860ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13889355-02e5-4b8b-9722-5b9b721c913e", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791509243300}, "additional": {"logType": "detail", "children": [], "durationId": "82b83426-67db-417a-8b57-1c3c60e46de6"}}, {"head": {"id": "a5bddfbb-5fee-4c1c-a8a9-930ae68a4c94", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791509819500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfdcea55-fdbf-4d63-96b4-308ae7f02069", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791509937300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a36797-b2f1-49d8-a66b-4854950f0344", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791511745800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9299a5b1-ac76-4c4e-87ee-58f7f001f54a", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791520392700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86327932-c8df-4390-986e-d888dbd5663c", "name": "entry : default@MakePackInfo cost memory 0.13845062255859375", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791520537600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59c6be5a-c1fa-4130-9422-7c5fa1f860ff", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791511730800, "endTime": 33791520613600}, "additional": {"logType": "info", "children": [], "durationId": "82b83426-67db-417a-8b57-1c3c60e46de6"}}, {"head": {"id": "fb3cf8e1-426f-4fbc-836d-41710f43cbc7", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791526872300, "endTime": 33791530314000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist."], "detailId": "be175094-02c2-4ca6-ad99-14bb76d69207", "logId": "38289aba-e0b2-49b9-a0ac-a2240906760a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be175094-02c2-4ca6-ad99-14bb76d69207", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791524460500}, "additional": {"logType": "detail", "children": [], "durationId": "fb3cf8e1-426f-4fbc-836d-41710f43cbc7"}}, {"head": {"id": "a5dfac0e-97c8-49be-81ed-e8140920ecd5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791525053400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd4ff39-009f-432d-99fe-0e19ddaad94e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791525336600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733650d6-c7b5-466d-80fd-0422f0432e6c", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791526884500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d907af74-e10c-43c3-8ee8-ca80ea8c154c", "name": "File: 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791527041300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a7b34c-3fcf-4732-9134-1fe80939332d", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791527819500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0550c149-f3f7-49a6-a765-470f087c7102", "name": "entry:default@SyscapTransform is not up-to-date, since the output file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791529746900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4e4538-8569-4edd-b7a6-1af20126cc61", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791529893600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1205cc3e-fe38-4c4c-a772-229277b2abce", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791529994200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7746ba00-5bf0-4d85-9ccb-9b0f7c5784f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791530050300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89afd17a-6e5e-4a7c-87cc-d93590d6043b", "name": "entry : default@SyscapTransform cost memory 0.1513824462890625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791530163700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "834f4282-7417-40da-85f7-32a9fab1320c", "name": "runTaskFromQueue task cost before running: 700 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791530256300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38289aba-e0b2-49b9-a0ac-a2240906760a", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791526872300, "endTime": 33791530314000, "totalTime": 3367000}, "additional": {"logType": "info", "children": [], "durationId": "fb3cf8e1-426f-4fbc-836d-41710f43cbc7"}}, {"head": {"id": "84589a0e-8db8-4cfc-adf3-680fdb1b7f1c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791534720500, "endTime": 33791536164300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "61533e76-6bae-4bcc-814b-b9b6603fa4aa", "logId": "0d2de767-d765-4dfa-8cb0-58f373574237"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61533e76-6bae-4bcc-814b-b9b6603fa4aa", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791532413200}, "additional": {"logType": "detail", "children": [], "durationId": "84589a0e-8db8-4cfc-adf3-680fdb1b7f1c"}}, {"head": {"id": "c1e6c3dd-d759-4417-85b0-aeca440688c1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791532891500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4eee921-1f8d-48ad-acf1-bded0ce6bff4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791533010800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3355e512-e1a8-4a67-bfd4-47b5e5048e1b", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791534737300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f98c5945-833c-4572-886d-7ed05beed475", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791535848900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4da8cd-4844-4a1e-9903-958dede50f5a", "name": "entry : default@ProcessProfile cost memory 0.05956268310546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791536055900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2de767-d765-4dfa-8cb0-58f373574237", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791534720500, "endTime": 33791536164300}, "additional": {"logType": "info", "children": [], "durationId": "84589a0e-8db8-4cfc-adf3-680fdb1b7f1c"}}, {"head": {"id": "d0288148-ded6-4c2d-85f3-74cd875e3cda", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791543641000, "endTime": 33791550751500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a0269a5f-fd9a-4e8c-9cc2-4f15e065869d", "logId": "ae62f1c3-fff7-4068-93e4-453bbf30bbb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0269a5f-fd9a-4e8c-9cc2-4f15e065869d", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791540539600}, "additional": {"logType": "detail", "children": [], "durationId": "d0288148-ded6-4c2d-85f3-74cd875e3cda"}}, {"head": {"id": "63f506c6-d970-43e9-847d-30b643f4fe84", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791541301400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eefaa65d-cc35-4d55-8fab-5bfd762c9066", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791541435100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "949ac642-04c6-4c35-b7e8-4887d5bf8ce5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791543654300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86707801-6a48-4305-b1f7-69817e73ba9a", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791550540200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a092527-2d1b-4f49-b141-b5f9659be960", "name": "entry : default@ProcessRouterMap cost memory 0.20146942138671875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791550680600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae62f1c3-fff7-4068-93e4-453bbf30bbb2", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791543641000, "endTime": 33791550751500}, "additional": {"logType": "info", "children": [], "durationId": "d0288148-ded6-4c2d-85f3-74cd875e3cda"}}, {"head": {"id": "1afea72d-f513-47f5-b35a-db182d968848", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791556874200, "endTime": 33791558816400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "ecd6f0ec-e3b1-46fc-9c7d-9873078b334a", "logId": "a5b6f296-e433-4e29-ada5-4f2f83c133cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ecd6f0ec-e3b1-46fc-9c7d-9873078b334a", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791554625300}, "additional": {"logType": "detail", "children": [], "durationId": "1afea72d-f513-47f5-b35a-db182d968848"}}, {"head": {"id": "5e6a878d-f52f-4c3d-bd08-e396f9f2b45d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791555453500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d315665-936f-4d4f-93f2-b597476416ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791555807400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a618f471-77d1-48fe-8d8d-24be5c87e298", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791556885700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa6793a-7c44-49d7-880b-2186d6bb18d5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791557033000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "964cec90-dfdb-4e38-83eb-78cebe8eda6c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791557542700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3ad727-9521-465e-8d59-fc72f052da2a", "name": "entry : default@BuildNativeWithNinja cost memory 0.0567779541015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791558604500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bdd7e2b-3967-4e29-95c6-3782ee495e67", "name": "runTaskFromQueue task cost before running: 728 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791558748200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5b6f296-e433-4e29-ada5-4f2f83c133cc", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791556874200, "endTime": 33791558816400, "totalTime": 1850700}, "additional": {"logType": "info", "children": [], "durationId": "1afea72d-f513-47f5-b35a-db182d968848"}}, {"head": {"id": "b8b4da41-b9ef-4517-85af-65d65c9070b8", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791565013700, "endTime": 33791574007000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "c9c9efe6-cce7-4376-b921-ba0d7c5c71e1", "logId": "24a5a0b9-2e86-4acc-96e8-d20fbf6366bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9c9efe6-cce7-4376-b921-ba0d7c5c71e1", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791561291900}, "additional": {"logType": "detail", "children": [], "durationId": "b8b4da41-b9ef-4517-85af-65d65c9070b8"}}, {"head": {"id": "a0d88c43-f02a-4715-a3dc-441a18c0dd77", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791561929000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a61b453-343d-4e73-9a95-8dc8b7abd246", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791562071000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52974caf-40bf-495d-8c0b-f043c30a36da", "name": "restool module names: entry; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791563499100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b3bc509-f129-4fce-a836-f29ea4ac9c57", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791568030600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bd17304-b4c7-449b-95e5-0f7562cc87a1", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791572132900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b7d979-d355-4e50-b9f1-ccdbed43bbc0", "name": "entry : default@ProcessResource cost memory 0.16941070556640625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791572268700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a5a0b9-2e86-4acc-96e8-d20fbf6366bc", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791565013700, "endTime": 33791574007000}, "additional": {"logType": "info", "children": [], "durationId": "b8b4da41-b9ef-4517-85af-65d65c9070b8"}}, {"head": {"id": "1d496bf9-c22b-4ee6-bb2c-4a543bcd0315", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791583762800, "endTime": 33791610579300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8c05c997-1441-494e-824a-b504ffd00ca5", "logId": "489f44d5-1dd1-4c67-b310-9e1f439499e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c05c997-1441-494e-824a-b504ffd00ca5", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791578984200}, "additional": {"logType": "detail", "children": [], "durationId": "1d496bf9-c22b-4ee6-bb2c-4a543bcd0315"}}, {"head": {"id": "640a2126-9aeb-49c6-89d8-53417e297693", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791579632900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46fd70d2-c12b-4c83-947b-a42b4a9d2984", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791579751000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8230f6ca-430b-42d3-abfc-e5f3339d5fbe", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791583775100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "948679a6-e246-4ac7-aff8-1b04575979a0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791609871300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145acf52-a73e-4dd5-9a29-1d56a6ae1f2d", "name": "entry : default@GenerateLoaderJson cost memory 0.76336669921875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791610416700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489f44d5-1dd1-4c67-b310-9e1f439499e2", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791583762800, "endTime": 33791610579300}, "additional": {"logType": "info", "children": [], "durationId": "1d496bf9-c22b-4ee6-bb2c-4a543bcd0315"}}, {"head": {"id": "8f0b7a2b-8cdd-46c1-a12c-45a7d21c2f75", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791620794600, "endTime": 33791625019100}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "0f6fcb2c-3aa0-4350-9621-217c4c1d060f", "logId": "e2c9329b-7581-45ae-8922-6daabc1f918c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f6fcb2c-3aa0-4350-9621-217c4c1d060f", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791619599200}, "additional": {"logType": "detail", "children": [], "durationId": "8f0b7a2b-8cdd-46c1-a12c-45a7d21c2f75"}}, {"head": {"id": "4a1b23d7-bfa6-4ce6-8604-b296b8fb70eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791620006800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b85327e-ecd6-4c09-b34c-de874d65883e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791620130300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "548a8c80-b595-4fdb-bfea-fafa967a22de", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791620807100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72458c01-bd42-40b5-9bb2-ac63dd400291", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791623407500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "788e0fa1-7187-48ed-874c-810d87e35de8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791623529400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca38c81f-26ee-4ca5-aa56-82cc85656dde", "name": "entry : default@ProcessLibs cost memory 0.12940216064453125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791624791100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "713ccb29-6a4a-4430-98da-5da80b51ef5e", "name": "runTaskFromQueue task cost before running: 795 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791624947600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c9329b-7581-45ae-8922-6daabc1f918c", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791620794600, "endTime": 33791625019100, "totalTime": 4122700}, "additional": {"logType": "info", "children": [], "durationId": "8f0b7a2b-8cdd-46c1-a12c-45a7d21c2f75"}}, {"head": {"id": "2904af0f-520d-4002-895c-cd185da331a8", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791633616000, "endTime": 33791653062200}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "6b4fd8ce-ff48-4b92-a393-d43ffefe775d", "logId": "fbf39cd9-644c-49b4-9aa7-cd5c2067d402"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b4fd8ce-ff48-4b92-a393-d43ffefe775d", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791627647500}, "additional": {"logType": "detail", "children": [], "durationId": "2904af0f-520d-4002-895c-cd185da331a8"}}, {"head": {"id": "7841c4af-c4eb-459b-9e9f-5975f9c7ddb9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791628025200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f384414-aebc-4e76-9ef3-74b76379033d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791628160900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfba87f3-9228-434d-bcc7-45aebb63c63e", "name": "restool module names: entry; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791628862900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f64d9cce-4d98-480c-bc8b-dc7fe4b39187", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791633644000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a5e48e-1cf9-4f4e-8c83-65eebf4fc78c", "name": "Incremental task entry:default@CompileResource pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791652850500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "febb9436-15ad-4d5f-b9be-a453133f6cef", "name": "entry : default@CompileResource cost memory 1.4074859619140625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791652980900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbf39cd9-644c-49b4-9aa7-cd5c2067d402", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791633616000, "endTime": 33791653062200}, "additional": {"logType": "info", "children": [], "durationId": "2904af0f-520d-4002-895c-cd185da331a8"}}, {"head": {"id": "f2e6b606-0547-41d0-81e0-a8418ff8979f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791659187400, "endTime": 33791660302500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f063d352-e2b1-42c3-9c43-17c6202a61d3", "logId": "8d13ade4-0920-4b07-9a98-161d74343798"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f063d352-e2b1-42c3-9c43-17c6202a61d3", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791656068900}, "additional": {"logType": "detail", "children": [], "durationId": "f2e6b606-0547-41d0-81e0-a8418ff8979f"}}, {"head": {"id": "dd50a2d2-b649-4ee0-81f6-d08db59f751f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791656540100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb820738-0f79-4de1-a313-624ab22d45ce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791656703000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "142d2a5c-7a38-4b7f-91d5-f96179125157", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791659202800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d12b8dd-0de2-407f-b881-096df43f797f", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791659455000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008a49b0-1702-40a1-8d69-4f9472b86af7", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791660152800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c2158b4-fa7f-466c-86e2-3d1cee422d9c", "name": "entry : default@DoNativeStrip cost memory 0.07440948486328125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791660238600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d13ade4-0920-4b07-9a98-161d74343798", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791659187400, "endTime": 33791660302500}, "additional": {"logType": "info", "children": [], "durationId": "f2e6b606-0547-41d0-81e0-a8418ff8979f"}}, {"head": {"id": "0ce3aa29-05ce-4723-b518-5ab02a7787da", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791665976300, "endTime": 33793511273800}, "additional": {"children": ["85410423-ae03-42c6-a0b9-e4fc0d9b1108", "2a54c496-21b6-473d-8763-1d550c7477b6"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed."], "detailId": "1b4132ac-59e3-4f1d-9d92-53e281c68b4b", "logId": "7a55f236-bb6e-4a07-936a-f8bc22739750"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b4132ac-59e3-4f1d-9d92-53e281c68b4b", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791661697200}, "additional": {"logType": "detail", "children": [], "durationId": "0ce3aa29-05ce-4723-b518-5ab02a7787da"}}, {"head": {"id": "ed54350f-7da3-4f57-9430-c268ee781991", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791662069000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48bda580-1ab6-4c07-975f-6b03579ae6ae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791662234300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9888f4b2-0535-4a99-a4e9-f0edd332e843", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791665989100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8382f9a3-2532-4db3-8ef5-aa5fb0737408", "name": "entry:default@CompileArkTS is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791677396400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abaca81f-6b8f-4bba-abef-088d7e03b44f", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 9 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791677535200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ab8269f-da42-4bce-a93d-df9ca04bf5a9", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791691688200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d47dc1-9a43-43da-aa6c-d122e6c38b4e", "name": "Compile arkts with external api path: C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\hms\\ets", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791692230300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5700c851-8bcf-418a-a8b7-0711dc1f6fe7", "name": "default@CompileArkTS work[103] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791693204800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33791795476000, "endTime": 33793504435800}, "additional": {"children": ["cc7f7da8-7969-4a83-bf10-cecbb7f1bb34", "bed351c4-fd98-44c0-8b93-40571e084068", "a371bc68-7790-415a-ba9f-c38ccfe45123", "efb7e1a5-c785-4cf3-b547-4630c0a57349", "ce9e82cc-17a0-42db-8839-87f66de7d20c", "4c403290-9da2-4df8-b1f9-364e4e305be3"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0ce3aa29-05ce-4723-b518-5ab02a7787da", "logId": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13053e29-06c2-4e6b-8b3d-2213cc2e7a06", "name": "default@CompileArkTS work[103] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791693890600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56416be1-8fd1-4c20-88ed-f6a9ce3adbdf", "name": "default@CompileArkTS work[103] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791693982500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f465ffa-974d-4f57-a489-709c4c09f0e3", "name": "CopyResources startTime: 33791694039100", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791694041000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7396fe04-3ddd-4e58-9c9b-5c46bf30d76c", "name": "default@CompileArkTS work[104] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791694093300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a54c496-21b6-473d-8763-1d550c7477b6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33793118918600, "endTime": 33793131813000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "0ce3aa29-05ce-4723-b518-5ab02a7787da", "logId": "c7aa525c-f248-4779-818f-b760a4f25f41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3be59854-58c9-4ad8-a7f8-754d254ba40a", "name": "default@CompileArkTS work[104] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791694993900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ac8689-10bc-4520-8e7d-66a5170e3cc2", "name": "default@CompileArkTS work[104] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791695086500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e896c05f-1b48-47b5-813d-bb4c3ae7c03a", "name": "entry : default@CompileArkTS cost memory 1.58856201171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791695315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb27b77c-a275-44fb-86c1-c7c01df0c3a8", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791700485800, "endTime": 33791703247800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "bba19672-8745-4984-a8c4-8b410a516300", "logId": "32db16c7-6f70-4740-a29e-4bcf9e00fa58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bba19672-8745-4984-a8c4-8b410a516300", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791696568100}, "additional": {"logType": "detail", "children": [], "durationId": "cb27b77c-a275-44fb-86c1-c7c01df0c3a8"}}, {"head": {"id": "aa98d0dd-3e0c-45e5-bdf2-b20d5ecd3107", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791696977300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86613892-5bbe-49b9-85df-f1a51c11baf9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791697056000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad03157f-c26b-46db-941f-3d045ec60e61", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791700497100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fa1e33d-3f85-4b3b-bc2d-c67de95090cb", "name": "entry : default@BuildJS cost memory 0.12686920166015625", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791703031900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ca17c37-4dd4-436c-aba6-f8470e90ec14", "name": "runTaskFromQueue task cost before running: 873 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791703170500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32db16c7-6f70-4740-a29e-4bcf9e00fa58", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791700485800, "endTime": 33791703247800, "totalTime": 2661900}, "additional": {"logType": "info", "children": [], "durationId": "cb27b77c-a275-44fb-86c1-c7c01df0c3a8"}}, {"head": {"id": "ef58506f-8cb3-4fab-a48b-3ae2604adcc1", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791707244500, "endTime": 33791708812000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4dfe84f6-2869-47e9-a1a9-2650276da89f", "logId": "dcf9716c-4df1-40c2-a0f9-31f29f8e0d27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dfe84f6-2869-47e9-a1a9-2650276da89f", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791704843900}, "additional": {"logType": "detail", "children": [], "durationId": "ef58506f-8cb3-4fab-a48b-3ae2604adcc1"}}, {"head": {"id": "2f52a1c1-971e-4700-8912-9996c9667d4d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791705211600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd18987-a68e-474c-8bb9-ee2a9f553e2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791705310300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c668df63-96ef-4e83-8f06-06e386118601", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791707253300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7f244a3-9ea7-458c-be78-3dc266c94f87", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791707564400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91f2a378-d4b1-4aa6-afd4-741772adaef1", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791708630700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "218de096-9ab2-4754-b2bf-3797bb1e1f9c", "name": "entry : default@CacheNativeLibs cost memory 0.08788299560546875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791708744100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcf9716c-4df1-40c2-a0f9-31f29f8e0d27", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791707244500, "endTime": 33791708812000}, "additional": {"logType": "info", "children": [], "durationId": "ef58506f-8cb3-4fab-a48b-3ae2604adcc1"}}, {"head": {"id": "d5cd1639-986c-4da0-927d-c18277e8ba57", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791795029700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fcbe111-e202-481a-a131-c0b18fdea526", "name": "default@CompileArkTS work[103] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791795343100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c71fbc49-0f39-47af-82a5-6ca92304284c", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791795449600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86cee70e-d2ef-4313-8a61-0dfb3fdabf43", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791795536700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6fafac5-d9aa-421e-a896-8d15c54ca0b0", "name": "Create  resident worker with id: 3.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791796054300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e8933a9-adfd-40ca-a778-9c13cb3a2217", "name": "default@CompileArkTS work[104] has been dispatched to worker[3].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791798035400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9ba24ee-c805-4f14-83ba-919d64f4da78", "name": "worker[3] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793132615200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8ee02df-f238-444e-8da2-0c2d9529159d", "name": "CopyResources is end, endTime: 33793132870800", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793132878000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22dd9d87-f2b4-4a22-a85d-d97e0b263721", "name": "default@CompileArkTS work[104] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793132988500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7aa525c-f248-4779-818f-b760a4f25f41", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker3", "startTime": 33793118918600, "endTime": 33793131813000}, "additional": {"logType": "info", "children": [], "durationId": "2a54c496-21b6-473d-8763-1d550c7477b6", "parent": "7a55f236-bb6e-4a07-936a-f8bc22739750"}}, {"head": {"id": "b6200af8-60a1-444c-a94f-6b0d57d27278", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793236406100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f399a634-1d34-44eb-96b1-ef7807e24e39", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793504756100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc7f7da8-7969-4a83-bf10-cecbb7f1bb34", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33791795555500, "endTime": 33791799409800}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "logId": "d12f9d48-6907-4185-87c9-56dc9f47c9c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d12f9d48-6907-4185-87c9-56dc9f47c9c2", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791795555500, "endTime": 33791799409800}, "additional": {"logType": "info", "children": [], "durationId": "cc7f7da8-7969-4a83-bf10-cecbb7f1bb34", "parent": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}}, {"head": {"id": "bed351c4-fd98-44c0-8b93-40571e084068", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33791799427300, "endTime": 33791799587700}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "logId": "4d15fa82-1b34-4510-98e4-33637e462688"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4d15fa82-1b34-4510-98e4-33637e462688", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791799427300, "endTime": 33791799587700}, "additional": {"logType": "info", "children": [], "durationId": "bed351c4-fd98-44c0-8b93-40571e084068", "parent": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}}, {"head": {"id": "a371bc68-7790-415a-ba9f-c38ccfe45123", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33791799610400, "endTime": 33791799650300}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "logId": "87e8fc1c-a860-4440-9be4-41df82e6381c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e8fc1c-a860-4440-9be4-41df82e6381c", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791799610400, "endTime": 33791799650300}, "additional": {"logType": "info", "children": [], "durationId": "a371bc68-7790-415a-ba9f-c38ccfe45123", "parent": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}}, {"head": {"id": "efb7e1a5-c785-4cf3-b547-4630c0a57349", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33791799670800, "endTime": 33793412051600}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "logId": "80286b0c-82bf-4cf9-8927-868b919e63df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "80286b0c-82bf-4cf9-8927-868b919e63df", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791799670800, "endTime": 33793412051600}, "additional": {"logType": "info", "children": [], "durationId": "efb7e1a5-c785-4cf3-b547-4630c0a57349", "parent": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}}, {"head": {"id": "ce9e82cc-17a0-42db-8839-87f66de7d20c", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793412070200, "endTime": 33793416157100}, "additional": {"children": ["7754cc81-0c44-425d-9f38-3284151ce783", "f1faaf6b-84f6-48f8-9ca1-3310afae55e8", "73d7a7bd-f667-4d41-b99d-90be8248bd56"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "logId": "667b0d1b-0820-411f-a5b5-b34cd86d6104"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "667b0d1b-0820-411f-a5b5-b34cd86d6104", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793412070200, "endTime": 33793416157100}, "additional": {"logType": "info", "children": ["7261d115-c679-44e0-87f6-c4e791aab4b6", "dec83a9f-940d-4aa1-9ef9-4d7a87dd5ac0", "d7bd6598-0869-44d0-b8f8-ee989224f043"], "durationId": "ce9e82cc-17a0-42db-8839-87f66de7d20c", "parent": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}}, {"head": {"id": "7754cc81-0c44-425d-9f38-3284151ce783", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793412092800, "endTime": 33793412097900}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce9e82cc-17a0-42db-8839-87f66de7d20c", "logId": "7261d115-c679-44e0-87f6-c4e791aab4b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7261d115-c679-44e0-87f6-c4e791aab4b6", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793412092800, "endTime": 33793412097900}, "additional": {"logType": "info", "children": [], "durationId": "7754cc81-0c44-425d-9f38-3284151ce783", "parent": "667b0d1b-0820-411f-a5b5-b34cd86d6104"}}, {"head": {"id": "f1faaf6b-84f6-48f8-9ca1-3310afae55e8", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793412101100, "endTime": 33793413466500}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce9e82cc-17a0-42db-8839-87f66de7d20c", "logId": "dec83a9f-940d-4aa1-9ef9-4d7a87dd5ac0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dec83a9f-940d-4aa1-9ef9-4d7a87dd5ac0", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793412101100, "endTime": 33793413466500}, "additional": {"logType": "info", "children": [], "durationId": "f1faaf6b-84f6-48f8-9ca1-3310afae55e8", "parent": "667b0d1b-0820-411f-a5b5-b34cd86d6104"}}, {"head": {"id": "73d7a7bd-f667-4d41-b99d-90be8248bd56", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793413469800, "endTime": 33793416117400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "ce9e82cc-17a0-42db-8839-87f66de7d20c", "logId": "d7bd6598-0869-44d0-b8f8-ee989224f043"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7bd6598-0869-44d0-b8f8-ee989224f043", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793413469800, "endTime": 33793416117400}, "additional": {"logType": "info", "children": [], "durationId": "73d7a7bd-f667-4d41-b99d-90be8248bd56", "parent": "667b0d1b-0820-411f-a5b5-b34cd86d6104"}}, {"head": {"id": "4c403290-9da2-4df8-b1f9-364e4e305be3", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793416175500, "endTime": 33793504293200}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "logId": "9b335a66-babc-42ee-ad76-00533b17f640"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9b335a66-babc-42ee-ad76-00533b17f640", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793416175500, "endTime": 33793504293200}, "additional": {"logType": "info", "children": [], "durationId": "4c403290-9da2-4df8-b1f9-364e4e305be3", "parent": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d"}}, {"head": {"id": "c585db7e-5ac7-4096-8687-f7c2964ee8a2", "name": "default@CompileArkTS work[103] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793511052500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9ae6835-90dc-41c7-b9e3-f53067b4a99d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33791795476000, "endTime": 33793504435800}, "additional": {"logType": "info", "children": ["d12f9d48-6907-4185-87c9-56dc9f47c9c2", "4d15fa82-1b34-4510-98e4-33637e462688", "87e8fc1c-a860-4440-9be4-41df82e6381c", "80286b0c-82bf-4cf9-8927-868b919e63df", "667b0d1b-0820-411f-a5b5-b34cd86d6104", "9b335a66-babc-42ee-ad76-00533b17f640"], "durationId": "85410423-ae03-42c6-a0b9-e4fc0d9b1108", "parent": "7a55f236-bb6e-4a07-936a-f8bc22739750"}}, {"head": {"id": "b971f86f-3430-4b5d-af3f-55cd139bea9a", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793511201400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a55f236-bb6e-4a07-936a-f8bc22739750", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33791665976300, "endTime": 33793511273800, "totalTime": 1738382000}, "additional": {"logType": "info", "children": ["a9ae6835-90dc-41c7-b9e3-f53067b4a99d", "c7aa525c-f248-4779-818f-b760a4f25f41"], "durationId": "0ce3aa29-05ce-4723-b518-5ab02a7787da"}}, {"head": {"id": "c89e18c6-f9d6-4f2c-8a40-0a547cdb5c13", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793516780400, "endTime": 33793518075500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "07bd31a8-cfd9-4577-a2d8-6583fc719546", "logId": "4d72bcb5-d375-4f3a-918a-ad267c01e79e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07bd31a8-cfd9-4577-a2d8-6583fc719546", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793514919400}, "additional": {"logType": "detail", "children": [], "durationId": "c89e18c6-f9d6-4f2c-8a40-0a547cdb5c13"}}, {"head": {"id": "a37be726-d963-4834-800e-9bd1766397bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793515392200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dc6bc06-1207-41e8-9ea9-e711172e8cea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793515569800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "839b2bf8-3aaa-460a-bcee-bbf43f738244", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793516791800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f955bd-eb9c-45f0-8161-190e58651ddc", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793517025800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72a4c41a-eb6a-486e-80f8-77ee3857fe62", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793517901000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ac8077c-81fd-410d-9879-33aa0ff45f06", "name": "entry : default@GeneratePkgModuleJson cost memory 0.07257843017578125", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793518008600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d72bcb5-d375-4f3a-918a-ad267c01e79e", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793516780400, "endTime": 33793518075500}, "additional": {"logType": "info", "children": [], "durationId": "c89e18c6-f9d6-4f2c-8a40-0a547cdb5c13"}}, {"head": {"id": "780c4b73-2a06-4190-a48d-ed0ec750666d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793526650400, "endTime": 33793993916500}, "additional": {"children": ["a68c3c38-12a5-494e-b22a-83935224c9e5", "5e47cc8a-f87f-44a6-80d4-48c034fe69da", "36db3eec-ed96-4f5a-ba70-d0943fa62baf"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed."], "detailId": "7e109420-5a7b-4d47-bbe3-744caa057aab", "logId": "3a1538db-43da-4416-95cf-00e37d3b449a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e109420-5a7b-4d47-bbe3-744caa057aab", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793520331300}, "additional": {"logType": "detail", "children": [], "durationId": "780c4b73-2a06-4190-a48d-ed0ec750666d"}}, {"head": {"id": "df08fbab-f093-4c4e-bbd4-1f5c92ff8114", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793520637600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "344f0655-3d9f-4c82-af33-f6a9b14b98f5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793520730200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5dcce258-05d4-4c7b-bc89-3f3d63c8eb8e", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793526660800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59801b67-f892-40bb-b625-3ab61288f462", "name": "entry:default@PackageHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793537894600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4e0421-fae9-4140-87c3-2b280a6e8d39", "name": "Incremental task entry:default@PackageHap pre-execution cost: 10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793538024500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60dadf74-5d30-4a9f-9796-2824850a8885", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793538129200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d15f287-3aae-4331-95d7-e83e9773950a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793538297400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68c3c38-12a5-494e-b22a-83935224c9e5", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793539414400, "endTime": 33793540840500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "780c4b73-2a06-4190-a48d-ed0ec750666d", "logId": "3c771280-3ce2-47dc-af5c-1499ee6076d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "682a7b8c-95b0-4803-bc27-14ad5aabd064", "name": "Use tool [C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\lib\\app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=GBK',\n  '-jar',\n  'C:\\\\Program Files\\\\Huawei\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\lib\\\\app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\stripped_native_libs\\\\default',\n  '--json-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\package\\\\default\\\\module.json',\n  '--resources-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources',\n  '--index-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\res\\\\default\\\\resources.index',\n  '--pack-info-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\pack.info',\n  '--out-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\outputs\\\\default\\\\entry-default-unsigned.hap',\n  '--ets-path',\n  'D:\\\\yysrc\\\\sourcecode\\\\mrhp-sharedoc\\\\2025\\\\SheShudu\\\\entry\\\\build\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\ets'\n]", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793540677000}, "additional": {"logType": "debug", "children": [], "durationId": "780c4b73-2a06-4190-a48d-ed0ec750666d"}}, {"head": {"id": "3c771280-3ce2-47dc-af5c-1499ee6076d6", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793539414400, "endTime": 33793540840500}, "additional": {"logType": "info", "children": [], "durationId": "a68c3c38-12a5-494e-b22a-83935224c9e5", "parent": "3a1538db-43da-4416-95cf-00e37d3b449a"}}, {"head": {"id": "5e47cc8a-f87f-44a6-80d4-48c034fe69da", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793541492000, "endTime": 33793543156400}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "780c4b73-2a06-4190-a48d-ed0ec750666d", "logId": "eb0575ef-c339-43c3-9c9a-d9d4d13c62c9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ba96cbb-73f2-4b41-8ec0-10b6eef6083b", "name": "default@PackageHap work[105] is submitted.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793542202200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36db3eec-ed96-4f5a-ba70-d0943fa62baf", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793543076500, "endTime": 33793993438600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "780c4b73-2a06-4190-a48d-ed0ec750666d", "logId": "c80f5f05-9f82-4133-8b0b-bf11ef2fa336"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14b4a2e8-afff-4155-be1a-1576e46ba062", "name": "default@PackageHap work[105] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793542841600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf33362b-055f-41eb-b28c-2e7bf4032d6b", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793542921300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d67987ad-d299-4082-aebf-15351eb080bf", "name": "default@PackageHap work[105] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793543013100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c65bdd1-7ef8-46bc-a4c3-acae1ef6e8ad", "name": "default@PackageHap work[105] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793543067600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb0575ef-c339-43c3-9c9a-d9d4d13c62c9", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793541492000, "endTime": 33793543156400}, "additional": {"logType": "info", "children": [], "durationId": "5e47cc8a-f87f-44a6-80d4-48c034fe69da", "parent": "3a1538db-43da-4416-95cf-00e37d3b449a"}}, {"head": {"id": "5d139f54-85ed-4fc6-b277-10a52ab321d8", "name": "entry : default@PackageHap cost memory 1.3072280883789062", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793547175600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32a32481-9416-4d0c-9e01-5f2f82ef2510", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793993525400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d47fbe9d-e45b-48a0-a828-ab82f1fef265", "name": "default@PackageHap work[105] done.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793993745600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c80f5f05-9f82-4133-8b0b-bf11ef2fa336", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Worker4", "startTime": 33793543076500, "endTime": 33793993438600}, "additional": {"logType": "info", "children": [], "durationId": "36db3eec-ed96-4f5a-ba70-d0943fa62baf", "parent": "3a1538db-43da-4416-95cf-00e37d3b449a"}}, {"head": {"id": "307b183a-6282-4c89-90cf-bc82b68702f9", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793993849000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1538db-43da-4416-95cf-00e37d3b449a", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793526650400, "endTime": 33793993916500, "totalTime": 466791100}, "additional": {"logType": "info", "children": ["3c771280-3ce2-47dc-af5c-1499ee6076d6", "eb0575ef-c339-43c3-9c9a-d9d4d13c62c9", "c80f5f05-9f82-4133-8b0b-bf11ef2fa336"], "durationId": "780c4b73-2a06-4190-a48d-ed0ec750666d"}}, {"head": {"id": "547c4f16-d8e8-49be-8b58-db5d0fecdc37", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794002428900, "endTime": 33794005198800}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed."], "detailId": "7c2573ac-1465-4176-98e3-60e849afdaa6", "logId": "a99e7257-d6ec-4cc1-a91f-b0ba435cc0d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c2573ac-1465-4176-98e3-60e849afdaa6", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793997610900}, "additional": {"logType": "detail", "children": [], "durationId": "547c4f16-d8e8-49be-8b58-db5d0fecdc37"}}, {"head": {"id": "52b969fa-7fab-486d-ad73-cb6386d427a6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793998055100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed8c21bf-1b17-491e-81d2-5971db9cddab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33793998314300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0211736a-4ffe-461a-946f-32229b06cf19", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794002441800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a3777a-e011-4c28-a61a-b4ca45906aef", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794002942800}, "additional": {"logType": "warn", "children": [], "durationId": "547c4f16-d8e8-49be-8b58-db5d0fecdc37"}}, {"head": {"id": "4b8ef415-dac2-4f18-8c4e-f6d5ae9eed54", "name": "entry:default@SignHap is not up-to-date, since the input file 'D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794004060700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "595bdc89-26aa-46f7-933e-b23de4200260", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794004249000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76122a97-dc06-4258-9daf-225261e22cb7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794004352200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f513ce9-4ffd-4b7f-a77a-607ec835e7e8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794004412500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23017c03-9361-491a-9bb4-d0d15222da31", "name": "entry : default@SignHap cost memory 0.1195526123046875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794005003300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eae8ce2-ba42-4872-9c24-3d5f398977e1", "name": "runTaskFromQueue task cost before running: 3 s 175 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794005111200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a99e7257-d6ec-4cc1-a91f-b0ba435cc0d7", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794002428900, "endTime": 33794005198800, "totalTime": 2660300}, "additional": {"logType": "info", "children": [], "durationId": "547c4f16-d8e8-49be-8b58-db5d0fecdc37"}}, {"head": {"id": "90d5345a-5853-4259-a5f9-535665af61b9", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794010821500, "endTime": 33794023696900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1865e1fb-dc72-4bf2-b612-48c500c75379", "logId": "251dccd3-825a-408f-a758-029d486fca77"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1865e1fb-dc72-4bf2-b612-48c500c75379", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794008938300}, "additional": {"logType": "detail", "children": [], "durationId": "90d5345a-5853-4259-a5f9-535665af61b9"}}, {"head": {"id": "23cfa1df-d3e3-4cf4-aaf9-f3625c69e1fa", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794009562900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49004c81-1718-4b82-a13e-6bec73e1896b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794009739000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "533f55da-7c90-4a2f-9427-2f168923bd6a", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794010834000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0add16c-0425-46d3-86f3-888f3ae875a8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794023188600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a15a93-a392-430f-91a3-07b79c4bf692", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794023318100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce6bc28-c893-419a-8f87-633c18f5c171", "name": "entry : default@CollectDebugSymbol cost memory 0.2615203857421875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794023484900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0122d76a-60bf-4eae-960c-ef77260d1801", "name": "runTaskFromQueue task cost before running: 3 s 193 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794023592400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "251dccd3-825a-408f-a758-029d486fca77", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794010821500, "endTime": 33794023696900, "totalTime": 12746300}, "additional": {"logType": "info", "children": [], "durationId": "90d5345a-5853-4259-a5f9-535665af61b9"}}, {"head": {"id": "50b789cb-1d46-40d4-a48e-ce8809f95f12", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794026041700, "endTime": 33794026593000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "295cca22-f41b-4663-a282-1e7bcb372e33", "logId": "0ea082fb-fdf8-41e6-8231-5757d102fb7c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "295cca22-f41b-4663-a282-1e7bcb372e33", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794025990700}, "additional": {"logType": "detail", "children": [], "durationId": "50b789cb-1d46-40d4-a48e-ce8809f95f12"}}, {"head": {"id": "6ec6d14b-8f85-4c30-9e8f-bf9921dcfd02", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794026048000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49438c7d-2187-4f05-850e-747f9f7433a8", "name": "entry : assembleHap cost memory 0.0112762451171875", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794026290800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "039c4298-8a0c-4181-af88-23bde484b38c", "name": "runTaskFromQueue task cost before running: 3 s 196 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794026504700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ea082fb-fdf8-41e6-8231-5757d102fb7c", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794026041700, "endTime": 33794026593000, "totalTime": 386400}, "additional": {"logType": "info", "children": [], "durationId": "50b789cb-1d46-40d4-a48e-ce8809f95f12"}}, {"head": {"id": "dc108dc7-e308-46b1-b3d9-beb484f041fa", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794039313500, "endTime": 33794039342000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "606ff829-a88b-483c-a4f4-cd97c727ebf1", "logId": "5beb1707-1dd1-4c84-bd79-ac3696971850"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5beb1707-1dd1-4c84-bd79-ac3696971850", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794039313500, "endTime": 33794039342000}, "additional": {"logType": "info", "children": [], "durationId": "dc108dc7-e308-46b1-b3d9-beb484f041fa"}}, {"head": {"id": "d1785618-bfb8-4355-9b08-43d83dff7e88", "name": "BUILD SUCCESSFUL in 3 s 209 ms ", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794039445800}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "cf2000a0-6b06-410a-91b3-82a588e61037", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33790830892300, "endTime": 33794040776300}, "additional": {"time": {"year": 2025, "month": 6, "day": 24, "hour": 17, "minute": 52}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.14.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "d27bb8e5-417f-49ea-b6bd-eb29df074a5d", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794040976800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed49c459-0cbc-4ed5-a696-b77d5339c542", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794041171600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "253862db-5f55-42a0-b61c-c4f4156008a4", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794041301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dbaadf8-f08b-43a9-b358-5a5ffe6853ef", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794041423800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e73206d0-6bb4-4327-b9c7-42b2b5c923cb", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794041771600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8404433-d948-441b-b2f3-59ffdb21fd6c", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794042239300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b4a1261-78a2-4333-bd7b-983c2ced9b55", "name": "Update task entry:default@ProcessIntegratedHsp output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build\\cache\\default\\integrated_hsp\\integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794043137000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfddcb9a-d2f4-48da-817c-389fed16e980", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794043420000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad59c9d-d53c-40b7-94bd-767727991651", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794043519100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fa82b29-f020-464f-911b-e2ce6a85f4b1", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\syscap_tool.exe cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794043591000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15f66292-fb53-4c32-8557-5fddf83001ba", "name": "Update task entry:default@SyscapTransform input file:C:\\Program Files\\Huawei\\DevEco Studio\\sdk\\default\\openharmony\\ets\\api\\device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794043928000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3db58861-81f7-4431-a0c0-15fa6280f235", "name": "Update task entry:default@SyscapTransform output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\syscap\\default\\rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794044878900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d393c7cf-f06d-4009-b75f-eb7f1990f16b", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794045142000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77958b41-524b-4a65-846b-b5981974a5f7", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794045212900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87ef3e38-f816-473a-aff1-946f2371bd08", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794045266500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d60e697-6f3e-407d-a74b-434565d7d870", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794045317700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cadc7f5-4669-421d-a213-c56e85d8515e", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794045366400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a78a75-1756-416a-8dbd-0bade18d4f56", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794045725800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aecacbc2-7278-4822-bf34-57b02267961c", "name": "Update task entry:default@ProcessLibs input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794046075300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2bea6a-12da-4f86-8cae-9bc84222ea0f", "name": "Update task entry:default@ProcessLibs output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794046307700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6535bc-84a7-47f3-9ad8-f34b5c7d8420", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794046972000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c7b1c59-3d68-4ea9-b1fa-cff0be871b63", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794047070900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "801ba414-84ce-4f26-85a9-205cd297caa1", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794047187800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "994375e7-3c64-4b10-9d60-da608f3aba98", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794052378200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c3bc9e-7f8a-47b0-92f8-087990f33669", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794053408500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "186c6a6a-e77e-478d-9455-c8682eb9d07c", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794054436600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "579279e4-4c30-483d-85a1-783332af4ed8", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794054690300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2963f9d9-5b2a-44b8-bf8f-45b33f7c8fbc", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794054954400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ef8185c-faa8-4a86-acc8-ab9c04141591", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\main\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794055661200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a55f7ca-54e6-4fc0-b877-ef9e89733681", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794055802700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9c7be4a-62af-4852-9c44-f41b83a0aa88", "name": "Update task entry:default@CompileArkTS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794056312700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40c7594f-11db-450f-be38-47b48bccaac0", "name": "Update task entry:default@CompileArkTS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794056575200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0139153-ca3d-4b9c-939d-d98e2c0a741b", "name": "Incremental task entry:default@CompileArkTS post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794057187600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c45ea4b-02ce-4eec-9d03-a225c0c0208e", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794058453800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56952110-e342-4594-b22f-a666916daeed", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794058991000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c5652c-1fa0-4239-8f23-a0a67aa08720", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794059901900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "598b3a45-17ba-4726-9edf-aa4143aee261", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794060110900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffc87896-c64f-48eb-8e38-d71fb230e0c3", "name": "Update task entry:default@BuildJS input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794060300000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ba07c9c-8585-439e-9554-13edde140cf0", "name": "Update task entry:default@BuildJS output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\js cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794061024200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7d4900-1edc-4850-8386-5047e602425e", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794061322600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb0b3f0f-70ad-4a54-8183-15e959c28d82", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794061410600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9d3f278-854e-4b19-a695-a4e57eb13525", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794061465200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62e43e6b-caaf-4e62-81fc-f41482148cf8", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\stripped_native_libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794062399100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed26ef5a-66e0-4009-b968-1e16eb59c316", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794062730000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4126f22b-6694-4418-8c3a-2d0a77e4a7c4", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794062983800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f2d89b3-7a46-469e-b066-87de35848fad", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\res\\default\\resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794073033300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e65fa58-203d-4cc0-b855-4180e75144c0", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794073418700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6683c32d-71a5-4ad8-b6b1-88beb47ee411", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794073865000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719d5ee2-4809-4758-9cce-4bd070bd4321", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794073974500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e5c6f7-c4bd-46ed-b838-027d480a4960", "name": "Update task entry:default@PackageHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794074379000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ef74083-f599-4ec9-9f58-8b8828aab30e", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794075060600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ea2e842-58b1-4e53-9a70-035b9b07b534", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\source_map\\default\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794075344200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a313a3f-777d-439b-8249-7ef7141819e8", "name": "Update task entry:default@PackageHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\mapping\\sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794075582700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3135bff4-082c-494e-85c4-425e94861c7f", "name": "Incremental task entry:default@PackageHap post-execution cost:15 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794075913200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "859b6da8-2ba7-40dc-a58d-ba94aed6849e", "name": "Update task entry:default@SignHap input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794076077100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "327b702f-ab70-4626-8503-9e978db45c91", "name": "Update task entry:default@SignHap output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794076157500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030367cd-508b-4b04-bbdf-f67c9c4df7e5", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794076371700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8828b68b-6d9c-47ac-9640-ceb2881ee433", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\loader_out\\default\\ets\\sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794078715400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8c2bea7-6847-4f35-85d2-14dd7c6d9a62", "name": "Update task entry:default@CollectDebugSymbol input file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\intermediates\\libs\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794079006300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ff8e02-26f9-4bb3-9fdc-ae3af193400a", "name": "Update task entry:default@CollectDebugSymbol output file:D:\\yysrc\\sourcecode\\mrhp-sharedoc\\2025\\SheShudu\\entry\\build\\default\\outputs\\default\\symbol cache.", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794079390700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb9b390-8e68-4f11-8d32-59b77e7d8fda", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 9248, "tid": "Main Thread", "startTime": 33794079713400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}