import { media } from '@kit.MediaKit';
import { audio } from '@kit.AudioKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { common } from '@kit.AbilityKit';
import Logger from './Logger';

export default  class MyAvPlayer{
  private avPlayer: media.AVPlayer | null = null;
  tag: string = 'SHEDAVPlay';
    async init(){
      if (this.avPlayer) {
        Logger.info('init avPlayer release2createNew');
        this.avPlayer.release();
        await this.msleepAsync(1500);
      }
      // 创建avPlayer实例对象
      this.avPlayer = await media.createAVPlayer();

      this.avPlayer.on('stateChange', async (state, reason) => {
        if (this.avPlayer == null) {
          Logger.info('vPlayer has not init on state change');
          return;
        }
        switch (state) {
          case 'idle': // 成功调用reset接口后触发该状态机上报
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state idle called.`);
            break;
          case 'initialized': // avplayer 设置播放源后触发该状态上报
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state initialized called.`);
            this.avPlayer.prepare();
            break;
          case 'prepared': // prepare调用成功后上报该状态机
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state prepared called.`);
            this.avPlayer.play(); // 调用播放接口开始播放
            break;
          case 'playing': // play成功调用后触发该状态机上报
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state playing called.`);
            break;
          case 'completed': // 播放结束后触发该状态机上报
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state completed called.`);
            break;
          case 'released':
            console.info(`${this.tag}: setAVPlayerCallback released called.`);
            break
          case 'stopped':
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state stopped called.`);
            break
          case 'error':
            console.error(`${this.tag}: setAVPlayerCallback AVPlayer state error called.`);
            break
          case 'paused':
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state paused called.`);
            break
          default:
            console.info(`${this.tag}: setAVPlayerCallback AVPlayer state unknown called.`);
            break;
        }
      });


    }

    async  setUrl(context:common.UIAbilityContext,fileName:string){
      // 为fdSrc赋值触发initialized状态机上报
      if(this.avPlayer){
        this.avPlayer.reset(async  (err: BusinessError) => {
          if (err) {
            console.error('音乐播放器重置失败 :' + err.message);
          } else {
            let fileDescriptor = await context.resourceManager.getRawFd(fileName);
            let avFileDescriptor: media.AVFileDescriptor =
              { fd: fileDescriptor.fd, offset: fileDescriptor.offset, length: fileDescriptor.length };
            this.avPlayer!.fdSrc = avFileDescriptor;
          }
        })

      }
    }
    release(){
      if(this.avPlayer){
        this.avPlayer.release();
        this.avPlayer=null;
      }
    }
    async msleepAsync(ms: number): Promise<boolean> {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true)
        }, ms)
      })
    }
  stop() {
    if(this.avPlayer){
      this.avPlayer.stop();
    }
  }
}